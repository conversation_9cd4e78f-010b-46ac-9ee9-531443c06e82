package tech.tiangong.sdp;

import cn.yibuyun.framework.config.OpenFeignUserContentConfig;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@Slf4j
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"tech.tiangong","cn.yibuyun","com.yibuyun","com.zjkj.scf","com.zjkj.supplychain","team.aikero.*","com.zjkj.aigc.feign","com.zjkj.aigc.client"},defaultConfiguration = OpenFeignUserContentConfig.class)
@MapperScan(basePackages = "tech.tiangong.sdp.**.mapper")
@SpringBootApplication(scanBasePackages = {"tech.tiangong","cn.yibuyun","com.yibuyun"})
@EnableAsync
public class StartSdpDesign {

	public static void main(String[] args) {
		SpringApplication.run(StartSdpDesign.class, args);
	}
}