package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.query.DesignDemandQuery;
import tech.tiangong.sdp.design.vo.req.demand.*;
import tech.tiangong.sdp.design.vo.resp.demand.DesignDemandCreateVo;
import tech.tiangong.sdp.design.vo.resp.demand.DesignDemandDetailInfoVo;
import tech.tiangong.sdp.design.vo.resp.demand.DesignDemandPageVo;
import tech.tiangong.sdp.design.vo.resp.demand.SuggestedMaterialDetailVo;

import java.util.List;

/**
 * 灵感设计需求表服务接口
 *
 * <AUTHOR>
 */
public interface DesignDemandService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<DesignDemandPageVo> page(DesignDemandQuery query);

    /**
     * 设计需求详情
     *
     * @param designDemandId 主键
     * @return 数据实体
     */
    DesignDemandDetailInfoVo getDetailById(Long designDemandId);

    /**
     * 查询设计需求的推荐面料
     *
     * @param designDemandId 设计需求id
     * @return 响应结果
     */
    List<SuggestedMaterialDetailVo> listSuggestMaterial(Long designDemandId);

    /**
     * 创建灵感设计需求(对接商品运营平台)
     *
     * @param req 数据实体
     */
    DesignDemandCreateVo add(DesignDemandCreateReq req);

    /**
     * 任务分配
     * @param req 入参
     */
    void allocate(DesignDemandAllocateReq req);

    /**
     * 分配变更
     *
     * @param req 请求参数对象
     */
    void reAllocateUpdate(DesignDemandReAllocateReq req);

    /**
     * 淘汰
     * @param req 入参
     */
    void noPass(DesignDemandNoPassReq req);

    /**
     * 开款
     * @param req 入参
     */
    void createSpu(DesignDemandCreateSpuReq req);

}
