package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.MessageRecord;
import tech.tiangong.sdp.design.mapper.MessageRecordMapper;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqStateEnum;
import tech.tiangong.sdp.design.mq.enums.MqTypeEnum;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * mq消息日志服务仓库类
 *
 * <AUTHOR>
 * @since 2021-05-24 14:14:10
 */
@Slf4j
@Repository
public class MessageRecordRepository extends BaseRepository<MessageRecordMapper, MessageRecord> {

    public void updateSate(Long id, MqStateEnum state, String remark) {
        log.info("更新消息状态-{}----id={}", state.getDesc(), id);
        MessageRecord record = new MessageRecord()
                .setMessageId(id)
                .setFinishTime(MqStateEnum.SUCC_HANDLE.equals(state) ? LocalDateTime.now() : null)
                .setState(state.getCode())
                .setRemark(remark != null && remark.length() > 500 ? remark.substring(0, 500) : remark);
        record.setRevisedTime(LocalDateTime.now());
        baseMapper.updateSate(record);
    }

    public MessageRecord saveLog(MqMessageReq event, MqTypeEnum typeEnum) {
        log.info("保存消息日志-{}----id={}", event.getBizType().getDesc(), event.getMessageId());
        MessageRecord entity = new MessageRecord()
                .setMessageId(event.getMessageId())
                .setUId(event.getUId())
                .setType(typeEnum.getCode())
                .setBizType(event.getBizType().getCode())
                .setExchange(event.getExchange())
                .setRoutingKey(event.getRoutingKey())
                .setQueue(event.getQueue())
                .setMqContent(Optional.ofNullable(event.getMqContent()).orElse(""))
                .setRetry(-1)
                .setState(MqStateEnum.WAIT_HANDLE.getCode());
        this.save(entity);
        return entity;
    }

}