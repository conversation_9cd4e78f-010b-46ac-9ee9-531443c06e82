package tech.tiangong.sdp.design.service.download.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.exception.BusinessException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zjkj.booster.common.constant.DatePatternConstants;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.clothes.vo.dto.DimensionPictureDto;
import tech.tiangong.sdp.core.config.DownloadProperties;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualSpuTypeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskProcessTypeEnum;
import tech.tiangong.sdp.design.helper.ImageDownloadHelper;
import tech.tiangong.sdp.design.helper.UploaderOssHelper;
import tech.tiangong.sdp.design.remote.SdpOrderHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.VisualTaskService;
import tech.tiangong.sdp.design.service.download.DownloadTaskStrategy;
import tech.tiangong.sdp.design.vo.dto.download.FileUploadDTO;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialPictureResp;
import tech.tiangong.sdp.design.vo.resp.visual.SpuAigcImageVo;
import tech.tiangong.sdp.design.vo.resp.visual.SpuImageMaterial;
import tech.tiangong.sdp.prod.vo.resp.StyleSizeDetailVo;
import tech.tiangong.sdp.prod.vo.resp.StyleSizeInfoVo;
import tech.tiangong.sdp.utils.FileCompressUtils;
import tech.tiangong.sdp.utils.PlmExcelExportUtil;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 下载视觉任务款式素材图
 */
@Slf4j
@AllArgsConstructor
@Component
public class DownloadVisualTaskSpuImageMaterialImpl implements DownloadTaskStrategy {

    private final VisualTaskRepository visualTaskRepository;
    private final ImageDownloadHelper imageDownloadHelper;
    private final AsyncTaskExecutor asyncTaskExecutor;
    private final DownloadProperties downloadProperties;
    private final UploaderOssHelper uploaderOssHelper;
    private final VisualTaskService visualTaskService;
    private final VisualSpuRepository visualSpuRepository;
    private final VisualTaskDetailRepository visualTaskDetailRepository;
    private final VisualTaskTryOnRepository visualTaskTryOnRepository;
    private final SpotSpuRepository spotSpuRepository;
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final SdpOrderHelper sdpOrderHelper;

    private static final DateTimeFormatter PURE_DATETIME_PATTERN = DateTimeFormatter.ofPattern(DatePatternConstants.PURE_DATETIME_PATTERN);
    private static final Long DOWNLOAD_TIMEOUT_SECONDS = 10 * 60L; // 10分钟超时

    @Override
    public DesignAsyncTaskTypeEnum getTaskType() {
        return DesignAsyncTaskTypeEnum.DOWNLOAD_VISUAL_TASK_SPU_IMAGE_MATERIAL;
    }

    @Override
    public List<FileUploadDTO> processDownloadTask(DesignAsyncTask task) {
        log.info("==== DownloadVisualTaskSpuImageMaterialImpl processDownloadTask {}:{}",getTaskType().getDesc(), JSONObject.toJSONString(task));
        File tempDir = null;
        try {
            tempDir = createTempDirectory(task.getAsyncTaskId());

            List<Long> visualTaskIds = JSONObject.parseArray(task.getParameters(), Long.class);
            List<VisualTask> visualTasks = visualTaskRepository.listByIds(visualTaskIds);
            Set<String> styleCodes = visualTasks.stream().map(VisualTask::getStyleCode).collect(Collectors.toSet());

            List<VisualSpu> spuList = visualSpuRepository.listByStyleCodes(styleCodes);
            if (CollectionUtil.isEmpty(spuList)) {
                throw new BusinessException("未找到对应SPU记录, asyncTaskId: "+task.getAsyncTaskId()+", styleCodes:"+JSONObject.toJSONString(styleCodes));
            }

            // 2. 异步下载图片并按SPU组织目录
            File finalTempDir = tempDir;
            List<CompletableFuture<File>> futures = spuList.stream()
                    .map (spu ->
                            CompletableFuture.supplyAsync(()->
                            downloadMaterialImagesForSpu(spu, finalTempDir),asyncTaskExecutor)
                                .orTimeout(DOWNLOAD_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                                .handle((result,ex)->{
                                    if (ex != null) {
                                        return handleDownloadException(ex, spu);
                                    }
                                    return result;
                                })
            ).collect(Collectors.toList());

            // 3. 等待所有下载任务完成
            List<File> downloadedFiles = waitForDownloadCompletion(futures);

            if (CollectionUtil.isEmpty(downloadedFiles)) {
                throw new BusinessException("所有文件下载均失败1");
            }
            downloadedFiles = downloadedFiles.stream().filter(Objects::nonNull).toList();
            if (CollectionUtil.isEmpty(downloadedFiles)) {
                throw new BusinessException("所有文件下载均失败2");
            }
            // 4. 压缩文件
            return compressFiles(task.getAsyncTaskId(), tempDir, downloadedFiles);
        } catch (Exception e) {
            log.error("系统处理异常: taskId={}, req={}, message={}",task.getAsyncTaskId(),task.getParameters(), e.getMessage());
            throw new BusinessException("下载任务处理失败", e);
        } finally {
            cleanupTempFiles(tempDir);
        }
    }

    /**
     * 压缩文件
     */
    private List<FileUploadDTO> compressFiles(Long taskId, File tempDir, List<File> files) {
        if (files.isEmpty()) {
            return Collections.emptyList();
        }

        Integer maxZipSizeMb = downloadProperties.getMaxZipSizeMb();
        String baseFileName = "images_"+taskId;
        List<File> zipFiles = FileCompressUtils.zipFilesWithSizeLimit(
                tempDir,
                baseFileName,
                maxZipSizeMb,
                files);

        if (zipFiles.isEmpty()) {
            throw new BusinessException("压缩文件失败，压缩后返回空文件");
        }
        return zipFiles.stream().map(uploaderOssHelper::createFileUploadDTO).collect(Collectors.toList());
    }

    /**
     * 下载单个SPU的所有素材
     */
    private File downloadMaterialImagesForSpu(VisualSpu visualSpu, File parentDir){
        if (StringUtils.isBlank(visualSpu.getStyleCode())) {
            log.warn("downloadMaterialImagesForSpu 异常， visualSpuId:{}",visualSpu.getVisualSpuId());
            return null;
        }

        String spuCode = visualSpu.getStyleCode();
        File spuDir = new File(parentDir, spuCode);

        try {
            FileUtils.forceMkdir(spuDir);

            //尺寸表EXCEL
            List<StyleSizeInfoVo> styleSizeInfoVos = sdpOrderHelper.listSizeInfoByStyleCode(List.of(spuCode));
            log.info("spuCode:{},尺寸表信息:{}", spuCode,JSONObject.toJSONString(styleSizeInfoVos));
            //生成尺寸表EXCEL
            if(CollectionUtil.isNotEmpty(styleSizeInfoVos)){
                generateStyleSizeExcel(spuDir.getAbsolutePath()+File.separator+generateFilePath(spuCode),styleSizeInfoVos.getFirst().getSizeDetailList());
            }

            //素材图片
            SpuImageMaterial spuImageMaterial = visualTaskService.buildSpuImageVo(visualSpu);
            log.info("spuCode:{},素材图片:{}", spuCode,JSONObject.toJSONString(spuImageMaterial));
            if(spuImageMaterial==null || CollectionUtil.isEmpty(spuImageMaterial.getSkcImageMaterials())){
                log.warn("SPU没有素材图片:{}",spuCode);
            }else {
                Map<String, List<ImageFile>> spuImages = new HashMap<>();
                SpuImageMaterial.SkcImageMaterial firstImageMaterial = spuImageMaterial.getSkcImageMaterials().getFirst();
                //设计款
                List<ImageFile> images = new ArrayList<>();
                if (VisualSpuTypeEnum.DESIGN_STYLE.getCode().equals(visualSpu.getStyleType())) {
                    //spu图片
                    this.buildDesignSpuImage(firstImageMaterial, spuCode, images);
                    //skc图片
                    spuImageMaterial.getSkcImageMaterials().forEach(skcMaterialImage -> {
                        this.buildDesignSkcImage(skcMaterialImage, images);
                    });
                }
                //现货
                else if (VisualSpuTypeEnum.SPOT_SPU.getCode().equals(visualSpu.getStyleType())) {
                    //spu图片
                    this.buildSpotSpuImage(spuCode, images);
                    //skc图片
                    spuImageMaterial.getSkcImageMaterials().forEach(skcMaterialImage -> {
                        this.buildSpotSkcImage(skcMaterialImage, images);
                    });
                }
                spuImages.put(spuCode, images);

                //下载图片
                if (CollUtil.isEmpty(spuImages)) {
                    log.warn("SPU没有图片记录:{}", spuCode);
                }else{
                    for (Map.Entry<String,List<ImageFile>> spuImageFile: spuImages.entrySet()) {
                        //按spu分文件夹
                        /*
                        File designDir = new File(spuDir.getAbsolutePath()+File.separator+skcImageFile.getKey());
                        if(!designDir.exists()){
                            designDir.mkdirs();
                        }
                         */
                        for(ImageFile imageFile : spuImageFile.getValue()){
                            // imageDownloadHelper.downloadSingleImageToDir(imageFile, designDir);
                            imageDownloadHelper.downloadSingleImageToDir(imageFile, spuDir);
                        }
                    }
                }
            }
            return spuDir;
        } catch (Exception e) {
            log.error("下载素材失败,spuCode: {}",spuCode,e);
            return null;
        }
    }

    private void buildSpotSpuImage(String spuCode, List<ImageFile> images) {
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(spuCode);
        if (spotSpu != null) {
            SpotSpuDetail spotSpuDetail = spotSpuDetailRepository.getBySpotSpuId(spotSpu.getSpotSpuId());
            if (CollectionUtil.isNotEmpty(spotSpuDetail.getTryOnPictureList())) {
                int i = 0;
                for (String tryOnPictureUrl : spotSpuDetail.getTryOnPictureList()) {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(spuCode + "-tryOn图-" + i);
                    imageFile.setOssImageUrl(tryOnPictureUrl);
                    images.add(imageFile);
                    i++;
                }
            }
            if (CollectionUtil.isNotEmpty(spotSpuDetail.getProductPictureList())) {
                int i = 0;
                for (String productPictureUrl : spotSpuDetail.getProductPictureList()) {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(spuCode + "-商品图-" + i);
                    imageFile.setOssImageUrl(productPictureUrl);
                    images.add(imageFile);
                    i++;
                }
            }
        }
    }

    private void buildSpotSkcImage(SpuImageMaterial.SkcImageMaterial skcMaterialImage, List<ImageFile> images) {
        String designCode = skcMaterialImage.getDesignCode();
        //设计图-这里其实是现货skc里面的商品图
        if (CollectionUtil.isNotEmpty(skcMaterialImage.getDesignImages())) {
            int i = 0;
            for (String designImageUrl : skcMaterialImage.getDesignImages()) {
                ImageFile imageFile = new ImageFile();
                imageFile.setOrgImgName(designCode + "-设计图-" + i);
                imageFile.setOssImageUrl(designImageUrl);
                images.add(imageFile);
                i++;
            }
        }
    }

    private void buildDesignSkcImage(SpuImageMaterial.SkcImageMaterial skcMaterialImage, List<ImageFile> images) {
        String designCode = skcMaterialImage.getDesignCode();
        //设计图
        if (CollectionUtil.isNotEmpty(skcMaterialImage.getDesignImages())) {
            int i = 0;
            for (String designImageUrl : skcMaterialImage.getDesignImages()) {
                ImageFile imageFile = new ImageFile();
                imageFile.setOrgImgName(designCode + "-设计图-" + i);
                imageFile.setOssImageUrl(designImageUrl);
                images.add(imageFile);
                i++;
            }
        }

        //bom物料图
        if (CollectionUtil.isNotEmpty(skcMaterialImage.getBomMaterialImages())) {
            int i = 0;
            for (BomMaterialPictureResp.MaterialPicture bomMaterialImageInfo : skcMaterialImage.getBomMaterialImages()) {
                ImageFile imageFile = new ImageFile();
                imageFile.setOrgImgName(designCode + "-" + bomMaterialImageInfo.getMaterialTypeName() + bomMaterialImageInfo.getMaterialName() + "-" + i);
                imageFile.setOssImageUrl(bomMaterialImageInfo.getPictureUrl());
                images.add(imageFile);
                i++;
            }
        }
        //二次工艺图
        if (CollectionUtil.isNotEmpty(skcMaterialImage.getCraftPictureList())) {
            int i = 0;
            for (String craftPictureUrl : skcMaterialImage.getCraftPictureList()) {
                ImageFile imageFile = new ImageFile();
                imageFile.setOrgImgName(designCode + "-二次工艺图-" + i);
                imageFile.setOssImageUrl(craftPictureUrl);
                images.add(imageFile);
                i++;
            }
        }
    }

    private void buildDesignSpuImage(SpuImageMaterial.SkcImageMaterial skcMaterialImage, String spuCode, List<ImageFile> images) {
        //AIGC图
        SpuAigcImageVo spuAigcImageVo = skcMaterialImage.getAigcImageInfo();
        if (spuAigcImageVo != null) {
            //1k图
            if (CollectionUtil.isNotEmpty(spuAigcImageVo.getOriginImageList())) {
                int i = 0;
                for (String imageUrl : spuAigcImageVo.getOriginImageList()) {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(spuCode + "-AIGC1K-" + i);
                    imageFile.setOssImageUrl(imageUrl);
                    images.add(imageFile);
                    i++;
                }
            }
            //4k图
            if (CollectionUtil.isNotEmpty(spuAigcImageVo.getHdImageList())) {
                int i = 0;
                for (String imageUrl : spuAigcImageVo.getHdImageList()) {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(spuCode + "-AIGC4K-" + i);
                    imageFile.setOssImageUrl(imageUrl);
                    images.add(imageFile);
                    i++;
                }
            }
            //1k主图
            if (CollectionUtil.isNotEmpty(spuAigcImageVo.getOriginMainImageList())) {
                int i = 0;
                for (String imageUrl : spuAigcImageVo.getOriginMainImageList()) {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(spuCode + "-AIGC1K主图-" + i);
                    imageFile.setOssImageUrl(imageUrl);
                    images.add(imageFile);
                    i++;
                }
            }
            //4k主图
            if (CollectionUtil.isNotEmpty(spuAigcImageVo.getHdMainImageList())) {
                int i = 0;
                for (String imageUrl : spuAigcImageVo.getHdMainImageList()) {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(spuCode + "-AIGC4K主图-" + i);
                    imageFile.setOssImageUrl(imageUrl);
                    images.add(imageFile);
                    i++;
                }
            }
        }
        //3D图
        DimensionPictureDto dimensionPictureDto = skcMaterialImage.getDimensionImages();
        if (dimensionPictureDto != null) {
            //3D图正面
            DimensionPictureDto.PictureDescribe frontPicture = dimensionPictureDto.getFrontPicture();
            if (frontPicture != null && CollectionUtil.isNotEmpty(frontPicture.getUrls())) {
                int i = 0;
                for (DimensionPictureDto.PictureUrl it : frontPicture.getUrls()) {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(spuCode + "-3D打版正面-" + i);
                    imageFile.setOssImageUrl(it.getUrl());
                    images.add(imageFile);
                    i++;
                }
            }
            //3D图侧面
            DimensionPictureDto.PictureDescribe sidePicture = dimensionPictureDto.getSidePicture();
            if (frontPicture != null && CollectionUtil.isNotEmpty(sidePicture.getUrls())) {
                int i = 0;
                for (DimensionPictureDto.PictureUrl it : sidePicture.getUrls()) {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(spuCode + "-3D打版侧面-" + i);
                    imageFile.setOssImageUrl(it.getUrl());
                    images.add(imageFile);
                    i++;
                }
            }
            //3D图背面
            DimensionPictureDto.PictureDescribe backPicture = dimensionPictureDto.getBackPicture();
            if (frontPicture != null && CollectionUtil.isNotEmpty(backPicture.getUrls())) {
                int i = 0;
                for (DimensionPictureDto.PictureUrl it : backPicture.getUrls()) {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(spuCode + "-3D打版背面-" + i);
                    imageFile.setOssImageUrl(it.getUrl());
                    images.add(imageFile);
                    i++;
                }
            }
            //3D图其他图片
            DimensionPictureDto.PictureDescribe detailPicture = dimensionPictureDto.getDetailPictures();
            if (frontPicture != null && CollectionUtil.isNotEmpty(detailPicture.getUrls())) {
                int i = 0;
                for (DimensionPictureDto.PictureUrl it : detailPicture.getUrls()) {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(spuCode + "-3D打版其他-" + i);
                    imageFile.setOssImageUrl(it.getUrl());
                    images.add(imageFile);
                    i++;
                }
            }
        }

        //版型图
        List<String> modePicture = skcMaterialImage.getModePictureList();
        if (CollectionUtil.isNotEmpty(modePicture)) {
            int i = 0;
            for (String url : modePicture) {
                ImageFile imageFile = new ImageFile();
                imageFile.setOrgImgName(spuCode + "-101-" + i);
                imageFile.setOssImageUrl(url);
                images.add(imageFile);
                i++;
            }
        }

        //类目图
        List<String> categoryChartImages = skcMaterialImage.getQuantityMethodPictureList();
        if (CollectionUtil.isNotEmpty(categoryChartImages)) {
            int i = 0;
            for (String url : categoryChartImages) {
                ImageFile imageFile = new ImageFile();
                imageFile.setOrgImgName(spuCode + "-202-" + i);
                imageFile.setOssImageUrl(url);
                images.add(imageFile);
                i++;
            }
        }

        //SPU最新任务的tryOn图
        VisualTask visualTask = visualTaskRepository.getLatestTaskBySpuAndProcessType(spuCode, VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF);
        if (visualTask != null) {
            VisualTaskDetail visualTaskDetail = visualTaskDetailRepository.getByTaskId(visualTask.getTaskId());
            if (visualTaskDetail != null && visualTaskDetail.getLatestTryOnDetailId() != null) {
                VisualTaskTryOn visualTaskTryOn = visualTaskTryOnRepository.getById(visualTaskDetail.getLatestTryOnDetailId());
                if (visualTaskTryOn != null && StringUtils.isNotBlank(visualTaskTryOn.getTryOnImages())) {
                    List<ImageFile> tryOnUrls = JSONObject.parseArray(visualTaskTryOn.getTryOnImages(), ImageFile.class);
                    int i = 0;
                    for (ImageFile tryOnUrl : tryOnUrls) {
                        ImageFile imageFile = new ImageFile();
                        imageFile.setOrgImgName(spuCode + "-tryOn图-" + i);
                        imageFile.setOssImageUrl(tryOnUrl.getOssImageUrl());
                        images.add(imageFile);
                        i++;
                    }
                }
            }
        }
        //SPU下数码描稿任务效果图
        List<String> digitalPaintingPictures = skcMaterialImage.getDigitalPaintingPictureList();
        if (CollectionUtil.isNotEmpty(digitalPaintingPictures)) {
            int i = 0;
            for (String digitalPaintingPictureUrl : digitalPaintingPictures) {
                ImageFile imageFile = new ImageFile();
                imageFile.setOrgImgName(spuCode + "-数码描码效果图-" + i);
                imageFile.setOssImageUrl(digitalPaintingPictureUrl);
                images.add(imageFile);
                i++;
            }
        }
    }

    /**
     * 处理下载异常
     */
    private File handleDownloadException(Throwable throwable, VisualSpu visualSpu){
        log.error("下载素材异常: visualSpu={}",JSONObject.toJSONString(visualSpu),throwable);
        return null;
    }

    /**
     * 等待所有下载任务完成
     */
    private List<File> waitForDownloadCompletion(List<CompletableFuture<File>> futures) {
        if(CollectionUtil.isEmpty(futures)){
            return null;
        }
        return futures.stream().map(future ->{
            try {
                return future.join();
            } catch (Exception e) {
                // 捕获异步任务中的异常
                Throwable cause = e.getCause(); // 获取原始异常
                log.error("下载任务执行失败: 原因={}",(cause!=null && StringUtils.isNotBlank(cause.getMessage()) ? cause.getMessage() : "未知"));
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private File createTempDirectory(Long taskId) throws IOException {
        String tempDirStr = FileUtils.getTempDirectoryPath() + File.separator +
                "download_" + taskId + File.separator +
                PURE_DATETIME_PATTERN.format(LocalDateTime.now());

        File tempDir = new File(tempDirStr);
        FileUtils.forceMkdir(tempDir);
        return tempDir;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(File tempDir) {
        try {
            if(tempDir!=null){
                FileUtils.deleteDirectory(tempDir);
            }
        } catch (IOException e) {
            log.error("清理临时文件失败:"+tempDir.getAbsolutePath(),e);
        }
    }

    // 生成并上传尺寸表EXCEL
    private void generateStyleSizeExcel(String filePath,List<StyleSizeDetailVo> styleDetailVos) {
        if(CollectionUtil.isEmpty(styleDetailVos)){
            log.warn("生成尺寸表excel失败，尺寸表明细为空");
            return;
        }
        StyleSizeExcelResp styleSizeExcelResp = getStyleSizeExcelResp(styleDetailVos);
        log.info("尺寸表EXCEL数据={}", JSON.toJSONString(styleSizeExcelResp));
        try (ByteArrayOutputStream byteArrayOutputStream = PlmExcelExportUtil.styleSizeTemplateExport(styleSizeExcelResp)) {
            log.info("生成尺寸表EXCEL");
            Files.write(Paths.get(filePath), byteArrayOutputStream.toByteArray());
        } catch (Exception e) {
            throw new RuntimeException("尺寸表EXCEL失败", e);
        }
    }
    // 生成文件路径
    private String generateFilePath(String styleCode) {
        String randomString = UUID.randomUUID().toString().replaceAll("-", "");
        return styleCode +"_size_" + randomString + ".xlsx";
    }
    public StyleSizeExcelResp getStyleSizeExcelResp(List<StyleSizeDetailVo> styleSizeDetailVos) {
        StyleSizeExcelResp styleSizeExcelResp = new StyleSizeExcelResp();
        if(CollectionUtil.isEmpty(styleSizeDetailVos)){
            log.warn("生成尺寸表excel对象失败，尺寸表明细为空1");
            return styleSizeExcelResp;
        }
        //尺码信息
        List<StyleSizeExcelResp.BillDetail> billDetails = new ArrayList<StyleSizeExcelResp.BillDetail>();
        styleSizeDetailVos.forEach(a -> {
            List<StyleSizeDetailVo.SizeData> skipSizeQuotietyList = a.getSkipSizeQuotietyList();
            List<StyleSizeDetailVo.SizeData> sizeList = a.getSizeList();
            StyleSizeExcelResp.BillDetail billDetail = new StyleSizeExcelResp.BillDetail();
            BeanUtils.copyProperties(a, billDetail);
            billDetail.setDesignSize(a.getDesignSize().toString());

            String sizeRowDatail = sizeList.stream()
                    .map(sizeData -> sizeData.getData().toString())
                    .collect(Collectors.joining(StrUtil.COMMA));

            billDetail.setRowDatails(sizeRowDatail + StrUtil.COMMA + ("±" + a.getDeviationRange()));
            billDetails.add(billDetail);
        });
        styleSizeExcelResp.setBillDetails(billDetails);


        ArrayList<Map<String, String>> titleLists = new ArrayList<Map<String, String>>();
        StyleSizeDetailVo first = styleSizeDetailVos.getFirst();

        List<HashMap<String, String>> sizeMapList = first.getSizeList().stream().map(sizeData -> {
            HashMap<String, String> map = new HashMap<>();
            map.put("title", sizeData.getSize());
            return map;
        }).toList();

        titleLists.addAll(sizeMapList);

        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("title", "允差");
        titleLists.addLast(stringStringHashMap);

        styleSizeExcelResp.setTitleList(titleLists);
        return styleSizeExcelResp;
    }
}
