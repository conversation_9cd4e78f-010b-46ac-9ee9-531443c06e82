package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.req.demand.DesignDemandSuggestedMaterialReq;
import tech.tiangong.sdp.design.vo.resp.demand.DesignDemandSuggestedMaterialVo;

/**
 * 设计需求_推荐物料表服务接口
 *
 * <AUTHOR>
 */
public interface DesignDemandSuggestedMaterialService {

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    DesignDemandSuggestedMaterialVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(DesignDemandSuggestedMaterialReq req);

    /**
     * 更新数据
     *
     * @param req 数据实体
     */
    void update(DesignDemandSuggestedMaterialReq req);


}
