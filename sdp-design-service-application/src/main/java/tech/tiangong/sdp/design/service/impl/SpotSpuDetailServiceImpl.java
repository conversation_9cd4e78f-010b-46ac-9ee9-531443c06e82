package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.AiAlibabaDistributionSyncStatusEnum;
import tech.tiangong.sdp.design.enums.spot.SpotResourceStateEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskImageTypeEnum;
import tech.tiangong.sdp.design.repository.SpotSkcRepository;
import tech.tiangong.sdp.design.repository.SpotSpuDetailRepository;
import tech.tiangong.sdp.design.repository.SpotSpuRepository;
import tech.tiangong.sdp.design.repository.VisualImagePackageRepository;
import tech.tiangong.sdp.design.service.SpotSpuDetailService;
import tech.tiangong.sdp.design.service.SpotSpuMurmurationService;
import tech.tiangong.sdp.design.service.VisualImagePackageService;
import tech.tiangong.sdp.design.vo.dto.ai.AlibabaDistributionOutputDto;
import tech.tiangong.sdp.design.vo.req.spot.SpotAiAlibabaDistributionRetryReq;
import tech.tiangong.sdp.design.vo.req.spot.SpotSpuDetailReq;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * spot_spu_detail表(SpotSpuDetail)服务
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:47
 */
@Slf4j
@Service
@RequiredArgsConstructor
@RefreshScope
public class SpotSpuDetailServiceImpl implements SpotSpuDetailService {
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final SpotSpuRepository spotSpuRepository;
    private final SpotSpuMurmurationService spotSpuMurmurationService;
    private final SpotSkcRepository spotSkcRepository;
    private final VisualImagePackageService visualImagePackageService;
    private final VisualImagePackageRepository visualImagePackageRepository;
    // 在类中添加
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${size.defaultImageUrl:https://chuangxin-oss-cdn.tiangong.tech/tiangong_4fa92bc39c38491f8c321cd2b67c6433.png}")
    private String sizeDefaultImageUrl;

    private static String PROCESS_AI_ALIBABA_DISTRIBUTION = "SPOT:SPU:PROCESSAIALIBABADISTRIBUTION:TASK:";
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SpotSpuDetailReq req) {
        SpotSpuDetail entity = new SpotSpuDetail();
        BeanUtils.copyProperties(req, entity);
        entity.setSpotSpuDetailId(IdPool.getId());
        spotSpuDetailRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SpotSpuDetailReq req) {
        SpotSpuDetail entity = new SpotSpuDetail();
        BeanUtils.copyProperties(req, entity);
        spotSpuDetailRepository.updateById(entity);
    }

    @Override
    public void doAlibabaDistributionJob(Long id) {
        long currentId = (id != null) ? id : 0L;
        int size = 1;
        log.info("图包拉取任务");

        // 循环导入，每次5000，更新最大id给下次查询
        while (true) {
            List<SpotSpu> list = spotSpuRepository.selectAlibabaDistributionTask(currentId, size);
            if (list.isEmpty()) {
                log.info("结束图包拉取");
                break;
            }

            log.info("开始拉取图包，本次拉取数量：{}", list.size());
            for (SpotSpu item : list) {
                try {
                    spotSpuMurmurationService.submitAlibabaDistributionTask(item);
                } catch (Exception e) {
                    log.error("图包拉取异常{}：{}", item.getSpotSpuId(), e.getMessage(), e);
                }
            }

            log.info("结束图包拉取，本次拉取数量：{}", list.size());
            if (list.size() < size) {
                break;
            }

            // 拿到最大Id
            currentId = list.stream()
                    .mapToLong(item -> item.getSpotSpuId() != null ? item.getSpotSpuId() : 0L)
                    .max()
                    .orElse(0L);

            if (currentId == 0L) {
                break;
            }
        }
    }

    /**
     * 图包结果重新解析
     * @param req
     */
    @Override
    public void retryAiAlibabaDistributionConvertResults(SpotAiAlibabaDistributionRetryReq req){
        for(String styleCode: req.getStyleCodeList()){
            // 获取SPU信息
            SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
            if (spotSpu == null) {
                log.warn("图包解析重试: 未找到styleCode={}", styleCode);
                return;
            }
            // 获取SPU详情
            SpotSpuDetail spotSpuDetail = spotSpuDetailRepository.getBySpotSpuId(spotSpu.getSpotSpuId());
            if (spotSpuDetail == null) {
                log.warn("图包解析重试: 未找到SpotSpuDetail,  styleCode={}",
                        styleCode);
                return;
            }
            if(StrUtil.isBlank(spotSpuDetail.getAiAlibabaDistributionResult())){
                log.warn("图包解析重试: 未找到图包结果,  styleCode={}",
                        styleCode);
                return;
            }
            // 替换原来的解析方式
            AlibabaDistributionOutputDto resultDto = null;
            try {
                resultDto = objectMapper.readValue(spotSpuDetail.getAiAlibabaDistributionResult(), AlibabaDistributionOutputDto.class);
            } catch (JsonProcessingException e) {
                log.error("图包解析重试: 获取图包结果失败,  styleCode={}",styleCode);
                throw new RuntimeException(e);
            }
            processAiAlibabaDistributionConvertResults(spotSpu.getSpotSpuId().toString(), resultDto);
        }

    }

    @Override
    public void aiAlibabaDistributionJob(Integer day, Integer aiAlibabaDistributionSyncStatus, Integer limit){
        log.info("图包拉取任务");
        if(day == null){
            day = 30;
        }
        if(aiAlibabaDistributionSyncStatus == null){
            aiAlibabaDistributionSyncStatus = AiAlibabaDistributionSyncStatusEnum.WAIT_SYNC.getCode();
        }
        if(limit == null){
            limit = 5000;
        }
        List<SpotSpuDetail> spotSpuDetailList = spotSpuDetailRepository.listRecentDaysSpuDetail(day,aiAlibabaDistributionSyncStatus,limit);
        for(SpotSpuDetail spotSpuDetail:spotSpuDetailList){
            try {
                AlibabaDistributionOutputDto resultDto = objectMapper.readValue(spotSpuDetail.getAiAlibabaDistributionResult(), AlibabaDistributionOutputDto.class);
                processAiAlibabaDistributionConvertResults(spotSpuDetail.getSpotSpuId().toString(), resultDto);
            } catch (Exception e) {
                log.error("aiAlibabaDistributionJob图包处理识别: spotSpuDetailId={}",spotSpuDetail.getSpotSpuDetailId());
            }

        }

    }

    /**
     * 重新发起图包任务
     * @param req
     */
    @Override
    public void aiAlibabaDistributionRetryJob(SpotAiAlibabaDistributionRetryReq req){
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(req.getStyleCodeList());
        if(CollectionUtil.isEmpty(spotSpuList)){
            log.info("=== 现货品spu不存在 ===");
            return;
        }
        for(SpotSpu spotSpu : spotSpuList){

            spotSpuMurmurationService.submitAlibabaDistributionTask(spotSpu);
        }
    }

    /**
     * 处理图包拉取任务转换结果
     */
    @Override
    public void processAiAlibabaDistributionConvertResults(String spuId, AlibabaDistributionOutputDto resultDto) {
        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        RLock lock = redissonClient.getLock(PROCESS_AI_ALIBABA_DISTRIBUTION + spuId);
        boolean isLock = false ;
        try {
            isLock = lock.tryLock(10, TimeUnit.SECONDS);
            if (!isLock) {
                log.info("processAiAlibabaDistributionConvertResults任务进行中{}", spuId);
                return;
            }
            // 获取SPU信息
            SpotSpu spotSpu = spotSpuRepository.getById(spuId);
            if (spotSpu == null) {
                log.warn("处理图包拉取任务结果失败: 未找到styleCode={}", spuId);
                return;
            }

            // 获取SPU详情
            SpotSpuDetail spotSpuDetail = spotSpuDetailRepository.getBySpotSpuId(spotSpu.getSpotSpuId());
            if (spotSpuDetail == null) {
                log.warn("处理图包拉取任务结果失败: 未找到SpotSpuDetail,  spotSpuId={}",
                        spotSpu.getSpotSpuId());
                return;
            }

            // 处理结果并存储
            if (vaildSkc(resultDto, spotSpu, spotSpuDetail)) {
                return;
            }
            // 视觉图包生成
            OnShelfImagePackage onShelfImagePackage = new OnShelfImagePackage();
            onShelfImagePackage.setStyleCode(spotSpu.getStyleCode());
            // spu图
            List<OnShelfImagePackage.SpuImage> spuImages = buildSpuImages(spotSpuDetail, spotSpu, resultDto);
            onShelfImagePackage.setSpuImages(spuImages);
            // skc图
            List<OnShelfImagePackage.SkcImage> skcImageList = buildSkcImages(resultDto);
            onShelfImagePackage.setSkcImages(skcImageList);
            VisualImagePackage visualImagePackage = visualImagePackageRepository.getLatestByStyleCode(onShelfImagePackage.getStyleCode());
            if(visualImagePackage!=null){
                log.warn("图包已存在，styleCode={}已存在,visualImagePackageId={}", onShelfImagePackage.getStyleCode(), visualImagePackage.getPackageId());
                spotSpuDetailRepository.update(new LambdaUpdateWrapper<SpotSpuDetail>()
                        .eq(SpotSpuDetail::getSpotSpuDetailId, spotSpuDetail.getSpotSpuDetailId())
                        .set(SpotSpuDetail::getAiAlibabaDistributionSyncStatus, AiAlibabaDistributionSyncStatusEnum.PACKAGE_EXIST.getCode())
                        .set(SpotSpuDetail::getAiAlibabaDistributionSyncMsg, "图包已存在"));
                return;
            }
            visualImagePackageService.saveImagePackage(onShelfImagePackage);
            spotSpuDetailRepository.update(new LambdaUpdateWrapper<SpotSpuDetail>()
                            .eq(SpotSpuDetail::getSpotSpuDetailId, spotSpuDetail.getSpotSpuDetailId())
                            .set(SpotSpuDetail::getAiAlibabaDistributionSyncStatus, AiAlibabaDistributionSyncStatusEnum.COMPLETE_SYNC.getCode())
                            .set(SpotSpuDetail::getAiAlibabaDistributionSyncMsg, null)
            );

            log.info("保存图包拉取结果成功: potSpuId={}",  spotSpu.getSpotSpuId());
        } catch (Exception e) {
            log.error("处理图包拉取结果异常: potSpuId={}, error={}",
                    spuId, e.getMessage(), e);
        }finally {
            if (isLock && lock.isHeldByCurrentThread() ) {
                lock.unlock();
            }
        }
    }

    private boolean vaildSkc(AlibabaDistributionOutputDto resultDto, SpotSpu spotSpu, SpotSpuDetail spotSpuDetail) {
        if(resultDto.getValid()==null || resultDto.getValid()!=1){
            log.warn("处理图包拉取任务无效: potSpuId={}", spotSpu.getSpotSpuId());
            spotSpuDetailRepository.update(new LambdaUpdateWrapper<SpotSpuDetail>()
                    .eq(SpotSpuDetail::getSpotSpuDetailId, spotSpuDetail.getSpotSpuDetailId())
                    .set(SpotSpuDetail::getAiAlibabaDistributionSyncStatus, AiAlibabaDistributionSyncStatusEnum.INVALID.getCode()));
            return true;
        }
        List<String> designCodeList = resultDto.getSkcInfos().stream().map(AlibabaDistributionOutputDto.SkcInfo::getDesignCode).toList();
        List<SpotSkc> skcList = spotSkcRepository.listByDesignCodes(designCodeList);
        // 判断ai_color_status都是2、3、4，未完成直接退出
        boolean noComplete = skcList.stream().anyMatch(v->!List.of(2,3,4).contains(v.getAiColorStatus()));
        if(noComplete){
            log.warn("处理图包拉取任务结果失败: 存在skc颜色名称为null或颜色任务为空,  spotSpuId={}",
                    spotSpu.getSpotSpuId());
            spotSpuDetailRepository.update(new LambdaUpdateWrapper<SpotSpuDetail>()
                    .eq(SpotSpuDetail::getSpotSpuDetailId, spotSpuDetail.getSpotSpuDetailId())
                    .set(SpotSpuDetail::getAiAlibabaDistributionSyncMsg, "存在skc颜色名称为null或颜色任务为空"));
            return true;
        }
        List<SpotSkc> completeSkcList = skcList.stream().filter(item -> SpotResourceStateEnum.FINISH.getCode().equals(item.getResourceStatus())).toList();
        if(CollUtil.isEmpty(completeSkcList)){
            spotSpuDetailRepository.update(new LambdaUpdateWrapper<SpotSpuDetail>()
                    .eq(SpotSpuDetail::getSpotSpuDetailId, spotSpuDetail.getSpotSpuDetailId())
                    .set(SpotSpuDetail::getAiAlibabaDistributionSyncStatus, AiAlibabaDistributionSyncStatusEnum.ATTRIBUTE_ERROR.getCode())
                    .set(SpotSpuDetail::getAiAlibabaDistributionSyncMsg, "无完成skc"));
        }
        return false;
    }

    @NotNull
    private  List<OnShelfImagePackage.SpuImage> buildSpuImages(SpotSpuDetail spotSpuDetail, SpotSpu spotSpu, AlibabaDistributionOutputDto resultDto) {
        List<OnShelfImagePackage.SpuImage> spuImages = new ArrayList<>();
        // 主图301
        OnShelfImagePackage.SpuImage mainImage = new OnShelfImagePackage.SpuImage();
        mainImage.setImageType(VisualTaskImageTypeEnum.PRODUCT_MAIN.getCode());
        mainImage.setImageTypeDesc(VisualTaskImageTypeEnum.PRODUCT_MAIN.getDesc());
        List<ImageFile> mainImageList = new ArrayList<>();
        int num = 1;
        String spuMainUrl = resultDto.getSpuMainUrl();
        if(StrUtil.isNotBlank(spuMainUrl)) {    //返回了主图就当301，没的话按旧逻辑处理
            ImageFile detailFile = new ImageFile();
            String spuUrlImageType = spuMainUrl.substring(spuMainUrl.lastIndexOf(".") + 1);
            detailFile.setOssImageUrl(spuMainUrl);
            detailFile.setOrgImgName(spotSpu.getStyleCode().concat("-").concat("30" + num).concat(".").concat(spuUrlImageType));
            mainImageList.add(detailFile);
            num++;
        }
        for(int i = 1; i <= resultDto.getSpuUrls().size(); i++,num++){
            ImageFile detailFile = new ImageFile();
            String spuUrl = resultDto.getSpuUrls().get(i - 1);
            detailFile.setOssImageUrl(spuUrl);
            String spuUrlImageType = spuUrl.substring(spuUrl.lastIndexOf(".") + 1);

            // 修改编号生成逻辑，使第10张图片编号为310而不是3010
            String imageNumber;
            if (num <= 9) {
                imageNumber = "30" + num;
            } else {
                imageNumber = "3" + num; // 从310开始: 310, 311, 312...
            }
            detailFile.setOrgImgName(spotSpu.getStyleCode().concat("-").concat(imageNumber).concat(".").concat(spuUrlImageType));
            mainImageList.add(detailFile);
        }
        mainImage.setImages(mainImageList);
        spuImages.add(mainImage);
        // 尺码表
        OnShelfImagePackage.SpuImage sizeChartImage = new OnShelfImagePackage.SpuImage();
        sizeChartImage.setImageType(VisualTaskImageTypeEnum.SIZE_CHART.getCode());
        sizeChartImage.setImageTypeDesc(VisualTaskImageTypeEnum.SIZE_CHART.getDesc());
        ImageFile sizeChartFile = new ImageFile();

        if(CollectionUtil.isNotEmpty(resultDto.getSizechartUrls())){
            sizeChartFile.setOssImageUrl(resultDto.getSizechartUrls().getFirst());
        }else{
            sizeChartFile.setOssImageUrl(sizeDefaultImageUrl);
        }
        String sizeChartFileImageType = sizeChartFile.getOssImageUrl().substring(sizeChartFile.getOssImageUrl().lastIndexOf(".") + 1);

        sizeChartFile.setOrgImgName(spotSpu.getStyleCode().concat("-").concat("401").concat(".").concat(sizeChartFileImageType));
        sizeChartImage.setImages(List.of(sizeChartFile));
        spuImages.add(sizeChartImage);
        // 尺寸图
        OnShelfImagePackage.SpuImage sizeImage = new OnShelfImagePackage.SpuImage();
        sizeImage.setImageType(VisualTaskImageTypeEnum.OTHER.getCode());
        sizeImage.setImageTypeDesc(VisualTaskImageTypeEnum.OTHER.getDesc());
        ImageFile sizeImage888File = new ImageFile();
        sizeImage888File.setOssImageUrl(resultDto.getWhiteBackgroundUrls().getSpuWhitePic11());
        String ImageType11 = sizeImage888File.getOssImageUrl().substring(sizeImage888File.getOssImageUrl().lastIndexOf(".") + 1);
        sizeImage888File.setOrgImgName(spotSpu.getStyleCode().concat("-").concat("888").concat(".").concat(ImageType11));
        ImageFile sizeImage999File = new ImageFile();
        sizeImage999File.setOssImageUrl(resultDto.getWhiteBackgroundUrls().getSpuWhitePic34());
        String ImageType34 = sizeImage999File.getOssImageUrl().substring(sizeImage999File.getOssImageUrl().lastIndexOf(".") + 1);
        sizeImage999File.setOrgImgName(spotSpu.getStyleCode().concat("-").concat("999").concat(".").concat(ImageType34));
        sizeImage.setImages(List.of(sizeImage888File, sizeImage999File));
        spuImages.add(sizeImage);
        return spuImages;
    }
    private List<OnShelfImagePackage.SkcImage> buildSkcImages(AlibabaDistributionOutputDto resultDto) {
        // skc图
        List<OnShelfImagePackage.SkcImage> skcImageList = new ArrayList<>();
        List<String> designCodeList = resultDto.getSkcInfos().stream().map(AlibabaDistributionOutputDto.SkcInfo::getDesignCode).toList();
        List<SpotSkc> skcList = spotSkcRepository.listByDesignCodes(designCodeList);
        // 只推送已完成skc
        List<SpotSkc> completeSkcList = skcList.stream().filter(item -> SpotResourceStateEnum.FINISH.getCode().equals(item.getResourceStatus())).toList();
        Map<String, SpotSkc> skcMap = completeSkcList.stream().collect(Collectors.toMap(SpotSkc::getDesignCode, v -> v, (v1, v2) -> v1));
        for(AlibabaDistributionOutputDto.SkcInfo skcInfo: resultDto.getSkcInfos()){
            SpotSkc skc = skcMap.get(skcInfo.getDesignCode());
            if(skc == null){
                continue;
            }
            OnShelfImagePackage.SkcImage skcImage = new OnShelfImagePackage.SkcImage();
            skcImage.setDesignCode(skcInfo.getDesignCode());
            skcImage.setColor(skc.getColor());
            skcImage.setColorEn(skc.getColorEnglishName() ==null?"" : skc.getColorEnglishName());
            ImageFile skcFile = new ImageFile();
            skcFile.setOssImageUrl(skcInfo.getSkcUrl());
            // 获取skcurl 图片类型
            String imageType = skcInfo.getSkcUrl().substring(skcInfo.getSkcUrl().lastIndexOf(".") + 1);
            skcFile.setOrgImgName((skc.getColorEnglishName() ==null?"" : skc.getColorEnglishName()).concat(".").concat(imageType));
            skcImage.setImages(List.of(skcFile));
            skcImageList.add(skcImage);
        }
        return skcImageList;
    }
}
