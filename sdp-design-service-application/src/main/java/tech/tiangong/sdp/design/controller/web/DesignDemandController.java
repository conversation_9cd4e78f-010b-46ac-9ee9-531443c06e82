package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.excel.design_demand.DesignDemandListener;
import tech.tiangong.sdp.design.service.DesignDemandImportService;
import tech.tiangong.sdp.design.service.DesignDemandService;
import tech.tiangong.sdp.design.vo.query.DesignDemandQuery;
import tech.tiangong.sdp.design.vo.req.DesignDemandImportReq;
import tech.tiangong.sdp.design.vo.req.demand.DesignDemandAllocateReq;
import tech.tiangong.sdp.design.vo.req.demand.DesignDemandCreateSpuReq;
import tech.tiangong.sdp.design.vo.req.demand.DesignDemandNoPassReq;
import tech.tiangong.sdp.design.vo.req.demand.DesignDemandReAllocateReq;
import tech.tiangong.sdp.design.vo.resp.demand.DesignDemandDetailInfoVo;
import tech.tiangong.sdp.design.vo.resp.demand.DesignDemandPageVo;
import tech.tiangong.sdp.design.vo.resp.demand.SuggestedMaterialDetailVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 灵感设计需求-web
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/design-demand")
public class DesignDemandController extends BaseController {
    private final DesignDemandService designDemandService;
    private final DesignDemandImportService designDemandImportService;

    /**
     * 查询列表（分页）
     *
     * @param queryDTO 分页对象
     * @return PageRespVo<DesignDemandVo>
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<DesignDemandPageVo>> page(@RequestBody @Validated DesignDemandQuery queryDTO) {
        return DataResponse.ok(designDemandService.page(queryDTO));
    }


    /**
     * 详情
     *
     * @param designDemandId 设计需求id
     * @return 响应结果
     */
    @GetMapping("/detail/{designDemandId}")
    public DataResponse<DesignDemandDetailInfoVo> getDetailById(@PathVariable(value = "designDemandId") Long designDemandId) {
        return DataResponse.ok(designDemandService.getDetailById(designDemandId));
    }

    /**
     * 查询推荐面料
     *
     * @param designDemandId 设计需求id
     * @return 响应结果
     */
    @GetMapping("/suggest-material/{designDemandId}")
    public DataResponse<List<SuggestedMaterialDetailVo>> listSuggestMaterial(@PathVariable(value = "designDemandId") Long designDemandId) {
        return DataResponse.ok(designDemandService.listSuggestMaterial(designDemandId));
    }

    /**
     * 任务分配
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/allocate")
    public DataResponse<Void> allocate(@RequestBody @Validated DesignDemandAllocateReq req) {
        designDemandService.allocate(req);
        return DataResponse.ok();
    }

    /**
     * 分配变更
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/re-allocate")
    public DataResponse<Void> reAllocateUpdate(@RequestBody @Validated DesignDemandReAllocateReq req) {
        designDemandService.reAllocateUpdate(req);
        return DataResponse.ok();
    }

    /**
     * 淘汰
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/no-pass")
    public DataResponse<Void> noPass(@RequestBody @Validated DesignDemandNoPassReq req) {
        designDemandService.noPass(req);
        return DataResponse.ok();
    }


    /**
     * 开款
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/create-spu")
    public DataResponse<Void> createSpu(@RequestBody @Validated DesignDemandCreateSpuReq req) {
        designDemandService.createSpu(req);
        return DataResponse.ok();
    }

    /**
     * 导入Excel
     */
    @PostMapping("/import")
    public DataResponse<DesignDemandListener.ProcessResult> importDesignDemand(@RequestBody DesignDemandImportReq req) {
        DesignDemandListener.ProcessResult result = designDemandImportService.importDesignDemand(req.getUrl());
        return DataResponse.ok(result);
    }

    @GetMapping("/export/template")
    public DataResponse<Void> exportDesignDemand(HttpServletResponse response) {
        designDemandImportService.exportExcelToResponse(response);
        return DataResponse.ok();
    }


}
