package tech.tiangong.sdp.design.converter;

import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.BeanUtils;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.BomOrderMaterial;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.vo.dto.bom.CraftNotificationDto;
import tech.tiangong.sdp.design.vo.dto.bom.ReplenishPreCuttingCraftDto;
import tech.tiangong.sdp.design.vo.dto.bom.SyncCraftClothesDto;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class NotificationConverter {

    public static List<CraftNotificationDto> buildCraftNotificationDto(List<CraftDemandInfo> addCraftDemands) {
        if (CollectionUtil.isEmpty(addCraftDemands)) {
            return Collections.emptyList();
        }
        List<CraftNotificationDto> craftNotificationDtos = addCraftDemands.stream().map(craftDemandInfo -> {
            CraftNotificationDto craftNotificationDto = new CraftNotificationDto();
            BeanUtils.copyProperties(craftDemandInfo, craftNotificationDto);
            return craftNotificationDto;
        }).collect(Collectors.toList());
        return craftNotificationDtos;
    }


    public static ReplenishPreCuttingCraftDto buildReplenishPreCuttingCraftDto(List<CraftDemandInfo> addCraftDemands, BomOrder bomOrder, final Map<Long, BomOrderMaterial> bomOrderMaterialMap) {
        if (CollectionUtil.isEmpty(addCraftDemands)) {
            return null;
        }

        List<ReplenishPreCuttingCraftDto.Craft> craftNotificationDtos = addCraftDemands.stream()
                .map(craftDemandInfo -> {
                    ReplenishPreCuttingCraftDto.Craft craft = new ReplenishPreCuttingCraftDto.Craft();
                    BeanUtils.copyProperties(craftDemandInfo, craft);
                    Long materialSnapshotId = null;
                    if (CollectionUtil.isNotEmpty(bomOrderMaterialMap)) {
                        materialSnapshotId = Optional.ofNullable(bomOrderMaterialMap.get(craftDemandInfo.getBomMaterialId())).map(BomOrderMaterial::getMaterialSnapshotId).orElse(null);
                    }
                    craft.setMaterialSnapshotId(materialSnapshotId);
                    return craft;
                }).collect(Collectors.toList());
        return ReplenishPreCuttingCraftDto.builder().bomOrder(bomOrder).crafts(craftNotificationDtos).build();
    }


    public static SyncCraftClothesDto buildSyncCraftClothesDto(List<CraftDemandInfo> addCraftDemands, BomOrder bomOrder) {
        if (CollectionUtil.isEmpty(addCraftDemands)) {
            return null;
        }

        List<SyncCraftClothesDto.Craft> craftNotificationDtos = addCraftDemands.stream()
                .map(craftDemandInfo -> {
                    SyncCraftClothesDto.Craft craft = new SyncCraftClothesDto.Craft();
                    BeanUtils.copyProperties(craftDemandInfo, craft);
                    return craft;
                }).collect(Collectors.toList());
        return SyncCraftClothesDto.builder().bomOrder(bomOrder).crafts(craftNotificationDtos).build();
    }


}
