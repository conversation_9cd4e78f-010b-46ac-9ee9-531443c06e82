package tech.tiangong.sdp.design.communication;

import kotlin.Unit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import team.aikero.blade.core.protocol.DataResponse;
import team.aikero.murmuration.common.req.AddCommunicationStyleImageRequest;
import team.aikero.murmuration.sdk.client.CommunicationStyleImageClient;
import tech.tiangong.sdp.design.communication.mapper.CommunicationStyleImagePushResultMapper;
import tech.tiangong.sdp.utils.UserHolderUtil;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 货通款式图库推送定时任务服务
 *
 * <AUTHOR>
 * @since 2025-09-16
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/jobs/communication-style-image")
public class CommunicationStyleImageJobController {

    private final CommunicationStyleImagePushResultMapper communicationStyleImagePushResultMapper;
    private final Environment environment;
    private final RedissonClient redisson;
    private final CommunicationStyleImageClient communicationStyleImageClient;

    /**
     * 定时任务-推送货通款式图库
     */
    @PostMapping("/push")
    public void push() {
        RLock lock = redisson.getLock("communication-style-image:push");
        if (!lock.tryLock()) {
            log.warn("货通款式图库推送任务正在执行中");
            return;
        }

        UserHolderUtil.setSystemUserContent();

        try {
            int batchSize = environment.getProperty("communication-style-image.push.batch-size", Integer.class, 500);
            List<CommunicationSpu> spuList = communicationStyleImagePushResultMapper.findUnPushedSpuList(batchSize);
            log.info("货通款式图库推送任务开始执行, 本次处理记录数={}", spuList.size());
            Instant start = Instant.now();
            
            for (CommunicationSpu spu : spuList) {
                CommunicationStyleImagePushResult result = new CommunicationStyleImagePushResult();
                result.setSpuCode(spu.getStyleCode());
                result.setCreatedTime(LocalDateTime.now());
                try {
                    doPush(spu);
                    result.setStatus(PushStatus.DONE);
                } catch (PushFailedException ex) {
                    result.setStatus(PushStatus.FAILED);
                    String truncatedMessage = StringUtils.truncate(ex.getMessage(), 200);
                    result.setFailedReason(truncatedMessage);
                }
                communicationStyleImagePushResultMapper.insert(result);
            }
            
            log.info("货通款式图库推送任务执行完成, 本次处理耗时={}s", Duration.between(start, Instant.now()).toSeconds());
        } catch (Throwable ex) {
            log.error("货通款式图库推送任务执行失败: {}", ex.getMessage(), ex);
            throw ex;
        } finally {
            UserHolderUtil.clean();
            lock.unlock();
        }
    }

    /**
     * 批量删除推送结果
     */
    @PostMapping("/batch-delete-push-results")
    public void batchDeletePushResults(@RequestBody List<String> spuCodes) {
        communicationStyleImagePushResultMapper.deleteByIds(spuCodes);
    }

    private void doPush(CommunicationSpu spu) throws PushFailedException {
        // 校验
        if (StringUtils.isEmpty(spu.getSupplyModeCode())) {
            throw new PushFailedException("供给方式为空");
        }
        if (StringUtils.isEmpty(spu.getCategory())) {
            throw new PushFailedException("品类为空");
        }

        // 调用 murmuration SDK
        AddCommunicationStyleImageRequest request = new AddCommunicationStyleImageRequest(
                spu.getMainUrl(),
                spu.getStyleCode(),
                spu.getSupplyModeCode(),
                spu.getCategory()
        );
        
        DataResponse<Unit> response = communicationStyleImageClient.add(request);
        if (!response.getSuccessful()) {
            throw new PushFailedException(response.getMessage());
        }
    }

    static class PushFailedException extends Exception {
        public PushFailedException(String message) {
            super(message);
        }
    }
}
