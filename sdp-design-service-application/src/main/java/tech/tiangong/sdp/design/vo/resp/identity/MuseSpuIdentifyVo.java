package tech.tiangong.sdp.design.vo.resp.identity;


import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * muse款式识别 Vo
 *
 * <AUTHOR>
 * @since 2025-02-25 11:37:13
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
public class MuseSpuIdentifyVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 识别url
     */
    private String museIdentifyUrl;

    /**
     * muse同款-识别等级(0/1/2)
     */
    private Integer museSameStyleLevel;

    /**
     * muse同款-识别状态: 0-待发起; 1-识别中; 2-成功; 3-失败;
     */
    private Integer museSameStyleIdentifyState;

    /**
     * muse侵权-识别等级(0/1/2)
     */
    private Integer museInfringementLevel;

    /**
     * muse侵权-识别状态: 0-待发起; 1-识别中; 2-成功; 3-失败;
     */
    private Integer museInfringementIdentifyState;

}
