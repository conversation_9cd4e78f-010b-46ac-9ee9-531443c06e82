package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.CraftDemandInfoTransient;
import tech.tiangong.sdp.design.mapper.CraftDemandInfoTransientMapper;
import tech.tiangong.sdp.design.vo.query.bom.CraftDemandInfoTransientQuery;
import tech.tiangong.sdp.design.vo.resp.bom.CraftDemandInfoTransientVo;
import tech.tiangong.sdp.material.enums.Bool;

import java.util.List;
import java.util.Objects;

/**
 * 工艺需求_暂存表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class CraftDemandInfoTransientRepository extends BaseRepository<CraftDemandInfoTransientMapper, CraftDemandInfoTransient> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<CraftDemandInfoTransientVo> findPage(CraftDemandInfoTransientQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public List<CraftDemandInfoTransient> listByBomTransientId(Long bomTransientId) {
        if (Objects.isNull(bomTransientId)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(CraftDemandInfoTransient::getBomTransientId, bomTransientId)
                .eq(CraftDemandInfoTransient::getIsDeleted, Bool.NO.getCode())
                .list();
    }

    public List<CraftDemandInfoTransient> listByBomId(Long bomId) {
        if (Objects.isNull(bomId)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(CraftDemandInfoTransient::getBomId, bomId)
                .eq(CraftDemandInfoTransient::getIsDeleted, Bool.NO.getCode())
                .list();
    }



    public List<CraftDemandInfoTransient> listByTransientMaterialIds(List<Long> transientMaterialIdList) {
        if (Objects.isNull(transientMaterialIdList)) {
            return List.of();
        }
        return lambdaQuery()
                .in(CraftDemandInfoTransient::getBomMaterialId, transientMaterialIdList)
                .list();
    }
}
