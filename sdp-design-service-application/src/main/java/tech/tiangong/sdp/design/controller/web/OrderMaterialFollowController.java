package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.OrderMaterialFollowService;
import tech.tiangong.sdp.design.vo.req.ordermaterial.MaterialOrderToCraftReq;
import tech.tiangong.sdp.design.vo.req.ordermaterial.OrderMaterialFollowPageReq;
import tech.tiangong.sdp.design.vo.resp.material.MaterialPurchaseFollowVo;
import tech.tiangong.sdp.design.vo.resp.ordermaterial.MaterialDemandVo;
import tech.tiangong.sdp.design.vo.resp.ordermaterial.OrderMaterialFollowPageVo;

import java.util.List;

/**
 *
 * 采购齐套管理-web
 * <br>CreateDate August 10,2021
 * <AUTHOR>
 * @since 1.0
 */
@RestController
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/order/material")
public class OrderMaterialFollowController extends BaseController {

    @Autowired
    private OrderMaterialFollowService orderMaterialFollowService;


    /**
     * 采购齐套管理列表
     *
     * @param req 物料齐套跟进查询参数
     * @return PageRespVo<OrderMaterialFollowPageVo>
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<OrderMaterialFollowPageVo>> pageList(@RequestBody OrderMaterialFollowPageReq req){
        return DataResponse.ok(orderMaterialFollowService.pageList(req));
    }

    /**
     * 扫码管理--齐套签收  -- 齐套签收列表查询
     *
     * @param designCode 设计款号  以设计款号的维度进行签收
     * @return List<MaterialPurchaseFollow>
     */
    @GetMapping("/sign-material-list/{designCode}")
    public DataResponse<List<MaterialPurchaseFollowVo>> allSetSignMaterialList(@PathVariable String designCode){
        return DataResponse.ok(orderMaterialFollowService.allSetSignMaterialList(designCode));
    }


    /**
     * 齐套签收
     *
     * @param designCode 设计款号  以设计款号的维度进行签收
     * @return
     */
    @GetMapping("/sign/{designCode}")
    public DataResponse<Void> sign(@PathVariable String designCode){
        orderMaterialFollowService.sign(designCode);
        return DataResponse.ok();
    }

    /**
     * 查询剪版单或者齐套单下的工艺信息|
     *
     * @param req 请求参数
     * @return
     */
    @PostMapping("/material-order-craft")
    public DataResponse<List<MaterialDemandVo>> materialOrderToCraft(@RequestBody @Validated MaterialOrderToCraftReq req){

        return DataResponse.ok(orderMaterialFollowService.materialOrderToCraft(req));
    }

}
