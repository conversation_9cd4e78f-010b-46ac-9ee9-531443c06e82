package tech.tiangong.sdp.design.repository;

import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.PrototypeSampleRecode;
import tech.tiangong.sdp.design.mapper.PrototypeSampleRecodeMapper;

import java.util.Collections;
import java.util.List;

/**
 * SKC_打版记录表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class PrototypeSampleRecodeRepository extends BaseRepository<PrototypeSampleRecodeMapper, PrototypeSampleRecode> {

    public List<PrototypeSampleRecode> getByDesignCode(String designCode) {
        if (StrUtil.isBlank(designCode)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(PrototypeSampleRecode::getDesignCode, designCode)
                .list();
    }
}
