package tech.tiangong.sdp.design.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.design.listener.entity.AIBoxTaskNotification;
import tech.tiangong.sdp.design.listener.entity.Tag;
import tech.tiangong.sdp.utils.UserHolderUtil;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = "${AIBox.callback.topic}",
        consumerGroup = "${AIBox.callback.group}",
        tag = "*"
)
public class AIBoxTaskListener implements RocketMQListener {

    private final ObjectMapper objectMapper;
    private final AIBoxMessageProcessor messageProcessor;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        try {
            // 设置系统用户信息
            UserHolderUtil.setSystemUserContent();
            
            log.info("接收到AI Box消息, keys: {}", messageView.getKeys());

            Tag tag = extractTag(messageView);
            if (tag == Tag.NONE) {
                return ConsumeResult.SUCCESS;
            }

            // 解析消息内容
            AIBoxTaskNotification taskNotification = parseMessage(messageView);


            log.info("处理AI Box任务: taskId={}, tag={}, status={}", 
                taskNotification.aiBoxTaskId(), tag, taskNotification.status());
            
            // 委托给消息处理器处理
            return messageProcessor.processMessage(tag, taskNotification);
            
        } catch (Exception ex) {
            log.error("处理AI Box消息失败: {}", ex.getMessage(), ex);
            return ConsumeResult.FAILURE;
        }
    }
    
    private AIBoxTaskNotification parseMessage(MessageView messageView) throws Exception {
        String body = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.debug("AI Box消息体: {}", body);
        return objectMapper.readValue(body, AIBoxTaskNotification.class);
    }
    
    private Tag extractTag(MessageView messageView) {
        try {
            return messageView.getTag()
                    .map(Tag::valueOf)
                    .orElse(Tag.NONE);
        } catch (IllegalArgumentException ex) {
            return Tag.NONE;
        }
    }
}