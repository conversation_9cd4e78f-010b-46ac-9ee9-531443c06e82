package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.SpotSkcDetail;
import tech.tiangong.sdp.design.mapper.SpotSkcDetailMapper;

import java.util.Collections;
import java.util.List;

/**
 * spot_skc_detail表(SpotSkcDetail)服务仓库类
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:40
 */
@Repository
public class SpotSkcDetailRepository extends BaseRepository<SpotSkcDetailMapper, SpotSkcDetail> {

    public SpotSkcDetail getBySpotSkcId(Long spotSkcId) {
        if (spotSkcId == null) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<SpotSkcDetail>()
                .eq(SpotSkcDetail::getSpotSkcId,spotSkcId),false);
    }

    public List<SpotSkcDetail> listBySkcIds(List<Long> skcIdList) {
        if (CollUtil.isEmpty(skcIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(SpotSkcDetail::getSpotSkcId, skcIdList).list();
    }
}
