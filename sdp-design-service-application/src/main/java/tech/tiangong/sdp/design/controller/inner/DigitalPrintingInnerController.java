package tech.tiangong.sdp.design.controller.inner;


import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.core.aop.ResetCurrentUser;
import tech.tiangong.sdp.design.service.DigitalPrintingStyleService;
import tech.tiangong.sdp.design.vo.req.digital.*;
import tech.tiangong.sdp.design.vo.resp.digital.DpStyleAddVo;

import java.util.List;


/**
 * 数码印花款-inner
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/digital-printing")
public class DigitalPrintingInnerController extends BaseController {

    private final DigitalPrintingStyleService digitalPrintingStyleService;

    /**
     * 批量创建数码印花款
     *
     * @param req 请求参数对象
     * @return spu维度创建, 创建失败则不返回对应元素
     */
    @NoRepeatSubmitLock
    @PostMapping("/batch-add")
    public DataResponse<List<DpStyleAddVo>> batchAdd(@RequestBody @Validated DpStyleBatchAddReq req) {
        UserContent userContent = UserContentHolder.get();
        log.info("批量创建数码印花款 userContent :{}", JSON.toJSONString(userContent));
        return DataResponse.ok(digitalPrintingStyleService.batchAdd(req));
    }

    /**
     * 单个创建数码印花款
     *
     * @param req 请求参数对象
     * @return spu维度创建, 创建失败则不返回对应元素
     */
    @NoRepeatSubmitLock
    @PostMapping("/save")
    public DataResponse<DpStyleAddVo> save(@RequestBody @Validated DpStyleAddReq req) {
        UserContent userContent = UserContentHolder.get();
        log.info("创建数码印花款 userContent :{}", JSON.toJSONString(userContent));
        return DataResponse.ok(digitalPrintingStyleService.save(req));
    }

    /**
     * 更新生产资料
     *
     * @param req 入参
     * @return void
     */
    @NoRepeatSubmitLock
    @PostMapping("/product-file/update")
    public DataResponse<Void> updateProductFile(@RequestBody @Validated DpProductFileUpdateReq req) {
        digitalPrintingStyleService.updateProductFile(req);
        return DataResponse.ok();
    }

    /**
     * 更新商品推送pop状态
     *
     * @param req 入参
     * @return void
     */
    @NoRepeatSubmitLock
    @PostMapping("/update/push-state")
    public DataResponse<Void> updatePushState(@RequestBody @Validated DpPushStateUpdateReq req) {
        digitalPrintingStyleService.updatePushState(req);
        return DataResponse.ok();
    }

    /**
     * 根据spu与状态推送pop(刷数接口)
     *
     * @return 响应结果
     */
    @ResetCurrentUser
    @PostMapping("/push-fail-by-spu")
    @NoRepeatSubmitLock(lockTime = 10L)
    public DataResponse<Void> pushFailBySpu(@RequestBody @Validated DpPushPopReq req) {
        digitalPrintingStyleService.pushFailBySpu(req);
        return DataResponse.ok();
    }

    /**
     * 修正推送状态(刷数接口)
     *
     *  已经成功推送到pop,但推送状态还不是成功的
     * @return 响应结果
     */
    @ResetCurrentUser
    @PostMapping("/fix-fail-state")
    @NoRepeatSubmitLock(lockTime = 10L)
    public DataResponse<Void> fixFailState(@RequestBody @Validated DpPushStateFixReq req) {
        digitalPrintingStyleService.fixFailState(req);
        return DataResponse.ok();
    }



}