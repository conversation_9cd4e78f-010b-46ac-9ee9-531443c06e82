package tech.tiangong.sdp.design.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tech.tiangong.sdp.design.entity.CraftDemandInfoTransient;
import tech.tiangong.sdp.design.vo.query.bom.CraftDemandInfoTransientQuery;
import tech.tiangong.sdp.design.vo.resp.bom.CraftDemandInfoTransientVo;

/**
 * 工艺需求_暂存表数据库访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface CraftDemandInfoTransientMapper extends BaseMapper<CraftDemandInfoTransient> {

    /**
     * 分页查询
     *
     * @param page  分页
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<CraftDemandInfoTransientVo> findPage(@Param("page") Page<CraftDemandInfoTransientVo> page, @Param("query") CraftDemandInfoTransientQuery query);

}
