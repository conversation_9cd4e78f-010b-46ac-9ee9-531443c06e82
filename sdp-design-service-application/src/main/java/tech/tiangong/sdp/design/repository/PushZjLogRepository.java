package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.PushZjLog;
import tech.tiangong.sdp.design.mapper.PushZjLogMapper;
import tech.tiangong.sdp.design.vo.query.PushZjLogQuery;
import tech.tiangong.sdp.design.vo.resp.PushZjLogVo;

import java.util.Collections;
import java.util.List;

/**
 * 数据推送致景记录表服务仓库类
 *
 * <AUTHOR>
 */

@AllArgsConstructor
@Slf4j
@Repository
public class PushZjLogRepository extends BaseRepository<PushZjLogMapper, PushZjLog> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<PushZjLogVo> findPage(PushZjLogQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public List<PushZjLog> listByBizCodes(List<String> bizCodeList) {
        if (CollUtil.isEmpty(bizCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(PushZjLog::getBizCode, bizCodeList).list();
    }

    public List<PushZjLog> listByBizCodesAndType(List<String> bizCodeList, List<Integer> pushTypeList) {
        if (CollUtil.isEmpty(bizCodeList) || CollUtil.isEmpty(pushTypeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(PushZjLog::getBizCode, bizCodeList)
                .in(PushZjLog::getPushType, pushTypeList)
                .list();
    }
}
