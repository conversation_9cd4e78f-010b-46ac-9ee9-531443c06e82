package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.design.entity.VisualTaskDetail;
import tech.tiangong.sdp.design.entity.VisualTaskNodeState;
import tech.tiangong.sdp.design.entity.VisualTaskOnShelf;
import tech.tiangong.sdp.design.entity.VisualTaskTryOn;
import tech.tiangong.sdp.design.enums.visual.VisualTaskNodeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskStepEnum;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.VisualTaskNodeStateService;
import tech.tiangong.sdp.design.vo.req.visual.DeleteVisualTaskNodeStepReq;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualTaskNodeStepStateReq;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 视觉任务-环节节点状态服务接口
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VisualTaskNodeStateServiceImpl implements VisualTaskNodeStateService {
    private final VisualTaskNodeLogRepository visualTaskNodeLogRepository;
    private final VisualTaskNodeStateRepository visualTaskNodeStateRepository;
    private final VisualTaskDetailRepository visualTaskDetailRepository;
    private final VisualTaskTryOnRepository visualTaskTryOnRepository;
    private final VisualTaskOnShelfRepository visualTaskOnShelfRepository;

    public void updateHandleState(List<Long> taskIds){
        List<VisualTaskDetail> details = visualTaskDetailRepository.listByTaskIds(taskIds);
        details.forEach(detail -> {
            if(detail.getLatestTryOnDetailId()==null){
                visualTaskNodeStateRepository.deleteNodeState(detail.getTaskId(),VisualTaskStepEnum.HANDLE,
                        VisualTaskNodeEnum.TRYON_HANDLE);
            }else{
                VisualTaskTryOn visualTaskTryOn = visualTaskTryOnRepository.getById(detail.getLatestTryOnDetailId());
                visualTaskNodeStateRepository.changeNodeState(detail.getTaskId(),VisualTaskStepEnum.HANDLE,
                        VisualTaskNodeEnum.TRYON_HANDLE,visualTaskTryOn.getHandleState());
            }

            if(detail.getLatestOnShelfDetailId()==null){
                visualTaskNodeStateRepository.deleteNodeState(detail.getTaskId(),VisualTaskStepEnum.HANDLE,
                        VisualTaskNodeEnum.ON_SHELF_HANDLE);
            }else{
                VisualTaskOnShelf visualTaskOnShelf = visualTaskOnShelfRepository.getById(detail.getLatestOnShelfDetailId());
                visualTaskNodeStateRepository.changeNodeState(detail.getTaskId(),VisualTaskStepEnum.HANDLE,
                        VisualTaskNodeEnum.ON_SHELF_HANDLE,visualTaskOnShelf.getHandleState());
            }
        });
    }

    @Override
    public void saveVisualTaskNodeStepStates(List<SaveVisualTaskNodeStepStateReq> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        list.forEach(req -> {
            saveVisualTaskNodeStepState(req.getVisualTaskId(),
                    req.getVisualTaskStep(),
                    req.getVisualTaskNode(),
                    req.getState());
        });
    }

    @Override
    public void saveVisualTaskNodeStepState(Long visualTaskId,
                                            VisualTaskStepEnum visualTaskStep,
                                            VisualTaskNodeEnum visualTaskNode,
                                            Integer state){
        //保存环节节点状态
        visualTaskNodeStateRepository.changeNodeState(visualTaskId,
                visualTaskStep, visualTaskNode,state);

        //记录环节节点状态日志
        visualTaskNodeLogRepository.saveNodeLog(visualTaskId,
                visualTaskStep, visualTaskNode,state);
    }

    public VisualTaskNodeState getVisualTaskNodeStepState(Long visualTaskId,
                                                          VisualTaskStepEnum visualTaskStep,
                                                          VisualTaskNodeEnum visualTaskNode){
        //保存环节节点状态
        return visualTaskNodeStateRepository.getOne(new LambdaQueryWrapper<VisualTaskNodeState>()
                        .eq(VisualTaskNodeState::getTaskId, visualTaskId)
                        .eq(VisualTaskNodeState::getProcessStep,visualTaskStep.getCode())
                        .eq(VisualTaskNodeState::getProcessNode,visualTaskNode.getCode())
        ,false);
    }

    public void deleteVisualTaskNodeStepState(Long visualTaskId,
                                              VisualTaskStepEnum visualTaskStep,
                                              VisualTaskNodeEnum visualTaskNode){
        //保存环节节点状态
        visualTaskNodeStateRepository.deleteNodeState(visualTaskId,
                visualTaskStep, visualTaskNode);
    }

    public void deleteVisualTaskNodeStepStates(Long visualTaskId,
                                               VisualTaskStepEnum visualTaskStep,
                                                 List<VisualTaskNodeEnum> visualTaskNodes){
        //保存环节节点状态
        visualTaskNodeStateRepository.deleteNodeStates(visualTaskId,
                visualTaskStep, visualTaskNodes);
    }

    public void deleteVisualTaskNodeStep(Long visualTaskId,
                                  VisualTaskStepEnum visualTaskStep){
        visualTaskNodeStateRepository.deleteStep(Collections.singletonList(visualTaskId),visualTaskStep);
    }

    public void deleteVisualTaskNodeStepList(List<DeleteVisualTaskNodeStepReq> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        list.forEach(req -> {
            deleteVisualTaskNodeStep(req.getVisualTaskId(),req.getVisualTaskStep());
        });
    }
}