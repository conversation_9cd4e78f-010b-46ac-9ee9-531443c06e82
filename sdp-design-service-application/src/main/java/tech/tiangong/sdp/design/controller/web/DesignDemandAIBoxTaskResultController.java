package tech.tiangong.sdp.design.controller.web;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.DesignDemandAIBoxTaskResultService;

@RestController
@RequestMapping("/ai-box/result")
@RequiredArgsConstructor
public class DesignDemandAIBoxTaskResultController {

    private final DesignDemandAIBoxTaskResultService designDemandAIBoxTaskResultService;

    @PatchMapping("/{id}")
    public void update(@PathVariable Long id, @RequestParam Boolean passed) {
        designDemandAIBoxTaskResultService.passDesignDemandAIBoxTaskResult(id,passed);
    }
}
