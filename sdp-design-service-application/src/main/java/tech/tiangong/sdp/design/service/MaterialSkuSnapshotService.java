package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.query.MaterialSkuSnapshotQuery;
import tech.tiangong.sdp.design.vo.req.material.MaterialSkuSnapshotReq;
import tech.tiangong.sdp.design.vo.resp.material.MaterialSkuSnapshotVo;

/**
 * 好料网sku快照表服务接口
 *
 * <AUTHOR>
 */
public interface MaterialSkuSnapshotService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<MaterialSkuSnapshotVo> page(MaterialSkuSnapshotQuery query);

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    MaterialSkuSnapshotVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(MaterialSkuSnapshotReq req);

}
