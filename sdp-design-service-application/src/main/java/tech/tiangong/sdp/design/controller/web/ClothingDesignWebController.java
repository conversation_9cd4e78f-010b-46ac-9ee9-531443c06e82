package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.ClothingDesignService;
import tech.tiangong.sdp.design.vo.req.clothingDesign.ClothingDesignPrototypeDetailBatchQuery;
import tech.tiangong.sdp.design.vo.req.clothingDesign.ClothingDesignPrototypeDetailQuery;
import tech.tiangong.sdp.design.vo.req.clothingDesign.ClothingDesignPrototypeListQuery;
import tech.tiangong.sdp.design.vo.req.clothingDesign.ClothingDesignStyleListQuery;
import tech.tiangong.sdp.design.vo.resp.clothingDesign.ClothingDesignPrototypeListVo;
import tech.tiangong.sdp.design.vo.resp.clothingDesign.ClothingDesignStyleListVo;
import tech.tiangong.sdp.design.vo.resp.clothingDesign.prototype.ClothingDesignPrototypeDetailInfoVo;


/**
 * PLM设计-统一公共接口管理-web
 *
 * <AUTHOR>
 * @since 2022-08-23 10:49:10
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/clothing-design")
public class ClothingDesignWebController {
    private final ClothingDesignService clothingDesignService;

    /**
     * 查询SPU列表（分页）
     *
     *  <p>分页数量最大值为1000</p>
     *
     * @param queryDTO 分页对象
     * @return PageRespVo<ClothingDesignStyleListVo>
     */
    @PostMapping("/spu/list")
    public DataResponse<PageRespVo<ClothingDesignStyleListVo>> querySpuList(@RequestBody @Validated ClothingDesignStyleListQuery queryDTO) {
        return DataResponse.ok(clothingDesignService.querySpuList(queryDTO));
    }

    /**
     * 查询SKC列表（分页）
     *
     *  <p>分页数量最大值为1000</p>
     *
     * @param queryDTO 分页对象
     * @return PageRespVo<ClothingDesignPrototypeListVo>
     */
    @PostMapping("/skc/list")
    public DataResponse<PageRespVo<ClothingDesignPrototypeListVo>> querySkcList(@RequestBody @Validated ClothingDesignPrototypeListQuery queryDTO) {
        return DataResponse.ok(clothingDesignService.querySkcList(queryDTO));
    }


    /**
     * 批量查询SKC详情（分页）
     *
     *  <p>分页数量最大值为1000</p>
     *
     * @param queryDTO 分页对象
     * @return PageRespVo<ClothingDesignPrototypeDetailInfoVo>
     */
    @PostMapping("/batch/skc/detail")
    public DataResponse<PageRespVo<ClothingDesignPrototypeDetailInfoVo>> queryBatchSkcDetail(@RequestBody @Validated ClothingDesignPrototypeDetailBatchQuery queryDTO) {
        return DataResponse.ok(clothingDesignService.queryBatchSkcDetail(queryDTO));
    }

    /**
     * 查询单个SKC详情
     *
     * @param queryDTO 分页对象
     * @return ClothingDesignPrototypeDetailInfoVo
     */
    @PostMapping("/skc/detail")
    public DataResponse<ClothingDesignPrototypeDetailInfoVo> querySkcDetail(@RequestBody @Validated ClothingDesignPrototypeDetailQuery queryDTO) {
        return DataResponse.ok(clothingDesignService.querySkcDetail(queryDTO));
    }
}
