package tech.tiangong.sdp.design.listener.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.springframework.stereotype.Component;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.Ability;
import tech.tiangong.sdp.design.enums.TaskStatus;
import tech.tiangong.sdp.design.enums.visual.VisualTaskHandleStateEnum;
import tech.tiangong.sdp.design.listener.AIBoxTaskHandler;
import tech.tiangong.sdp.design.listener.entity.Tag;
import tech.tiangong.sdp.design.listener.entity.AIBoxMQTaskResult;
import tech.tiangong.sdp.design.listener.entity.AIBoxTaskNotification;
import tech.tiangong.sdp.design.listener.entity.DifyTryOnAttribute;
import tech.tiangong.sdp.design.repository.*;

import java.util.List;
import java.util.Objects;

/**
 * 视觉质检处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VisualQcHandler implements AIBoxTaskHandler {

    private final VisualTaskRepository visualTaskRepository;
    private final VisualQcRepository visualQcRepository;
    private final VisualQcAiBoxImageRepository visualQcAiBoxImageRepository;
    private final VisualQcAiBoxLogRepository visualQcAiBoxLogRepository;

    @Override
    public Tag getSupportedTag() {
        return Tag.VISUAL_QC;
    }

    @Override
    public void handleTaskCreation(AIBoxTaskNotification taskNotification) {
        log.info("创建视觉质检任务: taskId={}", taskNotification.aiBoxTaskId());

        String[] taskSourceParts = parseTaskSourceId(taskNotification.taskSourceId());
        if (taskSourceParts == null) {
            return;
        }

        String qcRecordId = taskSourceParts[0];
        VisualQc visualQc = visualQcRepository.getById(qcRecordId);
        VisualTask visualTask = visualTaskRepository.getById(visualQc.getTaskId());

        VisualQcAiBoxLog logEntity = buildAiBoxLog(taskNotification, visualQc, visualTask);
        visualQcAiBoxLogRepository.save(logEntity);

        log.info("视觉质检任务创建成功: taskId={}", taskNotification.aiBoxTaskId());

    }

    @Override
    public void handleTaskUpdate(AIBoxTaskNotification taskNotification) {
        log.info("更新视觉质检任务: taskId={}, status={}",
                taskNotification.aiBoxTaskId(), taskNotification.status());

        // 更新任务状态
        updateTaskStatus(taskNotification);

        // 保存结果图片
        if (taskNotification.status() == TaskStatus.COMPLETED) {
            saveTaskResults(taskNotification);
        }

        log.info("视觉质检任务更新成功: taskId={}", taskNotification.aiBoxTaskId());

    }

    private String[] parseTaskSourceId(String taskSourceId) {
        String[] parts = taskSourceId.split(":", 2);
        if (parts.length != 2) {
            log.error("视觉质检任务源ID格式错误: {}", taskSourceId);
            return null;
        }
        return parts;
    }

    private VisualQcAiBoxLog buildAiBoxLog(
            AIBoxTaskNotification taskNotification,
            VisualQc visualQc,
            VisualTask visualTask) {
        VisualQcAiBoxLog logEntity = new VisualQcAiBoxLog();
        logEntity.setVisualQcAiBoxLogId(IdPool.getId());
        logEntity.setQcRecordId(visualQc.getQcRecordId());
        logEntity.setTaskId(visualQc.getTaskId());
        logEntity.setTaskCode(visualTask.getProcessCode());
        logEntity.setAiBoxTaskState(VisualTaskHandleStateEnum.DOING.getCode());
        logEntity.setAiBoxTaskId(taskNotification.aiBoxTaskId());
        logEntity.setAbility(taskNotification.ability());
        return logEntity;
    }

    private void updateTaskStatus(AIBoxTaskNotification taskNotification) {
        Integer status = taskNotification.status() == TaskStatus.COMPLETED
                ? VisualTaskHandleStateEnum.FINISH.getCode()
                : VisualTaskHandleStateEnum.FAIL.getCode();

        VisualQcAiBoxLog logEntity = visualQcAiBoxLogRepository
                .lambdaQuery()
                .eq(VisualQcAiBoxLog::getAiBoxTaskId, taskNotification.aiBoxTaskId())
                .one();

        if (logEntity != null) {
            logEntity.setAiBoxTaskState(status);
            visualQcAiBoxLogRepository.updateById(logEntity);
        }
    }

    private void saveTaskResults(AIBoxTaskNotification taskNotification) {
        if (CollectionUtil.isEmpty(taskNotification.results())) {
            return;
        }

        String[] taskSourceParts = parseTaskSourceId(taskNotification.taskSourceId());
        if (taskSourceParts == null) {
            return;
        }

        String urlName = taskSourceParts[1];
        VisualQcAiBoxLog logEntity = visualQcAiBoxLogRepository
                .lambdaQuery()
                .eq(VisualQcAiBoxLog::getAiBoxTaskId, taskNotification.aiBoxTaskId())
                .one();

        List<VisualQcAiBoxImage> imageList = taskNotification.results().stream()
                .map(result -> buildAiBoxImage(taskNotification, logEntity, result, urlName))
                .toList();

        visualQcAiBoxImageRepository.saveBatch(imageList);
    }

    private VisualQcAiBoxImage buildAiBoxImage(
            AIBoxTaskNotification taskNotification,
            VisualQcAiBoxLog logEntity,
            AIBoxMQTaskResult result,
            String urlName) {

        VisualQcAiBoxImage image = VisualQcAiBoxImage.builder()
                .visualQcAiBoxImageId(IdPool.getId())
                .qcRecordId(logEntity.getQcRecordId())
                .taskId(logEntity.getTaskId())
                .visualQcAiBoxLogId(logEntity.getVisualQcAiBoxLogId())
                .aiBoxTaskId(taskNotification.aiBoxTaskId())
                .ability(taskNotification.ability())
                .generatedImg(result.url())
                .generatedImgName(urlName)
                .build();

        if (!result.attributes().isNull()) {
            image.setAttributes(result.attributes());
            // 处理TRY_ON类型的特殊属性
            if (Objects.equals(taskNotification.ability(), Ability.TRY_ON.name())
                    && result.attributes().has("modelName")) {   //参考 DifyTryOnAttribute
                image.setModel(result.attributes().get("modelName").asText());
            }
        }

        return image;
    }
}