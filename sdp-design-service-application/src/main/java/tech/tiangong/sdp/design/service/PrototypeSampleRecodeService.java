package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.req.prototype.PrototypeSampleRecodeReq;
import tech.tiangong.sdp.design.vo.req.prototype.SampleRecodeCreateReq;
import tech.tiangong.sdp.design.vo.resp.prototype.PrototypeSampleRecodeVo;

/**
 * SKC_打版记录表服务接口
 *
 * <AUTHOR>
 */
public interface PrototypeSampleRecodeService {


    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    PrototypeSampleRecodeVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    Long create(SampleRecodeCreateReq req);

    /**
     * 更新数据
     *
     * @param req 数据实体
     */
    void update(PrototypeSampleRecodeReq req);

}
