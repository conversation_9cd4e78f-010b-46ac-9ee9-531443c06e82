package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.exception.BusinessException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DesignLogBizTypeEnum;
import tech.tiangong.sdp.design.enums.SupplyModeEnum;
import tech.tiangong.sdp.design.enums.spot.SpotProductPictureStateEnum;
import tech.tiangong.sdp.design.enums.spot.SpotResourceStateEnum;
import tech.tiangong.sdp.design.enums.spot.SpotTryOnStateEnum;
import tech.tiangong.sdp.design.enums.visual.*;
import tech.tiangong.sdp.design.remote.SdpOrderHelper;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.repository.SpotSpuRepository;
import tech.tiangong.sdp.design.repository.VisualDemandRepository;
import tech.tiangong.sdp.design.repository.VisualTaskRepository;
import tech.tiangong.sdp.design.service.DesignLogService;
import tech.tiangong.sdp.design.service.VisualDemandService;
import tech.tiangong.sdp.design.service.VisualSpuService;
import tech.tiangong.sdp.design.service.VisualTaskService;
import tech.tiangong.sdp.design.vo.req.log.DesignLogSdpSaveReq;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualDemandByIdReq;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualDemandBySpuReq;
import tech.tiangong.sdp.design.vo.resp.visual.VisualDemandVo;
import tech.tiangong.sdp.prod.vo.resp.StyleInfoBaseVo;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 视觉需求服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VisualDemandServiceImpl implements VisualDemandService {
    private final PrototypeRepository prototypeRepository;
    private final VisualDemandRepository visualDemandRepository;
    private final VisualTaskRepository visualTaskRepository;
    private final SpotSpuRepository spotSpuRepository;
    private final VisualTaskService visualTaskService;
    private final VisualSpuService visualSpuService;
    private final DesignLogService designLogService;
    private final SdpOrderHelper sdpOrderHelper;

    @Override
    public VisualDemandVo getVisualDemandVo(Long demandId) {
        if(demandId == null) {
            return null;
        }
        VisualDemand visualDemand = visualDemandRepository.getById(demandId);
        VisualDemandVo vo = new VisualDemandVo();
        BeanUtils.copyProperties(visualDemand, vo);

        if(StringUtils.isNotBlank(vo.getDemandImages())){
            vo.setDemandImageList(StrUtil.splitTrim(vo.getDemandImages(), StrUtil.COMMA));
        }
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public VisualDemand createVisualDemand4Front(SaveVisualDemandBySpuReq req){
        List<VisualTask> visualTasks = visualTaskRepository.queryLatestVisualTaskBySpu(req.getStyleCode());
        //判断是否可以合并需求，当前SPU最新任务(非尺寸表任务)如果不是已完成、已取消、已进入质检阶段，则可以合并
        if(CollectionUtil.isNotEmpty(visualTasks)) {
            visualTasks = visualTasks.stream().filter(v->
                    !VisualTaskTypeEnum.SIZE_TASK.getCode().equals(v.getTaskType())
                            && !VisualTaskStateEnum.FINISH.getCode().equals(v.getState())
                            && Bool.NO.getCode()==v.getIsCancel()).collect(Collectors.toList());
//            Assert.isTrue(CollectionUtil.isEmpty(visualTasks),"当前SPU存在在途视觉需求，是否前往编辑");
            if(CollectionUtil.isNotEmpty(visualTasks)) {
                VisualDemand visualDemand = visualDemandRepository.getById(visualTasks.getFirst().getLatestDemandId());
                log.error("当前SPU存在在途视觉需求，是否前往编辑,demandId:{}",visualDemand.getDemandId());
                throw new BusinessException(VisualErrorCodeEnum.ERROR_98123.getErrorMsg()+visualDemand.getDemandId());
            }
        }

        return doCreateVisualDemand(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VisualDemand createVisualDemand4ProdOrder(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            throw new BusinessException("SPU编码不能为空");
        }

        //判断大货资料是否提交
        StyleInfoBaseVo styleInfoBaseVo = sdpOrderHelper.latestSubmitOrder(styleCode);
        if (Objects.isNull(styleInfoBaseVo)) {
            throw new BusinessException("styleCode:"+styleCode+"还没提交大货资料");
        }
        //spu是否已动销, 动销则创建尺寸表任务
        List<Prototype> prototypes = prototypeRepository.listByStyleCode(styleCode);
        if (CollUtil.isEmpty(prototypes)) {
            throw new BusinessException("styleCode:"+styleCode+"下没有skc");
        }
        Prototype onSaleSkc = prototypes.stream().filter(Prototype::getIsOnSale).findFirst().orElse(null);
        if (Objects.isNull(onSaleSkc)) {
            throw new BusinessException("创建尺寸表任务 spu无动销"+ styleCode);
        }

        SaveVisualDemandBySpuReq req = new SaveVisualDemandBySpuReq();
        req.setStyleCode(styleCode);
        req.setIsProdOrder(Bool.YES.getCode());
        log.info("创建尺寸表任务 styleCode,{}", styleCode);
        return this.saveVisualDemandBySpu(req);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public VisualDemand saveVisualDemandById(SaveVisualDemandByIdReq req) {
        VisualDemand oldVisualDemand = visualDemandRepository.getById(req.getDemandId());
        Assert.isTrue(oldVisualDemand!=null,"没有找到对应的视觉需求");
        VisualDemand newVisualDemand = new VisualDemand();
        newVisualDemand.setDemandId(IdPool.getId());
        if(req.getIsAutoInit()){
            BeanUtils.copyProperties(oldVisualDemand, newVisualDemand,"demandId","createdTime");
            newVisualDemand.setDemandDesc(oldVisualDemand.getDemandDesc()+"\n"+req.getDemandDesc());
            newVisualDemand.setVersionNum(oldVisualDemand.getVersionNum()+1);
            newVisualDemand.setIsLatest(1);
        }else {
            newVisualDemand.setStyleCode(oldVisualDemand.getStyleCode());
            newVisualDemand.setDemandTime(oldVisualDemand.getDemandTime());
            newVisualDemand.setIsProdOrder(oldVisualDemand.getIsProdOrder());
            newVisualDemand.setProposerId(oldVisualDemand.getProposerId());
            newVisualDemand.setProposerName(oldVisualDemand.getProposerName());
            newVisualDemand.setDemandType(req.getDemandType());
            newVisualDemand.setRealObjectColorState(req.getRealObjectColorState());
            newVisualDemand.setDemandDesc(req.getDemandDesc());
            newVisualDemand.setVersionNum(oldVisualDemand.getVersionNum() + 1);
            newVisualDemand.setIsLatest(1);
            if (CollectionUtil.isNotEmpty(req.getModelPicList())) {
                newVisualDemand.setModelPic(String.join(",", req.getModelPicList()));
            }
            if (CollectionUtil.isNotEmpty(req.getBackgroundPicList())) {
                newVisualDemand.setBackgroundPic(String.join(",", req.getBackgroundPicList()));
            }
            if (CollectionUtil.isNotEmpty(req.getPosturePicList())) {
                newVisualDemand.setPosturePic(String.join(",", req.getPosturePicList()));
            }
            if (CollectionUtil.isNotEmpty(req.getDemandImages())) {
                newVisualDemand.setDemandImages(String.join(",", req.getDemandImages()));
            }
            if (CollectionUtil.isNotEmpty(req.getModelReferenceImageList())) {
                newVisualDemand.setModelReferenceImage(CollectionUtil.isEmpty(req.getModelReferenceImageList())? null :JSON.toJSONString(req.getModelReferenceImageList()));
            }
            if (CollectionUtil.isNotEmpty(req.getBackgroundImageList())) {
                newVisualDemand.setBackgroundImage(CollectionUtil.isEmpty(req.getBackgroundImageList())? null :JSON.toJSONString(req.getBackgroundImageList()));
            }
            if (CollectionUtil.isNotEmpty(req.getModelFaceImageList())) {
                newVisualDemand.setModelFaceImage(CollectionUtil.isEmpty(req.getModelFaceImageList())? null :JSON.toJSONString(req.getModelFaceImageList()));
            }
        }
        visualDemandRepository.save(newVisualDemand);
        List<VisualTask> tasks = visualTaskRepository.listLatestByDemandIds(Collections.singletonList(oldVisualDemand.getDemandId()));
        tasks.forEach(task -> {
            task.setLatestDemandId(newVisualDemand.getDemandId());
        });
        visualTaskRepository.updateBatchById(tasks);
        //过期掉旧版本的需求
        oldVisualDemand.setIsLatest(0);
        visualDemandRepository.updateById(oldVisualDemand);
        String logContent = "编辑视觉需求" + (req.getIsAutoInit() ? "-追加描述":"");
        tasks.forEach(task -> {
            addLog(task.getStyleCode(), task.getTaskId(),logContent);
        });
        return newVisualDemand;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public VisualDemand saveVisualDemandBySpu(SaveVisualDemandBySpuReq req){
        log.info("根据SPU发起视觉需求：{}",JSONObject.toJSONString(req));
        //大货资料发起的尺寸表任务，不需要合并需求，符合条件则创建，不符合则抛出异常
        if(req.getIsProdOrder()!=null && Bool.YES.getCode()==req.getIsProdOrder()){
            return doCreateVisualDemand(req);
        }
        //找出在途的需求
        VisualDemand visualDemand = null;
        VisualTask latestVisualTask = null;
        List<Integer> taskTypeList = List.of(VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode(), VisualTaskTypeEnum.OPTIMIZE_TASK.getCode());
        List<VisualTask> latestVisualTasks = visualTaskRepository.queryLatestHandlingTaskBySpuTaskType(req.getStyleCode(), taskTypeList);
        if (Objects.nonNull(latestVisualTasks)) {
            //优先返回在途的上新任务
            latestVisualTask = latestVisualTasks.stream().filter(v -> VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode().equals(v.getTaskType())).findFirst().orElse(null);
            if (latestVisualTask == null) {
                latestVisualTask = latestVisualTasks.stream().filter(v -> VisualTaskTypeEnum.OPTIMIZE_TASK.getCode().equals(v.getTaskType())).findFirst().orElse(null);
            }
            visualDemand = latestVisualTask!=null ? visualDemandRepository.getById(latestVisualTask.getLatestDemandId()) : null;
        }
        //判断是否可以合并需求，当前SPU最新任务(非尺寸表任务)如果不是已完成、已取消，则可以合并
        if(req.getIsAutoInit()){
            if(latestVisualTask!=null) {
                log.info("符合合并需求的任务ID：{}", latestVisualTask.getTaskId());
                SaveVisualDemandByIdReq saveVisualDemandReq = new SaveVisualDemandByIdReq();
                saveVisualDemandReq.setDemandId(latestVisualTask.getLatestDemandId());
                saveVisualDemandReq.setDemandDesc(req.getDemandDesc());
                saveVisualDemandReq.setIsAutoInit(req.getIsAutoInit());
                return saveVisualDemandById(saveVisualDemandReq);
            }else{
                //没有在途的则创建一个新的需求任务
                return doCreateVisualDemand(req);
            }
        }
        //存在一个在途的需求升级一个版本
        else if(visualDemand!=null){
            SaveVisualDemandByIdReq saveVisualDemandByIdReq = new SaveVisualDemandByIdReq();
            BeanUtils.copyProperties(req,saveVisualDemandByIdReq);
            saveVisualDemandByIdReq.setDemandId(visualDemand.getDemandId());
            saveVisualDemandByIdReq.setModelReferenceImageList(req.getModelReferenceImageList());
            saveVisualDemandByIdReq.setBackgroundImageList(req.getBackgroundImageList());
            saveVisualDemandByIdReq.setModelFaceImageList(req.getModelFaceImageList());
            return saveVisualDemandById(saveVisualDemandByIdReq);
        }
        return doCreateVisualDemand(req);
    }


    private VisualDemand doCreateVisualDemand(SaveVisualDemandBySpuReq req) {
        log.info("创建视觉需求开始:{}", JSONObject.toJSONString(req));
        //初始化SPU信息
        VisualSpu visualSpu = visualSpuService.initVisualSpu(req.getStyleCode());
        //现货款需要判断款式资料是否已完善，商品图是否已齐全，如果供给方式是tryOn，则还要判断tryOn状态是否已通过
        if(Objects.equals(VisualSpuTypeEnum.SPOT_SPU.getCode(),visualSpu.getStyleType())){
            SpotSpu spotSpu = spotSpuRepository.getByStyleCode(req.getStyleCode());
            //款式资料未完善，不能发起视觉需求
            Assert.isTrue(Objects.equals(spotSpu.getResourceStatus(), SpotResourceStateEnum.FINISH.getCode()),"现货款"+req.getStyleCode()+"款式资料未完善,不能发起视觉需求");
            //商品图片未齐全，不能发起视觉需求
            Assert.isTrue(Objects.equals(spotSpu.getProductPictureStatus(), SpotProductPictureStateEnum.FINISH.getCode()),"现货款"+req.getStyleCode()+"商品图未齐全,不能发起视觉需求");
            //如果供给方式是tryOn,tryOn状态不是已通过，不能发起视觉需求
            if (Objects.equals(spotSpu.getSupplyModeCode(), SupplyModeEnum.TRY_ON.getCode())){
                Assert.isTrue(Objects.equals(spotSpu.getTryOnStatus(), SpotTryOnStateEnum.PASS.getCode()),"现货款"+req.getStyleCode()+"tryOn状态未通过,不能发起视觉需求");
            }
        }

        VisualDemand visualDemand = new VisualDemand();
        BeanUtils.copyProperties(req, visualDemand);
        visualDemand.setDemandId(IdPool.getId());
        //模特图
        visualDemand.setModelPic(CollectionUtil.isEmpty(req.getModelPicList()) ? ""
                : String.join(",", req.getModelPicList()));
        //背景图
        visualDemand.setBackgroundPic(CollectionUtil.isEmpty(req.getBackgroundPicList()) ? ""
                : String.join(",", req.getBackgroundPicList()));
        //姿势图
        visualDemand.setPosturePic(CollectionUtil.isEmpty(req.getPosturePicList()) ? ""
                : String.join(",", req.getPosturePicList()));
        visualDemand.setModelReferenceImage(CollectionUtil.isEmpty(req.getModelReferenceImageList())? null :JSON.toJSONString(req.getModelReferenceImageList()));
        visualDemand.setBackgroundImage(CollectionUtil.isEmpty(req.getBackgroundImageList())? null :JSON.toJSONString(req.getBackgroundImageList()));
        visualDemand.setModelFaceImage(CollectionUtil.isEmpty(req.getModelFaceImageList())? null :JSON.toJSONString(req.getModelFaceImageList()));
        if(visualDemand.getDemandType() == null) {
            visualDemand.setDemandType(VisualTaskProcessTypeEnum.NO_FIX.getCode());
        }
        visualDemand.setVersionNum(1);
        visualDemand.setIsLatest(1);
        visualDemand.setDemandTime(LocalDateTime.now());
        //需求图
        if(CollectionUtil.isNotEmpty(req.getDemandImages())){
            visualDemand.setDemandImages(String.join(",", req.getDemandImages()));
        }
        //模特图
        if(CollectionUtil.isNotEmpty(req.getModelPicList())){
            visualDemand.setModelPic(String.join(",", req.getModelPicList()));
        }
        //背景图
        if(CollectionUtil.isNotEmpty(req.getBackgroundPicList())){
            visualDemand.setBackgroundPic(String.join(",", req.getBackgroundPicList()));
        }
        //姿势图
        if(CollectionUtil.isNotEmpty(req.getPosturePicList())){
            visualDemand.setPosturePic(String.join(",", req.getPosturePicList()));
        }
        //提需求的人
        if(visualDemand.getProposerId() == null) {
            UserContent userContent = UserContentHolder.get();
            visualDemand.setProposerId(userContent.getCurrentUserId());
            visualDemand.setProposerName(userContent.getCurrentUserName());
        }

        //保存需求
        visualDemandRepository.save(visualDemand);
        log.info("创建视觉需求结束:{}", JSONObject.toJSONString(visualDemand));
        if(req.getIsCreateTask()!=null && Bool.YES.getCode()==req.getIsCreateTask()){
            visualTaskService.createByVisualDemand(visualDemand);
        }
        return visualDemand;
    }


    private void addLog(String styleCode, Long bizId, String logContent) {
        DesignLogSdpSaveReq logSdpSaveReq = DesignLogSdpSaveReq.builder()
                .bizId(bizId)
                .bizType(DesignLogBizTypeEnum.VISUAL)
                .styleCode(styleCode)
                .content(logContent)
                .build();

        designLogService.sdpSave(logSdpSaveReq);
    }
}
