package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.PrototypeManageService;
import tech.tiangong.sdp.design.vo.query.prototype.PrototypeManageExcelQuery;
import tech.tiangong.sdp.design.vo.query.prototype.PrototypeManageQuery;
import tech.tiangong.sdp.design.vo.req.manage.ColorsMakingReq;
import tech.tiangong.sdp.design.vo.req.manage.PrototypeCancelReq;
import tech.tiangong.sdp.design.vo.req.manage.PrototypeExcelReq;
import tech.tiangong.sdp.design.vo.req.manage.PrototypeMakeClothesReq;
import tech.tiangong.sdp.design.vo.req.prototype.ChgDesignerReq;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeManageInfoBatchListReq;
import tech.tiangong.sdp.design.vo.resp.prototype.*;
import tech.tiangong.sdp.utils.PlmExcelExportUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 款式管理-web
 *
 * <AUTHOR>
 * @since 2021-08-09 14:43:19
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/prototype-manage")
public class PrototypeManageController extends BaseController {

    private final PrototypeManageService manageService;

    /**
     * 查询列表（分页）
     *
     * @param queryDTO 分页对象
     * @return 设计款号分页列表
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<PrototypeManageQueryResp>> page(@RequestBody @Validated PrototypeManageQuery queryDTO) {
        return DataResponse.ok(manageService.page(queryDTO));
    }

    /**
     * 打版_核价信息-批量查询
     *
     * @param req 查询对象
     * @return 打版核价信息
     */
    @PostMapping("/clothes-price")
    public DataResponse<List<PrototypeManageInfoQueryResp>> clothesPriceList(@RequestBody @Validated PrototypeManageInfoBatchListReq req) {
        return DataResponse.ok(manageService.clothesPriceList(req));
    }


    /**
     * 设计款详情-(SPU + SKC)
     *
     * @param designCode 设计款号
     * @param isEdit     是否查询编辑页: 0-否; 1-是;(默认否)
     * @return 返回
     */
    @GetMapping("/base-info/{designCode}")
    public DataResponse<PrototypeTagVo> spuSkcInfo(@PathVariable(value = "designCode") String designCode,
                                                   @RequestParam(value = "isEdit", required = false) Integer isEdit) {
        return DataResponse.ok(manageService.spuSkcInfo(designCode, isEdit));
    }

    /**
     * 复色
     *
     * @param req 入参
     * @return 响应结果
     */
    @PostMapping("/colors-making")
    public DataResponse<Long> colorsMaking(@RequestBody @Validated ColorsMakingReq req) {
        return DataResponse.ok(manageService.colorsMaking(req));
    }

    /**
     * 设计师变更
     *
     * @param req 设计师变更请求信息
     */
    @PostMapping("/designer-change")
    public DataResponse<Void> designerChange(@RequestBody @Validated ChgDesignerReq req) {
        manageService.designerChange(req);
        return DataResponse.ok();
    }

    /**
     * 取消设计款
     *
     * @param cancelReq 取消请求对象
     * @return Void
     */
    @PostMapping("/cancel")
    @NoRepeatSubmitLock
    public DataResponse<Void> cancelDesign(@RequestBody @Validated PrototypeCancelReq cancelReq) {
        manageService.cancelDesign(cancelReq);
        return DataResponse.ok();
    }

    /**
     * 发起打版
     *
     * @param req 请求对象
     * @return PrototypeMakeClothesVo
     */
    @PostMapping("/make-clothes")
    @NoRepeatSubmitLock
    public DataResponse<PrototypeMakeClothesVo> makeClothes(@RequestBody @Validated PrototypeMakeClothesReq req) {
        return DataResponse.ok(manageService.makeClothes(req));
    }

    /**
     * 导出款式数据---最多导出5K条记录
     * */
    @NoRepeatSubmitLock
    @PostMapping("/export/excel")
    public DataResponse<Void> prototypeManageExportExcel(@RequestBody @Validated PrototypeManageExcelQuery queryDTO, HttpServletResponse response) throws Exception {
        List<PrototypeExcelResp>  resp =  manageService.prototypeManageExportExcel(queryDTO);
        PlmExcelExportUtil.prototypeTemplateExport(resp, response);
        return DataResponse.ok();
    }

    /**
     * 导出款式数据图片
     * */
    @NoRepeatSubmitLock
    @PostMapping("/export/zip")
    public DataResponse<List<PrototypeZipResp>> prototypeManageExportZip(@RequestBody @Validated PrototypeExcelReq req, HttpServletResponse response) throws Exception {
        List<PrototypeZipResp>  resp =  manageService.prototypeManageExportZip(req);
        return DataResponse.ok(resp);
    }

    // /**
    //  * 标记-紧急
    //  *
    //  * @param urgentReq req
    //  * @return Void
    //  */
    // @PostMapping("/urgent-mark")
    // @NoRepeatSubmitLock(lockTime = 5L)
    // public DataResponse<Void> markUrgent(@RequestBody @Validated PrototypeUrgentReq urgentReq) {
    //     manageService.markUrgent(urgentReq);
    //     return DataResponse.ok();
    // }

    // /**
    //  * 设计款详情_bom标签页信息
    //  *
    //  * @param designCode 设计款号
    //  * @return 最新已提交的Bom详情
    //  */
    // @GetMapping("/bom-info/{designCode}")
    // public DataResponse<BomOrderDetailVo> getBomInfo(@PathVariable(value = "designCode")
    //                                                  @NotBlank(message = "设计款号不能为空") String designCode) throws Exception {
    //     bomOrderService.updateLatestPriceByDesignCode(designCode);
    //     return DataResponse.ok(manageService.getBomInfo(designCode));
    // }


}