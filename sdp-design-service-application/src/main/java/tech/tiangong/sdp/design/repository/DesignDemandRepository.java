package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.DesignDemand;
import tech.tiangong.sdp.design.mapper.DesignDemandMapper;
import tech.tiangong.sdp.design.vo.query.DesignDemandQuery;
import tech.tiangong.sdp.design.vo.req.prototype.inner.DesignDemandRefreshQuery;
import tech.tiangong.sdp.design.vo.resp.demand.DesignDemandPageVo;

import java.util.List;
import java.util.Objects;

/**
 * 灵感设计需求表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class DesignDemandRepository extends BaseRepository<DesignDemandMapper, DesignDemand> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<DesignDemandPageVo> findPage(DesignDemandQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public DesignDemand getByInspirationStyleId(Long inspirationStyleId) {
        if (Objects.isNull(inspirationStyleId)) {
            return null;
        }
        return lambdaQuery().eq(DesignDemand::getInspirationStyleId, inspirationStyleId).one();
    }

    public DesignDemand getByStyleCode(String styleCode) {
        if (Objects.isNull(styleCode)) {
            return null;
        }
        return lambdaQuery().eq(DesignDemand::getStyleCode, styleCode).one();
    }

    public DesignDemand getByBizId(Long sourceBizId) {
        if (Objects.isNull(sourceBizId)) {
            return null;
        }
        return lambdaQuery().eq(DesignDemand::getInspirationStyleId, sourceBizId).one();
    }

    public List<DesignDemand> noSubmitUserList(String designDemandId) {
        return lambdaQuery()
                .isNull(DesignDemand::getSubmitUserId)
                .eq(Objects.nonNull(designDemandId), DesignDemand::getDesignDemandId, designDemandId)
                .list();
    }

    public List<DesignDemand> selectAll(DesignDemandRefreshQuery query) {
        return lambdaQuery()
                .in(CollectionUtil.isNotEmpty(query.getIds()),DesignDemand::getDesignDemandId, query.getIds())
                .eq(DesignDemand::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(DesignDemand::getCreatedTime)
                .list();
    }
}
