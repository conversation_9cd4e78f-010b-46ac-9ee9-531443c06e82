package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.RefreshDataService;
import tech.tiangong.sdp.design.vo.req.prototype.inner.DesignDemandRefreshQuery;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SpuImgListRefreshQuery;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SpuRefreshQuery;

/**
 * 刷数据控制器
 *
 * <AUTHOR>
 * @since 1.0
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/refresh")
public class RefreshDataController extends BaseController {

    private final RefreshDataService refreshDataService;


    /**
     * 现货管理-向量相关刷数
     *
     * @return
     */
    @PostMapping("/spot-spu/vector")
    public DataResponse<Void> spotSpuRefresh(@RequestBody SpuRefreshQuery query) {
        refreshDataService.spotSpuRefresh(query);
        return DataResponse.ok();
    }


    /**
     * 灵感任务分配-向量相关刷数
     *
     * @return
     */
    @PostMapping("/design-demand/vector")
    public DataResponse<Void> designDemandVectorRefresh(@RequestBody DesignDemandRefreshQuery query) {
        refreshDataService.designDemandVectorRefresh(query);
        return DataResponse.ok();
    }



    /**
     * 款式管理-向量相关刷数
     *
     * @return
     */
    @PostMapping("/prototype-manage/vector")
    public DataResponse<Void> prototypeManageRefresh(@RequestBody SpuRefreshQuery query) {
        refreshDataService.prototypeManageRefresh(query);
        return DataResponse.ok();
    }

    /**
     * 1688商品url刷数
     *
     * @return
     */
    @PostMapping("/product-img-url")
    public DataResponse<Void> productImgUrlRefresh(@RequestBody SpuImgListRefreshQuery query) {
        refreshDataService.productImgUrlRefresh(query);
        return DataResponse.ok();
    }



}
