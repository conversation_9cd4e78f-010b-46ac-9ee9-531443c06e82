package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualImagePackage;
import tech.tiangong.sdp.design.mapper.VisualImagePackageMapper;
import tech.tiangong.sdp.design.vo.query.visual.VisualImagePackageQuery;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageListVo;

import java.util.Collection;
import java.util.List;

/**
 * (VisualImagePackage)服务仓库类
 */
@Repository
public class VisualImagePackageRepository extends BaseRepository<VisualImagePackageMapper, VisualImagePackage> {

    public IPage<VisualImagePackageListVo> queryByPage(VisualImagePackageQuery query) {
        return baseMapper.queryByPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public VisualImagePackage getLatestByStyleCode(String styleCode) {
        return getOne(new LambdaQueryWrapper<VisualImagePackage>()
                .eq(VisualImagePackage::getStyleCode,styleCode)
                .eq(VisualImagePackage::getIsLatest, Bool.YES.getCode())
                .eq(VisualImagePackage::getIsDeleted,Bool.NO.getCode())
                .orderByDesc(VisualImagePackage::getCreatedTime),false);
    }

    public List<VisualImagePackage> listLatestByStyleCodes(Collection<String> styleCodes) {
        return list(new LambdaQueryWrapper<VisualImagePackage>()
                .in(VisualImagePackage::getStyleCode,styleCodes)
                .eq(VisualImagePackage::getIsLatest, Bool.YES.getCode())
                .eq(VisualImagePackage::getIsDeleted,Bool.NO.getCode()));
    }

    public String getPopImgUrl(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            return null;
        }
        return baseMapper.getPopImgUrl(styleCode);
    }



    /**
     * 分页查询需要迁移的图包数据
     * @param page 分页对象
     * @param styleCodes 指定的SPU编码列表，为空则查询所有
     * @return 分页结果
     */
    public IPage<VisualImagePackage> pageForMigration(int pageNum,int pageSize, List<String> styleCodes) {
        // 构建查询条件 - 只查询最新版本的数据
        LambdaQueryWrapper<VisualImagePackage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VisualImagePackage::getIsLatest, Bool.YES.getCode())
                .isNotNull(VisualImagePackage::getMainUrl)
                .isNotNull(VisualImagePackage::getOnShelfImages);

        if (CollectionUtil.isNotEmpty(styleCodes)) {
            queryWrapper.in(VisualImagePackage::getStyleCode, styleCodes);
        }

        return this.page(new Page<VisualImagePackage>(pageNum, pageSize), queryWrapper);
    }



    /**
     * 更新裁剪任务状态
     *
     * @param packageId 包ID
     * @param status    状态码
     * @return 更新影响的行数
     */
    public boolean updateCroppingTaskStatus(Long packageId, Integer status) {
        UpdateWrapper<VisualImagePackage> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("package_id", packageId)
                .set("cropping_task_status", status);
        return this.update(updateWrapper);
    }

    /**
     * 更新视频生成状态
     *
     * @param packageId 包ID
     * @param status    状态码
     * @return 更新影响的行数
     */
    public boolean updateVideoGenerationStatus(Long packageId, Integer status) {
        UpdateWrapper<VisualImagePackage> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("package_id", packageId)
                .set("video_generation_status", status);
        return this.update(updateWrapper);
    }
}
