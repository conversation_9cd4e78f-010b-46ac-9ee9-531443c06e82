package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.req.spot.SpotSkcDetailReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcDetailVo;

/**
 * spot_skc_detail表(SpotSkcDetail)服务接口
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:35
 */
public interface SpotSkcDetailService {

    /**
     * 根据主键查询详情
     *
     * @param spotSkcDetailId 主键
     * @return 数据实体
     */
    SpotSkcDetailVo getById(Long spotSkcDetailId);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(SpotSkcDetailReq req);

    /**
     * 更新数据
     *
     * @param req 数据实体
     */
    void update(SpotSkcDetailReq req);

}
