package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.base.entity.BaseEntity;
import cn.yibuyun.framework.tuple.Pair;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.ClothingCodeGenerate;
import tech.tiangong.sdp.design.entity.DesignStyle;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.enums.ClothingCodeDeriveEnum;
import tech.tiangong.sdp.design.enums.ClothingCodeTypeEnum;
import tech.tiangong.sdp.design.enums.DesignStyleSourceTypeEnum;
import tech.tiangong.sdp.design.repository.ClothingCodeGenerateRepository;
import tech.tiangong.sdp.design.repository.DesignStyleRepository;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.service.ClothingCodeDeriveGenerateService;
import tech.tiangong.sdp.design.vo.req.ClothingCodeGenerateReq;
import tech.tiangong.sdp.utils.BusinessCodeGenerator;
import tech.tiangong.sdp.utils.CodeRuleEnum;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@Slf4j
public class ClothingCodeDeriveGenerateServiceImpl implements ClothingCodeDeriveGenerateService {

    private final BusinessCodeGenerator businessCodeGenerator;
    private final ClothingCodeGenerateRepository clothingCodeGenerateRepository;
    private final DesignStyleRepository designStyleRepository;
    private final PrototypeRepository prototypeRepository;

    @Value("${spring.profiles.active}")
    private String env;

    @Override
    public String spuCode(ClothingCodeGenerateReq generateReq) {
        String code = "";
        ClothingCodeGenerate codeGenerate = null;
        if (generateReq.getDeriveEnum() == ClothingCodeDeriveEnum.SPU_NEW) {
            code = businessCodeGenerator.clothingGenerate(CodeRuleEnum.CLOTHING_SPU_CODE_JV,
                    String.format(ClothingCodeDeriveEnum.SPU_NEW.getIncrRule(), 1), clothingCodeGenerateRepository::selectLatestSPUCode,
                    ClothingCodeEnvPrefixEnum.getCodeEnvPrefix(env));
        } else if (generateReq.getDeriveEnum() == ClothingCodeDeriveEnum.SPU_REMAKE) {
            SdpDesignException.notNull(generateReq.getCode(), "SPU改款编号必须传父SPU编号。");
            codeGenerate = clothingCodeGenerateRepository.getByCodeAndType(generateReq.getCode(), ClothingCodeTypeEnum.SPU.getCode());
            code = generateSpecialStyleCode(generateReq.getCode());
        }
        //加前缀
        code = CodeRuleEnum.CLOTHING_SPU_CODE_JV.getCode() + code;
        log.info("============ 生成的SPU编码: code:{} ====", code);
        this.saveGenerateData(code, Objects.nonNull(codeGenerate) ? ClothingCodeGenerate.builder()
                    .clothingCodeGenerateId(codeGenerate.getClothingCodeGenerateId())
                    .code(codeGenerate.getCode())
                    .build() : null, generateReq);
        return code;
    }

    @Override
    public String skcCode(ClothingCodeGenerateReq generateReq) {
        String code = "";
        ClothingCodeGenerate codeGenerate = null;
        if (generateReq.getDeriveEnum() == ClothingCodeDeriveEnum.SKC_NEW) {
            SdpDesignException.notNull(generateReq.getCode(), "创建SKC编号必须传SPU编号。");
            codeGenerate = clothingCodeGenerateRepository.getByCodeAndType(generateReq.getCode(), ClothingCodeTypeEnum.SPU.getCode());
            SdpDesignException.notNull(codeGenerate, "该SPU[%s]不存在", generateReq.getCode());
            code = generateReq.getCode().concat(String.format(ClothingCodeDeriveEnum.SKC_NEW.getIncrRule(), 1));

        } else if (generateReq.getDeriveEnum() == ClothingCodeDeriveEnum.SKC_COLOR_MARKING) {
            SdpDesignException.notNull(generateReq.getCode(), "创建SKC复色编号必须传父SKC编号。");
            codeGenerate = clothingCodeGenerateRepository.getByCodeAndType(generateReq.getCode(), ClothingCodeTypeEnum.SKC.getCode());
            //如果是旧规则版单号复色，则按新版单号逻辑生成新版单号
            if (Objects.isNull(codeGenerate.getDeriveType())) {
                code = this.generateSpecialDesignCode(generateReq.getCode());
            } else {
                String skcNumStr = StrUtil.removePrefixIgnoreCase(generateReq.getCode(), CodeRuleEnum.CLOTHING_SKC_CODE_JV.getCode());
                code = String.valueOf((Long.parseLong(skcNumStr) + 1));
            }
        }
        //加前缀(先删除SPU的前缀)
        code = StrUtil.removePrefixIgnoreCase(code, CodeRuleEnum.CLOTHING_SPU_CODE_JV.getCode());
        code = CodeRuleEnum.CLOTHING_SKC_CODE_JV.getCode() + code;
        log.info("============ 生成的SKC编码: code:{} ====", code);
        this.saveGenerateData(code, Objects.nonNull(codeGenerate) ? ClothingCodeGenerate.builder()
                    .clothingCodeGenerateId(codeGenerate.getClothingCodeGenerateId())
                    .code(codeGenerate.getCode())
                    .build() : null, generateReq);
        return code;
    }

    @Override
    public String spuCodeOld(ClothingCodeGenerateReq generateReq) {
        String code = businessCodeGenerator.clothingGenerateOld(CodeRuleEnum.CLOTHING_SPU_CODE, generateReq.getSourceEnum().getCode()
                            , clothingCodeGenerateRepository::selectLatestSPUCode, ClothingCodeEnvPrefixEnum.getCodeEnvPrefix(env));
        saveGenerateData(code, null, generateReq);
        return code;
    }

    @Override
    public String skcCodeOld(ClothingCodeGenerateReq generateReq) {
        SdpDesignException.notNull(generateReq.getCode(), "创建SKC必须传SPU编号。");
        ClothingCodeGenerate codeGenerate = clothingCodeGenerateRepository.getByCodeAndType(generateReq.getCode(),
                ClothingCodeTypeEnum.SPU.getCode());
        SdpDesignException.notNull(codeGenerate, "该SPU[%s]不存在", generateReq.getCode());
        String code = businessCodeGenerator.clothingGenerateOld(CodeRuleEnum.CLOTHING_SKC_CODE, generateReq.getSourceEnum().getCode()
                , clothingCodeGenerateRepository::selectLatestSKCCode, ClothingCodeEnvPrefixEnum.getCodeEnvPrefix(env));
        saveGenerateData(code, codeGenerate, generateReq);
        return code;
    }

    private String generateSpecialStyleCode(String parentStyleCode) {
        //1.查询被改款设计款信息
        DesignStyle designStyle = designStyleRepository.getByStyleCode(parentStyleCode);
        //2.查询最顶级父级款&其下所有改款数
        Pair<DesignStyle, Integer> topDesignStyleAndRemakeCount = getTopDesignStyleAndRemakeCount(designStyle);
        DesignStyle topDesignStyle = topDesignStyleAndRemakeCount.getVal0();
        if (topDesignStyle.getStyleCode().contains("SPU") || topDesignStyle.getStyleCode().contains("PC")) {
            //SPU前缀 + 版号（designStyleList.size()+1）
            return this.getSpuPrefix(topDesignStyle).concat(String.format(ClothingCodeDeriveEnum.SPU_NEW.getIncrRule(), topDesignStyleAndRemakeCount.getVal1() + 1));
        } else {
            return String.valueOf(Long.parseLong(topDesignStyle.getStyleCode()) + topDesignStyleAndRemakeCount.getVal1());
        }

    }

    private String generateSpecialDesignCode(String parentDesignCode) {
        //1.查询被复色版单所在款
        Prototype prototype = prototypeRepository.getByDesignCode(parentDesignCode);
        DesignStyle designStyle = designStyleRepository.getByStyleCode(prototype.getStyleCode());
        //2.查询被复色版单所在款复色数（包括正常打版）
        long count = prototypeRepository.count(new QueryWrapper<Prototype>().lambda().eq(Prototype::getStyleCode, prototype.getStyleCode()));
        int colorMarkingCount = (int) count;
        //2.查询最顶级父级款&其下所有改款数
        Pair<DesignStyle, Integer> topDesignStyleAndRemakeCount = getTopDesignStyleAndRemakeCount(designStyle);
        //SPU前缀 + 版号（designStyleList.size()）+ 复色版号（colorMarkingCount+1）
        return this.getSpuPrefix(topDesignStyleAndRemakeCount.getVal0())
                .concat(String.format(ClothingCodeDeriveEnum.SPU_NEW.getIncrRule(), topDesignStyleAndRemakeCount.getVal1()))
                .concat(String.format(ClothingCodeDeriveEnum.SKC_COLOR_MARKING.getIncrRule(), colorMarkingCount + 1));
    }

    /**
     * 获取SPU前缀
     *
     * @param topDesignStyle 头款
     * @return SPU前缀
     */
    private String getSpuPrefix(DesignStyle topDesignStyle) {
        //当月自建款数
        int designStyleCount = this.designStyleCount(topDesignStyle);
        //款编号日期前缀
        String datePrefix = DateUtil.format(Date.from(topDesignStyle.getCreatedTime().atZone(ZoneId.systemDefault()).toInstant()), CodeRuleEnum.CLOTHING_SPU_CODE_JV.getDatePrefix());
        //SPU前缀：yyMM+头款序号（designStyleCount)
        return datePrefix.concat(String.format(CodeRuleEnum.CLOTHING_SPU_CODE_JV.getIncrRule(), designStyleCount));
    }

    /**
     * 获取款所在月份自建款数
     *
     * @param topDesignStyle 头款
     * @return 自建款数
     */
    private int designStyleCount(DesignStyle topDesignStyle) {
        LocalDateTime monthBegin = topDesignStyle.getCreatedTime().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        long count = designStyleRepository.count(new QueryWrapper<DesignStyle>().lambda()
                .eq(DesignStyle::getSourceType, DesignStyleSourceTypeEnum.SELF_SPU_SOURCE.getCode())
                .gt(BaseEntity::getCreatedTime, monthBegin)
                .le(BaseEntity::getCreatedTime, topDesignStyle.getCreatedTime()));
        return (int) count;
    }

    /**
     * 查询最顶级父级款&其下所有改款数
     * @param designStyle
     * @return
     */
    private Pair<DesignStyle, Integer> getTopDesignStyleAndRemakeCount(DesignStyle designStyle) {
        Pair<DesignStyle, Integer> result = new Pair<>();
        // DesignStyle topDesignStyle = getTopDesignStyle(designStyle);
        DesignStyle topDesignStyle = new DesignStyle();  //todo
        Integer remakeCount = getRemakeCount(1, Stream.of(topDesignStyle).map(DesignStyle::getStyleCode).collect(Collectors.toList()));
        result.setVal0(topDesignStyle);
        result.setVal1(remakeCount);
        return result;
    }

     /**
      * 查询最顶级父级款&其下所有改款数
      * @param remakeCount 改款数（包括自己）
      * @param styleCodeList 设计款号集合
      * @return
      */
    private Integer getRemakeCount(Integer remakeCount, List<String> styleCodeList) {
        if (styleCodeList.isEmpty()) {
            return remakeCount;
        }
        // List<DesignStyle> childDesignStyleList = designStyleRepository.listByQuoteStyleCodes(styleCodeList);
        List<DesignStyle> childDesignStyleList = new ArrayList<>(); //todo
        List<String> childStyleCodeList = childDesignStyleList.stream().map(DesignStyle::getStyleCode).collect(Collectors.toList());
        // remakeCount += childStyleCodeList.size();
        return getRemakeCount(remakeCount, childStyleCodeList);
    }

    // /**
    //  * 查询最顶级父级款
    //  * @param designStyle
    //  * @return
    //  */
    // private DesignStyle getTopDesignStyle(DesignStyle designStyle) {
    //     if (Strings.isBlank(designStyle.getQuoteStyleCode())) {
    //         return designStyle;
    //     }
    //     DesignStyle parentDesignStyle = designStyleRepository.getByStyleCode(designStyle.getQuoteStyleCode());
    //     return getTopDesignStyle(parentDesignStyle);
    // }

    private ClothingCodeGenerate saveGenerateData(String code, ClothingCodeGenerate parentEntity, ClothingCodeGenerateReq req) {
        ClothingCodeGenerate entity = new ClothingCodeGenerate();
        entity.setClothingCodeGenerateId(IdPool.getId());
        entity.setCode(code);
        entity.setType(req.getTypeEnum().getCode());
        entity.setSource(req.getSourceEnum().getCode());
        entity.setDeriveType(Optional.ofNullable(req.getDeriveEnum()).map(ClothingCodeDeriveEnum::getCode).orElse(null));
        if (Objects.nonNull(parentEntity)) {
            entity.setParentId(parentEntity.getClothingCodeGenerateId());
            entity.setParentCode(parentEntity.getCode());
        }
        clothingCodeGenerateRepository.save(entity);
        return  entity;
    }

    @Getter
    @AllArgsConstructor
    public enum ClothingCodeEnvPrefixEnum {

        DEV1("dev", "1"),
        DEV2("dev2", "4"),
//        DEV3("dev3", "13"),
        QA1("qa", "2"),
        QA2("qa2", "5"),
//        QA3("qa3", "23"),

        /**
         * JV迁移前使用的是uat3环境, 对应前缀是3;
         * 迁移后, 运维把环境名改为uat1, 但数据库对应数据还是uat3的数据, 所以把uat1的环境前缀改为3
         */
        UAT1("uat", "3"),
        UAT2("uat2", "6"),
        UAT3("uat3", "7"),
        ;

        private final String env;

        private final String codePrefix;

        /**
         * 获取样衣code各环境前缀
         * @param env 环境
         * @return code前缀
         */
        public static String getCodeEnvPrefix(String env) {
            return Stream.of(ClothingCodeEnvPrefixEnum.values()).filter(e -> e.getEnv().equals(env))
                    .findFirst().map(ClothingCodeEnvPrefixEnum::getCodePrefix)
                    .orElse("");
        }
    }
}
