package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.PurchaseCancelRecord;
import tech.tiangong.sdp.design.mapper.PurchaseCancelRecordMapper;

/**
* 采购记录表
* <br>CreateDate August 13,2021
* <AUTHOR>
* @since 1.0
*/

@AllArgsConstructor
@Repository
public class PurchaseCancelRecordRepository extends BaseRepository<PurchaseCancelRecordMapper, PurchaseCancelRecord> {

    private final PurchaseCancelRecordMapper purchaseCancelRecordMapper;



}