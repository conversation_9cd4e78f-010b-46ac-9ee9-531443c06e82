package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.SpotSpuImportTaskService;

/**
 * @Author: caicijie
 * @description:
 * @Date: 2025/6/27 18:09
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/jobs/spot-spu/import")
public class SpotSpuImportJobController {

    private final SpotSpuImportTaskService spotSpuImportTaskService;

    /**
     * 图包导入定时任务执行
     * @return
     */
    @PostMapping("/import-run")
    public DataResponse<Void> importRun(){
        setSystemUser();
        spotSpuImportTaskService.importRun();
        return DataResponse.ok();
    }
    private void setSystemUser() {
        UserContentHolder.set(new UserContent()
                .setCurrentUserId(0L)
                .setCurrentUserCode("0")
                .setCurrentUserName("系统")
                .setTenantId(2L)
                .setSystemCode("SDP"));
    }
}
