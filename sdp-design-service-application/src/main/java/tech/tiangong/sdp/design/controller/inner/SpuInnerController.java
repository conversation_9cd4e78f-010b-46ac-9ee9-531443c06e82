package tech.tiangong.sdp.design.controller.inner;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.SpuInnerService;
import tech.tiangong.sdp.design.vo.req.style.PopCreateInfoReq;
import tech.tiangong.sdp.design.vo.req.style.SpuBatchReq;
import tech.tiangong.sdp.design.vo.req.style.SpuInnerQuery;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleInnerVo;
import tech.tiangong.sdp.design.vo.resp.style.PopCreateProductVo;
import tech.tiangong.sdp.design.vo.resp.style.SpuCancelResp;
import tech.tiangong.sdp.design.vo.resp.style.SpuInnerQueryVo;

import java.util.List;


/**
 * SPU-inner
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/spu")
public class SpuInnerController extends BaseController {
    private final SpuInnerService spuService;

    /**
     * 分页查询
     *
     * @param query 分页对象
     * @return PageRespVo<SpuInnerQueryVo>
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<SpuInnerQueryVo>> page(@RequestBody @Validated SpuInnerQuery query) {
        return DataResponse.ok(spuService.pageInner(query));
    }

    /**
     * 根据spu编号获取spu信息
     *
     * @param spuCode spu编码
     * @return 响应结果
     */
    @GetMapping("/{spuCode}")
    public DataResponse<DesignStyleInnerVo> getSpuInfo(@PathVariable(value = "spuCode") String spuCode) {
        return DataResponse.ok(spuService.getSpuInfo(spuCode));
    }

    /**
     * 根据spu批量获取spu信息
     *
     * @param req 入参
     * @return 响应结果
     */
    @PostMapping("/batch-info")
    public DataResponse<List<DesignStyleInnerVo>> batchSpuInfo(@RequestBody @Validated SpuBatchReq req) {
        return DataResponse.ok(spuService.batchSpuInfo(req));
    }

    /**
     * 根据spu,返回spu是否已取消
     *
     * @param spuCodeList spu编码
     * @return 响应结果
     */
    @PostMapping("/isCancel")
    public DataResponse<List<SpuCancelResp>> isCancelSpu(@RequestBody List<String> spuCodeList) {
        return DataResponse.ok(spuService.isCancelSpu(spuCodeList));
    }

    /**
     * 根据spu查询pop创建商品的信息
     *
     * @param req 入参
     * @return 响应结果
     */
    @PostMapping("/pop-style-info")
    public DataResponse<List<PopCreateProductVo>> queryStyleInfo4Pop(@RequestBody @Validated PopCreateInfoReq req) {
        return DataResponse.ok(spuService.queryStyleInfo4Pop(req));
    }

}