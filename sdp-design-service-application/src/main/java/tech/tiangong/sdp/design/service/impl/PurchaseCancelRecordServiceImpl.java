package tech.tiangong.sdp.design.service.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.design.repository.PurchaseCancelRecordRepository;
import tech.tiangong.sdp.design.service.PurchaseCancelRecordService;


/**
 * 取消剪版单记录表（供应履约返回的信息）
 *
 * <br>CreateDate August 09,2021
 * <AUTHOR>
 * @since 1.0
 */
@AllArgsConstructor
@Service
public class PurchaseCancelRecordServiceImpl implements PurchaseCancelRecordService {

    private final PurchaseCancelRecordRepository purchaseCancelRecordRepository;

}
