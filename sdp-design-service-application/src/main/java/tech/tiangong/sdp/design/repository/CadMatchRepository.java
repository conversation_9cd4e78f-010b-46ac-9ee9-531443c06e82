package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.collection.Collections;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.CadMatch;
import tech.tiangong.sdp.design.mapper.CadMatchMapper;
import tech.tiangong.sdp.utils.Bool;

import java.util.List;

;

/**
 * cad匹配表仓库类
 *
 * <AUTHOR>
 */
@Repository
public class CadMatchRepository extends BaseRepository<CadMatchMapper, CadMatch> {

    /**
     * 根据skc查询最新版本匹配更新
     */
    public CadMatch getLatestVersionByDesignCode(String designCode) {
        SdpDesignException.notBlank(designCode, "designCode设计款号为空! ");
        return lambdaQuery()
                .eq(CadMatch::getDesignCode, designCode)
                .eq(CadMatch::getIsLatest,1)
                .eq(CadMatch::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(CadMatch::getCreatedTime)
                .last("limit 0,1")
                .one();
    }

    /**
     * 根据cadStyleId查询最新版本匹配更新
     */
    public CadMatch getLatestVersionByCadStyleId(Long CadStyleId) {
        SdpDesignException.notNull(CadStyleId, "CadStyleId为空! ");
        return lambdaQuery()
                .eq(CadMatch::getCadStyleId, CadStyleId)
                .eq(CadMatch::getIsLatest,1)
                .eq(CadMatch::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(CadMatch::getCreatedTime)
                .last("limit 0,1")
                .one();
    }

    public List<CadMatch> getLatestVersionByDesignCodes(List<String> designCodes) {
        if (Collections.isEmpty(designCodes)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<CadMatch>().lambda().in(CadMatch::getDesignCode, designCodes).eq(CadMatch::getIsLatest, Bool.YES.getCode()));
    }


}
