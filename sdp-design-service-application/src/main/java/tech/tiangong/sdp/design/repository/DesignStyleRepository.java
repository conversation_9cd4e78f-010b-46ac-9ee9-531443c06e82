package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.bean.user.UserContent;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.DesignStyle;
import tech.tiangong.sdp.design.enums.StyleStatusEnum;
import tech.tiangong.sdp.design.mapper.DesignStyleMapper;
import tech.tiangong.sdp.design.vo.dto.SpuSkcNumDto;
import tech.tiangong.sdp.design.vo.dto.style.DesignStyleInsertDto;
import tech.tiangong.sdp.design.vo.dto.style.DesignStyleUpdateDto;
import tech.tiangong.sdp.design.vo.dto.style.Spu2SkcUpdateDto;
import tech.tiangong.sdp.design.vo.query.style.DesignStyleQuery;
import tech.tiangong.sdp.design.vo.req.clothingDesign.ClothingDesignStyleListQuery;
import tech.tiangong.sdp.design.vo.req.prototype.SpuProductTypeUpdateReq;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SpuRefreshQuery;
import tech.tiangong.sdp.design.vo.req.style.SpuInnerQuery;
import tech.tiangong.sdp.design.vo.resp.style.SpuInnerQueryVo;
import tech.tiangong.sdp.utils.Bool;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;


/**
 * SPU表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class DesignStyleRepository extends BaseRepository<DesignStyleMapper, DesignStyle> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<DesignStyle> findPage(DesignStyleQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }


    public IPage<DesignStyle> findPageForCommon(ClothingDesignStyleListQuery query ) {
        return baseMapper.findPageForCommon(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }


    public IPage<SpuInnerQueryVo> spuPageInner(SpuInnerQuery query) {
        return baseMapper.spuPageInner(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    /**
     * 根据styleCode查询spu信息(在design_style表中,一个spu只会有一条记录)
     */
    public DesignStyle getByStyleCode(String styleCode) {
        SdpDesignException.notBlank(styleCode, "styleCode为空! ");
        return lambdaQuery()
                .eq(DesignStyle::getStyleCode, styleCode)
                .eq(DesignStyle::getIsDeleted, Bool.NO.getCode())
                .one();
    }

    // /**
    //  * 根据quoteStyleCode查询spu信息
    //  */
    // public List<DesignStyle> listByQuoteStyleCodes(List<String> quoteStyleCodeList) {
    //     if (Collections.isEmpty(quoteStyleCodeList)) {
    //         return new ArrayList<>();
    //     }
    //     return lambdaQuery()
    //             .in(DesignStyle::getQuoteStyleCode, quoteStyleCodeList)
    //             .eq(DesignStyle::getIsDeleted, Bool.NO.getCode()).list();
    // }

    /**
     * 根据spu编码批量查询
     * @param styleCodeList spu编码集合
     * @return spu集合
     */
    public List<DesignStyle> listByStyleCodes(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(DesignStyle::getStyleCode, styleCodeList)
                .eq(DesignStyle::getIsDeleted, Bool.NO.getCode())
                .list();
    }
    /**
     * 根据spu编码批量查询
     * @param styleCodes spu编码集合
     * @return spu集合
     */
    public List<DesignStyle> listByStyleCodes(Set<String> styleCodes) {
        if (CollUtil.isEmpty(styleCodes)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(DesignStyle::getStyleCode, styleCodes)
                .eq(DesignStyle::getIsDeleted, Bool.NO.getCode())
                .list();
    }

    /**
     * 更新SPU-编辑SPU调用
     */
    public void updateSpuInfo(DesignStyleUpdateDto updateDto, UserContent userContent) {
        SdpDesignException.notNull(updateDto, "入参为null");
        SdpDesignException.notNull(updateDto.getDesignStyleId(), "spuId为空! ");
        SdpDesignException.notNull(updateDto.getVersionNum(), "versionNum为空! ");

        //服务对象与参考链接字段允许更新为null
        lambdaUpdate()
                .set(DesignStyle::getStyleStatus, StyleStatusEnum.SUBMITTED.getCode())
                .set(DesignStyle::getVersionNum, updateDto.getVersionNum())
                .set(DesignStyle::getLatestSubmitTime, updateDto.getSpuUpdateTime())
                .set(StringUtils.isNotBlank(updateDto.getSupplyModeCode()), DesignStyle::getSupplyModeCode, updateDto.getSupplyModeCode())
                .set(StringUtils.isNotBlank(updateDto.getSupplyModeName()), DesignStyle::getSupplyModeName, updateDto.getSupplyModeName())
                .set(StringUtils.isNotBlank(updateDto.getProductType()), DesignStyle::getProductType, updateDto.getProductType())
                .set(StringUtils.isNotBlank(updateDto.getProductTypeCode()), DesignStyle::getProductTypeCode, updateDto.getProductTypeCode())
                .set(StringUtils.isNotBlank(updateDto.getPalletTypeCode()), DesignStyle::getPalletTypeCode, updateDto.getPalletTypeCode())
                .set(StringUtils.isNotBlank(updateDto.getPalletTypeName()), DesignStyle::getPalletTypeName, updateDto.getPalletTypeName())
                .set(StringUtils.isNotBlank(updateDto.getCategory()), DesignStyle::getCategory, updateDto.getCategory())
                .set(StringUtils.isNotBlank(updateDto.getCategoryName()), DesignStyle::getCategoryName, updateDto.getCategoryName())
                .set(StringUtils.isNotBlank(updateDto.getSceneCode()), DesignStyle::getSceneCode, updateDto.getSceneCode())
                .set(StringUtils.isNotBlank(updateDto.getSceneName()), DesignStyle::getSceneName, updateDto.getSceneName())
                .set(StringUtils.isNotBlank(updateDto.getQualityLevel()), DesignStyle::getQualityLevel, updateDto.getQualityLevel())
                .set(StringUtils.isNotBlank(updateDto.getQualityLevelCode()), DesignStyle::getQualityLevelCode, updateDto.getQualityLevelCode())
                .set(StringUtils.isNotBlank(updateDto.getStyleSeason()), DesignStyle::getStyleSeason, updateDto.getStyleSeason())
                .set(StringUtils.isNotBlank(updateDto.getWeaveModeCode()), DesignStyle::getWeaveModeCode, updateDto.getWeaveModeCode())
                .set(StringUtils.isNotBlank(updateDto.getWeaveMode()), DesignStyle::getWeaveMode, updateDto.getWeaveMode())
                .set(StringUtils.isNotBlank(updateDto.getSuggestedSellingPrice()), DesignStyle::getSuggestedSellingPrice, updateDto.getSuggestedSellingPrice())
                .set(StringUtils.isNotBlank(updateDto.getFitCode()), DesignStyle::getFitCode, updateDto.getFitCode())
                .set(StringUtils.isNotBlank(updateDto.getFitName()), DesignStyle::getFitName, updateDto.getFitName())
                .set(StringUtils.isNotBlank(updateDto.getElasticCode()), DesignStyle::getElasticCode, updateDto.getElasticCode())
                .set(StringUtils.isNotBlank(updateDto.getElasticName()), DesignStyle::getElasticName, updateDto.getElasticName())
                .set(StringUtils.isNotBlank(updateDto.getElementCode()), DesignStyle::getElementCode, updateDto.getElementCode())
                .set(StringUtils.isNotBlank(updateDto.getElementName()), DesignStyle::getElementName, updateDto.getElementName())
                .set(StringUtils.isNotBlank(updateDto.getClothingStyleCode()), DesignStyle::getClothingStyleCode, updateDto.getClothingStyleCode())
                .set(StringUtils.isNotBlank(updateDto.getClothingStyleName()), DesignStyle::getClothingStyleName, updateDto.getClothingStyleName())
                .set(StringUtils.isNotBlank(updateDto.getSizeStandard()), DesignStyle::getSizeStandard, updateDto.getSizeStandard())
                .set(StringUtils.isNotBlank(updateDto.getSizeStandardCode()), DesignStyle::getSizeStandardCode, updateDto.getSizeStandardCode())
                .set(StringUtils.isNotBlank(updateDto.getProductThemeCode()), DesignStyle::getProductThemeCode, updateDto.getProductThemeCode())
                .set(StringUtils.isNotBlank(updateDto.getProductThemeName()), DesignStyle::getProductThemeName, updateDto.getProductThemeName())
                .set(Objects.nonNull(updateDto.getStoreId()), DesignStyle::getStoreId, updateDto.getStoreId())
                .set(StringUtils.isNotBlank(updateDto.getStoreName()), DesignStyle::getStoreName, updateDto.getStoreName())
                .set(Objects.nonNull(updateDto.getBuyerId()), DesignStyle::getBuyerId, updateDto.getBuyerId())
                .set(StringUtils.isNotBlank(updateDto.getBuyerName()), DesignStyle::getBuyerName, updateDto.getBuyerName())
                .set(StringUtils.isNotBlank(updateDto.getPlatformName()), DesignStyle::getPlatformName, updateDto.getPlatformName())
                .set(StringUtils.isNotBlank(updateDto.getCountrySiteCode()), DesignStyle::getCountrySiteCode, updateDto.getCountrySiteCode())
                .set(StringUtils.isNotBlank(updateDto.getCountrySiteName()), DesignStyle::getCountrySiteName, updateDto.getCountrySiteName())
                .set(updateDto.getPlanningType() != null, DesignStyle::getPlanningType, updateDto.getPlanningType())
                .set(StringUtils.isNotBlank(updateDto.getMarketCode()), DesignStyle::getMarketCode, updateDto.getMarketCode())
                .set(StringUtils.isNotBlank(updateDto.getMarketSeriesCode()), DesignStyle::getMarketSeriesCode, updateDto.getMarketSeriesCode())
                .set(DesignStyle::getElementCode, StringUtils.isBlank(updateDto.getElementCode()) ? null : updateDto.getElementCode())
                .set(DesignStyle::getElementName, StringUtils.isBlank(updateDto.getElementName()) ? null : updateDto.getElementName())
                .set(DesignStyle::getReferLink, StringUtils.isBlank(updateDto.getReferLink()) ? null : updateDto.getReferLink())
                .set(DesignStyle::getRevisedTime, Objects.isNull(updateDto.getSpuUpdateTime()) ? LocalDateTime.now() : updateDto.getSpuUpdateTime())
                .set(DesignStyle::getReviserId, userContent.getCurrentUserId())
                .set(DesignStyle::getReviserName, userContent.getCurrentUserName())
                .set(DesignStyle::getWaveBandCode,updateDto.getWaveBandCode())
                .set(DesignStyle::getWaveBandName,updateDto.getWaveBandName())
                .set(CollUtil.isNotEmpty(updateDto.getAttributes()), DesignStyle::getAttributes, JSON.toJSONString(updateDto.getAttributes()))
                .eq(DesignStyle::getDesignStyleId, updateDto.getDesignStyleId())
                .update();
    }

    /**
     * 刷数据-批量新增历史SPU
     */
    public void batchAddOldSpu(List<DesignStyleInsertDto> designStyleList) {
        if (CollUtil.isEmpty(designStyleList)) {
            return;
        }
        baseMapper.batchAddOldSpu(designStyleList);
    }

    /**
     * 查询所有的spu编码
     */
    public Set<String> allStyleCode() {
        return baseMapper.allStyleCode();
    }

    /**
     * 查询历史SPU需要回刷skc的信息
     *
     * @param styleCode spu编码, 为空时查全部
     * @return 回刷skc的信息集合
     */
    public List<Spu2SkcUpdateDto> spu2SkcUpdateInfo(String styleCode) {
        return baseMapper.spu2SkcUpdateInfo(styleCode);
    }

    /**
     * 根据spu批量更新商品类型
     */
    public Boolean updateProductTypeBySpu(SpuProductTypeUpdateReq req) {
        if (Objects.isNull(req)) {
            return null;
        }
        return lambdaUpdate()
                .set(DesignStyle::getProductType, req.getProductType())
                .set(DesignStyle::getProductTypeCode, req.getProductTypeCode())
                .in(DesignStyle::getStyleCode, req.getStyleCodeSet())
                .update();
    }

    public List<DesignStyle> selectAll(SpuRefreshQuery query) {
        return lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getStyleCode()),DesignStyle::getStyleCode, query.getStyleCode())
                .eq(DesignStyle::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(DesignStyle::getCreatedTime)
                .list();
    }

    public List<String> listAllSpu() {
        return baseMapper.listAllSpu();
    }

    public List<SpuSkcNumDto> querySpuSkcNum(List<String> styleCodeList) {
        return baseMapper.querySpuSkcNum(styleCodeList);
    }
}
