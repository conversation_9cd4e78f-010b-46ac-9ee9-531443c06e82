package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjkj.booster.common.constant.DatePatternConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.converter.visual.VisualImagePackageConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.enums.ProductCommunicationEnum;
import tech.tiangong.sdp.design.enums.visual.CropSpecificationEnum;
import tech.tiangong.sdp.design.enums.visual.CroppedTaskStatusEnum;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.remote.MurmurationRemoteHelper;
import tech.tiangong.sdp.design.repository.ImageCroppingTaskRepository;
import tech.tiangong.sdp.design.repository.SpotSpuRepository;
import tech.tiangong.sdp.design.repository.VisualImagePackageRepository;
import tech.tiangong.sdp.design.repository.VisualSpuRepository;
import tech.tiangong.sdp.design.service.VisualImagePackageService;
import tech.tiangong.sdp.design.service.VisualSpuService;
import tech.tiangong.sdp.design.service.download.DownloadTaskService;
import tech.tiangong.sdp.design.vo.query.visual.VisualImagePackageQuery;
import tech.tiangong.sdp.design.vo.req.visual.*;
import tech.tiangong.sdp.design.vo.resp.visual.ImageBatchUploadErrorResp;
import tech.tiangong.sdp.design.vo.resp.visual.MigrateImagePackageResp;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageListVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageVo;
import tech.tiangong.sdp.utils.ImageRatioUtils;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 视觉任务质检服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VisualImagePackageServiceImpl implements VisualImagePackageService {
    private final VisualImagePackageRepository visualImagePackageRepository;
    private final VisualSpuRepository visualSpuRepository;
    private final VisualImagePackageConverter visualImagePackageConverter;
    private final VisualTaskHelper visualTaskHelper;
    private final VisualSpuService visualSpuService;
    private final DownloadTaskService downloadTaskService;
    private final MurmurationRemoteHelper murmurationRemoteHelper;
    private final ImageCroppingTaskRepository imageCroppingTaskRepository;
    private final TransactionTemplate transactionTemplate;
    private final SpotSpuRepository spotSpuRepository;

    private final DateTimeFormatter PURE_DATETIME_PATTERN = DateTimeFormatter.ofPattern(DatePatternConstants.PURE_DATETIME_PATTERN);

    @Override
    public PageRespVo<VisualImagePackageListVo> page(VisualImagePackageQuery query) {
        IPage<VisualImagePackageListVo> page = visualImagePackageRepository.queryByPage(query);
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveImagePackage(OnShelfImagePackage onShelfImagePackage){
        VisualImagePackage visualImagePackage = visualImagePackageRepository.getLatestByStyleCode(onShelfImagePackage.getStyleCode());
        VisualImagePackage newVisualImagePackage = new VisualImagePackage();
        newVisualImagePackage.setPackageId(IdPool.getId());
        newVisualImagePackage.setStyleCode(onShelfImagePackage.getStyleCode());
        newVisualImagePackage.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
        newVisualImagePackage.setMainUrl(onShelfImagePackage.getMainUrl());
        if(visualImagePackage!=null){
            newVisualImagePackage.setOnShelfImages11(visualImagePackage.getOnShelfImages11());
            newVisualImagePackage.setOnShelfImages34(visualImagePackage.getOnShelfImages34());
            newVisualImagePackage.setVideoGenerations(visualImagePackage.getVideoGenerations());
            newVisualImagePackage.setCroppingTaskStatus(visualImagePackage.getCroppingTaskStatus());
            newVisualImagePackage.setVideoGenerationStatus(visualImagePackage.getVideoGenerationStatus());
            newVisualImagePackage.setVersionNum(visualImagePackage.getVersionNum() + 1);
            newVisualImagePackage.setPackageInitTime(visualImagePackage.getPackageInitTime());
            newVisualImagePackage.setPackageInitCreatorId(visualImagePackage.getPackageInitCreatorId());
            newVisualImagePackage.setPackageInitCreatorName(visualImagePackage.getPackageInitCreatorName());
        }else{
            UserContent userContent = UserContentHolder.get();
            newVisualImagePackage.setVersionNum(1);
            newVisualImagePackage.setPackageInitTime(LocalDateTime.now());
            newVisualImagePackage.setPackageInitCreatorId(userContent.getCurrentUserId());
            newVisualImagePackage.setPackageInitCreatorName(userContent.getCurrentUserName());
            if(StringUtils.isNotBlank(newVisualImagePackage.getMainUrl())){
                String imageRatioType = ImageRatioUtils.getImageRatioType(newVisualImagePackage.getMainUrl());
                log.info("{}的主图尺寸是{}", newVisualImagePackage.getStyleCode(), imageRatioType);
                if(CropSpecificationEnum.RATIO_1_1.getCode().equals(imageRatioType)){
                    newVisualImagePackage.setOnShelfImages11(newVisualImagePackage.getOnShelfImages());
                }else if (CropSpecificationEnum.RATIO_3_4.getCode().equals(imageRatioType)){
                    newVisualImagePackage.setOnShelfImages34(newVisualImagePackage.getOnShelfImages());
                }
            }
        }
        newVisualImagePackage.setIsLatest(1);
        visualImagePackageRepository.save(newVisualImagePackage);
        if(visualImagePackage!=null){
            visualImagePackage.setIsLatest(0);
            visualImagePackageRepository.updateById(visualImagePackage);
        }

        if(visualImagePackage == null && StringUtils.isNotBlank(newVisualImagePackage.getMainUrl())){
            //货通商品不执行裁剪
            SpotSpu spotSpu = spotSpuRepository.getByStyleCode(newVisualImagePackage.getStyleCode());
            if(spotSpu == null || ObjectUtils.notEqual(ProductCommunicationEnum.YES.getCode(), spotSpu.getCommunication())){
                createImageCroppingTask(newVisualImagePackage);
                //发起裁剪任务
                visualTaskHelper.createImageCroppingTaskMq(newVisualImagePackage.getPackageId(),newVisualImagePackage.getStyleCode());
            }
        }

        //通知POP图片变更
        visualTaskHelper.noticePopUpdateImage(newVisualImagePackage.getPackageId(), newVisualImagePackage.getStyleCode());
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveOnShelfImagePackage(SaveOnShelfImagePackageReq req){
        VisualImagePackage visualImagePackage = visualImagePackageRepository.getLatestByStyleCode(req.getStyleCode());
        VisualImagePackage newVisualImagePackage = new VisualImagePackage();
        newVisualImagePackage.setPackageId(IdPool.getId());
        newVisualImagePackage.setStyleCode(req.getStyleCode());

        OnShelfImagePackage onShelfImagePackage = visualImagePackageConverter.trans2OnShelfImagePackage(req);
        visualTaskHelper.checkOnShelfImagePackageImageFileName(onShelfImagePackage);

        newVisualImagePackage.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
        newVisualImagePackage.setMainUrl(onShelfImagePackage.getMainUrl());
        if(visualImagePackage!=null){
            newVisualImagePackage.setOnShelfImages11(visualImagePackage.getOnShelfImages11());
            newVisualImagePackage.setOnShelfImages34(visualImagePackage.getOnShelfImages34());
            newVisualImagePackage.setVideoGenerations(visualImagePackage.getVideoGenerations());
            newVisualImagePackage.setCroppingTaskStatus(visualImagePackage.getCroppingTaskStatus());
            newVisualImagePackage.setVideoGenerationStatus(visualImagePackage.getVideoGenerationStatus());
            newVisualImagePackage.setVersionNum(visualImagePackage.getVersionNum() + 1);
            newVisualImagePackage.setPackageInitTime(visualImagePackage.getPackageInitTime());
            newVisualImagePackage.setPackageInitCreatorId(visualImagePackage.getPackageInitCreatorId());
            newVisualImagePackage.setPackageInitCreatorName(visualImagePackage.getPackageInitCreatorName());
        }else{
            UserContent userContent = UserContentHolder.get();
            newVisualImagePackage.setVersionNum(1);
            newVisualImagePackage.setPackageInitTime(LocalDateTime.now());
            newVisualImagePackage.setPackageInitCreatorId(userContent.getCurrentUserId());
            newVisualImagePackage.setPackageInitCreatorName(userContent.getCurrentUserName());
            if(StringUtils.isNotBlank(newVisualImagePackage.getMainUrl())){
                String imageRatioType = ImageRatioUtils.getImageRatioType(newVisualImagePackage.getMainUrl());
                log.info("{}的主图尺寸是{}", req.getStyleCode(), imageRatioType);
                if(CropSpecificationEnum.RATIO_1_1.getCode().equals(imageRatioType)){
                    newVisualImagePackage.setOnShelfImages11(newVisualImagePackage.getOnShelfImages());
                }else if (CropSpecificationEnum.RATIO_3_4.getCode().equals(imageRatioType)){
                    newVisualImagePackage.setOnShelfImages34(newVisualImagePackage.getOnShelfImages());
                }
            }

        }

        newVisualImagePackage.setIsLatest(1);
        visualImagePackageRepository.save(newVisualImagePackage);


        if(visualImagePackage!=null){
            visualImagePackage.setIsLatest(0);
            visualImagePackageRepository.updateById(visualImagePackage);
        }

        if(visualImagePackage == null && StringUtils.isNotBlank(newVisualImagePackage.getMainUrl())){
            //货通商品不执行裁剪
            SpotSpu spotSpu = spotSpuRepository.getByStyleCode(newVisualImagePackage.getStyleCode());
            if(spotSpu == null || ObjectUtils.notEqual(ProductCommunicationEnum.YES.getCode(), spotSpu.getCommunication())){
                createImageCroppingTask(newVisualImagePackage);
                //发起裁剪任务
                visualTaskHelper.createImageCroppingTaskMq(newVisualImagePackage.getPackageId(),newVisualImagePackage.getStyleCode());
            }
        }
        //通知POP图片变更
        visualTaskHelper.noticePopUpdateImage(newVisualImagePackage.getPackageId(), newVisualImagePackage.getStyleCode());
        return true;
    }

    /**
     * 创建裁剪任务
     * @param newVisualImagePackage
     */
    private void createImageCroppingTask(VisualImagePackage newVisualImagePackage) {
        if(StrUtil.isNotBlank(newVisualImagePackage.getOnShelfImages())){
            OnShelfImagePackage onShelfImagePackage = JSONObject.parseObject(newVisualImagePackage.getOnShelfImages(), OnShelfImagePackage.class);
            String mainUrl = newVisualImagePackage.getMainUrl();
            if(StringUtils.isBlank(mainUrl)){
                log.info(newVisualImagePackage.getStyleCode()+"没有主图不进行裁剪任务");
                return;
            }
            String imageRatioType = ImageRatioUtils.getImageRatioType(mainUrl);
            log.info("{}的主图尺寸是{}", newVisualImagePackage.getStyleCode(), imageRatioType);
            // if(CropSpecificationEnum.RATIO_1_1.getCode().equals(imageRatioType)){
            //     newVisualImagePackage.setOnShelfImages11(newVisualImagePackage.getOnShelfImages());
            // }else if (CropSpecificationEnum.RATIO_3_4.getCode().equals(imageRatioType)){
            //     newVisualImagePackage.setOnShelfImages34(newVisualImagePackage.getOnShelfImages());
            // }
            //
            // visualImagePackageRepository.updateById(newVisualImagePackage);

            List<ImageFile> imageFiles = extractAllImages(onShelfImagePackage);

            // 去重：只处理唯一的URL
            Map<String, ImageFile> uniqueImages = imageFiles.stream()
                    .collect(Collectors.toMap(
                            ImageFile::getOssImageUrl,
                            image -> image,
                            (existing, replacement) -> existing
                    ));

            if (CollectionUtil.isNotEmpty(uniqueImages.values())) {
                if (CropSpecificationEnum.RATIO_1_1.getCode().equals(imageRatioType)) {
                    List<ImageCroppingTask> imageCroppingTasks =
                            uniqueImages.values().stream()
                                    .map(a -> this.convertToCroppingTask(a, CropSpecificationEnum.RATIO_3_4,
                                            newVisualImagePackage.getPackageId(), newVisualImagePackage.getStyleCode()))
                                    .collect(Collectors.toList());
                    imageCroppingTaskRepository.saveBatch(imageCroppingTasks);

                } else if (CropSpecificationEnum.RATIO_3_4.getCode().equals(imageRatioType)) {
                    List<ImageCroppingTask> imageCroppingTasks =
                            uniqueImages.values().stream()
                                    .map(a -> this.convertToCroppingTask(a, CropSpecificationEnum.RATIO_1_1,
                                            newVisualImagePackage.getPackageId(), newVisualImagePackage.getStyleCode()))
                                    .collect(Collectors.toList());
                    imageCroppingTaskRepository.saveBatch(imageCroppingTasks);
                } else {
                    List<ImageCroppingTask> imageCroppingTasks11 =
                            uniqueImages.values().stream()
                                    .map(a -> this.convertToCroppingTask(a, CropSpecificationEnum.RATIO_3_4,
                                            newVisualImagePackage.getPackageId(), newVisualImagePackage.getStyleCode()))
                                    .collect(Collectors.toList());

                    List<ImageCroppingTask> imageCroppingTasks34 =
                            uniqueImages.values().stream()
                                    .map(a -> this.convertToCroppingTask(a, CropSpecificationEnum.RATIO_1_1,
                                            newVisualImagePackage.getPackageId(), newVisualImagePackage.getStyleCode()))
                                    .collect(Collectors.toList());

                    imageCroppingTasks11.addAll(imageCroppingTasks34);
                    imageCroppingTaskRepository.saveBatch(imageCroppingTasks11);
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save4DigitalPrinting(SaveOnShelfImagePackageReq req) {
        VisualImagePackage visualImagePackage = visualImagePackageRepository.getLatestByStyleCode(req.getStyleCode());
        if (Objects.nonNull(visualImagePackage)) {
            return;
        }

        //新增视觉spu
        visualSpuService.initVisualSpu(req.getStyleCode());

        //新增图包
        this.saveOnShelfImagePackage(req);
    }

    @Override
    public VisualImagePackageVo getVisualImagePackageVoByPackageId(Long packageId){
        VisualImagePackage visualImagePackage = visualImagePackageRepository.getById(packageId);
        return visualImagePackageConverter.trans2VisualImagePackageVo(visualImagePackage);
    }

    @Override
    public VisualImagePackageVo getVisualImagePackageVoByStyleCode(String styleCode){
        VisualImagePackage visualImagePackage = visualImagePackageRepository.getLatestByStyleCode(styleCode);
        return visualImagePackageConverter.trans2VisualImagePackageVo(visualImagePackage);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cleanImagePackage(CleanImagePackageReq req){
        List<VisualImagePackage> list = visualImagePackageRepository.listLatestByStyleCodes(req.getStyleCodes());
        Assert.isTrue(CollectionUtil.isNotEmpty(list),"图包不存在");
        List<VisualImagePackage> newVisualImagePackageList = new ArrayList<>();
        list.forEach(v->{
            VisualImagePackage newImagePackage = new VisualImagePackage();
            newImagePackage.setPackageId(IdPool.getId());
            newImagePackage.setStyleCode(v.getStyleCode());
            newImagePackage.setOnShelfImages(null);
            newImagePackage.setPackageInitTime(v.getPackageInitTime());
            newImagePackage.setMainUrl(null);
            newImagePackage.setOnShelfImages11(null);
            newImagePackage.setOnShelfImages34(null);
            newImagePackage.setIsLatest(1);
            newImagePackage.setVersionNum(v.getVersionNum() + 1);
            newVisualImagePackageList.add(newImagePackage);
            v.setIsLatest(0);
        });
        visualImagePackageRepository.saveBatch(newVisualImagePackageList);
        visualImagePackageRepository.updateBatchById(list);
        newVisualImagePackageList.forEach(v->{
            //通知POP图片变更
            visualTaskHelper.noticePopUpdateImage(v.getPackageId(), v.getStyleCode());
        });
        return true;
    }

    @Override
    public Long submitSpuImagePackageDownloadTask(DownloadSpuImagePackageReq req){
        DesignAsyncTaskTypeEnum asyncTaskType = DesignAsyncTaskTypeEnum.DOWNLOAD_VISUAL_IMAGE_PACKAGE;
        String taskName = asyncTaskType.getDesc()+"_"+PURE_DATETIME_PATTERN.format(LocalDateTime.now());
        String parameters = JSONObject.toJSONString(req.getSpuCodeList());
        return downloadTaskService.createTask(taskName,asyncTaskType,parameters);
    }

    @Override
    public List<ImageBatchUploadErrorResp> batchUpdateImages(ImageBatchUpdateReq req) {
        List<ImageBatchUploadErrorResp> uploadResultFailList = new ArrayList<>();
        List<ImageBatchUpdateReq.ImageInfo> imageReqList = req.getImageList();
        //记录前端校验失败的数据
        List<ImageBatchUpdateReq.FrontErrorMsg> frontErrorMsgList = req.getFrontErrorMsgList();
        if(CollectionUtil.isNotEmpty(frontErrorMsgList)){
            frontErrorMsgList.forEach(errorMsg->{
                uploadResultFailList.add(ImageBatchUploadErrorResp.builder()
                        .fileName(errorMsg.getFileName()).reason(errorMsg.getErrorMsg()).build());
            });
        }
        if (CollUtil.isEmpty(imageReqList)) {
            uploadResultFailList.add(ImageBatchUploadErrorResp.builder().reason("提交图片为空").build());
        }else {
            //处理前端校验通过的数据
            Map<String, ImageBatchUpdateReq.FrontErrorMsg> errorFileMap = StreamUtil.list2Map(frontErrorMsgList, ImageBatchUpdateReq.FrontErrorMsg::getFileName);
            Map<String, List<ImageFile>> imagesBySpuCodeMap = this.getRightImageMap(imageReqList, errorFileMap, uploadResultFailList);
            Set<String> styleCodeSet = imagesBySpuCodeMap.keySet();
            if (CollUtil.isNotEmpty(styleCodeSet)) {
                //spu查询最新图包
                List<VisualImagePackage> imagePackageList = visualImagePackageRepository.listLatestByStyleCodes(styleCodeSet);
                if(CollectionUtil.isEmpty(imagePackageList)){
                    uploadResultFailList.addAll(styleCodeSet.stream()
                            .map(styleCode -> {
                                List<String> noSpuFileNameList = StreamUtil.convertListAndDistinct(imagesBySpuCodeMap.get(styleCode), ImageFile::getOrgImgName);
                                return ImageBatchUploadErrorResp.builder()
                                        .styleCode(styleCode).fileName(StrUtil.join(StrUtil.COMMA, noSpuFileNameList)).reason("SPU图包不存在")
                                        .build();
                            })
                            .toList());
                }else {
                    Set<String> existStyleCodes = imagePackageList.stream().map(VisualImagePackage::getStyleCode).collect(Collectors.toSet());
                    List<VisualSpu> visualSpuList = visualSpuRepository.listByStyleCodes(existStyleCodes);
                    Map<String, VisualSpu> visualSpuMap = StreamUtil.list2Map(visualSpuList, VisualSpu::getStyleCode);
                    Map<String, VisualImagePackage> imagePackageMap = StreamUtil.list2Map(imagePackageList, VisualImagePackage::getStyleCode);

                    //图片更新处理
                    this.handleBatchUpdateImage(imagesBySpuCodeMap, visualSpuMap, uploadResultFailList, imagePackageMap);
                }
            }
        }

        //创建异常信息下载任务
        this.handleImagePackageUploadFail(uploadResultFailList, DesignAsyncTaskTypeEnum.IMAGE_PACKAGE_BATCH_UPLOAD_FAIL);
        return uploadResultFailList;
    }

    private void handleBatchUpdateImage(Map<String, List<ImageFile>> imagesBySpuCodeMap,
                                        Map<String, VisualSpu> visualSpuMap,
                                        List<ImageBatchUploadErrorResp> uploadResultFailList,
                                        Map<String, VisualImagePackage> imagePackageMap) {
        if (CollUtil.isEmpty(imagesBySpuCodeMap)) {
            return;
        }
        imagesBySpuCodeMap.forEach((styleCode, newImageList) -> {
            List<String> noSpuFileNameList = StreamUtil.convertListAndDistinct(newImageList, ImageFile::getOrgImgName);
            if (Objects.isNull(visualSpuMap.get(styleCode))) {
                uploadResultFailList.add(ImageBatchUploadErrorResp.builder()
                        .styleCode(styleCode).fileName(StrUtil.join(StrUtil.COMMA, noSpuFileNameList)).reason("SPU不存在")
                        .build());
            } else {
                VisualImagePackage imagePackage = imagePackageMap.get(styleCode);
                try {
                    OnShelfImagePackage oldOnShelfImage = JSONObject.parseObject(imagePackage.getOnShelfImages(),OnShelfImagePackage.class);
                    //获取spu,skc图片集合
                    List<ImageFile> oldImageFileList = this.builImageFileList(oldOnShelfImage);
                    //查找整个图包管理中的所有图片是否存在对应同名的图片文件，如有则替换掉对应图片升版本后流转pop，若无则抛错误到excel中
                    if (CollectionUtil.isEmpty(oldImageFileList)) {
                        //若图包为空, 不替换, 记录失败
                        uploadResultFailList.add(ImageBatchUploadErrorResp.builder()
                                .styleCode(styleCode).fileName(StrUtil.join(StrUtil.COMMA, noSpuFileNameList)).reason("图包为空").build());
                    } else {
                        //找出同名的图片, 替换url后升版本
                        Map<String, ImageFile> oldImageMap = StreamUtil.list2Map(oldImageFileList, ImageFile::getOrgImgName);
                        List<String> updateFileList = new ArrayList<>();
                        List<String> failFileList = new ArrayList<>();
                        newImageList.forEach(newImage -> {
                            ImageFile oldImageFile = oldImageMap.get(newImage.getOrgImgName());
                            if (Objects.nonNull(oldImageFile)) {
                                oldImageFile.setOssImageUrl(newImage.getOssImageUrl());
                                updateFileList.add(newImage.getOrgImgName());
                            }else {
                                failFileList.add(newImage.getOrgImgName());
                                uploadResultFailList.add(ImageBatchUploadErrorResp.builder()
                                        .styleCode(styleCode).fileName(newImage.getOrgImgName()).reason("图片不存在").build());
                            }
                        });
                        //更新url后重新提交
                        if (CollUtil.isNotEmpty(updateFileList)) {
                            //解析归类图片
                            OnShelfImagePackage onShelfImagePackage = visualTaskHelper.classifyImage(visualSpuMap.get(styleCode), oldImageFileList);
                            this.saveImagePackage(onShelfImagePackage);
                        }
                        log.info("替换SPU图片完成，spu:{}; 成功:{}，失败:{}",
                                styleCode, JSON.toJSONString(updateFileList), JSON.toJSONString(failFileList));
                    }
                } catch (Exception e) {
                    log.error("图包图片批量更新异常, styleCode:" + styleCode, e);
                    uploadResultFailList.add(ImageBatchUploadErrorResp.builder()
                            .styleCode(styleCode).reason("系统异常" + e.getMessage()).build());
                }
            }
        });
    }

    private List<ImageFile> builImageFileList(OnShelfImagePackage oldOnShelfImage) {
        List<ImageFile> oldImageFileList = new ArrayList<>();
        if (CollUtil.isNotEmpty(oldOnShelfImage.getSpuImages())) {
            oldOnShelfImage.getSpuImages().stream()
                    .filter(spuImage -> CollUtil.isNotEmpty(spuImage.getImages()))
                    .forEach(spuImage -> oldImageFileList.addAll(spuImage.getImages()));
        }
        if (CollUtil.isNotEmpty(oldOnShelfImage.getSkcImages())) {
            oldOnShelfImage.getSkcImages().stream()
                    .filter(skcImage -> CollUtil.isNotEmpty(skcImage.getImages()))
                    .forEach(skcImage -> oldImageFileList.addAll(skcImage.getImages()));
        }
        return oldImageFileList;
    }

    private void handleImagePackageUploadFail(List<ImageBatchUploadErrorResp> uploadResultFailList, DesignAsyncTaskTypeEnum asyncTaskType){
        if(CollectionUtil.isNotEmpty(uploadResultFailList) && Objects.nonNull(asyncTaskType)){
            String taskName = asyncTaskType.getDesc() + StrUtil.UNDERLINE + PURE_DATETIME_PATTERN.format(LocalDateTime.now());
            String parameters = JSONObject.toJSONString(uploadResultFailList);
            downloadTaskService.createTask(taskName,asyncTaskType,parameters);
        }
    }

    private Map<String, List<ImageFile>> getRightImageMap(List<ImageBatchUpdateReq.ImageInfo> imageReqList, Map<String, ImageBatchUpdateReq.FrontErrorMsg> errorFileMap, List<ImageBatchUploadErrorResp> uploadResultFailList) {
        List<ImageBatchUpdateReq.ImageInfo> imageInfoList = imageReqList.stream()
                .filter(v-> Objects.isNull(errorFileMap.get(v.getOrgImgName()))).toList();

        //文件名或url为空的图片
        imageInfoList.stream()
                .filter(image -> StrUtil.isBlank(image.getOrgImgName()) || StrUtil.isBlank(image.getOssImageUrl()))
                .forEach(image -> {
                    uploadResultFailList.add(ImageBatchUploadErrorResp.builder()
                            .fileName(image.getOrgImgName()).reason("图片名与url不能为空").build());
                });

        // 记录格式不符的图片
        imageInfoList.stream()
                .filter(image -> StrUtil.isNotBlank(image.getOrgImgName()) && !image.getOrgImgName().contains(StrUtil.DASHED))
                .forEach(image -> {
                    uploadResultFailList.add(ImageBatchUploadErrorResp.builder().fileName(image.getOrgImgName()).reason("图片名称格式不符合要求，应包含'-'").build());
                });

        //筛选出符合命名规则的图片（包含"-"）
        List<ImageFile> validImageList = imageInfoList.stream()
                .filter(image -> StrUtil.isNotBlank(image.getOrgImgName()) && StrUtil.isNotBlank(image.getOssImageUrl()))
                .filter(image -> image.getOrgImgName().contains(StrUtil.DASHED))
                .map(image -> {
                    ImageFile imageFile = new ImageFile();
                    BeanUtils.copyProperties(image, imageFile);
                    return imageFile;
                })
                .toList();

        return this.validImageGroupMap(validImageList);
    }

    private Map<String, List<ImageFile>> validImageGroupMap(List<ImageFile> validImages) {
        Map<String, List<ImageFile>> imagesBySpuCodeMap = new HashMap<>();
        for (ImageFile image : validImages) {
            String spuCode = image.getOrgImgName().substring(0, image.getOrgImgName().lastIndexOf(StrUtil.DASHED));
            imagesBySpuCodeMap.computeIfAbsent(spuCode, k -> new ArrayList<>()).add(image);
        }
        return imagesBySpuCodeMap;
    }


    @Override
    public List<ImageBatchUploadErrorResp> batchUpdateImageByFolder(ImageBatchUpdateFolderReq req) {
        List<ImageBatchUploadErrorResp> uploadResultFailList = new ArrayList<>();
        List<ImageBatchUpdateFolderReq.SpuImage> spuImageList = req.getSpuImageList();
        if (CollUtil.isEmpty(spuImageList)) {
            uploadResultFailList.add(ImageBatchUploadErrorResp.builder().reason("提交图片为空").build());
        }else {
            //记录前端校验失败的数据
            List<ImageBatchUpdateFolderReq.SpuImage> errorSpuList = spuImageList.stream().filter(v->CollectionUtil.isNotEmpty(v.getFrontErrorMsgList())).toList();
            if(CollectionUtil.isNotEmpty(errorSpuList)){
                errorSpuList.forEach(spuImage->{
                    spuImage.getFrontErrorMsgList().forEach(errorMsg->{
                        ImageBatchUploadErrorResp f = new ImageBatchUploadErrorResp();
                        f.setStyleCode(spuImage.getStyleCode());
                        f.setFileName(errorMsg.getFileName());
                        f.setReason(errorMsg.getErrorMsg());
                        uploadResultFailList.add(f);
                    });
                });
            }


            //处理前端校验通过的数据
            this.handleUpdateImageFolder(spuImageList, uploadResultFailList);
        }

        //创建异常信息下载任务
        handleImagePackageUploadFail(uploadResultFailList, DesignAsyncTaskTypeEnum.IMAGE_PACKAGE_BATCH_UPLOAD_FOLDER_FAIL);
        return uploadResultFailList;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveOnShelfImagePackageV2(CreateVisualImagePackageRequest req) {
        SaveOnShelfImagePackageReq onShelfImages = req.getOnShelfImages();
        VisualImagePackage visualImagePackage = visualImagePackageRepository.getLatestByStyleCode(onShelfImages.getStyleCode());
        VisualImagePackage newVisualImagePackage = new VisualImagePackage();
        newVisualImagePackage.setPackageId(IdPool.getId());
        newVisualImagePackage.setStyleCode(onShelfImages.getStyleCode());

        OnShelfImagePackage onShelfImagePackage = visualImagePackageConverter.trans2OnShelfImagePackage(req.getOnShelfImages());
        visualTaskHelper.checkOnShelfImagePackageImageFileName(onShelfImagePackage);

        OnShelfImagePackage onShelfImagePackage11 = visualImagePackageConverter.trans2OnShelfImagePackage(req.getOnShelfImages11());
        visualTaskHelper.checkOnShelfImagePackageImageFileName(onShelfImagePackage11);

        OnShelfImagePackage onShelfImagePackage34 = visualImagePackageConverter.trans2OnShelfImagePackage(req.getOnShelfImages34());
        visualTaskHelper.checkOnShelfImagePackageImageFileName(onShelfImagePackage34);



        newVisualImagePackage.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
        newVisualImagePackage.setOnShelfImages11(JSONObject.toJSONString(onShelfImagePackage11));
        newVisualImagePackage.setOnShelfImages34(JSONObject.toJSONString(onShelfImagePackage34));
        if(CollectionUtil.isNotEmpty(req.getVideoGenerations())){
            newVisualImagePackage.setVideoGenerations(JSONObject.toJSONString(req.getVideoGenerations()));
        }
        newVisualImagePackage.setMainUrl(onShelfImagePackage.getMainUrl());
        if(visualImagePackage!=null){
            newVisualImagePackage.setCroppingTaskStatus(visualImagePackage.getCroppingTaskStatus());
            newVisualImagePackage.setVideoGenerationStatus(visualImagePackage.getVideoGenerationStatus());
            newVisualImagePackage.setVersionNum(visualImagePackage.getVersionNum() + 1);
            newVisualImagePackage.setPackageInitTime(visualImagePackage.getPackageInitTime());
            newVisualImagePackage.setPackageInitCreatorId(visualImagePackage.getPackageInitCreatorId());
            newVisualImagePackage.setPackageInitCreatorName(visualImagePackage.getPackageInitCreatorName());
        }else{
            UserContent userContent = UserContentHolder.get();
            newVisualImagePackage.setVersionNum(1);
            newVisualImagePackage.setPackageInitTime(LocalDateTime.now());
            newVisualImagePackage.setPackageInitCreatorId(userContent.getCurrentUserId());
            newVisualImagePackage.setPackageInitCreatorName(userContent.getCurrentUserName());
            if(StringUtils.isNotBlank(newVisualImagePackage.getMainUrl())){
                String imageRatioType = ImageRatioUtils.getImageRatioType(newVisualImagePackage.getMainUrl());
                log.info("{}的主图尺寸是{}", newVisualImagePackage.getStyleCode(), imageRatioType);
                if(CropSpecificationEnum.RATIO_1_1.getCode().equals(imageRatioType)){
                    newVisualImagePackage.setOnShelfImages11(newVisualImagePackage.getOnShelfImages());
                }else if (CropSpecificationEnum.RATIO_3_4.getCode().equals(imageRatioType)){
                    newVisualImagePackage.setOnShelfImages34(newVisualImagePackage.getOnShelfImages());
                }
            }
        }

        newVisualImagePackage.setIsLatest(1);
        visualImagePackageRepository.save(newVisualImagePackage);


        if(visualImagePackage!=null){
            visualImagePackage.setIsLatest(0);
            visualImagePackageRepository.updateById(visualImagePackage);
        }

        if(visualImagePackage == null && StringUtils.isNotBlank(newVisualImagePackage.getMainUrl())){
            createImageCroppingTask(newVisualImagePackage);
            //发起裁剪任务
            visualTaskHelper.createImageCroppingTaskMq(newVisualImagePackage.getPackageId(),newVisualImagePackage.getStyleCode());
        }
        //通知POP图片变更
        visualTaskHelper.noticePopUpdateImage(newVisualImagePackage.getPackageId(), newVisualImagePackage.getStyleCode());
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateVisualImagePackageWithCroppedImages(List<ImageCroppingTask> croppingTasks) {
        if (croppingTasks == null || croppingTasks.isEmpty()) {
            return;
        }

        Long packageId = croppingTasks.getFirst().getPackageId();

        VisualImagePackage visualImagePackage = visualImagePackageRepository.getById(packageId);
        if(visualImagePackage == null || StringUtils.isBlank(visualImagePackage.getOnShelfImages())){
            return;
        }

        // 从visualImagePackage中获取原始上架图包
        OnShelfImagePackage originalOnShelfImagePackage = JSONObject.parseObject(
                visualImagePackage.getOnShelfImages(),
                OnShelfImagePackage.class
        );

        if (originalOnShelfImagePackage == null) {
            return;
        }

        // 按裁剪规格分组 (1:1:1, 3:4:2)
        Map<Integer, List<ImageCroppingTask>> tasksByRatio = croppingTasks.stream()
                .collect(Collectors.groupingBy(ImageCroppingTask::getCroppingType));

        // 处理1:1的裁剪任务 (croppingType = 1)
        List<ImageCroppingTask> tasks11 = tasksByRatio.get(CropSpecificationEnum.RATIO_1_1.getValue());
        if (tasks11 != null && !tasks11.isEmpty()) {
            // 深拷贝原始包
            OnShelfImagePackage package11 = deepCopyOnShelfImagePackage(originalOnShelfImagePackage);
            replaceImageUrls(package11, tasks11);
            // 转换为JSON字符串并更新visualImagePackage
            visualImagePackage.setOnShelfImages11(JSONObject.toJSONString(package11));
        }

        // 处理3:4的裁剪任务 (croppingType = 2)
        List<ImageCroppingTask> tasks34 = tasksByRatio.get(2);
        if (tasks34 != null && !tasks34.isEmpty()) {
            // 深拷贝原始包
            OnShelfImagePackage package34 = deepCopyOnShelfImagePackage(originalOnShelfImagePackage);
            replaceImageUrls(package34, tasks34);
            // 转换为JSON字符串并更新visualImagePackage
            visualImagePackage.setOnShelfImages34(JSONObject.toJSONString(package34));
        }
        visualImagePackage.setCroppingTaskStatus(CroppedTaskStatusEnum.SUCCESS.getCode());
        // 保存更新后的visualImagePackage
        visualImagePackageRepository.updateById(visualImagePackage);

        //通知POP图片变更
        visualTaskHelper.noticeCuttingImageUpsert(visualImagePackage.getPackageId(), visualImagePackage.getStyleCode());
    }


    @Override
    public MigrateImagePackageResp migrateImagePackageByRatio(MigrateImagePackageReq req) {
        MigrateImagePackageResp resp = new MigrateImagePackageResp();
        long startTime = System.currentTimeMillis();

        // 使用线程安全的计数器
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger skipCount = new AtomicInteger(0);
        List<String> failedStyleCodes = Collections.synchronizedList(new ArrayList<>());

        // 添加图片比例缓存
        Map<String, String> imageRatioCache = new ConcurrentHashMap<>();

        try {
            // 分页处理
            int pageNum = 1;
            int batchSize = req.getBatchSize() != null ? Math.min(req.getBatchSize(), 1000) : 1000; // 减小批次大小
            IPage<VisualImagePackage> pageResult;

            do {
                // 使用Repository中的方法进行分页查询
                pageResult = visualImagePackageRepository.pageForMigration(pageNum, batchSize,
                        req.getStyleCodes()
                );

                List<VisualImagePackage> packages = pageResult.getRecords();
                if (CollectionUtil.isEmpty(packages)) {
                    break;
                }

                // 使用并行流处理当前批次
                List<VisualImagePackage> packagesToUpdate = packages.parallelStream()
                        .map(vip -> {
                            int currentProcessed = processedCount.incrementAndGet();
                            try {
                                String imageUrl = vip.getMainUrl();

                                // 检查缓存中是否已有该图片的比例信息
                                String ratioType = imageRatioCache.get(imageUrl);
                                if (ratioType == null) {
                                    // 缓存中没有，则获取图片比例
                                    ratioType = ImageRatioUtils.getImageRatioType(imageUrl);
                                    // 将结果存入缓存
                                    imageRatioCache.put(imageUrl, ratioType);
                                }

                                // 减少日志输出频率
                                if (currentProcessed % 100 == 0) {
                                    log.info("{}的主图尺寸是{}", vip.getStyleCode(), ratioType);
                                }

                                boolean needUpdate = false;

                                if (CropSpecificationEnum.RATIO_1_1.getCode().equals(ratioType)) {
                                    if (StringUtils.isBlank(vip.getOnShelfImages11()) ||
                                            !vip.getOnShelfImages().equals(vip.getOnShelfImages11())) {
                                        vip.setOnShelfImages11(vip.getOnShelfImages());
                                        needUpdate = true;
                                    }
                                } else if (CropSpecificationEnum.RATIO_3_4.getCode().equals(ratioType)) {
                                    if (StringUtils.isBlank(vip.getOnShelfImages34()) ||
                                            !vip.getOnShelfImages().equals(vip.getOnShelfImages34())) {
                                        vip.setOnShelfImages34(vip.getOnShelfImages());
                                        needUpdate = true;
                                    }
                                } else {
                                    skipCount.incrementAndGet();
                                    if (currentProcessed % 100 == 0) {
                                        log.info("{}的主图比例不符合要求，跳过", vip.getStyleCode());
                                    }
                                    return null;
                                }

                                if (needUpdate) {
                                    successCount.incrementAndGet();
                                    return vip;
                                } else {
                                    skipCount.incrementAndGet();
                                    if (currentProcessed % 100 == 0) {
                                        log.info("{}的数据已是最新，无需更新", vip.getStyleCode());
                                    }
                                    return null;
                                }
                            } catch (Exception e) {
                                failedStyleCodes.add(vip.getStyleCode());
                                log.error("处理SPU {} 时发生错误", vip.getStyleCode(), e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 批量更新当前批次
                if (CollectionUtil.isNotEmpty(packagesToUpdate)) {
                    visualImagePackageRepository.updateBatchById(packagesToUpdate);
                    log.info("已批量更新 {} 条数据", packagesToUpdate.size());
                }

                pageNum++;

                // 记录进度
                int currentTotalProcessed = processedCount.get();
                if (currentTotalProcessed % 1000 == 0) {
                    log.info("已处理 {}/{} 条数据", currentTotalProcessed, pageResult.getTotal());
                }

            } while (pageNum <= pageResult.getPages());

        } finally {
            long endTime = System.currentTimeMillis();
            int finalProcessedCount = processedCount.get();
            int finalSuccessCount = successCount.get();
            int finalSkipCount = skipCount.get();

            log.info("数据迁移完成，总共处理 {} 条数据，成功 {} 条，跳过 {} 条，失败 {} 条，耗时 {} 毫秒",
                    finalProcessedCount, finalSuccessCount, finalSkipCount, failedStyleCodes.size(), endTime - startTime);
        }

        resp.setProcessedCount(processedCount.get());
        resp.setSuccessCount(successCount.get());
        resp.setSkipCount(skipCount.get());
        resp.setFailedCount(failedStyleCodes.size());
        resp.setFailedStyleCodes(failedStyleCodes);
        resp.setExecutionTime(System.currentTimeMillis() - startTime);

        return resp;
    }


    @Override
    public void dataBrushBatchCrop(MigrateImagePackageReq req) {
        if(CollectionUtil.isEmpty(req.getStyleCodes())){
            log.info("开始批量裁剪任务，参数为空: {}");
            return;
        }
        long startTime = System.currentTimeMillis();
        log.info("开始批量裁剪任务，参数: {}", JSON.toJSONString(req));

        // 分页处理
        int pageNum = 1;
        int batchSize = req.getBatchSize() != null ? Math.min(req.getBatchSize(), 500) : 500; // 减小批次大小
        int totalProcessed = 0;
        IPage<VisualImagePackage> pageResult;

        try {
            do {
                // 使用Repository中的方法进行分页查询
                pageResult = visualImagePackageRepository.pageForMigration(pageNum, batchSize, req.getStyleCodes());
                List<VisualImagePackage> packages = pageResult.getRecords();

                if (CollectionUtil.isEmpty(packages)) {
                    break;
                }

                for (VisualImagePackage vip : packages) {
                    try {
                        // 使用编程式事务处理每个图像包
                        transactionTemplate.execute(status -> {
                            processImagePackage(vip);
                            return null;
                        });

                        totalProcessed++;
                    } catch (Exception e) {
                        log.error("处理图像包失败, packageId: {}, styleCode: {}", vip.getPackageId(), vip.getStyleCode(), e);
                        // 继续处理下一个，不中断批量任务
                    }
                }

                // 记录进度
                log.info("批量裁剪任务进度: 已处理 {}/{} 条数据", totalProcessed, pageResult.getTotal());
                pageNum++;

            } while (pageNum <= pageResult.getPages());

            long endTime = System.currentTimeMillis();
            log.info("批量裁剪任务完成，总共处理: {} 条数据，总耗时: {} ms", totalProcessed, endTime - startTime);

        } catch (Exception e) {
            log.error("批量裁剪任务执行失败", e);
            throw new RuntimeException("批量裁剪任务执行失败", e);
        }
    }


    /**
     * 处理单个图像包（带有事务）
     */
    @Transactional(rollbackFor = Exception.class)
    public void processImagePackage(VisualImagePackage vip) {
        // 创建裁剪任务
        createImageCroppingTask(vip);
        // 发送MQ消息
        visualTaskHelper.createImageCroppingTaskMq(vip.getPackageId(), vip.getStyleCode());
    }

    /**
     * 深拷贝OnShelfImagePackage
     */
    private OnShelfImagePackage deepCopyOnShelfImagePackage(OnShelfImagePackage original) {
        String json = JSONObject.toJSONString(original);
        return JSONObject.parseObject(json, OnShelfImagePackage.class);
    }

    private void handleUpdateImageFolder(List<ImageBatchUpdateFolderReq.SpuImage> spuImageList, List<ImageBatchUploadErrorResp> uploadResultFailList) {
        List<ImageBatchUpdateFolderReq.SpuImage> filterReqList = StreamUtil.filter(spuImageList, v -> CollectionUtil.isEmpty(v.getFrontErrorMsgList()));
        if (CollUtil.isEmpty(filterReqList)) {
            return;
        }
        Map<String, ImageBatchUpdateFolderReq.SpuImage> styleCodeToImageFileMap = StreamUtil.list2Map(filterReqList, ImageBatchUpdateFolderReq.SpuImage::getStyleCode);
        List<VisualSpu> spuList = visualSpuRepository.listByStyleCodes(styleCodeToImageFileMap.keySet());
        if(CollectionUtil.isEmpty(spuList)){
            uploadResultFailList.addAll(styleCodeToImageFileMap.keySet().stream().map(styleCode -> {
                ImageBatchUploadErrorResp f = new ImageBatchUploadErrorResp();
                f.setStyleCode(styleCode);
                f.setReason("SPU不存在");
                return f;
            }).toList());
        }else {
            Set<String> existStyleCodes = spuList.stream().map(VisualSpu::getStyleCode).collect(Collectors.toSet());
            List<VisualSpu> visualSpuList = visualSpuRepository.listByStyleCodes(existStyleCodes);
            Map<String, VisualSpu> visualSpuMap = StreamUtil.list2Map(visualSpuList, VisualSpu::getStyleCode);
            styleCodeToImageFileMap.forEach((styleCode, imageReq) -> {
                if (Objects.isNull(visualSpuMap.get(styleCode))) {
                    ImageBatchUploadErrorResp f = new ImageBatchUploadErrorResp();
                    f.setStyleCode(styleCode);
                    f.setReason("SPU不存在");
                    uploadResultFailList.add(f);
                } else {
                    //文件夹：查找是否存在对应文件夹名称的图包，如有则整个替换掉对应图包中所有图片升版本后流转pop，若无则抛错误到excel中。0717
                    VisualSpu visualSpu = visualSpuMap.get(styleCode);
                    if (CollUtil.isEmpty(imageReq.getImageFiles())) {
                        uploadResultFailList.add(ImageBatchUploadErrorResp.builder().styleCode(styleCode).reason("spu图片为空").build());
                        return;
                    }
                    List<ImageFile> imageFileList = StreamUtil.convertListBean(imageReq.getImageFiles(), ImageFile.class);
                    try {
                        //解析归类图片
                        OnShelfImagePackage onShelfImagePackage = visualTaskHelper.classifyImage(visualSpu, imageFileList);
                        if (Objects.isNull(onShelfImagePackage) || CollUtil.isEmpty(onShelfImagePackage.getSpuImages())) {
                            return;
                        }
                        //保存图包(升版本)
                        this.saveImagePackage(onShelfImagePackage);
                    } catch (Exception e) {
                        log.error("图包图片批量更新异常, styleCode:" + styleCode, e);
                        uploadResultFailList.add(ImageBatchUploadErrorResp.builder().styleCode(styleCode).reason("系统异常" + e.getMessage()).build());
                    }
                }
            });
        }
    }


    /**
     * 拿出上架图包所有的图片  只取imageType为30/50/60的图片
     * @param imagePackage
     * @return
     */
    public List<ImageFile> extractAllImages(OnShelfImagePackage imagePackage) {
        List<ImageFile> allImages = new ArrayList<>();

        // 处理SPU图片：只取imageType为30/50/60的图片
        if (imagePackage.getSpuImages() != null) {
            allImages.addAll(
                    imagePackage.getSpuImages().stream()
                            .filter(spuImage ->
                                    spuImage.getImages() != null &&
                                            ("30".equals(spuImage.getImageType()) ||
                                                    "50".equals(spuImage.getImageType()) ||
                                                    "60".equals(spuImage.getImageType())))
                            .flatMap(spuImage -> spuImage.getImages().stream())
                            .collect(Collectors.toList())
            );
        }

        // 处理SKC图片：取所有图片
        if (imagePackage.getSkcImages() != null) {
            allImages.addAll(
                    imagePackage.getSkcImages().stream()
                            .filter(skcImage -> skcImage.getImages() != null)
                            .flatMap(skcImage -> skcImage.getImages().stream())
                            .collect(Collectors.toList())
            );
        }

        return allImages;
    }



    private ImageCroppingTask convertToCroppingTask(
            ImageFile imageFile,
            CropSpecificationEnum croppingType,
            Long packageId,
            String styleCode) {

        ImageCroppingTask task = new ImageCroppingTask();

        // 设置基本字段
        task.setImageCroppingTaskId(IdPool.getId());
        task.setImageName(imageFile.getOrgImgName());
        task.setOriginalImageUrl(imageFile.getOssImageUrl());

        // 设置枚举相关字段
        if (croppingType != null) {
            task.setCroppingType(croppingType.getValue());
            task.setTargetWidth(croppingType.getWidth());
            task.setTargetHeight(croppingType.getHeight());
        }
        // 设置关联信息
        task.setPackageId(packageId);
        task.setStyleCode(styleCode);

        // 设置默认任务状态为已提交
        task.setTaskStatus(CroppedTaskStatusEnum.PENDING_SUBMISSION.getCode()); // 0-待提交

        // 设置默认重试参数
        task.setRetryCount(0);
        task.setMaxRetries(3); // 默认最大重试3次

        return task;
    }



    /**
     * 替换上架图包中的图片URL
     * @param imagePackage 上架图包
     * @param croppingTasks 裁剪任务列表
     */
    private void replaceImageUrls(OnShelfImagePackage imagePackage, List<ImageCroppingTask> croppingTasks) {
        // 构建原始URL到裁剪后URL的映射
        Map<String, String> urlMapping = croppingTasks.stream()
                .collect(Collectors.toMap(
                        ImageCroppingTask::getOriginalImageUrl,
                        ImageCroppingTask::getResultImageUrl,
                        (existing, replacement) -> existing // 如果有重复键，保留现有值
                ));

        // 替换SPU图片：只处理imageType为30/50/60的图片
        if (imagePackage.getSpuImages() != null) {
            for (OnShelfImagePackage.SpuImage spuImage : imagePackage.getSpuImages()) {
                // 只处理指定类型的SPU图片
                if (spuImage.getImages() != null &&
                        ("30".equals(spuImage.getImageType()) ||
                                "50".equals(spuImage.getImageType()) ||
                                "60".equals(spuImage.getImageType()))) {
                    for (ImageFile imageFile : spuImage.getImages()) {
                        String originalUrl = imageFile.getOssImageUrl();
                        if (urlMapping.containsKey(originalUrl)) {
                            imageFile.setOssImageUrl(urlMapping.get(originalUrl));
                        }
                    }
                }
            }
        }

        // 替换SKC图片
        if (imagePackage.getSkcImages() != null) {
            for (OnShelfImagePackage.SkcImage skcImage : imagePackage.getSkcImages()) {
                if (skcImage.getImages() != null) {
                    for (ImageFile imageFile : skcImage.getImages()) {
                        String originalUrl = imageFile.getOssImageUrl();
                        if (urlMapping.containsKey(originalUrl)) {
                            imageFile.setOssImageUrl(urlMapping.get(originalUrl));
                        }
                    }
                }
            }
        }
    }
}
