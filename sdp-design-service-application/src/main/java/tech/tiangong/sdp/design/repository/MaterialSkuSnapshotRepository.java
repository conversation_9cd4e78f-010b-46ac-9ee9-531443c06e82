package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.MaterialSkuSnapshot;
import tech.tiangong.sdp.design.mapper.MaterialSkuSnapshotMapper;
import tech.tiangong.sdp.design.vo.query.MaterialSkuSnapshotQuery;
import tech.tiangong.sdp.design.vo.resp.material.MaterialSkuSnapshotVo;

/**
 * 好料网sku快照表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class MaterialSkuSnapshotRepository extends BaseRepository<MaterialSkuSnapshotMapper, MaterialSkuSnapshot> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<MaterialSkuSnapshotVo> findPage(MaterialSkuSnapshotQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }
}
