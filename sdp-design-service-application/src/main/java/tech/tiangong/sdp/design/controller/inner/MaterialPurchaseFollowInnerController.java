package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.MaterialPurchaseFollowService;
import tech.tiangong.sdp.design.vo.req.ordermaterial.OrderMaterialStatusInnerReq;

/**
 * 面辅料采购跟进表
 * <br>CreateDate August 10,2021
 *
 * <AUTHOR>
 * @since 1.0
 */
@RestController
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/material/purchase")
public class MaterialPurchaseFollowInnerController extends BaseController {

    @Autowired
    private MaterialPurchaseFollowService materialpurchaseFollowService;

    /**
     * 根据加工单号查询是否已经下过采购单
     *
     * @param req
     * @return boolean  true 是  false 否
     */
    @PostMapping("/isPurchase")
    public DataResponse<Boolean> isPurchase(@RequestBody OrderMaterialStatusInnerReq req){
        return DataResponse.ok(materialpurchaseFollowService.isPurchase(req));
    }

}
