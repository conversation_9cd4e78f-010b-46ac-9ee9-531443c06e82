package tech.tiangong.sdp.design.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import tech.tiangong.sdp.design.entity.VisualTaskTryOnImage;
import tech.tiangong.sdp.design.mapper.VisualTaskTryOnImageMapper;
import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class VisualTaskTryOnImageRepository extends BaseRepository<VisualTaskTryOnImageMapper, VisualTaskTryOnImage> {

    public List<VisualTaskTryOnImage> listByTryOnTaskId(List<Long> tryOnTaskIdList) {
        return list(new LambdaQueryWrapper<VisualTaskTryOnImage>()
                .in(VisualTaskTryOnImage::getTryOnTaskId, tryOnTaskIdList));
    }

    public List<VisualTaskTryOnImage> listAvailableByTaskId(List<Long> visualTaskIdList) {
        return list(new LambdaQueryWrapper<VisualTaskTryOnImage>()
                .in(VisualTaskTryOnImage::getTaskId, visualTaskIdList)
                .eq(VisualTaskTryOnImage::getAvailable, 1));
    }
}