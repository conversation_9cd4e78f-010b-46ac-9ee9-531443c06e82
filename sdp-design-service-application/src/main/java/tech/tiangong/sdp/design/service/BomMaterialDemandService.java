package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.vo.query.bom.BomMaterialDemandQuery;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialDemandReq;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialDemandVo;

import java.util.List;

/**
 * bom物料需求表服务接口
 *
 * <AUTHOR>
 */
public interface BomMaterialDemandService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<BomMaterialDemandVo> page(BomMaterialDemandQuery query);

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    BomMaterialDemandVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(BomMaterialDemandReq req);

    /**
     * 更新数据
     *
     * @param req 数据实体
     */
    void update(BomMaterialDemandReq req);

    /**
     * 删除数据
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 好料-需求关闭mq处理
     *
     * @param req
     */
    void houliuDemandCloseCallBack(MqMessageReq req);

    /**
     * 根据bomId查询对应的需求信息
     * @param bomId bomId
     * @return 需求集合
     */
    List<BomMaterialDemandVo> listByBomId(Long bomId);

    /**
     * 根据bomId集合查询对应的需求信息
     * @param bomIdList bomId集合
     * @return 需求集合
     */
    List<BomMaterialDemandVo> listByBomIds(List<Long> bomIdList);
}
