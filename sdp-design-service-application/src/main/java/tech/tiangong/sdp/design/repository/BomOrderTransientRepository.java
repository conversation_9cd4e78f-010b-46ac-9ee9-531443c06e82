package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.BomOrderTransient;
import tech.tiangong.sdp.design.mapper.BomOrderTransientMapper;
import tech.tiangong.sdp.design.vo.query.bom.BomOrderTransientQuery;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderTransientVo;
import tech.tiangong.sdp.utils.Bool;

import java.util.Objects;

;

/**
 * bom暂存表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class BomOrderTransientRepository extends BaseRepository<BomOrderTransientMapper, BomOrderTransient> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<BomOrderTransientVo> findPage(BomOrderTransientQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public BomOrderTransient getByBomId(Long bomId) {
        return lambdaQuery()
                .eq(BomOrderTransient::getBomId, bomId)
                .eq(BomOrderTransient::getIsDeleted, Bool.NO.getCode())
                .one();
    }

    public void removeByBomId(Long bomId) {
        if (Objects.isNull(bomId)) {
            return;
        }
        baseMapper.removeByBomId(bomId);
    }
}
