package tech.tiangong.sdp.design.remote;

import cn.yibuyun.framework.net.DataResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.clothes.client.CheckPriceClient;
import tech.tiangong.sdp.clothes.vo.req.DesignCodeListReq;
import tech.tiangong.sdp.clothes.vo.req.DesignCodeSpuQuery;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.CheckPriceBaseInfoInnerVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.CheckPriceInnerVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.DesignPricingInfoVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class CheckPriceRemoteHelper {

    private final CheckPriceClient checkPriceClient;

    public List<CheckPriceBaseInfoInnerVo> findLatestCheckedBaseInfoBySkcBatch(DesignCodeListReq designCodeListReq){
        DataResponse<List<CheckPriceBaseInfoInnerVo>> response = checkPriceClient.findLatestCheckedBaseInfoBySkcBatch(designCodeListReq);
        SdpDesignException.isTrue(response.isSuccessful(),"调用核价失败接口失败.错误信息:{}",response.getMessage());
        return response.getData();
    }
    public CheckPriceInnerVo findLatestCheckedBaseInfoBySkc(String designCode){
        DataResponse<CheckPriceInnerVo> response = checkPriceClient.findLatestBySkc(designCode);
        SdpDesignException.isTrue(response.isSuccessful(),"调用核价失败接口失败.错误信息:{}",response.getMessage());
        return response.getData();
    }
    /*
    public CheckPriceDetailVo findLastCheckedVersion(String designCode){
        DataResponse<CheckPriceDetailVo> response = checkPriceClient.findLastCheckedVersion(designCode);
        SdpDesignException.isTrue(response.isSuccessful(),"调用核价失败接口失败.错误信息:{}",response.getMessage());
        return response.getData();
    }
     */
    public List<DesignPricingInfoVo> findLastPricingBySkcBatch(List<DesignCodeSpuQuery.DesignCodeSpuReq> designCodeSpuList){
        DesignCodeSpuQuery listReq = new DesignCodeSpuQuery();
        listReq.setDesignCodeSpuReqs(designCodeSpuList);
        DataResponse<List<DesignPricingInfoVo>> response = checkPriceClient.findLastPricingBySkcBatch(listReq);
        SdpDesignException.isTrue(response.isSuccessful(),"调用核价失败接口失败.错误信息:{}",response.getMessage());
        return response.getData();
    }
}
