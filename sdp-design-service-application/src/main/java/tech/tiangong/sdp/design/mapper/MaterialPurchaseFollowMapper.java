package tech.tiangong.sdp.design.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tech.tiangong.sdp.design.entity.MaterialPurchaseFollow;
import tech.tiangong.sdp.design.vo.query.purchase.MaterialPurchaseFollowQuery;
import tech.tiangong.sdp.design.vo.req.purchase.MaterialPurchaseFollowPageReq;
import tech.tiangong.sdp.design.vo.resp.material.PurchaseApplyFollowCountVO;
import tech.tiangong.sdp.design.vo.resp.purchase.MaterialPurchaseFollowPageVO;

import java.util.List;
import java.util.Set;


/**
* 面辅料采购跟进表 Mapper 接口
* <br>CreateDate August 10,2021
* <AUTHOR>
* @since 1.0
*/
@Mapper
public interface MaterialPurchaseFollowMapper extends BaseMapper<MaterialPurchaseFollow> {

    /**
    * 通过多个主键 批量删除
    * @param keys 多个主键
    * @return 返回结果列表
    */
    void deleteByKeys(List<String> keys);

    /**
    * 查询数据列表
    * @param queryDTO 查询对象
    * @return 返回结果列表
    */
    List<MaterialPurchaseFollow> findDataList(MaterialPurchaseFollowQuery queryDTO);

    /**
    * 查询数据总数
    * @param queryDTO 查询对象
    * @return 返回结果数量
    */
    Integer findDataCount(MaterialPurchaseFollowQuery queryDTO);

    /**
     * 取消物料采购
     *
     * @param materialPurchaseFollowList
     */
    void updateBatchByConditions(@Param("materialPurchaseFollowList") List<MaterialPurchaseFollow> materialPurchaseFollowList);

    /**
     * 面辅料采购跟进列表
     *
     * @param queryDTO
     * @return
     */
    List<MaterialPurchaseFollowPageVO> pageList(MaterialPurchaseFollowPageReq queryDTO);


    /**
     * 取消物料采购(批量)
     *
     * @param materialPurchaseFollow
     */
    void updateByConditions(MaterialPurchaseFollow materialPurchaseFollow);

    /**
     * 根据匹配id集合批量查询匹配的采购次数（自选物料版本）
     * @param materialSnapshotIdSet 物料快照id集合
     * @return
     */
    List<PurchaseApplyFollowCountVO> purchaseCountByMaterialSnapshotIds(@Param("materialSnapshotIdSet") Set<Long> materialSnapshotIdSet);

    @Select("SELECT purchase_request_code FROM material_purchase_follow where demand_type = 1 ORDER BY created_time DESC,material_purchase_follow_id DESC LIMIT 1")
    String selectLatestFabricPurchaseCode();

    @Select("SELECT purchase_request_code FROM material_purchase_follow where demand_type = 2 ORDER BY created_time DESC,material_purchase_follow_id DESC LIMIT 1")
    String selectLatestAccessoriesPurchaseCode();

}