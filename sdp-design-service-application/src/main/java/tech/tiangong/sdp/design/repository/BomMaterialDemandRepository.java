package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.entity.BaseEntity;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.collection.Collections;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.BomMaterialDemand;
import tech.tiangong.sdp.design.mapper.BomMaterialDemandMapper;
import tech.tiangong.sdp.design.vo.query.bom.BomMaterialDemandQuery;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialDemandVo;

import java.util.*;
import java.util.stream.Collectors;

/**
 * bom物料需求表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class BomMaterialDemandRepository extends BaseRepository<BomMaterialDemandMapper, BomMaterialDemand> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<BomMaterialDemandVo> findPage(BomMaterialDemandQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    /**
     * 根据bomId查询物料需求信息
     */
    public List<BomMaterialDemand> listByBomId(Long bomId) {
        if (Objects.isNull(bomId)) {
            return List.of();
        }
        return lambdaQuery().eq(BomMaterialDemand::getBomId, bomId).list();
    }

    /**
     * 根据bomId集合查询物料需求信息
     */
    public List<BomMaterialDemand> listByBomIdList(List<Long> bomIdList) {
        if (CollUtil.isEmpty(bomIdList)) {
            return List.of();
        }
        return lambdaQuery().in(BomMaterialDemand::getBomId, bomIdList).list();
    }

    /**
     * 根据bomId与需求状态集合查询物料需求信息
     */
    public List<BomMaterialDemand> listByBomIdAndState(Long bomId, List<Integer> stateList) {
        if (Objects.isNull(bomId)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(BomMaterialDemand::getBomId, bomId)
                .in(CollUtil.isNotEmpty(stateList), BomMaterialDemand::getDemandState, stateList)
                .list();
    }

    /**
     * 根据物料需求id获取物料需求信息
     *
     * @param bomMaterialDemandIds 物料需求id集合
     * @return
     */
    public List<BomMaterialDemand> listByIds(List<Long> bomMaterialDemandIds) {
        if (Collections.isEmpty(bomMaterialDemandIds)) {
            return new ArrayList<>();
        }
        return super.listByIds(bomMaterialDemandIds);
    }

    /**
     * 根据履约需求id查询最新bom的物料需求信息
     */
    public BomMaterialDemand latestBySupplyDemandId(Long supplyDemandId) {
        return baseMapper.latestBySupplyDemandId(supplyDemandId);
    }

    /**
     * 根据履约需求id查询最新物料需求信息
     */
    public List<BomMaterialDemand> latestBySupplyDemandIds(List<Long> supplyDemandIdList) {
        if (CollUtil.isEmpty(supplyDemandIdList)) {
            return List.of();
        }
        Map<Long, List<BomMaterialDemand>> bomMaterialDemandsMap = lambdaQuery()
                .in(BomMaterialDemand::getSupplyChainDemandId, supplyDemandIdList)
                .orderByDesc(BomMaterialDemand::getCreatedTime)
                .list().stream()
				.collect(Collectors.groupingBy(BomMaterialDemand::getSupplyChainDemandId));;
        List<BomMaterialDemand> bomMaterialDemandList = new ArrayList<>();
        bomMaterialDemandsMap.forEach((key, value) -> bomMaterialDemandList.add(value.stream().max(Comparator.comparing(BaseEntity::getCreatedTime)).orElseGet(BomMaterialDemand::new)));

        return bomMaterialDemandList;
    }
}
