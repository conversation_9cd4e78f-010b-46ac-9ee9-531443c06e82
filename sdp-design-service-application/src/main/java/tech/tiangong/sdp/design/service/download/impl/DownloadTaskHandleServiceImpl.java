package tech.tiangong.sdp.design.service.download.impl;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import com.alibaba.fastjson.JSON;
import com.zjkj.booster.common.constant.StrConstants;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignRedisConstants;
import tech.tiangong.sdp.design.constant.DownloadTaskConstants;
import tech.tiangong.sdp.design.entity.DesignAsyncTask;
import tech.tiangong.sdp.design.enums.DownloadTaskStatusEnum;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.service.download.DownloadTaskHandleService;
import tech.tiangong.sdp.design.service.download.DownloadTaskService;
import tech.tiangong.sdp.design.service.download.DownloadTaskStrategy;
import tech.tiangong.sdp.design.service.download.DownloadTaskStrategyFactory;
import tech.tiangong.sdp.design.vo.dto.download.FileUploadDTO;
import tech.tiangong.sdp.utils.UserHolderUtil;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 下载任务管理处理
 * 路由 调度
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
@Slf4j
@Service
@AllArgsConstructor
public class DownloadTaskHandleServiceImpl implements DownloadTaskHandleService {
    private final DownloadTaskService taskService;
    private final RedissonClient redissonClient;
    private final DownloadTaskStrategyFactory downloadTaskStrategyFactory;
    private final AtomicInteger currentTaskCount = new AtomicInteger(0);
    private final String instanceIdSdpDesign;

    @Override
    public void processTasks() {
        List<DesignAsyncTask> tasks = taskService.getDownloadTasks();
        if (CollectionUtils.isEmpty(tasks)) {
            log.info("暂无下载任务");
            return;
        }

        for (DesignAsyncTask task : tasks) {
            processTaskInner(task);
        }
    }

    @Override
    public void processTask(Long taskId) {
        DesignAsyncTask task = taskService.getById(taskId);
        if (Objects.isNull(task)) {
            throw new SdpDesignException("任务主键没有查询到对应的记录");
        }
        processTaskInner(task);
    }

    @Override
    public void forceProcessTask(Long taskId) {
        DesignAsyncTask task = taskService.getById(taskId);
        if (Objects.isNull(task)) {
            throw new SdpDesignException("任务主键没有查询到对应的记录");
        }
        this.doDownloadTask(task, true);
    }

    private void processTaskInner(DesignAsyncTask task) {
        if (currentTaskCount.incrementAndGet() > DownloadTaskConstants.MAX_CONCURRENT_TASKS) {
            currentTaskCount.decrementAndGet();
            log.info("当前任务数已达到上限 {}, 跳过任务 {}", DownloadTaskConstants.MAX_CONCURRENT_TASKS, task.getAsyncTaskId());
            return;
        }

        this.doDownloadTask(task, false);
    }


    private void doDownloadTask(DesignAsyncTask task, Boolean forceProcess) {
        String lockKey = DesignRedisConstants.SYNC_TASK_DOWNLOAD_LOCK + getInstanceIdStr() + StrConstants.COLON + task.getAsyncTaskId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean isLocked = false;
        UserContentHolder.set(new UserContent()
                .setCurrentUserId(task.getCreatorId()!=null ? task.getCreatorId() : 0L)
                .setCurrentUserCode(task.getCreatorId()!=null ? task.getCreatorId().toString() : "0")
                .setCurrentUserName(StringUtils.isNotBlank(task.getCreatorName()) ? task.getCreatorName() : "系统")
                .setTenantId(2L)
                .setSystemCode("SDP"));
        try {
            isLocked = lock.tryLock();
            if (isLocked) {
                //获取最新任务状态
                DesignAsyncTask freshTask = taskService.getById(task.getAsyncTaskId());
                SdpDesignException.notNull(freshTask, "任务主键没有查询到对应的记录");

                // 如果非强制处理模式，检查任务状态
                if (!forceProcess && ObjectUtils.notEqual(DownloadTaskStatusEnum.PENDING.getCode(), freshTask.getState())) {
                    log.info("任务已经不是待处理状态了，taskId: {}, status: {}", task.getAsyncTaskId(), task.getState());
                    return;
                }

                //更新任务为处理中状态
                DesignAsyncTask processingTask = new DesignAsyncTask();
                processingTask.setAsyncTaskId(task.getAsyncTaskId());
                processingTask.setState(DownloadTaskStatusEnum.PROCESSING.getCode());
                taskService.updateById(processingTask);

                //处理业务逻辑
                List<FileUploadDTO> resultFiles = this.processDownload(freshTask);

                //更新任务为完成状态
                DesignAsyncTask completedTask = new DesignAsyncTask();
                completedTask.setAsyncTaskId(task.getAsyncTaskId());
                completedTask.setResult(JSON.toJSONString(resultFiles));
                completedTask.setState(DownloadTaskStatusEnum.COMPLETED.getCode());
                taskService.updateById(completedTask);
            } else {
                if (forceProcess) {
                    throw new SdpDesignException("无法获取任务的锁，跳过处理");
                }
                log.info("无法获取任务 {} 的锁，跳过处理", task.getAsyncTaskId());
            }
        } catch (Exception e) {
            this.handleTaskException(task, e, forceProcess);
            if (forceProcess) {
                throw new SdpDesignException("处理任务失败", e);
            }
        } finally {
            UserContentHolder.clean();
            currentTaskCount.decrementAndGet();
            if (isLocked) {
                try {
                    lock.unlock();
                } catch (IllegalMonitorStateException e) {
                    log.warn("释放锁失败，可能锁已经过期", e);
                }
            }
        }
    }

    /**
     * 统一处理任务异常
     *
     * @param task 任务对象
     * @param e 捕获的异常
     * @param forceProcess 是否是强制处理模式
     */
    private void handleTaskException(DesignAsyncTask task, Exception e, Boolean forceProcess) {
        // 确定是否是锁获取失败异常
        boolean isLockFailure = e instanceof SdpDesignException && Objects.equals(e.getMessage(), "无法获取任务的锁，跳过处理");

        // 记录日志，但锁获取失败时只记录 info 级别
        if (isLockFailure) {
            if (!forceProcess) { // 在forceProcess模式下不需要再记录日志
                log.info("无法获取任务 {} 的锁，跳过处理", task.getAsyncTaskId());
            }
        } else {
            log.error("处理任务 {} 失败", task.getAsyncTaskId(), e);
            DesignAsyncTask failedTask = new DesignAsyncTask();
            failedTask.setAsyncTaskId(task.getAsyncTaskId());
            failedTask.setState(DownloadTaskStatusEnum.FAILED.getCode());
            failedTask.setErrorReason(ExceptionUtils.getStackTrace(e));
            taskService.updateById(failedTask);
        }
    }

    private List<FileUploadDTO> processDownload(DesignAsyncTask task) {
        DesignAsyncTaskTypeEnum taskTypeEnum = DesignAsyncTaskTypeEnum.findByCode(task.getBusinessType());
        if (Objects.isNull(taskTypeEnum)) {
            throw new SdpDesignException(String.format("无效的业务类型: %d", task.getBusinessType()));
        }
        DownloadTaskStrategy strategy = downloadTaskStrategyFactory.getStrategy(taskTypeEnum);
        return strategy.processDownloadTask(task);
    }

    private String getInstanceIdStr() {
        return this.instanceIdSdpDesign;
    }
}
