package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.req.bom.BomCraftSubmitHandleReq;

/**
 * bom单_工艺需求-提交处理 接口
 * <AUTHOR>
 * @date 2022/11/16 20:31
 */
public interface BomCraftSubmitHandleService {


    /**
     * 首次提交
     * <p>
     * 适用场景: bom 待提交_无暂存    (信息直接维护在原表中)
     * <p>
     * 工艺需求新增(); //新增的工艺需求, 维护到原表
     * <p>
     * 要处理工艺需求创建于同步: 推送履约,同步版房,补推裁前工艺
     * <p>
     * 注: 会有删除的工艺;
     * <p>
     * 历史数据可能有删除的工艺(2.0时工艺维护中拆板中, 拆板提交物料确认后,, 待提交的bom就有物料与工艺了)
     *
     * @param req 入参
     */
    void firstSubmitNoTransient(BomCraftSubmitHandleReq req);

    /**
     * 首次提交
     *
     * 适用场景: bom待提交_有暂存
     *
     * @param req 入参
     */
    void firstSubmitWithTransient(BomCraftSubmitHandleReq req);


    /**
     * 再次提交
     * <p>
     * 适用场景: bom 已提交/已核算/找料中_无暂存    (信息直接维护在原表中)
     * <p>
     * 工艺需求新增(); //新增工艺维护到新版本bom下
     * <p>
     * 工艺需求删除(); //复制一份旧bom工艺, close后添加到新版本bom下(打版查询用)
     * <p>
     * 要处理工艺需求创建于同步: 推送履约,同步版房,补推裁前工艺
     *
     * @param req 入参
     */
    void reSubmitNoTransient(BomCraftSubmitHandleReq req);

    /**
     * 再次提交
     *
     * 适用场景: bom 已提交/已核算/找料中_有暂存
     *
     *
     * @param req 入参
     */
    void reSubmitWithTransient(BomCraftSubmitHandleReq req);


}
