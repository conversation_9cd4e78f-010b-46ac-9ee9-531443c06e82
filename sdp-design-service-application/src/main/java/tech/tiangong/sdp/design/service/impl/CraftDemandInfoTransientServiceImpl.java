package tech.tiangong.sdp.design.service.impl;

import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.entity.CraftDemandInfoTransient;
import tech.tiangong.sdp.design.repository.CraftDemandInfoTransientRepository;
import tech.tiangong.sdp.design.service.CraftDemandInfoTransientService;
import tech.tiangong.sdp.design.vo.query.bom.CraftDemandInfoTransientQuery;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandInfoTransientReq;
import tech.tiangong.sdp.design.vo.resp.bom.CraftDemandInfoTransientVo;

/**
 * 工艺需求_暂存表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CraftDemandInfoTransientServiceImpl implements CraftDemandInfoTransientService {
    private final CraftDemandInfoTransientRepository craftDemandInfoTransientRepository;

    @Override
    public PageRespVo<CraftDemandInfoTransientVo> page(CraftDemandInfoTransientQuery query) {
        IPage<CraftDemandInfoTransientVo> page = craftDemandInfoTransientRepository.findPage(query);
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    public CraftDemandInfoTransientVo getById(Long id) {
        CraftDemandInfoTransient entity = craftDemandInfoTransientRepository.getById(id);
        CraftDemandInfoTransientVo vo = new CraftDemandInfoTransientVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CraftDemandInfoTransientReq req) {
        CraftDemandInfoTransient entity = new CraftDemandInfoTransient();
        BeanUtils.copyProperties(req, entity);
        craftDemandInfoTransientRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CraftDemandInfoTransientReq req) {
        CraftDemandInfoTransient entity = new CraftDemandInfoTransient();
        BeanUtils.copyProperties(req, entity);
        craftDemandInfoTransientRepository.updateById(entity);
    }

    @Override
    public void remove(Long id) {
        craftDemandInfoTransientRepository.removeById(id);
    }

}
