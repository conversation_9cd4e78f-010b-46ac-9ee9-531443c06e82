package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.SpotSpuDetail;
import tech.tiangong.sdp.design.entity.SpotSpuImportTask;
import tech.tiangong.sdp.design.entity.SpotSpuSupplier;
import tech.tiangong.sdp.design.enums.SpotSpuImportTaskStatusEnum;
import tech.tiangong.sdp.design.mapper.SpotSpuDetailMapper;
import tech.tiangong.sdp.design.mapper.SpotSpuImportTaskMapper;
import tech.tiangong.sdp.design.vo.query.spot.SpotSpuImportTaskQuery;
import tech.tiangong.sdp.design.vo.query.spot.SpotSpuQuery;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuImportTaskVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuVo;
import tech.tiangong.sdp.utils.Bool;

import java.util.List;

/**
 * @Author: caicijie
 * @description:
 * @Date: 2025/6/26 17:37
 */
@Repository
public class SpotSpuImportTaskRepository  extends BaseRepository<SpotSpuImportTaskMapper, SpotSpuImportTask> {
    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<SpotSpuImportTaskVo> findPage(SpotSpuImportTaskQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public List<SpotSpuImportTask> findByStatus(List<Integer> status) {
        return list(new LambdaQueryWrapper<SpotSpuImportTask>()
                .in(SpotSpuImportTask::getTaskStatus, status)
                .eq(SpotSpuImportTask::getIsDeleted, Bool.NO.getCode())
                .orderByAsc(SpotSpuImportTask::getId)
        );
    }
}
