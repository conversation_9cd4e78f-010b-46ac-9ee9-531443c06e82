package tech.tiangong.sdp.design.service.download.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.exception.BusinessException;
import com.alibaba.fastjson.JSONObject;
import com.zjkj.booster.common.constant.DatePatternConstants;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.core.config.DownloadProperties;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.helper.ImageDownloadHelper;
import tech.tiangong.sdp.design.helper.UploaderOssHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.download.DownloadTaskStrategy;
import tech.tiangong.sdp.design.vo.dto.download.FileUploadDTO;
import tech.tiangong.sdp.utils.FileCompressUtils;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Component
public class DownloadVisualTaskOnShelfAiBoxImageImpl implements DownloadTaskStrategy {

    private final VisualTaskRepository visualTaskRepository;
    private final ImageDownloadHelper imageDownloadHelper;
    private final DateTimeFormatter PURE_DATETIME_PATTERN = DateTimeFormatter.ofPattern(DatePatternConstants.PURE_DATETIME_PATTERN);
    private final Long DOWNLOAD_TIMEOUT_SECONDS = 10 * 60L; // 10分钟超时
    private final AsyncTaskExecutor asyncTaskExecutor;
    private final DownloadProperties downloadProperties;
    private final UploaderOssHelper uploaderOssHelper;
    private final VisualTaskOnShelfRepository visualTaskOnShelfRepository;
    private final VisualOnShelfAiBoxImageRepository visualOnShelfAiBoxImageRepository;

    @Override
    public DesignAsyncTaskTypeEnum getTaskType() {
        return DesignAsyncTaskTypeEnum.DOWNLOAD_VISUAL_TASK_ON_SHELF_AI_BOX_IMAGE;
    }

    @Override
    public List<FileUploadDTO> processDownloadTask(DesignAsyncTask task) {
        log.info("==== DownloadVisualTaskOnShelfAiBoxImageImpl processDownloadTask {}:{}",getTaskType().getDesc(), JSONObject.toJSONString(task));
        File tempDir = null;
        try {
            tempDir = createTempDirectory(task.getAsyncTaskId());

            List<Long> visualTaskIds = JSONObject.parseArray(task.getParameters(), Long.class);
            List<VisualTask> visualTasks = visualTaskRepository.listByIds(visualTaskIds);
            List<VisualTaskOnShelf> visualTaskOnShelfList = visualTaskOnShelfRepository.getLatestByTaskIdList(visualTaskIds);
            List<VisualOnShelfAiBoxImage> visualOnShelfAiBoxImageList = visualOnShelfAiBoxImageRepository.listByTaskIdList(visualTaskIds);
            // 按照taskCode组织图片
            Map<Long, List<VisualOnShelfAiBoxImage>> taskIdImageMap = visualOnShelfAiBoxImageList.stream().collect(Collectors.groupingBy(VisualOnShelfAiBoxImage::getTaskId));
            Map<Long, VisualTask> taskIdMap = visualTasks.stream().collect(Collectors.toMap(VisualTask::getTaskId, Function.identity(), (k1,k2)->k1));
            Map<String, List<ImageFile>> taskCodeImageMap = new HashMap<>();
            for (VisualTaskOnShelf visualTaskOnShelf : visualTaskOnShelfList) {
                List<VisualOnShelfAiBoxImage> visualOnShelfAiBoxImages = taskIdImageMap.get(visualTaskOnShelf.getTaskId());
                if (CollectionUtil.isEmpty(visualOnShelfAiBoxImages)) {
                    continue;
                }
                List<ImageFile> imageFiles = visualOnShelfAiBoxImages.stream()
                        .map(e -> new ImageFile(e.getGeneratedImgName(), e.getGeneratedImg()))
                        .toList();
                VisualTask visualTask = taskIdMap.get(visualTaskOnShelf.getTaskId());
                taskCodeImageMap.put(visualTask.getStyleCode(), imageFiles);
            }

            // 2. 异步下载图片并按TaskId组织目录
            File finalTempDir = tempDir;
            List<CompletableFuture<File>> futures = new ArrayList<>();
            taskCodeImageMap.forEach((taskCode, imageList)->{
                log.info("开始处理图片: taskCode={}, imageListSize={}",taskCode,imageList.size());
                // 对图片进行重命处理
                if (imageList.size() > 1) {
                    Map<String, Integer> imgIndexMap = new HashMap<>();
                    imageList.forEach(image -> {
                        String imgName = image.getOrgImgName();
                        if (imgIndexMap.containsKey(imgName)) {
                            String newImgName = FilenameUtils.getBaseName(imgName) + "-" + imgIndexMap.get(imgName) + "." + FilenameUtils.getExtension(imgName);
                            imgIndexMap.put(imgName, imgIndexMap.get(imgName) + 1);
                            image.setOrgImgName(newImgName);
                        } else {
                            imgIndexMap.put(imgName, 1);
                        }
                    });
                }
                futures.add(CompletableFuture.supplyAsync(()-> downloadImagesForTaskCode(taskCode,imageList, finalTempDir),asyncTaskExecutor)
                        .orTimeout(DOWNLOAD_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                        .exceptionally(throwable->handleDownloadException(throwable, imageList)));
            });

            // 3. 等待所有下载任务完成
            List<File> downloadedFiles = waitForDownloadCompletion(futures);

            if (CollectionUtil.isEmpty(downloadedFiles)) {
                throw new BusinessException("所有文件下载均失败1");
            }
            downloadedFiles = downloadedFiles.stream().filter(Objects::nonNull).toList();
            if (CollectionUtil.isEmpty(downloadedFiles)) {
                throw new BusinessException("所有文件下载均失败2");
            }
            // 4. 压缩文件
            return compressFiles(task.getAsyncTaskId(), tempDir, downloadedFiles);
        } catch (Exception e) {
            log.error("系统处理异常: taskId={}, req={}, message={}",task.getAsyncTaskId(),task.getParameters(), e.getMessage());
            throw new BusinessException("下载任务处理失败", e);
        } finally {
            cleanupTempFiles(tempDir);
        }
    }

    /**
     * 压缩文件
     */
    private List<FileUploadDTO> compressFiles(Long taskId, File tempDir, List<File> files) {
        if (files.isEmpty()) {
            return Collections.emptyList();
        }

        Integer maxZipSizeMb = downloadProperties.getMaxZipSizeMb();
        String baseFileName = "images_"+taskId;
        List<File> zipFiles = FileCompressUtils.zipFilesWithSizeLimit(
                tempDir,
                baseFileName,
                maxZipSizeMb,
                files);

        if (zipFiles.isEmpty()) {
            throw new BusinessException("压缩文件失败，压缩后返回空文件");
        }
        return zipFiles.stream().map(uploaderOssHelper::createFileUploadDTO).collect(Collectors.toList());
    }

    /**
     * 下载单个SPU的所有图片
     */
    private File downloadImagesForTaskCode(String taskCode, List<ImageFile> imagePackage, File parentDir){
        if (StringUtils.isBlank(taskCode)) {
            log.warn("downloadOnShelfAiBoxImageForTaskCode 异常， taskCode为空");
            return null;
        }

        File taskDir = new File(parentDir, taskCode);
        try {
            FileUtils.forceMkdir(taskDir);
            if (CollectionUtil.isEmpty(imagePackage)) {
                log.info("taskCode :{} 没有try on图", taskCode);
                return taskDir;
            }
            for (ImageFile imageFile : imagePackage) {
                imageDownloadHelper.downloadSingleImageToDir(imageFile, taskDir);
            }

        } catch (Exception e) {
            log.error("下载AI修图图片失败: {}", taskCode, e);
        }

        return taskDir;

    }

    /**
     * 处理下载异常
     */
    private File handleDownloadException(Throwable throwable, List<ImageFile> imageFiles){
        log.error("下载图片失败: visualImagePackage={}, error={}",JSONObject.toJSONString(imageFiles),throwable.getMessage());
        return null;
    }

    /**
     * 等待所有下载任务完成
     */
    private List<File> waitForDownloadCompletion(List<CompletableFuture<File>> futures) {
        if(CollectionUtil.isEmpty(futures)){
            return null;
        }
        return futures.stream().map(future ->{
            try {
                return future.join();
            } catch (CompletionException e) {
                // 捕获异步任务中的异常
                Throwable cause = e.getCause(); // 获取原始异常
                log.error("下载任务执行失败: 原因={}",(cause!=null && StringUtils.isNotBlank(cause.getMessage()) ? cause.getMessage() : "未知"));
            }
            return null;
        }).collect(Collectors.toList());
    }

    private File createTempDirectory(Long taskId) throws IOException {
        String tempDirStr = FileUtils.getTempDirectoryPath() + File.separator +
                "download_" + taskId + File.separator +
                PURE_DATETIME_PATTERN.format(LocalDateTime.now());

        File tempDir = new File(tempDirStr);
        FileUtils.forceMkdir(tempDir);
        return tempDir;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(File tempDir) {
        try {
            if(tempDir!=null){
                FileUtils.deleteDirectory(tempDir);
            }
        } catch (IOException e) {
            log.error("清理临时文件失败:"+tempDir.getAbsolutePath(),e);
        }
    }


}
