package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.bean.Beans;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.exception.BusinessException;
import cn.yibuyun.framework.net.DataResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import tech.tiangong.id.IdPool;
import tech.tiangong.inspiration.common.req.product.StyleLibraryDeleteReq;
import tech.tiangong.sdp.clothes.client.SampleClothesClient;
import tech.tiangong.sdp.clothes.vo.dto.CheckPriceExecuteProcessMqDto;
import tech.tiangong.sdp.clothes.vo.resp.SampleClothesDetailVo;
import tech.tiangong.sdp.clothes.vo.resp.SampleClothesInfoVo;
import tech.tiangong.sdp.clothes.vo.resp.SampleClothesVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.CheckPriceInnerVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.EstimateCheckPriceVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.business.BatchCreatingPrototypeTransaction;
import tech.tiangong.sdp.design.business.Transaction;
import tech.tiangong.sdp.design.converter.PrototypeConverter;
import tech.tiangong.sdp.design.converter.StyleLibraryConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.enums.spot.SpotPriceStateEnum;
import tech.tiangong.sdp.design.helper.StyleLibraryHelper;
import tech.tiangong.sdp.design.mapper.PrototypeMapper;
import tech.tiangong.sdp.design.remote.CheckPriceRemoteHelper;
import tech.tiangong.sdp.design.remote.DesignerRemoteHelper;
import tech.tiangong.sdp.design.remote.SampleClothesRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.PrototypeService;
import tech.tiangong.sdp.design.service.SpotSpuService;
import tech.tiangong.sdp.design.vo.base.prototype.OpsObject;
import tech.tiangong.sdp.design.vo.base.prototype.SalesChannelUpdate;
import tech.tiangong.sdp.design.vo.dto.style.DesignStyleUpdateDto;
import tech.tiangong.sdp.design.vo.req.manage.PrototypeCancelReq;
import tech.tiangong.sdp.design.vo.req.prototype.BatchPrintReq;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeCreateReq;
import tech.tiangong.sdp.design.vo.req.prototype.inner.*;
import tech.tiangong.sdp.design.vo.req.spot.UpdatePredictCheckPriceStatusReq;
import tech.tiangong.sdp.design.vo.resp.DesignerVo;
import tech.tiangong.sdp.design.vo.resp.SkcTagVO;
import tech.tiangong.sdp.design.vo.resp.prototype.*;
import tech.tiangong.sdp.design.vo.resp.zj.design.PrototypeOrderMaterialOpenResp;
import tech.tiangong.sdp.material.pojo.dto.DesignerDTO;
import tech.tiangong.sdp.material.pojo.web.request.designer.DesignerRemoteReq;
import tech.tiangong.sdp.material.sdk.service.remote.DesignerRemoteService;
import tech.tiangong.sdp.utils.AsyncTask;
import tech.tiangong.sdp.utils.CommonUtil;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 版单-主表服务
 *
 * <AUTHOR>
 * @since 2021-08-09 14:43:18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrototypeServiceImpl implements PrototypeService {
    private final PrototypeRepository prototypeRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final PrototypeHistoryRepository prototypeHistoryRepository;
    private final PurchasePrototypeInfoRepository purchasePrototypeInfoRepository;
    private final DesignerRemoteService designerRemoteService;
    private final SampleClothesClient sampleClothesClient;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final CheckPriceRemoteHelper checkPriceRemoteHelper;
    private final DesignStyleRepository designStyleRepository;
    private final DesignerRemoteHelper designerRemoteHelper;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;
    private final SpotSpuService spotSpuService;
    private final StyleLibraryHelper styleLibraryHelper;
    private final PrototypeMapper prototypeMapper;
    @Autowired
    private BomOrderRepository bomOrderRepository;


    @Override
    public PrototypeVo getById(Long id) {
        Prototype entity = prototypeRepository.getById(id);
        SdpDesignException.notNull(entity, "找不到当前设计款号");
        PrototypeVo vo = new PrototypeVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreating(List<PrototypeCreateReq> prototypeCreateReqList) {
        Transaction transaction = new BatchCreatingPrototypeTransaction(prototypeCreateReqList);
        transaction.execute();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NormalSkcCreateResp normalSkcCreate(DesignStyleVersion designStyleVersion, DesignStyleSourceTypeEnum sourceType, String designPicture) {
        log.info("=== 创建SPU时创建正常打版SKC spu:{} ===", JSON.toJSONString(designStyleVersion));
        SdpDesignException.notNull(designStyleVersion, "入参为空! ");
        String styleCode = designStyleVersion.getStyleCode();

        //skc
        Prototype prototype = this.buildFirstPrototype(sourceType, designStyleVersion);

        //创建skc与详情
        this.createFirstVersionSkc(designStyleVersion.getCategoryName(),prototype, SkcTypeEnum.NORMAL, designPicture);

        log.info("=== SPU创建正常打版SKC成功:designCode={}; prototypeId={}; styleCode:{}; sourceType:{} ====",
                prototype.getDesignCode(), prototype.getPrototypeId(), designStyleVersion.getStyleCode(), sourceType);

        return new NormalSkcCreateResp()
                .setDesignStyleId(designStyleVersion.getDesignStyleId())
                .setStyleCode(styleCode)
                .setPrototypeId(prototype.getPrototypeId())
                .setDesignCode(prototype.getDesignCode());
    }

    private void setSkcSourceType(DesignStyleSourceTypeEnum sourceType, Prototype prototype) {
        switch (sourceType) {
            case SELF_SPU_SOURCE -> prototype.setSkcSourceType(SkcSourceTypeEnum.PLM_SKC_SOURCE.getCode());
            case DESIGN_DEMAND -> prototype.setSkcSourceType(SkcSourceTypeEnum.DESIGN_DEMAND.getCode());
            case DIGITAL_PRINTING -> prototype.setSkcSourceType(SkcSourceTypeEnum.DIGITAL_PRINTING.getCode());
            case SPU_IMPORT -> prototype.setSkcSourceType(SkcSourceTypeEnum.SPU_IMPORT.getCode());
            case SPU_IMPORT_EXCEL -> prototype.setSkcSourceType(SkcSourceTypeEnum.SPU_IMPORT_EXCEL.getCode());
            default -> throw new BusinessException("未知款式来源");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<NormalSkcCreateResp> normalSkcBatchCreate(List<DesignStyleVersion> designStyleVersionList, DesignStyleSourceTypeEnum sourceType, Map<String, String> stylePictureMap) {
        SdpDesignException.notEmpty(designStyleVersionList, "spu为空! ");

        List<Prototype> prototypeList = new ArrayList<>(designStyleVersionList.size());
        List<PrototypeDetail> prototypeDetailList = new ArrayList<>(designStyleVersionList.size());
        List<PrototypeHistory> prototypeHistoryList = new ArrayList<>(designStyleVersionList.size());
        List<NormalSkcCreateResp> createRespList = designStyleVersionList.stream().map(designStyleVersion -> {
            //skc
            Prototype prototype = this.buildFirstPrototype(sourceType, designStyleVersion);
            //设置skc信息
            this.setPrototypeValue(prototype, SkcTypeEnum.NORMAL);
            //skc详情
            PrototypeDetail prototypeDetail = new PrototypeDetail();
            BeanUtils.copyProperties(prototype, prototypeDetail);
            prototypeDetail.setPrototypeId(prototype.getPrototypeId());
            prototypeDetail.setPrototypeDetailId(IdPool.getId());
            prototypeDetail.setCheckPriceState(Bool.NO.getCode());
            prototypeDetail.setPredictCheckPriceStatus(Bool.NO.getCode());
            String stylePicture = stylePictureMap.get(designStyleVersion.getStyleCode());
            if (StrUtil.isNotBlank(stylePicture)) {
                prototypeDetail.setDesignPicture(stylePicture);
            }

            //存到历史表
            PrototypeHistory prototypeHistory = new PrototypeHistory();
            BeanUtils.copyProperties(prototype, prototypeHistory);

            prototypeList.add(prototype);
            prototypeDetailList.add(prototypeDetail);
            prototypeHistoryList.add(prototypeHistory);

            //请求向量新增
            this.addStyleLibrary(designStyleVersion.getCategoryName(), prototype, prototypeDetail);

            log.info("=== SPU创建正常打版SKC成功:designCode={}; prototypeId={}; styleCode:{}; sourceType:{} ====",
                    prototype.getDesignCode(), prototype.getPrototypeId(), designStyleVersion.getStyleCode(), sourceType);

            return new NormalSkcCreateResp()
                    .setDesignStyleId(designStyleVersion.getDesignStyleId())
                    .setStyleCode(designStyleVersion.getStyleCode())
                    .setPrototypeId(prototype.getPrototypeId())
                    .setDesignCode(prototype.getDesignCode());
        }).toList();

        Assert.isTrue(prototypeRepository.saveBatch(prototypeList), "保存新增设计款失败");
        Assert.isTrue(prototypeDetailRepository.saveBatch(prototypeDetailList), "保存新增设计款详细信息失败");
        Assert.isTrue(prototypeHistoryRepository.saveBatch(prototypeHistoryList), "保存新增设计款历史失败");

        return createRespList;
    }

    private Prototype buildFirstPrototype(DesignStyleSourceTypeEnum sourceType, DesignStyleVersion designStyleVersion) {
        //SKC生成逻辑：SPU+2色号流水; 正常打版就是spu+01
        String designCode = designStyleVersion.getStyleCode() + "01";

        Prototype prototype = new Prototype();
        BeanUtils.copyProperties(designStyleVersion, prototype);
        prototype.setPrototypeId(IdPool.getId());
        prototype.setDesignCode(designCode);
        prototype.setSpuCreatedTime(LocalDateTime.now());
        this.setSkcSourceType(sourceType, prototype);

        return prototype;
    }

    private void createFirstVersionSkc(String categoryName, Prototype prototype, SkcTypeEnum skcTypeEnum, String designPicture) {
        //设置skc信息
        this.setPrototypeValue(prototype, skcTypeEnum);

        //skc详情
        PrototypeDetail prototypeDetail = new PrototypeDetail();
        BeanUtils.copyProperties(prototype, prototypeDetail);
        prototypeDetail.setPrototypeId(prototype.getPrototypeId());
        prototypeDetail.setPrototypeDetailId(IdPool.getId());
        prototypeDetail.setCheckPriceState(Bool.NO.getCode());
        prototypeDetail.setPredictCheckPriceStatus(Bool.NO.getCode());
        if (StrUtil.isNotBlank(designPicture)) {
            prototypeDetail.setDesignPicture(designPicture);
        }

        //存到历史表
        PrototypeHistory prototypeHistory = new PrototypeHistory();
        BeanUtils.copyProperties(prototype, prototypeHistory);

        Assert.isTrue(prototypeRepository.save(prototype), "保存新增设计款失败");
        Assert.isTrue(prototypeDetailRepository.save(prototypeDetail), "保存新增设计款详细信息失败");
        Assert.isTrue(prototypeHistoryRepository.save(prototypeHistory), "保存新增设计款历史失败");

        this.addStyleLibrary(categoryName, prototype, prototypeDetail);
    }

    private void setPrototypeValue(Prototype prototype, SkcTypeEnum skcTypeEnum) {
        //第一个版本是1
        LocalDateTime now = LocalDateTime.now();
        prototype.setVersionNum(1);
        prototype.setLatestVersionNum(1);
        prototype.setLatestPrototypeId(prototype.getPrototypeId());
        prototype.setPrototypeStatus(PrototypeStatusEnum.WAIT_DECOMPOSE.getCode());
        prototype.setIsDoneVersion(Boolean.FALSE);
        prototype.setIsMakeMore(Boolean.FALSE);
        prototype.setIsUrgent(Boolean.FALSE);
        prototype.setMakeMoreLatestTime(now);
        prototype.setIsCanceled(Boolean.FALSE);
        prototype.setCancelTime(null);
        prototype.setSkcCreatedTime(now);
        prototype.setSubmitTime(null);
        prototype.setFirstVersionDoneTime(null);
        prototype.setIsOnSale(null);
        //设置类型(正常/复色)
        prototype.setSkcType(skcTypeEnum.getCode());
        //如果是复色打版，需清空颜色
        if (SkcTypeEnum.COMPOUND_COLORS.getCode().equals(prototype.getSkcType())) {
            prototype.setMakeSameDesignCode(null);
            prototype.setColor(null);
        }
    }

    private void addStyleLibrary(String categoryName, Prototype prototype, PrototypeDetail prototypeDetail) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                //请求向量新增
                styleLibraryHelper.prototypeStyleLibrarySaveOrUpdate(prototype, categoryName, prototypeDetail,1);
            }
        });

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(AsyncTask.wrapper(() -> {
                    //请求向量新增
                    styleLibraryHelper.prototypeStyleLibrarySaveOrUpdate(prototype, categoryName, prototypeDetail,1);
                }));
            }
        });
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long colorsMakingCreate(Long normalPrototypeId) {
        Prototype prototype = prototypeRepository.getById(normalPrototypeId);
        SdpDesignException.isFalse(prototype.getIsCanceled(), "当前设计款号已取消! ");
        SdpDesignException.notNull(prototype, "版单信息不存在! ");
        DesignStyle designStyle = designStyleRepository.getByStyleCode(prototype.getStyleCode());
        SdpDesignException.notNull(designStyle, "SPU信息不存在! ");
        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId());
        SdpDesignException.notNull(prototypeDetail, "版单详情不存在! ");

        UserContent currentUser = UserContentHolder.get();
        SdpDesignException.notNull(currentUser, "用户信息为空,请登录! ");

        DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
        designerRemoteReq.setDesignerId(currentUser.getCurrentUserId() + "");
        DesignerDTO designerDTO = designerRemoteHelper.getByDesignerId(designerRemoteReq);
        SdpDesignException.notNull(designerDTO, "设计师才能发起复色! 请先加入设计组");

        long colorPrototypeId = IdPool.getId();

        //获取当前最新的skc
        // Prototype lastPrototype = prototypeRepository.getLastByStyleCode(prototype.getStyleCode());

        //SKC生成逻辑：SPU+2色号流水
        List<Prototype> oldSkcList = prototypeRepository.listByStyleCode(prototype.getStyleCode());
        String skcNum = String.format("%1$02d", oldSkcList.size() + 1);
        String colorDesignCode = prototype.getStyleCode() + skcNum;

        Prototype colorPrototype = PrototypeConverter.buildColorsPrototype(prototype, currentUser.getCurrentUserId(), designerDTO, colorPrototypeId, colorDesignCode);

        //创建skc
        this.createFirstVersionSkc(designStyle.getCategoryName(),colorPrototype, SkcTypeEnum.COMPOUND_COLORS, null);

        return colorPrototypeId;
    }

    @Override
    public PrototypeVo getSavedInfoByDesignCode(String designCode) {
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        Assert.notNull(prototype, "不存在此设计款号:{}", designCode);

        return convertPrototype(prototype, prototype.getPrototypeId());
    }

    /**
     * 查询该设计款号是否存在
     *
     * @param designCode 设计款号（精确）
     * @return 相应结果 ture-存在 false-不存在
     */
    @Override
    public Boolean exists(String designCode) {
        return Optional.ofNullable(prototypeRepository.getByDesignCode(designCode)).map(e -> true).orElse(Boolean.FALSE);
    }

    @Override
    public PrototypeVo getDoneDetailById(Long prototypeId) {
        SdpDesignException.notNull(prototypeId, "版单id为空! ");

        PrototypeHistory prototypeHistory = prototypeHistoryRepository.getDoneDetailById(prototypeId);
        SdpDesignException.notNull(prototypeHistory, "版单信息不存在; prototypeId:{}; ", prototypeId);

        Prototype prototype = Beans.copyValue(prototypeHistory, new Prototype());

        return convertPrototype(prototype, prototypeHistory.getPrototypeId());
    }

    @Override
    public List<PrototypeVo> getVersionList(String designCode) {
        if (StringUtils.isBlank(designCode)) {
            return List.of();
        }
        List<PrototypeHistory> prototypeHistoryList = prototypeHistoryRepository.listDoneVersionByDesignCode(designCode);
        if (CollUtil.isEmpty(prototypeHistoryList)) {
            return List.of();
        }
        //prototype_history表中最大版本记录prototypeId如果不是prototype表中prototypeId, 说明是暂存数据,过滤掉最大版本记录
        Prototype latestPrototype = null;
        if (CollUtil.isNotEmpty(prototypeHistoryList)) {
            PrototypeHistory prototypeHistory = prototypeHistoryList.get(0);
            latestPrototype = prototypeRepository.getById(prototypeHistory.getPrototypeId());
        }

        List<PrototypeVo> voList = new ArrayList<>(prototypeHistoryList.size());
        for (int i = 0; i < prototypeHistoryList.size(); i++) {
            if (i == 0 && Objects.isNull(latestPrototype)) {
                continue;
            }
            PrototypeVo vo = new PrototypeVo();
            BeanUtils.copyProperties(prototypeHistoryList.get(i), vo);
            voList.add(vo);
        }

        return voList;
    }

    @Override
    public PrototypeVo getSavedInfoByPrototypeId(Long prototypeId) {
        PrototypeHistory prototypeHistory = prototypeHistoryRepository.getByPrototypeId(prototypeId);
        Assert.notNull(prototypeHistory, "不存在此设计款; prototypeId: {}", prototypeId);
        Prototype prototype = Beans.copyValue(prototypeHistory, new Prototype());

        return convertPrototype(prototype, prototypeId);
    }

    @Override
    public List<PrototypeVo> getSavedInfoListByDesignCode(PrototypeListInnerReq req) {
        List<Prototype> prototypeList = prototypeRepository.list(Wrappers.lambdaQuery(Prototype.class)
                .in(Prototype::getDesignCode, req.getDesignCodeList()));

        List<PrototypeVo> prototypeVoList = prototypeList.stream().map(prototype -> {
            return convertPrototype(prototype, prototype.getPrototypeId());
        }).collect(Collectors.toList());

        return prototypeVoList;
    }

    @Override
    public List<PrototypeVo> getListByPrototypeId(PrototypeListByIdInnerReq req) {
        List<PrototypeHistory> prototypeList = prototypeHistoryRepository.list(Wrappers.lambdaQuery(PrototypeHistory.class)
                .in(PrototypeHistory::getPrototypeId, req.getPrototypeIdList()));

        List<PrototypeVo> prototypeVoList = prototypeList.stream().map(prototypeHistory -> {
            Prototype prototype = new Prototype();
            BeanUtils.copyProperties(prototypeHistory, prototype);
            return convertPrototype(prototype, prototypeHistory.getPrototypeId());
        }).collect(Collectors.toList());

        return prototypeVoList;
    }

    @Override
    public PrototypeMakeSameVo getMakeSameInfoByDesignCode(String designCode) {
        PrototypeVo prototypeVo = getSavedInfoByDesignCode(designCode);
        Assert.isTrue(prototypeVo.getIsDoneVersion(), "此设计款号未拆版，请勿引用");

        PrototypeMakeSameVo prototypeMakeSameVo = new PrototypeMakeSameVo();
        BeanUtils.copyProperties(prototypeVo, prototypeMakeSameVo);

        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototypeVo.getPrototypeId());
        SdpDesignException.notNull(prototypeDetail, "设计款详情不存在! ");
        prototypeMakeSameVo.setColorInfoList(prototypeDetail.getColorInfoList());

        return prototypeMakeSameVo;
    }

    @Override
    public void cancelPrototype(Long prototypeId, PrototypeCancelReq cancelReq, String currentUserBbCode) {
        Prototype prototype = prototypeRepository.getById(prototypeId);
        if (Objects.isNull(prototype)) {
            return;
        }
        UserContent currentUser = UserContentHolder.get();
        org.springframework.util.Assert.notNull(currentUser, "用户信息为空,请登录! ");
        //取消设计款
        prototypeRepository.updateById(Prototype.builder().prototypeId(prototypeId).isCanceled(true).cancelTime(LocalDateTime.now()).build());
        //取消设计款历史表
        PrototypeHistory canclePrototypeHistory = PrototypeHistory.builder()
                .isCanceled(true)
                .cancelTime(LocalDateTime.now())
                .reviserId(currentUser.getCurrentUserId())
                .reviserName(currentUser.getCurrentUserName())
                .revisedTime(LocalDateTime.now())
                .build();
        prototypeHistoryRepository.updateByDesignCode(prototype.getDesignCode(), canclePrototypeHistory);
        //取消设计款详情
        prototypeHistoryRepository.list(Wrappers.lambdaQuery(PrototypeHistory.class).eq(PrototypeHistory::getDesignCode, prototype.getDesignCode()))
                .stream().map(PrototypeHistory::getPrototypeId).forEach(pId -> {
                    // PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(pId);
                    PrototypeDetail cancelPrototypeDetail = PrototypeDetail.builder()
                            .cancelReason(cancelReq.getCancelReason())
                            .cancelRemark(cancelReq.getCancelRemark())
                            .cancelUserId(currentUser.getCurrentUserId())
                            .cancelUserName(currentUser.getCurrentUserName())
                            .cancelUserCode(currentUserBbCode)
                            .build();
                    // if (Objects.nonNull(prototypeDetail)) {
                    //     cancelPrototypeDetail.setStyleReferType(prototypeDetail.getStyleReferType());
                    //     cancelPrototypeDetail.setStyleReferDesignCode(prototypeDetail.getStyleReferDesignCode());
                    // }
                    prototypeDetailRepository.cancelByPrototypeId(pId, cancelPrototypeDetail);
                });

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(AsyncTask.wrapper(() -> {
                    PrototypeHistory prototypeHistory = prototypeHistoryRepository.selectFirstByDesignCode(prototype.getDesignCode());
                    //向量删除信息组装
                    StyleLibraryDeleteReq deleteReq = StyleLibraryConverter.deleteReq(StyleLibrarySourceTypeEnum.STYLE_MANAGEMENT.code,prototypeHistory.getPrototypeId());
                    //请求向量创建
                    styleLibraryHelper.styleLibraryDelete(deleteReq);
                }));
            }
        });

    }

    private PrototypePrintInfoVo buildPrototypePrintInfoVo(Prototype prototype) {
        PrototypeVo prototypeVo = convertPrototype(prototype, prototype.getPrototypeId());
        PrototypePrintInfoVo prototypePrintInfoVo = new PrototypePrintInfoVo();
        BeanUtils.copyProperties(prototypeVo, prototypePrintInfoVo);

        // prototypePrintInfoVo.setDemandType(DemandTaskTypeEnum.findByCode(prototype.getDemandTaskType()).getDemandType());
        //已拆版的数据,则返回第一次拆版完成的时间
        // if (PrototypeStatusEnum.DECOMPOSED.getCode().equals(prototype.getPrototypeStatus())) {
        //     prototypePrintInfoVo.setCurrentTime(prototype.getFirstVersionDoneTime());
        // }
        // prototypePrintInfoVo.setDemandType(DemandTaskTypeEnum.findByCode(prototype.getDemandTaskType()).getDemandType());
        //为了和前端统一计算当前耗时（）
        //环节创建时间（用于计算当前耗时【currentTime-processingStepCreatedTime】）（对应拆版的：款生成时间）
        // prototypePrintInfoVo.setProcessingStepCreatedTime(prototype.getSkcCreatedTime());
        // prototypePrintInfoVo.setMaterialDemandNameList(buildMaterialDemandNameList(prototypeVo.getMaterialDemandList()));
        prototypePrintInfoVo.setStorageLocation(findStorageLocation(prototype.getDesignCode()));

        if (SkcTypeEnum.COMPOUND_COLORS.getCode().equals(prototype.getSkcType())) {
            setPatternMakerInfo(prototype, prototypePrintInfoVo);
        }

        return prototypePrintInfoVo;
    }

    @Override
    public List<PrototypePrintInfoVo> batchPrintInfo(BatchPrintReq req) {
        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(req.getDesignCodeList());
        if (CollectionUtil.isEmpty(prototypeList)) {
            return Collections.emptyList();
        }
        return prototypeList.stream()
                .map(this::buildPrototypePrintInfoVo)
                .collect(Collectors.toList());
    }


    /**
     * 通过履约接口获取库位号
     *
     * @param designCode 设计款号
     */
    public String findStorageLocation(String designCode) {
        if (StrUtil.isBlank(designCode)) {
            return null;
        }

        List<PrototypeOrderMaterialOpenResp> latestMaterialList = zjDesignRemoteHelper.findLatestMaterial(Collections.singletonList(designCode), true);

        Map<String, PrototypeOrderMaterialOpenResp> openRespMap = StreamUtil.list2Map(latestMaterialList, PrototypeOrderMaterialOpenResp::getExDesignCode);

        if (openRespMap.containsKey(designCode)) {
            return openRespMap.get(designCode).getStorageLocation();
        } else {
            return null;
        }
    }

    /**
     * 组装 物料的名称
     *
     * @return
     */
    private List<String> buildMaterialDemandNameList(List<MaterialDemandVo> materialDemandList) {

        List<String> fabricNameList = materialDemandList.stream().filter(md -> MaterialDemandTypeEnum.FABRIC.getCode().equals(md.getMaterialDemandType())).map(MaterialDemandVo::getName).collect(Collectors.toList());
        List<String> accessoriesNameList = materialDemandList.stream().filter(md -> MaterialDemandTypeEnum.ACCESSORIES.getCode().equals(md.getMaterialDemandType())).map(MaterialDemandVo::getName).collect(Collectors.toList());
        List<String> materialDemandNameList = new ArrayList<>();
        if (cn.yibuyun.framework.collection.Collections.isNotEmpty(fabricNameList)) {
            cn.yibuyun.framework.collection.Collections.sort(fabricNameList);
            materialDemandNameList.addAll(fabricNameList);
        }
        if (cn.yibuyun.framework.collection.Collections.isNotEmpty(accessoriesNameList)) {
            cn.yibuyun.framework.collection.Collections.sort(accessoriesNameList);
            materialDemandNameList.addAll(accessoriesNameList);
        }
        return materialDemandNameList;
    }


    @Override
    public void updateCheckPriceState(PrototypeCheckPriceInnerReq req) {
        //根据设计款号更新核价状态:
        log.info("=== 更新设计款的核价状态-req: {} ===", JSONUtil.toJsonStr(req));

        //查询已完成版本的设计款历史表
        List<PrototypeHistory> list = prototypeHistoryRepository.list(Wrappers.lambdaQuery(PrototypeHistory.class)
                .eq(PrototypeHistory::getDesignCode, req.getDesignCode())
                .eq(PrototypeHistory::getIsDoneVersion, Bool.YES.getCode()));
        SdpDesignException.notEmpty(list, "设计款不存在! ");

        List<Long> prototypeIdList = list.stream().map(PrototypeHistory::getPrototypeId).collect(Collectors.toList());

        //todo 对接核价更新
        // prototypeDetailRepository.update(Wrappers.lambdaUpdate(PrototypeDetail.class)
        //         .set(PrototypeDetail::getCheckPriceState, req.getCheckPriceState())
        //         .in(PrototypeDetail::getPrototypeId, prototypeIdList));
        log.info("=== 更新设计款的核价状态, 更新成功; prototypeIdList: {} ===", JSONUtil.toJsonStr(prototypeIdList));

    }

    /**
     * 根据spu款式号查询该款式号下的设计款是否存在已完成的情况
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @Override
    public List<PrototypeVo> selectDemandDone(PrototypeDemandListReq req) {
        List<Prototype> prototypes = prototypeRepository.getByStyleCodes(req.getStyleCodeList());
        return prototypes.stream().map(e -> {
            PrototypeVo prototypeVo = new PrototypeVo();
            BeanUtils.copyProperties(e, prototypeVo);
            return prototypeVo;
        }).collect(Collectors.toList());
        //return prototypes.stream().map(Prototype::getStyleCode).collect(Collectors.toList());
    }

    private PrototypeVo convertPrototype(Prototype prototype, Long prototypeId) {

        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototypeId);
        PrototypeVo prototypeVo = new PrototypeVo();
        BeanUtils.copyProperties(prototypeDetail, prototypeVo);
        BeanUtils.copyProperties(prototype, prototypeVo);

        List<String> designPicture = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);
        prototypeVo.setDesignPicture(designPicture);

        return prototypeVo;
    }

    @Override
    public List<MakeSameDesignCodeVo> queryMakeSameByDesignCode(String designCode) {
        return prototypeRepository.queryMakeSameByDesignCode(designCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void prototypeToPurchasePrototype() {
        log.info("=========================> 开始将采购关联的prototype信息迁移到PurchasePrototypeInfo表中 <=========================");
        List<PurchasePrototypeTempVo> prototypeHistoryVos = prototypeRepository.queryPurchaseRelPrototype();

        List<PurchasePrototypeInfo> list = purchasePrototypeInfoRepository.list();

        if (CollectionUtil.isNotEmpty(prototypeHistoryVos) && CollectionUtil.isEmpty(list)) {
            prototypeHistoryVos.forEach(ph -> {
                PurchasePrototypeInfo purchasePrototypeInfo = purchasePrototypeInfoRepository.getOne(
                        Wrappers.<PurchasePrototypeInfo>lambdaQuery()
                                .eq(PurchasePrototypeInfo::getPrototypeId, ph.getPrototypeId())
                                .last("limit 1"));
                if (ObjectUtil.isEmpty(purchasePrototypeInfo)) {
                    PurchasePrototypeInfo info = new PurchasePrototypeInfo();
                    BeanUtils.copyProperties(ph, info);
                    info.setPurchasePrototypeInfoId(ph.getPrototypeId());
                    list.add(info);
                }
            });
            if (CollectionUtil.isNotEmpty(list)) {
                purchasePrototypeInfoRepository.saveBatch(list);
            }
        }
        log.info("=========================> 采购关联的prototype信息迁移到PurchasePrototypeInfo表中 end <=========================");
    }

    @Override
    public DesignerVo getDesignerInfo(Long userId) {
        DesignerRemoteReq req = new DesignerRemoteReq();
        req.setDesignerId(String.valueOf(userId));
        DataResponse<List<DesignerDTO>> listDataResponse = designerRemoteService.designerInfoList(req);

        log.info("【获取设计师信息】== listDataResponse = {},data={}", listDataResponse.isSuccessful(), JSONUtil.toJsonStr(listDataResponse.getData()));

        if (!listDataResponse.isSuccessful()) {
            throw new SdpDesignException(listDataResponse.getMessage());
        }

        List<DesignerDTO> designerDTOList = listDataResponse.getData();
        DesignerVo vo = new DesignerVo();
        if (CollectionUtil.isNotEmpty(designerDTOList)) {
            DesignerDTO designerDTO = designerDTOList.get(0);
            BeanUtils.copyProperties(designerDTO, vo);
        }

        return vo;
    }

    @Override
    public List<PrototypeSplicingInfoVo> queryIsSplicingByDesignCodes(PrototypeSplicingBatchInnerReq req) {
        List<Prototype> prototypeList = this.getLastByDesignCodes(req);

        if (CollectionUtil.isEmpty(prototypeList)) {
            return new ArrayList<>();
        }
        List<Long> prototypeIdList = prototypeList.stream().map(Prototype::getPrototypeId).collect(Collectors.toList());
        List<PrototypeSplicingInfoVo> prototypeSplicingInfoVoList = prototypeRepository.queryIsSplicingByPrototypeIds(prototypeIdList);

        return prototypeSplicingInfoVoList;
    }

    @Override
    public void updateSpuInfoWithinHistory(DesignStyleUpdateDto updateReq) {
        //根据spuCode更新所有prototype表与prototype_history表中SPU维度的信息

        SdpDesignException.notNull(updateReq, "入参为空");
        SdpDesignException.notBlank(updateReq.getStyleCode(), "styleCode为空");
        String styleCode = updateReq.getStyleCode();

        //1,查询styleCode下的版单, 历史版单, 版单详情
        List<Prototype> prototypeList = prototypeRepository.getListByStyleCode(styleCode);
        SdpDesignException.notEmpty(prototypeList, "{}对应的设计款信息不存在", styleCode);

        List<PrototypeHistory> prototypeHistoryList = prototypeHistoryRepository.getListByStyleCode(styleCode);
        SdpDesignException.notEmpty(prototypeHistoryList, "{}对应的设计款版本信息不存在", styleCode);
        List<Long> prototypeIdList = prototypeHistoryList.stream().map(PrototypeHistory::getPrototypeId).collect(Collectors.toList());

        List<PrototypeDetail> detailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIdList);
        SdpDesignException.notEmpty(detailList, "{}对应的设计款详情不存在", styleCode);

        //2,更新prototype与prototype_history表中的款式品类信息
        String category = updateReq.getCategory();
        String categoryName = updateReq.getCategoryName();
        List<Prototype> prototypeUpdateList = prototypeList.stream().map(item -> {
            Prototype updatePrototype = new Prototype();
            updatePrototype.setPrototypeId(item.getPrototypeId());
            updatePrototype.setCategory(category);
            updatePrototype.setCategoryName(categoryName);
            return updatePrototype;
        }).collect(Collectors.toList());
        prototypeRepository.updateBatchById(prototypeUpdateList);

        List<PrototypeHistory> prototypeHistoryUpdateList = prototypeHistoryList.stream().map(item -> {
            PrototypeHistory updatePrototype = new PrototypeHistory();
            updatePrototype.setPrototypeId(item.getPrototypeId());
            updatePrototype.setCategory(category);
            updatePrototype.setCategoryName(categoryName);
            return updatePrototype;
        }).collect(Collectors.toList());
        prototypeHistoryRepository.updateBatchById(prototypeHistoryUpdateList);

        //3,更新prototype_detail表中的spu信息: 销售渠道名称(saleChannelName -> sale_group); 品质等级, 参考链接;
        // List<PrototypeDetail> prototypeDetailUpdateList = detailList.stream().map(item -> {
        //     PrototypeDetail updateDetail = new PrototypeDetail();
        //     updateDetail.setPrototypeDetailId(item.getPrototypeDetailId());
        //     // updateDetail.setSaleGroup(updateReq.getSaleChannelName());
        //     updateDetail.setQualityLevel(updateReq.getQualityLevel());
        //     updateDetail.setQualityLevelCode(updateReq.getQualityLevelCode());
        //     updateDetail.setReferLink(updateReq.getReferLink());
        //     return updateDetail;
        // }).collect(Collectors.toList());
        // prototypeDetailRepository.updateBatchById(prototypeDetailUpdateList);

    }

    @Override
    public void syncCheckPriceProcess(CheckPriceExecuteProcessMqDto mqDto) {
        if (Objects.equals(mqDto.getType(), 2) && Objects.equals(mqDto.getStyleType(),2)) {//预估核价 && 现货的流程
            log.info("同步现货的预估核价dto:{}", JSONObject.toJSONString(mqDto));
            List<EstimateCheckPriceVo> list = sampleClothesRemoteHelper.listEstimateCheckPriceByIds(Arrays.asList(mqDto.getEstimateCheckPriceId()));
            if(CollectionUtil.isEmpty(list)) {
                log.error("根据预估核价ID没有找到对应的核价记录estimateCheckPriceId"+mqDto.getEstimateCheckPriceId());
            }else {
                EstimateCheckPriceVo vo = list.get(0);
                UpdatePredictCheckPriceStatusReq req = new UpdatePredictCheckPriceStatusReq();
                req.setPredictCheckPriceId(vo.getEstimateCheckPriceId());
                req.setStyleCode(vo.getStyleCode());
                req.setPredictCheckPriceTime(vo.getFinishTime());
                req.setTotalCost(vo.getTotalCost());
                req.setPricerId(vo.getPricerId());
                req.setPricerName(vo.getPricerName());
                //100待核价 110已核价 120驳回
                Integer state = vo.getState();
                switch (state){
                    case 100: req.setSpotPriceState(SpotPriceStateEnum.WAIT_CHECK);break;
                    case 110: {
                            req.setSpotPriceState(SpotPriceStateEnum.CHECKED);
                            if(vo.getIsReview()!=null && vo.getIsReview().equals(1)){
                                req.setSpotPriceState(SpotPriceStateEnum.RE_CHECK_PASS);
                            }
                        }
                        break;
                    case 120: req.setSpotPriceState(SpotPriceStateEnum.REJECT);break;
                    default: req.setSpotPriceState(SpotPriceStateEnum.WAIT_CHECK);
                }
                req.setPriceType(SpotSpuPricingTypeEnum.findByCode(vo.getPriceType()));
                spotSpuService.updateEstimateCheckPriceStatus(req);
            }
        }else{
            //精准核价
            if (Objects.equals(mqDto.getType(), 1)) {
                Prototype prototype = prototypeRepository.getByDesignCode(mqDto.getDesignCode());
                PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getLatestPrototypeId());
                if (mqDto.getState() == 100) {
                    prototypeDetail.setCheckPriceState(1);
                }
                if (mqDto.getState() == 110) {
                    prototypeDetail.setCheckPriceState(2);
                }
                prototypeDetail.setCheckPriceId(mqDto.getCheckPriceId());

                prototypeDetailRepository.updateById(prototypeDetail);
            }else if (Objects.equals(mqDto.getType(), 2)) {//预估核价
                List<Prototype> prototypeList = prototypeRepository.listByStyleCode(mqDto.getStyleCode());
                List<String> designCodes = !CollectionUtils.isEmpty(prototypeList) ? prototypeList.stream().map(Prototype::getDesignCode).collect(Collectors.toList()) : new ArrayList<>();
                //找出提交过BOM的skc
                List<BomOrder> bomOrders = bomOrderRepository.listLatestSubmitBomOrder(designCodes,false);
                designCodes = !CollectionUtils.isEmpty(bomOrders) ? bomOrders.stream().map(BomOrder::getDesignCode).collect(Collectors.toList()) : new ArrayList<>();
                //找出最新版本的skcId
                prototypeList = prototypeRepository.listByDesignCodes(designCodes);
                List<Long> prototypeIds = !CollectionUtils.isEmpty(prototypeList) ? prototypeList.stream().map(Prototype::getLatestPrototypeId).collect(Collectors.toList()) : new ArrayList<>();
                //找出最新版本的skc明细
                List<PrototypeDetail> prototypeDetails = prototypeDetailRepository.getListByPrototypeIds(prototypeIds);
                if(CollectionUtil.isEmpty(prototypeDetails)) {
                    log.error("更新skc预估核价状态失败，没找到已提交bom的skc明细");
                }else{
                    prototypeDetails.forEach(prototypeDetail->{
                        if (mqDto.getState() == 100) {
                            prototypeDetail.setPredictCheckPriceStatus(1);
                        }
                        if (mqDto.getState() == 110) {
                            prototypeDetail.setPredictCheckPriceStatus(2);
                        }
                        prototypeDetail.setPredictCheckPriceId(mqDto.getEstimateCheckPriceId());
                    });
                    prototypeDetailRepository.updateBatchById(prototypeDetails);
                }
            }
        }
    }

    @Override
    public PrototypeChangeInfoVo getSkcChangeInfo(String designCode) {
        SdpDesignException.notBlank(designCode, "设计款号为空! ");

        //查询skc最新版本新
        PrototypeVo prototypeVo = this.getSavedInfoByDesignCode(designCode);

        SdpDesignException.isTrue(Objects.equals(PrototypeStatusEnum.DECOMPOSED.getCode(), prototypeVo.getPrototypeStatus()), "设计款{}未提交! ", designCode);

        PrototypeChangeInfoVo changeInfoVo = Beans.copyValue(prototypeVo, new PrototypeChangeInfoVo());
        // changeInfoVo.setStyleType(prototypeVo.getDemandTaskType());

        //查询核价信息
        if (DesignCheckPriceStateEnum.FINISH_CHECK.getCode().equals(prototypeVo.getCheckPriceState())) {
            CheckPriceInnerVo checkedBaseInfo = checkPriceRemoteHelper.findLatestCheckedBaseInfoBySkc(designCode);
            changeInfoVo.setSellPrice(checkedBaseInfo.getTotalCostExt());
        }
        DesignStyle designStyle = designStyleRepository.getByStyleCode(prototypeVo.getStyleCode());
        Assert.notNull(designStyle, "当前skc的spu数据缺失");
        changeInfoVo.setSizeStandard(designStyle.getSizeStandard());
        changeInfoVo.setSizeStandardCode(designStyle.getSizeStandardCode());
        changeInfoVo.setStyleSeasonList(JSON.parseArray(designStyle.getStyleSeason(), OpsObject.class));
        // changeInfoVo.setSilhouetteName(designStyle.getSilhouetteName());
        // changeInfoVo.setSilhouetteCode(designStyle.getSilhouetteCode());
        changeInfoVo.setWeaveMode(designStyle.getWeaveMode());
        changeInfoVo.setWeaveModeCode(designStyle.getWeaveModeCode());
        changeInfoVo.setQualityLevel(designStyle.getQualityLevel());
        changeInfoVo.setQualityLevelCode(designStyle.getQualityLevelCode());
        // changeInfoVo.setStyleSourceCode(designStyle.getStyleSourceCode());
        // changeInfoVo.setStyleSourceName(designStyle.getStyleSourceName());
        // changeInfoVo.setPerformStandardCode(designStyle.getPerformStandardCode());
        // changeInfoVo.setPerformStandardName(designStyle.getPerformStandardName());
        // changeInfoVo.setSecurityCategoryCode(designStyle.getSecurityCategoryCode());
        // changeInfoVo.setSecurityCategoryName(designStyle.getSecurityCategoryName());
        changeInfoVo.setWaveBandCode(designStyle.getWaveBandCode());
        changeInfoVo.setWaveBandName(designStyle.getWaveBandName());
        // changeInfoVo.setPatternElementCode(designStyle.getPatternElementCode());
        // changeInfoVo.setPatternElementName(designStyle.getPatternElementName());
        // changeInfoVo.setQuoteDesignPictureList(prototypeVo.getDesignPicture());
        // changeInfoVo.setClothingStyle(prototypeVo.getClothingStyle());

        return changeInfoVo;
    }

    @Override
    public List<String> getSkcListBySpu(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            return new ArrayList<>();
        }
        return prototypeRepository.getListByStyleCode(styleCode).stream()
                .map(prototype -> prototype.getDesignCode())
                .collect(Collectors.toList());

    }

    /**
     * 根据设计款编码查询最后拆版的记录
     *
     * @param req
     * @return
     */
    public List<Prototype> getLastByDesignCodes(PrototypeSplicingBatchInnerReq req) {
        if (CollectionUtil.isEmpty(req.getDesignCodeList())) {
            return new ArrayList<>();
        }
        return prototypeRepository.getLastByDesignCodes(req.getDesignCodeList());
    }

    public void setPatternMakerInfo(Prototype prototype, PrototypePrintInfoVo prototypePrintInfoVo) {
        // 推款v0.1 正常打版打印设计版单时，因未到纸样环节故无该字段信息，无需展示。跟产品确认过，不管是否到纸样环节都不需要展示
        //复色版单打印时，查询被引用SKC最新提交的纸样师信息，若无则展示该SPU最新的纸样师信息

        // 如果引用的复色款号为空则查询SPU最新的纸样师信息
        if (StringUtils.isEmpty(prototype.getMakeSameDesignCode())) {
            // spu 款式号
            String styleCode = prototype.getStyleCode();
            DataResponse<List<SampleClothesVo>> sampleClothesVoResponse = sampleClothesClient.findByStyleCode(styleCode);
            if (sampleClothesVoResponse.isSuccessful() && CollectionUtil.isNotEmpty(sampleClothesVoResponse.getData())) {
                List<SampleClothesVo> sampleClothesVos = sampleClothesVoResponse.getData();
                List<SampleClothesVo> sampleClothesVoList = sampleClothesVos.stream().filter(item -> !SampleTypeEnum.BEFORE_PRODUCE_SAMPLE.getCode().equals(item.getSampleType())).sorted(Comparator.comparing(SampleClothesVo::getCreatedTime).reversed()).collect(Collectors.toList());
                for (SampleClothesVo sampleClothesVo : sampleClothesVoList) {
                    // 取出SPU下最新的skc
                    DataResponse<SampleClothesInfoVo> dataResponse = getClothesInfoDetailVoDataResponse(sampleClothesVo);
                    if (dataResponse.isSuccessful() && ObjectUtil.isNotEmpty(dataResponse.getData())) {
                        SampleClothesDetailVo detail = dataResponse.getData().getDetail();
                        if (ObjectUtil.isNotEmpty(detail.getPatternMakerId())) {
                            prototypePrintInfoVo.setPatternMakerId(detail.getPatternMakerId());
                            prototypePrintInfoVo.setPatternMakerName(detail.getPatternMakerName());
                            break;
                        }
                    }
                }
            }
        } else {
            boolean isPatternMaker = false;
            // 设计款号
            String designCode = prototype.getMakeSameDesignCode();
            DataResponse<List<SampleClothesVo>> sampleClothesVoResponse = sampleClothesClient.findByDesignCode(designCode);
            if (sampleClothesVoResponse.isSuccessful() && CollectionUtil.isNotEmpty(sampleClothesVoResponse.getData())) {
                List<SampleClothesVo> sampleClothesVoList1 = sampleClothesVoResponse.getData().stream().filter(item -> !SampleTypeEnum.BEFORE_PRODUCE_SAMPLE.getCode().equals(item.getSampleType())).sorted(Comparator.comparing(SampleClothesVo::getCreatedTime).reversed()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(sampleClothesVoList1)) {
                    for (SampleClothesVo sampleClothesVo : sampleClothesVoList1) {
                        DataResponse<SampleClothesInfoVo> dataResponse = getClothesInfoDetailVoDataResponse(sampleClothesVo);
                        if (dataResponse.isSuccessful() && ObjectUtil.isNotEmpty(dataResponse.getData())) {
                            SampleClothesDetailVo detail = dataResponse.getData().getDetail();
                            if (ObjectUtil.isNotEmpty(detail.getPatternMakerId())) {
                                isPatternMaker = true;
                                prototypePrintInfoVo.setPatternMakerId(detail.getPatternMakerId());
                                prototypePrintInfoVo.setPatternMakerName(detail.getPatternMakerName());
                                break;
                            }
                        }
                    }
                }
            }
            if (!isPatternMaker) {
                // spu 款式号
                String styleCode = prototype.getStyleCode();
                DataResponse<List<SampleClothesVo>> response = sampleClothesClient.findByStyleCode(styleCode);
                if (response.isSuccessful() && CollectionUtil.isNotEmpty(response.getData())) {
                    List<SampleClothesVo> sampleClothesVos = response.getData();
                    List<SampleClothesVo> sampleClothesVoList = sampleClothesVos.stream().filter(item -> !SampleTypeEnum.BEFORE_PRODUCE_SAMPLE.getCode().equals(item.getSampleType())).sorted(Comparator.comparing(SampleClothesVo::getCreatedTime).reversed()).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(sampleClothesVoList)) {
                        for (SampleClothesVo sampleClothesVo : sampleClothesVoList) {
                            // 取出SPU下最新的skc
                            DataResponse<SampleClothesInfoVo> dataResponse = getClothesInfoDetailVoDataResponse(sampleClothesVo);
                            if (dataResponse.isSuccessful() && ObjectUtil.isNotEmpty(dataResponse.getData())) {
                                SampleClothesDetailVo detail1 = dataResponse.getData().getDetail();
                                if (ObjectUtil.isNotEmpty(detail1.getPatternMakerId())) {
                                    prototypePrintInfoVo.setPatternMakerId(detail1.getPatternMakerId());
                                    prototypePrintInfoVo.setPatternMakerName(detail1.getPatternMakerName());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }

    }

    private DataResponse<SampleClothesInfoVo> getClothesInfoDetailVoDataResponse(SampleClothesVo sampleClothesVo) {
        return sampleClothesClient.getClothesInfoById(sampleClothesVo.getClothesId());
    }

    @Override
    public Map<String, SkcTagVO> mapSkcTagByDesignCodes(Set<String> designCodes) {
        return CommonUtil.getMapByQuery(prototypeRepository::querySkcTag, designCodes, SkcTagVO::getDesignCode);
    }

    @Override
    public Map<String, SkcTagVO> mapSkcTags(Set<String> designCodes) {
        if (CollectionUtil.isEmpty(designCodes)) {
            return Collections.emptyMap();
        }
        List<SkcTagVO> list = designCodes.stream().map(SkcTagVO::new).collect(Collectors.toList());
        return CommonUtil.getMapByList(list, SkcTagVO::getDesignCode);
    }

    // @Override
    // public List<SkcColorVo> getSkcColor(SkcColorReq req) {
    //     if (Objects.isNull(req) || CollUtil.isEmpty(req.getColorCodeList())) {
    //         return Collections.emptyList();
    //     }
    //
    //     List<Prototype> prototypeList = prototypeRepository.getSkcColor(req.getColorCodeList());
    //     if (CollUtil.isEmpty(prototypeList)) {
    //         return Collections.emptyList();
    //     }
    //
    //     return prototypeList.stream()
    //             .map(item -> Beans.copyValue(item, new SkcColorVo()))
    //             .collect(Collectors.toList());
    // }

    // @Override
    // public List<String> checkSkcColor(SkcColorReq req) {
    //     if (Objects.isNull(req) || CollUtil.isEmpty(req.getColorCodeList())) {
    //         return Collections.emptyList();
    //     }
    //
    //     return prototypeRepository.groupByColorCodes(req.getColorCodeList()).stream()
    //             .map(Prototype::getColorCode)
    //             .collect(Collectors.toList());
    // }


    @Override
    public Integer countByDesignCodeAndDesignerId(String designCode, Long designerId) {
        return prototypeRepository.countByDesignCodeAndDesignerId(designCode, designerId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public int batchUpdateWithMyBatisPlus(List<SalesChannelUpdate> updates) {
        if (updates == null || updates.isEmpty()) {
            return 0;
        }

        log.info("开始批量更新 {} 个SKC的销售渠道", updates.size());

        // 创建临时表
        prototypeMapper.createTempTable();

        try {
            // 分批插入数据到临时表
            int batchSize = 1000;
            for (int i = 0; i < updates.size(); i += batchSize) {
                int end = Math.min(i + batchSize, updates.size());
                List<SalesChannelUpdate> batch = updates.subList(i, end);
                prototypeMapper.batchInsertToTempTable(batch);
                log.info("已插入 {}/{} 条数据到临时表", end, updates.size());
            }

            // 使用临时表更新主表
            int affectedRows = prototypeMapper.updateFromTempTable();

            log.info("批量更新完成，影响 {} 行", affectedRows);
            return affectedRows;
        } finally {
            // 删除临时表
            prototypeMapper.dropTempTable();
        }
    }
}