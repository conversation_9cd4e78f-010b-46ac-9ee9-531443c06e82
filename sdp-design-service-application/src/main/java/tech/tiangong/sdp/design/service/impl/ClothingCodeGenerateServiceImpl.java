package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.base.entity.BaseEntity;
import cn.yibuyun.framework.cache.commands.CacheCommands;
import cn.yibuyun.framework.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.ClothingCodeGenerate;
import tech.tiangong.sdp.design.entity.DesignStyle;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.enums.ClothingCodeDeriveEnum;
import tech.tiangong.sdp.design.enums.ClothingCodeSourceEnum;
import tech.tiangong.sdp.design.enums.ClothingCodeTypeEnum;
import tech.tiangong.sdp.design.enums.DesignStyleSourceTypeEnum;
import tech.tiangong.sdp.design.repository.ClothingCodeGenerateRepository;
import tech.tiangong.sdp.design.repository.DesignStyleRepository;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.service.ClothingCodeDeriveGenerateService;
import tech.tiangong.sdp.design.service.ClothingCodeGenerateService;
import tech.tiangong.sdp.design.vo.req.ClothingCodeGenerateReq;
import tech.tiangong.sdp.utils.BusinessCodeGenerator;
import tech.tiangong.sdp.utils.CodeRuleEnum;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 样衣编号生成表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ClothingCodeGenerateServiceImpl implements ClothingCodeGenerateService {
    private final ClothingCodeGenerateRepository clothingCodeGenerateRepository;
    private final PrototypeRepository prototypeRepository;
    private final ClothingCodeDeriveGenerateService clothingCodeDeriveGenerateService;
    private final DesignStyleRepository designStyleRepository;
    private final CacheCommands cacheCommands;
    private final BusinessCodeGenerator businessCodeGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generate(ClothingCodeGenerateReq req) {
        String code;
        if (req.getSourceEnum() == ClothingCodeSourceEnum.DESIGN) {
            code = switch (req.getTypeEnum()) {
                case SPU -> clothingCodeDeriveGenerateService.spuCode(req);
                case SKC -> clothingCodeDeriveGenerateService.skcCode(req);
            };
        } else {
            //fob按旧逻辑走
            code = switch (req.getTypeEnum()) {
                case SPU -> clothingCodeDeriveGenerateService.spuCodeOld(req);
                case SKC -> clothingCodeDeriveGenerateService.skcCodeOld(req);
            };
        }
        return code;
    }

    private ClothingCodeGenerate saveGenerateData(String code, ClothingCodeGenerate parentEntity, ClothingCodeGenerateReq req) {
        ClothingCodeGenerate entity = new ClothingCodeGenerate();
        entity.setClothingCodeGenerateId(IdPool.getId());
        entity.setCode(code);
        entity.setType(req.getTypeEnum().getCode());
        entity.setSource(req.getSourceEnum().getCode());
        if (Objects.nonNull(parentEntity)) {
            entity.setParentId(parentEntity.getClothingCodeGenerateId());
            entity.setParentCode(parentEntity.getCode());
        }
        clothingCodeGenerateRepository.save(entity);
        return  entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateSpuCode(ClothingCodeSourceEnum sourceEnum) {
        SdpDesignException.notNull(sourceEnum, "sourceEnum为空!");

        ClothingCodeGenerateReq codeGenerateReq = ClothingCodeGenerateReq.builder()
                .typeEnum(ClothingCodeTypeEnum.SPU)
                .sourceEnum(sourceEnum)
                .deriveEnum(ClothingCodeDeriveEnum.SPU_NEW)
                .build();

        return this.generate(codeGenerateReq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateSpuCodeRemake(ClothingCodeSourceEnum sourceEnum, String parentSpuCode) {
        SdpDesignException.notNull(sourceEnum, "sourceEnum为空!");
        SdpDesignException.notBlank(parentSpuCode, "parentSpuCode为空!");

        ClothingCodeGenerateReq codeGenerateReq = ClothingCodeGenerateReq.builder()
                .typeEnum(ClothingCodeTypeEnum.SPU)
                .sourceEnum(sourceEnum)
                .deriveEnum(ClothingCodeDeriveEnum.SPU_REMAKE)
                .code(parentSpuCode)
                .build();

        return this.generate(codeGenerateReq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateSkcCode(ClothingCodeSourceEnum sourceEnum, String spuCode) {
        SdpDesignException.notNull(sourceEnum, "sourceEnum为空!");
        SdpDesignException.notBlank(spuCode, "spuCode为空!");

        ClothingCodeGenerateReq codeGenerateReq = ClothingCodeGenerateReq.builder()
                .typeEnum(ClothingCodeTypeEnum.SKC)
                .sourceEnum(sourceEnum)
                .deriveEnum(ClothingCodeDeriveEnum.SKC_NEW)
                .code(spuCode)
                .build();

        return this.generate(codeGenerateReq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateSkcCodeColorMarking(ClothingCodeSourceEnum sourceEnum, String parentSkcCode) {
        SdpDesignException.notNull(sourceEnum, "sourceEnum为空!");
        SdpDesignException.notBlank(parentSkcCode, "spuCode为空!");

        ClothingCodeGenerateReq codeGenerateReq = ClothingCodeGenerateReq.builder()
                .typeEnum(ClothingCodeTypeEnum.SKC)
                .sourceEnum(sourceEnum)
                .deriveEnum(ClothingCodeDeriveEnum.SKC_COLOR_MARKING)
                .code(parentSkcCode)
                .build();
        return this.generate(codeGenerateReq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initHistory() {
        List<Prototype> prototypes = prototypeRepository.list(Wrappers.lambdaQuery(Prototype.class)
                .select(Prototype::getDesignCode, Prototype::getStyleCode));
        for (Prototype prototype : prototypes) {

            if (StrUtil.isEmpty(prototype.getStyleCode())) {
                continue;
            }

            ClothingCodeGenerate spu = clothingCodeGenerateRepository.getByCodeAndType(prototype.getStyleCode(),
                    ClothingCodeTypeEnum.SPU.getCode());
            if (Objects.isNull(spu)) {
                ClothingCodeGenerateReq req = new ClothingCodeGenerateReq();
                req.setTypeEnum(ClothingCodeTypeEnum.SPU);
                req.setSourceEnum(ClothingCodeSourceEnum.DESIGN);
                spu = saveGenerateData(prototype.getStyleCode(), null, req);
            }
            if (StrUtil.isEmpty(prototype.getDesignCode())) {
                continue;
            }

            ClothingCodeGenerate skc = clothingCodeGenerateRepository.getByCodeAndType(prototype.getDesignCode(), ClothingCodeTypeEnum.SKC.getCode());
            if (Objects.isNull(skc)) {
                ClothingCodeGenerateReq skcReq = new ClothingCodeGenerateReq();
                skcReq.setTypeEnum(ClothingCodeTypeEnum.SKC);
                skcReq.setSourceEnum(ClothingCodeSourceEnum.DESIGN);
                saveGenerateData(prototype.getDesignCode(), spu, skcReq);
            }
        }
    }

    @Override
    public void initNewCode() {
        LocalDateTime monthBegin = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        long count = designStyleRepository.count(new QueryWrapper<DesignStyle>().lambda()
                .eq(DesignStyle::getSourceType, DesignStyleSourceTypeEnum.SELF_SPU_SOURCE.getCode())
                .gt(BaseEntity::getCreatedTime, monthBegin));
        String field = String.format("%s%s", CodeRuleEnum.CLOTHING_SPU_CODE_JV.getCode(), DateUtil.format(new Date(), CodeRuleEnum.CLOTHING_SPU_CODE_JV.getDatePrefix()));
        cacheCommands.hset(CodeRuleEnum.CLOTHING_SPU_CODE_JV.getKey(), field, count + "");
    }

}