package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.query.bom.CraftDemandInfoTransientQuery;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandInfoTransientReq;
import tech.tiangong.sdp.design.vo.resp.bom.CraftDemandInfoTransientVo;

/**
 * 工艺需求_暂存表服务接口
 *
 * <AUTHOR>
 */
public interface CraftDemandInfoTransientService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<CraftDemandInfoTransientVo> page(CraftDemandInfoTransientQuery query);

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    CraftDemandInfoTransientVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(CraftDemandInfoTransientReq req);

    /**
     * 更新数据
     *
     * @param req 数据实体
     */
    void update(CraftDemandInfoTransientReq req);

    /**
     * 删除数据
     *
     * @param id 主键ID
     */
    void remove(Long id);

}
