package tech.tiangong.sdp.design.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.entity.DigitalPrintingStyleDetail;
import tech.tiangong.sdp.design.repository.DigitalPrintingStyleDetailRepository;
import tech.tiangong.sdp.design.service.DigitalPrintingStyleDetailService;
import tech.tiangong.sdp.design.vo.req.digital.DigitalPrintingStyleDetailReq;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingStyleDetailVo;

/**
 * 数码印花_SPU详情表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalPrintingStyleDetailServiceImpl implements DigitalPrintingStyleDetailService {
    private final DigitalPrintingStyleDetailRepository digitalPrintingStyleDetailRepository;

    @Override
    public DigitalPrintingStyleDetailVo getById(Long id) {
        DigitalPrintingStyleDetail entity = digitalPrintingStyleDetailRepository.getById(id);
        DigitalPrintingStyleDetailVo vo = new DigitalPrintingStyleDetailVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(DigitalPrintingStyleDetailReq req) {
        DigitalPrintingStyleDetail entity = new DigitalPrintingStyleDetail();
        BeanUtils.copyProperties(req, entity);
        digitalPrintingStyleDetailRepository.save(entity);
    }

}
