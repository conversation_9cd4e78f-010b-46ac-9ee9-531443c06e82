package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.ClothingCodeGenerate;
import tech.tiangong.sdp.design.enums.ClothingCodeDeriveEnum;
import tech.tiangong.sdp.design.enums.ClothingCodeTypeEnum;
import tech.tiangong.sdp.design.mapper.ClothingCodeGenerateMapper;

import java.util.Optional;

/**
 * 样衣编号生成表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class ClothingCodeGenerateRepository extends BaseRepository<ClothingCodeGenerateMapper, ClothingCodeGenerate> {

    /**
     * 通过编号和类型
     *
     * @param code 编号
     * @param type 类型
     * @return 编号信息
     */
    public ClothingCodeGenerate getByCodeAndType(String code, Integer type) {
        return baseMapper.selectOne(new QueryWrapper<ClothingCodeGenerate>().lambda()
                .eq(ClothingCodeGenerate::getCode, code)
                .eq(ClothingCodeGenerate::getType, type)
                .orderByDesc(ClothingCodeGenerate::getCreatedTime).last("limit 1"));
    }

    /**
     * 查询最新的skc编号
     *
     * @return
     */
    public String selectLatestSPUCode() {
        return selectLatestCode(ClothingCodeTypeEnum.SPU.getCode());
    }

    /**
     * 查询最新的skc编号
     *
     * @return
     */
    public String selectLatestSKCCode() {
         ClothingCodeGenerate clothingCodeGenerate = baseMapper.selectOne(new QueryWrapper<ClothingCodeGenerate>().lambda()
                .select(ClothingCodeGenerate::getCode)
                .eq(ClothingCodeGenerate::getType, ClothingCodeTypeEnum.SKC.getCode())
                .orderByDesc(ClothingCodeGenerate::getCreatedTime).last("limit 1"));
        return Optional.ofNullable(clothingCodeGenerate).map(ClothingCodeGenerate::getCode).orElse("");
    }

    /**
     * 获取最新的样衣编号
     *
     * @param type 类型
     * @return 编号
     */
    public String selectLatestCode(Integer type) {
        ClothingCodeGenerate clothingCodeGenerate = baseMapper.selectOne(new QueryWrapper<ClothingCodeGenerate>().lambda()
                .select(ClothingCodeGenerate::getCode)
                .eq(ClothingCodeGenerate::getType, type)
                .eq(ClothingCodeGenerate::getDeriveType, ClothingCodeDeriveEnum.SPU_NEW.getCode())
                .orderByDesc(ClothingCodeGenerate::getCreatedTime).last("limit 1"));
        return Optional.ofNullable(clothingCodeGenerate).map(ClothingCodeGenerate::getCode).orElse("");
    }


}