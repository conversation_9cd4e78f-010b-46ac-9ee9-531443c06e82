package tech.tiangong.sdp.design.remote;

import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.net.DataResponse;
import com.alibaba.fastjson.JSONObject;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierDetailListRespVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierDetailRespVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierInfoVo;
import com.yibuyun.scm.open.client.supplier.SCMSupplierClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 远程调用SDK获取-供应商信息-客户端
 *
 * <AUTHOR>
 * @date 2020/11/24
 * @since 2020/11/24 23:51
 * description:
 */
@Service
@Slf4j
public class SupplierInfoRemoteHelper {

    @Autowired
    private SCMSupplierClient scmSupplierClient;


    /**
     * 批量-根据供应商id获取供应商基本信息
     *
     * @param supplierIds 供应商id
     * @return {@link List< CommoditySupplierInfoVo >}
     */
    public List<CommoditySupplierInfoVo> getSupplierBaseInfo(List<Long> supplierIds) {
        if (CollectionUtil.isEmpty(supplierIds)) {
            return java.util.Collections.emptyList();
        }

        log.info("据供应商ID批量获取供应商信息,请求参数：{}", JSONObject.toJSONString(supplierIds));


        DataResponse<CommoditySupplierDetailListRespVo> supplierDetailListResp = scmSupplierClient.getCommoditySupplierDetailListRespVoBySupplierIds(supplierIds);
        log.info("据供应商ID批量获取供应商信息,响应结果：{}", JSONObject.toJSONString(supplierDetailListResp));
        CommoditySupplierDetailListRespVo data = supplierDetailListResp.getData();
        if (Objects.isNull(data) || CollectionUtil.isEmpty(data.getSuppliers())) {
            return java.util.Collections.emptyList();
        }

        return data.getSuppliers().stream().map(CommoditySupplierDetailRespVo::getCommoditySupplierInfoVo).collect(Collectors.toList());

    }


    /**
     * 批量-根据供应商id获取供应商详情信息
     *
     * @param supplierIds 供应商id
     * @return {@link List<CommoditySupplierDetailRespVo>}
     */
    public List<CommoditySupplierDetailRespVo> getSupplierDetailInfo(List<Long> supplierIds) {
        if (CollectionUtil.isEmpty(supplierIds)) {
            return java.util.Collections.emptyList();
        }

        log.info("据供应商ID批量获取供应商信息,请求参数：{}", JSONObject.toJSONString(supplierIds));


        DataResponse<CommoditySupplierDetailListRespVo> supplierDetailListResp = scmSupplierClient.getCommoditySupplierDetailListRespVoBySupplierIds(supplierIds);
        log.info("据供应商ID批量获取供应商信息,响应结果：{}", JSONObject.toJSONString(supplierDetailListResp));
        CommoditySupplierDetailListRespVo data = supplierDetailListResp.getData();
        if (Objects.isNull(data) || CollectionUtil.isEmpty(data.getSuppliers())) {
            return java.util.Collections.emptyList();
        }

        return data.getSuppliers();
    }


}
