package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.entity.SpuVisualDemandRecord;
import tech.tiangong.sdp.design.vo.req.visual.SpuVisualDemandRecordSaveReq;

/**
 * 视觉需求创建记录(SpuVisualDemandRecord)服务接口
 *
 * <AUTHOR>
 * @since 2025-06-05 18:27:24
 */
public interface SpuVisualDemandRecordService {


    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    SpuVisualDemandRecord create(SpuVisualDemandRecordSaveReq req);



}
