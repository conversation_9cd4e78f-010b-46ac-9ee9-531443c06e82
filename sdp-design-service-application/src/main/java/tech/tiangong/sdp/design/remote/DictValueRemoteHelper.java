package tech.tiangong.sdp.design.remote;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import team.aikero.admin.common.req.DictReq;
import team.aikero.admin.common.vo.DictVo;
import team.aikero.admin.sdk.client.DictClient;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.utils.StreamUtil;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *   字典服务
 * </p>
 *
 **/
@Service
@Slf4j
@AllArgsConstructor
public class DictValueRemoteHelper {

    private final DictClient dictClient;

    /**
     * 根据字典编码查询对应字典值 - 批量查询
     * @param dictCodes 字典编码集合
     * @return Map<String, DictVo>
     */
    public Map<String, DictVo> mapByDictCodes(List<String> dictCodes) {
        List<DictVo> dictResp = this.listByDictCodes(dictCodes);
        return StreamUtil.list2Map(dictResp, DictVo::getDictCode);
    }

    /**
     * 根据字典编码查询对应字典值 - 批量查询
     * @param dictCodes 字典编码集合
     * @return List<DictVo>
     */
    public List<DictVo> listByDictCodes(List<String> dictCodes) {
        SdpDesignException.notEmpty(dictCodes, "字典编码为空! ");
        DictReq dictReq = new DictReq(dictCodes);
        log.info("=== jv字典查询-req:{}", JSON.toJSONString(dictReq));
        team.aikero.blade.core.protocol.DataResponse<List<DictVo>> response = dictClient.listByDictCode(dictReq);
        log.debug("=== jv字典查询-response:{}", JSON.toJSONString(response));
        if (!response.getSuccessful()) {
            throw new SdpDesignException("字典查询失败:"+response.getMessage());

        }
        return response.getData();
    }

    /**
     * 根据层级获取市场相关字典的键值映射
     *
     * @param level 节点层级：1 表示一级（市场），2 表示二级（系列），3 表示三级（风格）
     * @return Map<dictCode, dictName>
     */
    public Map<String, String> getMarketNodeMap(int level) {
        DictVo marketDictVo = this.listByDictCodes(List.of(DictConstant.MARKET_STYLE)).getFirst();
        if (marketDictVo == null || marketDictVo.getChildren() == null) {
            return null;
        }

        List<DictVo> children = marketDictVo.getChildren();

        switch (level) {
            case 1:
                // 一级节点：市场
                return children.stream()
                        .collect(Collectors.toMap(
                                DictVo::getDictCode,
                                DictVo::getDictName,
                                (v1, v2) -> v1));
            case 2:
                // 二级节点：市场-系列
                return children.stream()
                        .filter(parent -> parent.getChildren() != null)
                        .flatMap(parent -> parent.getChildren().stream()
                                .map(series -> new AbstractMap.SimpleEntry<>(series.getDictCode(), series.getDictName())))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1));
            case 3:
                // 三级节点：市场-系列-风格
                return children.stream()
                        .filter(parent -> parent.getChildren() != null)
                        .flatMap(parent -> parent.getChildren().stream().flatMap(child ->
                                Optional.ofNullable(child.getChildren()).stream().flatMap(List::stream)
                                        .map(style -> new AbstractMap.SimpleEntry<>(style.getDictCode(), style.getDictName()))
                        ))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1));
            default:
                throw new IllegalArgumentException("不支持的层级：" + level);
        }
    }

    /**
     * 获取市场名称映射
     * @param level
     * @return
     */
    public Map<String, String> getMarketNameMap(int level) {
        DictVo marketDictVo = this.listByDictCodes(List.of(DictConstant.MARKET_STYLE)).getFirst();
        if (marketDictVo == null || marketDictVo.getChildren() == null) {
            return null;
        }

        List<DictVo> children = marketDictVo.getChildren();
        Map<String,String> result = new HashMap<>();
        switch (level) {
            case 1:
                 marketDictVo.getChildren().forEach(child -> result.put(child.getDictName(), child.getDictCode()));
                 return result;
            case 2:
                 marketDictVo.getChildren().forEach(parent -> {
                    if (parent.getChildren() != null) {
                        parent.getChildren().forEach(child ->
                                result.put(parent.getDictName() + "-" + child.getDictName(), child.getDictCode())
                        );
                    }
                });
                return result;
            case 3:
                marketDictVo.getChildren().forEach(parent -> {
                    if (parent.getChildren() != null) {
                        parent.getChildren().forEach(child -> {
                            if (child.getChildren() != null) {
                                child.getChildren().forEach(series ->
                                        result.put(parent.getDictName() + "-" + child.getDictName() + "-" + series.getDictName(), series.getDictCode())
                                );
                            }
                        });
                    }
                });
                return result;
            default:
                throw new IllegalArgumentException("不支持的层级：" + level);
        }

    }


    /**
     * 通过品类去查询字典
     * @param category
     * @return
     */
    public DictVo getDictVoByCategory(String category) {
        // 参数校验
        if (category == null || category.isEmpty()) {
            return null;
        }

        // 获取根节点字典
        DictVo categoryDictVo = this.listByDictCodes(List.of("clothing_category")).getFirst();
        if (categoryDictVo == null || categoryDictVo.getChildren() == null) {
            return null;
        }

        // 分割分类编码
        String[] split = category.split("-");
        if (split.length == 0) {
            return null;
        }

        // 使用Map缓存当前层级的节点，提高查找效率
        Map<String, DictVo> currentLevelMap = categoryDictVo.getChildren().stream()
                .collect(Collectors.toMap(DictVo::getDictCode, Function.identity()));

        DictVo result = null;

        // 逐级查找
        for (int i = 0; i < split.length; i++) {
            String code = split[i];

            // 在当前层级中查找匹配的编码
            DictVo found = currentLevelMap.get(code);

            // 如果当前级找不到，直接返回null
            if (found == null) {
                return null;
            }

            result = found;

            // 如果是最后一级或者没有子节点，则停止继续查找
            if (i == split.length - 1 || found.getChildren() == null || found.getChildren().isEmpty()) {
                break;
            }

            // 准备下一级查找，预先构建下一级的Map
            currentLevelMap = found.getChildren().stream()
                    .collect(Collectors.toMap(DictVo::getDictCode, Function.identity()));
        }

        return result;
    }



}
