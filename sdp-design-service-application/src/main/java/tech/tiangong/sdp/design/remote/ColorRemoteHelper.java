package tech.tiangong.sdp.design.remote;

import cn.yibuyun.framework.net.DataResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.material.pojo.web.request.color.ColorListReq;
import tech.tiangong.sdp.material.pojo.web.response.color.ColorResp;
import tech.tiangong.sdp.material.sdk.service.remote.ColorRemoteService;

import java.util.List;

/**
 * <p>
 *   基础资料-颜色
 * </p>
 *
 **/
@Service
@Slf4j
@AllArgsConstructor
public class ColorRemoteHelper {

    private final ColorRemoteService colorRemoteService;


    /**
     * 批量查询颜色
     * @param colorCodeList 颜色编码集合
     * @return List<ColorResp>
     */
    public List<ColorResp> list(List<String> colorCodeList) {
        ColorListReq req = new ColorListReq();
        req.setColorCodeList(colorCodeList);
        DataResponse<List<ColorResp>> response = colorRemoteService.list(req);

        if (!response.isSuccessful()) {
            throw new SdpDesignException(response.getMessage());
        }
        return response.getData();
    }

}
