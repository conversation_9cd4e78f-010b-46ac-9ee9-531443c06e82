package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.MaterialPurchaseFollowService;
import tech.tiangong.sdp.design.service.PrototypeService;
import tech.tiangong.sdp.design.vo.req.purchase.DesignCodePurchaseOrderReq;
import tech.tiangong.sdp.design.vo.resp.material.MaterialPurchaseFollowVo;

import java.util.List;
import java.util.Map;

/**
 * 采购信息关联版单信息表-内部接口-(PurchasePrototypeInfoInnerController)
 *
 * <AUTHOR>
 * @since 2022-03-21 11:08:19
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/purchasePrototypeInfo")
public class PurchasePrototypeInfoInnerController extends BaseController {

    private final PrototypeService prototypeService;
    private final MaterialPurchaseFollowService materialPurchaseFollowService;

    /**
     * 数据迁移
     * 采购信息关联版单信息表
     * @return void
     */
    @PostMapping("/prototype-data-migrate")
    @Deprecated(since = "2.0", forRemoval = true)
    public DataResponse<Void> prototypeToPurchasePrototype(){
        prototypeService.prototypeToPurchasePrototype();
        return DataResponse.ok();
    }

    @PostMapping("/material-purchase-designcode")
    public DataResponse<Map<String, List<MaterialPurchaseFollowVo>>> materialPurchaseByDesignCode(@Validated @RequestBody DesignCodePurchaseOrderReq req){

        return DataResponse.ok(materialPurchaseFollowService.materialPurchaseByDesignCode(req));
    }

}
