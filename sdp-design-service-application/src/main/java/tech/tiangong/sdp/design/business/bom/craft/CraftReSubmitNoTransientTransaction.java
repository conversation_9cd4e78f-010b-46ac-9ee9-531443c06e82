package tech.tiangong.sdp.design.business.bom.craft;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.zjkj.scf.bundle.common.dto.demand.dto.response.CraftDemandMatchDetailDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.business.Transaction;
import tech.tiangong.sdp.design.business.bom.notification.Notification;
import tech.tiangong.sdp.design.converter.CraftDemandInfoConverter;
import tech.tiangong.sdp.design.converter.NotificationConverter;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.BomOrderMaterial;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.enums.CraftDemandStateEnum;
import tech.tiangong.sdp.design.helper.PrepareBomCraftCycleHelper;
import tech.tiangong.sdp.design.repository.BomOrderMaterialRepository;
import tech.tiangong.sdp.design.repository.BomOrderRepository;
import tech.tiangong.sdp.design.repository.CraftDemandInfoRepository;
import tech.tiangong.sdp.design.vo.dto.bom.CraftNotificationDto;
import tech.tiangong.sdp.design.vo.dto.bom.ReplenishPreCuttingCraftDto;
import tech.tiangong.sdp.design.vo.dto.bom.SyncCraftClothesDto;
import tech.tiangong.sdp.design.vo.req.bom.BomCraftSubmitHandleReq;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 已提交/已核算的，提交。没有暂存
 * <p>第一种情况：</p>
 * <p>
 * · 若有原表数据，若有删选料和选料工艺，则删除原表选料、选料工艺，则需推送关闭工艺到履约。
 * · 若有原表数据，在原选料上增加工艺，则在原表，选料工艺添加工艺，则需推送创建工艺到履约。
 * · 若有原表数据，选料更换规格，删选料、工艺，并添加新的选料和工艺，和上面描述重复了。处理逻辑如上。
 * <p>
 * · 若有原表数据，若有删辅料需求，关闭需求下的所有工艺，则需推送删除辅料需求（无需推工艺需求，因履约会自动关闭工艺需求）到履约。
 * · 若有原表数据，若仅删辅料需求的工艺，则需推送关闭工艺到履约。
 * · 若有原表数据，若在辅料需求，添加工艺需求, 则需推送创建工艺到履约。
 * · 若更换物料，删除物料，并创建新物料，并且关联辅料需求，推送更换物料给履约
 * <p>
 * · 若有添加选料，创建bom_order_material和material_snapshot，直接保存在原表，推送工艺到履约
 * <p>
 * · 补推二次工艺
 * · bom版本需升，状态改成【已提交】
 * · 记得将已关闭的工艺也复制一份关联到新的bom
 *
 * </p>
 * <p>第二种情况</p>
 * <p>
 * · 若有原表数据，若有删选料和选料工艺，则删除原表选料、选料工艺，则需推送关闭工艺到履约。
 * · 若有原表数据，在原选料上增加工艺，则在原表，选料工艺添加工艺，则需推送创建工艺到履约。
 * · 若有原表数据，选料更换规格，删选料、工艺，并添加新的选料和工艺，和上面描述重复了。处理逻辑如上。
 * <p>
 * · 若有原表数据，若有删辅料需求，关闭需求下的所有工艺，则需推送删除辅料需求（无需推工艺需求，因履约会自动关闭工艺需求）到履约。
 * · 若有原表数据，若仅删辅料需求的工艺，则需推送关闭工艺到履约。
 * · 若有原表数据，若在辅料需求，添加工艺需求, 则需推送创建工艺到履约。
 * · 若更换物料，删除物料，并创建新物料，并且关联辅料需求，推送更换物料给履约
 * <p>
 * · 若有添加选料，创建bom_order_material和material_snapshot，直接保存在原表，推送工艺到履约
 * · 若有添加物料需求，创建bom_material_demand，bom_order_material 一条数据。bom_order_material表的material_snapshot_id是空，
 * 直接在原表，添加物料和工艺，需推送辅料需求、工艺（绑定需求）给履约
 * · 补推二次工艺
 * · bom版本升，状态改成【已提交-找料中】
 * · 记得将已关闭的工艺也复制一份关联到新的bom
 * </p>
 */
@Slf4j
@RequiredArgsConstructor
public class CraftReSubmitNoTransientTransaction implements Transaction {
    private final BomCraftSubmitHandleReq req;
    /**
     * 推送二次工艺
     */
    private final Notification<CraftNotificationDto> craftNotification;
    private final Notification<ReplenishPreCuttingCraftDto> replenishPreCuttingCraftNotification;

    private final Notification<SyncCraftClothesDto> syncCraftClothesNotification;

    private final CraftDemandInfoRepository craftDemandInfoRepository = SpringUtil.getBean(CraftDemandInfoRepository.class);
    private final BomOrderRepository bomOrderRepository = SpringUtil.getBean(BomOrderRepository.class);
    private final BomOrderMaterialRepository bomOrderMaterialRepository = SpringUtil.getBean(BomOrderMaterialRepository.class);
    private final PrepareBomCraftCycleHelper prepareBomCraftCycleHelper = SpringUtil.getBean(PrepareBomCraftCycleHelper.class);



    @Override
    public void execute() {
        SdpDesignException.notNull(req.getNewBomOrderId(), "新BOM的id不能为空");

        Set<Long> delCraftDemandIds = req.getDelCraftDemandIds();

        // 1. 查询新BOM的工艺需求
        BomOrder newBomOrder = bomOrderRepository.getById(req.getNewBomOrderId());
        // 2. 记得将已关闭的工艺也复制一份关联到新的bom 。 上个版本的bom关联工艺，不需要更新。
        // 这个类也会被一个场景使用。 已提交，有暂存，在这种场景，上游会传工艺暂存表的craftDemandTransientId，并赋值到craftDemandId
        List<CraftDemandInfo> addCraftDemands = req.getAddCraftDemandList().stream()
                .map(craftDemandReq -> CraftDemandInfoConverter.newCraftDemandV3(craftDemandReq, newBomOrder)).collect(Collectors.toList());

        // 如果删除工艺，也要？ 这个类也会被一个场景使用。 已提交，有暂存，在这种场景，上游会传工艺暂存表的craftDemandTransientId，并赋值到craftDemandId
        List<CraftDemandInfo> delCraftDemandInfos = handleDelCraftDemands(delCraftDemandIds, newBomOrder);


        //要拿没改动的数据，也要复制到原表，然后要过滤已关闭的工艺
        List<CraftDemandInfo> prevAllCraftDemands = craftDemandInfoRepository.getListByBomIdsAndState(Lists.newArrayList(req.getBomOrder().getBomId()), CraftDemandStateEnum.SUBMIT.getCode());
        List<CraftDemandInfo> notModifyCraftDemands = Collections.emptyList();

        if (CollectionUtil.isNotEmpty(prevAllCraftDemands)){
            notModifyCraftDemands = prevAllCraftDemands.stream().map(craftDemandInfo -> {
                if (CollectionUtil.isNotEmpty(delCraftDemandIds) && delCraftDemandIds.contains(craftDemandInfo.getCraftDemandId())) {
                    return null;
                }

                CraftDemandInfo newCraftDemandInfo = new CraftDemandInfo();
                BeanUtils.copyProperties(craftDemandInfo,newCraftDemandInfo);
                newCraftDemandInfo.setBomId(req.getNewBomOrderId());
                newCraftDemandInfo.setCraftDemandId(IdPool.getId());
                Long newBomMaterialId = req.getOldNewMaterialIdMap().get(craftDemandInfo.getBomMaterialId());
                SdpDesignException.notNull(newBomMaterialId,"工艺需求id映射关系错误.oldBomMaterialId:{} bomId:{}",craftDemandInfo.getBomMaterialId(),req.getBomOrder().getBomId());
                newCraftDemandInfo.setBomMaterialId(newBomMaterialId);
                return newCraftDemandInfo;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        }


        List<CraftDemandInfo> saveToDB = new LinkedList<>();
        // 工艺周期添加
        prepareCraftCycle(addCraftDemands, Boolean.FALSE);
        saveToDB.addAll(addCraftDemands);
        saveToDB.addAll(delCraftDemandInfos);
        // 工艺周期添加
        prepareCraftCycle(notModifyCraftDemands, Boolean.FALSE);
        saveToDB.addAll(notModifyCraftDemands);
        //保存到数据库
        log.info("【已提交-没有暂存】的提交,bomId:{},newBomId:{}, addCraftDemands:{}",req.getBomOrder().getBomId(),req.getNewBomOrderId(),JSONUtil.toJsonStr(addCraftDemands));
        log.info("【已提交-没有暂存】的提交,bomId:{},newBomId:{}, delCraftDemandInfos:{}",req.getBomOrder().getBomId(),req.getNewBomOrderId(),JSONUtil.toJsonStr(delCraftDemandInfos));
        log.info("【已提交-没有暂存】的提交,bomId:{},newBomId:{}, notModifyCraftDemands:{}",req.getBomOrder().getBomId(),req.getNewBomOrderId(),JSONUtil.toJsonStr(notModifyCraftDemands));
        craftDemandInfoRepository.saveBatch(saveToDB);

        //更新旧bom的工艺周期信息
        List<CraftDemandInfo> oldCraftDemandInfoList = craftDemandInfoRepository.getListByBomIdsAndState(List.of(req.getBomOrder().getBomId()), CraftDemandStateEnum.SUBMIT.getCode());
        this.prepareCraftCycle(oldCraftDemandInfoList, Boolean.TRUE);


        craftNotification.addBatch(NotificationConverter.buildCraftNotificationDto(addCraftDemands));
        // 暂时不过滤关闭辅料需求相关的工艺。 过于复杂
        //delCraftDemandInfos，也要推到打版
        craftNotification.addBatch(NotificationConverter.buildCraftNotificationDto(delCraftDemandInfos));
        //推送二次工艺到履约
        craftNotification.send();

        //创建工艺后，需回查已创建好的工艺,并设置履约的工艺id
        Map<Long, CraftDemandInfo> allCrafts = craftDemandInfoRepository.getListByBomIds(List.of(req.getNewBomOrderId())).stream().collect(Collectors.toMap(CraftDemandInfo::getCraftDemandId, Function.identity(), (k1, k2) -> k1));
        Stream.of(addCraftDemands, notModifyCraftDemands).flatMap(List::stream).filter(Objects::nonNull).forEach(craftDemandInfo -> {
            CraftDemandInfo craftDemandInfoDB = allCrafts.get(craftDemandInfo.getCraftDemandId());
            craftDemandInfo.setThirdPartyCraftDemandId(craftDemandInfoDB.getThirdPartyCraftDemandId());
            craftDemandInfo.setThirdPartyCraftDemandCode(craftDemandInfoDB.getThirdPartyCraftDemandCode());
        });


        //补推裁前工艺、打版
        List<Long> addBomMaterialIds = addCraftDemands.stream().map(CraftDemandInfo::getBomMaterialId).collect(Collectors.toList());
        Map<Long, BomOrderMaterial> addBomMaterialMap = Map.of();
        if (CollectionUtil.isNotEmpty(addBomMaterialIds)) {
           addBomMaterialMap = bomOrderMaterialRepository.listByIds(addBomMaterialIds).stream().collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, bomOrderMaterial -> bomOrderMaterial));
        }
        replenishPreCuttingCraftNotification.add(NotificationConverter.buildReplenishPreCuttingCraftDto(addCraftDemands, newBomOrder, addBomMaterialMap));
        replenishPreCuttingCraftNotification.send();

        //推送工艺需求到打版
        syncCraftClothesNotification.add(NotificationConverter.buildSyncCraftClothesDto(addCraftDemands,newBomOrder));
        syncCraftClothesNotification.add(NotificationConverter.buildSyncCraftClothesDto(delCraftDemandInfos,newBomOrder));
        syncCraftClothesNotification.send();

    }

    /**
     * 将已关闭的工艺也复制一份关联到新的bom
     *
     * @return
     */
    private List<CraftDemandInfo> handleDelCraftDemands(Set<Long> delCraftDemandIds, BomOrder bomOrder) {
        if (CollectionUtil.isEmpty(delCraftDemandIds)) {
            return Collections.emptyList();
        }

        //记得将已关闭的工艺也复制一份关联到新的bom
        return craftDemandInfoRepository.listByIds(delCraftDemandIds).stream().map(craftDemandInfo -> CraftDemandInfoConverter.closeCraftDemandV3(craftDemandInfo, bomOrder)).collect(Collectors.toList());
    }

    /**
     * 赋值最新工艺周期
     */
    private void prepareCraftCycle(List<CraftDemandInfo> craftDemands, Boolean updateCraft) {
        if (CollUtil.isEmpty(craftDemands)) {
            return;
        }
        List<Long> listThirdDemandId = craftDemands.stream().
                map(CraftDemandInfo::getThirdPartyCraftDemandId).distinct().collect(Collectors.toList());
        listThirdDemandId.removeIf(Objects::isNull);
        if (CollUtil.isEmpty(listThirdDemandId)) {
            return;
        }
        // 获取第三方id对应工艺周期map
        Map<Long, CraftDemandMatchDetailDto.CraftDemandMatchDetail> mapThirdIdDetail
                = prepareBomCraftCycleHelper.getCraftCycleMatchByThirdId(listThirdDemandId);
        for (CraftDemandInfo craftDemandInfo : craftDemands) {
            if (Objects.isNull(craftDemandInfo.getThirdPartyCraftDemandId())) {
                continue;
            }
            CraftDemandMatchDetailDto.CraftDemandMatchDetail craftDemandMatchDetail =
                    mapThirdIdDetail.get(craftDemandInfo.getThirdPartyCraftDemandId());
            if (null != craftDemandMatchDetail) {
                // 赋值工艺周期
                craftDemandInfo.setSampleCraftCycle(craftDemandMatchDetail.getSampleCraftCycle());
                craftDemandInfo.setBulkCraftCycle(craftDemandMatchDetail.getBulkCraftCycle());
                //更新工艺周期信息
                if (updateCraft && Objects.nonNull(craftDemandInfo.getCraftDemandId())) {
                    craftDemandInfoRepository.lambdaUpdate().
                            eq(CraftDemandInfo::getCraftDemandId, craftDemandInfo.getCraftDemandId()).
                            set(CraftDemandInfo::getSampleCraftCycle, craftDemandMatchDetail.getSampleCraftCycle()).
                            set(CraftDemandInfo::getBulkCraftCycle, craftDemandMatchDetail.getBulkCraftCycle()).update();
                }
            }
        }
    }


}
