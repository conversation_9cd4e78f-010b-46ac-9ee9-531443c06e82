package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.util.Json;
import com.alibaba.fastjson.JSON;
import com.yibuyun.framework2.match.vo.AccessoriesSkuInfoVo;
import com.yibuyun.scm.common.dto.accessories.response.CategoryTreeMapVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSkuVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierExtVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierInfoVo;
import com.zjkj.scf.bundle.common.dto.demand.dto.request.MaterialDetailReq;
import com.zjkj.scf.bundle.common.dto.demand.enums.BusinessTypeEnum;
import com.zjkj.scf.bundle.common.dto.demand.enums.DemandChannelEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.constant.PlmDesignConstants;
import tech.tiangong.sdp.design.converter.BomOrderConverter;
import tech.tiangong.sdp.design.converter.BomOrderMaterialConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.ProductRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjNotify2OldJvRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.dto.bom.BomCopyInfoDto;
import tech.tiangong.sdp.design.vo.dto.bom.BomMaterialInfoDto;
import tech.tiangong.sdp.design.vo.dto.material.BomMaterialUpdateDto;
import tech.tiangong.sdp.design.vo.dto.material.HouliuDemandMatchPushDto;
import tech.tiangong.sdp.design.vo.req.log.DesignLogReq;
import tech.tiangong.sdp.design.vo.req.material.MaterialSnapshotCreateReq;
import tech.tiangong.sdp.design.vo.req.purchase.PurchaseApplyMaterialConfirmBatchReq;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/21
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class DemandMatchServiceImpl implements DemandMatchService {

    private final PrototypeHistoryRepository prototypeHistoryRepository;
    private final PrototypeRepository prototypeRepository;
    private final MqProducer mqProducer;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final BomOrderMaterialTransientRepository bomOrderMaterialTransientRepository;
    private final BomMaterialDemandRepository bomMaterialDemandRepository;
    private final BomMaterialDemandTransientRepository bomMaterialDemandTransientRepository;
    private final BomOrderRepository bomOrderRepository;
    private final BomOrderTransientRepository bomOrderTransientRepository;
    private final MaterialSnapshotService materialSnapshotService;
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final PurchaseApplyFollowService purchaseApplyFollowService;
    private final BomOperateService bomOperateService;
    private final BomMaterialCommonService bomMaterialCommonService;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;
    private final DesignLogService designLogService;
    private final ProductRemoteHelper productRemoteHelper;
    private final DesignRemarksRepository designRemarksRepository;
    private final ZjNotify2OldJvRemoteHelper zjNotify2OldJvRemoteHelper;




    /**
     * 1、在skc开发bom中发起的辅料找料需求，会将需求数据回显至开发bom中，bom提交后推送创建需求信息给scm并与该版本开发bom做关联关系。
     * 2、需求回复后，将需求最新回复内容自动回填至与该需求有关联关系的bom中并自动用需求数量与物料最小单位下采购，如需求数量为3，回复物料最小单位为个则自动采购3个。（上新状态款逻辑见下）
     * 3、好料网需求关闭不影响开发bom中需求及回复物料展示，需求及开发bom关联关系的解除仅可通过开发bom的删除功能。
     * <p>
     * bom中需求回复版本更新逻辑：
     * 1、开发bom状态为【已提交-找料中】，bom中需求被回复，对应需求中物料被替换成新回复的sku。bom版本不变，此时若bom中仍存在未回复的需求则状态为【已提交-找料中】；否则为【已提交】。
     * 2、开发bom状态为【已提交】、【已核算】，bom中物料需求被回复，对应需求中物料被替换成新回复sku。原版本状态不变，bom版本+1，状态为【已提交】
     *
     * @param mqMessageReq mq消息体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void houliuDemandMatchReplayCallBack(MqMessageReq mqMessageReq) {
        HouliuDemandMatchPushDto demandMatchDto = JSON.parseObject(mqMessageReq.getMqContent(), HouliuDemandMatchPushDto.class);
        SdpDesignException.notNull(demandMatchDto,"好料需求匹配结果实体转换有误，接收匹配结果失败");
        log.info("===【好料需求匹配结果推送回调】 入参: {} ===", JSON.toJSONString(demandMatchDto));
        //接收好料匹配结果处理
        SdpDesignException.notNull(demandMatchDto.getSkuId(), "好料需求匹配结果推送回调, skuId为空");
        SdpDesignException.notNull(demandMatchDto.getDemandId(), "好料需求匹配结果推送回调, demandId为空");
        Long supplyDemandId = demandMatchDto.getDemandId();
        Long matchId = demandMatchDto.getMatchId();
        Long skuId = demandMatchDto.getSkuId();

        //校验物料供应商信息是否存在
        CommoditySupplierInfoVo supplierInfo = this.checkAndGetSupplierInfo(supplyDemandId, skuId);
        SdpDesignException.notNull(supplierInfo, "物料对应的供应商不存在! skuId:{}; supplyDemandId:{}", skuId, supplyDemandId);

        BomMaterialInfoDto materialInfoDto = this.checkMaterialState(demandMatchDto, matchId, skuId);

        //1, 根据履约需求id, 查询最新的bom物料需求
        BomMaterialDemand materialDemand = bomMaterialDemandRepository.latestBySupplyDemandId(supplyDemandId);
        if (Objects.isNull(materialDemand)) {
            //转发到旧JV
            log.info("=== 好料需求匹配-转发旧JV ====");
            zjNotify2OldJvRemoteHelper.notify2OldJv(mqMessageReq);
        }
        SdpDesignException.notNull(materialDemand, "好料匹配回复, 无对应需求信息! supplyDemandId:{}; matchId:{}", supplyDemandId, matchId);
        //需求状态不是已提交, 不处理
        if (!Objects.equals(materialDemand.getDemandState(), BomDemandStateEnum.SUBMIT.getCode())) {
            log.info("===【好料需求匹配结果推送回调】, 对应需求不是已提交状态, 不处理; supplyDemandId:{}; matchId:{}; demandInfo:{}; ===",
                    supplyDemandId, matchId, Json.serialize(materialDemand));
            return;
        }
        SdpDesignException.isTrue(Objects.equals(materialDemand.getDemandState(), BomDemandStateEnum.SUBMIT.getCode()),
                "好料匹配回复, 对应需求不是已提交状态, 不处理! supplyDemandId:{}; matchId:{}", supplyDemandId, matchId);

        //2, 匹配需求是否对应最新版本bom,不是最新bom单, 说明最新bom单删除了这个需求, 已归档, 不接收匹配
        BomOrder bomOrder = bomOrderRepository.getById(materialDemand.getBomId());
        BomOrder latestBomOrder = bomOrderRepository.getLatestBomByDesignCode(bomOrder.getDesignCode());
        if (!Objects.equals(bomOrder.getBomId(), latestBomOrder.getBomId())) {
            log.info("===【好料需求匹配结果推送回调】, 需求对应bom单不是最新版本, 不处理; supplyDemandId:{}; matchId:{}; bomId:{} ===",
                    supplyDemandId, matchId, bomOrder.getBomId());
            return;
        }

        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        SdpDesignException.notNull(prototype, "skc不存在!{}", bomOrder.getDesignCode());

        Long bomMaterialId;
        List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfos = new LinkedList<>();
        //4, 若bom是找料中, 不升版本,在原版本上替换物料的sku;
        if (Objects.equals(bomOrder.getMaterialSearchState(), Bool.YES.getCode())) {
            if (Objects.equals(materialDemand.getMaterialSearchState(), Bool.YES.getCode())) {
                //首次匹配回复
                log.info("=== 首次匹配回复 supplyDemandId: {} ===", supplyDemandId);
                bomMaterialId = this.firstMatchHandle(demandMatchDto, materialDemand, bomOrder, materialUpdateInfos, supplierInfo, materialInfoDto);
            } else {
                //多次匹配回复
                log.info("=== 多次匹配回复 supplyDemandId: {} ===", supplyDemandId);
                bomMaterialId = this.moreMatchHandle(demandMatchDto, materialDemand, bomOrder, materialUpdateInfos, supplierInfo, materialInfoDto);
            }
            //判断是否还存在需求有找料中状态, 如果没有, bom单更新为非找料中
            this.updateSearchState(bomOrder);

        }

        //5, bom已完成找料，bom中物料需求被回复，对应需求中物料被替换成新回复sku。原版本状态不变，bom版本+1，状态为【已提交】
        else {
            log.info("=== 多次匹配回复-升版本 supplyDemandId: {} ===", supplyDemandId);
            bomMaterialId = this.moreMatchNewVersionHandle(demandMatchDto, materialDemand, bomOrder, materialUpdateInfos, supplierInfo, materialInfoDto);
        }


        //6, 自动下采购
        // BomOrderMaterial matchBomMaterial = bomOrderMaterialRepository.getById(bomMaterialId);
        // this.autoPurchase(materialDemand, bomOrder, matchBomMaterial);

        //物料变更推送致景
        zjDesignRemoteHelper.demandMatchUpdateMaterial(materialUpdateInfos, prototype.getBizChannel());

    }

    private void syncBom2product(BomOrder newBomOrder) {
        if (Objects.isNull(newBomOrder)) {
            return;
        }
        String designCode = newBomOrder.getDesignCode();
        // BOM提交同步信息到推款-生产资料
        Map<String, Object> mqContent = new HashMap<>(16);
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        mqContent.put("styleCode", prototype.getStyleCode());
        mqContent.put("designCode", designCode);
        mqContent.put("bomId", newBomOrder.getBomId());
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.BOM_SUBMIT_SYNC_MEANS_OF_PRODUCTION,
                DesignMqConstant.ORDER_UPDATE_MEANS_OF_PRODUCTION_EXCHANGE, null,
                JSON.toJSONString(mqContent));
        log.info("【匹配回复bom变更-同步生产资料】mqMessageReq:{}", JSON.toJSONString(mqMessageReq));
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

    private BomMaterialInfoDto checkMaterialState(HouliuDemandMatchPushDto demandMatchDto, Long matchId, Long skuId) {
        String skuCode = demandMatchDto.getSkuCode();
        Long supplyDemandId = demandMatchDto.getDemandId();
        Set<Long> skuIdSet = new HashSet<>();
        skuIdSet.add(skuId);
        BomMaterialInfoDto materialInfoDto = bomMaterialCommonService.queryMaterialInfoBySkuId(skuIdSet, MaterialDemandTypeEnum.ACCESSORIES);
        SdpDesignException.notNull(materialInfoDto, "物料信息不存在! skuId:{}; supplyDemandId:{}", skuId, supplyDemandId);

        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = materialInfoDto.getAccessoriesSpuMap();

        ProductSkuVo productSkuVo = accessoriesSkuMap.get(skuId);
        SdpDesignException.notNull(productSkuVo, "匹配回复-辅料sku信息不存在! skuCode:{}; supplyDemandId:{}", skuCode, matchId);
        ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(productSkuVo.getSpuId());
        SdpDesignException.notNull(productSpuInfoVo, "匹配回复-辅料SPU信息不存在! skuCode:{};supplyDemandId:{}",  skuCode, matchId);
        //接收履约匹配回复结果校验未上架好料网商品不加入bom
        SdpDesignException.isTrue(Objects.equals(productSpuInfoVo.getOnShelf(), Bool.YES.getCode()),
                "匹配回复-物料未上架好料网; skuCode:{}; supplyDemandId:{}; skuId:{}", skuCode, matchId, skuId);
        SdpDesignException.isTrue(Objects.equals(productSpuInfoVo.getEnabled(), Bool.YES.getCode()),
                "匹配回复-物料未启用; skuCode:{}; supplyDemandId:{}; skuId:{}", skuCode, matchId, skuId);
        SdpDesignException.isTrue(Objects.nonNull(productSkuVo.getMinPrice()) && StringUtils.isNotBlank(productSkuVo.getMinUnit()),
                "匹配回复-物料缺少价格最小价格与单位; skuCode:{}; supplyDemandId:{}; skuId:{}", skuCode, matchId, skuId);

        return materialInfoDto;
    }

    private CommoditySupplierInfoVo checkAndGetSupplierInfo(Long supplyDemandId, Long skuId) {
        Set<Long> skuIdSet = new HashSet<>();
        skuIdSet.add(skuId);
        List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(skuIdSet);
        SdpDesignException.notEmpty(accessoriesSkuInfos, "好料需求匹配结果推送回调, 好料网物料信息不存在, skuId:{}; supplyDemandId:{}", skuId, supplyDemandId);
        Long supplierId = accessoriesSkuInfos.get(0).getSupplierId();
        SdpDesignException.notNull(supplierId, "好料需求匹配结果推送回调, 物料对应的供应商id为空! skuId:{}; supplyDemandId:{}", skuId, supplyDemandId);

        //查询供应商信息
        Map<Long, CommoditySupplierInfoVo> supplierInfoVoMap = bomMaterialCommonService.getSupplierInfo(List.of(supplierId));
        SdpDesignException.isTrue(CollUtil.isNotEmpty(supplierInfoVoMap), "好料需求匹配结果推送回调, 物料对应的供应商不存在! skuId:{}; supplyDemandId:{}", skuId, supplyDemandId);

        return supplierInfoVoMap.get(supplierId);
    }

    private void updateSearchState(BomOrder bomOrder) {
        if (Objects.equals(bomOrder.getMaterialSearchState(), Bool.NO.getCode())) {
            return;
        }
        List<BomMaterialDemand> demandList = bomMaterialDemandRepository.listByBomId(bomOrder.getBomId());
        BomMaterialDemand searchingDemand = demandList.stream()
                .filter(item -> Objects.equals(item.getDemandState(), BomDemandStateEnum.SUBMIT.getCode()))
                .filter(item -> Objects.equals(item.getMaterialSearchState(), Bool.YES.getCode()))
                .findFirst().orElse(null);
        if (Objects.isNull(searchingDemand)) {
            //所有已提交的需求完成找料, 去掉bom找料中状态
            bomOperateService.finishSearch(bomOrder.getBomId());

            //同步信息到推款-生产资料
            this.syncBom2product(bomOrder);
        }else {
            //bom推送致景
            bomOperateService.pushBom2Zj(bomOrder, PushZjTypeEnum.BOM_UPDATE);
        }
    }

    // @Override
    // @Transactional(rollbackFor = Exception.class)
    // public void houliuDemandMatchReplayCallBack(HouliuDemandMatchPushDto demandMatchDto) {
    //     log.info("===【好料需求匹配结果推送回调】 入参: {} ===", JSON.toJSONString(demandMatchDto));
    //     //接收好料匹配结果处理
    //     Long supplyDemandId = demandMatchDto.getDemandId();
    //     Long matchId = demandMatchDto.getMatchId();
    //
    //     //1, 根据履约需求id, 查询最新的bom物料需求
    //     BomMaterialDemand materialDemand = bomMaterialDemandRepository.latesBySupplyDemandId(supplyDemandId);
    //     PlmDesignException.notNull(materialDemand, "好料匹配回复, 无对应需求信息! supplyDemandId:{}; matchId:{}", supplyDemandId, matchId);
    //
    //     //2, 匹配需求是否对应最新版本bom,不是最新bom单, 说明最新bom单删除了这个需求, 已归档, 不接收匹配
    //     BomOrder bomOrder = bomOrderRepository.getById(materialDemand.getBomId());
    //     BomOrder latestBomOrder = bomOrderRepository.getLatestBomByDesignCode(bomOrder.getDesignCode());
    //     if (!Objects.equals(bomOrder.getBomId(), latestBomOrder.getBomId())) {
    //         log.info("===【好料需求匹配结果推送回调】, 需求对应bom单不是最新版本, 不处理; supplyDemandId:{}; matchId:{}; bomId:{} ===",
    //                 supplyDemandId, matchId, bomOrder.getBomId());
    //         return;
    //     }
    //
    //     Long bomMaterialId = null;
    //
    //     //2, 如果是找料中, 说明还没有匹配回复,
    //     if (Objects.equals(materialDemand.getMaterialSearchState(), Bool.YES.getCode())) {
    //         //需求首次回复匹配
    //         bomMaterialId = this.firstMatchHandle(demandMatchDto, materialDemand, bomOrder);
    //     }
    //     //3, 需求已存在匹配回复
    //     else {
    //         OnShelfStateResp onShelfStateResp = prototypeOnShelfService.querySkcShelfState(bomOrder.getDesignCode());
    //         //skc已上新, 物料需求新回复内容不会自动回填至bom及下采购; (更新需求匹配数量,但不替换物料)
    //         if (Objects.nonNull(onShelfStateResp) && Objects.equals(onShelfStateResp.getOnShelfStatus(), Bool.YES.getCode())) {
    //             //更新需求匹配数量,但不替换物料;
    //             this.matchOnShelfHandle(materialDemand, bomOrder);
    //             return;
    //         }
    //         //若skc未上新, 替换需求下最新物料, 并自动下采购; 若有暂存, 同步到暂存需求与物料
    //         bomMaterialId = this.moreMatchHandle(demandMatchDto, materialDemand, bomOrder);
    //     }
    //
    //     BomOrderMaterial matchBomMaterial = bomOrderMaterialRepository.getById(bomMaterialId);
    //     //4, 自动下采购
    //     this.autoPurchase(materialDemand, bomOrder, matchBomMaterial);
    //
    //     //5, 如果bom是找料中, 判断是否还存在需求有找料中状态, 如果没有, bom单更新为非找料中
    //     if (Objects.equals(bomOrder.getMaterialSearchState(), Bool.YES.getCode())) {
    //         List<BomMaterialDemand> demandList = bomMaterialDemandRepository.listByBomId(bomOrder.getBomId());
    //         BomMaterialDemand searchingDemand = demandList.stream()
    //                 .filter(item -> Objects.equals(item.getMaterialSearchState(), Bool.YES.getCode()))
    //                 .findFirst().orElse(null);
    //         if (Objects.isNull(searchingDemand)) {
    //             //所有需求已完成找料, 去掉bom找料中状态
    //             bomOperateService.finishSearch(bomOrder.getBomId());
    //         }
    //     }
    //
    // }

    private void autoPurchase(BomMaterialDemand materialDemand, BomOrder bomOrder, BomOrderMaterial matchBomMaterial) {
        //自动下采购:自动用需求数量与物料最小单位下采购，如需求数量为3，回复物料最小单位为个则自动采购3个。自动下采购自有预料为否
        Long bomMaterialId = matchBomMaterial.getBomMaterialId();
        Long materialSnapshotId = matchBomMaterial.getMaterialSnapshotId();
        //物料快照
        MaterialSnapshot materialSnapshot = materialSnapshotRepository.getById(materialSnapshotId);
        //工艺信息
        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getListByMaterialId(bomMaterialId);
        PrototypeHistory prototypeHistory = prototypeHistoryRepository.getByPrototypeId(bomOrder.getPrototypeId());
        //备注信息
        List<DesignRemarks> designRemarksList = designRemarksRepository.getListByBizChildIds(List.of(materialDemand.getLatestBomMaterialId()));

        PurchaseApplyMaterialConfirmBatchReq purchaseBatchReq = this.buildAutoPurchaseReq(materialDemand, bomOrder,
                matchBomMaterial, materialSnapshot, craftDemandInfoList, prototypeHistory, designRemarksList);

        log.info("=== 匹配回复自动下采购-req:{} ===", Json.serialize(purchaseBatchReq));
        purchaseApplyFollowService.purchaseApplyMaterialConfirmBatch(purchaseBatchReq);
    }

    private PurchaseApplyMaterialConfirmBatchReq buildAutoPurchaseReq(BomMaterialDemand materialDemand, BomOrder bomOrder,
                                                                      BomOrderMaterial matchBomMaterial, MaterialSnapshot materialSnapshot,
                                                                      List<CraftDemandInfo> craftDemandInfoList, PrototypeHistory prototypeHistory,
                                                                      List<DesignRemarks> designRemarksList) {
        PurchaseApplyMaterialConfirmBatchReq purchaseBatchReq = new PurchaseApplyMaterialConfirmBatchReq();
        purchaseBatchReq.setPrototypeId(bomOrder.getPrototypeId());
        purchaseBatchReq.setStyleCode(prototypeHistory.getStyleCode());
        purchaseBatchReq.setDesignCode(prototypeHistory.getDesignCode());
        purchaseBatchReq.setPurchaseApplyCause("版房系统自动下采购");
        purchaseBatchReq.setOrderAuto(Boolean.TRUE);
        purchaseBatchReq.setCreatorName(PlmDesignConstants.AUTO_PURCHASE_USER_NAME);

        PurchaseApplyMaterialConfirmBatchReq.PurchaseListReq purchaseItemReq = new PurchaseApplyMaterialConfirmBatchReq.PurchaseListReq();
        purchaseItemReq.setSkuId(materialSnapshot.getSkuId());
        purchaseItemReq.setSkuCode(materialSnapshot.getSkuCode());

        if (CollUtil.isNotEmpty(craftDemandInfoList)) {
            purchaseItemReq.setCraftDemandInfoList(craftDemandInfoList);
            //拼接裁前二次工艺,取末级(有些只有二级)
            List<String> beforeCraftCategory = this.getBeforeCraftCategory(craftDemandInfoList);
            if (CollUtil.isNotEmpty(beforeCraftCategory)) {
                purchaseItemReq.setCuttingProcess((StrUtil.join(StrUtil.COMMA, beforeCraftCategory)));
            }
        }
        purchaseItemReq.setPurchaseQuantity(materialDemand.getDemandNum() + "");
        purchaseItemReq.setCutMethod(null);
        purchaseItemReq.setCuttingMethod("40");
        purchaseItemReq.setPurchaseUnit(materialSnapshot.getMinPriceUnit());
        purchaseItemReq.setMaterialCode(materialSnapshot.getSkuCode());//辅料取SKU
        purchaseItemReq.setMaterialCategory(materialDemand.getPrototypeMaterialName());
        purchaseItemReq.setMaterialName(materialSnapshot.getCommodityName());
        purchaseItemReq.setMaterialColorNo(null);
        purchaseItemReq.setColorCardPictureUrl(null);

        String materialPictureJson = materialSnapshot.getMaterialPicture();
        if (StringUtils.isNotBlank(materialPictureJson)) {
            List<String> pictureList = JSON.parseArray(materialPictureJson, String.class);
            if (CollUtil.isNotEmpty(pictureList)) {
                purchaseItemReq.setMatchPicture(StrUtil.join(StrUtil.COMMA, pictureList));
            }
        }
        purchaseItemReq.setDemandType(MaterialDemandTypeEnum.ACCESSORIES.getCode());
        String skuAttrs = materialSnapshot.getSkuAttrs();
        if (StringUtils.isNotBlank(skuAttrs)) {
            purchaseItemReq.setSkuAttrs(materialSnapshot.getSkuAttrs());
            String accessoryColor = this.getAccessoryColor(skuAttrs);
            if (StringUtils.isNotBlank(accessoryColor)) {
                purchaseItemReq.setMaterialColor(accessoryColor);
            }
        }
        purchaseItemReq.setMaterialSnapshotId(materialSnapshot.getMaterialSnapshotId());
        purchaseItemReq.setBomMaterialId(matchBomMaterial.getBomMaterialId());
        purchaseItemReq.setPartUse(matchBomMaterial.getPartUse());
        // purchaseItemReq.setDemandId(materialSnapshot.getDemandId());

        DesignRemarks designRemarks = designRemarksList.stream().max(Comparator.comparing(DesignRemarks::getCreatedTime)).orElseGet(DesignRemarks::new);
        purchaseItemReq.setRemark(designRemarks.getRemark());

        purchaseBatchReq.setPurchaseApplyList(List.of(purchaseItemReq));
        return purchaseBatchReq;
    }

    private String getAccessoryColor(String skuAttrJson) {
        String accessoryColorName = "";
        if (StringUtils.isNotBlank(skuAttrJson)) {
            List<AccessoriesSkuInfoVo.SkuAttrInfo> skuAttrs = JSON.parseArray(skuAttrJson,AccessoriesSkuInfoVo.SkuAttrInfo.class);
            if (!CollectionUtils.isEmpty(skuAttrs)) {
                for (AccessoriesSkuInfoVo.SkuAttrInfo skuAttr : skuAttrs) {
                    String attrName = skuAttr.getAttrName();
                    if (StringUtils.isNotBlank(attrName)) {
                        if (attrName.contains("颜色")) {
                            accessoryColorName = skuAttr.getAttrValue();
                        }
                    }
                }
            }
        }
        return accessoryColorName;
    }


    private List<String> getBeforeCraftCategory(List<CraftDemandInfo> craftDemandInfoList) {
        return craftDemandInfoList.stream()
                .filter(item -> Objects.equals(CraftCuttingTypeEnum.BEFORE.getCode(), item.getCraftsRequire()))
                .map(item -> StringUtils.isBlank(item.getCategory3()) ? item.getCategory2() : item.getCategory3())
                .collect(Collectors.toList());
    }

    private Long firstMatchHandle(HouliuDemandMatchPushDto demandMatchDto,
                                  BomMaterialDemand materialDemand,
                                  BomOrder bomOrder,
                                  List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfos,
                                  CommoditySupplierInfoVo supplierInfo,
                                  BomMaterialInfoDto materialInfoDto) {
        //1, 查需求下的找料中的物料;
        BomOrderMaterial searchMaterial = this.getSearchingMaterial(materialDemand);

        //2, 创建物料快照
        Long materialSnapshotId = this.createMaterialSnapshot(demandMatchDto, searchMaterial, bomOrder, supplierInfo, materialInfoDto);

        //3, 物料关联snapshot,更新状态
        BomOrderMaterial updateMaterial = new BomOrderMaterial();
        updateMaterial.setBomMaterialId(searchMaterial.getBomMaterialId());
        updateMaterial.setMaterialSnapshotId(materialSnapshotId);
        updateMaterial.setMaterialState(BomMaterialStateEnum.NORMAL.getCode());
        //价格信息
        this.resetPriceInfo(demandMatchDto, materialInfoDto, updateMaterial);

        this.updateBomMaterial(updateMaterial);

        //4, 需求管理最新物料,更新状态
        BomMaterialDemand updateDemand = new BomMaterialDemand();
        updateDemand.setBomMaterialDemandId(materialDemand.getBomMaterialDemandId());
        updateDemand.setMaterialSearchState(Bool.NO.getCode());
        updateDemand.setMaterialMatchNum(1);
        updateDemand.setLatestBomMaterialId(searchMaterial.getBomMaterialId());
        bomMaterialDemandRepository.updateById(updateDemand);

        //5, 如果有暂存,同步更新暂存表
        this.matchFirstTimeSyncTransient(materialDemand, bomOrder, materialSnapshotId, demandMatchDto, materialInfoDto);

        //6, 封装物料更新MQ消息体
        this.buildUpdateInfoFirstMatch(demandMatchDto, materialSnapshotId, materialUpdateInfos, materialDemand);

        return searchMaterial.getBomMaterialId();
    }

    private void updateBomMaterial(BomOrderMaterial updateMaterial) {
        if (Objects.isNull(updateMaterial)) {
            return;
        }
        bomOrderMaterialRepository.lambdaUpdate()
                .set(Objects.nonNull(updateMaterial.getMaterialState()), BomOrderMaterial::getMaterialState, updateMaterial.getMaterialState())
                .set(BomOrderMaterial::getMaterialSnapshotId, updateMaterial.getMaterialSnapshotId())
                .set(BomOrderMaterial::getMinPrice, updateMaterial.getMinPrice())
                .set(BomOrderMaterial::getPriceInvalidTime, updateMaterial.getPriceInvalidTime())
                .set(BomOrderMaterial::getSamplePurchasingCycle, updateMaterial.getSamplePurchasingCycle())
                .set(BomOrderMaterial::getBulkPurchasingCycle, updateMaterial.getBulkPurchasingCycle())
                .set(BomOrderMaterial::getRevisedTime, LocalDateTime.now())
                .eq(BomOrderMaterial::getBomMaterialId, updateMaterial.getBomMaterialId())
                .update();
    }

    private void resetPriceInfo(HouliuDemandMatchPushDto demandMatchDto,
                                BomMaterialInfoDto materialInfoDto,
                                BomOrderMaterial updateMaterial) {
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        ProductSkuVo productSkuVo = accessoriesSkuMap.get(demandMatchDto.getSkuId());
        if (Objects.nonNull(productSkuVo)) {
            // 价格失效时间
            updateMaterial.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());
            updateMaterial.setMinPrice(productSkuVo.getMinPrice());
            //采购周期
            updateMaterial.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
            updateMaterial.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
        }
    }

    private void resetPriceInfoTransient(HouliuDemandMatchPushDto demandMatchDto,
                                         BomMaterialInfoDto materialInfoDto,
                                         BomOrderMaterialTransient updateMaterialTransient) {
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        ProductSkuVo productSkuVo = accessoriesSkuMap.get(demandMatchDto.getSkuId());
        if (Objects.nonNull(productSkuVo)) {
            // 价格失效时间
            updateMaterialTransient.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());
            updateMaterialTransient.setMinPrice(productSkuVo.getMinPrice());
            //采购周期
            updateMaterialTransient.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
            updateMaterialTransient.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
        }
    }

    private void sendBomMaterialUpdateMqFanout(List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfoList) {
        log.info("=== 匹配回复后-物料变更对象-materialUpdateInfoList:{} ===", JSON.toJSONString(materialUpdateInfoList));
        if (CollUtil.isEmpty(materialUpdateInfoList)) {
            return;
        }
        BomMaterialUpdateDto bomMaterialUpdateDto = new BomMaterialUpdateDto();
        bomMaterialUpdateDto.setUpdateInfoList(materialUpdateInfoList);
        MqMessageReq messageReq = MqMessageReq.build(
                MqBizTypeEnum.BOM_SUBMIT_SYNC_MATERIAL_UPDATE,
                DesignMqConstant.BOM_MATERIAL_UPDATE_EXCHANGE,
                JSON.toJSONString(bomMaterialUpdateDto));
        // final Map<String, String> messageHeaders = new HashMap<>(16);
        // messageHeaders.put("acs_code", "SDP_DESIGN_MATERIAL_UPDATE");
        log.info("===bom单物料变更-匹配回复后_发送mq: mqDto: {} ===", JSON.toJSONString(bomMaterialUpdateDto));
        mqProducer.sendOnAfterCommit(messageReq);

        log.info("===bom单物料变更-匹配回复后_mq消息发送完毕===");
    }


    private void buildUpdateInfoFirstMatch(HouliuDemandMatchPushDto demandMatchDto,
                                           Long materialSnapshotId,
                                           List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfos,
                                           BomMaterialDemand materialDemand) {
        //封装物料变更入参
        BomMaterialUpdateDto.MaterialUpdateInfo updateInfo = new BomMaterialUpdateDto.MaterialUpdateInfo();
        //要传供应链的需求id
        updateInfo.setDemandId(materialDemand.getSupplyChainDemandId());
        updateInfo.setNewMaterialSnapShotId(materialSnapshotId);
        updateInfo.setNewSkuId(demandMatchDto.getSkuId());
        updateInfo.setNewCommodityId(demandMatchDto.getCommodityId());

        updateInfo.setOldMaterialSnapShotId(null);
        updateInfo.setOldSkuId(null);
        updateInfo.setOldCommodityId(null);
        materialUpdateInfos.add(updateInfo);
    }



    /**
     * 需求非第一个匹配回复处理-bom升版本
     * bom已完成找料，bom中物料需求被回复，对应需求中物料被替换成新回复sku。原版本状态不变，bom版本+1，状态为【已提交】
     */
    private Long moreMatchNewVersionHandle(HouliuDemandMatchPushDto demandMatchDto,
                                           BomMaterialDemand materialDemand,
                                           BomOrder bomOrder,
                                           List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfos,
                                           CommoditySupplierInfoVo supplierInfo,
                                           BomMaterialInfoDto materialInfoDto) {
        //开发bom状态为【已提交】、【已核算】，bom中物料需求被回复，对应需求中物料被替换成新回复sku。
        // 原版本状态不变，bom版本+1，状态为【已提交】 --v3.11  汶俊
        if (!(Objects.equals(bomOrder.getState(), BomOrderStateEnum.SUBMITTED.getCode()) || Objects.equals(bomOrder.getState(), BomOrderStateEnum.CALCULATED.getCode()))){
            throw new SdpDesignException("bom单不是已提交或已核算状态, 不能自动升版本");
        }
        BomOrderMaterial oldMaterial = bomOrderMaterialRepository.getById(materialDemand.getLatestBomMaterialId());
        Long oldSnapshotId = oldMaterial.getMaterialSnapshotId();

        //1, 先把当前bom升版本
        BomOrder newBomOrder = bomOperateService.copyNewVersionBom(bomOrder, true, bomOrder.getVersionNum() + 1, BomOrderStateEnum.SUBMITTED, new BomCopyInfoDto());
        //记录日志
        DesignLogReq designLogReq = BomOrderConverter.buildBomOrderLog(newBomOrder, "匹配回复更新");
        designLogService.create(designLogReq);

        Long newBomOrderId = newBomOrder.getBomId();

        //2, 根据物料快照id,找出新bom中对应的物料
        BomOrderMaterial bomOrderMaterial = bomOrderMaterialRepository.getListByBomId(newBomOrderId).stream()
                .filter(item -> Objects.equals(item.getMaterialSnapshotId(), oldSnapshotId))
                .findFirst().orElse(null);
        SdpDesignException.notNull(bomOrderMaterial, "新版本Bom对应的物料不存在, 物料快照id:{}; bomId:{}", oldSnapshotId, newBomOrderId);

        //3, 创建物料快照
        Long newSnapshotId = this.createMaterialSnapshot(demandMatchDto, bomOrderMaterial, newBomOrder, supplierInfo, materialInfoDto);

        //4, 将新bom物料的snapshotId进行替换;
        BomOrderMaterial updateMaterial = new BomOrderMaterial();
        updateMaterial.setBomMaterialId(bomOrderMaterial.getBomMaterialId());
        updateMaterial.setMaterialSnapshotId(newSnapshotId);
        //价格信息
        this.resetPriceInfo(demandMatchDto, materialInfoDto, updateMaterial);

        this.updateBomMaterial(updateMaterial);

        //5, 需求管理最新物料,更新匹配数量
        BomMaterialDemand updateDemand = new BomMaterialDemand();
        updateDemand.setBomMaterialDemandId(bomOrderMaterial.getBomMaterialDemandId());
        updateDemand.setMaterialMatchNum(materialDemand.getMaterialMatchNum() + 1 );
        bomMaterialDemandRepository.updateById(updateDemand);

        //6, 如果有暂存,同步更新暂存表(新增暂存物料关联原物料id,更新暂存需求)
        this.matchMoreSyncTransient(bomOrderMaterial.getBomMaterialDemandId(), newBomOrder, bomOrderMaterial, demandMatchDto, materialInfoDto, newSnapshotId);

        //7, 封装物料更新MQ消息体
        this.buildUpdateInfoNewVersion(demandMatchDto, materialDemand, oldSnapshotId, newSnapshotId, materialUpdateInfos);

        //bom推送致景
        bomOperateService.pushBom2Zj(newBomOrder, PushZjTypeEnum.BOM_SUBMIT);

        //同步信息到推款-生产资料
        this.syncBom2product(newBomOrder);

        return bomOrderMaterial.getBomMaterialId();
    }

    private void buildUpdateInfoNewVersion(HouliuDemandMatchPushDto demandMatchDto,
                                           BomMaterialDemand materialDemand,
                                           Long oldSnapshotId,
                                           Long newSnapshotId,
                                           List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfos) {

        MaterialSnapshot oldSnapshot = materialSnapshotRepository.getById(oldSnapshotId);

        BomMaterialUpdateDto.MaterialUpdateInfo updateInfo = new BomMaterialUpdateDto.MaterialUpdateInfo();
        //要传供应链的需求id
        updateInfo.setDemandId(materialDemand.getSupplyChainDemandId());
        updateInfo.setNewMaterialSnapShotId(newSnapshotId);
        updateInfo.setNewSkuId(demandMatchDto.getSkuId());
        updateInfo.setNewCommodityId(demandMatchDto.getCommodityId());

        updateInfo.setOldMaterialSnapShotId(oldSnapshotId);
        updateInfo.setOldSkuId(oldSnapshot.getSkuId());
        updateInfo.setOldCommodityId(oldSnapshot.getCommodityId());
        materialUpdateInfos.add(updateInfo);

    }


    /**
     * 需求非第一个匹配回复处理
     */
    private Long moreMatchHandle(HouliuDemandMatchPushDto demandMatchDto,
                                 BomMaterialDemand materialDemand,
                                 BomOrder bomOrder,
                                 List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfos,
                                 CommoditySupplierInfoVo supplierInfo,
                                 BomMaterialInfoDto materialInfoDto) {

        BomOrderMaterial latestMaterial = bomOrderMaterialRepository.getById(materialDemand.getLatestBomMaterialId());
        //1, 创建物料快照
        Long newSnapshotId = this.createMaterialSnapshot(demandMatchDto, latestMaterial, bomOrder, supplierInfo, materialInfoDto);
        SdpDesignException.notNull(latestMaterial, "需求下关联的物料不存在! supplyDemandId:{}; bomDemandId:{}; latestBomMaterialId:{}",
                materialDemand.getBomMaterialDemandId(), materialDemand.getLatestBomMaterialId());

        //2, 将物料的snapshotId进行替换;
        BomOrderMaterial updateMaterial = new BomOrderMaterial();
        updateMaterial.setBomMaterialId(latestMaterial.getBomMaterialId());
        updateMaterial.setMaterialSnapshotId(newSnapshotId);
        //价格信息
        this.resetPriceInfo(demandMatchDto, materialInfoDto, updateMaterial);

        this.updateBomMaterial(updateMaterial);

        //3, 需求管理最新物料,更新匹配数量,
        BomMaterialDemand updateDemand = new BomMaterialDemand();
        updateDemand.setBomMaterialDemandId(materialDemand.getBomMaterialDemandId());
        updateDemand.setMaterialMatchNum(materialDemand.getMaterialMatchNum() + 1 );
        bomMaterialDemandRepository.updateById(updateDemand);

        //4, 如果有暂存,同步更新暂存表(新增暂存物料关联原物料id,更新暂存需求)
        this.matchMoreSyncTransient(materialDemand.getBomMaterialDemandId(), bomOrder, latestMaterial, demandMatchDto, materialInfoDto, newSnapshotId);

        //5, 封装物料更新MQ消息体
        this.buildUpdateInfoMoreMatch(demandMatchDto, latestMaterial, newSnapshotId, materialUpdateInfos, materialDemand);

        return latestMaterial.getBomMaterialId();
    }

    private void buildUpdateInfoMoreMatch(HouliuDemandMatchPushDto demandMatchDto,
                                          BomOrderMaterial latestMaterial,
                                          Long newSnapshotId,
                                          List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfos,
                                          BomMaterialDemand materialDemand) {
        Long oldSnapshotId = latestMaterial.getMaterialSnapshotId();
        MaterialSnapshot oldSnapshot = materialSnapshotRepository.getById(oldSnapshotId);

        BomMaterialUpdateDto.MaterialUpdateInfo updateInfo = new BomMaterialUpdateDto.MaterialUpdateInfo();
        //要传供应链的需求id
        updateInfo.setDemandId(materialDemand.getSupplyChainDemandId());
        updateInfo.setNewMaterialSnapShotId(newSnapshotId);
        updateInfo.setNewSkuId(demandMatchDto.getSkuId());
        updateInfo.setNewCommodityId(demandMatchDto.getCommodityId());

        updateInfo.setOldMaterialSnapShotId(oldSnapshotId);
        updateInfo.setOldSkuId(oldSnapshot.getSkuId());
        updateInfo.setOldCommodityId(oldSnapshot.getCommodityId());
        materialUpdateInfos.add(updateInfo);
    }

    private void matchOnShelfHandle(BomMaterialDemand materialDemand, BomOrder bomOrder) {
        //更新需求匹配数量
        BomMaterialDemand updateDemand = new BomMaterialDemand();
        updateDemand.setBomMaterialDemandId(materialDemand.getBomMaterialDemandId());
        updateDemand.setMaterialMatchNum(materialDemand.getMaterialMatchNum() + 1);
        bomMaterialDemandRepository.updateById(updateDemand);

        //如果有暂存,同步更新暂存表
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomOrder.getBomId());
        BomMaterialDemandTransient transientDemand = bomMaterialDemandTransientRepository.getByBomTransientIdAndOriginDemandId(transientBom.getBomTransientId(), materialDemand.getBomMaterialDemandId());
        if (Objects.nonNull(transientDemand)) {
            BomMaterialDemandTransient updateDemandTransient = new BomMaterialDemandTransient();
            updateDemandTransient.setBomMaterialDemandId(materialDemand.getBomMaterialDemandId());
            updateDemandTransient.setMaterialSearchState(Bool.NO.getCode());
            updateDemandTransient.setMaterialMatchNum(transientDemand.getMaterialMatchNum() + 1);
            bomMaterialDemandTransientRepository.updateById(updateDemandTransient);
        }
    }

    /**
     * 匹配回复同步暂存表
     * 新增暂存物料关联原物料id,更新暂存需求
     */
    private void matchMoreSyncTransient(Long originDemandId,
                                        BomOrder bomOrder,
                                        BomOrderMaterial laterMaterial,
                                        HouliuDemandMatchPushDto demandMatchDto,
                                        BomMaterialInfoDto materialInfoDto,
                                        Long newSnapshotId) {
        //1, 如果bom有暂存, 同步到暂存表
        if (!Objects.equals(bomOrder.getTransientState(), Bool.YES.getCode())) {
            return;
        }
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomOrder.getBomId());
        if (Objects.isNull(transientBom)) {
            return;
        }

        //2, 查询暂需求 更新暂存需求(匹配数量)
        BomMaterialDemandTransient transientDemand = bomMaterialDemandTransientRepository.getByBomTransientIdAndOriginDemandId(transientBom.getBomTransientId(), originDemandId);
        if (Objects.nonNull(transientDemand)) {
            BomMaterialDemandTransient updateDemandTransient = new BomMaterialDemandTransient();
            updateDemandTransient.setBomMaterialDemandId(transientDemand.getBomMaterialDemandId());
            updateDemandTransient.setMaterialMatchNum(transientDemand.getMaterialMatchNum() + 1 );
            bomMaterialDemandTransientRepository.updateById(updateDemandTransient);

            BomOrderMaterialTransient materialTransient = bomOrderMaterialTransientRepository.getByDemandIdAndSnapshotId(transientDemand.getBomMaterialDemandId(), laterMaterial.getMaterialSnapshotId());

            //3, 更新暂存物料的snapshotId
            if (Objects.nonNull(materialTransient)) {
                BomOrderMaterialTransient updateTransientMaterial = new BomOrderMaterialTransient();
                updateTransientMaterial.setBomMaterialId(materialTransient.getBomMaterialId());
                updateTransientMaterial.setMaterialSnapshotId(newSnapshotId);
                //设置价格信息
                this.resetPriceInfoTransient(demandMatchDto, materialInfoDto, updateTransientMaterial);

                this.updateBomMaterialTransient(updateTransientMaterial);
            }
        }



    }

    /**
     * 辅料需求匹配首次回复时, 同步到暂存表
     */
    private void matchFirstTimeSyncTransient(BomMaterialDemand materialDemand,
                                             BomOrder bomOrder,
                                             Long materialSnapshotId,
                                             HouliuDemandMatchPushDto demandMatchDto,
                                             BomMaterialInfoDto materialInfoDto) {
        //如果bom有暂存, 同步到暂存表
        if (!Objects.equals(bomOrder.getTransientState(), Bool.YES.getCode())) {
            return;
        }
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomOrder.getBomId());
        if (Objects.isNull(transientBom)) {
            return;
        }
        //暂存需求
        BomMaterialDemandTransient transientDemand = bomMaterialDemandTransientRepository.getByBomTransientIdAndOriginDemandId(transientBom.getBomTransientId(), materialDemand.getBomMaterialDemandId());
        //暂存物料
        BomOrderMaterialTransient searchMaterialTransient = this.getSearchingMaterialTransient(transientDemand);

        //更新暂存物料
        if (Objects.nonNull(searchMaterialTransient)) {
            BomOrderMaterialTransient updateMaterialTransient = new BomOrderMaterialTransient();
            updateMaterialTransient.setBomMaterialId(searchMaterialTransient.getBomMaterialId());
            updateMaterialTransient.setMaterialSnapshotId(materialSnapshotId);
            updateMaterialTransient.setMaterialState(BomMaterialStateEnum.NORMAL.getCode());
            //设置价格信息
            this.resetPriceInfoTransient(demandMatchDto, materialInfoDto, updateMaterialTransient);

            this.updateBomMaterialTransient(updateMaterialTransient);
        }
        //更新暂存需求
        if (Objects.nonNull(transientDemand)) {
            BomMaterialDemandTransient updateDemandTransient = new BomMaterialDemandTransient();
            updateDemandTransient.setBomMaterialDemandId(transientDemand.getBomMaterialDemandId());
            updateDemandTransient.setMaterialSearchState(Bool.NO.getCode());
            updateDemandTransient.setMaterialMatchNum(1);
            updateDemandTransient.setLatestBomMaterialId(searchMaterialTransient.getBomMaterialId());
            bomMaterialDemandTransientRepository.updateById(updateDemandTransient);
        }
    }


    private void updateBomMaterialTransient(BomOrderMaterialTransient updateMaterial) {
        if (Objects.isNull(updateMaterial)) {
            return;
        }
        bomOrderMaterialTransientRepository.lambdaUpdate()
                .set(Objects.nonNull(updateMaterial.getMaterialState()), BomOrderMaterialTransient::getMaterialState, updateMaterial.getMaterialState())
                .set(BomOrderMaterialTransient::getMaterialSnapshotId, updateMaterial.getMaterialSnapshotId())
                .set(BomOrderMaterialTransient::getMinPrice, updateMaterial.getMinPrice())
                .set(BomOrderMaterialTransient::getPriceInvalidTime, updateMaterial.getPriceInvalidTime())
                .set(BomOrderMaterialTransient::getSamplePurchasingCycle, updateMaterial.getSamplePurchasingCycle())
                .set(BomOrderMaterialTransient::getBulkPurchasingCycle, updateMaterial.getBulkPurchasingCycle())
                .set(BomOrderMaterialTransient::getRevisedTime, LocalDateTime.now())
                .eq(BomOrderMaterialTransient::getBomMaterialId, updateMaterial.getBomMaterialId())
                .update();
    }

    private BomOrderMaterial getSearchingMaterial(BomMaterialDemand materialDemand) {
        BomOrderMaterial searchMaterial = null;
        try {
            searchMaterial = bomOrderMaterialRepository.getSearchingOne(materialDemand.getBomMaterialDemandId());
        } catch (Exception e) {
            throw new SdpDesignException("需求下物料信息异常, 存在多个找料中的物料! materialDemandId:{}" + materialDemand.getBomMaterialDemandId());
        } return searchMaterial;
    }

    private BomOrderMaterialTransient getSearchingMaterialTransient(BomMaterialDemandTransient materialDemandTransient) {
        BomOrderMaterialTransient searchMaterial = null;
        try {
            searchMaterial = bomOrderMaterialTransientRepository.getSearchingOne(materialDemandTransient.getBomMaterialDemandId());
        } catch (Exception e) {
            throw new SdpDesignException("需求下物料信息异常, 存在多个找料中的暂存物料! materialDemandId:{}" + materialDemandTransient.getBomMaterialDemandId());
        } return searchMaterial;
    }

    private Long createMaterialSnapshot(HouliuDemandMatchPushDto demandMatchDto,
                                        BomOrderMaterial bomOrderMaterial,
                                        BomOrder bomOrder,
                                        CommoditySupplierInfoVo supplierInfo,
                                        BomMaterialInfoDto materialInfoDto) {
        //根据匹配回复创建物料快照
        Long matchId = demandMatchDto.getMatchId();
        String skuCode = demandMatchDto.getSkuCode();
        Long skuId = demandMatchDto.getSkuId();
        SdpDesignException.notNull(bomOrderMaterial, "匹配回复-物料快照创建失败, 物料对象为null! skuCode:{}; matchId:{}", skuCode, matchId);

        //物料信息已经校验过了
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = materialInfoDto.getAccessoriesSpuMap();
        Map<Long, CategoryTreeMapVo> categoryTreeMap = materialInfoDto.getCategoryTreeMap();

        ProductSkuVo productSkuVo = accessoriesSkuMap.get(skuId);
        ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(productSkuVo.getSpuId());
        MaterialSnapshotCreateReq snapshotCreateReq = BomOrderMaterialConverter.buildAccessoriesMaterialSnapshotV3(bomOrderMaterial.getBomMaterialId(), productSkuVo, productSpuInfoVo, categoryTreeMap);
        snapshotCreateReq.setMatchId(matchId);
        snapshotCreateReq.setMatchType(MaterialMatchTypeEnum.HAO_LIAO.getCode());
        snapshotCreateReq.setDemandId(demandMatchDto.getDemandId());

        //设置供应商合作关系(履约的开票状态)
        CommoditySupplierExtVo supplierExtVo = supplierInfo.getExt();
        if (Objects.nonNull(supplierExtVo)) {
            snapshotCreateReq.setInvoiceState(supplierExtVo.getInvoiceState());
        }

        Long materialSnapshotId = materialSnapshotService.create(snapshotCreateReq);

        //物料快照推送履约
        List<MaterialDetailReq> supplyChainMaterialReqs = new LinkedList<>();
        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        MaterialSnapshot materialSnapshot = materialSnapshotRepository.getById(materialSnapshotId);
        MaterialDetailReq materialDetailReq = this.buildSupplyChainMaterialReq(prototype, bomOrderMaterial, materialSnapshot);
        supplyChainMaterialReqs.add(materialDetailReq);

        //bom提交同步至商品服务
        // demandRemoteHelper.createMaterialToSupplyChain(supplyChainMaterialReqs);
        return materialSnapshotId;
    }


    private MaterialDetailReq buildSupplyChainMaterialReq(Prototype prototype, BomOrderMaterial bomOrderMaterial, MaterialSnapshot materialSnapshot) {
        MaterialDetailReq materialDetailReq = new MaterialDetailReq();
        materialDetailReq.setMaterialSnapshotId(materialSnapshot.getMaterialSnapshotId());
        materialDetailReq.setSkuId(materialSnapshot.getSkuId());
        materialDetailReq.setSpuId(materialSnapshot.getCommodityId());
        materialDetailReq.setSpuCode(materialSnapshot.getCommodityCode());
        materialDetailReq.setCategoryName(materialSnapshot.getCategoryName());
        materialDetailReq.setDemandTag(bomOrderMaterial.getPrototypeMaterialName());
        materialDetailReq.setCommodityType(materialSnapshot.getCommodityType());
        materialDetailReq.setDemandChannel(DemandChannelEnum.DESIGNER_CHOOSE_MATERIAL_NO_DEMAND.getCode());
        materialDetailReq.setBusinessType(BusinessTypeEnum.PROTOTYPE.getCode());
        materialDetailReq.setStyleCode(prototype.getStyleCode());
        materialDetailReq.setDesignCode(prototype.getDesignCode());
        return materialDetailReq;
    }

}
