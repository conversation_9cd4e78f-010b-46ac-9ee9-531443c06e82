package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.clothes.vo.req.DesignCodeListReq;
import tech.tiangong.sdp.clothes.vo.req.audit.SampleAuditPictureReq;
import tech.tiangong.sdp.clothes.vo.resp.audit.SampleAuditPictureVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.CheckPriceBaseInfoInnerVo;
import tech.tiangong.sdp.design.converter.ClothingDesignConverter;
import tech.tiangong.sdp.design.entity.DesignStyle;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.entity.PrototypeDetail;
import tech.tiangong.sdp.design.enums.DesignStyleSourceTypeEnum;
import tech.tiangong.sdp.design.enums.SampleTypeEnum;
import tech.tiangong.sdp.design.remote.CheckPriceRemoteHelper;
import tech.tiangong.sdp.design.remote.InspSampleAuditRemoteHelper;
import tech.tiangong.sdp.design.repository.DesignStyleRepository;
import tech.tiangong.sdp.design.repository.PrototypeDetailRepository;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.service.BomOrderService;
import tech.tiangong.sdp.design.service.ClothingDesignService;
import tech.tiangong.sdp.design.vo.base.prototype.OpsObject;
import tech.tiangong.sdp.design.vo.req.DesignCodeReq;
import tech.tiangong.sdp.design.vo.req.clothingDesign.*;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderDetailVo;
import tech.tiangong.sdp.design.vo.resp.clothingDesign.*;
import tech.tiangong.sdp.design.vo.resp.clothingDesign.prototype.ClothingDesignPrototypeDetailInfoVo;
import tech.tiangong.sdp.design.vo.resp.clothingDesign.prototype.ClothingDesignPrototypeDetailVo;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * PLM设计-公共接口
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ClothingDesignServiceImpl implements ClothingDesignService {

    private final DesignStyleRepository designStyleRepository;
    private final PrototypeRepository prototypeRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final BomOrderService bomOrderService;
    private final CheckPriceRemoteHelper checkPriceRemoteHelper;
    private final InspSampleAuditRemoteHelper inspSampleAuditRemoteHelper;

    /**
     * 分页数量最大
     */
    private static final int PAGE_SIZE_LIMIT_MAX = 1000;
    @Override
    public PageRespVo<ClothingDesignStyleListVo> querySpuList(ClothingDesignStyleListQuery query) {
        if (Objects.nonNull(query.getPageSize()) && query.getPageSize() > PAGE_SIZE_LIMIT_MAX){
            query.setPageSize(PAGE_SIZE_LIMIT_MAX);
        }
        IPage<DesignStyle> page = designStyleRepository.findPageForCommon(query);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), Collections.EMPTY_LIST);
        }

        List<String> styleCodes = page.getRecords().stream().map(DesignStyle::getStyleCode).collect(Collectors.toList());

        List<Prototype> prototypes = prototypeRepository.getByStyleCodes(styleCodes);
        Map<Long, PrototypeDetail> prototypeDetailMap = prototypeDetailRepository.getListByPrototypeIds(prototypes.stream().map(Prototype::getPrototypeId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(PrototypeDetail::getPrototypeId, Function.identity(), (k1, k2) -> k1));
        //spu旗下的skc
        Map<String, List<ClothingDesignPrototypeVo>> multimap = prototypes.stream().map(prototype -> {
            if (!query.getQueryAllPrototype() && Boolean.TRUE.equals(prototype.getIsCanceled())) {
                return null;
            }
            ClothingDesignPrototypeVo clothingDesignPrototypeVo = new ClothingDesignPrototypeVo();
            ClothingDesignConverter.buildClothingDesignPrototypeVo(clothingDesignPrototypeVo, prototypeDetailMap, prototype);
            return clothingDesignPrototypeVo;
        }).filter(Objects::nonNull).collect(Collectors.groupingBy(ClothingDesignPrototypeVo::getStyleCode));


        List<ClothingDesignStyleListVo> clothingDesignStyleListVos = page.getRecords().stream().map(designStyle -> {
            //SPU的信息
            ClothingDesignStyleDetailListVo styleVo = new ClothingDesignStyleDetailListVo();
            BeanUtils.copyProperties(designStyle, styleVo);
            // styleVo.setCustomerPictureList(StrUtil.splitTrim(designStyle.getCustomerPicture(),StrUtil.COMMA));

            ClothingDesignStyleListVo designStyleListVo = new ClothingDesignStyleListVo();
            designStyleListVo.setStyleDetailListVo(styleVo);
            //放skc列表
            designStyleListVo.setPrototypes(multimap.get(designStyle.getStyleCode()));
            return designStyleListVo;
        }).collect(Collectors.toList());

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), clothingDesignStyleListVos);
    }

    @Override
    public ClothingDesignStyleDetailVo querySpuDetail(ClothingDesignStyleDetailQuery query) {
        return designStyleRepository.lambdaQuery().eq(DesignStyle::getStyleCode, query.getStyleCode()).oneOpt().map(designStyle -> {
            ClothingDesignStyleDetailVo clothingDesignStyleDetailVo = new ClothingDesignStyleDetailVo();
            BeanUtils.copyProperties(designStyle, clothingDesignStyleDetailVo);
            // clothingDesignStyleDetailVo.setCustomerPictureList(StrUtil.splitTrim(designStyle.getCustomerPicture(),StrUtil.COMMA));
            return clothingDesignStyleDetailVo;
        }).orElse(null);
    }

    @Override
    public PageRespVo<ClothingDesignPrototypeListVo> querySkcList(ClothingDesignPrototypeListQuery query) {
        ClothingDesignPrototypeDetailBatchQuery clothingDesignPrototypeDetailBatchQuery = new ClothingDesignPrototypeDetailBatchQuery();
        BeanUtils.copyProperties(query, clothingDesignPrototypeDetailBatchQuery);
        PageRespVo<ClothingDesignPrototypeDetailInfoVo> detailPage = queryBatchSkcDetail(clothingDesignPrototypeDetailBatchQuery);

        List<ClothingDesignPrototypeListVo> collect = detailPage.getList().stream().map(clothingDesignPrototypeDetailInfoVo -> {
            ClothingDesignPrototypeListVo clothingDesignPrototypeListVo = new ClothingDesignPrototypeListVo();
            BeanUtils.copyProperties(clothingDesignPrototypeDetailInfoVo.getPrototypeDetail(), clothingDesignPrototypeListVo);
            return clothingDesignPrototypeListVo;
        }).collect(Collectors.toList());

        return new PageRespVo<>(detailPage.getPage(), detailPage.getTotal(), collect);
    }

    @Override
    public PageRespVo<ClothingDesignPrototypeDetailInfoVo> queryBatchSkcDetail(ClothingDesignPrototypeDetailBatchQuery query) {
        if (Objects.nonNull(query.getPageSize()) && query.getPageSize() > PAGE_SIZE_LIMIT_MAX){
            query.setPageSize(PAGE_SIZE_LIMIT_MAX);
        }

        IPage<ClothingDesignPrototypeDetailVo> page = prototypeRepository.findPageForCommon(query);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), Collections.EMPTY_LIST);
        }

        List<ClothingDesignPrototypeDetailInfoVo> prototypeDetailInfoVos = page.getRecords().stream().map(vo -> {
            vo.setCustomerPicture(StrUtil.splitTrim(vo.getCustomerPictureStr(),StrUtil.COMMA));
            vo.setDesignPicture(StrUtil.splitTrim(vo.getDesignPictureStr(),StrUtil.COMMA));
            //设置是否改款标识
            buildIsChange(vo);
            vo.setStyleSeasonList(JSON.parseArray(vo.getStyleSeason(), OpsObject.class));

            ClothingDesignPrototypeDetailInfoVo clothingDesignPrototypeDetailInfoVo = new ClothingDesignPrototypeDetailInfoVo();
            clothingDesignPrototypeDetailInfoVo.setPrototypeDetail(vo);
            return clothingDesignPrototypeDetailInfoVo;
        }).collect(Collectors.toList());

        List<String> designCodeList = page.getRecords().stream().map(ClothingDesignPrototypeDetailVo::getDesignCode).collect(Collectors.toList());
        if (Boolean.TRUE.equals(query.getIsQueryBom())) {
            //查询bom
            List<BomOrderDetailVo> boms = bomOrderService.getLatestBomOrderDetailList4Inner(new DesignCodeReq().setDesignCodeList(designCodeList));
            ClothingDesignConverter.buildBatchSkcDetailBom(boms, prototypeDetailInfoVos);
        }

        if (Boolean.TRUE.equals(query.getIsQueryCheckPrice())) {
            //查询核价
            List<CheckPriceBaseInfoInnerVo> checkPrices = checkPriceRemoteHelper.findLatestCheckedBaseInfoBySkcBatch(new DesignCodeListReq().setDesignCodeList(designCodeList));
            ClothingDesignConverter.buildBatchSkcDetailCheckPrice(checkPrices, prototypeDetailInfoVos);
        }

        if (Boolean.TRUE.equals(query.getIsQueryAudit())) {
            //查询联合审版
            List<SampleAuditPictureVo> sampleAuditPictureVos = inspSampleAuditRemoteHelper.pictureInfoList(new SampleAuditPictureReq(designCodeList));
            ClothingDesignConverter.buildAudit(sampleAuditPictureVos,prototypeDetailInfoVos);
        }


        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), prototypeDetailInfoVos);
    }

    @Override
    public ClothingDesignPrototypeDetailInfoVo querySkcDetail(ClothingDesignPrototypeDetailQuery query) {
        ClothingDesignPrototypeDetailBatchQuery batchQuery = new ClothingDesignPrototypeDetailBatchQuery();
        BeanUtils.copyProperties(query,batchQuery);
        batchQuery.setDesignCode(Lists.newArrayList(query.getDesignCode()));
        batchQuery.setPageSize(1);
        batchQuery.setPageNum(1);
      return  queryBatchSkcDetail(batchQuery).getList().stream().findFirst().orElse(null);
    }

    @Override
    public PageRespVo<ClothingDesignBaseSkcVo> queryBaseSkc(ClothingDesignSkcBaseQuery query) {
        // if (Objects.nonNull(query.getPageSize()) && query.getPageSize() > PAGE_SIZE_LIMIT_MAX){
        //     query.setPageSize(PAGE_SIZE_LIMIT_MAX);
        // }

        Page<ClothingDesignBaseSkcVo> page = PageHelper.startPage(query.getPageNum(), query.getPageSize())
                .doSelectPage(() -> prototypeRepository.findBaseSkc(query));
        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getResult())) {
            return PageRespVoHelper.empty();
        }

        return PageRespVoHelper.of(page.getPageNum(), page.getTotal(), page.getResult());
    }

    private void buildIsChange(ClothingDesignPrototypeDetailVo vo){
        vo.setIsChange(Bool.NO.getCode());
        //1. 改款: CRM改款或设计款改款,且打版类型 != 复色打版;
        //2. 非改款: 自建款
        if((DesignStyleSourceTypeEnum.CRM_SPU_SOURCE.getCode().equals(vo.getSourceType())
                || DesignStyleSourceTypeEnum.DESIGN_SPU_SOURCE.getCode().equals(vo.getSourceType()))
            && !SampleTypeEnum.COMPOUND_COLORS_MAKING.getCode().equals(vo.getSampleType())){
            vo.setIsChange(Bool.YES.getCode());
        }
    }
}
