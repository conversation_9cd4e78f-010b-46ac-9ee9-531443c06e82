package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.base.entity.BaseEntity;
import cn.yibuyun.framework.bean.Beans;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.Json;
import cn.yibuyun.framework.util.PageRespVoHelper;
import cn.yibuyun.framework.util.StringConstants;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.RichTextStringData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yibuyun.framework2.craft.bean.CraftDemandAuditCallbackDto;
import com.yibuyun.scm.common.dto.accessories.response.*;
import com.yibuyun.scm.common.dto.fabric.response.CommoditySkuCollectionRespVo;
import com.yibuyun.scm.common.dto.houliu.response.DemandDetailResp;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierDetailRespVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierExtVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierInfoVo;
import com.yibuyun.scm.common.dto.supplier.response.SupplierPersonnelVo;
import com.yibuyun.scm.common.enums.fabric.CategoryNo1Enum;
import com.yibuyun.scm.common.enums.fabric.DefaultSalesRegionEnum;
import com.yibuyun.scm.common.enums.fabric.SkuCombinationTypeEnum;
import com.yibuyun.scm.common.enums.supplier.SupplierPersonnelTypeEnum;
import com.zjkj.scf.bundle.common.dto.demand.dto.response.CraftDemandMatchDetailDto;
import com.zjkj.scf.bundle.common.dto.demand.dto.response.CraftDemandRegistrationInfoDto;
import com.zjkj.scf.bundle.common.dto.demand.dto.response.CraftSampleTaskInnerInfoResp;
import com.zjkj.scf.bundle.common.dto.demand.enums.CommodityTypeEnum;
import com.zjkj.scf.bundle.common.dto.demand.enums.DemandCraftsRequireEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.converter.BomOrderConverter;
import tech.tiangong.sdp.design.converter.BomOrderMaterialConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.*;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.dto.bom.BomOrderListDto;
import tech.tiangong.sdp.design.vo.dto.bom.BomOrderStateStatisticsDto;
import tech.tiangong.sdp.design.vo.dto.material.BomOrderMaterialInfoSummariseDto;
import tech.tiangong.sdp.design.vo.query.bom.BomMaterialExportQuery;
import tech.tiangong.sdp.design.vo.query.bom.BomOrderListQuery;
import tech.tiangong.sdp.design.vo.query.bom.CraftDemandQuery;
import tech.tiangong.sdp.design.vo.req.*;
import tech.tiangong.sdp.design.vo.req.bom.*;
import tech.tiangong.sdp.design.vo.req.log.DesignLogBizListReq;
import tech.tiangong.sdp.design.vo.req.log.DesignLogReq;
import tech.tiangong.sdp.design.vo.req.material.OppositeColorMaterialReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;
import tech.tiangong.sdp.design.vo.resp.bom.*;
import tech.tiangong.sdp.design.vo.resp.log.DesignLogVO;
import tech.tiangong.sdp.design.vo.resp.material.*;
import tech.tiangong.sdp.design.vo.resp.zj.design.PrototypeOrderMaterialOpenResp;
import tech.tiangong.sdp.material.pojo.dto.DesignerDTO;
import tech.tiangong.sdp.material.pojo.web.request.designer.DesignerRemoteReq;
import tech.tiangong.sdp.material.sdk.service.remote.DesignerRemoteService;
import tech.tiangong.sdp.utils.BusinessCodeGenerator;
import tech.tiangong.sdp.utils.CodeRuleEnum;
import tech.tiangong.sdp.utils.StreamUtil;
import tech.tiangong.sdp.utils.YbfUrlUtil;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Bom 服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/13 14:58
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BomOrderServiceImpl implements BomOrderService {

    private static final int CORE_NUM = Runtime.getRuntime().availableProcessors();
    private static final ThreadPoolExecutor THREAD_POOL = ExecutorBuilder.create()
            .setCorePoolSize(CORE_NUM * 2)
            .setMaxPoolSize(CORE_NUM * 2 + 12)
            .setWorkQueue(new LinkedBlockingQueue<>(100))
            .setKeepAliveTime(60, TimeUnit.SECONDS)
            .setHandler(new ThreadPoolExecutor.CallerRunsPolicy())
            .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("bom-").build())
            .build();

    private final BusinessCodeGenerator businessCodeGenerator;
    private final BomOrderRepository bomOrderRepository;
    private final BomOrderTransientRepository bomOrderTransientRepository;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final PrototypeHistoryRepository prototypeHistoryRepository;
    private final PrototypeRepository prototypeRepository;
    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final DesignRemarksRepository designRemarksRepository;
    private final DesignRemarksService designRemarksService;
    private final DesignLogService designLogService;
    private final MqProducer mqProducer;
    private final DemandRemoteHelper demandRemoteHelper;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final SupplierInfoRemoteHelper supplierInfoRemoteHelper;
    private final SpecialAccessoriesRepository specialAccessoriesRepository;
    private final DesignerRemoteService designerRemoteService;
    private final ProductRemoteHelper productRemoteHelper;
    private final MaterialSnapshotService materialSnapshotService;
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final DesignStyleRepository designStyleRepository;
    private final DesignDemandRepository designDemandRepository;
    private final BomMaterialDemandService bomMaterialDemandService;

    private final CraftDemandInfoService craftDemandInfoService ;
    private final MaterialPurchaseFollowRepository materialPurchaseFollowRepository;
    private final BomMaterialDemandRepository bomMaterialDemandRepository;
    private final DesignDemandSuggestedMaterialRepository designDemandSuggestedMaterialRepository;
    private MaterialPurchaseFollowService materialPurchaseFollowService;


    @Autowired
    public void setMaterialPurchaseFollowService(@Lazy MaterialPurchaseFollowService materialPurchaseFollowService) {
        this.materialPurchaseFollowService = materialPurchaseFollowService;
    }

	@Override
	public PageRespVo<BomOrderListResp> page(BomOrderListReq req) {
		BomOrderListQuery bomOrderListQuery = new BomOrderListQuery();
        List<CraftDemandInfo> craftDemandInfoList = new ArrayList<>();
		BeanUtils.copyProperties(req, bomOrderListQuery);
		bomOrderListQuery.setBomOrderState(Optional.ofNullable(req.getBomOrderState()).map(BomOrderStateEnum::getCode).orElse(null));
		UserContent userContent = UserContentHolder.get();
        //根据设计组查询
        if(Objects.equals(req.getClothesDesigner(), Bool.YES.getCode())){
            List<String> designerGroupCodeList = this.queryDesignerGroup(req, userContent);
            if (CollUtil.isEmpty(designerGroupCodeList)) {
                // 若用户不属于任意设计组则展示空数据
                return PageRespVoHelper.empty();
            }
            bomOrderListQuery.setDesignerGroupCodeList(designerGroupCodeList);
        }

		Page<BomOrderListDto> page = PageHelper.startPage(req.getPageNum(), req.getPageSize())
				.doSelectPage(() -> bomOrderRepository.pageQuery(bomOrderListQuery));
		if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getResult())) {
			return PageRespVoHelper.empty();
		}

        List<BomOrderListDto> bomOrderDtoList = page.getResult();

        //获取skc最新的拆板单
        List<String> designCodeList = StreamUtil.convertListAndDistinct(bomOrderDtoList, BomOrderListDto::getDesignCode);
        List<Long> bomIds = StreamUtil.convertListAndDistinct(bomOrderDtoList, BomOrderListDto::getBomId);
        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
        Map<String, Prototype> prototypeMap = StreamUtil.list2Map(prototypeList, Prototype::getDesignCode);

        List<CraftDemandInfoVo> craftDemandInfoForDemolitions = craftDemandInfoService.getListByBomIdsAndState(bomIds, CraftDemandStateEnum.SUBMIT.getCode());
        craftDemandInfoList = craftDemandInfoForDemolitions.stream()
                .map(craftDemandInfoForDemolition -> {
                    CraftDemandInfo craftDemandInfo = new CraftDemandInfo();
                    BeanUtils.copyProperties(craftDemandInfoForDemolition, craftDemandInfo);
                    return craftDemandInfo;
                }).collect(Collectors.toList());

        List<BomOrderListResp> resp = new ArrayList<>();

        //查询款式图片: 取最新skc维护的
        Map<String, PrototypeDetail> prototypeDetailMap = this.getPrototypeDetailMap(prototypeList);

        //SPU信息
        List<String> styleCodeList = StreamUtil.convertListAndDistinct(prototypeList, Prototype::getStyleCode);
        List<DesignStyle> styleList = designStyleRepository.listByStyleCodes(styleCodeList);
        Map<String, DesignStyle> styleMap = StreamUtil.list2Map(styleList, DesignStyle::getStyleCode);

        resp = BomOrderConverter.newPage(bomOrderDtoList,craftDemandInfoList, prototypeDetailMap, prototypeMap, styleMap);
        return PageRespVoHelper.of(page.getPageNum(), page.getTotal(), resp);
    }

    private List<String> queryDesignerGroup(BomOrderListReq queryDTO, UserContent userContent) {
        if(Objects.equals(queryDTO.getClothesDesigner(), Bool.YES.getCode())){
            if(ObjectUtil.isEmpty(userContent)){
                // 若用户不属于任意设计组则展示空数据
                return List.of();
            }
            DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
            designerRemoteReq.setDesignerId(String.valueOf(userContent.getCurrentUserId()));
            DataResponse<List<DesignerDTO>> listDataResponse = designerRemoteService.designerInfoList(designerRemoteReq);
            if(listDataResponse.isSuccessful() && CollectionUtil.isNotEmpty(listDataResponse.getData())){
                List<DesignerDTO> data = listDataResponse.getData();
                List<String> designerGroupCodeList = data.stream().map(DesignerDTO::getDesignerGroupCode).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(designerGroupCodeList)) {
                    return designerGroupCodeList;
                }
            }
        }
        return List.of();
    }

    private Map<String, PrototypeDetail> getPrototypeDetailMap(List<Prototype> prototypeList) {
        if (CollUtil.isEmpty(prototypeList)) {
            return Collections.emptyMap();
        }
        Map<String, Prototype> prototypeMap = prototypeList.stream().collect(Collectors.toMap(Prototype::getDesignCode, Function.identity(), (k1, k2) -> k1));

        List<Long> prototypeIdList = prototypeList.stream().map(Prototype::getPrototypeId).collect(Collectors.toList());
        Map<Long, PrototypeDetail> detailMap = prototypeDetailRepository.getListByPrototypeIds(prototypeIdList)
                .stream().collect(Collectors.toMap(PrototypeDetail::getPrototypeId, Function.identity(), (k1, k2) -> k1));

        Map<String, PrototypeDetail> prototypeDetailMap = new HashMap<>(prototypeList.size());
        prototypeMap.forEach((key, value) -> {
            PrototypeDetail prototypeDetail = detailMap.get(value.getPrototypeId());
            if (Objects.nonNull(prototypeDetail)) {
                prototypeDetailMap.put(key, prototypeDetail);
            }
        });
        return prototypeDetailMap;
    }

    @Override
    public BomOrderDetailResp detail(Long bomId) throws Exception {
        BomOrder bomOrder = bomOrderRepository.getEntityByBomId(bomId);
        if (Objects.isNull(bomOrder)) {
            return null;
        }

        //公共信息
        BomOrderMaterialInfoSummariseDto materialInfoSummariseDto = queryCommonInfo(bomOrder, false);

        List<BomOrderMaterial> bomOrderMaterialList = materialInfoSummariseDto.getBomOrderMaterialList();

        //若bom无物料且是详情页查询, 返回基本信息
        if (CollectionUtil.isEmpty(bomOrderMaterialList)) {
            return getBomBasicInfo(bomOrder);
        }

        //SPU与SKC信息
        Prototype prototype = materialInfoSummariseDto.getPrototype();
        SdpDesignException.notNull(prototype, "设计款查找失败! ");
        DesignStyle designStyle = designStyleRepository.getByStyleCode(prototype.getStyleCode());
        SdpDesignException.notNull(designStyle, "SPU查找失败! ");

        //bom物料其他信息(获取最新skc的信息)
        CompletableFuture<PrototypeDetail> prototypeDetailFuture = CompletableFuture.supplyAsync(() ->
                prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId()), THREAD_POOL);
        CompletableFuture.allOf(prototypeDetailFuture).join();

		BomOrderDetailResp detailResp = BomOrderConverter.detail(bomOrder, bomOrderMaterialList, designStyle, prototype,
				prototypeDetailFuture.get(), materialInfoSummariseDto.getMaterialSnapshotList(), materialInfoSummariseDto.getCraftDemandInfoList(),
				materialInfoSummariseDto.getDesignRemarksList(), materialInfoSummariseDto.getPurchaseApplyFollowCountVOS(),
				materialInfoSummariseDto.getBomMaterialDemandVos(), materialInfoSummariseDto.getConfirmCraftMatchMap(), productRemoteHelper);

        Boolean isDisplayUpdateBomButton = handlerBomUpdateButton(bomOrder, prototype);
        detailResp.setIsDisplayUpdateBomButton(isDisplayUpdateBomButton);

        detailResp.setLatestColor(prototype.getColor());

        List<BomOrderMaterialVo> bomOrderMaterials = detailResp.getBomOrderMaterialList();
        purchaseColorCardPicture(bomOrderMaterials);

        //补充特殊辅料信息
        List<BomOrderMaterialVo> specialAccessoriesMaterials = getSpecialAccessoriesInfo(bomId, materialInfoSummariseDto.getDesignRemarksList());
        detailResp.getBomOrderMaterialList().addAll(specialAccessoriesMaterials);

        //Bom历史版本
        detailResp.setBomOrderHistoryVersionList(getBomOrderHistoryVersion(bomOrder.getBomCode()));

        //查询需求信息
        //物料名称与id映射
        Map<String, Long> materialNameIdMap = bomOrderMaterialList.stream()
                .collect(Collectors.toMap(BomOrderMaterial::getPrototypeMaterialName, BomOrderMaterial::getBomMaterialId, (k1, k2) -> k1));
        this.resetDemandInfo(detailResp.getMaterialDemandList(), materialNameIdMap);

        //spu店铺平台信息
        detailResp.setPlatformName(designStyle.getPlatformName());

        return detailResp;
    }

    private void setChosenMaterial(BomOrderDetailResp detailResp, Integer detailType) {
        if (Objects.isNull(detailResp)) {
            return;
        }
        //正常款skc
        String designCode = detailResp.getDesignCode();
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        if (!Objects.equals(prototype.getSkcType(), SkcTypeEnum.NORMAL.getCode())) {
            return;
        }

        //选中物料信息
        Long designDemandId = detailResp.getDesignDemandId();
        if (Objects.nonNull(designDemandId)
                && Objects.equals(detailResp.getBomOrderState(), BomOrderStateEnum.WAIT_SUBMIT)
                && Objects.equals(detailType, Bool.YES.getCode())

        ) {
            DesignDemandSuggestedMaterial chosenMaterial = designDemandSuggestedMaterialRepository.getChosen(designDemandId);
            if (Objects.nonNull(chosenMaterial)) {
                BomOrderDetailResp.SpuSkuIdReq spuSkuIdReq = new BomOrderDetailResp.SpuSkuIdReq();
                spuSkuIdReq.setSpuId(chosenMaterial.getSpuId());
                spuSkuIdReq.setSkuId(chosenMaterial.getSkuId());

                detailResp.setFabricSpuSkuList(List.of(spuSkuIdReq));
            }
        }
    }

    private void resetDemandInfo(List<BomMaterialDemandVo> materialDemandList, Map<String, Long> materialNameIdMap) {
        if (CollUtil.isEmpty(materialDemandList)) {
            return;
        }
        //设置对色关联物料id
        materialDemandList.forEach(item -> {
            String colorMatchMaterialName = item.getColorMatchMaterialName();
            if (StringUtils.isNotBlank(colorMatchMaterialName) && Objects.nonNull(materialNameIdMap.get(colorMatchMaterialName))) {
                item.setColorMatchMaterialId(materialNameIdMap.get(colorMatchMaterialName));
            }
        });

        //查询需求处理人信息: 未匹配的需求也会有处理人信息
        Set<Long> supplyDemandIdSet = materialDemandList.stream()
                .filter(item -> Objects.nonNull(item.getSupplyChainDemandId()))
                .map(BomMaterialDemandVo::getSupplyChainDemandId)
                .collect(Collectors.toSet());

        //避免需求处理人信息查询影响整个bom详情展示, catch一下
        try {
            Map<Long, DemandDetailResp> supplyDemandMap = demandRemoteHelper.batchQueryDemand(supplyDemandIdSet).stream()
                    .collect(Collectors.toMap(DemandDetailResp::getDemandId, Function.identity(), (k1, k2) -> k1));
            if (CollUtil.isEmpty(supplyDemandMap)) {
                return;
            }
            materialDemandList.forEach(item -> {
                DemandDetailResp demandResp = supplyDemandMap.get(item.getSupplyChainDemandId());
                if (Objects.isNull(demandResp)) {
                    return;
                }
                item.setDemandHandlerName(demandResp.getHandlerName());
            });
        } catch (Exception e) {
            log.error("=== 查询需求信息异常 ===", e);
        }
    }

    private BomOrderMaterialInfoSummariseDto queryCommonInfo(BomOrder bomOrder, boolean queryCraftRegistration) throws ExecutionException, InterruptedException {
        Long bomId = bomOrder.getBomId();
        //信息汇总
        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        List<BomOrderMaterial> bomOrderMaterialList = bomOrderMaterialRepository.getListByBomId(bomId);
        List<Long> materialSnapshotIds = bomOrderMaterialList.stream().map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());

        CompletableFuture<List<MaterialSnapshotVo>> materialSnapshotListFuture = CompletableFuture.supplyAsync(() ->
                materialSnapshotService.listByIds(materialSnapshotIds), THREAD_POOL);
        CompletableFuture<List<BomMaterialDemandVo>> bomMaterialDemandFuture = CompletableFuture.supplyAsync(() ->
                bomMaterialDemandService.listByBomId(bomId), THREAD_POOL);
        CompletableFuture<List<CraftDemandInfo>> craftDemandInfoListFuture = CompletableFuture.supplyAsync(() ->
                craftDemandInfoRepository.getListByBomIdsAndState(List.of(bomId), CraftDemandStateEnum.SUBMIT.getCode()), THREAD_POOL);
        CompletableFuture<List<DesignRemarks>> designRemarkListFuture = CompletableFuture.supplyAsync(() ->
                designRemarksRepository.getListByBizId(bomId), THREAD_POOL);

        CompletableFuture.allOf(craftDemandInfoListFuture, bomMaterialDemandFuture, materialSnapshotListFuture, designRemarkListFuture).join();

        // 根据materialSnapshotIds集合批量查询匹配的采购次数
        List<PurchaseApplyFollowCountVO> purchaseApplyFollowCountVOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(materialSnapshotIds)) {
            Set<Long> materialSnapshotIdSet = Sets.newHashSet(materialSnapshotIds);
            purchaseApplyFollowCountVOS = materialPurchaseFollowService.purchaseCountByMaterialSnapshotIds(materialSnapshotIdSet);
        }

		//履约工艺登记信息
		List<CraftDemandRegistrationInfoDto> demandRegistrationInfoList = Collections.emptyList();
		if (queryCraftRegistration) {
			List<Long> supplyChainCraftDemandIds = craftDemandInfoListFuture.get().stream().map(CraftDemandInfo::getThirdPartyCraftDemandId).collect(Collectors.toList());
			demandRegistrationInfoList = demandRemoteHelper.getRegistrationInfo(supplyChainCraftDemandIds);
		}
		//若是最新版本bom,查询工艺匹配信息
		BomOrder latestBom = bomOrderRepository.getLatestBomByDesignCode(bomOrder.getDesignCode());
		Map<Long, CraftDemandMatchDetailDto.CraftDemandMatchDetail> confirmCraftMatchMap = new HashMap<>(32);
		Boolean latestBomFlag = Boolean.FALSE;
		if (Objects.equals(latestBom.getBomId(), bomOrder.getBomId())) {
			latestBomFlag = Boolean.TRUE;
			List<Long> supplyChainCraftDemandIds = craftDemandInfoListFuture.get().stream().map(CraftDemandInfo::getThirdPartyCraftDemandId).collect(Collectors.toList());
			List<CraftDemandMatchDetailDto.CraftDemandMatchDetail> craftConfirmMatchList = demandRemoteHelper.getCraftMatchConfirmList(supplyChainCraftDemandIds);
			if (CollUtil.isNotEmpty(craftConfirmMatchList)) {
				Map<Long, CraftDemandMatchDetailDto.CraftDemandMatchDetail> demandMatchMap = craftConfirmMatchList.stream()
						.collect(Collectors.toMap(CraftDemandMatchDetailDto.CraftDemandMatchDetail::getCraftDemandId, Function.identity(), (k1, k2) -> k1));
				supplyChainCraftDemandIds.forEach(supplyDemandId -> {
					//只要已确认的匹配
					CraftDemandMatchDetailDto.CraftDemandMatchDetail craftDemandMatchDetail = demandMatchMap.get(supplyDemandId);
					if (Objects.nonNull(craftDemandMatchDetail)) {
						confirmCraftMatchMap.put(supplyDemandId, craftDemandMatchDetail);
					}
				});
			}
		}

		return BomOrderMaterialInfoSummariseDto.builder()
				.bomOrder(bomOrder)
				.latestBomFlag(latestBomFlag)
				.prototype(prototype)
				.bomOrderMaterialList(bomOrderMaterialList)
				.bomMaterialDemandVos(bomMaterialDemandFuture.get())
				.materialSnapshotList(materialSnapshotListFuture.get())
				.craftDemandInfoList(craftDemandInfoListFuture.get())
				.designRemarksList(designRemarkListFuture.get())
				.demandRegistrationInfoList(demandRegistrationInfoList)
				.purchaseApplyFollowCountVOS(purchaseApplyFollowCountVOS)
				.confirmCraftMatchMap(confirmCraftMatchMap)
				.build();
	}

	@Override
	public BomOrderDetailResp webDetail(BomWebDetailReq req) throws Exception {
		//归档详情
        BomOrderDetailResp detailResp = this.detail(req.getBomId());
        //最新bom查询最新的价格与采购周期信息
        if (Objects.nonNull(detailResp)) {
            BomOrder latestBom = bomOrderRepository.getLatestBomByDesignCode(detailResp.getDesignCode());
            if (Objects.equals(latestBom.getBomId(), req.getBomId())) {
                this.resetLatestPriceInfo(detailResp.getBomOrderMaterialList());
                this.resetDemandMaterialPrice(detailResp.getMaterialDemandList());
            }
        }

        //查询选料
        this.setChosenMaterial(detailResp, req.getDetailType());

        return detailResp;
        // if (Objects.equals(Bool.NO.getCode(), req.getDetailType())) {
		// }
        //
        // //编辑页详情(要查暂存的信息, 只查最新版本的)  V3.3 编辑页查询最新的物料价格和价格失效时间
        // BomOrder bomOrder = bomOrderRepository.getEntityByBomId(req.getBomId());
        // SdpDesignException.notNull(bomOrder, "bom单不存在! ");
        // BomOrder latestBomOrder = bomOrderRepository.getLatestBomByDesignCode(bomOrder.getDesignCode());
        // if (Objects.equals(Bool.YES.getCode(), req.getDetailType()) && Objects.equals(latestBomOrder.getBomId(), req.getBomId())) {
        //     //暂存详情
        //     return this.getTransientDetailV3(req.getBomId());
        // }
        // return null;
    }

    /**
     * 获取BOM单基本信息
     *
     */
    private BomOrderDetailResp getBomBasicInfo(BomOrder bomOrder) {

        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        // PrototypeHistory prototypeHistory = prototypeHistoryRepository.getByPrototypeId(bomOrder.getPrototypeId());
        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId());
        DesignStyle designStyle = designStyleRepository.getByStyleCode(prototype.getStyleCode());

        //bom详情spu与skc信息取最新的
        BomOrderDetailResp detailResp = BomOrderConverter.assembleBasicInfo(bomOrder, designStyle, prototype, prototypeDetail);

        Boolean isDisplayUpdateBomButton = handlerBomUpdateButton(bomOrder, prototype);
        detailResp.setIsDisplayUpdateBomButton(isDisplayUpdateBomButton);

        detailResp.setLatestColor(prototype.getColor());
        detailResp.setLatestPrototypeId(prototype.getPrototypeId());

        //补充特殊辅料信息
        List<DesignRemarks> designRemarkList = designRemarksRepository.getListByBizId(bomOrder.getBomId());
        List<BomOrderMaterialVo> specialAccessoriesMaterials = getSpecialAccessoriesInfo(bomOrder.getBomId(), designRemarkList);
        detailResp.setBomOrderMaterialList(specialAccessoriesMaterials);

        //Bom历史版本
        detailResp.setBomOrderHistoryVersionList(getBomOrderHistoryVersion(bomOrder.getBomCode()));

        //spu店铺平台信息
        detailResp.setPlatformName(designStyle.getPlatformName());

        return detailResp;
    }

    private List<BomOrderDetailResp.BomOrderHistoryVersion> getBomOrderHistoryVersion(String bomCode) {
        List<BomOrder> bomOrderList = bomOrderRepository.getListByBomCode(bomCode);
        return bomOrderList.stream().sorted(Comparator.comparing(BomOrder::getVersionNum).reversed()).map(bomOrder -> {
            BomOrderDetailResp.BomOrderHistoryVersion bomOrderHistoryVersion = new BomOrderDetailResp.BomOrderHistoryVersion();
            bomOrderHistoryVersion.setBomId(bomOrder.getBomId());
            bomOrderHistoryVersion.setBomCode(bomOrder.getBomCode());
            bomOrderHistoryVersion.setBomVersionNum(bomOrder.getVersionNum());
            bomOrderHistoryVersion.setBomOrderState(BomOrderStateEnum.findEntityByCode(bomOrder.getState()));
            bomOrderHistoryVersion.setPrototypeId(bomOrder.getPrototypeId());
            return bomOrderHistoryVersion;
        }).collect(Collectors.toList());
    }

    private List<BomOrderMaterialVo> getSpecialAccessoriesInfo(Long bomId, List<DesignRemarks> designRemarksList) {
        List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.getListByBomId(bomId);
        if (CollectionUtil.isEmpty(specialAccessoriesList)) {
            return Collections.emptyList();
        }

        Map<Long, List<DesignRemarks>> designRemarkMap = designRemarksList.stream().filter(remark -> Objects.nonNull(remark.getBizChildId()))
                .collect(Collectors.groupingBy(DesignRemarks::getBizChildId));

        List<BomOrderMaterialVo> orderMaterialVoList = specialAccessoriesList.stream().filter(special -> Objects.equals(special.getState(), SpecialAccessoriesStateEnum.SUBMIT.getCode()))
                .map(special -> {
                    BomOrderMaterialVo bomOrderMaterialVo = new BomOrderMaterialVo();
                    BeanUtils.copyProperties(special, bomOrderMaterialVo);
                    bomOrderMaterialVo.setBomMaterialId(special.getSpecialAccessoriesId());
                    bomOrderMaterialVo.setPrototypeMaterialName(special.getName());

                    //特殊辅料备注
                    List<DesignRemarks> specialRemarksList = designRemarkMap.get(special.getSpecialAccessoriesId());
                    if (CollectionUtil.isNotEmpty(specialRemarksList)) {
                        List<MaterialRemarkVo> materialRemarkList = specialRemarksList.stream().sorted(Comparator.comparing(DesignRemarks::getCreatedTime).reversed())
                                .map(remark -> {
                                    MaterialRemarkVo materialRemark = new MaterialRemarkVo();
                                    BeanUtils.copyProperties(remark, materialRemark);
                                    return materialRemark;
                                }).collect(Collectors.toList());
                        bomOrderMaterialVo.setMaterialRemarkList(materialRemarkList);
                    }

                    bomOrderMaterialVo.setBomMaterialType(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode());
                    bomOrderMaterialVo.setDemandType(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode());
                    bomOrderMaterialVo.setCommodityType(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.name());
                    bomOrderMaterialVo.setCommodityName(special.getSpuName());
                    bomOrderMaterialVo.setCommodityId(special.getSpuId());
                    if (StringUtils.isNotBlank(special.getSkuPicture())) {
                        bomOrderMaterialVo.setMatchPictureList(Arrays.stream(special.getSkuPicture().split(",")).collect(Collectors.toList()));
                    }
                    bomOrderMaterialVo.setCommodityCode(special.getSpuCode());
                    //大货进价取最小价格单位
                    bomOrderMaterialVo.setBulkPurchasePrice(special.getMinPrice());
                    bomOrderMaterialVo.setBulkPurchasePriceUnit(special.getMinPriceUnit());

                    return bomOrderMaterialVo;
                }).sorted(Comparator.comparing(BomOrderMaterialVo::getPrototypeMaterialName)).collect(Collectors.toList());

        return orderMaterialVoList;
    }


    /**
     * 处理bom修改按钮是否显示
     *
     */
    private Boolean handlerBomUpdateButton(BomOrder bomOrder, Prototype prototype) {

        //版单关闭,不能再进行修改
        if (prototype.getIsCanceled()) {
            return Boolean.FALSE;
        }
        BomOrder latestBomOrder = bomOrderRepository.getLatestBomByDesignCode(bomOrder.getDesignCode());
        return Objects.equals(latestBomOrder.getBomId(), bomOrder.getBomId()) ? Boolean.TRUE : Boolean.FALSE;
    }

    /**
     * bom更新异步通知资源库服务
     *
     */
    @Async(value = "asyncTaskExecutor")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void asyncInformResourceBomUpdate(Long bomId, UserContent userContent) {

        try {
            //睡3秒是为了bom更新数据入库,再进行查询,否则可以会出现bomId查询不到
            TimeUnit.SECONDS.sleep(10L);
            UserContentHolder.set(userContent);

            BomOrderDetailResp detail = detail(bomId);
            List<DesignLogVO> designLogVOList = designLogService.dateBizList(DesignLogBizListReq.builder().bizId(bomId).build());
            List<BomOrderDetailExtVo.OperationLog> operationLogList = designLogVOList.stream().map(log ->
                    BomOrderDetailExtVo.OperationLog.builder().content(log.getContent()).operatorId(log.getCreatorId())
                            .operatorName(log.getCreatorName()).operationTime(log.getCreatedTime()).build())
                    .collect(Collectors.toList());
            BomOrderDetailExtVo orderDetailExtVo = new BomOrderDetailExtVo();
            BeanUtils.copyProperties(detail, orderDetailExtVo);
            orderDetailExtVo.setOperationLogList(operationLogList);

            MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.BOM_UPDATE_SYNC_RESOURCE,
                    DesignMqConstant.SDP_DESIGN_BOM_ORDER_UPDATE_EXCHANGE, DesignMqConstant.SDP_DESIGN_BOM_ORDER_UPDATE_ROUTING_KEY,
                    JSON.toJSONString(orderDetailExtVo));

            //发送消息
            mqProducer.sendOnAfterCommit(mqMessageReq);
        } catch (Exception e) {
            log.error("【bom更新异步通知资源库服务】异常", e);
        }
    }

    @Override
    public BomOrderPrintVo print(Long bomId) throws Exception {
        BomOrder bomOrder = bomOrderRepository.getEntityByBomId(bomId);
        if (Objects.isNull(bomOrder)) {
            return null;
        }

        //SPU与SKC信息
        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        SdpDesignException.notNull(prototype, "设计款查找失败! ");
        DesignStyle designStyle = designStyleRepository.getByStyleCode(prototype.getStyleCode());
        SdpDesignException.notNull(designStyle, "SPU查找失败! ");

        // PrototypeHistory prototypeHistory = prototypeHistoryRepository.getByPrototypeId(bomOrder.getPrototypeId());

        CompletableFuture<PrototypeDetail> prototypeDetailFuture = CompletableFuture.supplyAsync(() ->
                prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId()), THREAD_POOL);
        CompletableFuture<List<CraftDemandInfo>> craftDemandInfoListFuture = CompletableFuture.supplyAsync(() ->
                craftDemandInfoRepository.getListByBomIdsAndState(List.of(bomId), CraftDemandStateEnum.SUBMIT.getCode()), THREAD_POOL);
        CompletableFuture<List<BomOrderMaterial>> bomOrderMaterialListFuture = CompletableFuture.supplyAsync(() ->
                bomOrderMaterialRepository.getListByBomId(bomId), THREAD_POOL);
        CompletableFuture<List<DesignRemarks>> designRemarkListFuture = CompletableFuture.supplyAsync(() -> designRemarksRepository.getListByBizId(bomId), THREAD_POOL);

        CompletableFuture<List<PrototypeOrderMaterialOpenResp>> orderMaterialFuture = CompletableFuture.supplyAsync(() ->
                zjDesignRemoteHelper.findLatestMaterial(Collections.singletonList(prototype.getDesignCode()), true), THREAD_POOL);

        CompletableFuture.allOf(prototypeDetailFuture, craftDemandInfoListFuture, bomOrderMaterialListFuture,
                designRemarkListFuture, orderMaterialFuture).join();

		//若是最新版本bom,查询工艺匹配信息
		BomOrder latestBom = bomOrderRepository.getLatestBomByDesignCode(bomOrder.getDesignCode());
		Map<Long, CraftDemandMatchDetailDto.CraftDemandMatchDetail> confirmCraftMatchMap = new HashMap<>(32);
		Boolean latestBomFlag = Boolean.FALSE;
		if (Objects.equals(latestBom.getBomId(), bomOrder.getBomId())) {
			latestBomFlag = Boolean.TRUE;
			List<Long> supplyChainCraftDemandIds = craftDemandInfoListFuture.get().stream().map(CraftDemandInfo::getThirdPartyCraftDemandId).collect(Collectors.toList());
			List<CraftDemandMatchDetailDto.CraftDemandMatchDetail> craftConfirmMatchList = demandRemoteHelper.getCraftMatchConfirmList(supplyChainCraftDemandIds);
			if (CollUtil.isNotEmpty(craftConfirmMatchList)) {
				Map<Long, CraftDemandMatchDetailDto.CraftDemandMatchDetail> demandMatchMap = craftConfirmMatchList.stream()
						.collect(Collectors.toMap(CraftDemandMatchDetailDto.CraftDemandMatchDetail::getCraftDemandId, Function.identity(), (k1, k2) -> k1));
				supplyChainCraftDemandIds.forEach(supplyDemandId -> {
					//只要已确认的匹配
					CraftDemandMatchDetailDto.CraftDemandMatchDetail craftDemandMatchDetail = demandMatchMap.get(supplyDemandId);
					if (Objects.nonNull(craftDemandMatchDetail)) {
						confirmCraftMatchMap.put(supplyDemandId, craftDemandMatchDetail);
					}
				});
			}
		}

		List<Long> materialSnapshotIds = bomOrderMaterialListFuture.get().stream().map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
		List<MaterialSnapshotVo> materialSnapshotList = materialSnapshotService.listByIds(materialSnapshotIds);
		BomOrderPrintVo printResp = BomOrderConverter.print(bomOrder, bomOrderMaterialListFuture.get(), materialSnapshotList,
				prototype, prototypeDetailFuture.get(), craftDemandInfoListFuture.get(), designRemarkListFuture.get(), confirmCraftMatchMap);
		printResp.setStorageLocation(CollectionUtil.isNotEmpty(orderMaterialFuture.get()) ? orderMaterialFuture.get().getFirst().getStorageLocation() : "");

		//特殊辅料信息
		List<BomOrderMaterialVo> specialAccessoriesInfo = getSpecialAccessoriesInfo(bomOrder.getBomId(), designRemarkListFuture.get());
		printResp.getBomOrderMaterialList().addAll(specialAccessoriesInfo);

		//最新bom查询最新的价格与采购周期信息
		if (latestBomFlag) {
			this.resetLatestPriceInfo(printResp.getBomOrderMaterialList());
		}else {
		    //面料企划料信息
		    this.resetFabricPlaningInfo(printResp.getBomOrderMaterialList());
        }

		return printResp;
	}

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void closeBomWithPrototype(BomCloseWithPrototypeReq req) {
        log.info("取消设计款号下的工艺需求 请求参数:{}", JSON.toJSONString(req));
        SdpDesignException.notNull(req.getPrototypeId(), "设计款id为空! ");
        SdpDesignException.notNull(req.getDesignCode(), "取消bom单,设计款号为空! ");
        //bom单更新为取消
        List<BomOrder> bomOrderList = bomOrderRepository.getListByDesignCodes(Collections.singletonList(req.getDesignCode()));
        if (CollUtil.isEmpty(bomOrderList)) {
            return;
        }
        bomOrderList.forEach(bomOrder -> {
            BomOrder bomOrderUpdate = BomOrder.builder().bomId(bomOrder.getBomId()).state(BomOrderStateEnum.CLOSED.getCode()).build();
            bomOrderRepository.updateById(bomOrderUpdate);
        });

        // 设计2.0 取消设计skcId,将该设计下的所有工艺都关闭
        // 取消设计款号已通知履约,履约会将该设计款号下的面料、辅料、工艺需求(含旗下的工艺任务)都取消, 找料需求也会取消。
        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getCraftDemandInfo(new CraftDemandQuery().setPrototypeId(req.getPrototypeId()));
        if (CollectionUtil.isEmpty(craftDemandInfoList)) {
            log.info("取消设计款号下的工艺需求 此设计款号 prototypeId:{} 下无工艺需求", req.getPrototypeId());
            return;
        }

        UserContent userContent = UserContentHolder.get();
        LocalDateTime now = LocalDateTime.now();
        List<CraftDemandInfo> updateCraftList = craftDemandInfoList.stream()
                .map(craft -> {
                    CraftDemandInfo updateCraft = new CraftDemandInfo();
                    updateCraft.setCraftDemandId(craft.getCraftDemandId());
                    updateCraft.setIsPrototypeCancel(Bool.YES.getCode());
                    updateCraft.setReviserName(userContent.getCurrentUserName());
                    updateCraft.setReviserId(userContent.getCurrentUserId());
                    updateCraft.setRevisedTime(now);
                    return updateCraft;
                }).collect(Collectors.toList());

        craftDemandInfoRepository.updateBatchById(updateCraftList);

        //不用单独取消bom下的找料需求, 取消skc的mq中履约会取消; 只需关闭需求状态
        this.updateDemandState(req);

    }

    private void updateDemandState(BomCloseWithPrototypeReq req) {
        String designCode = req.getDesignCode();
        //最新版本的bom
        BomOrder bomOrder = bomOrderRepository.getLatestBomByDesignCode(designCode);
        if (Objects.isNull(bomOrder)) {
            return;
        }

        //bom下的所有找料需求
        List<BomMaterialDemand> demandList = bomMaterialDemandRepository.listByBomId(bomOrder.getBomId());
        if (CollUtil.isEmpty(demandList)) {
            log.info("取消设计款号下的辅料需求 此设计款号: {}下无找料需求", designCode);
            return;
        }
        //将已提交的需求状态更新为PLM关闭
        List<BomMaterialDemand> updateDemandList = demandList.stream()
                .filter(item -> Objects.equals(item.getDemandState(), BomDemandStateEnum.SUBMIT.getCode()))
                .map(item -> {
                    BomMaterialDemand updateDemand = new BomMaterialDemand();
                    updateDemand.setBomMaterialDemandId(item.getBomMaterialDemandId());
                    updateDemand.setDemandState(BomDemandStateEnum.CLOSED_BY_PLM.getCode());
                    return updateDemand;
                }).collect(Collectors.toList());

        bomMaterialDemandRepository.updateBatchById(updateDemandList);

    }

    @Override
    public List<BomOrderStateStatisticsResp> bomStateStatistics(Long designerId, Integer clothesDesigner) {

        //旧有方法统计
        return oldBomStateStatistics(designerId, clothesDesigner);
    }

    /**
     * 旧的方式统计
     *
     */
    private List<BomOrderStateStatisticsResp> oldBomStateStatistics(Long designerId, Integer clothesDesigner) {
        List<String> designerGroupNameList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(clothesDesigner) && Objects.equals(clothesDesigner, Bool.YES.getCode())) {
            UserContent userContent = UserContentHolder.get();
            DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
            designerRemoteReq.setDesignerId(String.valueOf(userContent.getCurrentUserId()));
            DataResponse<List<DesignerDTO>> listDataResponse = designerRemoteService.designerInfoList(designerRemoteReq);
            if (listDataResponse.isSuccessful() && CollectionUtil.isNotEmpty(listDataResponse.getData())) {
                List<DesignerDTO> data = listDataResponse.getData();
                designerGroupNameList = data.stream().map(DesignerDTO::getDesignerGroupName).collect(Collectors.toList());
            } else {
                List<BomOrderStateEnum> bomOrderStateEnumList = Arrays.stream(BomOrderStateEnum.values()).filter(bomOrderStateEnum ->
                        !Objects.equals(bomOrderStateEnum, BomOrderStateEnum.UNKNOWN)).collect(Collectors.toList());
                return bomOrderStateEnumList.stream().map(bomOrderStateEnum -> {
                    BomOrderStateStatisticsResp statisticsResp = new BomOrderStateStatisticsResp();
                    statisticsResp.setBomOrderState(bomOrderStateEnum);
                    statisticsResp.setQuantity(0);
                    return statisticsResp;
                }).collect(Collectors.toList());
            }
        }

        List<BomOrderStateStatisticsDto> list = bomOrderRepository.bomStateStatistics(designerId, designerGroupNameList);
        //Bom状态不足，手动补上
        List<BomOrderStateEnum> bomOrderStateEnumList = Arrays.stream(BomOrderStateEnum.values()).filter(bomOrderStateEnum ->
                !Objects.equals(bomOrderStateEnum, BomOrderStateEnum.UNKNOWN)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return bomOrderStateEnumList.stream().map(bomOrderStateEnum -> {
                BomOrderStateStatisticsResp statisticsResp = new BomOrderStateStatisticsResp();
                statisticsResp.setBomOrderState(bomOrderStateEnum);
                statisticsResp.setQuantity(0);
                return statisticsResp;
            }).collect(Collectors.toList());
        }

        if (list.size() < bomOrderStateEnumList.size()) {
            Set<BomOrderStateEnum> bomOrderStateEnumQuery = list.stream().map(BomOrderStateStatisticsDto::getBomOrderState)
                    .map(BomOrderStateEnum::findEntityByCode).collect(Collectors.toSet());
            bomOrderStateEnumList.forEach(orderStateEnum -> {
                if (!bomOrderStateEnumQuery.contains(orderStateEnum)) {
                    BomOrderStateStatisticsDto newStatisticsDto = new BomOrderStateStatisticsDto();
                    newStatisticsDto.setBomOrderState(orderStateEnum.getCode());
                    newStatisticsDto.setQuantity(0);
                    list.add(newStatisticsDto);
                }
            });
        }

        return list.stream().map(bom -> {
            BomOrderStateStatisticsResp statisticsResp = new BomOrderStateStatisticsResp();
            statisticsResp.setBomOrderState(BomOrderStateEnum.findEntityByCode(bom.getBomOrderState()));
            statisticsResp.setQuantity(bom.getQuantity());
            return statisticsResp;
        }).collect(Collectors.toList());
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handlerCraftDemandCloseFail(CraftDemandAuditCallbackDto callbackDto) {
        Long craftDemandId = callbackDto.getCraftDemandId();
        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getListByThirdPartyCraftDemandIds(List.of(craftDemandId));
        if (CollectionUtil.isEmpty(craftDemandInfoList)) {
            log.info("【工艺需求关闭失败】此工艺不存在 thirdPartyCraftDemandId:{}", craftDemandId);
            return;
        }
        List<DesignRemarksReq> designRemarksReqList = craftDemandInfoList.stream().map(craft -> {
            DesignRemarksReq remarksReq = new DesignRemarksReq();
            remarksReq.setBizId(craft.getBomId());
            remarksReq.setBizType(DesignRemarksBizTypeEnum.BOM_ORDER.name());
            remarksReq.setBizChildId(craft.getBomMaterialId());
            String category = craft.getCategory1() + craft.getCategory2() + Optional.ofNullable(craft.getCategory3()).orElse("")
                    + DemandCraftsRequireEnum.getInfoByCode(craft.getCraftsRequire()).getDesc();
            remarksReq.setRemark("履约工艺需求关闭失败, 工艺类型:" + category + "原因:" + callbackDto.getRejectReason());
            return remarksReq;
        }).collect(Collectors.toList());

        designRemarksReqList.forEach(designRemarksService::create);

    }

    @Override
    public List<CraftDemandInfoVo> getLatestCraftInfoList(DesignCodeReq req, boolean noSearch) {
        List<Integer> submitStateList = BomOrderStateEnum.submitStateList().stream()
                .map(BomOrderStateEnum::getCode).collect(Collectors.toList());

        List<BomOrder> bomOrderList = bomOrderRepository.getLatestBomOrderList(req.getDesignCodeList(), submitStateList, noSearch);
        if (CollectionUtil.isEmpty(bomOrderList)) {
            log.info("【根据设计款号查询最新已提交的工艺信息】无Bom designCode:{}", JSON.toJSONString(req));
            return Collections.emptyList();
        }
        List<Long> bomIdList = bomOrderList.stream().map(BomOrder::getBomId).collect(Collectors.toList());

        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getListByBomIdsAndState(bomIdList, CraftDemandStateEnum.SUBMIT.getCode());
        if (CollectionUtil.isEmpty(craftDemandInfoList)) {
            log.info("【根据设计款号查询最新已提交的工艺信息】无工艺信息 designCode:{}", JSON.toJSONString(req));
            return Collections.emptyList();
        }

        Map<Long, BomOrder> bomOrderMap = bomOrderList.stream().collect(Collectors.toMap(BomOrder::getBomId, Function.identity(), (k1, k2) -> k1));
        return craftDemandInfoList.stream().map(craft -> {
            BomOrder bomOrder = bomOrderMap.get(craft.getBomId());
            if (Objects.nonNull(bomOrder) && Objects.equals(craft.getPrototypeId(), bomOrder.getPrototypeId())) {
                CraftDemandInfoVo craftDemandInfoVo = new CraftDemandInfoVo();
                BeanUtils.copyProperties(craft, craftDemandInfoVo);
                craftDemandInfoVo.setDesignCode(bomOrder.getDesignCode());
                return craftDemandInfoVo;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<BomOrderVo> getLatestBomOrder(DesignCodeReq req) {
        List<Integer> submitStateList = BomOrderStateEnum.submitStateList().stream()
                .map(BomOrderStateEnum::getCode).collect(Collectors.toList());

        List<BomOrder> bomOrderList = bomOrderRepository.getLatestBomOrderList(req.getDesignCodeList(), submitStateList, req.getNoSearch());
        if (CollectionUtil.isEmpty(bomOrderList)) {
            return Collections.emptyList();
        }

        return bomOrderList.stream().map(this::entity2Vo).collect(Collectors.toList());
    }

    private BomOrderVo entity2Vo(BomOrder bomOrder) {
        BomOrderVo bomOrderVo = new BomOrderVo();
        BeanUtils.copyProperties(bomOrder, bomOrderVo);
        bomOrderVo.setState(BomOrderStateEnum.findEntityByCode(bomOrder.getState()));
        return bomOrderVo;
    }

    @Override
    public BomOrderVo getBomById(Long bomId) {
        BomOrder bomOrder = bomOrderRepository.getById(bomId);
        if (Objects.isNull(bomOrder)) {
            return null;
        }
        return this.entity2Vo(bomOrder);
    }

    @Override
    public BomOrderVo getBomBySkc(String designCode) {
        BomOrder bomOrder = bomOrderRepository.getLatestByDesignCode(designCode);
        if (Objects.isNull(bomOrder)) {
            return null;
        }
        return this.entity2Vo(bomOrder);
    }

    @Override
    public BomOrderDetailVo getLatestBomOrderDetail(String designCode) throws Exception {
        BomOrder bomOrder = bomOrderRepository.getLatestSubmitBomByDesignCode(designCode);
        if (Objects.isNull(bomOrder)) {
            return null;
        }
        return getBomOrderDetailVo(bomOrder);
    }

    @Override
    public BomOrderDetailVo getLatestBomOrderDetailWithDemand(String designCode) throws Exception {
        BomOrder bomOrder = bomOrderRepository.getLatestSubmitBom(designCode);
        if (Objects.isNull(bomOrder)) {
            return null;
        }
        BomOrderDetailVo detailVo = this.getBomOrderDetailVo(bomOrder);
        if (Objects.isNull(detailVo)) {
            return detailVo;
        }
        List<BomMaterialDemandVo> materialDemandList = detailVo.getMaterialDemandList();
        if (CollUtil.isEmpty(materialDemandList)) {
            return detailVo;
        }

        //查询所有bom单下所有物料
        List<BomOrderMaterial> bomOrderMaterialList = bomOrderMaterialRepository.getListByBomId(bomOrder.getBomId());
        if (CollUtil.isEmpty(bomOrderMaterialList)) {
            return detailVo;
        }
        //物料名称与id映射
        Map<String, Long> materialNameIdMap = bomOrderMaterialList.stream()
                .collect(Collectors.toMap(BomOrderMaterial::getPrototypeMaterialName, BomOrderMaterial::getBomMaterialId, (k1, k2) -> k1));
        //对色信息与辅料信息
        this.resetDemandInfo(materialDemandList, materialNameIdMap);

        return detailVo;
    }

    @Override
    public BomOrderDetailVo getLatestBomOrderDetail4Inner(String designCode, Boolean noSearch) throws Exception {
        BomOrder bomOrder = null;
        if (noSearch) {
            bomOrder = bomOrderRepository.getLatestSubmitBomByDesignCode(designCode);
        } else {
            bomOrder = bomOrderRepository.getLatestSubmitBom(designCode);
        }
        if (Objects.isNull(bomOrder)) {
            return null;
        }
        return getBomOrderDetailVoInner(bomOrder);
    }

    @Override
    public BomOrderDetailVo getBomOrderDetailBySdk(Long bomId) throws Exception {
        BomOrder bomOrder = bomOrderRepository.getEntityByBomId(bomId);

        if (Objects.isNull(bomOrder)) {
            return null;
        }
        return getBomOrderDetailVoInner(bomOrder);
    }

    @Override
    public List<SpuContainBomInfoVo> getSpuContainBomInfo(String styleCode) {
        List<PrototypeHistory> prototypeHistoryList = prototypeHistoryRepository.getListByStyleCode(styleCode);
        if (CollectionUtil.isEmpty(prototypeHistoryList)) {
            return Collections.emptyList();
        }

        List<Long> prototypeIdList = prototypeHistoryList.stream().map(PrototypeHistory::getPrototypeId).collect(Collectors.toList());
        List<BomOrder> bomOrderList = bomOrderRepository.getListByPrototypeIds(prototypeIdList);
        List<SpuContainBomInfoVo> spuContainBomInfoVoList = new ArrayList<>();
        if (CollectionUtil.isEmpty(bomOrderList)) {
            spuContainBomInfoVoList = prototypeHistoryList.stream().map(prototypeHistory -> {
                SpuContainBomInfoVo spuContainBomInfoVo = new SpuContainBomInfoVo();
                BeanUtils.copyProperties(prototypeHistory, spuContainBomInfoVo);
                return spuContainBomInfoVo;
            }).collect(Collectors.toList());
        } else {
            Map<Long, List<BomOrder>> bomOrderMap = bomOrderList.stream().collect(Collectors.groupingBy(BomOrder::getPrototypeId));
            Set<Integer> bomStateSet = BomOrderStateEnum.submitStateList().stream().map(BomOrderStateEnum::getCode).collect(Collectors.toSet());

            spuContainBomInfoVoList = prototypeHistoryList.stream().map(prototypeHistory -> {
                SpuContainBomInfoVo spuContainBomInfoVo = new SpuContainBomInfoVo();
                BeanUtils.copyProperties(prototypeHistory, spuContainBomInfoVo);

                List<BomOrder> bomOrderList1 = bomOrderMap.get(prototypeHistory.getPrototypeId());
                if (CollectionUtil.isNotEmpty(bomOrderList1)) {
                    boolean isSubmitBom = bomOrderList1.stream().map(BomOrder::getState).anyMatch(bomStateSet::contains);
                    spuContainBomInfoVo.setIsSubmitBom(isSubmitBom);
                }
                return spuContainBomInfoVo;
            }).collect(Collectors.toList());
        }
        return spuContainBomInfoVoList;
    }

    @Override
    public List<BomOrderPrintVo> batchPrint(BomBatchPrintReq req) {
        if (!req.getIsPerformance()) {
            SdpDesignException.notEmpty(req.getBomIds(), "BomID不能为空");
            return req.getBomIds().parallelStream().map(bomId -> {
                try {
                    return print(bomId);
                } catch (Exception e) {
                    log.warn("【Bom批量打印出现异常】", e);
                }
                return null;
            }).collect(Collectors.toList());
        } else {
            SdpDesignException.notEmpty(req.getDesignCodeList(), "设计款号不能为空");
            DesignCodeReq designCodeReq = new DesignCodeReq();
            designCodeReq.setDesignCodeList(req.getDesignCodeList());
            return this.getBomOrderPrintListSdk(designCodeReq);
        }
    }


    /*
    public BomOrderDetailResp getTransientDetailV3(Long bomId) throws Exception {

        BomOrder bomOrder = bomOrderRepository.getEntityByBomId(bomId);

		//无暂存返回详情
		if (!Objects.equals(bomOrder.getTransientState(), Bool.YES.getCode())) {
			BomOrderDetailResp detailResp = this.detail(bomId);
			if (Objects.nonNull(detailResp)) {
				//编辑页 实时查询最新的物料信息 V3.3 编辑页查询最新的物料价格和价格失效时间
				this.resetLatestPriceInfo(detailResp.getBomOrderMaterialList());
				this.resetDemandMaterialPrice(detailResp.getMaterialDemandList());
			}
			return detailResp;
		}

        //获取暂存bom单
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomId);

        //先查询基础信息
        BomOrderDetailResp orderDetailResp = this.getBomBasicInfo(bomOrder);
        //引用款号使用暂存表中记录
        orderDetailResp.setQuoteDesignCode(transientBom.getQuoteDesignCode());

        //查询暂存表中的物料, 需求, 工艺 (过滤关闭的)
        //物料信息
        Long bomTransientId = transientBom.getBomTransientId();
        List<BomOrderMaterialTransient> materialTransientList = bomOrderMaterialTransientRepository.listByBomTransientId(bomTransientId).stream()
                .filter(item -> !Objects.equals(item.getMaterialState(), BomMaterialStateEnum.CLOSED.getCode()))
                .collect(Collectors.toList());
        //物料快照信息
        List<Long> materialSnapshotIds = materialTransientList.stream()
                .filter(item -> Objects.nonNull(item.getMaterialSnapshotId()))
                .map(BomOrderMaterialTransient::getMaterialSnapshotId).collect(Collectors.toList());
        List<MaterialSnapshotVo> materialSnapshotList = materialSnapshotService.listByIds(materialSnapshotIds);
        Map<Long, MaterialSnapshotVo> materialSnapshotMap = materialSnapshotList.stream().collect(Collectors.toMap(MaterialSnapshotVo::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));
        // 根据materialSnapshotIds集合批量查询匹配的采购次数
        List<PurchaseApplyFollowCountVO> purchaseApplyFollowCountVOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(materialSnapshotIds)) {
            Set<Long> materialSnapshotIdSet = Sets.newHashSet(materialSnapshotIds);
            purchaseApplyFollowCountVOS = materialPurchaseFollowService.purchaseCountByMaterialSnapshotIds(materialSnapshotIdSet);
        }
        Map<Long, PurchaseApplyFollowCountVO> purchaseApplyFollowCountMap = purchaseApplyFollowCountVOS.stream().collect(
                Collectors.toMap(PurchaseApplyFollowCountVO::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));

		//需求信息
		List<BomMaterialDemandTransientVo> materialDemandTransientVoList = bomMaterialDemandTransientService.listByBomTransientId(bomTransientId).stream()
				.filter(item -> !Objects.equals(item.getDemandState(), BomDemandStateEnum.CLOSED.getCode()))
				.collect(Collectors.toList());
		//工艺信息
		List<CraftDemandInfoTransient> craftList = craftDemandInfoTransientRepository.listByBomTransientId(bomTransientId).stream()
				.filter(item -> !Objects.equals(item.getState(), CraftDemandStateEnum.CLOSED.getCode()))
				.collect(Collectors.toList());
		//查询并设置最新的工艺匹配周期信息
		this.restLatestCraftCycle(craftList);

		Map<Long, List<CraftDemandInfoTransient>> craftDemandTransientMap = craftList.stream()
				.collect(Collectors.groupingBy(CraftDemandInfoTransient::getBomMaterialId));

        //备注信息
        List<DesignRemarks> designRemarksList = designRemarksRepository.getListByBizIdWithTransient(bomTransientId);
        Map<Long, List<DesignRemarks>> designRemarkMap = designRemarksList.stream()
                .filter(remark -> Objects.nonNull(remark.getBizChildId()))
                .collect(Collectors.groupingBy(DesignRemarks::getBizChildId));

        //商品图片获取
        Map<String, List<String>> commodityPicturesMap = BomOrderConverter.assembleCommodityPicturesShow(materialSnapshotList, productRemoteHelper);

        //设置物料信息
        List<BomOrderMaterialVo> allMaterialList = this.resetTransientBomMaterial(transientBom, materialTransientList,
                materialSnapshotMap, purchaseApplyFollowCountMap, craftDemandTransientMap, designRemarkMap, commodityPicturesMap);

        //编辑页 实时查询最新的物料信息 V3.3
        this.resetLatestPriceInfo(allMaterialList);

        //对选料物料与需求进行分类
        BomOrderConverter.classifyMaterialAndDemandTransient(materialDemandTransientVoList, allMaterialList, orderDetailResp);

        //补充特殊辅料信息
        List<BomOrderMaterialVo> specialAccessoriesMaterials = this.getSpecialAccessoriesTransient(bomTransientId, designRemarkMap);
        //实时查询最新的物料信息
        this.resetLatestPriceInfo(specialAccessoriesMaterials);
        orderDetailResp.getBomOrderMaterialList().addAll(specialAccessoriesMaterials);

        //需求信息处理
        //物料名称与id映射
        Map<String, Long> materialNameIdMap = materialTransientList.stream()
                .collect(Collectors.toMap(BomOrderMaterialTransient::getPrototypeMaterialName, BomOrderMaterialTransient::getBomMaterialId, (k1, k2) -> k1));
        this.resetDemandInfo(orderDetailResp.getMaterialDemandList(), materialNameIdMap);

        return orderDetailResp;
    }

     */

	private void restLatestCraftCycle(List<CraftDemandInfoTransient> craftList) {
		if (CollUtil.isEmpty(craftList)) {
			return;
		}
		//查询并设置最新的工艺匹配周期信息
		List<Long> supplyChainCraftDemandIds = craftList.stream().map(CraftDemandInfoTransient::getThirdPartyCraftDemandId).filter(Objects::nonNull).collect(Collectors.toList());
		Map<Long, CraftDemandMatchDetailDto.CraftDemandMatchDetail> demandMatchMap = demandRemoteHelper.getCraftMatchConfirmList(supplyChainCraftDemandIds).stream()
				.collect(Collectors.toMap(CraftDemandMatchDetailDto.CraftDemandMatchDetail::getCraftDemandId, Function.identity(), (k1, k2) -> k1));
		craftList.forEach(craftTransient -> {
			CraftDemandMatchDetailDto.CraftDemandMatchDetail craftDemandMatchDetail = demandMatchMap.get(craftTransient.getThirdPartyCraftDemandId());
			if (Objects.nonNull(craftDemandMatchDetail)) {
				craftTransient.setSampleCraftCycle(craftDemandMatchDetail.getSampleCraftCycle());
				craftTransient.setBulkCraftCycle(craftDemandMatchDetail.getBulkCraftCycle());
			}
		});
	}

	private void resetDemandMaterialPrice(List<BomMaterialDemandVo> demandList) {
		if (CollUtil.isEmpty(demandList)) {
			return;
		}
		List<BomOrderMaterialVo> demandMaterialList = demandList.stream()
				.map(BomMaterialDemandVo::getBomOrderMaterial)
				.filter(item -> Objects.nonNull(item.getMaterialSnapshotId()))
				.collect(Collectors.toList());
		if (CollUtil.isEmpty(demandMaterialList)) {
			return;
		}
		this.resetLatestPriceInfo(demandMaterialList);

        Map<Long, BomOrderMaterialVo> demandMaterialMap = demandMaterialList.stream()
                .collect(Collectors.toMap(BomOrderMaterialVo::getBomMaterialDemandId, Function.identity(), (k1, k2) -> k1));

        demandList.forEach(item -> {
            BomOrderMaterialVo materialVo = demandMaterialMap.get(item.getBomMaterialDemandId());
            if (Objects.nonNull(materialVo)) {
                item.setBomOrderMaterial(materialVo);
            }
        });
    }

    private List<BomOrderMaterialVo> resetTransientBomMaterial(BomOrderTransient transientBom,
                                                               List<BomOrderMaterialTransient> materialTransientList,
                                                               Map<Long, MaterialSnapshotVo> materialSnapshotMap,
                                                               Map<Long, PurchaseApplyFollowCountVO> purchaseApplyFollowCountMap,
                                                               Map<Long, List<CraftDemandInfoTransient>> craftDemandTransientMap,
                                                               Map<Long, List<DesignRemarks>> designRemarkMap,
                                                               Map<String, List<String>> commodityPicturesMap) {
        List<BomOrderMaterialVo> orderMaterialVoList = materialTransientList.stream().map(bomMaterial -> {
            BomOrderMaterialVo bomOrderMaterialVo = new BomOrderMaterialVo();
            //jv特殊处理
            bomOrderMaterialVo.setIdentifyMaterialId(bomMaterial.getIdentifyMaterialId());

            //bom物料备注
            bomOrderMaterialVo.setMaterialRemarkList(assembleMaterialRemark(designRemarkMap, bomMaterial));

            if (CollectionUtil.isNotEmpty(purchaseApplyFollowCountMap) && Objects.nonNull(bomMaterial.getMaterialSnapshotId())) {
                Long materialSnapshotId = bomMaterial.getMaterialSnapshotId();
                MaterialSnapshotVo materialSnapshotVo = materialSnapshotMap.get(bomMaterial.getMaterialSnapshotId());
                if (ObjectUtils.isNotEmpty(materialSnapshotVo)) {
                    if (Objects.equals(materialSnapshotVo.getNewMaterial(), Bool.NO.getCode()) && Objects.nonNull(materialSnapshotVo.getMatchId())) {
                        materialSnapshotId = materialSnapshotVo.getMatchId();
                    }
                    PurchaseApplyFollowCountVO purchaseApplyFollowCountVO = purchaseApplyFollowCountMap.get(materialSnapshotId);
                    // 采购次数
                    if (ObjectUtils.isNotEmpty(purchaseApplyFollowCountVO)) {
                        bomOrderMaterialVo.setPurchaseApplyFollowCount(purchaseApplyFollowCountVO.getPurchaseCount());
                    }
                }
            }

            MaterialSnapshotVo materialSnapshotVo = materialSnapshotMap.get(bomMaterial.getMaterialSnapshotId());
            if (Objects.nonNull(materialSnapshotVo)) {

                BeanUtils.copyProperties(materialSnapshotVo, bomOrderMaterialVo);

                //成分
                List<MaterialSnapshotVo.ComponentVo> componentList = materialSnapshotVo.getComponentList();
                if (CollUtil.isNotEmpty(componentList)) {
                    bomOrderMaterialVo.setMaterial(Json.serialize(componentList));
                }
                bomOrderMaterialVo.setDemandType(materialSnapshotVo.getMaterialType());
                List<String> latestPictures = commodityPicturesMap.getOrDefault(materialSnapshotVo.getSpuSkuId(), new ArrayList<>());
				bomOrderMaterialVo.setMatchPictureList(latestPictures.isEmpty() ? materialSnapshotVo.getMaterialPictureList() : latestPictures);
                //属性
                bomOrderMaterialVo.setSkuAttrs(JSON.toJSONString(materialSnapshotVo.getSkuAttrList()));

                BeanUtils.copyProperties(bomMaterial, bomOrderMaterialVo);
                bomOrderMaterialVo.setCuttingMethod(bomMaterial.getCuttingMethod());
                bomOrderMaterialVo.setPrototypeMaterialName(bomMaterial.getPrototypeMaterialName());

                //大货进价
                BomOrderConverter.setBulkPurchase(materialSnapshotVo, bomOrderMaterialVo);

                //销售单位
                String saleUnit = BomOrderConverter.transformBomSaleUnit(materialSnapshotVo);
                bomOrderMaterialVo.setSaleUnit(saleUnit);
                //兼容历史数据无核算单位
                if (Objects.equals(transientBom.getState(), BomOrderStateEnum.CALCULATED.getCode()) && Objects.isNull(bomOrderMaterialVo.getDosageAccountUnit())) {
                    bomOrderMaterialVo.setDosageAccountUnit(saleUnit);
                }
            } else {
                BeanUtils.copyProperties(bomMaterial, bomOrderMaterialVo);
                //找料中物料默认辅料
                bomOrderMaterialVo.setDemandType(MaterialDemandTypeEnum.ACCESSORIES.getCode());
            }
            //工艺信息
            List<CraftDemandInfoVo> craftDemandInfoVoList = BomOrderConverter.assembleCraftDemandInfoTransient(craftDemandTransientMap, bomOrderMaterialVo, new HashMap<>());
            bomOrderMaterialVo.setCraftDemandInfoList(craftDemandInfoVoList);
            return bomOrderMaterialVo;
        }).collect(Collectors.toList());

        //物料排序
        return BomOrderConverter.bomOrderMaterialSort(orderMaterialVoList);
    }

    private static List<MaterialRemarkVo> assembleMaterialRemark(Map<Long, List<DesignRemarks>> designRemarkMap, BomOrderMaterialTransient bomMaterial) {
        //bom物料备注
        List<DesignRemarks> designRemarksList = designRemarkMap.get(bomMaterial.getBomMaterialId());
        if (CollectionUtil.isEmpty(designRemarksList)) {
            return Collections.emptyList();
        }

        return designRemarksList.stream().sorted(Comparator.comparing(DesignRemarks::getCreatedTime).reversed())
                .map(remark -> {
                    MaterialRemarkVo materialRemark = new MaterialRemarkVo();
                    BeanUtils.copyProperties(remark, materialRemark);
                    return materialRemark;
                }).collect(Collectors.toList());
    }

    @Override
    public BomOrderExcelResp bomOrderExportExcel(Long bomId) throws Exception {
        BomOrderDetailResp bomDetailResp = this.exportDetailResp(bomId);
        // BomOrderDetailResp bomDetailResp = detail(bomId);
        BomOrderExcelResp bomExcelResp = new BomOrderExcelResp();
        bomExcelResp.setBomCode(bomDetailResp.getBomCode());
        bomExcelResp.setBomVersionNum(bomDetailResp.getBomVersionNum());
        bomExcelResp.setStyleCode(bomDetailResp.getStyleCode());
        bomExcelResp.setDesignCode(bomDetailResp.getDesignCode());
        bomExcelResp.setDesignerName(bomDetailResp.getDesignerName());
        PrototypeHistory prototypeHistory = prototypeHistoryRepository.getByPrototypeId(bomDetailResp.getPrototypeId());
        // bomExcelResp.setDemandType(DemandTaskTypeEnum.findByCode(prototypeHistory.getDemandTaskType()).getDemandType() + " 款");

        //图片: 优先取第一张客户图; 不存在则取第一张设计图
        bomExcelResp.setCustomerPicture(getPictureStream(bomDetailResp.getDesignPictureList().get(0)));

        //无bom物料清单
        if (CollectionUtil.isEmpty(bomDetailResp.getBomOrderMaterialList())) {
            return bomExcelResp;
        }

        //字典
        List<DictVo> dictResp = dictValueRemoteHelper.listByDictCodes(List.of(DictConstant.PART_USE_CODE, DictConstant.BOM_CUTTING_METHOD_CODE, DictConstant.INVOICE_DICT));
        Map<String, DictVo> dictValueMap = dictResp.stream().collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));

        //面辅料供应商信息 key为 skuId
        Map<String, CommoditySupplierInfoVo> fabricAccessoriesSkuSupplierMap = getFabricAccessoriesSupplierInfo(bomDetailResp.getBomOrderMaterialList());

        //特殊辅料供应商信息
        List<Long> specialAccessoriesSupplierIds = bomDetailResp.getBomOrderMaterialList().stream().filter(bomMaterialVo ->
                Objects.equals(bomMaterialVo.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode()))
                .map(BomOrderMaterialVo::getSupplierId).collect(Collectors.toList());
        Map<Long, CommoditySupplierInfoVo> specialAccessoriesSupplierMap = getSpecialAccessoriesSupplierInfo(specialAccessoriesSupplierIds);

        AtomicInteger atomicInteger = new AtomicInteger(1);
        List<BomOrderExcelResp.MaterialVo> materialVoList = bomDetailResp.getBomOrderMaterialList().stream().map(bomMaterialVo -> {
            BomOrderExcelResp.MaterialVo materialVo = new BomOrderExcelResp.MaterialVo();
            materialVo.setSequenceNo(atomicInteger.getAndIncrement());
            materialVo.setMaterialType(bomMaterialVo.getPrototypeMaterialName());

            //先给字典字段赋值
            this.buildBomExcelDictValue(bomMaterialVo, materialVo, dictValueMap);

            //设置品名与物料信息 --v5.6.1
            this.setBomExcelMaterialInfo(bomMaterialVo, materialVo);

            // 物料属性
            String materialProp = buildBomExcelMaterialProp(bomMaterialVo);

            //设置物料价格信息
            this.setBomExcelPriceInfo(bomMaterialVo, materialVo);

            //特殊辅料供应商
            if (Objects.equals(bomMaterialVo.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())) {
                CommoditySupplierInfoVo specialAccessoriesSupplier = specialAccessoriesSupplierMap.get(bomMaterialVo.getSupplierId());
                materialVo.setSupplierInfo(buildSupperInfo(specialAccessoriesSupplier));
            } else {
                //面辅料供应商信息
                CommoditySupplierInfoVo supplierInfoVo = fabricAccessoriesSkuSupplierMap.get(bomMaterialVo.getSpuSkuId());
                materialVo.setSupplierInfo(buildSupperInfo(supplierInfoVo));
            }

            materialVo.setMaterialProp(materialProp);

            // 匹配物料图片
            if (CollectionUtil.isNotEmpty(bomMaterialVo.getMatchPictureList())) {
                materialVo.setPicture(getPictureStream(bomMaterialVo.getMatchPictureList().get(0)));
            }

            String material = bomMaterialVo.getMaterial();
            if (StringUtils.isNotBlank(material)) {
                List<JSONObject> jsonObjectList = JSON.parseArray(material, JSONObject.class);
                String element = jsonObjectList.stream().map(ent -> ent.getString("name") + ent.getString("percent") + "%")
                        .collect(Collectors.joining(StrUtil.CRLF));
                materialVo.setElement(element);
            }

            //裁剪方式/对色
            String cuttingColorInfo = null;
            if (Objects.equals(bomMaterialVo.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
                cuttingColorInfo = materialVo.getCuttingMethod();
            } else if ( !Objects.equals(bomMaterialVo.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())
                    && !Objects.equals(bomMaterialVo.getColorMatchMaterialState(), BomColorMaterialStateEnum.NO_THING.getCode())) {
                cuttingColorInfo = BomColorMaterialStateEnum.findEntityByCode(bomMaterialVo.getColorMatchMaterialState()).getDesc()
                        + StrUtil.COLON + StrUtil.SPACE + bomMaterialVo.getColorMatchMaterialName();
            }
            materialVo.setCuttingColorInfo(cuttingColorInfo);

            materialVo.setSingleDosage(Objects.nonNull(bomMaterialVo.getDosageAccount()) ? String.valueOf(bomMaterialVo.getDosageAccount()) : "");
            materialVo.setSingleDosageUnit(Optional.ofNullable(bomMaterialVo.getDosageAccountUnit()).orElse(""));

            // 二次工艺/环节
            List<CraftDemandInfoVo> craftDemandInfoList = bomMaterialVo.getCraftDemandInfoList();
            if (CollectionUtil.isNotEmpty(craftDemandInfoList)) {
                String craftInfo = craftDemandInfoList.stream().map(craftDemandVo -> {
                    String category = StringUtils.isNotBlank(craftDemandVo.getCategory3()) ? craftDemandVo.getCategory3() : craftDemandVo.getCategory2();
                    String craftsRequire = Objects.equals(craftDemandVo.getCraftsRequire(), DemandCraftsRequireEnum.PRE_CUTTING_CRAFTS.getCode()) ? "裁前" : "裁后";
                    return category + "/" + craftsRequire;
                }).collect(Collectors.joining(StrUtil.CRLF));
                materialVo.setCraftInfo(craftInfo);
            }

			//物料与工艺采购周期信息
			String purchaseCycleInfo = this.buildPurchaseInfo(bomMaterialVo);
			materialVo.setPurchaseCycleInfo(purchaseCycleInfo);

			return materialVo;
		}).collect(Collectors.toList());

        bomExcelResp.setMaterialVoList(materialVoList);
        return bomExcelResp;
    }

    private String buildPurchaseInfo(BomOrderMaterialVo bomMaterialVo) {
		if (Objects.isNull(bomMaterialVo)) {
			return null;
		}
		//物料的采购周期信息
		StringBuilder samplePurchaseCycleInfo = new StringBuilder("样衣" + StrUtil.CRLF);
		String materialSamplePurchaseCycle = Objects.isNull(bomMaterialVo.getSamplePurchasingCycle()) ?
				StrUtil.DASHED : bomMaterialVo.getSamplePurchasingCycle() + bomMaterialVo.getSamplePurchasingCycleUnit();
		samplePurchaseCycleInfo.append("物料").append(StrUtil.COLON).append(StrUtil.SPACE)
				.append(materialSamplePurchaseCycle).append(StrUtil.CRLF);

		StringBuilder bulkPurchaseCycleInfo = new StringBuilder("大货" + StrUtil.CRLF);
		String materialBulkPurchaseCycle = Objects.isNull(bomMaterialVo.getBulkPurchasingCycle())
				? StrUtil.DASHED : bomMaterialVo.getBulkPurchasingCycle() + bomMaterialVo.getBulkPurchasingCycleUnit();
		bulkPurchaseCycleInfo.append("物料").append(StrUtil.COLON).append(StrUtil.SPACE)
				.append(materialBulkPurchaseCycle).append(StrUtil.CRLF);

		// 二次工艺的采购周期信息
		List<CraftDemandInfoVo> craftDemandInfoList = bomMaterialVo.getCraftDemandInfoList();
		if (CollectionUtil.isNotEmpty(craftDemandInfoList)) {
			for (CraftDemandInfoVo craftDemandVo : craftDemandInfoList) {
				String category = StringUtils.isNotBlank(craftDemandVo.getCategory3())
						? craftDemandVo.getCategory3() : craftDemandVo.getCategory2();
				String craftSamplePurchaseCycle = Objects.isNull(craftDemandVo.getSampleCraftCycle())
						? StrUtil.DASHED : craftDemandVo.getSampleCraftCycle() + craftDemandVo.getSampleCraftCycleUnit();
				samplePurchaseCycleInfo.append(category).append(StrUtil.COLON).append(StrUtil.SPACE)
						.append(craftSamplePurchaseCycle).append(StrUtil.CRLF);

				String craftBulkPurchaseCycle = Objects.isNull(craftDemandVo.getBulkCraftCycle())
						? StrUtil.DASHED : craftDemandVo.getBulkCraftCycle() + craftDemandVo.getBulkCraftCycleUnit();
				bulkPurchaseCycleInfo.append(category).append(StrUtil.COLON).append(StrUtil.SPACE)
						.append(craftBulkPurchaseCycle).append(StrUtil.CRLF);
			}
		}

		return samplePurchaseCycleInfo.toString() + bulkPurchaseCycleInfo.toString();
	}

	/**
	 * bom导出的物料详情
	 */
	private BomOrderDetailResp exportDetailResp(Long bomId) throws ExecutionException, InterruptedException {
		BomOrder bomOrder = bomOrderRepository.getEntityByBomId(bomId);
		SdpDesignException.notNull(bomOrder, "bom单不存在! ");

        BomOrderDetailResp bomDetailResp = this.getBomBasicInfo(bomOrder);

		BomOrderMaterialInfoSummariseDto infoSummariseDto = this.queryCommonInfo(bomOrder, false);
		//bom物料清单信息
		List<BomOrderMaterialVo> allMaterialVoList = BomOrderConverter.assembleBomOrderMaterialVo(infoSummariseDto.getBomOrderMaterialList(),
				infoSummariseDto.getMaterialSnapshotList(), infoSummariseDto.getCraftDemandInfoList(), infoSummariseDto.getDesignRemarksList(),
				Collections.emptyList(), infoSummariseDto.getPurchaseApplyFollowCountVOS(), bomOrder, infoSummariseDto.getConfirmCraftMatchMap(), productRemoteHelper);


        //过滤没有物快照id的物料
        allMaterialVoList = allMaterialVoList.stream().filter(e -> Objects.nonNull(e.getMaterialSnapshotId())).collect(Collectors.toList());
        bomDetailResp.setBomOrderMaterialList(allMaterialVoList);

        //补充特殊辅料信息
        List<BomOrderMaterialVo> specialAccessoriesMaterials = this.getSpecialAccessoriesInfo(bomId, infoSummariseDto.getDesignRemarksList());
        bomDetailResp.getBomOrderMaterialList().addAll(specialAccessoriesMaterials);

		//最新bom查询最新的价格与采购周期信息
		if (infoSummariseDto.getLatestBomFlag()) {
			this.resetLatestPriceInfo(bomDetailResp.getBomOrderMaterialList());
		}

		return bomDetailResp;
	}

    public InputStream getPictureStream(String pictureUrl) {
        if (StringUtils.isBlank(pictureUrl)) {
            return null;
        }

        String newPictureUrl = pictureUrl.startsWith("http") ? pictureUrl : YbfUrlUtil.getUrl(pictureUrl);
        try {
            HttpRequest httpRequest = HttpRequest.of(newPictureUrl, CharsetUtil.CHARSET_UTF_8).method(Method.GET);

            //百布oss图片加了防盗链,需要配置请求头
            final HttpResponse response = httpRequest
                    .header("Referer", "https://cx-backend.ibaibu.com")
                    .setFollowRedirects(true).executeAsync();
            if (!response.isOk()) {
                throw new HttpException("Server response error with status code: [{}]", response.getStatus());
            }
            return response.bodyStream();
        } catch (Exception e) {
            log.info("Bom下载图片 :{} 失败 ", newPictureUrl, e);
        }
        return null;
    }

    /**
     * 组装供应商信息
     *
     */
    private String buildSupperInfo(CommoditySupplierInfoVo supplierInfoVo) {
        if (Objects.isNull(supplierInfoVo) || Objects.isNull(supplierInfoVo.getSupplierId())) {
            return StringUtils.EMPTY;
        }

        String phone = StringUtils.isNotBlank(supplierInfoVo.getMobilePhone()) ? supplierInfoVo.getMobilePhone() : supplierInfoVo.getTelephone();
        return "名称: " + supplierInfoVo.getSupplierName() + StrUtil.CRLF
                + "电话: " + Optional.ofNullable(phone).orElse("") + StrUtil.CRLF
                + "地址： " + Optional.ofNullable(supplierInfoVo.getProvince()).orElse("") +
                Optional.ofNullable(supplierInfoVo.getCity()).orElse("") +
                Optional.ofNullable(supplierInfoVo.getArea()).orElse("") +
                Optional.ofNullable(supplierInfoVo.getAddress()).orElse("");
    }

    /**
     * 获取面辅料匹配供应商信息
     *
     */
    private Map<String, CommoditySupplierInfoVo> getFabricAccessoriesSupplierInfo(List<BomOrderMaterialVo> bomOrderMaterialList) {
        //过滤后的面辅料
        List<BomOrderMaterialVo> filterBomMaterials = bomOrderMaterialList.stream().filter(bomMaterial -> !Objects.equals(bomMaterial.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(filterBomMaterials)) {
            return Collections.emptyMap();
        }

        Map<Integer, Set<Long>> bomMaterialTypeMap = filterBomMaterials.stream().collect(
                Collectors.groupingBy(BomOrderMaterialVo::getDemandType, Collectors.mapping(BomOrderMaterialVo::getSkuId, Collectors.toSet())));

        //需要查询的sku
        Map<String, Long> querySpuSkuMap = StreamUtil.list2MapWithValue(filterBomMaterials, BomOrderMaterialVo::getSpuSkuId, BomOrderMaterialVo::getBomMaterialId);

        Map<String, CommoditySupplierInfoVo> skuSupplierMap = new HashMap<>();
        Set<Long> supplierIds = new HashSet<>(128);

        try {
            //辅料商品
            Set<Long> accessoriesSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.ACCESSORIES.getCode());
            List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
            Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream()
                    .map(ProductSpuInfoVo::getSkus).flatMap(Collection::stream)
                    //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
                    .filter(item -> Objects.nonNull(querySpuSkuMap.get(BomOrderConverter.getAccessoriesSpuSkuId(item))))
                    .collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
            accessoriesSkuInfos.forEach(accessoriesSku -> supplierIds.add(accessoriesSku.getSupplierId()));

            //面料商品
            Set<Long> fabricSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.FABRIC.getCode());
            List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(fabricSkuIds, querySpuSkuMap);
            Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = fabricSkuInfos.stream()
                    .collect(Collectors.toMap(BomOrderConverter::getLyFabricSpuSkuId, Function.identity(), (k1, k2) -> k1));

            //if (CollectionUtil.isNotEmpty(fabricSkuInfos)) {
            fabricSkuInfos.forEach(fabricSku -> supplierIds.add(fabricSku.getSupplierId()));
            //}

            List<CommoditySupplierInfoVo> supplierInfoList = supplierInfoRemoteHelper.getSupplierBaseInfo(new ArrayList<>(supplierIds));
            Map<Long, CommoditySupplierInfoVo> supplierInfoVoMap = supplierInfoList.stream().collect(Collectors.toMap(CommoditySupplierInfoVo::getSupplierId, Function.identity(), (k1, k2) -> k1));

            skuSupplierMap = filterBomMaterials.stream().collect(Collectors.toMap(BomOrderMaterialVo::getSpuSkuId,
                    bomMaterial -> {
                        Long supplierId = null;
                        if (Objects.equals(bomMaterial.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
                            if (CollUtil.isNotEmpty(fabricSkuMap) && Objects.nonNull(fabricSkuMap.get(bomMaterial.getSpuSkuId()))) {
                                supplierId = fabricSkuMap.get(bomMaterial.getSpuSkuId()).getSupplierId();
                            } else {
                                log.warn("==== 面料信息为空: 物料名称:{}; skuId:{} ===", bomMaterial.getPrototypeMaterialName(), bomMaterial.getSkuId());
                            }
                        } else if (Objects.equals(bomMaterial.getDemandType(), MaterialDemandTypeEnum.ACCESSORIES.getCode())) {
                            if (CollUtil.isNotEmpty(accessoriesSkuMap) && Objects.nonNull(accessoriesSkuMap.get(bomMaterial.getSkuId()))) {
                                supplierId = accessoriesSkuMap.get(bomMaterial.getSkuId()).getSupplierId();
                            } else {
                                log.warn("==== 辅料信息为空: 物料名称:{}; skuId:{} ===", bomMaterial.getPrototypeMaterialName(), bomMaterial.getSkuId());
                            }
                        }
                        return supplierInfoVoMap.getOrDefault(supplierId, new CommoditySupplierInfoVo());
                    }, (k1, k2) -> k1));

        } catch (Exception e) {
            log.warn("【Bom导出Excel】获取面辅料匹配供应商异常", e);
        }
        return skuSupplierMap;
    }


    /**
     * 获取特殊辅料供应商信息
     *
     */
    private Map<Long, CommoditySupplierInfoVo> getSpecialAccessoriesSupplierInfo(List<Long> supplierIds) {
        if (CollectionUtil.isEmpty(supplierIds)) {
            return Collections.emptyMap();
        }

        Map<Long, CommoditySupplierInfoVo> supplierInfoVoMap = new HashMap<>();
        try {
            List<CommoditySupplierInfoVo> supplierInfoList = supplierInfoRemoteHelper.getSupplierBaseInfo(supplierIds);
            supplierInfoVoMap = supplierInfoList.stream().collect(Collectors.toMap(CommoditySupplierInfoVo::getSupplierId, Function.identity(), (k1, k2) -> k1));
        } catch (Exception e) {
            log.warn("【Bom导出Excel】获取特殊辅料供应商异常", e);
        }
        return supplierInfoVoMap;
    }


    /**
     * 获取供应商信息
     *
     */
    private Map<Long, CommoditySupplierInfoVo> getSupplierInfo(List<Long> supplierIds) {
        if (CollectionUtil.isEmpty(supplierIds)) {
            return Collections.emptyMap();
        }

        Map<Long, CommoditySupplierInfoVo> supplierInfoVoMap = new HashMap<>();
        try {
            List<CommoditySupplierInfoVo> supplierInfoList = supplierInfoRemoteHelper.getSupplierBaseInfo(supplierIds);
            supplierInfoVoMap = supplierInfoList.stream().collect(Collectors.toMap(CommoditySupplierInfoVo::getSupplierId, Function.identity(), (k1, k2) -> k1));
        } catch (Exception e) {
            log.warn("获取供应商异常", e);
        }
        return supplierInfoVoMap;
    }

    /**
     * 构建BomExcel字典值
     *
     */
    private void buildBomExcelDictValue(BomOrderMaterialVo bomMaterialVo, BomOrderExcelResp.MaterialVo materialVo, Map<String, DictVo> dictValueMap) {

        //使用部分
        DictVo partUseDict = dictValueMap.get(DictConstant.PART_USE_CODE);
        if (Objects.nonNull(partUseDict) && CollectionUtil.isNotEmpty(partUseDict.getChildren())) {
            String partUse = bomMaterialVo.getPartUse();
            if (StringUtils.isNotBlank(partUse)) {
                Map<String, DictVo> partUseDictValueMap = partUseDict.getChildren().stream()
                        .collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));
                String partUsrName = Arrays.stream(partUse.split(",")).map(partStr ->
                        Optional.ofNullable(partUseDictValueMap.get(partStr)).map(DictVo::getDictName).orElse(""))
                        .collect(Collectors.joining(","));
                materialVo.setPartUse(partUsrName);
            }
        }

        //裁剪方法
        DictVo bomCuttingMethodDict = dictValueMap.get(DictConstant.BOM_CUTTING_METHOD_CODE);
        if (Objects.nonNull(bomCuttingMethodDict) && CollectionUtil.isNotEmpty(bomCuttingMethodDict.getChildren())) {
            bomCuttingMethodDict.getChildren().stream().filter(dictValueVo -> Objects.equals(bomMaterialVo.getCuttingMethod(), Integer.valueOf(dictValueVo.getDictCode())))
                    .findFirst().ifPresent(dictValueVo -> materialVo.setCuttingMethod(dictValueVo.getDictName()));
        }

        //合作关系
        DictVo invoiceDict = dictValueMap.get(DictConstant.INVOICE_DICT);
        if (Objects.nonNull(invoiceDict) && CollectionUtil.isNotEmpty(invoiceDict.getChildren())) {
            invoiceDict.getChildren().stream().filter(dictValueVo -> Objects.equals(bomMaterialVo.getInvoiceState(), Integer.valueOf(dictValueVo.getDictCode())))
                    .findFirst().ifPresent(dictValueVo -> materialVo.setInvoiceStateStr(dictValueVo.getDictName()));
        }

    }

    /**
     * 设置BomExcel价格信息
     */
    private void setBomExcelPriceInfo(BomOrderMaterialVo bomMaterialVo, BomOrderExcelResp.MaterialVo materialVo) {
        String priceInfo =  "合作关系：" + this.null2empty(materialVo.getInvoiceStateStr()) + StrUtil.CRLF;
        // 需求类型: 1面料 2辅料
        if (Objects.equals(bomMaterialVo.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
            priceInfo += "空差：" + this.null2empty(bomMaterialVo.getMatchPurchaseGap()) + StrUtil.CRLF
                    + "足米价：" + this.null2empty(bomMaterialVo.getMeterPrice()) + "元/" + bomMaterialVo.getMeterPriceUnit();
        } else if (Objects.equals(bomMaterialVo.getDemandType(), MaterialDemandTypeEnum.ACCESSORIES.getCode())) {
            priceInfo += "包装数量：" + this.null2empty(bomMaterialVo.getPackNumber()) + this.null2empty(bomMaterialVo.getPackAssistantUnitName())
                    + "/" + this.null2empty(bomMaterialVo.getPackUnitName()) + StrUtil.CRLF
                    + "大货进价：" + this.null2empty(bomMaterialVo.getMinPrice()) + "元/" + this.null2empty(bomMaterialVo.getMinPriceUnit());
        } else if (Objects.equals(bomMaterialVo.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())) {
            priceInfo += "大货进价：" + this.null2empty(bomMaterialVo.getMinPrice()) + "元/" + this.null2empty(bomMaterialVo.getMinPriceUnit());
        }

        //价格失效时间
        priceInfo += StrUtil.CRLF + Optional.ofNullable(bomMaterialVo.getPriceInvalidTime())
                .map(item -> LocalDateTimeUtil.formatNormal(bomMaterialVo.getPriceInvalidTime())).orElse(StringConstants.EMPTY);

        materialVo.setPriceInfo(priceInfo);
    }

    private String null2empty(Object value) {
        return Objects.isNull(value) ? StringConstants.EMPTY : value + StringConstants.EMPTY;
    }

    /**
     * 构建BomExcel物料属性
     *
     */
    private String buildBomExcelMaterialProp(BomOrderMaterialVo bomMaterialVo) {
        String materialProp = "";
        // 需求类型: 1面料 2辅料
        if (Objects.equals(bomMaterialVo.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
            String colorNumber = StringUtils.isNotBlank(bomMaterialVo.getColorNumber()) ? "(" + bomMaterialVo.getColorNumber() + ")" : "";
            materialProp = "颜色: " + Optional.ofNullable(bomMaterialVo.getColorName()).orElse("") + colorNumber + StrUtil.CRLF
                    + (Objects.isNull(bomMaterialVo.getWidthConfirm()) ? "" : "幅宽：" + bomMaterialVo.getWidthConfirm() + "cm" + StrUtil.CRLF)
                    + "克重：" + bomMaterialVo.getWeightStrFormat();

        } else {
            String skuAttrs = bomMaterialVo.getSkuAttrs();
            if (StringUtils.isNotBlank(skuAttrs)) {
                // [{"attrId":6814447565640040472,"attrName":"材质","attrType":1,"valueId":6814447565640067080,"attrValue":"PE"},{"attrId":6814447565640040696,"attrName":"颜色","attrType":1,"valueId":6814447565640040840,"attrValue":"透明色"},{"attrId":6814447565640067424,"attrName":"品种","attrType":1,"valueId":6814447565640067448,"attrValue":"3*3"}]
                List<JSONObject> jsonObjectList = JSON.parseArray(skuAttrs, JSONObject.class);
                materialProp = jsonObjectList.stream().map(skuAttr -> skuAttr.getString("attrName") + ": " + skuAttr.getString("attrValue"))
                        .collect(Collectors.joining(StrUtil.CRLF));
            }
        }
        return materialProp;
    }

    /**
     * 设置品名与物料信息
     */
    private void setBomExcelMaterialInfo(BomOrderMaterialVo bomMaterialVo, BomOrderExcelResp.MaterialVo excelMaterialVo) {
        if (Objects.isNull(bomMaterialVo)) {
            return;
        }

        String materialInfo = "SPU: " + bomMaterialVo.getCommodityCode() + StrUtil.CRLF
                + "SKU：" + bomMaterialVo.getSkuCode() + StrUtil.CRLF
                + "货号：" + bomMaterialVo.getCommodityNumber();
        //企划料
        if (Objects.equals(Bool.YES.getCode(), bomMaterialVo.getIsPlanning())) {
            String bandDateStr = Objects.isNull(bomMaterialVo.getBandDate()) ? null
                    : DateTimeFormatter.ofPattern("yyyy年MM月").format(bomMaterialVo.getBandDate());
            materialInfo += StrUtil.CRLF + "企划料：" + bandDateStr;
        }

        String commodityName = "";
        // 需求类型:
        if (Objects.equals(bomMaterialVo.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
            //净色-品名; 花型-品类
            if (Objects.equals(bomMaterialVo.getCommodityType(), CommodityTypeEnum.PURE.getCode())) {
                commodityName = "品名：" + bomMaterialVo.getCommodityName();
            } else if (Objects.equals(bomMaterialVo.getCommodityType(), CommodityTypeEnum.FLOWER.getCode())) {
                commodityName = "品类：" + bomMaterialVo.getFlowerCategory();
            }

        } else {
            //辅料品名
            commodityName = "品名：" + bomMaterialVo.getCommodityName();
        }

        materialInfo = commodityName + StrUtil.CRLF + materialInfo;

        excelMaterialVo.setMaterialInfo(materialInfo);
        excelMaterialVo.setCommodityName(commodityName);

        // 设置单个单元格多种样式
        WriteCellData<String> materialInfoRichText = new WriteCellData<>();
        materialInfoRichText.setType(CellDataTypeEnum.RICH_TEXT_STRING);

        RichTextStringData richTextStringData = new RichTextStringData();
        richTextStringData.setTextString(materialInfo);

        if (StringUtils.isNotBlank(commodityName)) {
            //对品名标红
            WriteFont writeFont = new WriteFont();
            writeFont.setColor(IndexedColors.RED.getIndex());
            richTextStringData.applyFont(0, commodityName.length(), writeFont);
        }

        materialInfoRichText.setRichTextStringDataValue(richTextStringData);
        excelMaterialVo.setMaterialInfoRichText(materialInfoRichText);
    }

    @Override
    public List<CraftDemandInfoVo> getCraftDemandInfoBySupplyChain(SupplyChainCraftDemandReq req) {
        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getListByThirdPartyCraftDemandIds(req.getSupplyChainCraftIds());
        if (CollectionUtil.isEmpty(craftDemandInfoList)) {
            return Collections.emptyList();
        }

        return craftDemandInfoList.stream().map(craftDemandInfo -> {
            CraftDemandInfoVo craftDemandInfoVo = new CraftDemandInfoVo();
            BeanUtils.copyProperties(craftDemandInfo, craftDemandInfoVo);
            if (StringUtils.isNotBlank(craftDemandInfo.getPicture())) {
                craftDemandInfoVo.setPictureList(Arrays.stream(craftDemandInfo.getPicture().split(",")).collect(Collectors.toList()));
            }
            return craftDemandInfoVo;
        }).collect(Collectors.toList());

    }

    @Override
    public List<BomOrderDetailVo> getLatestBomOrderDetailList4Inner(DesignCodeReq req) {
        return req.getDesignCodeList().parallelStream().map(designCode -> {
            try {
                return getLatestBomOrderDetail4Inner(designCode, req.getNoSearch());
            } catch (Exception e) {
                log.error("【根据设计款号 {} 查询最新已提交的Bom详情 异常】", designCode, e);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

    }


    @Override
    public List<BomOrderDetailVo> getSubmittedBomVersionList(String designCode) {
        return bomOrderRepository.getSubmittedBomVersionList(designCode).stream().map(bomOrder -> {
            try {
                return getBomOrderDetailVoInner(bomOrder);
            } catch (Exception e) {
                throw new SdpDesignException("获取bom异常", e);
            }
        }).collect(Collectors.toList());
    }

    @Override
    public List<BomOrderPrintVo> getBomOrderPrintListSdk(DesignCodeReq req) {
        List<BomOrderPrintVo> bomOrderPrintList = req.getDesignCodeList().parallelStream().map(designCode -> {
            BomOrder bomOrder = bomOrderRepository.getLatestSubmitBom(designCode);
            if (Objects.nonNull(bomOrder)) {
                try {
                    BomOrderPrintVo bomOrderPrintVo = print(bomOrder.getBomId());
                    //过滤掉特殊辅料信息
                    List<BomOrderMaterialVo> materialVoList = bomOrderPrintVo.getBomOrderMaterialList().stream().filter(bomOrderMaterialVo ->
                            !StringUtils.equals(bomOrderMaterialVo.getCommodityType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.name()))
                            .collect(Collectors.toList());
                    bomOrderPrintVo.setBomOrderMaterialList(materialVoList);
                    return bomOrderPrintVo;
                } catch (Exception e) {
                    log.warn("此设计款号:{} bomId:{} 打印Bom异常", designCode, bomOrder.getBomId(), e);
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        return bomOrderPrintList;
    }

	@Override
	public List<CraftDemandMatchResp> getCraftDemandMatchInfo(Long craftDemandId) {
        if (Objects.isNull(craftDemandId)) {
            return Collections.emptyList();
        }
		CraftDemandInfo craftDemandInfo = craftDemandInfoRepository.getEntity(craftDemandId);
		Long supplyChainCraftDemandId = null;
		if (Objects.isNull(craftDemandInfo)) {
            return Collections.emptyList();
		}else {
			supplyChainCraftDemandId = craftDemandInfo.getThirdPartyCraftDemandId();
		}
		//v4.9 优先查看最新处理登记信息、无再查看确认的工艺匹配结果
        List<Long> craftDemandIdList = Objects.isNull(supplyChainCraftDemandId) ? Collections.emptyList() : Collections.singletonList(supplyChainCraftDemandId);
        CraftDemandRegistrationInfoDto registrationInfoDto = demandRemoteHelper.getRegistrationInfo(craftDemandIdList)
				.stream().max(Comparator.comparing(CraftDemandRegistrationInfoDto::getCreatedTime)).orElse(null);
		List<CraftDemandMatchDetailDto.CraftDemandMatchDetail> craftDemandMatchList = demandRemoteHelper.getCraftDemandMatchDetail(supplyChainCraftDemandId);

		if (CollectionUtil.isEmpty(craftDemandMatchList)) {
			return Collections.emptyList();
		}
		CraftDemandMatchResp matchResp = new CraftDemandMatchResp();
		matchResp.setThirdPartyCraftDemandId(supplyChainCraftDemandId);
		matchResp.setCraftDemandId(craftDemandId);
		if (Objects.nonNull(registrationInfoDto)) {
            Map<Long, List<CraftSampleTaskInnerInfoResp>> sampleTaskMap = demandRemoteHelper.getTaskBySupplyChainCraftIds(List.of(supplyChainCraftDemandId));
            CraftSampleTaskInnerInfoResp sampleTask = sampleTaskMap.get(supplyChainCraftDemandId).stream().filter(e -> e.getTaskId().equals(registrationInfoDto.getTaskId())).findFirst().orElseGet(CraftSampleTaskInnerInfoResp::new);
            CraftDemandMatchDetailDto.CraftDemandMatchDetail craftDemandMatch = craftDemandMatchList.stream().filter(e -> sampleTask.getMatchId().equals(e.getId())).findFirst().orElseGet(CraftDemandMatchDetailDto.CraftDemandMatchDetail::new);
            BeanUtils.copyProperties(craftDemandMatch, matchResp);
			if (Objects.nonNull(registrationInfoDto.getCraftFactoryId())) {
                List<CommoditySupplierDetailRespVo> supplierDetailRespVoList = supplierInfoRemoteHelper.getSupplierDetailInfo(List.of(registrationInfoDto.getCraftFactoryId()));
				CommoditySupplierInfoVo supplierInfoVo = CollectionUtil.isEmpty(supplierDetailRespVoList) ? new CommoditySupplierInfoVo() : supplierDetailRespVoList.get(0).getCommoditySupplierInfoVo();
                //联系人信息
                SupplierPersonnelVo supplierPersonnelVo = CollectionUtil.isEmpty(supplierDetailRespVoList) ? new SupplierPersonnelVo() : supplierDetailRespVoList.get(0).getSupplierPersonnelList()
                        .stream().filter(e -> SupplierPersonnelTypeEnum.HEADER.getCode().equals(e.getType())).findFirst().orElseGet(SupplierPersonnelVo::new);
                matchResp.setSupplierName(supplierInfoVo.getSupplierName());
                matchResp.setSupplierPhone(supplierPersonnelVo.getMobilePhone());
                matchResp.setSupplierAddress(CollectionUtil.join(Stream.of(supplierInfoVo.getProvince(), supplierInfoVo.getCity(), supplierInfoVo.getArea(), supplierInfoVo.getAddress())
					.filter(Objects::nonNull).collect(Collectors.toList()), ""));
			}
            matchResp.setTaskCostPrice(registrationInfoDto.getBulkPrice());
            matchResp.setUnit(registrationInfoDto.getBulkUnit());
			matchResp.setCreatorName(registrationInfoDto.getCreatorName());
			matchResp.setCreatedTime(registrationInfoDto.getCreatedTime());
        } else {
            CraftDemandMatchDetailDto.CraftDemandMatchDetail craftDemandMatch = craftDemandMatchList.stream().filter(e -> e.getIsConfirm() == Bool.YES.getCode()).findFirst().orElseGet(CraftDemandMatchDetailDto.CraftDemandMatchDetail::new);
            BeanUtils.copyProperties(craftDemandMatch, matchResp);
            matchResp.setSupplierAddress(CollectionUtil.join(Stream.of(craftDemandMatch.getProvince(), craftDemandMatch.getCity(), craftDemandMatch.getArea(), craftDemandMatch.getAddress())
					.filter(Objects::nonNull).collect(Collectors.toList()), ""));
            if (CollectionUtil.isNotEmpty(craftDemandMatch.getCraftDemandMatchStepPriceRespList())) {
                List<CraftDemandMatchResp.ProductStepPriceVo> productStepPriceVoList = craftDemandMatch.getCraftDemandMatchStepPriceRespList().stream().map(matchStepPriceDto -> {
                    CraftDemandMatchResp.ProductStepPriceVo productStepPriceVo = new CraftDemandMatchResp.ProductStepPriceVo();
                    BeanUtils.copyProperties(matchStepPriceDto, productStepPriceVo);
                    return productStepPriceVo;
                }).collect(Collectors.toList());

				matchResp.setProductStepPriceVoList(productStepPriceVoList);
			}
        }
        return List.of(matchResp);
    }

    @Override
    public List<CraftDemandInfoVo> getCraftDemandListByDesignCode(String designCode) {
        List<PrototypeHistory> prototypeHistoryList = prototypeHistoryRepository.list(new QueryWrapper<PrototypeHistory>().lambda().eq(PrototypeHistory::getDesignCode, designCode));
        if (CollectionUtil.isEmpty(prototypeHistoryList)) {
            return Collections.emptyList();
        }

        List<Long> prototypeIds = prototypeHistoryList.stream().map(PrototypeHistory::getPrototypeId).collect(Collectors.toList());
        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getCraftDemandInfo(new CraftDemandQuery().setPrototypeIds(prototypeIds).setIsTransient(Bool.NO.getCode()));
        if (CollectionUtil.isEmpty(craftDemandInfoList)) {
            return Collections.emptyList();
        }

        return craftDemandInfoList.stream().map(craftDemandInfo -> {
            CraftDemandInfoVo craftDemandInfoVo = new CraftDemandInfoVo();
            BeanUtils.copyProperties(craftDemandInfo, craftDemandInfoVo);
            if (StringUtils.isNotBlank(craftDemandInfo.getPicture())) {
                craftDemandInfoVo.setPictureList(Arrays.stream(craftDemandInfo.getPicture().split(",")).collect(Collectors.toList()));
            }
            return craftDemandInfoVo;
        }).collect(Collectors.toList());

    }

    @Override
    public Map<Long, BomOrderVo> getBomOrderByMaterialIds(BomMaterialReq bomMaterialReq) {
        List<BomOrderMaterial> bomOrderMaterialList = bomOrderMaterialRepository.list(new QueryWrapper<BomOrderMaterial>().lambda().in(BomOrderMaterial::getBomMaterialId, bomMaterialReq.getBomMaterialIdList())
                .eq(BomOrderMaterial::getIsDeleted, Bool.NO.getCode()).eq(BomOrderMaterial::getIsTransient, Bool.NO.getCode()));
        if (CollectionUtil.isEmpty(bomOrderMaterialList)) {
            return Collections.emptyMap();
        }

        List<Long> bomIds = bomOrderMaterialList.stream().map(BomOrderMaterial::getBomId).distinct().collect(Collectors.toList());
        Map<Long, BomOrder> bomOrderMap = bomOrderRepository.list(new QueryWrapper<BomOrder>().lambda()
                .in(BomOrder::getBomId, bomIds)).stream().collect(Collectors.toMap(BomOrder::getBomId, Function.identity(), (k1, k2) -> k1));


        Map<Long, BomOrderVo> bomOrderVoMap = new HashMap<>(100);
        bomOrderMaterialList.forEach(bomOrderMaterial -> {
            BomOrder bomOrder = bomOrderMap.get(bomOrderMaterial.getBomId());
            BomOrderVo bomOrderVo = new BomOrderVo();
            BeanUtils.copyProperties(bomOrder, bomOrderVo);
            bomOrderVoMap.put(bomOrderMaterial.getBomMaterialId(), bomOrderVo);
        });

        return bomOrderVoMap;
    }

    @Override
    public List<CraftDemandInfoVo> getCraftDemandListAfterTime(LocalDateTime craftCreateTime) {
        // SELECT third_party_craft_demand_id FROM plm_design.craft_demand_info WHERE created_time >= '2022-07-07 00:00:00' and crafts_require = 110 AND third_party_craft_demand_id  is not null ;
        LambdaQueryWrapper<CraftDemandInfo> queryWrapper = new QueryWrapper<CraftDemandInfo>().lambda()
                .gt(CraftDemandInfo::getCreatedTime, craftCreateTime).eq(CraftDemandInfo::getCraftsRequire, DemandCraftsRequireEnum.POST_CUTTING_CRAFTS.getCode())
                .isNotNull(CraftDemandInfo::getThirdPartyCraftDemandId);
        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.list(queryWrapper);
        if (CollectionUtil.isEmpty(craftDemandInfoList)) {
            return Collections.emptyList();
        }

        //过滤掉关闭的工艺
        Set<Long> closeThirdPartyCraftDemandIds = craftDemandInfoList.stream().filter(craft -> Objects.equals(craft.getState(), CraftDemandStateEnum.CLOSED.getCode()))
                .map(CraftDemandInfo::getThirdPartyCraftDemandId).collect(Collectors.toSet());
        craftDemandInfoList = craftDemandInfoList.stream().filter(craft -> !closeThirdPartyCraftDemandIds.contains(craft.getThirdPartyCraftDemandId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(craftDemandInfoList)) {
            return Collections.emptyList();
        }

        List<Long> prototypeIds = craftDemandInfoList.stream().map(CraftDemandInfo::getPrototypeId).distinct().collect(Collectors.toList());
        Map<Long, String> prototypeMap = prototypeHistoryRepository.getByPrototypeIds(prototypeIds).stream().collect(Collectors.toMap(PrototypeHistory::getPrototypeId, PrototypeHistory::getDesignCode, (k1, k2) -> k1));

        List<CraftDemandInfoVo> craftDemandInfoVoList = new ArrayList<>(1024);
        craftDemandInfoList.stream().collect(Collectors.toMap(CraftDemandInfo::getThirdPartyCraftDemandId, Function.identity(), (k1, k2) -> k2)).forEach((key, value) -> {
            CraftDemandInfoVo craftDemandInfoVo = new CraftDemandInfoVo();
            BeanUtils.copyProperties(value, craftDemandInfoVo);
            if (StringUtils.isNotBlank(value.getPicture())) {
                craftDemandInfoVo.setPictureList(Arrays.stream(value.getPicture().split(",")).collect(Collectors.toList()));
            }
            craftDemandInfoVo.setDesignCode(prototypeMap.get(value.getPrototypeId()));

            craftDemandInfoVoList.add(craftDemandInfoVo);
        });

        return craftDemandInfoVoList;
    }

    @Override
    public List<CraftDemandInfoVo> getLatestDemolitionCraftList(DesignCodeReq designCodeReq) {

        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeReq.getDesignCodeList());
        if (CollectionUtil.isEmpty(prototypeList)) {
            return Collections.emptyList();
        }
        List<Long> latestPrototypeIds = prototypeList.stream().map(Prototype::getLatestPrototypeId).distinct().collect(Collectors.toList());
        Map<Long, String> prototypeMap = prototypeList.stream().collect(Collectors.toMap(Prototype::getLatestPrototypeId, Prototype::getDesignCode, (k1, k2) -> k1));

        List<CraftDemandInfo> craftInfoList = craftDemandInfoRepository.list(new QueryWrapper<CraftDemandInfo>().lambda()
                .in(CraftDemandInfo::getPrototypeId, latestPrototypeIds).isNotNull(CraftDemandInfo::getThirdPartyCraftDemandId));

        //.eq(CraftDemandInfo::getState, CraftDemandStateEnum.SUBMIT.getCode())
        //.eq(CraftDemandInfo::getIsTransient, Bool.NO.getCode()));
        if (CollectionUtil.isEmpty(craftInfoList)) {
            return Collections.emptyList();
        }

        List<CraftDemandInfoVo> totalCraftVoList = new ArrayList<>(128);

        craftInfoList.stream().collect(Collectors.groupingBy(CraftDemandInfo::getThirdPartyCraftDemandId)).values().forEach(craftList -> {
            boolean closed = craftList.stream().anyMatch(craft -> Objects.equals(craft.getState(), CraftDemandStateEnum.CLOSED.getCode()) && Objects.equals(craft.getIsTransient(), Bool.NO.getCode()));
            if (!closed) {
                CraftDemandInfo craftInfo = craftList.get(0);
                CraftDemandInfoVo craftDemandInfoVo = new CraftDemandInfoVo();
                BeanUtils.copyProperties(craftInfo, craftDemandInfoVo);
                craftDemandInfoVo.setDesignCode(prototypeMap.get(craftInfo.getPrototypeId()));

                if (StringUtils.isNotBlank(craftInfo.getPicture())) {
                    craftDemandInfoVo.setPictureList(Arrays.stream(craftInfo.getPicture().split(",")).collect(Collectors.toList()));
                }
                totalCraftVoList.add(craftDemandInfoVo);
            }
        });

        return totalCraftVoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void demolitionGenerateBom(DemolitionGenerateBomReq generateBomReq) {
        log.info("【拆版生成Bom单】请求参数 req:{}", JSON.toJSONString(generateBomReq));
        // 一个skc只有1个bom单号
        List<BomOrder> bomOrderList = bomOrderRepository.list(Wrappers.<BomOrder>lambdaQuery().eq(BomOrder::getDesignCode, generateBomReq.getDesignCode()));
        SdpDesignException.isEmpty(bomOrderList, "不是第一次拆版生成Bom单 designCode {}", generateBomReq.getDesignCode());

        BomOrder saveBomOrder = new BomOrder();
        saveBomOrder.setBomId(IdPool.getId());
        saveBomOrder.setBomCode(businessCodeGenerator.generate(CodeRuleEnum.BOM_ORDER_CODE, bomOrderRepository::selectLatestBomCode));
        saveBomOrder.setVersionNum(1);
        saveBomOrder.setPrototypeId(generateBomReq.getPrototypeId());
        saveBomOrder.setDesignCode(generateBomReq.getDesignCode());
        saveBomOrder.setState(BomOrderStateEnum.WAIT_SUBMIT.getCode());
        UserContent userContent = UserContentHolder.get();
        saveBomOrder.setCreatorId(userContent.getCurrentUserId());
        saveBomOrder.setCreatorName(userContent.getCurrentUserName());
        saveBomOrder.setCreatedTime(LocalDateTime.now());
        saveBomOrder.setReviserId(userContent.getCurrentUserId());
        saveBomOrder.setReviserName(userContent.getCurrentUserName());
        saveBomOrder.setRevisedTime(LocalDateTime.now());

        //同步之前特殊辅料信息
        syncBeforeSpecialAccessories(null, saveBomOrder);

        //处理Bom日志
        DesignLogReq designLogReq = BomOrderConverter.buildBomOrderLog(saveBomOrder, "拆版生成Bom单");
        designLogService.create(designLogReq);

        bomOrderRepository.save(saveBomOrder);
    }

    @Override
    public GoodMaterialInfoResp goodMaterialInfo(GoodMaterialInfoReq req) {

        Set<Long> supplierIdSet = new HashSet<>();

        //根据skuId从履约查询辅料信息
        List<BomOrderMaterialVo> accessoriesMaterialList = this.getAccessoryMaterialVoFromDemand(req.getAccessoriesSpuSkuList(), supplierIdSet);

        //根据skuId从履约查询面料信息
        List<BomOrderMaterialVo> fabricMaterialList = this.getFabricMaterialVo(req.getFabricSpuSkuList(), supplierIdSet);

        //查询供应商合作关系
        this.resetInvoiceState(supplierIdSet, accessoriesMaterialList, fabricMaterialList);

        return new GoodMaterialInfoResp()
                .setAccessoriesMaterialList(accessoriesMaterialList)
                .setFabricMaterialList(fabricMaterialList);
    }

    @Override
    public LyMaterialQueryResp queryLyMaterial(LyMaterialQueryReq req) {
        LyMaterialQueryResp queryResp = new LyMaterialQueryResp();
        //面料
        List<Long> fabricSkuIdList = req.getFabricSkuIdList();
        if (CollUtil.isNotEmpty(fabricSkuIdList)) {
            Set<Long> fabricSkuIdSet = new HashSet<>(fabricSkuIdList);
            List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(fabricSkuIdSet);
            log.info("=== 获取面料sku信息,响应结果 dataResponse:{} ===", JSON.toJSONString(fabricSkuInfos));
            if (CollUtil.isNotEmpty(fabricSkuInfos)) {
                queryResp.setFabricMaterialStr(JSON.toJSONString(fabricSkuInfos));
            }

        }

        //辅料
        List<Long> accessoriesSkuIdList = req.getAccessoriesSkuIdList();
        if (CollUtil.isNotEmpty(accessoriesSkuIdList)) {
            Set<Long> accessoriesSkuIdSet = new HashSet<>(accessoriesSkuIdList);
            List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIdSet);
            log.info("=== 获取辅料sku信息,响应结果 dataResponse:{} ===", JSON.toJSONString(accessoriesSkuInfos));
            if (CollUtil.isNotEmpty(accessoriesSkuInfos)) {
                queryResp.setAccessoriesMaterialStr(JSON.toJSONString(accessoriesSkuInfos));
            }
        }
        return queryResp;
    }

    private void resetInvoiceState(Set<Long> supplierIdSet,
                                   List<BomOrderMaterialVo> accessoriesMaterialList,
                                   List<BomOrderMaterialVo> fabricMaterialList) {
        if (CollUtil.isEmpty(supplierIdSet)) {
            return;
        }
        //查询供应商信息
        List<Long> supplierIds = new ArrayList<>(supplierIdSet);
        Map<Long, CommoditySupplierInfoVo> supplierMap = this.getSupplierInfo(supplierIds);
        if (CollUtil.isEmpty(supplierMap)) {
            return;
        }
        //设置供应商合作关系(履约的开票状态)
        if (CollUtil.isNotEmpty(accessoriesMaterialList)) {
            accessoriesMaterialList.forEach(item -> {
                if (Objects.nonNull(item.getSupplierId()) && Objects.nonNull(supplierMap.get(item.getSupplierId()))) {
                    CommoditySupplierInfoVo supplierInfoVo = supplierMap.get(item.getSupplierId());
                    CommoditySupplierExtVo supplierExtVo = supplierInfoVo.getExt();
                    if (Objects.nonNull(supplierExtVo)) {
                        item.setInvoiceState(supplierExtVo.getInvoiceState());
                    }
                }
            });
        }
        //设置供应商合作关系(履约的开票状态)
        if (CollUtil.isNotEmpty(fabricMaterialList)) {
            fabricMaterialList.forEach(item -> {
                if (Objects.nonNull(item.getSupplierId()) && Objects.nonNull(supplierMap.get(item.getSupplierId()))) {
                    CommoditySupplierInfoVo supplierInfoVo = supplierMap.get(item.getSupplierId());
                    CommoditySupplierExtVo supplierExtVo = supplierInfoVo.getExt();
                    if (Objects.nonNull(supplierExtVo)) {
                        item.setInvoiceState(supplierExtVo.getInvoiceState());
                    }
                }
            });
        }
    }

    @Override
    public List<BomOrderMaterialVo> getFabricMaterialVo(List<GoodMaterialInfoReq.SpuSkuIdReq> spuSkuReqList, Set<Long> supplierIdSet) {
        // 根据skuId从履约查询面料信息
        if (CollUtil.isEmpty(spuSkuReqList)) {
            return List.of();
        }

        Map<String, GoodMaterialInfoReq.SpuSkuIdReq> querySpuSkuMap = StreamUtil.list2Map(spuSkuReqList, GoodMaterialInfoReq.SpuSkuIdReq::getSpuSkuId);


        Set<Long> fabricSkuIdSet = StreamUtil.convertSet(spuSkuReqList, GoodMaterialInfoReq.SpuSkuIdReq::getSkuId);
        List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(fabricSkuIdSet);
        if (CollUtil.isEmpty(fabricSkuInfos)) {
            return List.of();
        }

        List<BomOrderMaterialVo> fabricMaterialList = new LinkedList<>();
        fabricSkuInfos.forEach(productSkuVo -> {
            //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
            if (Objects.isNull(querySpuSkuMap.get(BomOrderConverter.getFabricSpuSkuId(productSkuVo)))) {
                return;
            }

            BomOrderMaterialVo materialVo = new BomOrderMaterialVo();
            materialVo.setDemandType(MaterialDemandTypeEnum.FABRIC.getCode());

            if (CollectionUtil.isNotEmpty(productSkuVo.getPictures())) {
                List<String> matchPictureList = BomOrderMaterialConverter.buildFabricPicture(productSkuVo);
                materialVo.setMatchPictureList(matchPictureList);
            }
            String commodityType = StringUtils.equals(productSkuVo.getCategoryNo1(), CategoryNo1Enum.CATEGORY_NO1_1.getCode()) ? CommodityTypeEnum.PURE.getCode() : CommodityTypeEnum.FLOWER.getCode();
            materialVo.setCommodityType(commodityType);
            materialVo.setCommodityName(productSkuVo.getCommodityName());
            materialVo.setCommodityId(productSkuVo.getCommodityId());
            materialVo.setCommodityCode(productSkuVo.getCommodityCode());
            materialVo.setCommodityNumber(productSkuVo.getCommodityNumber());
            materialVo.setSkuId(productSkuVo.getSkuId());
            materialVo.setSkuCode(productSkuVo.getSkuCode());
            materialVo.setMaterial(productSkuVo.getMaterial());
            // materialVo.setWidthLow(Objects.isNull(productSkuVo.getWidthLow()) ? null : String.valueOf(productSkuVo.getWidthLow()));
            // materialVo.setWidthHigh(Objects.isNull(productSkuVo.getWidthHigh()) ? null : String.valueOf(productSkuVo.getWidthHigh()));
            // materialVo.setWidthUnit(productSkuVo.getWidthUnit());
            // materialVo.setWeightLow(Objects.isNull(productSkuVo.getWeightLow()) ? null : String.valueOf(productSkuVo.getWeightLow()));
            // materialVo.setWeightHigh(Objects.isNull(productSkuVo.getWeightHigh()) ? null : String.valueOf(productSkuVo.getWeightHigh()));
            // materialVo.setWeightUnit(productSkuVo.getWeightUnit());
            //新的克重,幅宽字段
            materialVo.setWidthStrFormat(productSkuVo.getWidthStrFormat());
            materialVo.setWeightStrFormat(productSkuVo.getWeightStrFormat());

            materialVo.setMatchPurchaseGap(productSkuVo.getSaleGap());
            materialVo.setFlowerCategory(productSkuVo.getCategory());

            if (Objects.equals(productSkuVo.getSkuCombinationType(), SkuCombinationTypeEnum.COMBINATION_0.getCode())) {
                JSONObject jsonObject = JSON.parseObject(productSkuVo.getSkuCombination());
                materialVo.setColorName(jsonObject.getString("色系"));
            }

            materialVo.setColorNumber(productSkuVo.getColorNumber());
            if (CollectionUtil.isNotEmpty(productSkuVo.getSkuPriceVos())) {
                productSkuVo.getSkuPriceVos().stream().filter(skuPriceVo -> Objects.equals(skuPriceVo.getSalesRegionId(), DefaultSalesRegionEnum.NATION.getRegionId()))
                        .findFirst().ifPresent(skuPriceVo -> {
                    materialVo.setMatchGuidePrice(skuPriceVo.getGuidePrice());
                    materialVo.setMatchCostPriceUnit(productSkuVo.getUnit());
                    materialVo.setMeterPrice(skuPriceVo.getMeterPrice());
                    materialVo.setMeterPriceUnit(productSkuVo.getMeterPriceUnit());
                    //价格回复时间
                    materialVo.setPriceReplyTime(skuPriceVo.getRevisedTime());
                    //价格失效时间
                    materialVo.setPriceInvalidTime(skuPriceVo.getValidityEndTime());

                    //给前端统一面料大货进价字段: 面料取足米价单位
                    materialVo.setBulkPurchasePrice(skuPriceVo.getMeterPrice());
                    materialVo.setBulkPurchasePriceUnit(productSkuVo.getMeterPriceUnit());
                });
            }

            CommoditySkuCollectionRespVo.Sku.SampleVo sampleVo = productSkuVo.getSampleVo();
            if (Objects.nonNull(sampleVo)) {
                materialVo.setMatchSampleGuidePrice(sampleVo.getSampleSalePrice());
                materialVo.setMatchSampleUnit(productSkuVo.getSampleUnit());
            }
            //供应商信息
            materialVo.setSupplierId(productSkuVo.getSupplierId());
            materialVo.setSupplierCode(productSkuVo.getSupplierCode());
            materialVo.setSupplierName(productSkuVo.getSupplierName());
            if (CollUtil.isNotEmpty(supplierIdSet)) {
                supplierIdSet.add(productSkuVo.getSupplierId());
            }

			//启用与上架状态
			materialVo.setEnableState(Integer.valueOf(productSkuVo.getIsEnable()));
			materialVo.setOnShelfState(productSkuVo.getIsHouliu());
			//样衣与大货周期
			materialVo.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
			materialVo.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
            //企划料信息
            materialVo.setIsPlanning(productSkuVo.getIsPlanning());
            materialVo.setBandDate(productSkuVo.getBandDate());

            fabricMaterialList.add(materialVo);
        });
        return fabricMaterialList;
    }

    private List<BomOrderMaterialVo> getAccessoryMaterialVoFromDemand(List<GoodMaterialInfoReq.SpuSkuIdReq> spuSkuReqList, Set<Long> supplierIdSet) {
        //根据skuId从履约查询辅料信息
        if (CollUtil.isEmpty(spuSkuReqList)) {
            return List.of();
        }

        Map<String, GoodMaterialInfoReq.SpuSkuIdReq> querySpuSkuMap = StreamUtil.list2Map(spuSkuReqList, GoodMaterialInfoReq.SpuSkuIdReq::getSpuSkuId);

        //辅料商品
        Set<Long> accessoriesSkuIdSet = StreamUtil.convertSet(spuSkuReqList, GoodMaterialInfoReq.SpuSkuIdReq::getSkuId);
        List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIdSet);
        if (CollUtil.isEmpty(accessoriesSkuInfos)) {
            return List.of();
        }
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream()
                .collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
        if (CollUtil.isEmpty(accessoriesSpuMap)) {
            return List.of();
        }

        //可能一个spu下多个sku
        Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus)
                .flatMap(Collection::stream)
                //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
                .filter(item -> Objects.nonNull(querySpuSkuMap.get(BomOrderConverter.getAccessoriesSpuSkuId(item))))
                .collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
        if (CollUtil.isEmpty(accessoriesSkuMap)) {
            return List.of();
        }

        //封装bom物料对象
        List<BomOrderMaterialVo> accessoriesMaterialList = new LinkedList<>();
        accessoriesSkuMap.forEach((skuId, productSkuVo) -> {
            ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(productSkuVo.getSpuId());

            BomOrderMaterialVo materialVo = new BomOrderMaterialVo();
            materialVo.setDemandType(MaterialDemandTypeEnum.ACCESSORIES.getCode());
            //sku图>主图 --v4.3_汶俊;
                List<String> pictureList = productSkuVo.getPictures().stream().map(ProductPictureWebVo::getPicturePath).collect(Collectors.toList());
                 pictureList.addAll(productSpuInfoVo.getPictures().stream().map(ProductPictureVo::getPicturePath).collect(Collectors.toList()));
                materialVo.setMatchPictureList(pictureList);

            materialVo.setCommodityType(CommodityTypeEnum.ACCESSORIES.getCode());
            materialVo.setCommodityName(productSpuInfoVo.getProductName());
            materialVo.setCommodityId(productSpuInfoVo.getSpuId());
            materialVo.setCommodityCode(productSpuInfoVo.getSpuCode());
            materialVo.setCommodityNumber(productSkuVo.getProductNumber());
            materialVo.setSkuId(productSkuVo.getSkuId());
            materialVo.setSkuCode(productSkuVo.getSkuCode());
            if (CollectionUtil.isNotEmpty(productSkuVo.getPrices())) {
                ProductSkuPriceVo skuPriceVo = productSkuVo.getPrices().get(0);
                materialVo.setMatchSalePrice(skuPriceVo.getSkuPrice());
                materialVo.setMatchPurchaseUnitName(skuPriceVo.getValuationUnitName());
                materialVo.setPurchasePrice(skuPriceVo.getPurchasePrice());
                materialVo.setSkuPrice(skuPriceVo.getSkuPrice());
                materialVo.setSaleUnit(skuPriceVo.getValuationUnitName());
                //价格回复时间
                materialVo.setPriceReplyTime(skuPriceVo.getRevisedTime());
                //价格失效时间
                materialVo.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());
            }
            materialVo.setPackNumber(productSkuVo.getPackNumber());
            materialVo.setPackUnitName(productSkuVo.getPackUnitName());
            materialVo.setPackAssistantUnitName(productSkuVo.getPackAssistantUnitName());
            materialVo.setSkuAttrs(JSON.toJSONString(productSkuVo.getSkuAttrs()));
            materialVo.setMinPrice(productSkuVo.getMinPrice());
            materialVo.setMinPriceUnit(productSkuVo.getMinUnit());
            //给前端统一辅料大货字段: 辅料取最小价格单位
            if (Objects.nonNull(productSkuVo.getMinPrice())) {
                String formatPrice = new DecimalFormat("0.0000").format(productSkuVo.getMinPrice());
                materialVo.setBulkPurchasePrice(new BigDecimal(formatPrice));
            }
            materialVo.setBulkPurchasePriceUnit(productSkuVo.getMinUnit());

            //供应商信息
            materialVo.setSupplierId(productSpuInfoVo.getSupplierId());
            materialVo.setSupplierCode(productSpuInfoVo.getSupplierCode());
            materialVo.setSupplierName(productSpuInfoVo.getSupplierName());
            supplierIdSet.add(productSpuInfoVo.getSupplierId());

			//启用与上架状态
			materialVo.setEnableState(productSpuInfoVo.getEnabled());
			materialVo.setOnShelfState(productSpuInfoVo.getOnShelf());
			//样衣与大货周期
			materialVo.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
			materialVo.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
            //企划料信息
            materialVo.setIsPlanning(productSkuVo.getIsPlanning());
            materialVo.setBandDate(productSkuVo.getBandDate());

            accessoriesMaterialList.add(materialVo);
        });
        return accessoriesMaterialList;
    }

    @Override
    public BomQuoteSkcResp quoteSkc(Long bomId) {
        BomOrder bomOrder = bomOrderRepository.getById(bomId);
        SdpDesignException.notNull(bomOrder, "bom单不存在! ");
        BomOrder latestBom = bomOrderRepository.getLatestBomByDesignCode(bomOrder.getDesignCode());
        SdpDesignException.isTrue(Objects.equals(bomOrder.getBomId(), latestBom.getBomId()), "当前bom单不是最新版本! ");

        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        SdpDesignException.notNull(prototype, "设计款号不存在! ");
        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId());
        SdpDesignException.notNull(prototypeDetail, "设计款详情不存在! ");

        BomQuoteSkcResp skcResp = null;
        if (Objects.equals(BomOrderStateEnum.WAIT_SUBMIT.getCode(), bomOrder.getState())) {
            //待提交的, 如果有 套生款skc,复色款skc或改款引用skc; 返回该skc;
            skcResp = this.getFirstVersionBomQuoteSkcResp(bomOrder, prototype, prototypeDetail);
        } else {
            //bom提交后, 再次拆板时引用了新的skc
            skcResp = this.getSubmitBomQuoteSkcResp(bomOrder, prototype);
        }

        if (Objects.nonNull(skcResp) && StringUtils.isNotBlank(skcResp.getQuoteDesignCode())) {
            //返回暂存状态
            skcResp.setTransientState(bomOrder.getTransientState());
            //查询引用款对应的已提交/已核算状态中的最新bom单
            BomOrder quoteSubmitBom = bomOrderRepository.getLatestSubmitBom(skcResp.getQuoteDesignCode());
            if (Objects.nonNull(quoteSubmitBom)) {
                skcResp.setQuoteBomId(quoteSubmitBom.getBomId());
                skcResp.setQuoteBomVersionNum(quoteSubmitBom.getVersionNum());
            }
        }
        return skcResp;
    }

    private BomQuoteSkcResp getSubmitBomQuoteSkcResp(BomOrder bomOrder, Prototype prototype) {
        //其他版本编辑时, 查询bom最新版本提交后, skc是否有拆板, 如果有并且引用款与bom提交时skc的引用款不一样, 提示;
        PrototypeHistory latestDonePrototype = prototypeHistoryRepository.geByDesignCodeAndVersionNum(prototype.getDesignCode(), prototype.getVersionNum());
        LocalDateTime prototypeCreatedTime = latestDonePrototype.getCreatedTime();

        //若最新版本bom提交后,又生成了拆板单, 判断是否有引用skc, 并判断引用的skc是否与bom提交时引用的skc一样
        //注: 第一版本使用提交时间判断
        LocalDateTime bomTime = bomOrder.getVersionNum() == 1 ? bomOrder.getSubmitTime() : bomOrder.getCreatedTime();
        if (prototypeCreatedTime.isAfter(bomTime)) {
            PrototypeDetail latestPrototypeDetail = prototypeDetailRepository.getByPrototypeId(latestDonePrototype.getPrototypeId());
            // boolean styleRefer = Objects.equals(SampleTypeEnum.PATTERN_MAKING.getCode(), latestDonePrototype.getSampleType())
            //         && Objects.nonNull(latestPrototypeDetail.getStyleReferType())
            //         && StringUtils.isNotBlank(latestPrototypeDetail.getStyleReferDesignCode());
            boolean colorRefer = Objects.equals(SkcTypeEnum.COMPOUND_COLORS.getCode(), latestDonePrototype.getSkcType())
                    && StringUtils.isNotBlank(latestDonePrototype.getMakeSameDesignCode());
            if (colorRefer) {
                //查询bom提交时的拆板单: 查找第一个创建时间在最新版本bom单创建时间之前的
                List<PrototypeHistory> prototypeHistoryList = prototypeHistoryRepository.listDoneVersionByDesignCode(prototype.getDesignCode());
                PrototypeHistory bomPrototype = prototypeHistoryList.stream()
                        .filter(item -> item.getCreatedTime().isBefore(bomOrder.getCreatedTime()))
                        .findFirst().orElse(null);
                if (Objects.isNull(bomPrototype)) {
                    return null;
                }
                BomQuoteSkcResp skcResp = new BomQuoteSkcResp();
                skcResp.setBomId(bomOrder.getBomId());
                //返回暂存状态
                skcResp.setTransientState(bomOrder.getTransientState());
                //套生款
                // if (styleRefer) {
                //     PrototypeDetail bomPrototypeDetail = prototypeDetailRepository.getByPrototypeId(bomPrototype.getPrototypeId());
                //     if (!Objects.equals(latestPrototypeDetail.getStyleReferDesignCode(), bomPrototypeDetail.getStyleReferDesignCode())) {
                //         //套版款
                //         if (Objects.equals(PrototypeStyleReferEnum.THE_SAME.getCode(), latestPrototypeDetail.getStyleReferType())) {
                //             skcResp.setBomQuoteType(BomQuoteTypeEnum.THE_SAME.getCode());
                //         }
                //         //衍生款
                //         else if (Objects.equals(PrototypeStyleReferEnum.DERIVE.getCode(), latestPrototypeDetail.getStyleReferType())) {
                //             skcResp.setBomQuoteType(BomQuoteTypeEnum.DERIVE.getCode());
                //         }
                //         skcResp.setQuoteDesignCode(latestPrototypeDetail.getStyleReferDesignCode());
                //         return skcResp;
                //     }
                // }
                //复色款
                if (colorRefer) {
                    if (!Objects.equals(latestDonePrototype.getMakeSameDesignCode(), bomPrototype.getMakeSameDesignCode())) {
                        skcResp.setBomQuoteType(BomQuoteTypeEnum.COLORS.getCode());
                        skcResp.setQuoteDesignCode(latestDonePrototype.getMakeSameDesignCode());
                        return skcResp;
                    }
                }
            }
        }
        return null;
    }

    private BomQuoteSkcResp getFirstVersionBomQuoteSkcResp(BomOrder bomOrder, Prototype prototype, PrototypeDetail prototypeDetail) {
        BomQuoteSkcResp skcResp = new BomQuoteSkcResp();
        skcResp.setBomId(bomOrder.getBomId());
        //返回暂存状态
        skcResp.setTransientState(bomOrder.getTransientState());
        // if (Objects.equals(SkcTypeEnum.NORMAL.getCode(), prototype.getSkcType())) {
        //     if (Objects.nonNull(prototypeDetail.getStyleReferType()) && StringUtils.isNotBlank(prototypeDetail.getStyleReferDesignCode())) {
        //         //套版款
        //         if (Objects.equals(PrototypeStyleReferEnum.THE_SAME.getCode(), prototypeDetail.getStyleReferType())) {
        //             skcResp.setBomQuoteType(BomQuoteTypeEnum.THE_SAME.getCode());
        //         }
        //         //衍生款
        //         else if (Objects.equals(PrototypeStyleReferEnum.DERIVE.getCode(), prototypeDetail.getStyleReferType())) {
        //             skcResp.setBomQuoteType(BomQuoteTypeEnum.DERIVE.getCode());
        //         }
        //         skcResp.setQuoteDesignCode(prototypeDetail.getStyleReferDesignCode());
        //         return skcResp;
        //     }
        // }
        //复色款
        if (Objects.equals(SkcTypeEnum.COMPOUND_COLORS.getCode(), prototype.getSkcType())
                && StringUtils.isNotBlank(prototype.getMakeSameDesignCode())) {
            skcResp.setBomQuoteType(BomQuoteTypeEnum.COLORS.getCode());
            skcResp.setQuoteDesignCode(prototype.getMakeSameDesignCode());
            return skcResp;
        }
        //判断是否为改款
        // DesignStyle designStyle = designStyleRepository.getByStyleCode(prototype.getStyleCode());
        // boolean waitSubmit = Objects.equals(bomOrder.getState(), BomOrderStateEnum.WAIT_SUBMIT.getCode());
        // boolean normalSkc = Objects.equals(prototype.getSampleType(), SampleTypeEnum.PATTERN_MAKING.getCode());
        // boolean changeSkc = Objects.equals(designStyle.getSourceType(), DesignStyleSourceTypeEnum.DESIGN_SPU_SOURCE.getCode()) || Objects.equals(designStyle.getSourceType(), DesignStyleSourceTypeEnum.CRM_SPU_SOURCE.getCode());
        // if (waitSubmit && normalSkc && changeSkc) {
        //     String quoteDesignCode = designStyle.getQuoteDesignCode();
        //     if (Objects.equals(designStyle.getSourceType(), DesignStyleSourceTypeEnum.CRM_SPU_SOURCE.getCode())) {
        //         skcResp.setBomQuoteType(BomQuoteTypeEnum.CRM_CHANGE.getCode());
        //         skcResp.setQuoteDesignCode(quoteDesignCode);
        //         return skcResp;
        //     }
        //     if (Objects.equals(designStyle.getSourceType(), DesignStyleSourceTypeEnum.DESIGN_SPU_SOURCE.getCode())) {
        //         skcResp.setBomQuoteType(BomQuoteTypeEnum.DESIGN_CHANGE.getCode());
        //         skcResp.setQuoteDesignCode(quoteDesignCode);
        //         return skcResp;
        //     }
        // }
        return null;
    }

    @Override
    public BomOrderDetailResp getQuoteBomOrderDetail(BomQuoteDetailReq req) throws Exception {
        SdpDesignException.notBlank(req.getQuoteDesignCode(), "引用设计款不能为空! ");
        String quoteDesignCode = req.getQuoteDesignCode();
        BomOrder bomOrder = bomOrderRepository.getLatestSubmitBom(quoteDesignCode);
        if (Objects.isNull(bomOrder)) {
            return null;
        }
        BomOrderDetailResp detail = this.detail(bomOrder.getBomId());
        if (Objects.isNull(detail)) {
            return null;
        }
        List<BomOrderMaterialVo> bomOrderMaterialList = detail.getBomOrderMaterialList();
        if (CollUtil.isEmpty(bomOrderMaterialList)) {
            return detail;
        }
        //引用时, 将需求中已匹配回复的物料返回
        List<BomMaterialDemandVo> materialDemandList = detail.getMaterialDemandList();
        if (CollUtil.isNotEmpty(materialDemandList)) {
            //获取需求中 不是找料中状态 的物料信息
            List<BomOrderMaterialVo> materialVoList = materialDemandList.stream()
                    .filter(item -> !Objects.equals(item.getMaterialSearchState(), Bool.YES.getCode()))
                    .map(BomMaterialDemandVo::getBomOrderMaterial).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(materialVoList)) {
                //添加的物料集合中返回(引用时引用需求的物料, 不引用需求)
                bomOrderMaterialList.addAll(materialVoList);
            }
        }

        //需实时从好料网查询面辅料数据信息
        this.resetMaterialList(bomOrderMaterialList, true);

        //校验并清空相关数据
        bomOrderMaterialList.forEach(item -> {
            if (Objects.equals(item.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())) {
                //特殊辅料要返回
                item.setBomMaterialIdCopy(item.getBomMaterialId());
            }
            //将物料相关信息置为空(前端要作为新物料)
            item.setBomMaterialId(null);
            item.setDosageAccountUnit(null);
            item.setDosageAccount(null);
            // item.setCuttingMethod(null);
            // item.setPartUse(null);
            item.setWidthConfirm(null);
            item.setMaterialSnapshotId(null);
            item.setMaterialContextId(null);
            item.setAccessoriesFlagId(null);
            //采购次数清空
            item.setPurchaseApplyFollowCount(null);
            List<CraftDemandInfoVo> craftDemandInfoList = item.getCraftDemandInfoList();
            if (CollUtil.isNotEmpty(craftDemandInfoList)) {
                craftDemandInfoList.forEach(craft -> {
                    craft.setCraftDemandId(null);
                    craft.setBomMaterialId(null);
                    craft.setThirdPartyCraftDemandId(null);
                    craft.setThirdPartyCraftDemandCode(null);
					craft.setSampleCraftCycle(null);
					craft.setSampleCraftCycleUnit(null);
					craft.setBulkCraftCycle(null);
					craft.setBulkCraftCycleUnit(null);
                });
                item.setIsNoCraft(Bool.NO.getCode());
            } else {
                item.setIsNoCraft(Bool.YES.getCode());
            }
        });
        detail.setBomOrderMaterialList(bomOrderMaterialList);
        return detail;
    }

	@Override
	public BomOrderApplyPurchaseResp getBomApplyPurchaseList(String designCode) throws ExecutionException, InterruptedException {
		BomOrder bomOrder = bomOrderRepository.getLatestSubmitBom(designCode);
		if (Objects.isNull(bomOrder)) {
			return null;
		}
		BomOrderMaterialInfoSummariseDto infoSummariseDto = queryCommonInfo(bomOrder, false);
		//bom物料清单信息
		List<BomOrderMaterialVo> allMaterialVoList = BomOrderConverter.assembleBomOrderMaterialVo(infoSummariseDto.getBomOrderMaterialList(),
				infoSummariseDto.getMaterialSnapshotList(), infoSummariseDto.getCraftDemandInfoList(), infoSummariseDto.getDesignRemarksList(),
				Collections.emptyList(), infoSummariseDto.getPurchaseApplyFollowCountVOS(), bomOrder, infoSummariseDto.getConfirmCraftMatchMap(), productRemoteHelper);
		//过滤没有物快照id的需求
		allMaterialVoList = allMaterialVoList.stream().filter(e -> Objects.nonNull(e.getMaterialSnapshotId())).collect(Collectors.toList());

        List<BomMaterialDemandVo> demandVoList = infoSummariseDto.getBomMaterialDemandVos();
        Map<Long, BomMaterialDemandVo> bomDemandMap = CollUtil.isEmpty(demandVoList) ? new HashMap<>() : infoSummariseDto.getBomMaterialDemandVos().stream()
                .collect(Collectors.toMap(BomMaterialDemandVo::getBomMaterialDemandId, Function.identity(), (k1, k2) -> k1));

        List<BomOrderMaterialVo> finalMaterialVoList = new LinkedList<>();
        allMaterialVoList.forEach(materialVo -> {
            //不返回找料中未回复的物料
            if (Objects.isNull(materialVo.getSkuId())) {
                return;
            }
            //选料的
            if (Objects.isNull(materialVo.getBomMaterialDemandId())) {
                finalMaterialVoList.add(materialVo);
                return;
            }
            //需求的
            if (CollUtil.isNotEmpty(bomDemandMap) && Objects.nonNull(bomDemandMap.get(materialVo.getBomMaterialDemandId()))) {
                BomMaterialDemandVo demandVo = bomDemandMap.get(materialVo.getBomMaterialDemandId());
                //取最新的物料
                if (Objects.equals(demandVo.getLatestBomMaterialId(), materialVo.getBomMaterialId())) {
                    finalMaterialVoList.add(materialVo);
                }
            }
        });

		//最新bom查询最新的价格与采购周期信息
		if (infoSummariseDto.getLatestBomFlag()) {
			this.resetLatestPriceInfo(finalMaterialVoList);
		}

		//处理采购的色卡图片回显
		this.purchaseColorCardPicture(finalMaterialVoList);
		Prototype prototype = infoSummariseDto.getPrototype();
		return BomOrderApplyPurchaseResp.builder()
				.prototypeId(prototype.getPrototypeId())
				.designCode(prototype.getDesignCode())
				.styleCode(prototype.getStyleCode())
				.bomOrderMaterialList(finalMaterialVoList).build();
	}


    private void resetLatestPriceInfo(List<BomOrderMaterialVo> bomOrderMaterialVoList) {
        if (CollUtil.isEmpty(bomOrderMaterialVoList)) {
            return;
        }
        //根据skuId查询好料网最新物料信息
        Map<Integer, Set<Long>> bomMaterialTypeMap = bomOrderMaterialVoList.stream()
                .filter(item -> Objects.nonNull(item.getSkuId())).collect(
                        Collectors.groupingBy(BomOrderMaterialVo::getDemandType, Collectors.mapping(BomOrderMaterialVo::getSkuId, Collectors.toSet())));

        //需要查询的sku
        Map<String, Long> querySpuSkuMap = StreamUtil.list2MapWithValue(bomOrderMaterialVoList, BomOrderMaterialVo::getSpuSkuId, BomOrderMaterialVo::getBomMaterialId);

        //辅料商品(包含特殊辅料)
        Set<Long> accessoriesSkuIds = new HashSet<>();
        Set<Long> normalAccessoriesSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.ACCESSORIES.getCode());
        Set<Long> specialAccessoriesSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode());
        if (CollUtil.isNotEmpty(normalAccessoriesSkuIds)) {
            accessoriesSkuIds.addAll(normalAccessoriesSkuIds);
        }
        if (CollUtil.isNotEmpty(specialAccessoriesSkuIds)) {
            accessoriesSkuIds.addAll(specialAccessoriesSkuIds);
        }
        List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
        // Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream()
        // 		.collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
        Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus)
                .flatMap(Collection::stream)
                //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
                .filter(item -> Objects.nonNull(querySpuSkuMap.get(BomOrderConverter.getAccessoriesSpuSkuId(item))))
                .collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));

        //面料商品
        Set<Long> fabricSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.FABRIC.getCode());
        List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(fabricSkuIds, querySpuSkuMap);
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = StreamUtil.list2Map(fabricSkuInfos, BomOrderConverter::getLyFabricSpuSkuId);

		bomOrderMaterialVoList.forEach(item -> {
			if (Objects.isNull(item.getSkuId())) {
				return;
			}
			//辅料以spu维度返回商品的 启用,上架状态
			if (CollUtil.isNotEmpty(accessoriesSkuMap)) {
				//注: 履约有可能将sku删除, bom单中提交的物料根据skuId可能查不到;
				ProductSkuVo productSkuVo = accessoriesSkuMap.get(item.getSkuId());
				if (Objects.nonNull(productSkuVo)) {
					if (CollectionUtil.isNotEmpty(productSkuVo.getPrices())){
						ProductSkuPriceVo skuPriceVo = productSkuVo.getPrices().get(0);
						//价格回复时间
						item.setPriceReplyTime(skuPriceVo.getRevisedTime());
						// 价格失效时间
						item.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());
					}
                    //给前端统一辅料大货字段: 辅料取最小价格单位
                    if (Objects.nonNull(productSkuVo.getMinPrice())) {
                        item.setMinPrice(productSkuVo.getMinPrice());
                        String formatPrice = new DecimalFormat("0.0000").format(productSkuVo.getMinPrice());
                        item.setBulkPurchasePrice(new BigDecimal(formatPrice));
                    }
                    if (StringUtils.isNotBlank(productSkuVo.getMinUnit())) {
                        item.setMinPriceUnit(productSkuVo.getMinUnit());
                        item.setBulkPurchasePriceUnit(productSkuVo.getMinUnit());
                    }
					//样衣与大货周期
					item.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
					item.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
                    //企划料信息
                    item.setIsPlanning(productSkuVo.getIsPlanning());
                    item.setBandDate(productSkuVo.getBandDate());
				} else if (!Objects.equals(item.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
					log.info("=== 查询履约辅料信息不存在! skuCode:{}; skuId: {} ====", item.getSkuCode(), item.getSkuId());
				}
			}
			if (CollUtil.isNotEmpty(fabricSkuMap) && Objects.nonNull(fabricSkuMap.get(item.getSpuSkuId()))) {
				CommoditySkuCollectionRespVo.Sku fabricSku = fabricSkuMap.get(item.getSpuSkuId());
				if (Objects.nonNull(fabricSku)) {
					if (CollectionUtil.isNotEmpty(fabricSku.getSkuPriceVos())) {
						fabricSku.getSkuPriceVos().stream()
								.filter(skuPriceVo -> Objects.equals(skuPriceVo.getSalesRegionId(), DefaultSalesRegionEnum.NATION.getRegionId()))
								.findFirst().ifPresent(skuPriceVo -> {
                            //价格回复时间
                            item.setPriceReplyTime(skuPriceVo.getRevisedTime());
                            //价格失效时间
                            item.setPriceInvalidTime(skuPriceVo.getValidityEndTime());

                            //给前端统一面料大货进价字段: 面料取足米价单位
                            if (Objects.nonNull(skuPriceVo.getMeterPrice())) {
                                item.setMeterPrice(skuPriceVo.getMeterPrice());
                                item.setBulkPurchasePrice(skuPriceVo.getMeterPrice());
                            }
                            if (StringUtils.isNotBlank(fabricSku.getMeterPriceUnit())) {
                                item.setMeterPriceUnit(fabricSku.getMeterPriceUnit());
                                item.setBulkPurchasePriceUnit(fabricSku.getMeterPriceUnit());
                            }
						});
					}

					//样衣与大货周期
					item.setSamplePurchasingCycle(fabricSku.getSamplePurchasingCycle());
					item.setBulkPurchasingCycle(fabricSku.getBulkPurchasingCycle());
					//企划料信息
                    item.setIsPlanning(fabricSku.getIsPlanning());
                    item.setBandDate(fabricSku.getBandDate());
				} else if (Objects.equals(item.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
					log.info("=== 查询履约面料信息不存在! skuCode:{}; skuId: {} ====", item.getSkuCode(), item.getSkuId());
				}
			}
		});
	}

    private void resetFabricPlaningInfo(List<BomOrderMaterialVo> bomOrderMaterialVoList) {
        if (CollUtil.isEmpty(bomOrderMaterialVoList)) {
            return;
        }
        //根据skuId查询好料网最新物料信息
        Map<Integer, Set<Long>> bomMaterialTypeMap = bomOrderMaterialVoList.stream()
                .filter(item -> Objects.nonNull(item.getSkuId())).collect(
                        Collectors.groupingBy(BomOrderMaterialVo::getDemandType, Collectors.mapping(BomOrderMaterialVo::getSkuId, Collectors.toSet())));
        //需要查询的sku
        Map<String, Long> querySpuSkuMap = StreamUtil.list2MapWithValue(bomOrderMaterialVoList, BomOrderMaterialVo::getSpuSkuId, BomOrderMaterialVo::getBomMaterialId);

        //面料商品
        Set<Long> fabricSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.FABRIC.getCode());
        List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(fabricSkuIds, querySpuSkuMap);
        if (CollUtil.isEmpty(fabricSkuInfos)) {
            return;
        }

        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = StreamUtil.list2Map(fabricSkuInfos, BomOrderConverter::getLyFabricSpuSkuId);

        bomOrderMaterialVoList.forEach(item -> {
            if (Objects.isNull(item.getSkuId())) {
                return;
            }
            if (CollUtil.isNotEmpty(fabricSkuMap) && Objects.nonNull(fabricSkuMap.get(item.getSpuSkuId()))) {
                CommoditySkuCollectionRespVo.Sku fabricSku = fabricSkuMap.get(item.getSpuSkuId());
                if (Objects.nonNull(fabricSku)) {
                    //企划料信息
                    item.setIsPlanning(fabricSku.getIsPlanning());
                    item.setBandDate(fabricSku.getBandDate());
                } else {
                    log.info("=== 查询履约面料信息不存在! skuCode:{}; skuId: {} ====", item.getSkuCode(), item.getSkuId());
                }
            }
        });
    }


    private void resetMaterialList(List<BomOrderMaterialVo> bomOrderMaterialVoList, Boolean changeSkc) {
        //根据skuId查询好料网最新物料信息
        Map<Integer, Set<Long>> bomMaterialTypeMap = bomOrderMaterialVoList.stream().collect(
                Collectors.groupingBy(BomOrderMaterialVo::getDemandType, Collectors.mapping(BomOrderMaterialVo::getSkuId, Collectors.toSet())));

        //需要查询的sku
        Map<String, Long> querySpuSkuMap = StreamUtil.list2MapWithValue(bomOrderMaterialVoList, BomOrderMaterialVo::getSpuSkuId, BomOrderMaterialVo::getBomMaterialId);

        //辅料商品(包含特殊辅料)
        Set<Long> accessoriesSkuIds = new HashSet<>();
        Set<Long> normalAccessoriesSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.ACCESSORIES.getCode());
        Set<Long> specialAccessoriesSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode());
        if (CollUtil.isNotEmpty(normalAccessoriesSkuIds)) {
            accessoriesSkuIds.addAll(normalAccessoriesSkuIds);
        }
        if (CollUtil.isNotEmpty(specialAccessoriesSkuIds)) {
            accessoriesSkuIds.addAll(specialAccessoriesSkuIds);
        }
        List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream()
                .collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
        Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus)
                .flatMap(Collection::stream)
                //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
                .filter(item -> Objects.nonNull(querySpuSkuMap.get(BomOrderConverter.getAccessoriesSpuSkuId(item))))
                .collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));

        //面料商品
        Set<Long> fabricSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.FABRIC.getCode());
        List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuSetEnableState(fabricSkuIds, querySpuSkuMap);
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = StreamUtil.list2Map(fabricSkuInfos, BomOrderConverter::getLyFabricSpuSkuId);

        bomOrderMaterialVoList.forEach(item -> {
            Integer supplyExistState = Bool.NO.getCode();
            //辅料以spu维度返回商品的 启用,上架状态
            if (CollUtil.isNotEmpty(accessoriesSpuMap) && Objects.nonNull(accessoriesSpuMap.get(item.getCommodityId()))) {
                ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(item.getCommodityId());
                //注: 履约有可能将sku删除, bom单中提交的物料根据skuId可能查不到;
                ProductSkuVo productSkuVo = accessoriesSkuMap.get(item.getSkuId());
                if (Objects.nonNull(productSkuVo)) {
                    supplyExistState = Bool.YES.getCode();
                    //查好料网辅料的启用,上架状态
                    item.setEnableState(productSpuInfoVo.getEnabled()).setOnShelfState(productSpuInfoVo.getOnShelf());
                    //若改款引用, 返回好料网中实时的辅料属性
                    this.resetAccessoriesRealTimeValue(changeSkc, item, productSpuInfoVo, productSkuVo);

                } else if (!Objects.equals(item.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
                    log.info("=== 查询履约辅料信息不存在! skuCode:{}; skuId: {} ====", item.getSkuCode(), item.getSkuId());
                }
            }
            if (CollUtil.isNotEmpty(fabricSkuMap) && Objects.nonNull(fabricSkuMap.get(item.getSpuSkuId()))) {
                CommoditySkuCollectionRespVo.Sku fabricSku = fabricSkuMap.get(item.getSpuSkuId());
                if (Objects.nonNull(fabricSku)) {
                    supplyExistState = Bool.YES.getCode();
                    //查好料网面料的启用(注, 若对应的spu未启用, sku也设置为未启用),上架状态
                    item.setEnableState(Integer.valueOf(fabricSku.getIsEnable())).setOnShelfState(fabricSku.getIsHouliu());
                    //若改款引用, 返回好料网中实时的面料属性
                    this.restFabricRealTimeValue(changeSkc, item, fabricSku);
                } else if (Objects.equals(item.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
                    log.info("=== 查询履约面料信息不存在! skuCode:{}; skuId: {} ====", item.getSkuCode(), item.getSkuId());
                }
            }
            item.setSupplyExistState(supplyExistState);
        });

        //查询供应商信息
        List<Long> supplierIds = bomOrderMaterialVoList.stream().map(BomOrderMaterialVo::getSupplierId).collect(Collectors.toList());
        Map<Long, CommoditySupplierInfoVo> supplierMap = this.getSupplierInfo(supplierIds);
        if (CollUtil.isEmpty(supplierMap)) {
            return;
        }
        //设置供应商合作关系(履约的开票状态)
        bomOrderMaterialVoList.forEach(item -> {
            if (Objects.nonNull(item.getSupplierId()) && Objects.nonNull(supplierMap.get(item.getSupplierId()))) {
                CommoditySupplierInfoVo supplierInfoVo = supplierMap.get(item.getSupplierId());
                CommoditySupplierExtVo supplierExtVo = supplierInfoVo.getExt();
                if (Objects.nonNull(supplierExtVo)) {
                    item.setInvoiceState(supplierExtVo.getInvoiceState());
                }
            }
        });
    }


    private void restFabricRealTimeValue(Boolean changeSkc, BomOrderMaterialVo item, CommoditySkuCollectionRespVo.Sku fabricSku) {
        //改款引入的skc需要的属性都要实时获取
        if (changeSkc) {
            item.setCommodityName(fabricSku.getCommodityName());
            item.setCommodityCode(fabricSku.getCommodityCode());
            item.setCommodityNumber(fabricSku.getCommodityNumber());
            //引用图片先置空
            item.setMatchPictureList(null);
            if (CollectionUtil.isNotEmpty(fabricSku.getPictures())) {
                //面料图片取值逻辑修改 --v3.5.2_汶俊;
                //净色面料：细节图>色卡图>主图>图稿 花型面料：SKU参考图>底布细节图>底布主图>底布尺图
                List<String> matchPictureList = BomOrderMaterialConverter.buildFabricPicture(fabricSku);
                item.setMatchPictureList(matchPictureList);
            }
            //成分
            item.setFlowerCategory(fabricSku.getCategory());
            item.setMaterial(fabricSku.getMaterial());
            // item.setWidthLow(Objects.isNull(fabricSku.getWidthLow()) ? null : String.valueOf(fabricSku.getWidthLow()));
            // item.setWidthHigh(Objects.isNull(fabricSku.getWidthHigh()) ? null : String.valueOf(fabricSku.getWidthHigh()));
            // item.setWidthUnit(fabricSku.getWidthUnit());
            // item.setWeightLow(Objects.isNull(fabricSku.getWeightLow()) ? null : String.valueOf(fabricSku.getWeightLow()));
            // item.setWeightHigh(Objects.isNull(fabricSku.getWeightHigh()) ? null : String.valueOf(fabricSku.getWeightHigh()));
            // item.setWeightUnit(fabricSku.getWeightUnit());
            //新的克重,幅宽字段
            item.setWidthStrFormat(fabricSku.getWidthStrFormat());
            item.setWeightStrFormat(fabricSku.getWeightStrFormat());
            item.setMatchPurchaseGap(fabricSku.getSaleGap());

            if (Objects.equals(fabricSku.getSkuCombinationType(), SkuCombinationTypeEnum.COMBINATION_0.getCode())) {
                JSONObject jsonObject = JSON.parseObject(fabricSku.getSkuCombination());
                item.setColorName(jsonObject.getString("色系"));
            }
            item.setColorNumber(fabricSku.getColorNumber());
            if (CollectionUtil.isNotEmpty(fabricSku.getSkuPriceVos())) {
                fabricSku.getSkuPriceVos().stream().filter(skuPriceVo -> Objects.equals(skuPriceVo.getSalesRegionId(), DefaultSalesRegionEnum.NATION.getRegionId()))
                        .findFirst().ifPresent(skuPriceVo -> {
                    item.setMatchGuidePrice(skuPriceVo.getGuidePrice());
                    item.setMatchCostPriceUnit(fabricSku.getUnit());
                    item.setMeterPrice(skuPriceVo.getMeterPrice());
                    item.setMeterPriceUnit(fabricSku.getMeterPriceUnit());
                    //价格回复时间
                    item.setPriceReplyTime(skuPriceVo.getRevisedTime());

                    //价格失效时间
                    item.setPriceInvalidTime(skuPriceVo.getValidityEndTime());

                    //给前端统一面料大货进价字段: 面料取足米价单位
                    item.setBulkPurchasePrice(skuPriceVo.getMeterPrice());
                    item.setBulkPurchasePriceUnit(fabricSku.getMeterPriceUnit());
                });
            }
            CommoditySkuCollectionRespVo.Sku.SampleVo sampleVo = fabricSku.getSampleVo();
            if (Objects.nonNull(sampleVo)) {
                item.setMatchSampleGuidePrice(sampleVo.getSampleSalePrice());
                item.setMatchSampleUnit(fabricSku.getSampleUnit());
            }

			//供应商信息
			item.setSupplierId(fabricSku.getSupplierId());
			item.setSupplierCode(fabricSku.getSupplierCode());
			item.setSupplierName(fabricSku.getSupplierName());
			//样衣与大货周期
			item.setSamplePurchasingCycle(fabricSku.getSamplePurchasingCycle());
			item.setBulkPurchasingCycle(fabricSku.getBulkPurchasingCycle());
            //企划料信息
            item.setIsPlanning(fabricSku.getIsPlanning());
            item.setBandDate(fabricSku.getBandDate());
		}
	}

    private void resetAccessoriesRealTimeValue(Boolean changeSkc, BomOrderMaterialVo item, ProductSpuInfoVo productSpuInfoVo, ProductSkuVo productSkuVo) {
        //改款引入的skc下的辅料的图片, 物料属性, 价格信息都要实时获取
        if (changeSkc) {
            item.setCommodityName(productSpuInfoVo.getProductName());
            item.setCommodityCode(productSpuInfoVo.getSpuCode());
            if (CollectionUtil.isNotEmpty(productSkuVo.getPrices())) {
                ProductSkuPriceVo skuPriceVo = productSkuVo.getPrices().get(0);
                item.setMatchSalePrice(skuPriceVo.getSkuPrice());
                item.setMatchPurchaseUnitName(skuPriceVo.getValuationUnitName());
                item.setPurchasePrice(skuPriceVo.getPurchasePrice());
                item.setSkuPrice(skuPriceVo.getSkuPrice());
                item.setSaleUnit(skuPriceVo.getValuationUnitName());
                //价格回复时间
                item.setPriceReplyTime(skuPriceVo.getRevisedTime());
                // 价格失效时间
                item.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());
            }
            //图片先置空,再从履约查询
            item.setMatchPictureList(List.of());
            if (CollectionUtil.isNotEmpty(productSkuVo.getPictures())) {
                List<String> pictureList = productSkuVo.getPictures().stream().map(ProductPictureWebVo::getPicturePath).collect(Collectors.toList());
                item.setMatchPictureList(pictureList);
            }
            //若sku图为空,取商品图 --v3.5.2_汶俊;
            else if (CollectionUtil.isNotEmpty(productSpuInfoVo.getPictures())) {
                item.setMatchPictureList(productSpuInfoVo.getPictures().stream().map(ProductPictureVo::getPicturePath).collect(Collectors.toList()));
            }
            item.setCommodityNumber(productSkuVo.getProductNumber());
            item.setPackNumber(productSkuVo.getPackNumber());
            item.setPackUnitName(productSkuVo.getPackUnitName());
            item.setPackAssistantUnitName(productSkuVo.getPackAssistantUnitName());
            //辅料属性
            item.setSkuAttrs(JSON.toJSONString(productSkuVo.getSkuAttrs()));

            item.setMinPrice(productSkuVo.getMinPrice());
            item.setMinPriceUnit(productSkuVo.getMinUnit());

            //给前端统一辅料大货字段: 辅料取最小价格单位
            item.setBulkPurchasePrice(productSkuVo.getMinPrice());
            item.setBulkPurchasePriceUnit(productSkuVo.getMinUnit());

			//供应商信息
			item.setSupplierId(productSpuInfoVo.getSupplierId());
			item.setSupplierCode(productSpuInfoVo.getSupplierCode());
			item.setSupplierName(productSpuInfoVo.getSupplierName());
			//样衣与大货周期
			item.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
			item.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
			//企划料信息
            item.setIsPlanning(productSkuVo.getIsPlanning());
            item.setBandDate(productSkuVo.getBandDate());
		}
	}

    private BomOrderDetailVo getBomOrderDetailVo(BomOrder bomOrder) throws InterruptedException, java.util.concurrent.ExecutionException {
        //公共信息
        BomOrderMaterialInfoSummariseDto queryCommonInfo = queryCommonInfo(bomOrder, true);

        CompletableFuture<PrototypeHistory> prototypeHistoryFuture = CompletableFuture.supplyAsync(() ->
                prototypeHistoryRepository.getByPrototypeId(bomOrder.getPrototypeId()), THREAD_POOL);
        CompletableFuture<PrototypeDetail> prototypeDetailFuture = CompletableFuture.supplyAsync(() ->
                prototypeDetailRepository.getByPrototypeId(bomOrder.getPrototypeId()), THREAD_POOL);

        CompletableFuture.allOf(prototypeHistoryFuture, prototypeDetailFuture).join();

		BomOrderDetailVo bomOrderDetailVo = BomOrderConverter.assembleBomOrderDetailVo(bomOrder, queryCommonInfo.getBomOrderMaterialList(), queryCommonInfo.getBomMaterialDemandVos(), queryCommonInfo.getMaterialSnapshotList(),
				queryCommonInfo.getCraftDemandInfoList(), queryCommonInfo.getDesignRemarksList(), prototypeHistoryFuture.get(), prototypeDetailFuture.get(),
				queryCommonInfo.getDemandRegistrationInfoList(), queryCommonInfo.getPurchaseApplyFollowCountVOS(), queryCommonInfo.getConfirmCraftMatchMap(), productRemoteHelper);

        //特殊辅料信息
        List<BomOrderMaterialVo> specialAccessoriesInfo = getSpecialAccessoriesInfo(bomOrder.getBomId(), queryCommonInfo.getDesignRemarksList());
        bomOrderDetailVo.getBomOrderMaterialList().addAll(specialAccessoriesInfo);

		//最新bom查询最新的价格与采购周期信息
		if (queryCommonInfo.getLatestBomFlag()) {
			this.resetLatestPriceInfo(bomOrderDetailVo.getBomOrderMaterialList());
			this.resetDemandMaterialPrice(bomOrderDetailVo.getMaterialDemandList());
		}

		//字典值
		List<DictVo> dictResp = dictValueRemoteHelper.listByDictCodes(List.of(DictConstant.PART_USE_CODE, DictConstant.BOM_CUTTING_METHOD_CODE));
		Map<String, DictVo> dictValueMap = new HashMap<>(64);
		if (CollectionUtil.isNotEmpty(dictResp)) {
			dictValueMap = dictResp.stream().collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));
		}
		BomOrderConverter.buildDictValue(bomOrderDetailVo.getBomOrderMaterialList(), dictValueMap);
		return bomOrderDetailVo;
	}

    private BomOrderDetailVo getBomOrderDetailVoInner(BomOrder bomOrder) throws InterruptedException, java.util.concurrent.ExecutionException {
        //公共信息
        BomOrderMaterialInfoSummariseDto queryCommonInfo = queryCommonInfo(bomOrder, true);

        CompletableFuture<PrototypeHistory> prototypeHistoryFuture = CompletableFuture.supplyAsync(() ->
                prototypeHistoryRepository.getByPrototypeId(bomOrder.getPrototypeId()), THREAD_POOL);
        CompletableFuture<PrototypeDetail> prototypeDetailFuture = CompletableFuture.supplyAsync(() ->
                prototypeDetailRepository.getByPrototypeId(bomOrder.getPrototypeId()), THREAD_POOL);

        CompletableFuture.allOf(prototypeHistoryFuture, prototypeDetailFuture).join();

		//inner接口查询包含需求中已匹配的物料
		BomOrderDetailVo bomOrderDetailVo = BomOrderConverter.assembleBomOrderDetailVo4Inner(bomOrder, queryCommonInfo.getBomOrderMaterialList(), queryCommonInfo.getBomMaterialDemandVos(), queryCommonInfo.getMaterialSnapshotList(),
				queryCommonInfo.getCraftDemandInfoList(), queryCommonInfo.getDesignRemarksList(), prototypeHistoryFuture.get(), prototypeDetailFuture.get(),
				queryCommonInfo.getDemandRegistrationInfoList(), queryCommonInfo.getPurchaseApplyFollowCountVOS(), queryCommonInfo.getConfirmCraftMatchMap(), productRemoteHelper);

        //特殊辅料信息
        List<BomOrderMaterialVo> specialAccessoriesInfo = getSpecialAccessoriesInfo(bomOrder.getBomId(), queryCommonInfo.getDesignRemarksList());
        bomOrderDetailVo.getBomOrderMaterialList().addAll(specialAccessoriesInfo);

		//最新bom查询最新的价格与采购周期信息
		if (queryCommonInfo.getLatestBomFlag()) {
			this.resetLatestPriceInfo(bomOrderDetailVo.getBomOrderMaterialList());
		}

		//字典值
		List<DictVo> dictResp = dictValueRemoteHelper.listByDictCodes(List.of(DictConstant.PART_USE_CODE, DictConstant.BOM_CUTTING_METHOD_CODE));
		Map<String, DictVo> dictValueMap = new HashMap<>(64);
		if (CollectionUtil.isNotEmpty(dictResp)) {
			dictValueMap = dictResp.stream().collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));
		}
		BomOrderConverter.buildDictValue(bomOrderDetailVo.getBomOrderMaterialList(), dictValueMap);
		return bomOrderDetailVo;
	}

    @Override
    public List<CraftDemandInfoVo> getCraftDemandInfoList(Long bomId) {
        BomOrder bomOrder = bomOrderRepository.getEntityByBomId(bomId);
        if (Objects.isNull(bomOrder)) {
            return Collections.emptyList();
        }

        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getListByBomIdsAndState(List.of(bomId), CraftDemandStateEnum.SUBMIT.getCode());
        if (CollectionUtil.isEmpty(craftDemandInfoList)) {
            return Collections.emptyList();
        }

        return craftDemandInfoList.stream().filter(craft -> Objects.equals(craft.getPrototypeId(), bomOrder.getPrototypeId())).map(craft -> {
            CraftDemandInfoVo craftDemandInfoVo = new CraftDemandInfoVo();
            BeanUtils.copyProperties(craft, craftDemandInfoVo);
            craftDemandInfoVo.setDesignCode(bomOrder.getDesignCode());
            if (StringUtils.isNotBlank(craft.getPicture())) {
                craftDemandInfoVo.setPictureList(Arrays.stream(craft.getPicture().split(",")).collect(Collectors.toList()));
            }
            return craftDemandInfoVo;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBomMaterialDosageAccount(BomMaterialDosageAccountReq req) {
        BomOrder bomOrder = bomOrderRepository.getEntityByBomId(req.getBomId());
        if (Objects.isNull(bomOrder)) {
            log.error("【Bom物料清单核算用量】无此BomId:{}", req.getBomId());
            throw new SdpDesignException("【Bom物料清单核算用量】无此Bom");
        }
        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        SdpDesignException.notNull(prototype, "skc不存在!{}", bomOrder.getDesignCode());
        UserContent userContent = UserContentHolder.get();
        LocalDateTime now = LocalDateTime.now();
        req.setBomVersionNum(bomOrder.getVersionNum());

        List<Long> bomMaterialIdList = req.getDosageAccountList().stream().map(BomMaterialDosageAccountReq.MaterialDosageAccount::getBomMaterialId).collect(Collectors.toList());
        List<BomOrderMaterial> bomOrderMaterialList = bomOrderMaterialRepository.getListByBomMaterialIds(bomMaterialIdList);
        Set<Long> bomOrderMaterialSet = bomOrderMaterialList.stream().map(BomOrderMaterial::getBomMaterialId).collect(Collectors.toSet());

        //更新bom与物料清单
        List<BomOrderMaterial> updateBomOrderMaterialList = req.getDosageAccountList().stream().filter(dosageAccountReq -> bomOrderMaterialSet.contains(dosageAccountReq.getBomMaterialId()))
                .map(dosageAccountReq -> BomOrderMaterial.builder()
                        .bomMaterialId(dosageAccountReq.getBomMaterialId()).dosageAccount(dosageAccountReq.getDosageAccount()).dosageAccountUnit(dosageAccountReq.getDosageAccountUnit())
                        .widthConfirm(dosageAccountReq.getWidthConfirm()).attritionRate(dosageAccountReq.getAttritionRate())
                        .reviserName(userContent.getCurrentUserName()).reviserId(userContent.getCurrentUserId()).revisedTime(now)
                        .build())
                .collect(Collectors.toList());

        BomOrder updateBomOrder = BomOrder.builder().bomId(bomOrder.getBomId()).state(BomOrderStateEnum.CALCULATED.getCode())
                .reviserName(userContent.getCurrentUserName()).reviserId(userContent.getCurrentUserId()).revisedTime(now)
                .build();

        //更新特殊辅料核算用量
        List<SpecialAccessories> updateSpecialAccessoriesList = req.getDosageAccountList().stream().filter(dosageAmountReq -> !bomOrderMaterialSet.contains(dosageAmountReq.getBomMaterialId()))
                .map(dosageAccountReq -> SpecialAccessories.builder()
                        .specialAccessoriesId(dosageAccountReq.getBomMaterialId())
                        .dosageAccount(dosageAccountReq.getDosageAccount())
                        .dosageAccountUnit(dosageAccountReq.getDosageAccountUnit())
                        .attritionRate(dosageAccountReq.getAttritionRate())
                        .reviserName(userContent.getCurrentUserName()).reviserId(userContent.getCurrentUserId()).revisedTime(now)
                        .build()).collect(Collectors.toList());

        //处理Bom核算用量日志
        DesignLogReq designLogReq = BomOrderConverter.buildBomOrderLog(bomOrder, "【开发bom表】核算用量完成");
        designLogService.create(designLogReq);

        //保存
        bomOrderMaterialRepository.updateBatchById(updateBomOrderMaterialList);
        bomOrderRepository.updateById(updateBomOrder);
        specialAccessoriesRepository.updateBatchById(updateSpecialAccessoriesList);

        //bom核算更新推送致景
        try {
            zjDesignRemoteHelper.updateBomDosageAccount(req, userContent.getCurrentUserName(), prototype.getBizChannel());
        } catch (Exception e) {
            log.info("===bom核算更新推送致景异常:{}===", e.getMessage());
        }

    }

    /**
     * 同步之前的特殊辅料
     *
     */
    private void syncBeforeSpecialAccessories(Long oldBomId, BomOrder newBomOrder) {

        //将之前特殊辅料未关联Bom的进行关联起来
        List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.getListByDesignCode(newBomOrder.getDesignCode());
        Set<Long> closeAccessoriesFlagIds = new HashSet<>();
        if (CollectionUtil.isNotEmpty(specialAccessoriesList)) {

            specialAccessoriesList.stream().filter(special -> Objects.equals(special.getState(), SpecialAccessoriesStateEnum.CLOSED.getCode()))
                    .forEach(special -> closeAccessoriesFlagIds.add(special.getAccessoriesFlagId()));

            List<SpecialAccessories> updateSpecialAccessories = specialAccessoriesList.stream()
                    .filter(special -> Objects.isNull(special.getBomId()))
                    .filter(special -> !closeAccessoriesFlagIds.contains(special.getAccessoriesFlagId()))
                    .map(special -> {
                        return SpecialAccessories.builder().specialAccessoriesId(special.getSpecialAccessoriesId())
                                .bomId(newBomOrder.getBomId()).build();
                    }).collect(Collectors.toList());
            specialAccessoriesRepository.updateBatchById(updateSpecialAccessories);
        }

        //复制拷贝旧BOM上的特殊辅料(不包含暂存的)-v3.5.1
        List<SpecialAccessories> oldSpecialAccessoriesList = specialAccessoriesRepository.getListByBomId(oldBomId);
        if (CollectionUtil.isEmpty(oldSpecialAccessoriesList)) {
            return;
        }

        List<DesignRemarks> newDesignRemarksList = new ArrayList<>(128);

        List<SpecialAccessories> newSpecialAccessoriesList = oldSpecialAccessoriesList.stream()
                .filter(special -> Objects.equals(special.getState(), SpecialAccessoriesStateEnum.SUBMIT.getCode()))
                .filter(special -> !closeAccessoriesFlagIds.contains(special.getAccessoriesFlagId()))
                .map(oldSpecial -> {
                    SpecialAccessories newSpecial = new SpecialAccessories();
                    BeanUtils.copyProperties(oldSpecial, newSpecial);
                    newSpecial.setSpecialAccessoriesId(IdPool.getId());
                    newSpecial.setBomId(newBomOrder.getBomId());
                    newSpecial.setPrototypeId(newBomOrder.getPrototypeId());

                    //复制之前的备注(包含暂存的)
                    List<DesignRemarks> designRemarksList = designRemarksRepository.getListByBizChildIds(Lists.newArrayList(oldSpecial.getSpecialAccessoriesId()), null);
                    if (CollectionUtil.isNotEmpty(designRemarksList)) {
                        designRemarksList.stream().max(Comparator.comparing(DesignRemarks::getCreatedTime)).ifPresent(oldRemark -> {
                            DesignRemarks newRemarks = new DesignRemarks();
                            BeanUtils.copyProperties(oldRemark, newRemarks);
                            newRemarks.setDesignRemarksId(IdPool.getId());
                            newRemarks.setBizId(newBomOrder.getBomId());
                            newRemarks.setBizChildId(newSpecial.getSpecialAccessoriesId());
                            newDesignRemarksList.add(newRemarks);
                        });
                    }
                    return newSpecial;
                }).collect(Collectors.toList());

        specialAccessoriesRepository.saveBatch(newSpecialAccessoriesList);
        designRemarksRepository.saveBatch(newDesignRemarksList);
    }


    public void purchaseColorCardPicture(List<BomOrderMaterialVo> bomOrderMaterials) {
        log.info("=========================> 色卡图片查询={}", JSONUtil.toJsonStr(bomOrderMaterials));
        if (CollectionUtil.isEmpty(bomOrderMaterials)) {
            return;
        }

        List<Long> materialSnapshotIdList = bomOrderMaterials.stream().map(BomOrderMaterialVo::getMaterialSnapshotId).collect(Collectors.toList());

        List<MaterialColorCardPictureVo> materialColorCardPictureVos = materialPurchaseFollowService.materialColorCardPictureLatest(materialSnapshotIdList);

        if (CollectionUtil.isEmpty(materialColorCardPictureVos)) {
            return;
        }

        Map<Long, MaterialColorCardPictureVo> materialColorCardPictureVoMap = materialColorCardPictureVos.stream().collect(Collectors.toMap(MaterialColorCardPictureVo::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));

        bomOrderMaterials.forEach(bomOrderMaterial -> {
            //历史物料(走履约匹配的), 需要根据matchId来统计采购次数, materialPurchaseFollow表中历史数据的material_snapshot_id就是matchId
            List<Long> materialSnapshotIds = new ArrayList<>();
            materialSnapshotIds.add(bomOrderMaterial.getMaterialSnapshotId());
            List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(materialSnapshotIds);
            Long materialSnapshotId = null;
            if (!CollectionUtils.isEmpty(snapshotList)) {
                for (MaterialSnapshot snapshot : snapshotList) {
                    materialSnapshotId = snapshot.getMaterialSnapshotId();
                    //历史物料(走履约匹配的), 取matchId
                    if (Objects.equals(snapshot.getNewMaterial(), Bool.NO.getCode()) && Objects.nonNull(snapshot.getMatchId())) {
                        materialSnapshotId = snapshot.getMatchId();
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(materialSnapshotId)) {
                MaterialColorCardPictureVo materialColorCardPictureVo = materialColorCardPictureVoMap.get(materialSnapshotId);
                if (ObjectUtil.isNotEmpty(materialColorCardPictureVo)) {
                    bomOrderMaterial.setPurchaseColorCardPictureList(materialColorCardPictureVo.getColorCardPictureUrls());
                }
            }

        });
    }


    /**
     * 查询面料色号不一致或辅料属性不一致的物料信息
     * 注: 历史数据才会存在这种情况
     *
     */
    // public void diffMatchColorSkuAttr(List<Long> materialSnapshotIds, BomOrderDetailResp detailResp) {
    //     if (CollectionUtil.isEmpty(materialSnapshotIds)) {
    //         return;
    //     }
    //
    //     if (ObjectUtil.isEmpty(detailResp)) {
    //         return;
    //     }
    //
    //     List<BomOrderMaterialVo> bomOrderMaterialList = detailResp.getBomOrderMaterialList();
    //
    //     if (CollectionUtil.isEmpty(bomOrderMaterialList)) {
    //         return;
    //     }
    //
    //     Map<Long, BomOrderMaterialVo> bomOrderMaterialVoMap = bomOrderMaterialList.stream().collect(Collectors.toMap(BomOrderMaterialVo::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));
    //
    //     List<MatchColorSkuAttrVo> diffMatchValueList = materialSnapshotService.getDiffMatchValue(materialSnapshotIds);
    //     if (CollectionUtil.isEmpty(diffMatchValueList)) {
    //         return;
    //     }
    //
    //     diffMatchValueList.forEach(dmv -> {
    //         BomOrderMaterialVo bomOrderMaterialVo = bomOrderMaterialVoMap.get(dmv.getMaterialSnapshotId());
    //         if (ObjectUtil.isNotEmpty(bomOrderMaterialVo)) {
    //             bomOrderMaterialVo.setMatchColorSkuAttr(1);
    //         }
    //     });
    // }

    @Override
    public List<OppositeColorLatestVo> getLatestMaterialList(MaterialInfoReq req) {
        List<Long> demandIdList = req.getDemandIdList();
        List<Long> materialSnapshotIdList = req.getMaterialSnapshotIdList();
        //先查物料需求信息
        List<BomMaterialDemand> bomMaterialDemands = bomMaterialDemandRepository.latestBySupplyDemandIds(demandIdList);
        //再查需求有物料的物料信息
        List<Long> bomMaterialDemandIds = bomMaterialDemands.stream().map(BomMaterialDemand::getBomMaterialDemandId).collect(Collectors.toList());
        List<BomOrderMaterial> bomMaterialDemandList = bomOrderMaterialRepository.latestByBomMaterialDemandIds(bomMaterialDemandIds);
        //物料快照id查询物料信息
        List<BomOrderMaterial> bomOrderMaterialList = bomOrderMaterialRepository.latestByMaterialSnapshotId(materialSnapshotIdList);

        List<BomOrderMaterial> allBomOrderMaterials = new ArrayList<>(bomMaterialDemandList);
        allBomOrderMaterials.addAll(bomOrderMaterialList);

        //查询被对色的物料的物料&商品信息
        List<BomOrderMaterial> beOppositeColorMaterialList = bomOrderMaterialRepository.getBeOppositeColorMaterialByColorMatchMaterialName(allBomOrderMaterials);

        List<Long> materialSnapshotIds = allBomOrderMaterials.stream().map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
        materialSnapshotIds.addAll(beOppositeColorMaterialList.stream().map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList()));

		Map<Long, MaterialSnapshot> materialSnapshotMap = materialSnapshotRepository.listByIds(materialSnapshotIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()))
				.stream().collect(Collectors.toMap(MaterialSnapshot::getMaterialSnapshotId, Function.identity(), (v1, v2) -> v1));

        //尚未有物料数据的需求数据处理
        List<OppositeColorLatestVo> oppositeColorLatestVoList = bomMaterialDemands.stream()
                .filter(e -> bomMaterialDemandList.stream().noneMatch(i -> Objects.nonNull(i.getBomMaterialDemandId()) && i.getBomMaterialDemandId().equals(e.getBomMaterialDemandId())))
                .map(item -> {
                    OppositeColorLatestVo oppositeColorLatestVo = new OppositeColorLatestVo();
                    oppositeColorLatestVo.setRelateId(item.getSupplyChainDemandId());
                    oppositeColorLatestVo.setColorMaterialState(item.getColorMatchMaterialState());
                    oppositeColorLatestVo.setOppositeColors(item.getColorMatchMaterialName());
                    oppositeColorLatestVo.setMaterialName(item.getPrototypeMaterialName());
                    return oppositeColorLatestVo;
                }).collect(Collectors.toList());

        //有物料数据的需求数据处理
        oppositeColorLatestVoList.addAll(bomMaterialDemandList.stream().map(item -> {
            OppositeColorLatestVo oppositeColorLatestVo = new OppositeColorLatestVo();
            oppositeColorLatestVo.setRelateId(bomMaterialDemands.stream().filter(e -> e.getBomMaterialDemandId().equals(item.getBomMaterialDemandId()))
                    .map(BomMaterialDemand::getSupplyChainDemandId).findFirst().orElse(null));
            oppositeColorLatestVo.setColorMaterialState(item.getColorMatchMaterialState());
            oppositeColorLatestVo.setOppositeColors(item.getColorMatchMaterialName());
            oppositeColorLatestVo.setMaterialName(item.getPrototypeMaterialName());
            oppositeColorLatestVo.setCommodityCode(materialSnapshotMap.getOrDefault(item.getMaterialSnapshotId(), new MaterialSnapshot()).getCommodityCode());

			//被对色的物料的物料信息
			BomOrderMaterial beOppositeColorOrderMaterial = beOppositeColorMaterialList.stream().filter(e -> e.getPrototypeMaterialName().equals(item.getColorMatchMaterialName()) && e.getBomId().equals(item.getBomId()))
					.findFirst().orElse(null);
			if (Objects.nonNull(beOppositeColorOrderMaterial)) {
				MaterialSnapshot beOppositeMaterialSnapshot = materialSnapshotMap.getOrDefault(beOppositeColorOrderMaterial.getMaterialSnapshotId(), new MaterialSnapshot());
				oppositeColorLatestVo.setOppositeCommodityCode(beOppositeMaterialSnapshot.getCommodityCode());
				oppositeColorLatestVo.setOppositeCommodityId(beOppositeMaterialSnapshot.getCommodityId());
				oppositeColorLatestVo.setOppositeCommodityType(beOppositeMaterialSnapshot.getCommodityType());
				oppositeColorLatestVo.setOppositeSkuId(beOppositeMaterialSnapshot.getSkuId());
				oppositeColorLatestVo.setOppositeMaterialType(beOppositeMaterialSnapshot.getMaterialType());
			}
			return oppositeColorLatestVo;
		}).collect(Collectors.toList()));

        //物料快照id的数据处理
        oppositeColorLatestVoList.addAll(bomOrderMaterialList.stream().map(item -> {
            OppositeColorLatestVo oppositeColorLatestVo = new OppositeColorLatestVo();
            oppositeColorLatestVo.setRelateId(item.getMaterialSnapshotId());
            oppositeColorLatestVo.setColorMaterialState(item.getColorMatchMaterialState());
            oppositeColorLatestVo.setOppositeColors(item.getColorMatchMaterialName());
            oppositeColorLatestVo.setMaterialName(item.getPrototypeMaterialName());
            oppositeColorLatestVo.setCommodityCode(materialSnapshotMap.getOrDefault(item.getMaterialSnapshotId(), new MaterialSnapshot()).getCommodityCode());

			//被对色的物料的物料信息
			BomOrderMaterial beOppositeColorOrderMaterial = beOppositeColorMaterialList.stream().filter(e -> e.getPrototypeMaterialName().equals(item.getColorMatchMaterialName()) && e.getBomId().equals(item.getBomId()))
					.findFirst().orElse(null);
			if (Objects.nonNull(beOppositeColorOrderMaterial)) {
				MaterialSnapshot beOppositeMaterialSnapshot = materialSnapshotMap.getOrDefault(beOppositeColorOrderMaterial.getMaterialSnapshotId(), new MaterialSnapshot());
				oppositeColorLatestVo.setOppositeCommodityCode(beOppositeMaterialSnapshot.getCommodityCode());
				oppositeColorLatestVo.setOppositeCommodityId(beOppositeMaterialSnapshot.getCommodityId());
				oppositeColorLatestVo.setOppositeCommodityType(beOppositeMaterialSnapshot.getCommodityType());
				oppositeColorLatestVo.setOppositeSkuId(beOppositeMaterialSnapshot.getSkuId());
				oppositeColorLatestVo.setOppositeMaterialType(beOppositeMaterialSnapshot.getMaterialType());
			}
			return oppositeColorLatestVo;
		}).collect(Collectors.toList()));

        return oppositeColorLatestVoList;
    }

	@Override
	public List<OppositeColorMaterialVo> getOppositeColorMaterialList(OppositeColorMaterialReq req) {
		List<MaterialPurchaseFollow> materialPurchaseFollowList = materialPurchaseFollowRepository.listByOrderCodes(req.getOrderCode());
		List<Long> materialSnapshotIds = materialPurchaseFollowList.stream().map(MaterialPurchaseFollow::getMaterialSnapshotId).collect(Collectors.toList());
		//被对色物料信息
		List<BomOrderMaterial> beOppositeColorBomOrderMaterialList = bomOrderMaterialRepository.latestByMaterialSnapshotId(materialSnapshotIds);
		//对色的物料信息
		List<BomOrderMaterial> oppositeColorMaterialList = bomOrderMaterialRepository.getOppositeColorMaterialByRelateMaterialName(beOppositeColorBomOrderMaterialList);

        List<Long> oppositeColorMaterialBomMaterialDemandIds = oppositeColorMaterialList.stream().map(BomOrderMaterial::getBomMaterialDemandId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> oppositeColorMaterialMaterialSnapshotIds = oppositeColorMaterialList.stream().map(BomOrderMaterial::getMaterialSnapshotId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> oppositeColorMaterialBomMaterialIds = oppositeColorMaterialList.stream().map(BomOrderMaterial::getBomMaterialId)
                .collect(Collectors.toList());

        //对色的物料商品信息
        List<MaterialSnapshot> oppositeColorMaterialSnapshots = materialSnapshotRepository.listByIds(oppositeColorMaterialMaterialSnapshotIds);
        //对色的物料需求信息
        List<BomMaterialDemand> oppositeColorMaterialDemands = bomMaterialDemandRepository.listByIds(oppositeColorMaterialBomMaterialDemandIds);
        //对色的物料下的采购单
        List<MaterialPurchaseFollow> oppositeColorMaterialMaterialPurchaseFollows = materialPurchaseFollowRepository.listByBomMaterialIds(oppositeColorMaterialBomMaterialIds);

		return beOppositeColorBomOrderMaterialList.stream().map(item -> {
			MaterialPurchaseFollow materialPurchaseFollow = materialPurchaseFollowList.stream()
					.filter(e -> e.getMaterialSnapshotId().equals(item.getMaterialSnapshotId())).findFirst().orElseGet(MaterialPurchaseFollow::new);
			OppositeColorMaterialVo beOppositeColorMaterialVo = new OppositeColorMaterialVo();
			beOppositeColorMaterialVo.setOrderCode(materialPurchaseFollow.getCuttingCode());
			beOppositeColorMaterialVo.setMaterialName(item.getPrototypeMaterialName());

            //对色被对色物料的物料信息筛选
            List<BomOrderMaterial> oppositeColorMaterials = oppositeColorMaterialList.stream()
                    .filter(e -> item.getBomId().equals(e.getBomId()) && item.getPrototypeMaterialName().equals(e.getColorMatchMaterialName()))
                    .collect(Collectors.toList());
            List<OppositeColorMaterialVo.OppositeColorInfo> oppositeColorInfoList = oppositeColorMaterials.stream().map(info -> {
                OppositeColorMaterialVo.OppositeColorInfo oppositeColorInfo = new OppositeColorMaterialVo.OppositeColorInfo();
                oppositeColorInfo.setColorMaterialState(info.getColorMatchMaterialState());
                oppositeColorInfo.setMaterialName(info.getPrototypeMaterialName());
                //需求信息返回
                BomMaterialDemand bomMaterialDemand = oppositeColorMaterialDemands.stream()
                        .filter(e -> e.getBomMaterialDemandId().equals(info.getBomMaterialDemandId())).findFirst().orElseGet(BomMaterialDemand::new);
                oppositeColorInfo.setDemandId(bomMaterialDemand.getSupplyChainDemandId());
                oppositeColorInfo.setDemandCode(bomMaterialDemand.getSupplyChainDemandCode());
                //物料商品信息返回
                MaterialSnapshot materialSnapshot = oppositeColorMaterialSnapshots.stream()
                        .filter(e -> e.getMaterialSnapshotId().equals(info.getMaterialSnapshotId())).findFirst().orElseGet(MaterialSnapshot::new);
                oppositeColorInfo.setCommodityCode(materialSnapshot.getCommodityCode());
                //最新下采购单返回
                MaterialPurchaseFollow purchaseFollow = oppositeColorMaterialMaterialPurchaseFollows.stream()
                        .filter(e -> e.getBomMaterialId().equals(info.getBomMaterialId()))
                        .max(Comparator.comparing(BaseEntity::getCreatedTime)).orElseGet(MaterialPurchaseFollow::new);
                oppositeColorInfo.setLatestOrderCode(purchaseFollow.getCuttingCode());
                return oppositeColorInfo;
            }).collect(Collectors.toList());

            beOppositeColorMaterialVo.setBeOppositeColorInfoList(oppositeColorInfoList);
            return beOppositeColorMaterialVo;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateLatestPriceByDesignCode(String designCode) {
        BomOrder latestSubmitBom = bomOrderRepository.getLatestSubmitBom(designCode);
        if (Objects.isNull(latestSubmitBom)) {
            return;
        }
        Long latestSubmitBomBomId = latestSubmitBom.getBomId();
        var bomOrderMaterialList = bomOrderMaterialRepository.getNormalListByBomId(latestSubmitBomBomId);
        var specialAccessories = specialAccessoriesRepository.getListByBomId(latestSubmitBomBomId);
        if (CollUtil.isEmpty(bomOrderMaterialList) && CollUtil.isEmpty(specialAccessories)) {
            return;
        }

        var bomOrderMaterialVos = queryBaseCommonInfo(bomOrderMaterialList, specialAccessories);

        //根据skuId查询好料网最新物料信息
        Map<Integer, Set<Long>> bomMaterialTypeMap = bomOrderMaterialVos.stream().collect(
                Collectors.groupingBy(BomOrderMaterialVo::getDemandType, Collectors.mapping(BomOrderMaterialVo::getSkuId, Collectors.toSet())));

        //需要查询的sku
        Map<String, Long> querySpuSkuMap = StreamUtil.list2MapWithValue(bomOrderMaterialVos, BomOrderMaterialVo::getSpuSkuId, BomOrderMaterialVo::getBomMaterialId);

        //辅料商品(包含特殊辅料)
        Set<Long> accessoriesSkuIds = new HashSet<>();
        Set<Long> normalAccessoriesSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.ACCESSORIES.getCode());
        Set<Long> specialAccessoriesSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode());
        if (CollUtil.isNotEmpty(normalAccessoriesSkuIds)) {
            accessoriesSkuIds.addAll(normalAccessoriesSkuIds);
        }
        if (CollUtil.isNotEmpty(specialAccessoriesSkuIds)) {
            accessoriesSkuIds.addAll(specialAccessoriesSkuIds);
        }
        List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream()
                .collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
        Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus)
                .flatMap(Collection::stream)
                //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
                .filter(item -> Objects.nonNull(querySpuSkuMap.get(BomOrderConverter.getAccessoriesSpuSkuId(item))))
                .collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));

        //面料商品
        Set<Long> fabricSkuIds = bomMaterialTypeMap.get(MaterialDemandTypeEnum.FABRIC.getCode());
        List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(fabricSkuIds, querySpuSkuMap);
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = StreamUtil.list2Map(fabricSkuInfos, BomOrderConverter::getLyFabricSpuSkuId);

        ArrayList<BomOrderMaterial> updateOrderMaterials = new ArrayList<>(bomOrderMaterialVos.size());
        ArrayList<SpecialAccessories> updateSpecial = new ArrayList<>(specialAccessories.size());

        bomOrderMaterialVos.forEach(material -> {

            if (Objects.equals(material.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
                var bomOrderMaterial = checkReturnFabricMaterial(material, fabricSkuMap.get(material.getSpuSkuId()));
                if (Objects.nonNull(bomOrderMaterial)) {
                    updateOrderMaterials.add(bomOrderMaterial);
                }
            }

            if (Objects.equals(material.getDemandType(), MaterialDemandTypeEnum.ACCESSORIES.getCode())) {
                var productSkuVo = checkReturnAccessoriesMaterial(accessoriesSkuMap.get(material.getSkuId()), accessoriesSpuMap.get(material.getCommodityId()));
                if (Objects.nonNull(productSkuVo)) {
                    var orderMaterial = BomOrderMaterial.builder()
                            .bomMaterialId(material.getBomMaterialId())
                            .minPrice(productSkuVo.getMinPrice())
                            .priceInvalidTime(productSkuVo.getPriceExpireEnd())
                            .build();
                    updateOrderMaterials.add(orderMaterial);
                }
            }


            if (Objects.equals(material.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())) {
                var productSkuVo = checkReturnAccessoriesMaterial(accessoriesSkuMap.get(material.getSkuId()), accessoriesSpuMap.get(material.getCommodityId()));
                if(Objects.nonNull(productSkuVo)){
                    var accessories = SpecialAccessories.builder()
                            .specialAccessoriesId(material.getBomMaterialId())
                            .minPrice(productSkuVo.getMinPrice())
                            .priceInvalidTime(productSkuVo.getPriceExpireEnd())
                            .build();
                    updateSpecial.add(accessories);
                }
            }
        });

        if (CollUtil.isNotEmpty(updateOrderMaterials)) {
            bomOrderMaterialRepository.updateBatchById(updateOrderMaterials);
        }

        if (CollUtil.isNotEmpty(updateSpecial)) {
            specialAccessoriesRepository.updateBatchById(updateSpecial);

            //更新BOM修改时间, bom的更新时间要在特殊辅料最新的更新时间之后,BOM提交时效验才通过,避免编辑页面bom内容不是最新的提示
            bomOrderRepository.updateById(BomOrder.builder().bomId(latestSubmitBomBomId).build());
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateLatestPriceByBomId(Long bomId) {
        if (Objects.isNull(bomId)) {
            return;
        }
        var entityByBomId = bomOrderRepository.getEntityByBomId(bomId);
        if (Objects.isNull(entityByBomId)) {
            return;
        }
        this.updateLatestPriceByDesignCode(entityByBomId.getDesignCode());
    }

    @Override
    public List<BomMaterialCheckCountVo> listBomMaterialCheckCount(BomMaterialReq req) {
        List<BomOrderMaterial> materialList = bomOrderMaterialRepository.listByIds(req.getBomMaterialIdList());
        if (CollUtil.isEmpty(materialList)) {
            return Collections.emptyList();
        }
        //物料快照
        List<Long> snapshotIdList = StreamUtil.convertListAndDistinct(materialList, BomOrderMaterial::getMaterialSnapshotId);
        List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(snapshotIdList);
        Map<Long, MaterialSnapshot> snapshotMap = StreamUtil.list2Map(snapshotList, MaterialSnapshot::getMaterialSnapshotId);

        return materialList.stream().map(item -> {
            BomMaterialCheckCountVo materialVo = new BomMaterialCheckCountVo();
            BeanUtils.copyProperties(item, materialVo);
            MaterialSnapshot snapshot = snapshotMap.get(item.getMaterialSnapshotId());
            if (Objects.nonNull(snapshot)) {
                BeanUtils.copyProperties(snapshot, materialVo);
            }
            return materialVo;
        }).toList();
    }

    @Override
    public BomOrderBaseResp getBomBaseBySkc(String designCode) {
        if (StrUtil.isBlank(designCode)) {
            return null;
        }
        BomOrder bomOrder = bomOrderRepository.getLatestBomByDesignCode(designCode);
        if (Objects.isNull(bomOrder)) {
            return null;
        }
        BomOrderBaseResp resp = new BomOrderBaseResp();
        BeanUtils.copyProperties(bomOrder, resp);
        resp.setBomOrderState(BomOrderStateEnum.findEntityByCode(bomOrder.getState()));
        return resp;
    }

    @Override
    public List<BomMaterialExcelResp> exportMaterialExcel(BomMaterialExportQuery query) {
        //先条件查询获取bomId
        this.setExportBomIdList(query);
        if (CollUtil.isEmpty(query.getExportBomIdList())) {
            return Collections.emptyList();
        }
        BomMaterialExportReq req = new BomMaterialExportReq();
        req.setExportBomIdList(query.getExportBomIdList());

        //字典
        List<DictVo> dictResp = dictValueRemoteHelper.listByDictCodes(List.of(DictConstant.BOM_CUTTING_METHOD_CODE));
        Map<String, DictVo> dictValueMap = dictResp.stream().collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));

        //分批查询导出的bom物料
        int handleSize = 500;
        List<BomMaterialExcelResp> resultList = new LinkedList<>();
        List<List<Long>> splitBomIdList = CollectionUtil.split(req.getExportBomIdList(), handleSize);
        for (List<Long> bomIdList : splitBomIdList) {
            //bom单
            List<BomOrder> bomOrderList = bomOrderRepository.listByIds(bomIdList);
            if (CollUtil.isEmpty(bomOrderList)) {
                return Collections.emptyList();
            }
            //skc
            List<String> designCodeList = StreamUtil.convertListAndDistinct(bomOrderList, BomOrder::getDesignCode);
            List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
            if (CollUtil.isEmpty(prototypeList)) {
                return Collections.emptyList();
            }
            Map<String, BomOrder> bomOrderMap = StreamUtil.list2Map(bomOrderList, BomOrder::getDesignCode);

            //bom单导出物料信息
            List<BomMaterialExcelResp> excelRespList = this.getMaterialExcelList(bomIdList, dictValueMap);
            if (CollUtil.isEmpty(excelRespList)) {
                return Collections.emptyList();
            }
            Map<Long, List<BomMaterialExcelResp>> materialExcelGroup = StreamUtil.groupingBy(excelRespList, BomMaterialExcelResp::getBomId);

            //设置skc信息
            for (Prototype skc : prototypeList) {
                BomOrder bomOrder = bomOrderMap.get(skc.getDesignCode());
                List<BomMaterialExcelResp> bomMaterialExcelRespList = materialExcelGroup.get(bomOrder.getBomId());
                //跳过无物料的bom单
                if (CollUtil.isEmpty(bomMaterialExcelRespList)) {
                    continue;
                }
                bomMaterialExcelRespList.forEach(item -> {
                    item.setStyleCode(skc.getStyleCode());
                    item.setDesignCode(skc.getDesignCode());
                });
                resultList.addAll(bomMaterialExcelRespList);
            }
            bomOrderList = null;
            prototypeList = null;
            bomOrderMap = null;
            excelRespList = null;
            materialExcelGroup = null;
        }

        return resultList;
    }

    private List<BomMaterialExcelResp> getMaterialExcelList(List<Long> bomIdList, Map<String, DictVo> dictValueMap) {
        List<BomMaterialExcelResp> excelRespList = new LinkedList<>();
        //选料(面辅料查询)
        List<BomMaterialExcelResp>  chooseMaterialList = bomOrderMaterialRepository.listMaterialExcel(bomIdList);
        if (CollUtil.isNotEmpty(chooseMaterialList)) {
            DictVo bomCuttingMethodDict = dictValueMap.get(DictConstant.BOM_CUTTING_METHOD_CODE);
            chooseMaterialList.forEach(resp -> {
                //裁剪方法
                if (Objects.nonNull(bomCuttingMethodDict) && CollectionUtil.isNotEmpty(bomCuttingMethodDict.getChildren())) {
                    bomCuttingMethodDict.getChildren().stream().filter(dictValueVo -> Objects.equals(resp.getCuttingMethod(), Integer.valueOf(dictValueVo.getDictCode())))
                            .findFirst().ifPresent(dictValueVo -> resp.setCuttingMethodName(dictValueVo.getDictName()));
                }
                //对色信息
                resp.setCuttingColorInfo(this.buildCuttingColorInfo(resp));
            });
            excelRespList.addAll(chooseMaterialList);
            chooseMaterialList = null;
        }
        //找料物料查询
        List<BomMaterialExcelResp>  demandMaterialList = bomOrderMaterialRepository.listMaterialDemandExcel(bomIdList);
        if (CollUtil.isNotEmpty(demandMaterialList)) {
            demandMaterialList.forEach(resp -> {
                resp.setCuttingColorInfo(this.buildCuttingColorInfo(resp));
            });
            excelRespList.addAll(demandMaterialList);
            demandMaterialList = null;
        }
        //特辅物料查询
        List<BomMaterialExcelResp>  specailMaterialList = specialAccessoriesRepository.listMaterialExcel(bomIdList);
        if (CollUtil.isNotEmpty(specailMaterialList)) {
            excelRespList.addAll(specailMaterialList);
            specailMaterialList = null;
        }
        return excelRespList;
    }

    private String buildCuttingColorInfo(BomMaterialExcelResp resp) {
        String cuttingColorInfo = null;
        if (Objects.equals(resp.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
            cuttingColorInfo = resp.getCuttingMethodName();
        }
        else if (!Objects.equals(resp.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())
                && !Objects.equals(resp.getColorMatchMaterialState(), BomColorMaterialStateEnum.NO_THING.getCode())) {
            cuttingColorInfo = BomColorMaterialStateEnum.findEntityByCode(resp.getColorMatchMaterialState()).getDesc()
                    + StrUtil.COLON + StrUtil.SPACE + resp.getColorMatchMaterialName();
        }
        return cuttingColorInfo;
    }

    private void setExportBomIdList(BomMaterialExportQuery query) {
        if (CollectionUtil.isNotEmpty(query.getExportBomIdList())) {
            return;
        }
        BomOrderListQuery bomOrderListQuery = new BomOrderListQuery();
        BeanUtils.copyProperties(query, bomOrderListQuery);
        bomOrderListQuery.setBomOrderState(Optional.ofNullable(query.getBomOrderState()).map(BomOrderStateEnum::getCode).orElse(null));
        UserContent userContent = UserContentHolder.get();
        //根据设计组查询
        if(Objects.equals(query.getClothesDesigner(), Bool.YES.getCode())){
            List<String> designerGroupCodeList = this.queryDesignerGroup(query, userContent);
            if (CollUtil.isEmpty(designerGroupCodeList)) {
                // 若用户不属于任意设计组则展示空数据
                return;
            }
            bomOrderListQuery.setDesignerGroupCodeList(designerGroupCodeList);
        }
        //最多导出1千条bom
        Page<BomOrderListDto> page = PageHelper.startPage(1, 1_000)
                .doSelectPage(() -> bomOrderRepository.pageQuery(bomOrderListQuery));
        List<BomOrderListDto> bomOrderList = page.getResult();
        if (CollUtil.isEmpty(bomOrderList)) {
            return;
        }
        List<Long> bomIdList = StreamUtil.convertListAndDistinct(bomOrderList, BomOrderListDto::getBomId);
        query.setExportBomIdList(bomIdList);
    }

    @Override
    public List<BomMaterialPictureResp> queryMaterialPicture(BomMaterialPictureReq req) {
        List<String> designCodeList = req.getDesignCodeList();
        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
        if (CollectionUtil.isEmpty(prototypeList)) {
            return Collections.emptyList();
        }

        //skc下最新版本bom
        List<BomOrder> bomOrderList = bomOrderRepository.getLatestBomOrderList(designCodeList, null, false);
        if (CollectionUtil.isEmpty(bomOrderList)) {
            return Collections.emptyList();
        }
        Map<String, BomOrder> bomOrderMap = StreamUtil.list2Map(bomOrderList, BomOrder::getDesignCode);

        List<Long> bomIdList = StreamUtil.convertList(bomOrderList, BomOrder::getBomId);
        List<BomOrderMaterial> bomMaterialList = new LinkedList<>();
        List<Long> materialSnapshotIdList = new LinkedList<>();

        //选料的辅料
        List<BomOrderMaterial> chooseMaterialList = bomOrderMaterialRepository.listChooseMaterial(bomIdList, MaterialTypeEnum.ACCESSORY.getCode());
        if (CollUtil.isNotEmpty(chooseMaterialList)) {
            bomMaterialList.addAll(chooseMaterialList);
            List<Long> chooseSnapshotIdList = StreamUtil.convertList(chooseMaterialList, BomOrderMaterial::getMaterialSnapshotId);
            materialSnapshotIdList.addAll(chooseSnapshotIdList);
        }
        //找料需求已回复的辅料
        List<BomOrderMaterial> demandMaterialList = bomOrderMaterialRepository.listDemandMaterialByBomIds(bomIdList, true);
        if (CollUtil.isNotEmpty(demandMaterialList)) {
            bomMaterialList.addAll(demandMaterialList);
            List<Long> demandSnapshotIdList = StreamUtil.convertList(demandMaterialList, BomOrderMaterial::getMaterialSnapshotId);
            materialSnapshotIdList.addAll(demandSnapshotIdList);
        }
        //特辅
        List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.listByBomIds(bomIdList, Bool.NO.getCode());

        //bom物料分组
        Map<Long, List<BomOrderMaterial>> bomMaterialGroup = StreamUtil.groupingBy(bomMaterialList, BomOrderMaterial::getBomId);
        Map<Long, List<SpecialAccessories>> specialGroup = StreamUtil.groupingBy(specialAccessoriesList, SpecialAccessories::getBomId);

        //物料快照
        List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(materialSnapshotIdList);
        Map<Long, MaterialSnapshot> materialSnapshotMap = StreamUtil.list2Map(snapshotList, MaterialSnapshot::getMaterialSnapshotId);

        //查询中台辅料图片
        Map<String, List<String>> commodityPicturesMap = this.getAccessoriesPictureMap(snapshotList);

        //封装出参
        return prototypeList.stream().map(skc -> {
            BomMaterialPictureResp resp = new BomMaterialPictureResp();
            resp.setStyleCode(skc.getStyleCode());
            resp.setDesignCode(skc.getDesignCode());
            BomOrder bomOrder = bomOrderMap.get(skc.getDesignCode());
            this.setAccessoriesPictureList(bomOrder, bomMaterialGroup, specialGroup, materialSnapshotMap, commodityPicturesMap, resp);
            return resp;
        }).toList();
    }

    private void setAccessoriesPictureList(BomOrder bomOrder, Map<Long, List<BomOrderMaterial>> bomMaterialGroup, Map<Long, List<SpecialAccessories>> specialGroup, Map<Long, MaterialSnapshot> materialSnapshotMap, Map<String, List<String>> commodityPicturesMap, BomMaterialPictureResp resp) {
        if (Objects.isNull(bomOrder) || CollUtil.isEmpty(bomMaterialGroup)) {
            return;
        }
        List<String> accessoriesPictureList = new LinkedList<>();
        //辅料图片
        List<BomOrderMaterial> materialList = bomMaterialGroup.get(bomOrder.getBomId());
        if (CollUtil.isNotEmpty(materialList)) {
            for (BomOrderMaterial material : materialList) {
                MaterialSnapshot snapshot = materialSnapshotMap.get(material.getMaterialSnapshotId());
                List<String> itemPictureList = commodityPicturesMap.get(snapshot.getSpuSkuId());
                if (CollUtil.isEmpty(itemPictureList)) {
                    continue;
                }
                //第一张图
                accessoriesPictureList.add(itemPictureList.getFirst());
            }
        }
        //特辅图片
        List<SpecialAccessories> specialList = specialGroup.get(bomOrder.getBomId());
        if (CollUtil.isNotEmpty(specialList)) {
            for (SpecialAccessories special : specialList) {
                String skuPicture = special.getSkuPicture();
                if (StrUtil.isNotBlank(skuPicture)) {
                    List<String> pictureList = StrUtil.splitTrim(skuPicture, StrUtil.COMMA);
                    accessoriesPictureList.add(pictureList.getFirst());
                }
            }
        }
        resp.setAccessoriesPictureList(accessoriesPictureList);
    }

    private Map<String, List<String>> getAccessoriesPictureMap(List<MaterialSnapshot> snapshotList) {
        if (CollUtil.isEmpty(snapshotList)) {
            return Collections.emptyMap();
        }
        //需要查询的sku
        Set<Long> skuIdList = StreamUtil.convertSet(snapshotList, MaterialSnapshot::getSkuId);
        Map<String, Long> querySpuSkuMap = StreamUtil.list2MapWithValue(snapshotList, MaterialSnapshot::getSpuSkuId, MaterialSnapshot::getMaterialSnapshotId);
        List<ProductSpuInfoVo> accessoriesSkuInfoList = productRemoteHelper.getAccessoriesSkuInfo(skuIdList);
        Map<String, List<String>> commodityPicturesMap = new HashMap<>();
        accessoriesSkuInfoList.forEach(product -> {
            List<String> picturesList = new ArrayList<>();
            Map<Long, ProductSkuVo> skuMap = product.getSkus().stream()
                    //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
                    .filter(item -> Objects.nonNull(querySpuSkuMap.get(BomOrderConverter.getAccessoriesSpuSkuId(item))))
                    .collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity()));
            skuMap.forEach((key, value) -> {
                picturesList.addAll(value.getPictures().stream().map(ProductPictureBaseVo::getPicturePath).toList());
                picturesList.addAll(product.getPictures().stream().map(ProductPictureVo::getPicturePath).toList());
                commodityPicturesMap.put(BomOrderConverter.getAccessoriesSpuSkuId(value), picturesList);
            });
        });
        return commodityPicturesMap;
    }

    private List<BomOrderMaterialVo> queryBaseCommonInfo(List<BomOrderMaterial> orderMaterials, List<SpecialAccessories> accessories) {
        ArrayList<BomOrderMaterialVo> materialVos = new ArrayList<>();

        if (CollUtil.isNotEmpty(orderMaterials)) {
            //面料辅料
            var materialSnapshotIds = orderMaterials.stream()
                    .filter(o -> Objects.equals(o.getMaterialState(), BomMaterialStateEnum.NORMAL.getCode()))
                    .map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
            var materialSnapshotVos = materialSnapshotService.listByIds(materialSnapshotIds);
            var snapshotVoMap = materialSnapshotVos.stream()
                    .collect(Collectors.toMap(MaterialSnapshotVo::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));

            orderMaterials.forEach(m -> {
                BomOrderMaterialVo materialVo = new BomOrderMaterialVo();
                if (Objects.nonNull(snapshotVoMap.get(m.getMaterialSnapshotId()))) {
                    Beans.copyIgnoreNull(snapshotVoMap.get(m.getMaterialSnapshotId()), materialVo);
                    materialVo.setDemandType(snapshotVoMap.get(m.getMaterialSnapshotId()).getMaterialType());
                }
                Beans.copyIgnoreNull(m, materialVo);
                materialVos.add(materialVo);
            });
        }

        //特殊辅料
        if (CollUtil.isNotEmpty(accessories)) {
            List<BomOrderMaterialVo> specialMaterialVos = accessories.stream().filter(special -> Objects.equals(special.getState(), SpecialAccessoriesStateEnum.SUBMIT.getCode())).map(a -> {
                BomOrderMaterialVo materialVo = new BomOrderMaterialVo();
                Beans.copyIgnoreNull(a, materialVo);
                materialVo.setBomMaterialId(a.getSpecialAccessoriesId());
                materialVo.setDemandType(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode());
                materialVo.setCommodityId(a.getSpuId());
                return materialVo;
            }).collect(Collectors.toList());
            materialVos.addAll(specialMaterialVos);
        }
        return materialVos;
    }


    /**
     * 检查判断面料返回
     *
     */
    public BomOrderMaterial checkReturnFabricMaterial(BomOrderMaterialVo materialVo, CommoditySkuCollectionRespVo.Sku skuVo) {
        if (Objects.nonNull(skuVo) && CollectionUtil.isNotEmpty(skuVo.getSkuPriceVos())) {

            BomOrderMaterial orderMaterial = null;

            var priceVo = skuVo.getSkuPriceVos().stream()
                    .filter(skuPriceVo -> Objects.equals(skuPriceVo.getSalesRegionId(), DefaultSalesRegionEnum.NATION.getRegionId()))
                    .findFirst().orElse(null);

            if (Objects.nonNull(priceVo) && Objects.nonNull(priceVo.getMeterPrice()) && Objects.nonNull(priceVo.getValidityEndTime())) {
                orderMaterial = BomOrderMaterial.builder()
                        .bomMaterialId(materialVo.getBomMaterialId())
                        .meterPrice(priceVo.getMeterPrice())
                        .priceInvalidTime(priceVo.getValidityEndTime())
                        .build();
            }
            return orderMaterial;
        } else {
            return null;
        }
    }


    /**
     * 检查判断辅料返回
     *
     */
    public ProductSkuVo checkReturnAccessoriesMaterial(ProductSkuVo productSkuVo,
                                                       ProductSpuInfoVo productSpuInfoVo) {
        if (Objects.nonNull(productSkuVo) && Objects.nonNull(productSpuInfoVo)
                && Objects.nonNull(productSkuVo.getMinPrice())
                && StringUtils.isNotBlank(productSkuVo.getMinUnit())
                && Objects.nonNull(productSkuVo.getPriceExpireEnd())) {
            return productSkuVo;
        }
        return null;
    }
}
