package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.download.DownloadTaskHandleService;

/**
 *
 * 任务下载-定时任务调用controller
 *
 */


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/jobs/download-task")
public class JobDownloadTaskController {
    private final DownloadTaskHandleService downloadTaskHandleService;

    /**
     * 处理下载任务
     * @return Void
     */
    @NoRepeatSubmitLock
    @PostMapping("/process")
    public DataResponse<Void> processTasks() {
        downloadTaskHandleService.processTasks();
        return DataResponse.ok();
    }

}
