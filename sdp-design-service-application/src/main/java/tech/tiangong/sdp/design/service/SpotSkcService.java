package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.entity.SpotSkc;
import tech.tiangong.sdp.design.entity.SpotSpu;
import tech.tiangong.sdp.design.vo.dto.product.CreateProductCallbackDto;
import tech.tiangong.sdp.design.vo.dto.spot.SpotPickStyleCreateDto;
import tech.tiangong.sdp.design.vo.query.spot.SpotSkcQuery;
import tech.tiangong.sdp.design.vo.req.spot.*;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcForOfpVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcUpdateVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcVo;
import tech.tiangong.sdp.design.vo.resp.style.PopCreateProductVo;

import java.util.List;

/**
 * spot_skc表(SpotSkc)服务接口
 *
 * <AUTHOR>
 * @since 2025-02-25 11:37:13
 */
public interface SpotSkcService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<SpotSkcVo> page(SpotSkcQuery query);

    /**
     * 复色SKC
     *
     * @param req     入参
     * @param spotSpu
     * @return 复色后skc
     */
    SpotSkc colorMaking(SpotColorMakingReq req, SpotSpu spotSpu);

    /**
     * 创建skc-新建款号
     *
     * @param styleCode  spu编码
     * @param req 入参
     * @return List<SpotSkc>
     */
    List<SpotSkc> create4SelfCreate(String styleCode, SpotSpuSelfCreateReq req);

    /**
     * 创建skc-选款
     *
     * @param styleCode  spu编码
     * @param req 入参
     * @return SpotSkc
     */
    SpotSkc create4PickStyle(String styleCode, SpotPickStyleCreateDto req);

    /**
     * 创建skc-SPU导入
     * @param spotSpuEo spu
     * @return skc
     */
    SpotSkc create4ImportSpu(SpotSpu spotSpuEo);

    /**
     * 批量创建skc-excel导入
     * @param spotSpuList spu集合
     * @return skc集合
     */
    List<SpotSkc> create4ExcelImport(List<SpotSpu> spotSpuList);

    /**
     * 选款创建skc-spu已存在时
     *
     * @param styleCode    spu
     * @param skcReq       入参
     * @return skc
     */
    SpotSkc create4PickStyleWithSpu(String styleCode, SpotPickStyleCreateDto skcReq);

    /**
     * 视觉弹框校验-编辑skc
     *
     * @param req 入参
     * @return 异常信息集合(为空时可以发起视觉需求, 非空时不提交视觉需求)
     */
    List<String> visualCheck(SpotSkcVisualCheckReq req);

    /**
     * 编辑SKC
     *
     * @param req 请求参数
     * @return 响应结果
     */
    SpotSkcUpdateVo update(SpotSkcUpdateReq req);

    /**
     * 批量更新SKC
     *
     * @param styleCode spu
     * @param skcUpdateList 入参
     */
    void updateBatch(String styleCode, List<SpotSkcUpdateReq> skcUpdateList);

    /**
     * 批量取消SKC
     *
     * @param req 请求参数
     */
    void batchCancel(SpotSkcBatchCancelReq req);

    /**
     * 同步现货SKC的首张采购单信息
     * @param req
     */
    void syncSpotSkcFirstPurchaseOrder(SyncSpotSkcPurchaseOrderReq req);

    /**
     * 根据来源分页查询SKC（可查现货管理的款或者买手系统的款）
     * @param req
     */
    PageRespVo<SpotSkcForOfpVo> querySkcForOfp(QuerySkcForOfpReq req);

    /**
     * 查询SKC列表（包含旧JV买手系统的款式）
     * @param req
     */
    List<SpotSkcForOfpVo> listSkcForOfp(ListSkcForOfpReq req);

    /**
     * 以spu维度推送SKC信息到商品平台
     * @param spotSpu
     */
    boolean pushPopProductBySpotSpu(SpotSpu spotSpu);

    /**
     * 校验spu下skc是否存在相同颜色
     * @param styleCode spu
     * @param color 颜色名称
     */
    Boolean checkSpuSameColor(String styleCode, String color);

    /**
     * 商品平台创建商品回调处理
     * @param list
     */
    @Deprecated
    void createProductCallBackHandle(List<CreateProductCallbackDto> list);

    /**
     * 根据spu查询pop创建商品的信息
     * @param styleCodeList spu集合
     * @return pop创建商品信息
     */
    List<PopCreateProductVo> queryStyleInfo4Pop(List<String> styleCodeList);

}
