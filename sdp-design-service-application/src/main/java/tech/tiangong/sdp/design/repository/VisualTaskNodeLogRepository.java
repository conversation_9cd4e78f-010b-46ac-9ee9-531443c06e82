package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.VisualTaskNodeLog;
import tech.tiangong.sdp.design.enums.visual.VisualTaskNodeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskStepEnum;
import tech.tiangong.sdp.design.mapper.VisualTaskNodeLogMapper;

/**
 * 视觉任务处理环节节点状态流转服务仓库类
 *
 */
@Repository
public class VisualTaskNodeLogRepository extends BaseRepository<VisualTaskNodeLogMapper, VisualTaskNodeLog> {
    public void saveNodeLog(Long taskId,
                            VisualTaskStepEnum visualTaskStep, VisualTaskNodeEnum visualTaskNode,
                            Integer nodeState){
        VisualTaskNodeLog visualTaskNodeLog = VisualTaskNodeLog.builder()
                .nodeLogId(IdPool.getId())
                .taskId(taskId)
                .processStep(visualTaskStep.getCode())
                .processNode(visualTaskNode.getCode())
                .processNodeState(nodeState)
                .remark(visualTaskNode.getDesc()+":"+visualTaskNode.getStateEnum().getDescByCode(nodeState))
                .build();
        this.save(visualTaskNodeLog);
    }
}