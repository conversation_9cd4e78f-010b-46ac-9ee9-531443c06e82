package tech.tiangong.sdp.design.repository;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.mapper.PrototypeManageMapper;
import tech.tiangong.sdp.design.vo.query.prototype.PrototypeManageCancelQuery;
import tech.tiangong.sdp.design.vo.query.prototype.PrototypeManageQuery;
import tech.tiangong.sdp.design.vo.req.manage.PrototypeExcelReq;
import tech.tiangong.sdp.design.vo.resp.prototype.*;

import java.util.List;
import java.util.Objects;

/**
 * 版单管理-服务仓库类
 *
 * <AUTHOR>
 * @since 2021-08-17 15:52:50
 */
@AllArgsConstructor
@Repository
public class PrototypeManageRepository {
    private final PrototypeManageMapper prototypeManageMapper;

    public List<PrototypeManageQueryResp> listQuery(PrototypeManageQuery queryDTO) {
        if (Objects.nonNull(queryDTO.getIsCraft())) {
            return prototypeManageMapper.listMoreQuery(queryDTO);
        }
        return prototypeManageMapper.listQuery(queryDTO);
    }

    public List<PrototypeManageCancelQueryResp> listCancelQuery(PrototypeManageCancelQuery queryDTO) {
        return prototypeManageMapper.listCancelQuery(queryDTO);
    }

    public List<PrototypeExcelResp> listExcel(PrototypeExcelReq req) {
        return prototypeManageMapper.listExcel(req);
    }

    public List<BomOrderMaterialExcelResp> listBomOrderMaterialExcel(List<Long> bomIdList) {
        return prototypeManageMapper.listBomOrderMaterialExcel(bomIdList);
    }

    public List<PrototypeZipResp> listZip(PrototypeExcelReq req) {
        return prototypeManageMapper.listZip(req);
    }


}