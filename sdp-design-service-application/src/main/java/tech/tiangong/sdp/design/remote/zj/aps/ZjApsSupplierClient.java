package  tech.tiangong.sdp.design.remote.zj.aps;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import team.aikero.blade.core.protocol.DataResponse;
import tech.tiangong.sdp.core.config.ZjOpenFeignUserContentConfig;
import tech.tiangong.sdp.design.vo.req.zj.aps.SupplierInfoDataReq;
import tech.tiangong.sdp.design.vo.resp.zj.aps.SupplierSimpleResp;

import java.util.List;

@FeignClient(value = "aps-supplier-service",
        contextId = "jv-aps-supplier-info", configuration = ZjOpenFeignUserContentConfig.class,
        path = "/zj-tg-api/aps-supplier/tg-api/inner/v1/supplier-info",
        url = "${cx-tg.domain.url}")
public interface ZjApsSupplierClient {

    /**
     * 【获取供应商精简信息】
     */
    @PostMapping("/get-supplier-name")
    DataResponse<List<SupplierSimpleResp>> getSupplierName(@RequestBody @Validated SupplierInfoDataReq req);

}
