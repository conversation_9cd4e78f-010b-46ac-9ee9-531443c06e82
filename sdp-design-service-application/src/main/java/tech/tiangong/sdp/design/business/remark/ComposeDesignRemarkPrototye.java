package tech.tiangong.sdp.design.business.remark;

import cn.hutool.core.lang.Assert;
import cn.hutool.extra.spring.SpringUtil;
import tech.tiangong.sdp.design.entity.DesignRemarks;
import tech.tiangong.sdp.design.entity.PrototypeHistory;
import tech.tiangong.sdp.design.repository.PrototypeHistoryRepository;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;

/**
 * <AUTHOR>
 * @create 2021/8/19
 */
public class ComposeDesignRemarkPrototye implements ComposeDesignRemark {
    @Override
    public DesignRemarks compose(DesignRemarksReq req, DesignRemarks designRemarks) {
        PrototypeHistoryRepository prototypeHistoryRepository = SpringUtil.getBean(PrototypeHistoryRepository.class);
        PrototypeHistory prototypeHistory = prototypeHistoryRepository.getById(req.getBizId());
        Assert.notNull(prototypeHistory, "不存在此设计款号");
        designRemarks.setStyleCode(prototypeHistory.getStyleCode());
        designRemarks.setDesignCode(prototypeHistory.getDesignCode());
        return designRemarks;
    }
}
