package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.bean.user.UserContent;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.SpotSpuSupplier;
import tech.tiangong.sdp.design.mapper.SpotSpuSupplierMapper;
import tech.tiangong.sdp.design.vo.dto.spot.SpotSupplierUpdateDto;
import tech.tiangong.sdp.design.vo.req.spot.SpotSpuSupplierReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotPayeeUpdateVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuSupplierVo;
import tech.tiangong.sdp.design.vo.resp.zj.aps.SupplierSimpleResp;
import tech.tiangong.sdp.utils.Bool;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * spot_spu_supplier表(SpotSpuSupplier)服务仓库类
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:53
 */
@Slf4j
@Repository
public class SpotSpuSupplierRepository extends BaseRepository<SpotSpuSupplierMapper, SpotSpuSupplier> {


    public List<SpotSpuSupplierVo> getAllSupplierList(SpotSpuSupplierReq req) {
        return baseMapper.getAllSupplierList(req);
    }
    public List<SpotSpuSupplier> listByStyleCodes(Collection<String> styleCodes){
        if(CollectionUtils.isEmpty(styleCodes)){
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SpotSpuSupplier>()
                .in(SpotSpuSupplier::getStyleCode,styleCodes)
                .orderByAsc(SpotSpuSupplier::getCreatedTime));
    }

    public List<SpotSpuSupplier> selectBySupplier(String supplierStyle, String supplierName) {
        return list(new LambdaQueryWrapper<SpotSpuSupplier>()
                .eq(SpotSpuSupplier::getSupplierStyle,supplierStyle)
                .eq(SpotSpuSupplier::getSupplierName,supplierName)
                .eq(SpotSpuSupplier::getIsDeleted, Bool.NO.getCode())
        );
    }
    public List<SpotSpuSupplier> selectBySupplierStyles(List<String> supplierStyle) {
        return list(new LambdaQueryWrapper<SpotSpuSupplier>()
                .in(SpotSpuSupplier::getSupplierStyle,supplierStyle)
                .eq(SpotSpuSupplier::getIsDeleted, Bool.NO.getCode())
        );
    }
    public BigDecimal getMaxPurchasePrice(String styleCode){
        if(StringUtils.isBlank(styleCode)){
            return null;
        }
        return baseMapper.getMaxPurchasePrice(styleCode);
    }


    public void modify(List<SpotSupplierUpdateDto> updateDtoList, UserContent userContent) {
        SdpDesignException.notEmpty(updateDtoList, "入参为空");

        updateDtoList.forEach(updateDto -> {
            lambdaUpdate()
                    .set(SpotSpuSupplier::getPayeeId, updateDto.getPayeeId())
                    .set(SpotSpuSupplier::getPayeeCode, updateDto.getPayeeCode())
                    .set(SpotSpuSupplier::getPayeeName, updateDto.getPayeeName())
                    .set(SpotSpuSupplier::getSupplierStyle, updateDto.getSupplierStyle())
                    .set(SpotSpuSupplier::getPurchasePrice, updateDto.getPurchasePrice())
                    .set(StringUtils.isNotBlank(updateDto.getSupplierName()), SpotSpuSupplier::getSupplierName, updateDto.getSupplierName())
                    .set(SpotSpuSupplier::getRevisedTime, LocalDateTime.now())
                    .set(SpotSpuSupplier::getReviserId, userContent.getCurrentUserId())
                    .set(SpotSpuSupplier::getReviserName, userContent.getCurrentUserName())
                    .eq(SpotSpuSupplier::getSpotSpuSupplierId, updateDto.getSpotSpuSupplierId())
                    .update();
        });
    }

    public List<SpotSpuSupplier> listBySupplierNameStyle(List<String> supplierNameList, List<String> supplierStyleList) {
        SdpDesignException.notEmpty(supplierNameList, "供应商名称为空");
        SdpDesignException.notEmpty(supplierStyleList, "供应商款号为空");
        return lambdaQuery()
                .in(SpotSpuSupplier::getSupplierName, supplierNameList)
                .in(SpotSpuSupplier::getSupplierStyle, supplierStyleList)
                .list();


    }

    public void updatePayeeByStyleCode(SpotPayeeUpdateVo updateVo) {
        if (Objects.isNull(updateVo) || Objects.isNull(updateVo.getPayeeId())) {
            return;
        }
        lambdaUpdate()
                .set(SpotSpuSupplier::getPayeeId, updateVo.getPayeeId())
                .set(SpotSpuSupplier::getPayeeCode, updateVo.getPayeeCode())
                .set(SpotSpuSupplier::getPayeeName, updateVo.getPayeeName())
                .eq(SpotSpuSupplier::getStyleCode, updateVo.getStyleCode())
                .update();
    }

    /**
     * 根据styleCode查询供应商记录
     * 
     * @param styleCode SPU编码
     * @return 供应商记录列表
     */
    public List<SpotSpuSupplier> listByStyleCode(String styleCode) {
        if (StringUtils.isBlank(styleCode)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SpotSpuSupplier>()
                .eq(SpotSpuSupplier::getStyleCode, styleCode)
                .eq(SpotSpuSupplier::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(SpotSpuSupplier::getCreatedTime));
    }

    /**
     * 保存或更新供应商记录（货通商品AI结果回填场景）
     * 支持1688默认供应商信息设置
     * 
     * @param styleCode SPU编码
     * @param sourceType 数据来源（20-选款）
     * @param supplierName 供应商名称（默认"1688店铺"）
     * @param supplierStyle 供应商款号（默认使用1688商品ID）
     * @param payeeName 收款人名称（默认"阿里"）
     * @param purchasePrice 采购价（从SKC中获取最大价格）
     * @param productId 1688商品ID
     * @param supplierInfo 供应商信息
     * @return 是否保存/更新成功
     */
    public boolean saveOrUpdateForCommunication(String styleCode,
                                                Integer sourceType,
                                                String supplierName,
                                                String supplierStyle,
                                                String payeeName,
                                                BigDecimal purchasePrice,
                                                Long productId,
                                                SupplierSimpleResp supplierInfo
    ) {
        if (StringUtils.isBlank(styleCode)) {
            return false;
        }
        
        try {
            // 先查询是否已存在该styleCode的供应商记录
            List<SpotSpuSupplier> existSuppliers = listByStyleCode(styleCode);
            
            if (!existSuppliers.isEmpty()) {
                // 查询到现有记录，构建更新实体
                SpotSpuSupplier existSupplier = existSuppliers.getFirst();
                SpotSpuSupplier updateEntity = new SpotSpuSupplier();
                updateEntity.setSpotSpuSupplierId(existSupplier.getSpotSpuSupplierId());
                
                // 基础字段更新
                if (supplierStyle != null) {
                    updateEntity.setSupplierStyle(supplierStyle);
                }
                if (payeeName != null) {
                    updateEntity.setPayeeName(payeeName);
                }
                if (purchasePrice != null) {
                    updateEntity.setPurchasePrice(purchasePrice);
                }

                // APS供应商信息字段更新
                if (supplierInfo != null) {
                    if (StringUtils.isNotBlank(supplierInfo.getSupplierCode())) {
                        updateEntity.setPayeeCode(supplierInfo.getSupplierCode());
                    }
                    if (ObjectUtils.isNotEmpty(supplierInfo.getSupplierId())) {
                        updateEntity.setPayeeId(supplierInfo.getSupplierId());
                    }
                    if (ObjectUtils.isNotEmpty(supplierInfo.getSupplierName())) {
                        updateEntity.setPayeeName(supplierInfo.getSupplierName());
                    }
                }
                
                // 设置修订时间
                updateEntity.setRevisedTime(LocalDateTime.now());
                
                // 执行更新
                updateById(updateEntity);
                log.debug("更新供应商记录完成，styleCode:{}, supplierId:{}", styleCode, existSupplier.getSpotSpuSupplierId());
                
            } else {
                // 创建新记录
                SpotSpuSupplier newSupplier = SpotSpuSupplier.builder()
                        .styleCode(styleCode)
                        .sourceType(sourceType)
                        .supplierName(supplierInfo != null && StringUtils.isNotBlank(supplierInfo.getSupplierName()) 
                                    ? supplierInfo.getSupplierName() : supplierName)
                        .payeeCode(supplierInfo != null ? supplierInfo.getSupplierCode() : null)
                        .supplierStyle(supplierStyle != null ? supplierStyle : 
                                      (productId != null ? productId.toString() : null))
                        .payeeName(payeeName)
                        .purchasePrice(purchasePrice)
                        .createdTime(LocalDateTime.now())
                        .build();
                        
                save(newSupplier);
                log.debug("创建供应商记录完成，styleCode:{}", styleCode);
            }
            return true;
            
        } catch (Exception e) {
            log.error("保存或更新供应商信息失败，styleCode:{}, supplierName:{}, error:{}", 
                styleCode, supplierName, e.getMessage(), e);
            throw new RuntimeException("保存或更新供应商信息失败，styleCode=" + styleCode 
                + ", 原因:" + e.getMessage(), e);
        }
    }
}
