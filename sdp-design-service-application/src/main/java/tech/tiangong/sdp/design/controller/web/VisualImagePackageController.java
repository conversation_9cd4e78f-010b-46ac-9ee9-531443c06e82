package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.VisualImagePackageService;
import tech.tiangong.sdp.design.vo.query.visual.VisualImagePackageQuery;
import tech.tiangong.sdp.design.vo.req.visual.*;
import tech.tiangong.sdp.design.vo.resp.visual.ImageBatchUploadErrorResp;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageListVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageVo;

import java.util.List;


/**
 * 视觉图包处理-web
 *
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/visual-image-package")
public class VisualImagePackageController extends BaseController {
    private final VisualImagePackageService visualImagePackageService;

    /**
     * 列表
     *
     * @param query 分页参数
     * @return PageRespVo<VisualTaskQcListVo>
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<VisualImagePackageListVo>> page(@RequestBody @Validated VisualImagePackageQuery query) {
        return DataResponse.ok(visualImagePackageService.page(query));
    }

    /**
     * 根据图包ID获取图包
     */
    @GetMapping("/get-image-package-by-id/{packageId}")
    public DataResponse<VisualImagePackageVo> getImagePackageByPackageId(@PathVariable(value = "packageId") Long packageId) {
        return DataResponse.ok(visualImagePackageService.getVisualImagePackageVoByPackageId(packageId));
    }

    /**
     * 提交图包
     */
    @PostMapping("/save-on-shelf-image-package")
    public DataResponse<Boolean> saveOnShelfImagePackage(@RequestBody @Validated SaveOnShelfImagePackageReq req) {
        return DataResponse.ok(visualImagePackageService.saveOnShelfImagePackage(req));
    }


    /**
     * 提交图包 新0904
     */
    @PostMapping("/save-on-shelf-image-package-v2")
    public DataResponse<Boolean> saveOnShelfImagePackageV2(@RequestBody @Validated CreateVisualImagePackageRequest req) {
        return DataResponse.ok(visualImagePackageService.saveOnShelfImagePackageV2(req));
    }



    /**
     * 清空图包
     */
    @PostMapping("/clean-image-package")
    public DataResponse<Boolean> cleanImagePackage(@RequestBody @Validated CleanImagePackageReq req) {
        return DataResponse.ok(visualImagePackageService.cleanImagePackage(req));
    }

    /**
     * 创建批量下载任务
     * @param req spu
     * @return 任务id
     */
    @PostMapping("/batch-download/submit-task")
    public DataResponse<String> submitSpuImagePackageDownloadTask(@RequestBody @Validated DownloadSpuImagePackageReq req) {
        return DataResponse.ok(String.valueOf(visualImagePackageService.submitSpuImagePackageDownloadTask(req)));
    }

    /**
     * 批量更新-图片
     */
    @PostMapping("/batch-update-images")
    public DataResponse<List<ImageBatchUploadErrorResp>> batchUpdateImages(@RequestBody @Validated ImageBatchUpdateReq req) {
        return DataResponse.ok(visualImagePackageService.batchUpdateImages(req));
    }

    /**
     * 批量更新-文件夹方式
     */
    @PostMapping("/batch-update-folder")
    public DataResponse<List<ImageBatchUploadErrorResp>> batchUpdateImageByFolder(@RequestBody @Validated ImageBatchUpdateFolderReq req) {
        return DataResponse.ok(visualImagePackageService.batchUpdateImageByFolder(req));
    }


}
