package tech.tiangong.sdp.design.converter;

import cn.hutool.core.collection.CollUtil;
import org.springframework.beans.BeanUtils;
import tech.tiangong.pop.common.dto.CreateProductDto;
import tech.tiangong.pop.common.dto.Image;
import tech.tiangong.pop.common.dto.ImageLabelInfoDto;
import tech.tiangong.pop.common.dto.PictureKitInfo;
import tech.tiangong.sdp.design.vo.dto.DpPictureKitInfo;
import tech.tiangong.sdp.design.vo.dto.ImageDTO;
import tech.tiangong.sdp.design.vo.resp.style.PopCreateProductVo;
import tech.tiangong.sdp.utils.StreamUtil;

import java.util.List;

public class PopCreateInfoConverter {

    public static PopCreateProductVo buildPopCreateProductVo(CreateProductDto productReq) {
        PopCreateProductVo productVo = new PopCreateProductVo();
        BeanUtils.copyProperties(productReq, productVo);
        productVo.setImage101Urls(convertImageDto(productReq.getImage101Urls()));
        productVo.setImage201Urls(convertImageDto(productReq.getImage201Urls()));
        productVo.setImage301Urls(convertImageDto(productReq.getImage301Urls()));
        productVo.setImage401Urls(convertImageDto(productReq.getImage401Urls()));
        productVo.setImage601Urls(convertImageDto(productReq.getImage601Urls()));
        productVo.setImageFlowerUrls(convertImageDto(productReq.getImageFlowerUrls()));
        productVo.setMaterialImageList(convertImageDto(productReq.getMaterialImageList()));

        List<CreateProductDto.Skc> dataList = productReq.getDataList();
        if (CollUtil.isNotEmpty(dataList)) {
            List<PopCreateProductVo.Skc> skcVoList = dataList.stream().map(skc -> {
                PopCreateProductVo.Skc skcVo = new PopCreateProductVo.Skc();
                BeanUtils.copyProperties(skc, skcVo);
                List<CreateProductDto.Sku> skuList = skc.getSkuList();
                if (CollUtil.isNotEmpty(skuList)) {
                    skcVo.setSkuList(StreamUtil.convertListBean(skuList, PopCreateProductVo.Sku.class));
                }
                return skcVo;
            }).toList();
            productVo.setDataList(skcVoList);
        }

        List<CreateProductDto.Attributes> attributesList = productReq.getAttributesList();
        if (CollUtil.isNotEmpty(attributesList)) {
            productVo.setAttributesList(StreamUtil.convertListBean(attributesList, PopCreateProductVo.Attributes.class));
        }
        List<ImageLabelInfoDto> imageLabelInfoList = productReq.getImageLabelInfoList();
        if (CollUtil.isNotEmpty(imageLabelInfoList)) {
            productVo.setImageLabelInfoList(StreamUtil.convertListBean(imageLabelInfoList, PopCreateProductVo.ImageLabelInfoDto.class));
        }
        List<PictureKitInfo> pictureKitInfoList = productReq.getPictureKitInfoList();
        if (CollUtil.isNotEmpty(pictureKitInfoList)) {
            productVo.setPictureKitInfoList(StreamUtil.convertListBean(pictureKitInfoList, DpPictureKitInfo.class));
        }
        return productVo;
    }

    public static List<ImageDTO> convertImageDto(List<Image> imageList) {
        if (CollUtil.isEmpty(imageList)) {
            return null;
        }
        return StreamUtil.convertListBean(imageList, ImageDTO.class);
    }
}
