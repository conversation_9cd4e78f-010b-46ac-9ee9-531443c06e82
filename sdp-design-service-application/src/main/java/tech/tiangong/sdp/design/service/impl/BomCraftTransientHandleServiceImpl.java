package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.zjkj.scf.bundle.common.dto.demand.dto.response.CraftDemandMatchDetailDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.CraftDemandInfoTransientConverter;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.entity.CraftDemandInfoTransient;
import tech.tiangong.sdp.design.enums.CraftDemandStateEnum;
import tech.tiangong.sdp.design.helper.PrepareBomCraftCycleHelper;
import tech.tiangong.sdp.design.repository.CraftDemandInfoRepository;
import tech.tiangong.sdp.design.repository.CraftDemandInfoTransientRepository;
import tech.tiangong.sdp.design.service.BomCraftTransientHandleService;
import tech.tiangong.sdp.design.vo.req.bom.BomCraftTransientHandleReq;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * bom暂存 工艺处理服务
 * <AUTHOR>
 * @date 2022/11/17 11:30
 */


@Slf4j
@Service
@RequiredArgsConstructor
public class BomCraftTransientHandleServiceImpl implements BomCraftTransientHandleService {

    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final CraftDemandInfoTransientRepository craftDemandInfoTransientRepository;
    private final PrepareBomCraftCycleHelper prepareBomCraftCycleHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void firstTransient(BomCraftTransientHandleReq req) {
        //兼容历史
        //1.若有历史数据为删除，直接过滤。 2.已提交，无暂存的数据，过滤已删除的工艺
        List<CraftDemandInfo> originCraftDemandInfo = craftDemandInfoRepository.getListByBomIdsAndState(Lists.newArrayList(req.getBomOrder().getBomId()), CraftDemandStateEnum.SUBMIT.getCode());
        List<CraftDemandInfoTransient> craftDemandInfoTransients = originCraftDemandInfo.stream().map(craftDemandInfo -> {
            CraftDemandInfoTransient craftDemandInfoTransient = new CraftDemandInfoTransient();
            BeanUtils.copyProperties(craftDemandInfo, craftDemandInfoTransient);
            craftDemandInfoTransient.setOriginCraftDemandId(craftDemandInfo.getCraftDemandId());
            //待提交的首次暂存的临时表数据都是原表
            craftDemandInfoTransient.setBomTransientId(craftDemandInfo.getBomId());
            return craftDemandInfoTransient;
        }).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(req.getDelCraftDemandIds())){
            //若删除历史数据的工艺,设置为关闭状态
            craftDemandInfoTransients.stream().forEach(craftDemandInfoTransient -> {
                if (req.getDelCraftDemandIds().contains(craftDemandInfoTransient.getOriginCraftDemandId())){
                    craftDemandInfoTransient.setState(CraftDemandStateEnum.CLOSED.getCode());
                }
            });
        }


        log.info("【待提交_无暂存,首次暂存】 历史数据:{}", JSONUtil.toJsonStr(craftDemandInfoTransients));

        //新增工艺
        List<CraftDemandInfoTransient> addCraftDemandList = req.getAddCraftDemandList().stream()
                .map(craftDemandSaveV3Req -> CraftDemandInfoTransientConverter.buildNewOne(craftDemandSaveV3Req,req.getTransientBomId(), req.getBomOrder().getBomId(),req.getBomOrder().getPrototypeId()))
                .collect(Collectors.toList());

        log.info("【待提交_无暂存,首次暂存】 保存:{}", JSONUtil.toJsonStr(addCraftDemandList));
        //保存
        addCraftDemandList.addAll(craftDemandInfoTransients);
        craftDemandInfoTransientRepository.saveBatch(addCraftDemandList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reTransient(BomCraftTransientHandleReq req) {
        //新增工艺
        List<CraftDemandInfoTransient> addCraftDemandList = req.getAddCraftDemandList().stream()
                .map(craftDemandSaveV3Req -> CraftDemandInfoTransientConverter.buildNewOne(craftDemandSaveV3Req,req.getTransientBomId(), req.getBomOrder().getBomId(),req.getBomOrder().getPrototypeId()))
                .collect(Collectors.toList());
        log.info("【再次暂存】新增工艺：{}", JSONUtil.toJsonStr(addCraftDemandList));
        craftDemandInfoTransientRepository.saveBatch(addCraftDemandList);

        //需求物料更换, 更新关联的物料id
        Map<Long, Long> oldNewMaterialIdMap = req.getOldNewMaterialIdMap();
        if (CollUtil.isNotEmpty(oldNewMaterialIdMap)) {
            List<CraftDemandInfoTransient> materialIdUpdateList = Lists.newLinkedList();
            craftDemandInfoTransientRepository.listByBomTransientId(req.getTransientBomId())
                    .forEach(item -> {
                        Long bomMaterialId = item.getBomMaterialId();
                        if (Objects.isNull(bomMaterialId)) {
                            return;
                        }
                        Long newMaterialId = oldNewMaterialIdMap.get(bomMaterialId);
                        if (Objects.isNull(newMaterialId)) {
                            return;
                        }
                        CraftDemandInfoTransient updateTransient = new CraftDemandInfoTransient();
                        updateTransient.setCraftDemandId(item.getCraftDemandId());
                        updateTransient.setBomMaterialId(newMaterialId);
                        materialIdUpdateList.add(updateTransient);
                    });
            if (CollUtil.isNotEmpty(materialIdUpdateList)) {
                log.info("【再次暂存】更换物料工艺关联新物料id：{}", JSONUtil.toJsonStr(materialIdUpdateList));
                craftDemandInfoTransientRepository.updateBatchById(materialIdUpdateList);
            }
        }

        //删除工艺
        if (CollUtil.isEmpty(req.getDelCraftDemandIds())) {
            return;
        }
        List<CraftDemandInfoTransient> delCraftDemandInfoTransientsDB = craftDemandInfoTransientRepository.listByIds(req.getDelCraftDemandIds());

        //暂存的数据，要逻辑删除
        List<Long> removeIds = Lists.newLinkedList();

        List<CraftDemandInfoTransient> delCraftDemandInfoTransients = delCraftDemandInfoTransientsDB.stream().map(craftDemandInfoTransient -> {
            if (Objects.isNull(craftDemandInfoTransient.getOriginCraftDemandId())) {
                //如果是暂存的数据，则删除
                removeIds.add(craftDemandInfoTransient.getCraftDemandId());
                return null;
            } else {
                CraftDemandInfoTransient delCraft = new CraftDemandInfoTransient();
                delCraft.setCraftDemandId(craftDemandInfoTransient.getCraftDemandId());
                //如果是原表有的数据，则设置为关闭
                delCraft.setState(CraftDemandStateEnum.CLOSED.getCode());
                return delCraft;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());

        log.info("【再次暂存】逻辑删除工艺：{} 关闭工艺:{}", JSONUtil.toJsonStr(removeIds), JSONUtil.toJsonStr(delCraftDemandInfoTransients));
        craftDemandInfoTransientRepository.updateBatchById(delCraftDemandInfoTransients);
        craftDemandInfoTransientRepository.removeByIds(removeIds);
        // 赋值工艺周期
        prepareCraftCycle(req.getTransientBomId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void firstTransient4Submitted(BomCraftTransientHandleReq req) {
        //兼容历史
        //1.若有历史数据为删除，直接过滤。 2.已提交，无暂存的数据，过滤已删除的工艺
        List<CraftDemandInfo> originCraftDemandInfo = craftDemandInfoRepository.getListByBomIdsAndState(Lists.newArrayList(req.getBomOrder().getBomId()), CraftDemandStateEnum.SUBMIT.getCode());
        List<CraftDemandInfoTransient> craftDemandInfoTransients = originCraftDemandInfo.stream().map(craftDemandInfo -> {
            Long newBomMaterialId = req.getOldNewMaterialIdMap().get(craftDemandInfo.getBomMaterialId());
            SdpDesignException.notNull(newBomMaterialId,"工艺需求id映射关系错误.oldBomMaterialId:{} bomId:{}",craftDemandInfo.getBomMaterialId(),req.getBomOrder().getBomId());

            CraftDemandInfoTransient craftDemandInfoTransient = new CraftDemandInfoTransient();
            BeanUtils.copyProperties(craftDemandInfo, craftDemandInfoTransient);
            craftDemandInfoTransient.setCraftDemandId(IdPool.getId());
            craftDemandInfoTransient.setOriginCraftDemandId(craftDemandInfo.getCraftDemandId());
            craftDemandInfoTransient.setBomMaterialId(newBomMaterialId);
            craftDemandInfoTransient.setBomTransientId(req.getTransientBomId());
            craftDemandInfoTransient.setBomId(req.getTransientBomId());
            return craftDemandInfoTransient;
        }).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(req.getDelCraftDemandIds())){
            //若删除历史数据的工艺,设置为关闭状态
            craftDemandInfoTransients.stream().forEach(craftDemandInfoTransient -> {
                if (req.getDelCraftDemandIds().contains(craftDemandInfoTransient.getOriginCraftDemandId())){
                    craftDemandInfoTransient.setState(CraftDemandStateEnum.CLOSED.getCode());
                }
            });
        }


        //新增工艺
        List<CraftDemandInfoTransient> addCraftDemandList = req.getAddCraftDemandList().stream()
                .map(craftDemandSaveV3Req -> CraftDemandInfoTransientConverter.buildNewOne(craftDemandSaveV3Req,req.getTransientBomId(), req.getBomOrder().getBomId(),req.getBomOrder().getPrototypeId()))
                .collect(Collectors.toList());


        craftDemandInfoTransients.addAll(addCraftDemandList);
        craftDemandInfoTransientRepository.saveBatch(craftDemandInfoTransients);

        log.info("【已提交，首次暂存】新增工艺：{}", JSONUtil.toJsonStr(craftDemandInfoTransients));
        // 赋值工艺周期
        prepareCraftCycle(req.getTransientBomId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reTransient4Submitted(BomCraftTransientHandleReq req) {
        this.reTransient(req);
    }

    /**
     * 赋值最新工艺周期
     */
    private void prepareCraftCycle(Long transientBomId) {
        // 根据 暂存bom单Id 查询对应暂存单的工艺集合
        List<CraftDemandInfoTransient> craftDemandInfoTransients = craftDemandInfoTransientRepository.
                listByBomTransientId(transientBomId);
        // 移除关闭的（删除的）
        craftDemandInfoTransients.removeIf(item -> CraftDemandStateEnum.CLOSED.getCode().equals(item.getState()));
        if (CollUtil.isEmpty(craftDemandInfoTransients)) {
            return;
        }
        List<Long> listThirdDemandId = craftDemandInfoTransients.stream().
                map(CraftDemandInfoTransient::getThirdPartyCraftDemandId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(listThirdDemandId)) {
            return;
        }
        // 获取第三方id对应工艺周期map
        Map<Long, CraftDemandMatchDetailDto.CraftDemandMatchDetail> mapThirdIdDetail
                = prepareBomCraftCycleHelper.getCraftCycleMatchByThirdId(listThirdDemandId);
        // 获取bom的所有工艺，更新工艺周期
        for (CraftDemandInfoTransient craftDemandInfoTransient : craftDemandInfoTransients) {
            if (Objects.isNull(craftDemandInfoTransient.getThirdPartyCraftDemandId())) {
                continue;
            }
            CraftDemandMatchDetailDto.CraftDemandMatchDetail craftDemandMatchDetail =
                    mapThirdIdDetail.get(craftDemandInfoTransient.getThirdPartyCraftDemandId());
            if (null != craftDemandMatchDetail) {
                // 更新赋值工艺周期
                craftDemandInfoTransientRepository.lambdaUpdate().
                        eq(CraftDemandInfoTransient::getCraftDemandId, craftDemandInfoTransient.getCraftDemandId()).
                        set(CraftDemandInfoTransient::getSampleCraftCycle, craftDemandMatchDetail.getSampleCraftCycle()).
                        set(CraftDemandInfoTransient::getBulkCraftCycle, craftDemandMatchDetail.getBulkCraftCycle()).update();
            }
        }
    }

}
