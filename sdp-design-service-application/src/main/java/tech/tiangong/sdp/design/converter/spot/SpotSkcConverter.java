package tech.tiangong.sdp.design.converter.spot;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.pop.common.dto.CreateProductDto;
import tech.tiangong.pop.common.dto.ProductAttributesV2Dto;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.ProductCommunicationEnum;
import tech.tiangong.sdp.design.enums.SdpStyleTypeEnum;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.remote.SampleClothesRemoteHelper;
import tech.tiangong.sdp.design.repository.SpotSkcDetailRepository;
import tech.tiangong.sdp.design.repository.SpotSpuDetailRepository;
import tech.tiangong.sdp.design.repository.SpotSpuSupplierRepository;
import tech.tiangong.sdp.design.vo.base.AttributeVo;
import tech.tiangong.sdp.design.vo.base.prototype.OpsObject;
import tech.tiangong.sdp.design.vo.dto.spot.SpotSpuEstimateCheckPriceDto;
import tech.tiangong.sdp.design.vo.req.digital.TemplateSizeDetailReq;
import tech.tiangong.sdp.design.vo.resp.buyer.BuyerPrototypeToOfpResp;
import tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcForOfpVo;
import tech.tiangong.sdp.utils.StreamUtil;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Component
public class SpotSkcConverter {
    private final SpotSpuSupplierRepository spotSpuSupplierRepository;
    private final SpotSkcDetailRepository spotSkcDetailRepository;
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final PopProductHelper popProductHelper;
    private final DictValueRemoteHelper dictValueRemoteHelper;

    public CreateProductDto convertProductCreateReq(SpotSpu spotSpu,List<SpotSkc> spotSkcs, List<SpotSkcDetail> spotSkcDetails) {
        //只推pop未存在的skc
        List<String> designCodeList = StreamUtil.convertListAndDistinct(spotSkcs, SpotSkc::getDesignCode);
        Map<String,Boolean> skcIsExistMap = popProductHelper.checkSkcIsExist(designCodeList);
        List<SpotSkc> pushSkcList = spotSkcs.stream()
                .filter(item -> !skcIsExistMap.get(item.getDesignCode())).toList();
        if (CollUtil.isEmpty(pushSkcList) || CollUtil.isEmpty(spotSkcDetails)) {
            return null;
        }

        SpotSpuDetail spotSpuDetail = spotSpuDetailRepository.getBySpotSpuId(spotSpu.getSpotSpuId());

        SpotSpuEstimateCheckPriceDto checkPriceDto = sampleClothesRemoteHelper.getEstimateCheckPriceBySpotSpu(spotSpu);

        //采购价以供应商中最大额值的
        BigDecimal purchasePrice = spotSpuSupplierRepository.getMaxPurchasePrice(spotSpu.getStyleCode());

//        List<SpotSkcDetail> spotSkcDetails = spotSkcDetailRepository.listBySkcIds(pushSkcList.stream().map(SpotSkc::getSpotSkcId).collect(Collectors.toList()));

        //查询当前品类字典获取尺码明细表
        DictVo dictVoByCategory = dictValueRemoteHelper.getDictVoByCategory(spotSpu.getCategory());

        return this.buildCreateProductDto(spotSpu, spotSpuDetail, checkPriceDto, pushSkcList, purchasePrice, spotSkcDetails,dictVoByCategory);
    }

    public CreateProductDto buildCreateProductDto(SpotSpu spotSpu, SpotSpuDetail spotSpuDetail,
                                                  SpotSpuEstimateCheckPriceDto checkPriceDto,
                                                  List<SpotSkc> pushSkcList, BigDecimal purchasePrice,
                                                  List<SpotSkcDetail> spotSkcDetails, DictVo dictVoByCategory) {
        CreateProductDto productReq = new CreateProductDto();
        productReq.setStyleType(SdpStyleTypeEnum.SPOT.getCode());
        productReq.setShopId(spotSpu.getStoreId());
        productReq.setShopName(spotSpu.getStoreName());
        productReq.setSpotTypeCode(spotSpu.getSpotTypeCode());
        productReq.setGoodsRepType(spotSpu.getPalletTypeName());
        productReq.setGoodsType(spotSpu.getProductType());
        productReq.setSupplyMode(spotSpu.getSupplyModeCode());
        //主图只要1张
        if (CollUtil.isNotEmpty(spotSpuDetail.getProductPictureList())) {
            productReq.setMainImgUrl(spotSpuDetail.getProductPictureList().getFirst());
        }
        productReq.setWaves(spotSpu.getWaveBandName());
        productReq.setSpuCode(spotSpu.getStyleCode());
        productReq.setCategoryCode(spotSpu.getCategory());
        productReq.setCategoryName(spotSpu.getCategoryName());
        productReq.setSizeGroupName(spotSpu.getSizeStandard());
        productReq.setSizeGroupCode(spotSpu.getSizeStandardCode());
        productReq.setClothingStyleName(spotSpu.getClothingStyleName());
        productReq.setClothingStyleCode(spotSpu.getClothingStyleCode());
        productReq.setPlanningType(spotSpu.getPlanningType());
        productReq.setMarketCode(spotSpu.getMarketCode());
        productReq.setMarketSeriesCode(spotSpu.getMarketSeriesCode());
        List<OpsObject> styleSeasonList = spotSpuDetail.getStyleSeasonList();
        if (CollUtil.isNotEmpty(styleSeasonList)) {
            productReq.setStyleSeason(JSON.toJSONString(styleSeasonList));
        }

        if(checkPriceDto !=null){
            productReq.setPricingType(checkPriceDto.getPriceType());
        }

        productReq.setDataList(convertSkc(pushSkcList, checkPriceDto, purchasePrice, spotSkcDetails));


        //0904
        // productReq 弹性看需不需要  POD是放在商品属性里的
        productReq.setSpuName(spotSpu.getSpuName());
        productReq.setSpuNameTrans(spotSpu.getSpuNameTrans());
        productReq.setWeaveModeCode(spotSpu.getWeaveModeCode());
        productReq.setWeaveMode(spotSpu.getWeaveMode());
        if (StrUtil.isNotBlank(spotSpu.getCountrySiteName())) {
            productReq.setCountrys(Collections.singletonList(spotSpu.getCountrySiteName()));
        }

        productReq.setProductThemeCode(spotSpu.getProductThemeCode());
        productReq.setProductThemeName(spotSpu.getProductThemeName());
        productReq.setStyleElementName(spotSpu.getElementName());
        productReq.setStyleElementCode(spotSpu.getElementCode());
        productReq.setElasticCode(spotSpu.getElasticCode());
        productReq.setElasticName(spotSpu.getElasticName());
        productReq.setBuyerId(spotSpu.getBuyerId());
        productReq.setBuyerName(spotSpu.getBuyerName());
        productReq.setProductLink(spotSpuDetail.getProductLink());
        productReq.setGoodsPass(Optional.ofNullable(spotSpu.getCommunication()).orElse(ProductCommunicationEnum.NO.getCode()));

        if(CollectionUtil.isNotEmpty(spotSpuDetail.getAttributes())){
            List<AttributeVo> attributeVos = spotSpuDetail.getAttributes();

            // 转换为 ProductAttributesV2 列表，以 values 维度转换
            List<ProductAttributesV2Dto> productAttributesV2List = attributeVos.stream()
                    .filter(attributeVo -> attributeVo.getValues() != null) // 过滤掉没有值的属性
                    .flatMap(attributeVo -> attributeVo.getValues().stream()
                            .map(value -> {
                                ProductAttributesV2Dto productAttributesV2 = new ProductAttributesV2Dto();
                                productAttributesV2.setAttributeId(attributeVo.getAttributeId());
                                productAttributesV2.setCategoryId(attributeVo.getCategoryId());
                                productAttributesV2.setAttributeValueId(value.getAttributeValueId());
                                productAttributesV2.setAttributeValue(value.getAttributeValue());
                                return productAttributesV2;
                            }))
                    .collect(Collectors.toList());

            productReq.setAttributesV2List(productAttributesV2List);
        }

        changeProductSizeDetail(dictVoByCategory, productReq);
        return productReq;
    }

    private List<CreateProductDto.Skc> convertSkc(List<SpotSkc> spotSkcs, SpotSpuEstimateCheckPriceDto estimateCheckPrice, BigDecimal purchasePrice, List<SpotSkcDetail> spotSkcDetails) {
        if (CollectionUtils.isEmpty(spotSkcs)) {
            return Collections.emptyList();
        }
        Map<Long,SpotSkcDetail> spotSkcIdToDetailMap = spotSkcDetails.stream().collect(Collectors.toMap(SpotSkcDetail::getSpotSkcId,v->v,(k1,k2)->k2));
        return spotSkcs.stream().map(spotSkc->{
            CreateProductDto.Skc skcInfo = new CreateProductDto.Skc();
            skcInfo.setSkc(spotSkc.getDesignCode());
            SpotSkcDetail spotSkcDetail = spotSkcIdToDetailMap.get(spotSkc.getSpotSkcId());
            if (ObjectUtil.isNotNull(spotSkcDetail)) {
                skcInfo.setPictures(spotSkcDetail.getProductPictureList());
                //颜色信息
                List<ColorInfoVo> colorInfoList = spotSkcDetail.getColorInfoList();
                if (CollUtil.isNotEmpty(colorInfoList)) {
                    SdpDesignException.notNull(colorInfoList.getFirst(), "颜色信息为空, 请维护skc颜色信息! skc:{}", spotSkc.getDesignCode());
                    List<String> colorList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColor);
                    List<String> abbrCodeList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColorAbbrCode);
                    List<String> englishNameList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColorEnglishName);
                    String color = StrUtil.join(StrUtil.SPACE, colorList);
                    String colorCode = StrUtil.join(StrUtil.SPACE, englishNameList);
                    String abbrCode = StrUtil.join(StrUtil.SPACE, abbrCodeList);
                    skcInfo.setColor(color);
                    skcInfo.setColorCode(colorCode);
                    skcInfo.setColorAbbrCode(abbrCode);
                }
                List<CreateProductDto.Sku> skuList = new ArrayList<>();
                if(StringUtils.isNotBlank(spotSkcDetail.getSampleSize())){
                    skuList.addAll(Arrays.stream(spotSkcDetail.getSampleSize().split(",")).map(s->{
                        CreateProductDto.Sku sku = new CreateProductDto.Sku();
                        sku.setSizeName(s);
                        return sku;
                    }).toList());
                }
                skcInfo.setSkuList(skuList);
            }

            if(estimateCheckPrice!=null){
                skcInfo.setLocalPrice(estimateCheckPrice.getTotalCost());
            }
            skcInfo.setPurchasePrice(purchasePrice);
            return skcInfo;
        }).collect(Collectors.toList());
    }

    public void fillSpotSkcForOfpVo(List<SpotSkcForOfpVo> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        Set<String> styleCodes = list.stream().map(SpotSkcForOfpVo::getStyleCode).collect(Collectors.toSet());
        List<SpotSpuSupplier> spotSpuSuppliers = spotSpuSupplierRepository.listByStyleCodes(styleCodes);
        if(!CollectionUtil.isEmpty(spotSpuSuppliers)) {
            Map<String,List<SpotSpuSupplier>> styleCodeToSupplierMap = spotSpuSuppliers.stream().collect(Collectors.groupingBy(SpotSpuSupplier::getStyleCode));
            list.forEach(spotSkcForOfpVo -> {
                if(StringUtils.isNotBlank(spotSkcForOfpVo.getSkcProductPicture())){
                    List<String> pics = JSONArray.parseArray(spotSkcForOfpVo.getSkcProductPicture(),String.class);
                    spotSkcForOfpVo.setCustomerPicture(String.join(",",pics));
                }
                //补充供应商信息
                List<SpotSpuSupplier> spuSuppliers = styleCodeToSupplierMap.get(spotSkcForOfpVo.getStyleCode());
                if(!CollectionUtil.isEmpty(spuSuppliers)) {
                    List<SpotSkcForOfpVo.Supplier> supplierList = spuSuppliers.stream().map(v->{
                        SpotSkcForOfpVo.Supplier supplier = new SpotSkcForOfpVo.Supplier();
                        supplier.setSupplierName(v.getSupplierName());
                        supplier.setSupplierId(v.getSpotSpuSupplierId());
                        supplier.setCostPrice(v.getPurchasePrice());
                        supplier.setSourceType(v.getSourceType());
                        supplier.setSupplierStyle(v.getSupplierStyle());
                        supplier.setPayeeCode(v.getPayeeCode());
                        supplier.setPayeeName(v.getPayeeName());
                        supplier.setPayeeId(v.getPayeeId());
                        return supplier;
                    }).collect(Collectors.toList());
                    spotSkcForOfpVo.setSuppliers(supplierList);
                }
            });
        }

    }

    public List<SpotSkcForOfpVo> trans2SpotSkcForOfpVo(Collection<BuyerPrototypeToOfpResp> buyerPrototypeToOfpResps){
        if(CollectionUtils.isEmpty(buyerPrototypeToOfpResps)){
            return Collections.emptyList();
        }
        return buyerPrototypeToOfpResps.stream().map(v->{
            SpotSkcForOfpVo spotSkcForOfpVo = new SpotSkcForOfpVo();
            spotSkcForOfpVo.setColor(v.getColor());
            spotSkcForOfpVo.setCategory(v.getCategory());
            spotSkcForOfpVo.setCategoryName(v.getCategoryName());
            spotSkcForOfpVo.setSkcSourceType(10);
            spotSkcForOfpVo.setStyleCode(v.getStyleCode());
            spotSkcForOfpVo.setDesignCode(v.getDesignCode());
            spotSkcForOfpVo.setCreatorName(v.getCreatorName());
            spotSkcForOfpVo.setIsCanceled(0);
            spotSkcForOfpVo.setCustomerPicture(v.getCustomerPicture());
//            spotSkcForOfpVo.setProductLink();

            spotSkcForOfpVo.setPrototypeStatus(2);
            spotSkcForOfpVo.setWeaveModeCode(v.getWeaveModeCode());
            spotSkcForOfpVo.setWeaveMode(v.getWeaveMode());
            spotSkcForOfpVo.setWaveBandCode(v.getWaveBandCode());
            spotSkcForOfpVo.setWaveBandName(v.getWaveBandName());
            spotSkcForOfpVo.setVersionNum(v.getVersionNumber());
            spotSkcForOfpVo.setStyleDeveloperName(v.getStyleDeveloperName());
            spotSkcForOfpVo.setStyleDeveloperId(v.getStyleDeveloperId());
            spotSkcForOfpVo.setSizeStandardCode(v.getSizeStandardCode());
            spotSkcForOfpVo.setSizeStandard(v.getSizeStandard());

            spotSkcForOfpVo.setQualityLevel(v.getQualityLevel());
            spotSkcForOfpVo.setQualityLevelCode(v.getQualityLevelCode());
            List<SpotSkcForOfpVo.Supplier> supplierList = new ArrayList<>();
            SpotSkcForOfpVo.Supplier supplier = new SpotSkcForOfpVo.Supplier();
            supplier.setSourceType(10);//买手款的供应商来源默认为自建
            supplier.setSupplierName(v.getSupplierName());
            supplier.setSupplierId(v.getSupplierId());
            supplier.setCostPrice(v.getCostPrice());
            supplier.setSupplierStyle(v.getSupplierArticleNumber());
//            supplier.setPayeeCode();
            supplier.setPayeeName(v.getSupplierName());
            supplier.setPayeeId(v.getSupplierId());
            supplierList.add(supplier);
            spotSkcForOfpVo.setSuppliers(supplierList);
            return spotSkcForOfpVo;
        }).collect(Collectors.toList());

    }

    private void changeProductSizeDetail(DictVo dictVoByCategory, CreateProductDto productReq) {
        if(dictVoByCategory != null && CollectionUtil.isNotEmpty(dictVoByCategory.getAttributes())){

            Optional<team.aikero.admin.common.vo.AttributeVo> codeAttributeOpt = dictVoByCategory.getAttributes().stream()
                    .filter(attr -> attr != null && "CMMXB".equals(attr.getCode()))
                    .findFirst();


            if (codeAttributeOpt.isPresent() && StringUtils.isNotBlank(codeAttributeOpt.get().getName())) {
                String jsonString = codeAttributeOpt.get().getName();
                List<TemplateSizeDetailReq> sizeDetailReqs;

                try {
                    // 尝试解析JSON，失败时显式抛出异常
                    sizeDetailReqs = JSON.parseArray(jsonString, TemplateSizeDetailReq.class);
                } catch (Exception e) {
                    // 日志记录异常信息（可选）
                    log.error("JSON解析失败，原始字符串: {}", jsonString, e);
                    // 抛出明确的运行时异常，可替换为自定义异常
                    throw new RuntimeException("字典里的产品尺寸详情JSON格式错误,请检查配置", e);
                }
                // 转换为 ProductSizeDetail 列表
                List<CreateProductDto.ProductSizeDetail> productSizeDetails = sizeDetailReqs.stream()
                        .map(templateReq -> {
                            CreateProductDto.ProductSizeDetail productSizeDetail = new CreateProductDto.ProductSizeDetail();
                            productSizeDetail.setPartName(templateReq.getPartName());

                            // 转换 SizeData 列表到 ProductSizeJson 列表
                            if (templateReq.getSizeList() != null) {
                                List<CreateProductDto.ProductSizeJson> productSizeJsons = templateReq.getSizeList().stream()
                                        .map(sizeData -> {
                                            CreateProductDto.ProductSizeJson productSizeJson = new CreateProductDto.ProductSizeJson();
                                            productSizeJson.setSize(sizeData.getSize());
                                            // 将 BigDecimal 转换为 String
                                            productSizeJson.setData(sizeData.getData() != null ? sizeData.getData().toString() : null);
                                            return productSizeJson;
                                        })
                                        .collect(Collectors.toList());
                                productSizeDetail.setSizeJson(productSizeJsons);
                            }

                            return productSizeDetail;
                        })
                        .collect(Collectors.toList());

                productReq.setSizeDetails(productSizeDetails);
            }
        }
    }
}
