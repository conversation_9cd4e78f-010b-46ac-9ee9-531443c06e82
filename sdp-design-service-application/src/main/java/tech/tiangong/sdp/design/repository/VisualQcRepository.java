package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualQc;
import tech.tiangong.sdp.design.enums.visual.VisualQcTypeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskNodeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskQcResultEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskStateEnum;
import tech.tiangong.sdp.design.mapper.VisualQcMapper;
import tech.tiangong.sdp.design.vo.query.visual.VisualQcQuery;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskQcListVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskQcResultCountVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskStateCountVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskStepNodeStateCountVo;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * (VisualQc)服务仓库类
 */
@Repository
public class VisualQcRepository extends BaseRepository<VisualQcMapper, VisualQc> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<VisualTaskQcListVo> queryByPage(VisualQcQuery query) {
        return baseMapper.queryByPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }
    /**
     * 任务环节节点状态统计
     * @return
     */
    public List<VisualTaskStepNodeStateCountVo> getVisualTaskStepNodeStateCountVo(VisualQcQuery query){
        List<VisualTaskStepNodeStateCountVo> list = baseMapper.getVisualTaskStepNodeStateCountVo(query);
        Map<String, VisualTaskStepNodeStateCountVo> countVoMap = CollectionUtil.isEmpty(list) ? new HashMap<>()
                : list.stream().collect(Collectors.toMap(v -> v.getProcessNode() + "-" + v.getNodeState(), v -> v, (o1, o2) -> o2));

        List<VisualTaskStepNodeStateCountVo> result = new ArrayList<>();
        //没有统计到结果的数据，补全
        Stream.of(VisualTaskNodeEnum.values()).filter(v -> !v.name().equals("UNKNOWN"))
                .forEach(stepNode -> {
                    result.addAll(stepNode.getStateEnum().getBizStateDtoList().stream()
                            .filter(e -> !e.getName().equals("UNKNOWN"))
                            .map(state -> {
                                return countVoMap.getOrDefault(stepNode.getCode() + "-" + state.getCode(), new VisualTaskStepNodeStateCountVo().setCount(0))
                                        .setProcessStep(stepNode.getVisualStep().getCode())
                                        .setProcessStepDesc(stepNode.getVisualStep().getDesc())
                                        .setProcessNode(stepNode.getCode())
                                        .setProcessNodeDesc(stepNode.getDesc())
                                        .setNodeState(state.getCode())
                                        .setNodeStateDesc(state.getDesc());
                            }).toList());
                });
        return result;
    }

    /**
     * 任务质检结果统计
     * @return
     */
    public List<VisualTaskQcResultCountVo> getVisualQcResultCountVo(VisualQcQuery query){
        List<VisualTaskQcResultCountVo> list = baseMapper.getVisualQcResultCountVo(query);
        if(CollectionUtil.isNotEmpty(list)){
            list.forEach(item->{
                VisualTaskQcResultEnum visualTaskQcResult = VisualTaskQcResultEnum.findByCode(item.getQcResultCode());
                item.setQcResultDesc(visualTaskQcResult.getDesc());
            });
        }
        return list;
    }

    /**
     * 任务状态统计
     * @return
     */
    public List<VisualTaskStateCountVo> getVisualQcTaskStateCountVo(VisualQcQuery query){
        List<VisualTaskStateCountVo> list = baseMapper.getVisualQcTaskStateCountVo(query);
        if(CollectionUtil.isNotEmpty(list)){
            list.forEach(item->{
                VisualTaskStateEnum visualTaskState = VisualTaskStateEnum.findByCode(item.getTaskStateCode());
                item.setTaskStateDesc(visualTaskState.getDesc());
            });
        }
        return list;
    }

    public List<VisualQc> listLatestByTaskId(Long visualTaskId) {
        if(visualTaskId==null){
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<VisualQc>()
                .eq(VisualQc::getTaskId, visualTaskId)
                .eq(VisualQc::getIsLatest,Bool.YES.getCode())
                .eq(VisualQc::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualQc::getCreatedTime));
    }

    public List<VisualQc> listLatestByTaskIds(Collection<Long> visualTaskIds) {
        if(CollectionUtil.isEmpty(visualTaskIds)){
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<VisualQc>()
                .in(VisualQc::getTaskId, visualTaskIds)
                .eq(VisualQc::getIsLatest,Bool.YES.getCode())
                .eq(VisualQc::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualQc::getCreatedTime));
    }

    public VisualQc getLatestByTaskId(Long visualTaskId, VisualQcTypeEnum visualQcType) {
        if(visualTaskId==null || visualQcType==null){
            return null;
        }
        return getOne(new LambdaQueryWrapper<VisualQc>()
                .eq(VisualQc::getTaskId, visualTaskId)
                .eq(VisualQc::getQcType,visualQcType.getCode())
                .eq(VisualQc::getIsLatest,Bool.YES.getCode())
                .eq(VisualQc::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualQc::getCreatedTime),false);
    }
    public VisualQc latestQcResult(Long visualTaskId, VisualTaskQcResultEnum qcResult) {
        if(visualTaskId==null || qcResult==null){
            return null;
        }
        return lambdaQuery()
                .eq(VisualQc::getTaskId, visualTaskId)
                .eq(VisualQc::getQcResult, qcResult.getCode())
                .orderByDesc(VisualQc::getCreatedTime)
                .last("limit 1").one();
    }

    public void expireByTaskIds(List<Long> visualTaskIds) {
        if(CollectionUtil.isNotEmpty(visualTaskIds)){
            return;
        }
        update(new LambdaUpdateWrapper<VisualQc>()
                .in(VisualQc::getTaskId, visualTaskIds)
                .eq(VisualQc::getIsLatest, Bool.YES.getCode())
                .set(VisualQc::getIsLatest,Bool.NO.getCode()));
    }
}
