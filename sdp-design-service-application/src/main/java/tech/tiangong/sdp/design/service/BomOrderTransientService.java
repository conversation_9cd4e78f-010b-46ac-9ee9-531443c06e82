package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.query.bom.BomOrderTransientQuery;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderTransientReq;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderTransientVo;

/**
 * bom暂存表服务接口
 *
 * <AUTHOR>
 */
public interface BomOrderTransientService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<BomOrderTransientVo> page(BomOrderTransientQuery query);

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    BomOrderTransientVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(BomOrderTransientReq req);

    /**
     * 更新数据
     *
     * @param req 数据实体
     */
    void update(BomOrderTransientReq req);

    /**
     * 删除数据
     *
     * @param id 主键ID
     */
    void remove(Long id);

}
