package tech.tiangong.sdp.design.converter;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import com.zjkj.scf.bundle.common.dto.demand.dto.request.CraftDemandAddReq;
import com.zjkj.scf.bundle.common.dto.demand.enums.BusinessTypeEnum;
import com.zjkj.scf.bundle.common.dto.demand.enums.BusinessVersionEnum;
import com.zjkj.scf.bundle.common.dto.demand.enums.DemandChannelEnum;
import org.springframework.beans.BeanUtils;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.CraftDemandStateEnum;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandSaveReq;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandSaveV3Req;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 工艺需求转换
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/20 11:47
 */
public class CraftDemandInfoConverter {

	public static final String DEMAND_CODE_IS_NULL = "DEMAND_CODE_IS_NULL";
	public static final String CRAFT_DEMAND_ID = "craft_demand_id";


	public static CraftDemandInfo newCraftDemand(CraftDemandSaveReq craftDemandSaveReq, BomOrder bomOrder, BomOrderMaterial bomMaterial) {
		CraftDemandInfo craftDemandInfo = new CraftDemandInfo();
		BeanUtils.copyProperties(craftDemandSaveReq, craftDemandInfo);
		craftDemandInfo.setCraftDemandId(IdPool.getId());
		craftDemandInfo.setBomId(bomOrder.getBomId());
		craftDemandInfo.setBomMaterialId(bomMaterial.getBomMaterialId());
		craftDemandInfo.setState(CraftDemandStateEnum.SUBMIT.getCode());
		craftDemandInfo.setPrototypeId(bomOrder.getPrototypeId());
		craftDemandInfo.setMaterialSnapshotId(bomMaterial.getMaterialSnapshotId());
		UserContent userContent = UserContentHolder.get();
		craftDemandInfo.setCreatorId(userContent.getCurrentUserId());
		craftDemandInfo.setCreatorName(userContent.getCurrentUserName());
		craftDemandInfo.setCreatedTime(LocalDateTime.now());
		craftDemandInfo.setReviserId(userContent.getCurrentUserId());
		craftDemandInfo.setReviserName(userContent.getCurrentUserName());
		craftDemandInfo.setRevisedTime(LocalDateTime.now());
		return craftDemandInfo;
	}

	/**
	 * 拆版添加工艺需求
	 * @param demandSaveReq
	 * @param prototypeId
	 * @return
	 */
	public static CraftDemandInfo demolitionAddCraft(CraftDemandSaveReq demandSaveReq, Long prototypeId) {
		CraftDemandInfo craftDemandInfo = new CraftDemandInfo();
		BeanUtils.copyProperties(demandSaveReq, craftDemandInfo);
		UserContent userContent = UserContentHolder.get();
		craftDemandInfo.setCraftDemandId(IdPool.getId());
		craftDemandInfo.setPrototypeId(prototypeId);
		craftDemandInfo.setState(CraftDemandStateEnum.SUBMIT.getCode());
		craftDemandInfo.setCreatorName(userContent.getCurrentUserName());
		craftDemandInfo.setReviserName(userContent.getCurrentUserName());
		craftDemandInfo.setReviserId(userContent.getCurrentUserId());
		LocalDateTime now = LocalDateTime.now();
		craftDemandInfo.setRevisedTime(now);
		craftDemandInfo.setIsDeleted(Bool.NO.getCode());
		craftDemandInfo.setCreatorId(userContent.getCurrentUserId());
		craftDemandInfo.setCreatedTime(now);
		return craftDemandInfo;
	}

	/**
	 * 组装bom二次工艺同步履约请求数据
	 * @param craft
	 * @param prototypeHistory
	 * @param prototypeDetail
	 * @return
	 */
	@Deprecated(since = "v3.11")
	public static CraftDemandAddReq assemblyCraftDemandAddReq(CraftDemandInfo craft, PrototypeHistory prototypeHistory, PrototypeDetail prototypeDetail) {
		CraftDemandAddReq craftDemandAddReq = new CraftDemandAddReq();
		craftDemandAddReq.setBusinessType(BusinessTypeEnum.PROTOTYPE);

		CraftDemandAddReq.PrototypeReq prototypeReq = new CraftDemandAddReq.PrototypeReq();
		//prototypeReq.setCraftRelationDemandId(craft.getRelationDemandId());
		//prototypeReq.setCraftRelationDemandCode(DEMAND_CODE_IS_NULL);
		// prototypeReq.setPrototypeId(prototypeHistory.getFakeId());
		prototypeReq.setPrototypeCode(prototypeHistory.getDesignCode());
		prototypeReq.setPrototypeCategory(prototypeHistory.getCategoryName());
		// prototypeReq.setRegionId(prototypeHistory.getRegionId());
		prototypeReq.setMaterialSnapshotId(craft.getMaterialSnapshotId());
		// String regionName = prototypeHistory.getRegionName();
		// regionName = StringUtils.contains(regionName,"市" ) ? regionName : regionName + "市";
		// prototypeReq.setRegionName(regionName);
		Map<String, Object> extra = new HashMap<>(16);
		extra.put(CRAFT_DEMAND_ID, craft.getCraftDemandId());
		prototypeReq.setExtra(extra);
		craftDemandAddReq.setPrototypeReq(List.of(prototypeReq));

		CraftDemandAddReq.CraftDemandBaseReq craftDemandBaseReq = new CraftDemandAddReq.CraftDemandBaseReq();
		BeanUtils.copyProperties(craft, craftDemandBaseReq);
		craftDemandBaseReq.setDemandChannel(DemandChannelEnum.DESIGNER_CHOOSE_MATERIAL_NO_DEMAND.getCode());
		craftDemandBaseReq.setBusinessVersion(BusinessVersionEnum.PROTOTYPE_BULK_INTEGRATED);
		craftDemandBaseReq.setCreateUser(craft.getCreatorName());
		craftDemandBaseReq.setCreateId(craft.getCreatorId());
		craftDemandAddReq.setCraftDemandBaseReq(craftDemandBaseReq);

		CraftDemandAddReq.CraftDemandDetailReq craftDemandDetailReq = new CraftDemandAddReq.CraftDemandDetailReq();
		BeanUtils.copyProperties(craft, craftDemandDetailReq);
		craftDemandDetailReq.setCraftUndertakerObj(craft.getFactoryName());
		craftDemandDetailReq.setFactory(craft.getCustomerSupplyFactory());
		craftDemandDetailReq.setFactoryId(craft.getInnerFactoryId());
		// PrototypeStyleReferEnum prototypeStyleReferEnum = PrototypeStyleReferEnum.findByCode(prototypeDetail.getStyleReferType());
		// craftDemandDetailReq.setUrgencyType(Objects.nonNull(prototypeStyleReferEnum) ? prototypeStyleReferEnum.getDesc() : "");
		craftDemandAddReq.setCraftDemandDetailReq(craftDemandDetailReq);
		return craftDemandAddReq;
	}


	/**
	 * 要关闭的物料
	 * @param craftDemandInfo
	 * @param bomOrder
	 * @return
	 */
	public static CraftDemandInfo closeCraftDemandV3(CraftDemandInfo craftDemandInfo, BomOrder bomOrder) {
		CraftDemandInfo closeCraftDemandInfo = new CraftDemandInfo();
		BeanUtils.copyProperties(craftDemandInfo, closeCraftDemandInfo);
		closeCraftDemandInfo.setCraftDemandId(IdPool.getId());
		closeCraftDemandInfo.setBomId(bomOrder.getBomId());
		closeCraftDemandInfo.setState(CraftDemandStateEnum.CLOSED.getCode());
		return closeCraftDemandInfo;
	}

	/**
	 * 要关闭的物料（要提交有【暂存】的工艺， 关闭工艺需求)
	 *
	 * @param craftDemandInfo
	 * @param bomOrder
	 * @param craftDemandId 传暂存表的craftDemandId进来
	 * @return
	 */
	public static CraftDemandInfo closeCraftDemandV3ForUpgradeBom(CraftDemandInfo craftDemandInfo, BomOrder bomOrder, Long craftDemandId) {
		CraftDemandInfo closeCraftDemandInfo = new CraftDemandInfo();
		BeanUtils.copyProperties(craftDemandInfo, closeCraftDemandInfo);
		closeCraftDemandInfo.setCraftDemandId(craftDemandId);
		closeCraftDemandInfo.setBomId(bomOrder.getBomId());
		closeCraftDemandInfo.setState(CraftDemandStateEnum.CLOSED.getCode());
		return closeCraftDemandInfo;
	}


	/**
	 * 创建工艺
	 *
	 * 先看临时表是否有数据，有的话，取临时表的id
	 * 若是辅料需求，待提交、找料中状态是没有materialSnapshotId。
	 * @param craftDemandSaveReq
	 * @param bomOrder
	 * @return
	 */
	public static CraftDemandInfo newCraftDemandV3(CraftDemandSaveV3Req craftDemandSaveReq, BomOrder bomOrder) {
		CraftDemandInfo craftDemandInfo = new CraftDemandInfo();
		BeanUtils.copyProperties(craftDemandSaveReq, craftDemandInfo);
		// 先看临时表是否有数据，有的话，取临时表的id
		Long craftDemandId = Optional.ofNullable(craftDemandSaveReq.getCraftDemandTransientId()).orElse(IdPool.getId());
		craftDemandInfo.setCraftDemandId(craftDemandId);
		craftDemandInfo.setBomId(bomOrder.getBomId());
		craftDemandInfo.setBomMaterialId(craftDemandSaveReq.getBomMaterialId());
		craftDemandInfo.setState(CraftDemandStateEnum.SUBMIT.getCode());
		craftDemandInfo.setPrototypeId(bomOrder.getPrototypeId());
		//beancopy会拷贝，无需设置。物料需求要传的。
//		craftDemandInfo.setRelationDemandId(craftDemandSaveReq.getRelationDemandId());
		//若是辅料需求，待提交、找料中状态是没有materialSnapshotId。
		craftDemandInfo.setMaterialSnapshotId(craftDemandInfo.getMaterialSnapshotId());
		return craftDemandInfo;
	}


}
