package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.PurchaseApplyFollow;
import tech.tiangong.sdp.design.mapper.PurchaseApplyFollowMapper;
import tech.tiangong.sdp.design.vo.dto.purchase.CuttingProcessUpdateDto;
import tech.tiangong.sdp.design.vo.req.purchase.PurchaseApplyFollowPageReq;
import tech.tiangong.sdp.design.vo.resp.purchase.PurchaseApplyFollowPageVO;

import java.util.List;


/**
* 剪版需求跟进表（采购申请跟进）
* <br>CreateDate August 09,2021
* <AUTHOR>
* @since 1.0
*/


@AllArgsConstructor
@Slf4j
@Repository
public class PurchaseApplyFollowRepository extends BaseRepository<PurchaseApplyFollowMapper, PurchaseApplyFollow> {


    private final PurchaseApplyFollowMapper purchaseApplyFollowMapper;
    public Integer selectCount(String designCode){
        Long count = lambdaQuery().eq(PurchaseApplyFollow::getDesignCode, designCode).count();
        return Math.toIntExact(count);
    }

    public String selectLatestGCCode(){
        return purchaseApplyFollowMapper.selectLatestGCCode();
    }

    public List<PurchaseApplyFollowPageVO> pageList(PurchaseApplyFollowPageReq req){
        return purchaseApplyFollowMapper.pageList(req);
    }

    /**
     * 查询辅料下采购时cuttingProcess记录异常的数据
     * @param purchaseOrderNo 采购单号, 为空时查全部
     */
    public List<CuttingProcessUpdateDto> listErrorCuttingProcess(String purchaseOrderNo) {
        return purchaseApplyFollowMapper.listErrorCuttingProcess(purchaseOrderNo);
    }


    /*public void updateBatchByConditions(List<PurchaseApplyFollow> purchaseApplyFollowList){
        purchaseApplyFollowMapper.updateBatchByConditions(purchaseApplyFollowList);
    }*/


}