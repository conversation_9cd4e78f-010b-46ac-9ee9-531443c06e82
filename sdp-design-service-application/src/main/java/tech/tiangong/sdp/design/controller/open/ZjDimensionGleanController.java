package tech.tiangong.sdp.design.controller.open;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.DimensionGleanTaskFollowService;
import tech.tiangong.sdp.design.vo.req.dimensionglean.SyncDimensionGleanPurchaseReq;
import tech.tiangong.sdp.design.vo.req.dimensionglean.SyncDimensionGleanReq;

import java.util.Objects;

/**
 * 承接zj中台和履约的3D采集（采购）任务-openapi
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/openapi/v1" + "/dimension-glean")
public class ZjDimensionGleanController {
    private final DimensionGleanTaskFollowService dimensionGleanTaskFollowService;
    /**
     * 同步3D采集任务
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/sync-glean-task")
    @NoRepeatSubmitLock
    public DataResponse<Void> syncGleanTask(@RequestBody @Validated SyncDimensionGleanReq req) {
        log.info("同步3D采集任务请求参数:{}", JSON.toJSONString(req));
        initUserContent();
        dimensionGleanTaskFollowService.syncDimensionGleanTask(req);
        return DataResponse.ok();
    }

    /**
     * 修改3D采集任务对应采购单的状态
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/sync-glean-purchase")
    @NoRepeatSubmitLock
    public DataResponse<Void> syncGleanPurchase(@RequestBody @Validated SyncDimensionGleanPurchaseReq req) {
        log.info("修改3D采集任务对应采购单的信息请求参数:{}", JSON.toJSONString(req));
        initUserContent();
        dimensionGleanTaskFollowService.syncDimensionGleanPurchase(req);
        return DataResponse.ok();
    }

    private void initUserContent(){
        //zj默认用户: 101010102	ZJ默认用户
        UserContent userContent = UserContentHolder.get();
        if (Objects.isNull(userContent)) {
            userContent = new UserContent();
            userContent.setCurrentUserId(101010102L);
            userContent.setCurrentUserCode("101010102");
            userContent.setCurrentUserName("ZJ默认用户");
            userContent.setTenantId(2L);
            userContent.setSystemCode("SDP");
            UserContentHolder.set(userContent);
        }
        log.info("dimension-glean userContent: {}", JSON.toJSONString(userContent));
    }
}
