package tech.tiangong.sdp.design.remote;

import cn.yibuyun.framework.net.DataResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.material.pojo.web.query.AiCategoryMappingBatchQuery;
import tech.tiangong.sdp.material.pojo.web.response.AiCategoryMappingVO;
import tech.tiangong.sdp.material.sdk.service.remote.AiCategoryMappingRemoteService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/7 20:23
 */

@Component
@Slf4j
@RequiredArgsConstructor
public class AiCategoryMappingRemoteHelper {
    private final AiCategoryMappingRemoteService aiCategoryMappingRemoteService;

    public List<AiCategoryMappingVO> findByAiCategoryCode(List<String> aiCategoryCodes){
        AiCategoryMappingBatchQuery batchQuery = new AiCategoryMappingBatchQuery();
        batchQuery.setAiCategoryCodes(aiCategoryCodes);
        DataResponse<List<AiCategoryMappingVO>> response = aiCategoryMappingRemoteService.findByAiCategoryCode(batchQuery);
        SdpDesignException.isTrue(response.isSuccessful(),"根据AI品类code批量查询.错误信息:{}",response.getMessage());
        return response.getData();
    }


}
