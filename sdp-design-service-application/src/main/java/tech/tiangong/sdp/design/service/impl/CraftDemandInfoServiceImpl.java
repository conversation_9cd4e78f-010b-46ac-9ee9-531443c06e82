package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.repository.CraftDemandInfoRepository;
import tech.tiangong.sdp.design.service.CraftDemandInfoService;
import tech.tiangong.sdp.design.vo.resp.bom.CraftDemandInfoVo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工艺 服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/16 15:52
 */
@Slf4j
@AllArgsConstructor
@Service
public class CraftDemandInfoServiceImpl implements CraftDemandInfoService {

	private final CraftDemandInfoRepository craftDemandInfoRepository;




    @Override
    public List<CraftDemandInfoVo> getListByBomIdsAndState(List<Long> bomIdList, Integer state) {
		if (CollUtil.isEmpty(bomIdList)) {
			return List.of();
		}
		List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getListByBomIdsAndState(bomIdList, state);
		if (CollUtil.isEmpty(craftDemandInfoList)) {
			return List.of();
		}

		List<CraftDemandInfoVo> craftVoList = new ArrayList<>(craftDemandInfoList.size());
		//处理工艺图片
		craftDemandInfoList.forEach(craftDemandInfo -> {
			CraftDemandInfoVo craftDemandVo = new CraftDemandInfoVo();
			BeanUtils.copyProperties(craftDemandInfo, craftDemandVo);
			//工艺图片
			if (StringUtils.isNotBlank(craftDemandInfo.getPicture())) {
				craftDemandVo.setPictureList(Arrays.stream(craftDemandInfo.getPicture().split(",")).collect(Collectors.toList()));
			}
			craftVoList.add(craftDemandVo);
		});
		return craftVoList;
    }


}
