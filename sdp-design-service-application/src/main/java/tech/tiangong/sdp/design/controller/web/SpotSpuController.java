package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tech.tiangong.sdp.design.enums.SpuVisualHandleTypeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualErrorCodeEnum;
import tech.tiangong.sdp.design.service.SpotSpuService;
import tech.tiangong.sdp.design.vo.query.spot.SpotSpuQuery;
import tech.tiangong.sdp.design.vo.req.spot.*;
import tech.tiangong.sdp.design.vo.resp.prototype.VisualDemandInfoVo;
import tech.tiangong.sdp.design.vo.resp.spot.*;
import tech.tiangong.sdp.utils.PlmExcelExportUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


/**
 * 现货管理SPU-web
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:41
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/spot-spu")
public class SpotSpuController extends BaseController {
    private final SpotSpuService spotSpuService;

    /**
     * 列表  v0904
     *
     * @param query 分页参数
     * @return PageRespVo<SpotManagePageVo>
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<SpotManagePageVo>> page(@RequestBody @Validated SpotSpuQuery query) {
        return DataResponse.ok(spotSpuService.page(query));
    }

    /**
     * 核价_tryOn_视觉需求-信息查询
     *
     * @param req 查询对象
     * @return 核价与tryOn信息
     */
    @PostMapping("/price-try-on")
    public DataResponse<List<SpotPriceTryOnVo>> priceTryOnList(@RequestBody @Validated SpotPriceTryOnReq req) {
        return DataResponse.ok(spotSpuService.priceTryOnList(req));
    }

    /**
     * 新建款号
     *
     * @param req 请求参数
     * @deprecated 该创建入口废弃, 使用图片导入或excel导入  -素材中心1期
     * @return 响应结果
     */
    @Deprecated
    @NoRepeatSubmitLock
    @PostMapping("/self-create")
    public DataResponse<SpotSpuSelfCreateVo> selfCreate(@RequestBody @Validated SpotSpuSelfCreateReq req) {
        return DataResponse.ok(spotSpuService.selfCreate(req));
    }

    /**
     * 导入SPU
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/import")
    @NoRepeatSubmitLock(lockTime = 5L)
    public DataResponse<SpotSpuImportVo> importSpu(@RequestBody @Validated SpotSpuImportReq req) {
        return DataResponse.ok(spotSpuService.importSpu(req));
    }

    /**
     * excel导入
     *
     * @param file excel文件
     * @return 响应结果
     */
    @NoRepeatSubmitLock(lockTime = 5L)
    @PostMapping("/excel-import")
    public DataResponse<List<SpotSpuImportVo>> excelImport(MultipartFile file) throws IOException {
        return DataResponse.ok(spotSpuService.excelImport(file.getInputStream()));
    }

    /**
     * 详情 v0904
     *
     * @param styleCode spu编码
     * @return 响应结果
     */
    @GetMapping("/web-detail/{styleCode}")
    public DataResponse<SpotWebDetailVo> getWebDetail(@PathVariable(value = "styleCode") String styleCode) {
        return DataResponse.ok(spotSpuService.getWebDetail(styleCode));
    }

    /**
     * SPU信息
     *
     * @param styleCode spu编码
     * @return 响应结果
     */
    @GetMapping("/spu-info/{styleCode}")
    public DataResponse<SpotSpuVo> getSpuInfo(@PathVariable(value = "styleCode") String styleCode) {
        return DataResponse.ok(spotSpuService.getSpuInfo(styleCode));
    }

    /**
     * 编辑SPU v0904
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/update-spu-skc")
    public DataResponse<Void> updateSpuSkc(@RequestBody @Validated SpotSpuUpdateReq req) {
        spotSpuService.updateSpuSkc(req);
        return DataResponse.ok();
    }

    /**
     * 视觉弹框校验-spu编辑/复色
     *
     * @param req 入参
     * @return 异常信息集合(为空时可以发起视觉需求, 非空时不提交视觉需求)
     */
    @PostMapping("/visual-check")
    public DataResponse<List<String>> visualCheck(@RequestBody @Validated SpotVisualCheckReq req) {
        return DataResponse.ok(spotSpuService.visualCheck(req));
    }

    /**
     * 视觉弹框校验-批量提交
     *
     * @param styleCodeList spu编码
     * @return 异常信息集合(为空时可以发起视觉需求, 非空时不能批量提交)
     */
    @PostMapping("/batch-commit/visual-check")
    public DataResponse<List<String>> batchCommitVisualCheck(@RequestBody List<String> styleCodeList) {
        return DataResponse.ok(spotSpuService.batchCommitVisualCheck(styleCodeList));
    }

    /**
     * 批量提交
     *
     * @param req 查询对象
     * @return 异常信息
     */
    @PostMapping("/batch/commit")
    public DataResponse<List<String>> batchCommit(@RequestBody @Validated BatchCommitReq req) {
        return DataResponse.ok(spotSpuService.batchCommit(req));
    }

    /**
     * 上传图片校验
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/upload/picture/commit")
    public DataResponse<List<SpotSpuUploadPictureResultVo>> uploadCommit(@RequestBody @Validated UploadPictureReq req) {
        return DataResponse.ok(spotSpuService.uploadPictureByMatchingType(req));
    }

    /**
     * 导入图包(异步)
     * @return
     */
    @PostMapping("/upload/excel/sync")
    public DataResponse<List<SpotSpuUploadExcelResultVo>> uploadExcelSync(@RequestParam("file") MultipartFile file,
                                                                          @RequestParam("fileName") String fileName,
                                                                          @RequestParam("fileUrl") String fileUrl,
                                                                          @RequestParam("pictureType") Integer pictureType,
                                                                          @RequestParam("matchingType") Integer matchingType){
        UploadExcelReq req = new UploadExcelReq();
        req.setFile(file);
        req.setFileUrl(fileUrl);
        req.setFileName(fileName);
        req.setPictureType(pictureType);
        req.setMatchingType(matchingType);
        return DataResponse.ok(spotSpuService.uploadExcelSync(req));
    }
    /**
     * 复色
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/color-making")
    public DataResponse<SpotColorMakingVo> colorMaking(@RequestBody @Validated SpotColorMakingReq req) {
        return DataResponse.ok(spotSpuService.colorMaking(req));
    }

    /**
     * 导出现货管理数据
     */
    @NoRepeatSubmitLock
    @PostMapping("/export/excel")
    public DataResponse<Void> spotSpuExportExcel(@RequestBody @Validated SpotSpuQuery query, HttpServletResponse response) throws Exception {
        List<SpotSpuExportVo> resp = spotSpuService.spotSpuExportExcel(query);
        PlmExcelExportUtil.spotSpuTemplateExport(resp, response);
        return DataResponse.ok();
    }

    /**
     * 获取复核预估核价信息
     *
     * @param styleCode spu编码
     * @return 响应结果
     */
    @GetMapping("/get-re-estimate-check-price")
    public DataResponse<SpotSpuReEstimateCheckPriceVo> getReEstimateCheckPriceInfo(@RequestParam(value = "styleCode") String styleCode) {
        return DataResponse.ok(spotSpuService.getReEstimateCheckPriceInfo(styleCode));
    }

    /**
     * SPU供应商查询
     *
     * @param req 查询对象
     * @return 供应商信息
     */
    @PostMapping("/list-supplier")
    public DataResponse<List<SpotSupplierVo>> listSupplier(@RequestBody @Validated SpotSupplierListReq req) {
        return DataResponse.ok(spotSpuService.listSupplier(req));
    }

    /**
     * 查询视觉需求信息
     *
     * @param styleCode spu
     * @return 响应结果
     */
    @GetMapping("/visual-task-query/{styleCode}")
    public DataResponse<VisualDemandInfoVo> queryVisualTaskForEdit(@PathVariable(value = "styleCode") String styleCode) {
        return DataResponse.ok(spotSpuService.queryVisualTaskForEdit(styleCode));
    }

    /**
     * 发起视觉需求
     *
     * @param req 请求对象
     * @return 视觉需求id(若已创建)
     */
    @PostMapping("/submit-visual-task")
    @NoRepeatSubmitLock
    public DataResponse<Long> submitVisualTask(@RequestBody @Validated SpotVisualSubmitReq req) {
        req.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_START);
        try {
            return DataResponse.ok(spotSpuService.submitVisualTask(req));
        }catch (Exception e) {
            VisualErrorCodeEnum visualErrorCode = VisualErrorCodeEnum.findByCode(e.getMessage());
            if(!VisualErrorCodeEnum.UNKNOWN.equals(visualErrorCode)){
                log.error("发起视觉需求失败",e);
                DataResponse<Long> dataResponse = DataResponse.failed();
                dataResponse.setCode(visualErrorCode.getCode());
                dataResponse.setMessage(visualErrorCode.getDesc());
                dataResponse.setSuccessful(false);
                return dataResponse;
            }
            throw e;
        }
    }

    /**
     * 初始化风格历史数据
     * @param file
     * @return
     */
    @PostMapping("/initHisDataV2")
    public DataResponse<Void> initHisDataV2(MultipartFile file){
        spotSpuService.initHisDataV2(file);
        return DataResponse.ok();
    }
}
