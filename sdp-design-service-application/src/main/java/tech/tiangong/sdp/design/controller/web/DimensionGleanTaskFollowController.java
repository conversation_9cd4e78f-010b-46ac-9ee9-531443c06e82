package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.DimensionGleanTaskFollowService;
import tech.tiangong.sdp.design.vo.dimensiongleantask.DimensionGleanTaskExportQuery;
import tech.tiangong.sdp.design.vo.dimensiongleantask.DimensionGleanTaskPageQuery;
import tech.tiangong.sdp.design.vo.resp.dimensiongleantask.DimensionGleanTaskExcelResp;
import tech.tiangong.sdp.design.vo.resp.dimensiongleantask.DimensionGleanTaskListVo;
import tech.tiangong.sdp.design.vo.resp.dimensiongleantask.DimensionGleanTaskStateCountVo;
import tech.tiangong.sdp.utils.PlmExcelExportUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 3D采集任务-web
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/dimension-glean")
public class DimensionGleanTaskFollowController {

    private final DimensionGleanTaskFollowService dimensionGleanTaskFollowService;
    /**
     * 按采集任务状态统计3D采集任务数量
     *
     * @param
     * @return 响应结果
     */
    @PostMapping("/count-by-state")
    public DataResponse<List<DimensionGleanTaskStateCountVo>> countByState() {
        log.info("按采集任务状态统计3D采集任务数量");
        return DataResponse.ok(dimensionGleanTaskFollowService.countByState());
    }
    /**
     * 分页查询同步的3D采集任务
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/queryByPage")
    public DataResponse<PageRespVo<DimensionGleanTaskListVo>> queryByPage(@RequestBody DimensionGleanTaskPageQuery req) {
        log.info("分页查询同步的3D采集任务请求参数:{}", JSON.toJSONString(req));
        return DataResponse.ok(dimensionGleanTaskFollowService.queryByPage(req));
    }

    /**
     * 导出Excel
     * @param
     * @return Void
     */
    @NoRepeatSubmitLock
    @PostMapping("/export/excel")
    public void exportExcel(@RequestBody DimensionGleanTaskExportQuery query, HttpServletResponse response) throws Exception {
        List<DimensionGleanTaskExcelResp> excelResp = dimensionGleanTaskFollowService.exportExcel(query);
        PlmExcelExportUtil.dimensionGleanTaskTemplateExport(excelResp, response);
    }
}
