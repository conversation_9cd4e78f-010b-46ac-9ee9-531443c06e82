package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.base.Strings;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.enums.PrototypeStatusEnum;
import tech.tiangong.sdp.design.enums.SkcTypeEnum;
import tech.tiangong.sdp.design.mapper.PrototypeMapper;
import tech.tiangong.sdp.design.vo.dto.EncryptUpdateTestDto;
import tech.tiangong.sdp.design.vo.dto.style.DesignStyleOldDto;
import tech.tiangong.sdp.design.vo.dto.style.PrototypeUpdateSpuIdDto;
import tech.tiangong.sdp.design.vo.dto.style.Spu2SkcUpdateDto;
import tech.tiangong.sdp.design.vo.query.prototype.PrototypeQuery;
import tech.tiangong.sdp.design.vo.req.clothingDesign.ClothingDesignPrototypeDetailBatchQuery;
import tech.tiangong.sdp.design.vo.req.clothingDesign.ClothingDesignSkcBaseQuery;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SkcBaseInnerReq;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SkcBomSubmitReq;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SkcInnerQuery;
import tech.tiangong.sdp.design.vo.resp.SkcTagVO;
import tech.tiangong.sdp.design.vo.resp.clothingDesign.ClothingDesignBaseSkcVo;
import tech.tiangong.sdp.design.vo.resp.clothingDesign.prototype.ClothingDesignPrototypeDetailVo;
import tech.tiangong.sdp.design.vo.resp.prototype.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 版单-主表服务仓库类
 *
 * <AUTHOR>
 * @since 2021-08-09 15:52:50
 */
@AllArgsConstructor
@Repository
public class PrototypeRepository extends BaseRepository<PrototypeMapper, Prototype> {

    public IPage<PrototypeListVo> findPage(PrototypeQuery query) {
        IPage<PrototypeListTempVo> page = baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
        IPage<PrototypeListVo> prototypeListVoPage = new Page<>();
        BeanUtils.copyProperties(page, prototypeListVoPage);

        List<PrototypeListVo> list = page.getRecords().stream().map(tempVo -> {
            PrototypeListVo prototypeListVo = new PrototypeListVo();
            BeanUtils.copyProperties(tempVo, prototypeListVo);
            List<String> customerPicture = StrUtil.splitTrim(tempVo.getCustomerPicture(), StrUtil.COMMA);
            prototypeListVo.setCustomerPicture(customerPicture);

            List<String> designPicture = StrUtil.splitTrim(tempVo.getDesignPicture(), StrUtil.COMMA);
            prototypeListVo.setDesignPicture(designPicture);

            //已拆版的数据,则返回第一次拆版完成的时间
            if (PrototypeStatusEnum.DECOMPOSED.getCode().equals(prototypeListVo.getPrototypeStatus())) {
                prototypeListVo.setCurrentTime(tempVo.getFirstVersionDoneTime());
            }
            //为了和前端统一计算当前耗时（）
            //环节创建时间（用于计算当前耗时【currentTime-processingStepCreatedTime】）（对应拆版的：款生成时间）
            prototypeListVo.setProcessingStepCreatedTime(tempVo.getSkcCreatedTime());

            return prototypeListVo;
        }).collect(Collectors.toList());

        prototypeListVoPage.setRecords(list);

        return prototypeListVoPage;
    }

    public IPage<ClothingDesignPrototypeDetailVo> findPageForCommon(ClothingDesignPrototypeDetailBatchQuery query) {
        return baseMapper.findPageForCommon(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public IPage<SkcInnerQueryVo> skcPageInner(SkcInnerQuery query) {
        return baseMapper.skcPageInner(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    /**
     * 更新id和内容
     *
     * @param prototype
     * @param originPrototypeId 原prototypeId
     * @return
     */
    public Boolean updateIdAndPrototype(Prototype prototype, Long originPrototypeId) {
        return baseMapper.updateIdAndPrototype(prototype, originPrototypeId);
    }

    public Prototype getByDesignCode(String designCode) {
        return getOne(Wrappers.<Prototype>lambdaQuery().eq(Prototype::getDesignCode, designCode));
    }


    public List<Prototype> getByStyleCodes(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return new ArrayList<>();
        }
        /*return lambdaQuery()
                .eq(Prototype::getIsDoneVersion,1)
                .eq(Prototype::getIsCanceled,0)
                .in(Prototype::getStyleCode, styleCodeList)
                .groupBy(Prototype::getStyleCode)
                .list();*/
        return lambdaQuery()
                .in(Prototype::getStyleCode, styleCodeList)
                .list();
    }

    public List<Prototype> getNotCancelByStyleCodes(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return new ArrayList<>();
        }
        return lambdaQuery()
                .in(Prototype::getStyleCode, styleCodeList)
                .eq(Prototype::getIsCanceled,  Bool.NO.getCode())
                .list();
    }

    public List<Prototype> listByStyleCode(String styleCode) {
        return baseMapper.selectList(new QueryWrapper<Prototype>().lambda().eq(Prototype::getStyleCode, styleCode)
                .eq(Prototype::getIsDeleted, cn.yibuyun.framework.enumeration.Bool.NO.getCode()));
    }

    public Prototype getByStyleCode(String styleCode, Integer skcType) {
        return baseMapper.selectOne(new QueryWrapper<Prototype>().lambda().eq(Prototype::getStyleCode, styleCode)
                .eq(Prototype::getSkcType, skcType)
                .eq(Prototype::getIsDeleted, cn.yibuyun.framework.enumeration.Bool.NO.getCode()));
    }

    public Prototype getLatestSubmitByStyleCode(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            return null;
        }
        return lambdaQuery()
                .eq(Prototype::getStyleCode, styleCode)
                .eq(Prototype::getPrototypeStatus, PrototypeStatusEnum.DECOMPOSED.getCode())
                .eq(Prototype::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(Prototype::getSubmitTime).last("limit 1").one();
    }


    public List<Prototype> getListByStyleCode(String styleCode) {
        return baseMapper.selectList(new QueryWrapper<Prototype>().lambda().eq(Prototype::getStyleCode, styleCode)
                .eq(Prototype::getIsDeleted, cn.yibuyun.framework.enumeration.Bool.NO.getCode()));
    }

    public List<MakeSameDesignCodeVo> queryMakeSameByDesignCode(String designCode) {
        return baseMapper.queryMakeSameByDesignCode(designCode);
    }

    public List<PurchasePrototypeTempVo> queryPurchaseRelPrototype() {
        return baseMapper.queryPurchaseRelPrototype();
    }

    public List<ComplementMissingPrototypeVo> queryPurchaseNotRelPrototype() {
        return baseMapper.queryPurchaseNotRelPrototype();
    }

    public List<Prototype> listByDesignCodes(Collection<String> designCodeList) {
        if (CollUtil.isEmpty(designCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(Prototype::getDesignCode, designCodeList).list();
    }

    /**
     * 分组查询所有客户联系人手机号
     *
     * @return
     */
    public List<String> getPurchaserPhone(String purchaserCode) {
        return Collections.emptyList();
    }


    // ================== 测试用接口 不要调用 以下 ===================


    public boolean updateEncryptDtoTest(EncryptUpdateTestDto updateTestDto) {
        if (Objects.isNull(updateTestDto)) {
            return false;
        }
        return baseMapper.updateEncryptDtoTest(updateTestDto);
    }

    public EncryptUpdateTestDto getEncryptDtoTest(Long prototypeId) {
        if (Objects.isNull(prototypeId)) {
            return null;
        }
        return baseMapper.getEncryptDtoTest(prototypeId);
    }

    // ================== 测试用接口 不要调用 以上 ===================


    /**
     * 根据设计款编码查询最新的拆版数据
     *
     * @param designCodes
     * @return
     */
    public List<Prototype> getLastByDesignCodes(List<String> designCodes) {
        if (CollectionUtil.isEmpty(designCodes)) {
            return new ArrayList<>();
        }
        return list(Wrappers.<Prototype>lambdaQuery()
                .in(Prototype::getDesignCode, designCodes)
                .eq(Prototype::getPrototypeStatus, PrototypeStatusEnum.DECOMPOSED.getCode())
                .eq(Prototype::getIsCanceled, Bool.NO.getCode())
                .eq(Prototype::getIsDeleted, Bool.NO.getCode()));
    }

    /**
     * 根据款号获取skc创建时间最新的版单（解决批量插入创建时间一致问题）
     *
     * @param styleCode spu号
     * @return
     */
    public Prototype getLastSkcByStyleCode(String styleCode) {
        if (Strings.isBlank(styleCode)) {
            return null;
        }
        return getOne(new QueryWrapper<Prototype>().lambda()
                .eq(Prototype::getStyleCode, styleCode)
                .orderByDesc(Prototype::getSkcCreatedTime).last("limit 1"));
    }

    /**
     * 根据款号获取最新的版单
     *
     * @param styleCode spu号
     * @return
     */
    public Prototype getLastByStyleCode(String styleCode) {
        if (Strings.isBlank(styleCode)) {
            return null;
        }
        return getOne(new QueryWrapper<Prototype>().lambda()
                .eq(Prototype::getStyleCode, styleCode)
                .orderByDesc(Prototype::getSkcCreatedTime).last("limit 1"));
    }

    public List<PrototypeSplicingInfoVo> queryIsSplicingByPrototypeIds(List<Long> prototypeIdList) {
        return baseMapper.queryIsSplicingByPrototypeIds(prototypeIdList);
    }

    public void batchUpdateSpuId(List<PrototypeUpdateSpuIdDto> prototypeUpdateSpuIdList) {
        if (CollUtil.isEmpty(prototypeUpdateSpuIdList)) {
            return;
        }
        baseMapper.batchUpdateSpuId(prototypeUpdateSpuIdList);
    }

    public List<DesignStyleOldDto> latestSubmitByStyleCodes(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return List.of();
        }
        return baseMapper.latestSubmitByStyleCodes(styleCodeList);
    }

    public void batchUpdateCategoryInfo(List<Spu2SkcUpdateDto> updateList) {
        if (CollUtil.isEmpty(updateList)) {
            return;
        }
        baseMapper.batchUpdateCategoryInfo(updateList);
    }

    public List<SkcTagVO> querySkcTag(Set<String> designCodes) {
        if (CollectionUtil.isEmpty(designCodes)) {
            return List.of();
        }
        return baseMapper.querySkcTag(designCodes);
    }


    public Integer refreshStepByDesignCodes(List<String> skcCodeList) {
        if (CollectionUtil.isEmpty(skcCodeList)) {
            return null;
        }
        return baseMapper.refreshStepByDesignCodes(skcCodeList);
    }

    // public List<Prototype> getSkcColor(List<String> colorCodeList) {
    //     if (CollectionUtil.isEmpty(colorCodeList)) {
    //         return List.of();
    //     }
    //     return lambdaQuery()
    //             .in(Prototype::getColorCode, colorCodeList)
    //             .list();
    // }

    // public List<Prototype> groupByColorCodes(List<String> colorCodeList) {
    //     if (CollectionUtil.isEmpty(colorCodeList)) {
    //         return List.of();
    //     }
    //     return lambdaQuery()
    //             .in(Prototype::getColorCode, colorCodeList)
    //             .groupBy(Prototype::getColorCode)
    //             .list();
    // }

    public Integer countByDesignCodeAndDesignerId(String designCode, Long designerId) {
        if (Objects.isNull(designerId) || StringUtils.isBlank(designCode)) {
            return 0;
        }
        Long count = lambdaQuery()
                .eq(Prototype::getDesignCode, designCode)
                .eq(Prototype::getDesignerId, designerId)
                .count();
        return Math.toIntExact(count);
    }

    public List<Prototype> listStyleVersionIdIsNull(String designCode) {
        return lambdaQuery()
                .eq(StringUtils.isNotBlank(designCode), Prototype::getDesignCode, designCode)
                .isNull(Prototype::getDesignStyleVersionId)
                .list();
    }

    public List<ClothingDesignBaseSkcVo> findBaseSkc(ClothingDesignSkcBaseQuery query) {
        return baseMapper.findBaseSkc(query);
    }


    public Prototype getFirstByDesignCode(String designCode) {
        if (StrUtil.isBlank(designCode)) {
            return null;
        }
        return lambdaQuery()
                .eq(Prototype::getDesignCode, designCode)
                .orderByAsc(Prototype::getSkcCreatedTime).last("limit 1").one();
    }

    public List<Prototype> listBomSubmitSkc(SkcBomSubmitReq req) {
        return baseMapper.listBomSubmitSkc(req);
    }

    public List<SkcBaseInnerVo> listBaseSkc(SkcBaseInnerReq req) {
        if (CollUtil.isEmpty(req.getDesignCodeList())) {
            return Collections.emptyList();
        }
        return baseMapper.listBaseSkc(req);
    }

    public Prototype normalSkc(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            return null;
        }
        return lambdaQuery()
                .eq(Prototype::getStyleCode, styleCode)
                .eq(Prototype::getSkcType, SkcTypeEnum.NORMAL.getCode())
                .last("limit 1").one();
    }

    public List<Prototype> listByStyleCodes(List<String> styleCodeList, Integer prototypeStatus) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(Prototype::getStyleCode, styleCodeList)
                .eq(Objects.nonNull(prototypeStatus), Prototype::getPrototypeStatus, prototypeStatus)
                .list();
    }
}