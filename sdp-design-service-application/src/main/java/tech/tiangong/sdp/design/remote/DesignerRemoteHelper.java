package tech.tiangong.sdp.design.remote;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.yibuyun.framework.net.DataResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.material.pojo.dto.DesignerDTO;
import tech.tiangong.sdp.material.pojo.web.request.designer.DesignerRemoteReq;
import tech.tiangong.sdp.material.sdk.service.remote.DesignerRemoteService;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/8/19
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DesignerRemoteHelper {
    private final DesignerRemoteService designerRemoteService;

    public DesignerDTO getByDesignerId(DesignerRemoteReq designerRemoteReq) {
        DataResponse<List<DesignerDTO>> response = designerRemoteService.designerInfoList(designerRemoteReq);
        Assert.notNull(response, "查询设计师失败");
        Assert.isTrue(response.isSuccessful(), response.getMessage());
        List<DesignerDTO> data = response.getData();
        return CollectionUtil.isEmpty(data) ? null : data.get(0);
    }
    public List<DesignerDTO> listByDesignerId(List<String> designerIdList) {
        SdpDesignException.notEmpty(designerIdList, "设计师id为空");
        DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
        designerRemoteReq.setDesignerIdList(designerIdList);
        DataResponse<List<DesignerDTO>> response = designerRemoteService.designerInfoList(designerRemoteReq);
        Assert.notNull(response, "查询设计师失败");
        Assert.isTrue(response.isSuccessful(), response.getMessage());
        return response.getData();
    }
}
