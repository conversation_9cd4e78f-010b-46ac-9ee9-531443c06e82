package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.enums.ClothingCodeSourceEnum;
import tech.tiangong.sdp.design.vo.req.ClothingCodeGenerateReq;

/**
 * 样衣编号生成表服务接口
 *
 * <AUTHOR>
 */
public interface ClothingCodeGenerateService {


    /**
     * 样衣编号自增
     *
     * @param req 请求入参数
     * @return
     */
    String generate(ClothingCodeGenerateReq req);

    /**
     * 生成SPU编码
     * @since 1212-v3.20.2
     *
     * @param sourceEnum 来源
     * @return spu编码
     */
    String generateSpuCode(ClothingCodeSourceEnum sourceEnum);

    /**
     * 生成SPU编码（改款）
     * @since 1212-v3.20.2
     *
     * @param sourceEnum 来源
     * @return spu编码
     */
    //todo 删除
    String generateSpuCodeRemake(ClothingCodeSourceEnum sourceEnum, String parentSpuCode);

    /**
     * 生成SKC编码（正常）
     *
     * @since 1212-v3.20.2
     * @param sourceEnum 来源
     * @param spuCode spu编码
     * @return skc编码
     */
    String generateSkcCode(ClothingCodeSourceEnum sourceEnum, String spuCode);

    /**
     * 生成SKC编码（复色）
     * @since 1212-v3.20.2
     *
     * @param sourceEnum 来源
     * @param parentSkcCode 父skcCode
     * @return skc编码
     */
    String generateSkcCodeColorMarking(ClothingCodeSourceEnum sourceEnum, String parentSkcCode);

    /**
     * 初始化历史数据
     */
    void initHistory();

    /**
     * 初始化新的款号redis值
     *
     * @since 1212-v3.20.2
     */
    void initNewCode();
}