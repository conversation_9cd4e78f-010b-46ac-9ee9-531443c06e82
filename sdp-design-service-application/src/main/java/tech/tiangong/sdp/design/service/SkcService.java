package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.req.mq.ClothesChangeMqDto;
import tech.tiangong.sdp.design.vo.req.prototype.inner.*;
import tech.tiangong.sdp.design.vo.resp.prototype.*;
import tech.tiangong.sdp.design.vo.resp.style.PopCreateProductVo;

import java.util.List;

/**
 * SKC 服务接口
 *
 * <AUTHOR>
 */
public interface SkcService {


    //==================== inner查询 =========================
    /**
     * 分页查询
     *
     * @param query 分页对象
     * @return PageRespVo<SkcInnerQueryVo>
     */
    PageRespVo<SkcInnerQueryVo> pageInner(SkcInnerQuery query);


    /**
     * 发起打版-inner
     *
     * @param req 入参
     * @return 打版信息
     */
    AskClothesVo askClothes(AskClothesInnerReq req);

    /**
     * skc详情查询_包含SPU与上架图(根据设计款号或id)
     *
     * @param req 入参
     * @return SkcDetailInnerVo
     */
    SkcDetailInnerVo getSkcInfo(SkcInfoReq req);

    /**
     * 获取已提交版本的skc信息
     *
     * @param designCode 设计款号
     * @return 响应结果
     */
    PrototypeInnerVo getSavedInfoByDesignCode(String designCode);

    /**
     * 根据spu获取最新已提交版本的skc信息
     *
     * @param styleCode SPU款号
     * @return 响应结果
     */
    PrototypeInnerVo latestSubmitBySpu(String styleCode);

    /**
     * 获取已提交版本的skc信息_包含spu信息
     *
     * @param designCode 设计款号
     * @return 响应结果
     */
    SkcSpuInnerVo getSaveInfoWithSpu(String designCode);

    /**
     * 根据spu获取最新已提交版本的skc详情(包含spu信息)
     *
     * @param styleCode SPU款号
     * @return 响应结果
     */
    SkcSpuInnerVo latestSubmitWithSpu(String styleCode);

    /**
     * 根据spu查询正常款的设计图信息
     *
     * @param req 入参
     * @return List<NormalSkcImageVo>
     */
    List<NormalSkcImageVo> getNormalImage(SkcImageQuery req);

    /**
     * 设计款skc推送商品运营平台
     * @param req 入参
     * @return Boolean
     */
    Boolean pushSkc(SkcPriceReq req);

    /**
     * 修改打版方式-通知致景齐套预占位
     *
     * @param req 入参
     */
    void prePlacementMaterial2Zj(PrePlacementMaterialReq req);

    /**
     * 打版变更同步致景
     * @param mqDto 消息体
     */
    void clothesChange2Zj(ClothesChangeMqDto mqDto);

    /**
     * 根据spu查询所有已提交bom单的skc
     *
     * @param req 入参
     * @return SkcBomSubmitVo
     */
    SkcBomSubmitVo listBomSubmitSkc(SkcBomSubmitReq req);

    /**
     * skc信息基础信息查询
     *
     * @param req 入参
     * @return List<SkcBaseInnerVo>
     */
    List<SkcBaseInnerVo> listBaseSkc(SkcBaseInnerReq req);

    /**
     * 根据spu查询正常款的skc基础信息
     * @param styleCode spu
     * @return 正常款skc信息
     */
    SkcBaseInnerVo normalBySpu(String styleCode);

    /**
     * 批量 设计款skc推送商品运营平台
     * @param req 入参
     * @return Boolean
     */
    Boolean batchPushSkc(List<SkcPriceReq> req);

    /**
     * 根据skc批量查询上架图信息
     * @param req 入参
     * @return skc上架图信息
     */
    List<SkcOnShelfImageVo> listOnShelfImg(SkcOnShelfImgInnerReq req);

    /**
     * 根据spu查询pop创建商品的信息
     * @param styleCodeList spu集合
     * @return pop创建商品信息
     */
    List<PopCreateProductVo> queryStyleInfo4Pop(List<String> styleCodeList);
}