package tech.tiangong.sdp.design.service.download;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件下载 策略工厂类（基于 Spring 注入）
 *
 * <AUTHOR>
 */
@Service
public class DownloadTaskStrategyFactory {

    /**
     *  文件下载 策略集合
     */
    private final Map<DesignAsyncTaskTypeEnum, DownloadTaskStrategy> strategyMap = new ConcurrentHashMap<>();

    /**
     * 装载下载策略器
     *
     * @param strategyList  下载策略器集合
     */
    public DownloadTaskStrategyFactory(Collection<DownloadTaskStrategy> strategyList) {
        if (!CollectionUtils.isEmpty(strategyList)) {
            strategyList.forEach(strategy -> strategyMap.putIfAbsent(strategy.getTaskType(), strategy));
        }
    }

    public DownloadTaskStrategy getStrategy(DesignAsyncTaskTypeEnum taskType) {
        DownloadTaskStrategy strategy = strategyMap.get(taskType);
        if (strategy == null) {
            throw new IllegalArgumentException("Unsupported business type: " + taskType);
        }
        return strategy;
    }
}