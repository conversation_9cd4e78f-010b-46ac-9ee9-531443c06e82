package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import com.yibuyun.scm.common.dto.accessories.response.CategoryPageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.remote.zj.scm.ZjSCMAccessoriesCategoryClient;

import java.util.List;

/**
 * @Created by jero<PERSON><PERSON>
 * @ClassName SCMController
 * @Description scm 接口
 * @Date 2024/12/3 21:09
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/scm")
public class SCMController {

    private final ZjSCMAccessoriesCategoryClient scmAccessoriesCategoryClient;

    /**
     * 类目 - 商品类目
     * <p>
     * 商品类型
     * </p>
     * <p>
     *     <ul>
     *        <li>
     *            辅料 : 10
     *        </li>
     *        <li>
     *            工艺 : 20
     *        </li>
     *     </ul>
     * </p>
     *
     * @param productType 商品类型
     * @return 类目
     */
    @GetMapping(value = "/product/inner/v1/category/list/{productType}")
    DataResponse<List<CategoryPageVo>> listByProductType(@PathVariable("productType") String productType) {
        return scmAccessoriesCategoryClient.listByProductType(productType);
    }
}
