package tech.tiangong.sdp.design.service.impl;

import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.entity.MaterialSkuSnapshot;
import tech.tiangong.sdp.design.repository.MaterialSkuSnapshotRepository;
import tech.tiangong.sdp.design.service.MaterialSkuSnapshotService;
import tech.tiangong.sdp.design.vo.query.MaterialSkuSnapshotQuery;
import tech.tiangong.sdp.design.vo.req.material.MaterialSkuSnapshotReq;
import tech.tiangong.sdp.design.vo.resp.material.MaterialSkuSnapshotVo;

import java.util.Objects;

/**
 * 好料网sku快照表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialSkuSnapshotServiceImpl implements MaterialSkuSnapshotService {
    private final MaterialSkuSnapshotRepository materialSkuSnapshotRepository;

    @Override
    public PageRespVo<MaterialSkuSnapshotVo> page(MaterialSkuSnapshotQuery query) {
        IPage<MaterialSkuSnapshotVo> page = materialSkuSnapshotRepository.findPage(query);
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    public MaterialSkuSnapshotVo getById(Long id) {
        MaterialSkuSnapshot entity = materialSkuSnapshotRepository.getById(id);
        MaterialSkuSnapshotVo vo = new MaterialSkuSnapshotVo();
        if (Objects.isNull(entity)) {
            return vo;
        }
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(MaterialSkuSnapshotReq req) {
        MaterialSkuSnapshot entity = new MaterialSkuSnapshot();
        BeanUtils.copyProperties(req, entity);
        materialSkuSnapshotRepository.save(entity);
    }

}
