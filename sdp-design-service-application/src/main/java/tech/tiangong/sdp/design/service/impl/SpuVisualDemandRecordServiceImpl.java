package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.cache.commands.CacheCommands;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.constant.DesignRedisConstants;
import tech.tiangong.sdp.design.entity.SpuVisualDemandRecord;
import tech.tiangong.sdp.design.repository.SpuVisualDemandRecordRepository;
import tech.tiangong.sdp.design.service.SpuVisualDemandRecordService;
import tech.tiangong.sdp.design.vo.req.visual.SpuVisualDemandRecordSaveReq;

import java.util.Objects;
import java.util.concurrent.locks.Lock;

/**
 * 视觉需求创建记录(SpuVisualDemandRecord)服务
 *
 * <AUTHOR>
 * @since 2025-06-05 18:27:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpuVisualDemandRecordServiceImpl implements SpuVisualDemandRecordService {
    private final SpuVisualDemandRecordRepository spuVisualDemandRecordRepository;
    private final CacheCommands cacheCommands;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpuVisualDemandRecord create(SpuVisualDemandRecordSaveReq req) {
        Lock lock = cacheCommands.getDistributedMutexLock(DesignRedisConstants.getVisualCreateLock(req.getStyleCode()));
        boolean tryLock = lock.tryLock();
        Assert.isTrue(tryLock, "正在处理，请稍后再试");
        try {
            //spu下最新记录
            SpuVisualDemandRecord oldDemandRecord = spuVisualDemandRecordRepository.getLatestBySpu(req.getStyleCode());

            //新增需求记录
            int versionNum = 1;
            if (Objects.nonNull(oldDemandRecord)) {
                versionNum = oldDemandRecord.getVersionNum() + 1;
            }
            SpuVisualDemandRecord entity = new SpuVisualDemandRecord();
            BeanUtils.copyProperties(req, entity);
            entity.setDemandImageList(CollUtil.isEmpty(req.getDemandImageList())? null: req.getDemandImageList());
            entity.setBackgroundPicList(CollUtil.isEmpty(req.getBackgroundPicList())? null: req.getBackgroundPicList());
            entity.setModelPicList(CollUtil.isEmpty(req.getModelPicList())? null: req.getModelPicList());
            entity.setPosturePicList(CollUtil.isEmpty(req.getPosturePicList())? null: req.getPosturePicList());
            entity.setModelReferenceImage(CollectionUtil.isEmpty(req.getModelReferenceImageList())? null :JSON.toJSONString(req.getModelReferenceImageList()));
            entity.setBackgroundImage(CollectionUtil.isEmpty(req.getBackgroundImageList())? null :JSON.toJSONString(req.getBackgroundImageList()));
            entity.setModelFaceImage(CollectionUtil.isEmpty(req.getModelFaceImageList())? null :JSON.toJSONString(req.getModelFaceImageList()));
            entity.setDemandRecordId(IdPool.getId());
            entity.setVersionNum(versionNum);
            entity.setLatestState(1);
            spuVisualDemandRecordRepository.save(entity);

            // 更新旧记录, 设置latestState=0
            if (Objects.nonNull(oldDemandRecord)) {
                SpuVisualDemandRecord updateRecord = new SpuVisualDemandRecord();
                updateRecord.setDemandRecordId(oldDemandRecord.getDemandRecordId());
                oldDemandRecord.setLatestState(0);
                spuVisualDemandRecordRepository.updateById(oldDemandRecord);
            }
            return entity;
        } finally {
            lock.unlock();
        }

    }

}
