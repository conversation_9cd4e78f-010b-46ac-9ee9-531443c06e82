package tech.tiangong.sdp.design.controller.app;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.OrderMaterialFollowService;
import tech.tiangong.sdp.design.vo.req.ordermaterial.SignMaterialOrderReq;
import tech.tiangong.sdp.design.vo.req.ordermaterial.SignMaterialOrderToDesignCodeReq;

/**
 *
 * 齐套签收-app
 *
 * <br>CreateDate August 10,2021
 * <AUTHOR>
 * @since 1.0
 */
@RestController
@RequestMapping(UrlVersionConstant.APP + UrlVersionConstant.VERSION_V1 + "/order/material")
public class OrderMaterialAppController {

    @Autowired
    private OrderMaterialFollowService orderMaterialFollowService;

    /**
     * 齐套签收 app端（扫码签收）
     *
     * @param req 以齐套单号维度进行签收
     * @return void
     */
    @PostMapping("/sign-order-material")
    public DataResponse<String> sign(@RequestBody @Validated SignMaterialOrderReq req){
        return DataResponse.ok(orderMaterialFollowService.signByOrderMaterialCode(req));
    }


    /**
     * 齐套签收 app端（手输设计款号签收）
     *
     * @param req 以设计款号号维度进行签收
     * @return void
     */
    @PostMapping("/sign-by-design-code")
    public DataResponse<String> signByDesignCode(@RequestBody @Validated SignMaterialOrderToDesignCodeReq req){
        return DataResponse.ok(orderMaterialFollowService.signByApp(req));
    }
}
