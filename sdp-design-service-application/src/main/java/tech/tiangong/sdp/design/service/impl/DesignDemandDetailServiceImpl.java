package tech.tiangong.sdp.design.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.design.entity.DesignDemandDetail;
import tech.tiangong.sdp.design.repository.DesignDemandDetailRepository;
import tech.tiangong.sdp.design.service.DesignDemandDetailService;
import tech.tiangong.sdp.design.vo.resp.demand.DesignDemandDetailVo;

import java.util.Objects;

/**
 * 灵感设计需求_详情表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DesignDemandDetailServiceImpl implements DesignDemandDetailService {
    private final DesignDemandDetailRepository designDemandDetailRepository;


    @Override
    public DesignDemandDetailVo getById(Long id) {
        DesignDemandDetail entity = designDemandDetailRepository.getById(id);
        if (Objects.isNull(entity)) {
            return null;
        }
        DesignDemandDetailVo vo = new DesignDemandDetailVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }


}
