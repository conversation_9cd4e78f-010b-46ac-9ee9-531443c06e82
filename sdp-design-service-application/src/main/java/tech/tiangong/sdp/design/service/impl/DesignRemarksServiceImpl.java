package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.business.remark.*;
import tech.tiangong.sdp.design.entity.DesignRemarks;
import tech.tiangong.sdp.design.enums.DesignRemarksBizTypeEnum;
import tech.tiangong.sdp.design.repository.DesignRemarksRepository;
import tech.tiangong.sdp.design.service.DesignRemarksService;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksBatchBizListReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksListReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;
import tech.tiangong.sdp.design.vo.resp.remark.DesignRemarksBatchListReq;
import tech.tiangong.sdp.design.vo.resp.remark.DesignRemarksVO;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* 设计打版备注信息
* <br>CreateDate August 10,2021
* <AUTHOR>
* @since 1.0
*/

@AllArgsConstructor
@Service
public class DesignRemarksServiceImpl implements DesignRemarksService {

    private final DesignRemarksRepository designRemarksRepository;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DesignRemarksVO create(DesignRemarksReq req) {
        UserContent userContent = UserContentHolder.get();
        DesignRemarks designRemarks = new DesignRemarks();
        BeanUtils.copyProperties(req,designRemarks);
        designRemarks.setDesignRemarksId(IdPool.getId());
        DesignRemarksBizTypeEnum designRemarksBizTypeEnum = DesignRemarksBizTypeEnum.valueOf(req.getBizType());
        Assert.notNull(designRemarksBizTypeEnum,"不存在此业务类型:{}",req.getBizType());
        ComposeDesignRemark composeDesignRemark = null;
        switch (designRemarksBizTypeEnum) {
            case MATERIAL_PURCHASE -> composeDesignRemark = new ComposeDesignRemarkPurchase();
            case BOM_ORDER -> composeDesignRemark = new ComposeDesignRemarkBomOrder();
            case DESIGN_PROTOTYPE, CANCELLED -> composeDesignRemark = new ComposeDesignRemarkPrototye();
            case ORDER_MATERIAL_FOLLOW -> composeDesignRemark = new ComposeDesignRemarkMaterialFollow();
            case DESIGN_DEMAND -> composeDesignRemark = new ComposeDesignRemarkDesignDemand();
            case DIGITAL_PRINTING -> composeDesignRemark = new ComposeDesignRemarkDigitalPrinting();
            default -> Assert.isTrue(false, "无法处理业务类型:{}，请联系开发人员！", req.getBizType());
        }
        composeDesignRemark.compose(req,designRemarks);
        if(userContent != null){
            designRemarks.setCreatedName(userContent.getCurrentUserName());
        }
        designRemarksRepository.save(designRemarks);
        DesignRemarksVO vo = new DesignRemarksVO();
        BeanUtils.copyProperties(designRemarks,vo);
        return vo;
    }

    @Override
    public List<DesignRemarksVO> dataList(DesignRemarksListReq req) {

        List<DesignRemarks> designRemarkList = designRemarksRepository.getListByDesignCode(req.getDesignCode());
        if (CollectionUtil.isEmpty(designRemarkList)) {
            return Collections.emptyList();
        }
        //过滤掉Bom详情明细的备注
        List<DesignRemarksVO> remarksVOList = designRemarkList.stream().filter(remark ->
                !(StringUtils.equals(remark.getBizType(), DesignRemarksBizTypeEnum.BOM_ORDER.name()) && Objects.nonNull(remark.getBizChildId())))
                .map(remark -> {
                    DesignRemarksVO remarksVO = new DesignRemarksVO();
                    BeanUtils.copyProperties(remark, remarksVO);
                    return remarksVO;
                }).collect(Collectors.toList());
        return remarksVOList;
    }

    @Override
    public Map<String, List<DesignRemarksVO>> batchDataList(DesignRemarksBatchListReq req) {

        List<DesignRemarks> designRemarkList = designRemarksRepository.getListByDesignCodeList(req.getDesignCodes());
        if (CollectionUtil.isEmpty(designRemarkList)) {
            return Collections.emptyMap();
        }

        //过滤掉Bom详情明细的备注
        Map<String, List<DesignRemarksVO>> result = designRemarkList.stream().filter(remark ->
                !(StringUtils.equals(remark.getBizType(), DesignRemarksBizTypeEnum.BOM_ORDER.name()) && Objects.nonNull(remark.getBizChildId())))
                .map(remark -> {
                    DesignRemarksVO remarksVO = new DesignRemarksVO();
                    BeanUtils.copyProperties(remark, remarksVO);
                    return remarksVO;
                }).collect(Collectors.groupingBy(DesignRemarksVO::getDesignCode));
        return result;
    }


    @Override
    public Map<Long, List<DesignRemarksVO>> batchBizDataList(DesignRemarksBatchBizListReq req) {
        List<DesignRemarks> designRemarkList = designRemarksRepository.getListByBizIds(req.getBizIds());

        if (CollectionUtil.isEmpty(designRemarkList)) {
            return Collections.emptyMap();
        }
        //过滤掉Bom详情明细的备注
        Map<Long, List<DesignRemarksVO>> result =  designRemarkList.stream().filter(remark ->
                !(StringUtils.equals(remark.getBizType(), DesignRemarksBizTypeEnum.BOM_ORDER.name()) && Objects.nonNull(remark.getBizChildId())))
                .map(remark -> {
                    DesignRemarksVO remarksVO = new DesignRemarksVO();
                    BeanUtils.copyProperties(remark, remarksVO);
                    return remarksVO;
                }).collect(Collectors.groupingBy(DesignRemarksVO::getBizId));
        return result;
    }


}