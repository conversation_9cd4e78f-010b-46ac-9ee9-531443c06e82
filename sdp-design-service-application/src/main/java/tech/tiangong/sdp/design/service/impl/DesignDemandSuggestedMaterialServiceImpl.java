package tech.tiangong.sdp.design.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.entity.DesignDemandSuggestedMaterial;
import tech.tiangong.sdp.design.repository.DesignDemandSuggestedMaterialRepository;
import tech.tiangong.sdp.design.service.DesignDemandSuggestedMaterialService;
import tech.tiangong.sdp.design.vo.req.demand.DesignDemandSuggestedMaterialReq;
import tech.tiangong.sdp.design.vo.resp.demand.DesignDemandSuggestedMaterialVo;

/**
 * 设计需求_推荐物料表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DesignDemandSuggestedMaterialServiceImpl implements DesignDemandSuggestedMaterialService {
    private final DesignDemandSuggestedMaterialRepository designDemandSuggestedMaterialRepository;


    @Override
    public DesignDemandSuggestedMaterialVo getById(Long id) {
        DesignDemandSuggestedMaterial entity = designDemandSuggestedMaterialRepository.getById(id);
        DesignDemandSuggestedMaterialVo vo = new DesignDemandSuggestedMaterialVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(DesignDemandSuggestedMaterialReq req) {
        DesignDemandSuggestedMaterial entity = new DesignDemandSuggestedMaterial();
        BeanUtils.copyProperties(req, entity);
        designDemandSuggestedMaterialRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DesignDemandSuggestedMaterialReq req) {
        DesignDemandSuggestedMaterial entity = new DesignDemandSuggestedMaterial();
        BeanUtils.copyProperties(req, entity);
        designDemandSuggestedMaterialRepository.updateById(entity);
    }

}
