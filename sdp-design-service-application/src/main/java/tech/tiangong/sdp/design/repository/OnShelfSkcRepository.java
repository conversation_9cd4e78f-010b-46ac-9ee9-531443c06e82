package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.OnShelfSkc;
import tech.tiangong.sdp.design.mapper.OnShelfSkcMapper;

import java.util.Collections;
import java.util.List;

/**
 * 上架skc信息表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class OnShelfSkcRepository extends BaseRepository<OnShelfSkcMapper, OnShelfSkc> {


    public List<OnShelfSkc> listByDesignCodes(List<String> designCodeList) {
        if (CollUtil.isEmpty(designCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(OnShelfSkc::getDesignCode, designCodeList)
                .list();
    }

    public List<String> listSyncSpu(List<String> spuList, Boolean forceSync) {
        return baseMapper.listSyncSpu(spuList, forceSync);
    }

    public List<String> listAllSkc() {
        return baseMapper.listAllSkc();
    }
}
