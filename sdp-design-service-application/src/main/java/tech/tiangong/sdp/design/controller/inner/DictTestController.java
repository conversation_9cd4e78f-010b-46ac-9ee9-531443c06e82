package tech.tiangong.sdp.design.controller.inner;

import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.utils.StreamUtil;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 字典测试
 *
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/dict/test")
public class DictTestController {

	private final DictValueRemoteHelper dictValueRemoteHelper;

	/**
	 * 字典查询
	 */
	@GetMapping("/get-by-code/{dictCode}")
	public DataResponse<List<DictVo>> getByDictCodes(@PathVariable(value = "dictCode") String dictCode) {
		List<String> dictCodeList = List.of(dictCode);

		UserContent userContent = UserContentHolder.get();
		System.out.println(userContent);

		List<DictVo> dictVos = dictValueRemoteHelper.listByDictCodes(dictCodeList);

		return DataResponse.ok(dictVos);
	}


	/**
	 * 字典查询
	 */
	@PostMapping("/list-by-code")
	public DataResponse<List<DictVo>> listByDictCodes(@RequestBody List<String> dictCodeList) {
		List<DictVo> dictVos = dictValueRemoteHelper.listByDictCodes(dictCodeList);
		return DataResponse.ok(dictVos);
	}

	@GetMapping("/mode-picture/{modeCode}")
	public DataResponse<String> modePicture(@PathVariable(value = "modeCode") String modeCode) {
		return DataResponse.ok(this.getModePicture(modeCode));
	}

	private String getModePicture(String modeCode) {
		Map<String, DictVo> dictValueMap = dictValueRemoteHelper.mapByDictCodes(List.of(DictConstant.OPERATE_MODEL_PICTURE));
		DictVo modePictureDict = dictValueMap.get(DictConstant.OPERATE_MODEL_PICTURE);
		String modePicture = null;
		if (Objects.nonNull(modePictureDict) && CollectionUtil.isNotEmpty(modePictureDict.getChildren())) {
			if (StringUtils.isNotBlank(modeCode)) {
				Map<String, DictVo> partUseDictValueMap = StreamUtil.list2Map(modePictureDict.getChildren(), DictVo::getDictCode);
				modePicture = Optional.ofNullable(partUseDictValueMap.get(modeCode)).map(DictVo::getDictName).orElse(null);
			}
		}
		return modePicture;
	}


}
