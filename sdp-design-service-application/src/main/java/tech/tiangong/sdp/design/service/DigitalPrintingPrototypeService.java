package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.req.digital.DigitalPrintingPrototypeReq;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingPrototypeVo;

/**
 * 数码印花_SKC表服务接口
 *
 * <AUTHOR>
 */
public interface DigitalPrintingPrototypeService {

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    DigitalPrintingPrototypeVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(DigitalPrintingPrototypeReq req);

}
