package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.SpuIdentify;
import tech.tiangong.sdp.design.mapper.SpuIdentifyMapper;
import tech.tiangong.sdp.utils.Bool;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static java.util.Collections.emptyList;

/**
 * 款式图片识别表(SpuIdentify)服务仓库类
 *
 * <AUTHOR>
 * @since 2025-08-07 17:30:30
 */
@Repository
public class SpuIdentifyRepository extends BaseRepository<SpuIdentifyMapper, SpuIdentify> {

    public List<SpuIdentify> retryQuery(List<Long> identifyIdList, Integer sourceType, Integer identifyState) {
        if (CollUtil.isEmpty(identifyIdList)) {
            return emptyList();
        }
        return lambdaQuery()
                .in(SpuIdentify::getIdentifyId, identifyIdList)
                .eq(Objects.nonNull(sourceType), SpuIdentify::getSourceType, sourceType)
                .eq(Objects.nonNull(identifyState), SpuIdentify::getIdentifyState, identifyState)
                .list();
    }

    public List<SpuIdentify> listByBizCodeSource(Collection<String> bizCodes, Integer sourceType, Integer identifyState) {
        if (CollUtil.isEmpty(bizCodes)) {
            return emptyList();
        }
        return lambdaQuery()
                .in(SpuIdentify::getBizCode, bizCodes)
                .eq(Objects.nonNull(sourceType), SpuIdentify::getSourceType, sourceType)
                .eq(Objects.nonNull(identifyState), SpuIdentify::getIdentifyState, identifyState)
                .eq(SpuIdentify::getLatestState, Bool.YES.getCode())
                .list();
    }

    public List<SpuIdentify> listByBizIdSource(Collection<Long> bizIds, Integer sourceType, Integer identifyState) {
        if (CollUtil.isEmpty(bizIds)) {
            return emptyList();
        }
        return lambdaQuery()
                .in(SpuIdentify::getBizId, bizIds)
                .eq(Objects.nonNull(sourceType), SpuIdentify::getSourceType, sourceType)
                .eq(Objects.nonNull(identifyState), SpuIdentify::getIdentifyState, identifyState)
                .eq(SpuIdentify::getLatestState, Bool.YES.getCode())
                .list();
    }
}
