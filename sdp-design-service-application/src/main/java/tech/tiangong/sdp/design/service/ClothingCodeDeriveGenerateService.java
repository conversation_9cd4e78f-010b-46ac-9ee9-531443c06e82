package tech.tiangong.sdp.design.service;


import tech.tiangong.sdp.design.vo.req.ClothingCodeGenerateReq;

/**
 * 样衣编号衍生编号生成接口
 *
 * <AUTHOR>
 */
public interface ClothingCodeDeriveGenerateService {

    /**
     * spu生成code
     *
     * @param generateReq 生成入参
     * @return 新spu编号
     */
    String spuCode(ClothingCodeGenerateReq generateReq);

    /**
     * skc生成code
     *
     * @param generateReq 生成入参
     * @return 新spu编号
     */
    String skcCode(ClothingCodeGenerateReq generateReq);

    /**
     * FOB旧SpuCode
     * @param generateReq
     * @return
     */
    String spuCodeOld(ClothingCodeGenerateReq generateReq);

    /**
     * FOB旧skcCode
     * @param generateReq
     * @return
     */
    String skcCodeOld(ClothingCodeGenerateReq generateReq);

}