package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.PushZjLogService;
import tech.tiangong.sdp.design.vo.query.PushZjLogQuery;
import tech.tiangong.sdp.design.vo.resp.PushZjLogVo;


/**
 * 数据推送致景记录表管理-web
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/push-zj-log")
public class PushZjLogController extends BaseController {
    private final PushZjLogService pushZjLogService;

    /**
     * 查询列表（分页）
     *
     * @param queryDTO 分页对象
     * @return PageRespVo<PushZjLogVo>
     */
    @GetMapping("/page")
    public DataResponse<PageRespVo<PushZjLogVo>> page(PushZjLogQuery queryDTO) {
        return DataResponse.ok(pushZjLogService.page(queryDTO));
    }

}
