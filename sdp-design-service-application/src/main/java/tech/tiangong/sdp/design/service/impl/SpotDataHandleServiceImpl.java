package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.pop.common.dto.CreateProductDto;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.EstimateCheckPriceVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.converter.spot.SpotSkcConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.ProductUpdateTypeEnum;
import tech.tiangong.sdp.design.enums.SpotSpuPricingTypeEnum;
import tech.tiangong.sdp.design.enums.StylePushPopTypeEnum;
import tech.tiangong.sdp.design.enums.SupplyModeEnum;
import tech.tiangong.sdp.design.enums.spot.SpotResourceStateEnum;
import tech.tiangong.sdp.design.excel.SpotPayeeUpdateData;
import tech.tiangong.sdp.design.excel.SpotSpuSkcPushData;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.remote.SampleClothesRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjApsRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.SpotDataHandleService;
import tech.tiangong.sdp.design.vo.dto.spot.SpotCategoryUpdateDto;
import tech.tiangong.sdp.design.vo.dto.spot.SpotSpuEstimateCheckPriceDto;
import tech.tiangong.sdp.design.vo.req.mq.demand.ProductUpdateMqDto;
import tech.tiangong.sdp.design.vo.req.spot.SpotCategoryUpdateReq;
import tech.tiangong.sdp.design.vo.req.spot.SpotSkcPushPopTypeReq;
import tech.tiangong.sdp.design.vo.req.spot.SpuPushPopTypeReq;
import tech.tiangong.sdp.design.vo.req.zj.ZjSupplierReq;
import tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotPayeeUpdateVo;
import tech.tiangong.sdp.design.vo.resp.spot.StylePushPopTypeVo;
import tech.tiangong.sdp.design.vo.resp.zj.aps.SupplierSimpleResp;
import tech.tiangong.sdp.qy.converter.DictTreeConverter;
import tech.tiangong.sdp.utils.Bool;
import tech.tiangong.sdp.utils.SizeVerifyUtil;
import tech.tiangong.sdp.utils.StreamUtil;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/9 10:04
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class SpotDataHandleServiceImpl implements SpotDataHandleService {

    private final SpotSpuRepository spotSpuRepository;
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final SpotSpuSupplierRepository spotSpuSupplierRepository;
    private final SpotSkcRepository spotSkcRepository;
    private final SpotSkcDetailRepository spotSkcDetailRepository;
    private final SpotSkcConverter spotSkcConverter;
    private final ZjApsRemoteHelper zjApsRemoteHelper;
    private final PopProductHelper popProductHelper;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final MqProducer mqProducer;

    @Override
    public List<SpotPayeeUpdateVo> updatePayee(InputStream inputStream) {
        //读取excel
        List<SpotPayeeUpdateData> updateDataList =  EasyExcel.read(inputStream)
                // .excelType(ExcelTypeEnum.XLSX)
                .head(SpotPayeeUpdateData.class)
                .sheet()
                .doReadSync();
        if (CollUtil.isEmpty(updateDataList)) {
            return Collections.emptyList();
        }
        //查询供应商
        ZjSupplierReq req = new ZjSupplierReq();
        List<SupplierSimpleResp> supplierList = zjApsRemoteHelper.queryApsSupplier(req);
        Map<String, SupplierSimpleResp> supplierMap = StreamUtil.list2Map(supplierList, SupplierSimpleResp::getSupplierName);

        List<SpotPayeeUpdateVo> updateVoList = updateDataList.stream().map(item -> {
            SpotPayeeUpdateVo updateVo = new SpotPayeeUpdateVo();
            BeanUtils.copyProperties(item, updateVo);
            SupplierSimpleResp supplier = supplierMap.get(item.getPayeeName());
            if (Objects.nonNull(supplier)) {
                updateVo.setPayeeCode(supplier.getSupplierCode());
                updateVo.setPayeeId(supplier.getSupplierId());
            }
            return updateVo;
        }).toList();


        //更新spu收款人信息与资源状态
        int handleSize = 500;
        CollectionUtil.split(updateVoList, handleSize).forEach(this::handlePayeeUpdate);


        return updateVoList;
    }


    private void handlePayeeUpdate(List<SpotPayeeUpdateVo> updateList) {
        List<String> styleCodeList = StreamUtil.convertListAndDistinct(updateList, SpotPayeeUpdateVo::getStyleCode);
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(spotSpuList)) {
            return;
        }
        Map<String, SpotSpu> spotSpuMap = StreamUtil.list2Map(spotSpuList, SpotSpu::getStyleCode);

        //更新收款人
        updateList.forEach(item -> {
            SpotSpu spotSpu = spotSpuMap.get(item.getStyleCode());
            if (Objects.isNull(spotSpu)) {
                return;
            }
            spotSpuSupplierRepository.updatePayeeByStyleCode(item);
        });
    }

    @Override
    public List<SpotPayeeUpdateVo> updatePayeeResourceState(InputStream inputStream) {
        //读取excel
        List<SpotPayeeUpdateData> updateDataList =  EasyExcel.read(inputStream)
                // .excelType(ExcelTypeEnum.XLSX)
                .head(SpotPayeeUpdateData.class)
                .sheet()
                .doReadSync();
        if (CollUtil.isEmpty(updateDataList)) {
            return Collections.emptyList();
        }
        //查询供应商
        ZjSupplierReq req = new ZjSupplierReq();
        List<SupplierSimpleResp> supplierList = zjApsRemoteHelper.queryApsSupplier(req);
        Map<String, SupplierSimpleResp> supplierMap = StreamUtil.list2Map(supplierList, SupplierSimpleResp::getSupplierName);

        List<SpotPayeeUpdateVo> updateVoList = updateDataList.stream().map(item -> {
            SpotPayeeUpdateVo updateVo = new SpotPayeeUpdateVo();
            BeanUtils.copyProperties(item, updateVo);
            SupplierSimpleResp supplier = supplierMap.get(item.getPayeeName());
            if (Objects.nonNull(supplier)) {
                updateVo.setPayeeCode(supplier.getSupplierCode());
                updateVo.setPayeeId(supplier.getSupplierId());
            }
            return updateVo;
        }).toList();

        //更新spu收款人信息与资源状态
        int handleSize = 500;
        CollectionUtil.split(updateVoList, handleSize).forEach(this::handlePayeeAndResourceStateUpdate);

        return updateVoList;
    }

    private void handlePayeeAndResourceStateUpdate(List<SpotPayeeUpdateVo> updateList) {
        List<String> styleCodeList = StreamUtil.convertListAndDistinct(updateList, SpotPayeeUpdateVo::getStyleCode);
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(spotSpuList)) {
            return;
        }
        Map<String, SpotSpu> spotSpuMap = StreamUtil.list2Map(spotSpuList, SpotSpu::getStyleCode);

        //spu资料状态更新为已完善
        List<SpotSpu> spotSpuUpdateList = spotSpuList.stream()
                .filter(item -> Objects.equals(item.getResourceStatus(), SpotResourceStateEnum.WAIT_COMPLETE.getCode()))
                .map(item -> {
                    SpotSpu updateSpu = new SpotSpu();
                    updateSpu.setSpotSpuId(item.getSpotSpuId());
                    updateSpu.setResourceStatus(SpotResourceStateEnum.FINISH.getCode());
                    return updateSpu;
                }).toList();
        if (CollUtil.isNotEmpty(spotSpuUpdateList)) {
            spotSpuRepository.updateBatchById(spotSpuUpdateList);
        }

        //skc资料状态更新为已完善
        List<SpotSkc> skcList = spotSkcRepository.listByStyleCodes(styleCodeList);
        List<SpotSkc> spotSkcUpdateList = skcList.stream()
                .filter(item -> Objects.equals(item.getResourceStatus(), SpotResourceStateEnum.WAIT_COMPLETE.getCode()))
                .map(item -> {
                    SpotSkc updateSkc = new SpotSkc();
                    updateSkc.setSpotSkcId(item.getSpotSkcId());
                    updateSkc.setResourceStatus(SpotResourceStateEnum.FINISH.getCode());
                    return updateSkc;
                }).toList();
        if (CollUtil.isNotEmpty(spotSkcUpdateList)) {
            spotSkcRepository.updateBatchById(spotSkcUpdateList);
        }

        //更新收款人
        updateList.forEach(item -> {
            SpotSpu spotSpu = spotSpuMap.get(item.getStyleCode());
            if (Objects.isNull(spotSpu)) {
                return;
            }
            spotSpuSupplierRepository.updatePayeeByStyleCode(item);
        });
    }

    @Override
    public List<String> push2Pop(InputStream inputStream) {
        //读取excel
        List<SpotPayeeUpdateData> updateDataList =  EasyExcel.read(inputStream)
                // .excelType(ExcelTypeEnum.XLSX)
                .head(SpotPayeeUpdateData.class)
                .sheet()
                .doReadSync();
        if (CollUtil.isEmpty(updateDataList)) {
            return Collections.emptyList();
        }

        StopWatch sw = new StopWatch();
        sw.start("现货推送pop刷数");

        int handleSize = 100;
        List<String> pushDesignCodeList = new LinkedList<>();
        int spuNum = 1;
        for (List<SpotPayeeUpdateData> updateList : CollectionUtil.split(updateDataList, handleSize)) {
            List<String> styleCodeList = StreamUtil.convertListAndDistinct(updateList, SpotPayeeUpdateData::getStyleCode);
            //spu,skc信息
            List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(styleCodeList);
            if (CollUtil.isEmpty(spotSpuList)) {
                continue;
            }
            List<SpotSkc> spotSkcList = spotSkcRepository.listByStyleCodes(styleCodeList);
            Map<String, List<SpotSkc>> skcGroupMap = StreamUtil.groupingBy(spotSkcList, SpotSkc::getStyleCode);

            //校验是否推送过pop
            List<String> designCodeList = StreamUtil.convertListAndDistinct(spotSkcList, SpotSkc::getDesignCode);
            Map<String,Boolean> skcIsExistMap = popProductHelper.checkSkcIsExist(designCodeList);

            //spu维度推送skc到pop
            for (SpotSpu spu : spotSpuList) {
                log.info("=== 执行推送pop的现货spu:{}; 序号:{} ===", spu.getStyleCode(), spuNum);
                this.handleSkcPush2Pop(spu, skcGroupMap, pushDesignCodeList, skcIsExistMap);
                spuNum++;
            }
            spotSpuList = null;
            spotSkcList = null;
            skcIsExistMap = null;
        }
        sw.stop();
        log.info(sw.prettyPrint());
        log.info("==== 现货推送pop刷数 完成,:推送skc数量:{}; 耗时:{}s; =========",
                pushDesignCodeList.size(),  sw.getTotalTimeSeconds());

        return pushDesignCodeList;
    }


    @Override
    public List<String> pushPopBySpuSkc(InputStream inputStream, Boolean pushData) {
        //读取excel
        List<SpotSpuSkcPushData> updateDataList =  EasyExcel.read(inputStream)
                .head(SpotSpuSkcPushData.class)
                .sheet()
                .doReadSync();
        if (CollUtil.isEmpty(updateDataList)) {
            return Collections.emptyList();
        }
        log.info("=== 现货推送pop 数据量:{} ===", updateDataList.size());
        StopWatch sw = new StopWatch();
        sw.start("现货推送pop刷数");

        int handleSize = 200;
        List<String> pushDesignCodeList = new LinkedList<>();
        int countNum = 1;
        for (List<SpotSpuSkcPushData> updateList : CollectionUtil.split(updateDataList, handleSize)) {
            log.info("=== 现货推送pop countNum:{} ===", countNum);
            countNum++;
            List<String> styleCodeList = StreamUtil.convertListAndDistinct(updateList, SpotSpuSkcPushData::getStyleCode);
            //spu
            List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(styleCodeList);
            if (CollUtil.isEmpty(spotSpuList)) {
                log.info("=== spu不存在 styleCodeList:{} ===", JSON.toJSONString(styleCodeList));
                continue;
            }
            //核价信息
            Map<Long, EstimateCheckPriceVo> priceMap = this.getEstimateCheckPriceVoMap(spotSpuList);

            //符合推送条件的spu
            List<SpotSpu> pushSpuList = this.getPushSpuList(spotSpuList, priceMap);
            if (CollUtil.isEmpty(pushSpuList)) {
                log.info("=== 无符合推送条件的spu styleCodeList:{} ===", JSON.toJSONString(styleCodeList));
                continue;
            }
            Map<String, SpotSpu> pushSpuMap = StreamUtil.list2Map(pushSpuList, SpotSpu::getStyleCode);
            List<Long> spotSpuIdList = StreamUtil.convertListAndDistinct(pushSpuList, SpotSpu::getSpotSpuId);
            List<SpotSpuDetail> spotSpuDetailList = spotSpuDetailRepository.listBySpotSpuIds(spotSpuIdList);
            Map<Long, SpotSpuDetail> spuDetailMap = StreamUtil.list2Map(spotSpuDetailList, SpotSpuDetail::getSpotSpuId);

            List<String> pushStyleCodeList = StreamUtil.convertListAndDistinct(pushSpuList, SpotSpu::getStyleCode);

            //符合条件的spu下的skc
            List<String> queryDesignCodeList = new ArrayList<>();
            updateList.forEach(item -> {
                if (Objects.nonNull(pushSpuMap.get(item.getStyleCode()))) {
                    queryDesignCodeList.add(item.getDesignCode());
                }
            });
            //skc
            List<SpotSkc> spotSkcList = spotSkcRepository.listByDesignCodes(queryDesignCodeList);
            if (CollUtil.isEmpty(spotSkcList)) {
                log.info("=== skc不存在 designCodeList:{} ===", JSON.toJSONString(queryDesignCodeList));
                continue;
            }
            Map<String, List<SpotSkc>> skcGroupMap = StreamUtil.groupingBy(spotSkcList, SpotSkc::getStyleCode);

            List<Long> spotSkcIdList = StreamUtil.convertListAndDistinct(spotSkcList, SpotSkc::getSpotSkcId);
            List<SpotSkcDetail> spotSkcDetailList = spotSkcDetailRepository.listBySkcIds(spotSkcIdList);
            Map<Long, SpotSkcDetail> skcDetailMap = StreamUtil.list2Map(spotSkcDetailList, SpotSkcDetail::getSpotSkcId);

            //spu供应商
            List<SpotSpuSupplier> supplierList = spotSpuSupplierRepository.listByStyleCodes(pushStyleCodeList);
            Map<String, List<SpotSpuSupplier>> supplierGroupMap = StreamUtil.groupingBy(supplierList, SpotSpuSupplier::getStyleCode);

            //查询skc在pop是否已存在(过滤无颜色, 无尺码的skc)
            Map<String, Boolean> skcIsExistMap = this.getSkcIsExistMap(spotSkcList, skcDetailMap);

            //spu维度推送skc到pop
            for (SpotSpu spu : pushSpuList) {
                this.buildPushPopDto(spu, skcGroupMap, skcIsExistMap, pushDesignCodeList, pushData, priceMap, supplierGroupMap, spuDetailMap, skcDetailMap);
            }
            spotSpuList = null;
            spotSpuDetailList = null;
            pushSpuList = null;
            spotSkcList = null;
            skcGroupMap = null;
            skcIsExistMap = null;
            priceMap = null;
            supplierList = null;
            supplierGroupMap = null;
            spuDetailMap = null;
            spotSkcDetailList = null;
            skcDetailMap = null;
        }

        sw.stop();
        log.info(sw.prettyPrint());
        log.info("==== 现货推送pop刷数 完成,:推送skc数量:{}; 耗时:{}s; =========",
                pushDesignCodeList.size(),  sw.getTotalTimeSeconds());
        log.info("=== 现货推送pop刷数, 推送skc:{} ===", JSON.toJSONString(pushDesignCodeList));

        return pushDesignCodeList;
    }

    private Map<String, Boolean> getSkcIsExistMap(List<SpotSkc> spotSkcList, Map<Long, SpotSkcDetail> skcDetailMap) {
        List<String> existDesignCodeList = new ArrayList<>();
        for (SpotSkc spotSkc : spotSkcList) {
            if (StrUtil.isBlank(spotSkc.getColor())) {
                continue;
            }
            SpotSkcDetail skcDetail = skcDetailMap.get(spotSkc.getSpotSkcId());
            if (Objects.isNull(skcDetail) || StrUtil.isBlank(skcDetail.getSampleSize())) {
                continue;
            }
            existDesignCodeList.add(spotSkc.getDesignCode());
        }
        // List<String> existDesignCodeList = StreamUtil.convertListAndDistinct(spotSkcList, SpotSkc::getDesignCode);
        return popProductHelper.checkSkcIsExist(existDesignCodeList);
    }

    private Map<Long, EstimateCheckPriceVo> getEstimateCheckPriceVoMap(List<SpotSpu> spotSpuList) {
        Map<Long, EstimateCheckPriceVo> priceMap = new HashMap<>();
        if (CollUtil.isEmpty(spotSpuList)) {
            return priceMap;
        }
        List<Long> checkPriceIdList = StreamUtil.convertListAndDistinct(
                StreamUtil.filter(spotSpuList, item -> Objects.nonNull(item.getPredictCheckPriceId())),
                SpotSpu::getPredictCheckPriceId
        );
        if (CollUtil.isNotEmpty(checkPriceIdList)) {
            List<EstimateCheckPriceVo> priceList = sampleClothesRemoteHelper.listEstimateCheckPriceByIds(checkPriceIdList);
            priceMap = StreamUtil.list2Map(priceList, EstimateCheckPriceVo::getEstimateCheckPriceId);
        }
        return priceMap;
    }

    private List<SpotSpu> getPushSpuList(List<SpotSpu> spotSpuList, Map<Long, EstimateCheckPriceVo> priceMap) {
        if (CollUtil.isEmpty(spotSpuList)) {
            return Collections.emptyList();
        }
        List<SpotSpu> pushSpuList = new ArrayList<>();
        for (SpotSpu spu : spotSpuList) {
            //tryOn款核价类型不能为空
            if (Objects.equals(spu.getSupplyModeCode(), SupplyModeEnum.TRY_ON.getCode())) {
                if (Objects.isNull(spu.getPredictCheckPriceId()) || Objects.isNull(priceMap.get(spu.getPredictCheckPriceId()))) {
                    log.info("=== tryOn spu 核价信息为空, spu:{} ===", spu.getStyleCode());
                    continue;
                }
                EstimateCheckPriceVo priceVo = priceMap.get(spu.getPredictCheckPriceId());
                if (Objects.isNull(priceVo.getPriceType())) {
                    log.info("=== tryOn spu 定价类型为空, spu:{} ===", spu.getStyleCode());
                    continue;
                }
            }
            pushSpuList.add(spu);
        }
        return pushSpuList;
    }

    private void buildPushPopDto(SpotSpu spotSpu, Map<String, List<SpotSkc>> skcGroupMap, Map<String, Boolean> skcIsExistMap,
                                 List<String> pushDesignCodeList, Boolean pushData,
                                 Map<Long, EstimateCheckPriceVo> priceMap,
                                 Map<String, List<SpotSpuSupplier>> supplierGroupMap,
                                 Map<Long, SpotSpuDetail> spuDetailMap, Map<Long, SpotSkcDetail> skcDetailMap) {
        List<SpotSkc> skcList = skcGroupMap.get(spotSpu.getStyleCode());
        if (CollUtil.isEmpty(skcList)) {
            log.info("=== spu无skc, spu:{} ===", spotSpu.getStyleCode());
            return;
        }
        //推送未取消的skc
        List<SpotSkc> filterSkcList = skcList.stream()
                .filter(v -> Objects.equals(v.getIsCanceled(), Bool.NO.getCode()))
                .toList();
        if (CollUtil.isEmpty(filterSkcList)) {
            log.info("=== spu无符合推送条件的skc, spu:{} ===", spotSpu.getStyleCode());
            return;
        }
        List<SpotSkc> pushSkc = new LinkedList<>();
        for (SpotSkc skc : filterSkcList) {
            //过滤无颜色, 无尺码的skc
            if (StrUtil.isBlank(skc.getColor())) {
                log.info("=== skc无颜色 不推送pop, skc:{} ===", skc.getDesignCode());
                continue;
            }
            SpotSkcDetail skcDetail = skcDetailMap.get(skc.getSpotSkcId());
            if (Objects.isNull(skcDetail) || StrUtil.isBlank(skcDetail.getSampleSize())) {
                log.info("=== skc无尺码 不推送pop, skc:{} ===", skc.getDesignCode());
                continue;
            }
            //skc在pop不存在时才推送
            if (!skcIsExistMap.get(skc.getDesignCode())) {
                log.info("=== 需要推送pop的现货spu:{}; skc:{} ===", spotSpu.getStyleCode(), skc.getDesignCode());
                pushSkc.add(skc);
                pushDesignCodeList.add(skc.getDesignCode());
            }
        }
        if (CollUtil.isEmpty(pushSkc)) {
            log.info("=== spu无需要推送的skc, spu:{} ===", spotSpu.getStyleCode());
            return;
        }
        List<SpotSkcDetail> skcDetailList = pushSkc.stream()
                .filter(item -> Objects.nonNull(skcDetailMap.get(item.getSpotSkcId())))
                .map(item -> skcDetailMap.get(item.getSpotSkcId())).toList();

        SpotSpuEstimateCheckPriceDto checkPriceDto = new SpotSpuEstimateCheckPriceDto();
        EstimateCheckPriceVo priceVo = priceMap.get(spotSpu.getPredictCheckPriceId());
        if (Objects.nonNull(priceVo)) {
            BeanUtils.copyProperties(priceVo, checkPriceDto);
        }

        BigDecimal purchasePrice = null;
        List<SpotSpuSupplier> suppliers = supplierGroupMap.get(spotSpu.getStyleCode());
        if (CollUtil.isNotEmpty(suppliers)) {
            SpotSpuSupplier maxPriceSupplier = suppliers.stream().max(Comparator.comparing(SpotSpuSupplier::getPurchasePrice)).orElse(new SpotSpuSupplier());
            purchasePrice = maxPriceSupplier.getPurchasePrice();
        }

        SpotSpuDetail spotSpuDetail = spuDetailMap.get(spotSpu.getSpotSpuId());
        //推送pop入参
        if (pushData) {
            //查询当前品类字典获取尺码明细表
            DictVo dictVoByCategory = dictValueRemoteHelper.getDictVoByCategory(spotSpu.getCategory());
            CreateProductDto createProductDto = spotSkcConverter.buildCreateProductDto(spotSpu, spotSpuDetail, checkPriceDto, pushSkc, purchasePrice, skcDetailList,dictVoByCategory);
            popProductHelper.noticeCreateSpotProduct(createProductDto);
        }
        filterSkcList = null;
        pushSkc = null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SpotCategoryUpdateDto> updateCategoryBySpu(List<SpotCategoryUpdateReq> reqList) {
        if (CollUtil.isEmpty(reqList)) {
            return Collections.emptyList();
        }
        SdpDesignException.isTrue(reqList.size() <= 200, "更新spu数量不能大于200个");
        StopWatch sw = new StopWatch();
        sw.start("现货品类更新刷数");

        //品类校验
        //品类字典查询
        Map<String, DictVo> dictValueMap = dictValueRemoteHelper.mapByDictCodes(List.of(DictConstant.CLOTHING_CATEGORY));
        DictVo categoryDictVo = dictValueMap.get(DictConstant.CLOTHING_CATEGORY);
        Map<String, String> categoryMap = DictTreeConverter.convertToMap(categoryDictVo, "-");
        SdpDesignException.isTrue(CollUtil.isNotEmpty(categoryMap), "品类信息不存在");

        //品类校验
        List<String> errorStrList = new ArrayList<>();
        reqList.forEach(item -> {
            String categoryCode = categoryMap.get(item.getCategoryName());
            if (StrUtil.isBlank(categoryCode)) {
                errorStrList.add("品类不存在:" + item.getCategoryName() + ", spu:" + item.getStyleCode());
            }
            item.setCategory(categoryCode);
        });
        SdpDesignException.isEmpty(errorStrList, "数据异常:{}", JSON.toJSONString(errorStrList));

        //spu信息校验与更新数据封装
        List<SpotCategoryUpdateDto> finalUpdateDataList = this.checkAndGetUpdateCategoryDto(reqList);
        if (CollUtil.isEmpty(finalUpdateDataList)) {
            return Collections.emptyList();
        }

        //更新品类并推送pop
        for (SpotCategoryUpdateDto item : finalUpdateDataList) {
            SpotSpu updateSpu = new SpotSpu();
            updateSpu.setSpotSpuId(item.getSpotSpuId());
            updateSpu.setCategory(item.getCategory());
            updateSpu.setCategoryName(item.getCategoryName());
            log.info("现货品类更新: spu:{}", JSON.toJSONString(item));
            //更新品类
            spotSpuRepository.updateById(updateSpu);

            //推送品类更新到pop封装
            ProductUpdateMqDto.Data categoryUpdateDto = ProductUpdateMqDto.Data.builder()
                    .spuCode(item.getStyleCode())
                    .opType(ProductUpdateTypeEnum.UPDATE_CATEGORY.getCode())
                    .categoryCode(item.getCategory())
                    .categoryName(item.getCategoryName())
                    .build();
            this.sendUpdateProductMessageSingle(categoryUpdateDto);
        }

        sw.stop();
        log.info(sw.prettyPrint());
        log.info("==== 现货品类更新刷数 完成 耗时:{}s; 更新spu数量:{}; =========", sw.getTotalTimeSeconds(), finalUpdateDataList.size());
        log.info("==== 更新spu:{}; =========", JSON.toJSONString(finalUpdateDataList));

        return finalUpdateDataList;
    }

    private List<SpotCategoryUpdateDto> checkAndGetUpdateCategoryDto(List<SpotCategoryUpdateReq> updateDataList) {
        List<SpotCategoryUpdateDto> finalUpdateDataList = new ArrayList<>();
        //spu信息
        List<String> styleCodeList = StreamUtil.convertListAndDistinct(updateDataList, SpotCategoryUpdateReq::getStyleCode);
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(spotSpuList)) {
            log.info("=== spu信息不存在, 跳过本次处理 spu:{} ===", JSON.toJSONString(styleCodeList));
            return Collections.emptyList();
        }
        Map<String, SpotSpu> spotSpuMap = StreamUtil.list2Map(spotSpuList, SpotSpu::getStyleCode);

        //按spu维度更新品类
        Map<String, SpotCategoryUpdateReq> updateDataMap = StreamUtil.list2Map(updateDataList, SpotCategoryUpdateReq::getStyleCode);
        for (Map.Entry<String, SpotCategoryUpdateReq> updateDataEntry : updateDataMap.entrySet()) {
            String styleCode = updateDataEntry.getKey();
            SpotCategoryUpdateReq updateData = updateDataEntry.getValue();
            SpotSpu spotSpu = spotSpuMap.get(styleCode);
            SdpDesignException.notNull(spotSpu, "spu信息不存在! {}", styleCode);
            // if (Objects.equals(spotSpu.getCategoryName(), updateData.getCategoryName())) {
            //     log.info("品类一样,不更新: spu:{}; 品类:{}", styleCode, spotSpu.getCategoryName());
            //     continue;
            // }
            //过滤掉已取消的spu
            if (Objects.equals(spotSpu.getIsCanceled(), Bool.YES.getCode())) {
                log.info("spu已取消,不更新: spu:{};", styleCode);
                continue;
            }
            SpotCategoryUpdateDto updateDto = SpotCategoryUpdateDto.builder()
                    .spotSpuId(spotSpu.getSpotSpuId())
                    .styleCode(styleCode)
                    .category(updateData.getCategory())
                    .categoryName(updateData.getCategoryName())
                    .build();
            finalUpdateDataList.add(updateDto);
        }
        spotSpuList = null;
        spotSpuMap = null;
        updateDataMap = null;
        return finalUpdateDataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushCategoryPop(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return;
        }
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(spotSpuList)) {
            log.info("=== 现货品类推送pop spu不存在 ===");
            return;
        }
        //推送品类更新到pop
        spotSpuList.forEach(spotSpu  -> {
            ProductUpdateMqDto.Data categoryUpdateDto = ProductUpdateMqDto.Data.builder()
                    .spuCode(spotSpu.getStyleCode())
                    .opType(ProductUpdateTypeEnum.UPDATE_CATEGORY.getCode())
                    .categoryCode(spotSpu.getCategory())
                    .categoryName(spotSpu.getCategoryName())
                    .build();

            this.sendUpdateProductMessageSingle(categoryUpdateDto);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StylePushPopTypeVo> pushPopSpuWithType(SpuPushPopTypeReq req) {
        List<String> styleCodeList = req.getStyleCodeList();
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(spotSpuList)) {
            log.info("=== 现货品spu不存在 ===");
            return List.of();
        }
        Map<Long, EstimateCheckPriceVo> checkPriceMap = new HashMap<>();
        if (Objects.equals(req.getPushPopType(), StylePushPopTypeEnum.UPDATE_PRICE)
                || Objects.equals(req.getPushPopType(), StylePushPopTypeEnum.UPDATE_PRICING_TYPE)) {
            checkPriceMap = this.getEstimateCheckPriceVoMap(spotSpuList);
        }

        StylePushPopTypeEnum pushPopType = req.getPushPopType();
        List<StylePushPopTypeVo> pushPopTypeVoList = new ArrayList<>(spotSpuList.size());
        for (SpotSpu spotSpu : spotSpuList) {
            ProductUpdateMqDto.Data spotUpdateDto = ProductUpdateMqDto.Data.builder()
                    .spuCode(spotSpu.getStyleCode())
                    .build();
            switch (pushPopType) {
                case UPDATE_CATEGORY:
                    //更新品类
                    spotUpdateDto.setOpType(ProductUpdateTypeEnum.UPDATE_CATEGORY.getCode());
                    spotUpdateDto.setCategoryName(spotSpu.getCategoryName());
                    spotUpdateDto.setCategoryCode(spotSpu.getCategory());
                    break;
                case UPDATE_PURCHASES_PRICE:
                    //更新采购价
                    BigDecimal purchasePrice = spotSpuSupplierRepository.getMaxPurchasePrice(spotSpu.getStyleCode());
                    if(purchasePrice!=null){
                        //因为现货的采购价是在编辑SPU的供应商时录入的，所以这里用SPU的更新人作为核价师传给POP
                        popProductHelper.updateProductPurchasePriceBySpotSpu(spotSpu,purchasePrice,spotSpu.getReviserId(),spotSpu.getReviserName());
                        spotUpdateDto.setOpType(ProductUpdateTypeEnum.UPDATE_PURCHASES_PRICE.getCode());
                        spotUpdateDto.setPurchasePrice(purchasePrice);
                        spotUpdateDto.setPricerId(spotSpu.getReviserId());
                        spotUpdateDto.setPricerName(spotSpu.getReviserName());
                    }
                    break;
                case UPDATE_CLOTHING_STYLE:
                    //更新款式风格
                    spotUpdateDto.setOpType(ProductUpdateTypeEnum.UPDATE_CLOTHING_STYLE.getCode());
                    spotUpdateDto.setClothingStyleCode(spotSpu.getClothingStyleCode());
                    spotUpdateDto.setClothingStyleName(spotSpu.getClothingStyleName());
                    break;
                case UPDATE_PRICING_TYPE:
                    //更新定价类型
                    if (Objects.nonNull(spotSpu.getPredictCheckPriceId())
                            && Objects.nonNull(checkPriceMap.get(spotSpu.getPredictCheckPriceId()))) {
                        EstimateCheckPriceVo checkPriceVo = checkPriceMap.get(spotSpu.getPredictCheckPriceId());
                        SpotSpuPricingTypeEnum spotSpuPricingType = SpotSpuPricingTypeEnum.findByCode(checkPriceVo.getPriceType());
                        if (spotSpuPricingType != null) {
                            //更新定价类型
                            spotUpdateDto.setOpType(ProductUpdateTypeEnum.UPDATE_PRICING_TYPE.getCode());
                            spotUpdateDto.setPricingType(spotSpuPricingType.getCode());
                        }
                    }
                    break;
                case UPDATE_PRICE:
                    //更新预估核价
                    if (Objects.nonNull(spotSpu.getPredictCheckPriceId())
                            && Objects.nonNull(checkPriceMap.get(spotSpu.getPredictCheckPriceId()))) {
                        EstimateCheckPriceVo checkPriceVo = checkPriceMap.get(spotSpu.getPredictCheckPriceId());
                        spotUpdateDto.setOpType(ProductUpdateTypeEnum.UPDATE_PRICE.getCode());
                        spotUpdateDto.setCheckPriceType(2);
                        spotUpdateDto.setPrice(checkPriceVo.getTotalCost());
                        spotUpdateDto.setPricerId(checkPriceVo.getPricerId());
                        spotUpdateDto.setPricerName(checkPriceVo.getPricerName());
                    }
                    break;
                default:
                    throw new SdpDesignException("未知操作类型");
            }
            if (Objects.isNull(spotUpdateDto.getOpType())) {
                continue;
            }
            log.info("=== 现货更新推送pop spotUpdateDto:{}; pushPopType:{} ===", JSON.toJSONString(spotUpdateDto), pushPopType.getDesc());
            this.sendUpdateProductMessageSingle(spotUpdateDto);

            StylePushPopTypeVo pushPopTypeVo = StylePushPopTypeVo.builder()
                    .styleCode(spotSpu.getStyleCode())
                    .pushPopType(pushPopType)
                    .pushContent(JSON.toJSONString(spotUpdateDto))
                    .build();
            pushPopTypeVoList.add(pushPopTypeVo);
        }

        return pushPopTypeVoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StylePushPopTypeVo> pushPopSkcWithType(SpotSkcPushPopTypeReq req) {
        List<String> designCodeList = req.getDesignCodeList();
        List<SpotSkc> spotSkcList = spotSkcRepository.listByDesignCodes(designCodeList);
        if (CollUtil.isEmpty(spotSkcList)) {
            log.info("=== 现货品skc不存在 ===");
            return List.of();
        }
        List<SpotSkcDetail> skcDetailList = spotSkcDetailRepository.listBySkcIds(StreamUtil.convertList(spotSkcList, SpotSkc::getSpotSkcId));
        Map<Long, SpotSkcDetail> skcdetailMap = StreamUtil.list2Map(skcDetailList, SpotSkcDetail::getSpotSkcId);

        StylePushPopTypeEnum pushPopType = req.getPushPopType();
        List<StylePushPopTypeVo> pushPopTypeVoList = new ArrayList<>(spotSkcList.size());
        for (SpotSkc spotSkc : spotSkcList) {
            ProductUpdateMqDto.Data spotUpdateDto = ProductUpdateMqDto.Data.builder()
                    .spuCode(spotSkc.getStyleCode())
                    .skc(spotSkc.getDesignCode())
                    .build();
            SpotSkcDetail skcDetail = skcdetailMap.get(spotSkc.getSpotSkcId());
            if (Objects.isNull(skcDetail)) {
                continue;
            }
            switch (pushPopType) {
                case UPDATE_COLOR:
                    //更新颜色
                    List<ColorInfoVo> colorInfoList = skcDetail.getColorInfoList();
                    List<String> colorList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColor);
                    List<String> abbrCodeList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColorAbbrCode);
                    List<String> englishNameList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColorEnglishName);
                    String color = StrUtil.join(StrUtil.SPACE, colorList);
                    String colorCode = StrUtil.join(StrUtil.SPACE, englishNameList);
                    String abbrCode = StrUtil.join(StrUtil.SPACE, abbrCodeList);

                    spotUpdateDto.setOpType(ProductUpdateTypeEnum.UPDATE_COLOR.getCode());
                    spotUpdateDto.setColor(color);
                    spotUpdateDto.setColorCode(colorCode);
                    spotUpdateDto.setColorAbbrCode(abbrCode);
                    break;
                case UPDATE_SIZE:
                    //更新尺码
                    spotUpdateDto.setOpType(ProductUpdateTypeEnum.UPDATE_SIZE.getCode());
                    spotUpdateDto.setSizeNameList(StrUtil.splitTrim(skcDetail.getSampleSize(), StrUtil.COMMA));
                    break;
                default:
                    throw new SdpDesignException("未知操作类型");
            }
            log.info("=== 现货更新推送pop spotUpdateDto:{}; pushPopType:{} ===", JSON.toJSONString(spotUpdateDto), pushPopType.getDesc());
            this.sendUpdateProductMessageSingle(spotUpdateDto);

            StylePushPopTypeVo pushPopTypeVo = StylePushPopTypeVo.builder()
                    .styleCode(spotSkc.getStyleCode())
                    .designCode(spotSkc.getDesignCode())
                    .pushPopType(pushPopType)
                    .pushContent(JSON.toJSONString(spotUpdateDto))
                    .build();
            pushPopTypeVoList.add(pushPopTypeVo);
        }

        return pushPopTypeVoList;
    }

    private void sendUpdateProductMessageSingle(ProductUpdateMqDto.Data mqData){
        if (Objects.isNull(mqData)) {
            return;
        }
        ProductUpdateMqDto mqDTO = ProductUpdateMqDto.builder()
                .dataList(Collections.singletonList(mqData))
                .build();
        //exchange跟取消SKC一样
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.PROTOTYPE_MANAGE_CANCEL_SKC,
                DesignMqConstant.PROTOTYPE_MANAGE_CANCEL_SKC_EXCHANGE,
                DesignMqConstant.PROTOTYPE_MANAGE_CANCEL_SKC_ROUTING_KEY,
                JSONUtil.toJsonStr(mqDTO));
        log.info("=== 现货更新推送pop mqDTO: {} ===", JSON.toJSONString(mqMessageReq));
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

    private void handleSkcPush2Pop(SpotSpu spu, Map<String,
                                           List<SpotSkc>> skcGroupMap,
                                   List<String> pushDesignCodeList,
                                   Map<String, Boolean> skcIsExistMap) {
        List<SpotSkc> skcList = skcGroupMap.get(spu.getStyleCode());
        if (CollUtil.isEmpty(skcList)) {
            log.info("=== spu无skc, spu:{} ===", spu.getStyleCode());
            return;
        }
        //推送未取消, 且资源状态为已完成的skc
        List<SpotSkc> filterSkcList = skcList.stream()
                .filter(v -> Objects.equals(v.getIsCanceled(), Bool.NO.getCode()) && SpotResourceStateEnum.FINISH.getCode().equals(v.getResourceStatus()))
                .toList();
        if (CollUtil.isEmpty(filterSkcList)) {
            log.info("=== spu无符合推送条件的skc, spu:{} ===", spu.getStyleCode());
            return;
        }
        List<SpotSkc> pushSkc = new LinkedList<>();
        for (SpotSkc skc : filterSkcList) {
            //skc在pop不存在时才推送
            if (!skcIsExistMap.get(skc.getDesignCode())) {
                pushSkc.add(skc);
                pushDesignCodeList.add(skc.getDesignCode());
            }
        }
        if (CollUtil.isEmpty(pushSkc)) {
            log.info("=== spu无需要推送的skc, spu:{} ===", spu.getStyleCode());
            return;
        }
        //推送skc到pop
        List<SpotSkcDetail> spotSkcDetails = spotSkcDetailRepository.listBySkcIds(pushSkc.stream().map(SpotSkc::getSpotSkcId).collect(Collectors.toList()));
        if (CollUtil.isEmpty(spotSkcDetails) || spotSkcDetails.stream().anyMatch(e -> StrUtil.isBlank(e.getSampleSize()))) {
            log.info("=== spu下的存在无尺码的skc, spu:{} ===", spu.getStyleCode());
            return;
        }
        // 所有SKC的尺码范围需一致
        List<String> sizeList = spotSkcDetails.stream().map(SpotSkcDetail::getSampleSize).toList();
        Set<String> sizeIntersectionSet = SizeVerifyUtil.findSizeIntersection(sizeList);
        if (CollUtil.isEmpty(sizeIntersectionSet)) {
            log.info("=== spu下的skc尺码无交集, spu:{} ===", spu.getStyleCode());
            return;
        }
        String sizeIntersection = String.join(",", sizeIntersectionSet);
        // 符合尺码范围
        DictVo dictVoByCategory = dictValueRemoteHelper.getDictVoByCategory(spu.getCategory());
        if (Objects.nonNull(dictVoByCategory) && CollUtil.isNotEmpty(dictVoByCategory.getAttributes())) {    //不为空则才判断尺码范围
            Optional<team.aikero.admin.common.vo.AttributeVo> codeAttributeOpt = dictVoByCategory.getAttributes().stream()
                    .filter(attr -> attr != null && "popSize".equals(attr.getCode()))
                    .findFirst();


            if (codeAttributeOpt.isPresent() && StringUtils.isNotBlank(codeAttributeOpt.get().getName())) {
                List<String> popSizeList = Arrays.stream(codeAttributeOpt.get().getName().split(",")).collect(Collectors.toList());
                popSizeList.retainAll(sizeIntersectionSet);
                if (CollUtil.isEmpty(popSizeList)) {
                    log.info("=== spu下的skc尺码不在字典配置的尺码范围内, spu:{}, 尺码:{} ===", spu.getStyleCode(), sizeIntersectionSet);
                    return;
                }
                sizeIntersection = String.join(",", popSizeList);
            }
        }
        for (SpotSkcDetail spotSkcDetail : spotSkcDetails) {
            // 设置成统一的尺码给到POP
            spotSkcDetail.setSampleSize(sizeIntersection);
        }
        CreateProductDto req = spotSkcConverter.convertProductCreateReq(spu,pushSkc, spotSkcDetails);
        if (Objects.nonNull(req)) {
            popProductHelper.noticeCreateSpotProduct(req);
        }

        filterSkcList = null;
        pushSkc = null;
        req = null;
    }
}
