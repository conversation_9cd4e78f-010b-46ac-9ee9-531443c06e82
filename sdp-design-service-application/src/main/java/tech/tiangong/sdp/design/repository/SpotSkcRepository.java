package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.SpotSkc;
import tech.tiangong.sdp.design.enums.spot.SpotPurchaseOrderStateEnum;
import tech.tiangong.sdp.design.mapper.SpotSkcMapper;
import tech.tiangong.sdp.design.vo.query.spot.SpotSkcQuery;
import tech.tiangong.sdp.design.vo.req.spot.ListSkcForOfpReq;
import tech.tiangong.sdp.design.vo.req.spot.QuerySkcForOfpReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcForOfpVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcVo;
import tech.tiangong.sdp.qy.vo.dto.QySkcColorDto;
import tech.tiangong.sdp.qy.vo.dto.SkcPicDto;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * spot_skc表(SpotSkc)服务仓库类
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:33
 */
@Repository
public class SpotSkcRepository extends BaseRepository<SpotSkcMapper, SpotSkc> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<SpotSkcVo> findPage(SpotSkcQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public IPage<SpotSkcForOfpVo> querySkcForOfp(QuerySkcForOfpReq query){
        return baseMapper.querySkcForOfp(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public List<SpotSkcForOfpVo> listSkcForOfp(ListSkcForOfpReq query){
        return baseMapper.listSkcForOfp(query);
    }

    public SpotSkc getByDesignCode(String designCode) {
        if(StringUtils.isBlank(designCode)){
            return null;
        }
        return getOne(new LambdaQueryWrapper<SpotSkc>()
                .eq(SpotSkc::getDesignCode,designCode),false);
    }

    public List<SpotSkc> listByStyleCode(String styleCode) {
        if(StringUtils.isBlank(styleCode)){
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SpotSkc>()
                .eq(SpotSkc::getStyleCode,styleCode));
    }

    public List<SpotSkc> listByDesignCodes(List<String> designCodeList) {
        if (CollUtil.isEmpty(designCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(SpotSkc::getDesignCode, designCodeList).list();
    }

    public List<SpotSkc> listByStyleCodes(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(SpotSkc::getStyleCode, styleCodeList)
                .orderByAsc(SpotSkc::getCreatedTime)
                .list();
    }

    public List<SpotSkc> listByStyleCodes(List<String> styleCodeList, Integer prototypeStatus) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(SpotSkc::getStyleCode, styleCodeList)
                .eq(Objects.nonNull(prototypeStatus), SpotSkc::getPrototypeStatus, prototypeStatus)
                .list();
    }

    public List<SpotSkc> listNotCancelByStyleCodes(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(SpotSkc::getStyleCode, styleCodeList)
                .eq(SpotSkc::getIsCanceled,  Bool.NO.getCode())
                .orderByAsc(SpotSkc::getCreatedTime)
                .list();
    }

    public void updatePurchaseOrderStatus(Long spotSkcId,
                                          SpotPurchaseOrderStateEnum spotPurchaseOrderState,
                                          String purchaseOrderCode){
        if(spotSkcId==null){
            return;
        }
        baseMapper.update(new LambdaUpdateWrapper<SpotSkc>()
                .set(SpotSkc::getPurchaseOrderStatus,spotPurchaseOrderState!=null ? spotPurchaseOrderState.getCode() : null)
                .set(SpotSkc::getPurchaseOrderCode,purchaseOrderCode)
                .eq(SpotSkc::getSpotSkcId,spotSkcId));
    }

    public List<SpotSkc> listByStyleBizIds(List<Long> pickBizIdList) {
        if (CollUtil.isEmpty(pickBizIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(SpotSkc::getPickBizId, pickBizIdList).list();
    }

    public SpotSkc ByStyleBizId(Long pickBizId) {
        if (Objects.isNull(pickBizId)) {
            return null;
        }
        return lambdaQuery()
                .eq(SpotSkc::getPickBizId, pickBizId)
                .last("limit 1").one();
    }

    public String selectLatestDesignCode() {
        SpotSkc spotSkc = lambdaQuery()
                .select(SpotSkc::getDesignCode)
                .orderByDesc(SpotSkc::getCreatedTime)
                .last("limit 1").one();
        return Optional.ofNullable(spotSkc).map(SpotSkc::getDesignCode).orElse("");
    }

    public List<QySkcColorDto> queryColorBySpuList(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return baseMapper.queryColorBySpuList(styleCodeList);
    }

    public List<SkcPicDto> findSkcPicList(List<String> styleCodeList){
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return baseMapper.findSkcPicList(styleCodeList);
    }
}
