package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.entity.DesignDemand;
import tech.tiangong.sdp.design.vo.req.identify.SpuIdentifyAddReq;
import tech.tiangong.sdp.design.vo.req.identify.SpuIdentifyRetryReq;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleCreateResp;

import java.util.List;

/**
 * 款式图片识别表(SpuIdentify)服务接口
 *
 * <AUTHOR>
 * @since 2025-08-07 17:30:30
 */
public interface SpuIdentifyService {


    /**
     * 批量新增-异步识别
     */
    void batchAddAsync(List<SpuIdentifyAddReq> addReqList);

    /**
     * 识别重试
     */
    void retry(SpuIdentifyRetryReq req);

    /**
     * 灵感需求开款创建spu-新增款式识别记录
     */
    void add4DesignDemand(DesignDemand designDemand, DesignStyleCreateResp spuSkcCreateResp);
}
