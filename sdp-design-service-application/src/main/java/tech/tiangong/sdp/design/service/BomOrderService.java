package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import com.yibuyun.framework2.craft.bean.CraftDemandAuditCallbackDto;
import tech.tiangong.sdp.design.vo.query.bom.BomMaterialExportQuery;
import tech.tiangong.sdp.design.vo.req.*;
import tech.tiangong.sdp.design.vo.req.bom.*;
import tech.tiangong.sdp.design.vo.req.material.OppositeColorMaterialReq;
import tech.tiangong.sdp.design.vo.resp.bom.*;
import tech.tiangong.sdp.design.vo.resp.material.OppositeColorLatestVo;
import tech.tiangong.sdp.design.vo.resp.material.OppositeColorMaterialVo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * 干什么
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/13 14:49
 */
public interface BomOrderService {

	/**
	 * bom列表查询
	 * @param req 入参
	 * @return BomOrderListResp
	 */
	PageRespVo<BomOrderListResp> page(BomOrderListReq req);

	/**
	 * Bom详情
	 *
     * @param bomId bomId
	 * @return BomOrderDetailResp
	 * @throws Exception 线程异常
	 */
	BomOrderDetailResp detail(Long bomId) throws Exception;

	/**
	 * bom详情-web端
	 * @param req 详情查询req
	 * @return bom详情
	 */
	BomOrderDetailResp webDetail(BomWebDetailReq req) throws Exception;

	/**
	 * 打印bom单
	 * @param bomId bomId
	 * @return BomOrderPrintVo
	 * @throws Exception 线程异常
	 */
	BomOrderPrintVo print(Long bomId) throws Exception;

	/**
	 * 根据设计款skc_id关闭bom订单
	 * @param req 入参
	 */
	void closeBomWithPrototype(BomCloseWithPrototypeReq req);

	/**
	 * bom状态数量统计
	 * @param designerId 设计师ID
	 * @return BomOrderStateStatisticsResp
	 */
	List<BomOrderStateStatisticsResp> bomStateStatistics(Long designerId,Integer clothesDesigner);

	/**
	 * 处理工艺需求关闭失败
	 * @param callbackDto 入参
	 */
	void handlerCraftDemandCloseFail(CraftDemandAuditCallbackDto callbackDto);

	/**
	 * 根据设计款号查询最新已提交的工艺信息
	 * @param req 入参
	 * @return List<CraftDemandInfoVo>
	 */
	List<CraftDemandInfoVo> getLatestCraftInfoList(DesignCodeReq req, boolean noSearch);

	/**
	 * 根据设计款号查询最新已提交的Bom信息
	 * @param req 入参
	 * @return List<BomOrderVo>
	 */
	List<BomOrderVo> getLatestBomOrder(DesignCodeReq req);

	/**
	 * 根据BomID查询Bom基础信息
	 *
	 * @param bomId bom主键
	 * @return 响应结果
	 */
	BomOrderVo getBomById(Long bomId);

	/**
	 * 根据designCode查询Bom基础信息（含取消）
	 *
	 * @param designCode 款号
	 * @return 响应结果
	 */
	BomOrderVo getBomBySkc(String designCode);

	/**
	 * 根据设计款号查询最新已提交的Bom详情
	 * @param designCode 设计款号
	 * @return BomOrderDetailVo
	 * @throws Exception 线程异常
	 */
	BomOrderDetailVo getLatestBomOrderDetail(String designCode) throws Exception;

	/**
	 * 根据设计款号查询最新已提交的Bom详情(包含找料需求信息)
	 * @param designCode 设计款号 设计款号
	 * @return bom详情
	 */
	BomOrderDetailVo getLatestBomOrderDetailWithDemand(String designCode) throws Exception;

	/**
	 * 根据设计款号查询最新已提交的Bom详情-inner调用(过滤找料中的bom)
	 * @param designCode 设计款号
	 * @param noSearch 不包含的【找料中】的状态  true:不包含 false:包含
	 * @return BomOrderDetailVo
	 * @throws Exception 线程异常
	 */
	BomOrderDetailVo getLatestBomOrderDetail4Inner(String designCode, Boolean noSearch) throws Exception;

	/**
	 * 根据BomID查询工艺信息
	 * @param bomId bomId
	 * @return List<CraftDemandInfoVo>
	 */
	List<CraftDemandInfoVo> getCraftDemandInfoList(Long bomId);

	/**
	 * Bom物料清单核算用量
	 * @param req 入参
	 */
	void updateBomMaterialDosageAccount(BomMaterialDosageAccountReq req);

	/**
	 * 获取Bom物料
	 * @param bomId bomId
	 * @return BomOrderDetailVo
	 * @throws Exception 线程异常
	 */
	BomOrderDetailVo getBomOrderDetailBySdk(Long bomId) throws Exception;

	/**
	 * 根据款式SPU获取Bom信息
	 * @param styleCode spu编号
	 * @return List<SpuContainBomInfoVo>
	 */
	List<SpuContainBomInfoVo> getSpuContainBomInfo(String styleCode);

	/**
	 * bom批量打印
	 * @param req 入参
	 * @return List<BomOrderPrintVo>
	 */
	List<BomOrderPrintVo> batchPrint(BomBatchPrintReq req);

	/**
	 * 根据设计款号列表查询最新已提交的Bom详情-inner调用(过滤找料中的bom)
	 * @param req 入参
	 * @return List<BomOrderDetailVo>
	 */
	List<BomOrderDetailVo> getLatestBomOrderDetailList4Inner(DesignCodeReq req);

	/**
	 * 根据设计款号获取已提交的bom版本列表
	 * @param designCode 设计款号
	 * @return List<BomOrderDetailVo>
	 */

	List<BomOrderDetailVo> getSubmittedBomVersionList(String designCode);

	/**
	 * 根据设计款号列表打印最新已提交的Bom
	 * @param req 入参
	 * @return List<BomOrderPrintVo>
	 */
	List<BomOrderPrintVo> getBomOrderPrintListSdk(DesignCodeReq req);

	/**
	 * Bom导出Excel
	 * @param bomId bomId
	 * @return BomOrderExcelResp
	 */
	BomOrderExcelResp bomOrderExportExcel(Long bomId) throws Exception;

	/**
	 * 根据履约工艺请求获取Bom工艺信息
	 * @param req 入参
	 * @return List<CraftDemandInfoVo>
	 */
	List<CraftDemandInfoVo> getCraftDemandInfoBySupplyChain(SupplyChainCraftDemandReq req);

	/**
	 * 工艺需求匹配信息
	 * @param craftDemandId 工艺需求Id
	 * @return List<CraftDemandMatchResp>
	 */
	List<CraftDemandMatchResp> getCraftDemandMatchInfo(Long craftDemandId);

	/**
	 * 根据设计款号获取工艺信息(排除暂存工艺)
	 * @param designCode 设计款号
	 * @return List<CraftDemandInfoVo>
	 */
	List<CraftDemandInfoVo> getCraftDemandListByDesignCode(String designCode);

	/**
	 * 根据BOM物料ID获取BOM单(排除暂存物料)
	 * @param bomMaterialReq 入参
	 * @return Map<Long, BomOrderVo>
	 */
	Map<Long, BomOrderVo> getBomOrderByMaterialIds(BomMaterialReq bomMaterialReq);

	/**
	 * 获取工艺创建时间之后的裁后工艺需求数据(已推送至履约)
	 * @param craftCreateTime 工艺创建时间
	 * @return List<CraftDemandInfoVo>
	 */
	List<CraftDemandInfoVo> getCraftDemandListAfterTime(LocalDateTime craftCreateTime);

	/**
	 * 根据设计款号列表查询最新拆版已提交的工艺信息
	 * @param designCodeReq Req
	 */
	List<CraftDemandInfoVo> getLatestDemolitionCraftList(DesignCodeReq designCodeReq);

	/**
	 * 第一次拆版生成BOM单
	 * @param generateBomReq 入参
	 */
	void demolitionGenerateBom(DemolitionGenerateBomReq generateBomReq);

	/**
	 * 查询好料网面辅料信息  自选物料
	 *
	 * @param req 入参
	 * @return GoodMaterialInfoResp
	 */
	GoodMaterialInfoResp goodMaterialInfo(GoodMaterialInfoReq req);

	/**
	 * 查询中台面料信息
	 * 	(灵感设计需求-推荐面料查询)
	 * @param spuSkuReqList 入参
	 * @param supplierIdSet 供应商id集合
	 * @return 物料信息
	 */
	List<BomOrderMaterialVo> getFabricMaterialVo(List<GoodMaterialInfoReq.SpuSkuIdReq> spuSkuReqList, Set<Long> supplierIdSet);

	/**
	 * skuId查询好料网面辅料信息
	 *
	 * @param req 入参
	 * @return GoodMaterialInfoResp
	 */
	LyMaterialQueryResp queryLyMaterial(LyMaterialQueryReq req);

	/**
	 * 查询bom单可引用的skc
	 *
	 * @param bomId bomId
	 * @return BomQuoteSkcResp
	 */
	BomQuoteSkcResp quoteSkc(Long bomId);

	/**
	 * 查询引用款的Bom详情
	 *
	 * @param req 入参
	 * @return bom详情
	 * @throws Exception 线程异常
	 */
	BomOrderDetailResp getQuoteBomOrderDetail(BomQuoteDetailReq req) throws Exception;

	/**
	 * 根据设计款号查询最新已提交(已核算)的Bom采购列表
	 *
	 * @param designCode 设计款号
	 * @return 采购列表
	 */
	BomOrderApplyPurchaseResp getBomApplyPurchaseList(String designCode) throws ExecutionException, InterruptedException;

	/**
	 * 根据物料id或者需求id批量查询最新的物料详情信息
	 *
	 * @param req 入参
	 * @return 响应结果
	 */
	List<OppositeColorLatestVo> getLatestMaterialList(MaterialInfoReq req);

	/**
	 * 根据物料id或者需求id批量查询最新的物料详情信息（及其被对色的物料信息）
	 *
	 * @param req 入参
	 * @return 响应结果
	 */
	List<OppositeColorMaterialVo> getOppositeColorMaterialList(OppositeColorMaterialReq req);


	/**
	 * 更新最新版本物料的价格和价格有效期
	 *
	 * @param designCode 设计款号
	 */
	void updateLatestPriceByDesignCode(String designCode);


	/**
	 * 更新最新版本物料的价格和价格有效期
	 * @param bomId bomId
	 */
	void updateLatestPriceByBomId(Long bomId);

	/**
	 * 根据bom单物料id批量查询物料信息
	 * @param req 入参
	 * @return bom单物料
	 */
	List<BomMaterialCheckCountVo> listBomMaterialCheckCount(BomMaterialReq req);

	/**
	 * 根据设计款号查询最新bom单基础信息
	 *
	 * @param designCode 设计款号
	 * @return BomOrderBaseResp
	 */
    BomOrderBaseResp getBomBaseBySkc(String designCode);

	/**
	 * bom单物料导出excel
	 * @param query 入参
	 * @return bom物料信息
	 */
    List<BomMaterialExcelResp> exportMaterialExcel(BomMaterialExportQuery query);

	/**
	 * 根据skc查询最新bom单物料图片信息
	 *
	 * @param req 入参
	 * @return List<BomMaterialPictureResp>
	 */
	List<BomMaterialPictureResp> queryMaterialPicture(BomMaterialPictureReq req);
}
