package tech.tiangong.sdp.design.controller.inner;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.enums.SpuSkcPushTypeEnum;
import tech.tiangong.sdp.design.service.DesignStyleService;
import tech.tiangong.sdp.design.vo.req.prototype.inner.PushSpuSkc2ZjReq;


/**
 * 设计SPU-内部接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/style")
public class DesignStyleInnerController extends BaseController {
    private final DesignStyleService designStyleService;


    /**
     * 推送SPU与SKC信息到致景(推送过也会推送)
     *
     * @param req 入参
     * @return 致景response
     */
    @PostMapping("/push2zj/spu-skc")
    @NoRepeatSubmitLock(lockTime = 6L)
    DataResponse<String> forcePushSpuSkc2Zj(@RequestBody @Validated PushSpuSkc2ZjReq req) {
        req.setPushZjTypeEnum(SpuSkcPushTypeEnum.SPU_SKC);
        return DataResponse.ok(designStyleService.forcePushSpuSkc2Zj(req));
    }


}