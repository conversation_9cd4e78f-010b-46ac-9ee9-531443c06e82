package tech.tiangong.sdp.design.service.download.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.net.MediaType;
import com.zjkj.booster.common.constant.DatePatternConstants;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.design.entity.DesignAsyncTask;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.helper.UploaderOssHelper;
import tech.tiangong.sdp.design.service.download.DownloadTaskStrategy;
import tech.tiangong.sdp.design.vo.dto.download.FileUploadDTO;
import tech.tiangong.sdp.design.vo.resp.visual.BatchUploadFolderErrorResp;
import tech.tiangong.sdp.utils.PlmExcelExportUtil;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

/**
 * 下载视觉任务批量上传图片异常信息
 */
@Slf4j
@AllArgsConstructor
@Component
public class DownloadVisualTaskBatchUploadImageFailImpl implements DownloadTaskStrategy {

    private final UploaderOssHelper uploaderOssHelper;

    private static final DateTimeFormatter PURE_DATETIME_PATTERN = DateTimeFormatter.ofPattern(DatePatternConstants.PURE_DATETIME_PATTERN);

    @Override
    public DesignAsyncTaskTypeEnum getTaskType() {
        return DesignAsyncTaskTypeEnum.DOWNLOAD_VISUAL_TASK_BATCH_UPLOAD_IMAGE_FAIL;
    }

    @Override
    public List<FileUploadDTO> processDownloadTask(DesignAsyncTask task) {
        log.info("==== DownloadVisualTaskBatchUploadImageFailImpl processDownloadTask {}:{}",getTaskType().getDesc(), JSONObject.toJSONString(task));
        File tempDir = null;
        try {
            tempDir = createTempDirectory(task.getAsyncTaskId());
            // 创建Excel文件
            File excelFile = new File(tempDir, task.getTaskName()+".xlsx");
            List<BatchUploadFolderErrorResp> dataList = JSONArray.parseArray(task.getParameters(), BatchUploadFolderErrorResp.class);
            PlmExcelExportUtil.exportToExcel(excelFile, BatchUploadFolderErrorResp.class, dataList);
            FileUploadDTO fileUploadDTO = uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type());
            return Collections.singletonList(fileUploadDTO);
        } catch (Exception e) {
            throw new RuntimeException("下载视觉任务批量上传图片异常信息失败", e);
        }finally {
            cleanupTempFiles(tempDir);
        }
    }

    private File createTempDirectory(Long taskId) throws IOException {
        String tempDirStr = FileUtils.getTempDirectoryPath() + File.separator +
                "download_" + taskId + File.separator +
                PURE_DATETIME_PATTERN.format(LocalDateTime.now());

        File tempDir = new File(tempDirStr);
        FileUtils.forceMkdir(tempDir);
        return tempDir;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(File tempDir) {
        try {
            if(tempDir!=null){
                FileUtils.deleteDirectory(tempDir);
            }
        } catch (IOException e) {
            log.error("清理临时文件失败:"+tempDir.getAbsolutePath(),e);
        }
    }
}
