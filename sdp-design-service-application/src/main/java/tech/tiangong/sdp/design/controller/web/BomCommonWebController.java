package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.BomCommonService;
import tech.tiangong.sdp.design.vo.req.DesignCodeReq;
import tech.tiangong.sdp.design.vo.req.bom_common.BomDetailQuery;
import tech.tiangong.sdp.design.vo.req.bom_common.LatestBomDetailQuery;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderVo;
import tech.tiangong.sdp.design.vo.resp.bom_common.BomDetailVo;

import java.util.List;

/**
 * BOM单-公共接口-web
 *
 *  提供给非PLM系统调用
 * <AUTHOR>
 * @date 2023/5/31 14:57
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/bom-common")
public class BomCommonWebController {

    private final BomCommonService bomCommonService;

    /**
     * 根据设计款号查询最新已提交的Bom信息
     *
     * @param req 入参
     * @return List<BomOrderVo>
     */
    @PostMapping("/bom-order/latest")
    DataResponse<List<BomOrderVo>> getLatestBomOrder(@Validated @RequestBody DesignCodeReq req) {
        return DataResponse.ok(bomCommonService.getLatestBomOrder(req));
    }

    /**
     * 根据BomID查询Bom详情
     *
     * @param req 入参
     * @return BomDetailVo
     */
    @PostMapping("/detail")
    DataResponse<BomDetailVo> getBomOrderDetail(@Validated @RequestBody BomDetailQuery req) {
        return DataResponse.ok(bomCommonService.getBomDetailById(req));
    }

    /**
     * 根据设计款号查询最新已提交的Bom详情
     *
     * @param req 入参
     * @return BomDetailVo
     */
    @PostMapping("/detail/skc-latest")
    DataResponse<BomDetailVo> getLatestBomDetailBySkc(@Validated @RequestBody LatestBomDetailQuery req) {
        return DataResponse.ok(bomCommonService.getLatestBomDetailBySkc(req));
    }

}
