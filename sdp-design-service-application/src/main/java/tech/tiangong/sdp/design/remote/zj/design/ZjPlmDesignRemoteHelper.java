package tech.tiangong.sdp.design.remote.zj.design;

import cn.yibuyun.framework.net.DataResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import tech.tiangong.sdp.core.config.ZjOpenFeignUserContentConfig;
import tech.tiangong.sdp.design.vo.req.zj.design.*;
import tech.tiangong.sdp.design.vo.resp.zj.design.DesignStyleCreateOpenV2Resp;
import tech.tiangong.sdp.design.vo.resp.zj.design.PrototypeInfoOpenV2Resp;
import tech.tiangong.sdp.design.vo.resp.zj.design.PrototypeOrderMaterialOpenResp;

import java.util.List;

/**
 * @Created by j<PERSON><PERSON><PERSON>
 * @ClassName PlmDesignRemoteHelper
 * @Description 致景PLM-设计模块 对接路由
 * @Date 2024/11/30 11:28
 */
@FeignClient(value = "plm-design",
        contextId = "PLM-DesignCommodityClient",
        configuration = ZjOpenFeignUserContentConfig.class,
        path = "/zj-tg-api/plm-design/open",
        url = "${cx-tg.domain.url}")
public interface ZjPlmDesignRemoteHelper {

    /**
     * 创建款-v2
     *
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84457"
     * @param createReq 创建请求
     * @return
     */
    @PostMapping("/v2/design-prototype/create")
    DataResponse<DesignStyleCreateOpenV2Resp> createDesignPrototype(@RequestBody DesignStyleCreateOpenV2Req createReq);

    /**
     * 上传营销图
     *
     * @param req 上传营销图请求
     * @return Void
     */
    @PostMapping("/v2/prototype/marketing-picture/save")
    DataResponse<Void> saveMarketingPicture(@RequestBody @Validated PrototypeMarketingPictureSaveOpenV2Req req);

    /**
     * 取消设计款-v2
     *
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84455"
     * @param cancelReq 取消请求对象
     * @return Void
     */
    @PostMapping("/v2/prototype/cancel")
    DataResponse<Void> cancelDesign(@RequestBody PrototypeCancelOpenV2Req cancelReq);


    /**
     * 查询渠道skc对应plm的skc信息-v2
     *
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84459"
     *
     * @param infoReq skc信息请求参数
     * @return
     */
    @PostMapping("/v2/prototype/info")
    DataResponse<PrototypeInfoOpenV2Resp> prototypeInfo(@RequestBody PrototypeInfoOpenV2Req infoReq);

    /**
     * 查询渠道skc最新的齐套单
     *
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84520"
     *
     * @param materialReq 请求参数
     * @return 齐套单信息
     */
    @PostMapping("/v2/prototype/find-latest-materials")
    DataResponse<List<PrototypeOrderMaterialOpenResp>> findLatestMaterial(@RequestBody PrototypeOrderMaterialOpenReq materialReq);

    /**
     * 批量更新设计师
     *
     * @param updateReq 创建请求
     * @return Void
     */
    @PostMapping("/v2/prototype/update-designer")
    DataResponse<Void> batchUpdateDesigner(@RequestBody @Validated DesignerUpdateOpenV2Req updateReq);

    /**
     * 修改skc制作方式
     *
     * @param updateReq 更新请求
     * @return void
     */
    @PostMapping("/v2/prototype/update-clothes-type")
    DataResponse<Void> updatePrototypeClothesType(@RequestBody @Validated ZjUpdateClothesTypeOpenV2Req updateReq);

    /**
     * 根据spu就旧款bizChannel变成2
     *
     * @param styleCodeList 旧款SPU集合
     * @return void
     */
    @PostMapping("/v1/prototype/migrate-old-style")
    DataResponse<Void> updateBizChannelBySpu(@RequestBody @Validated List<String> styleCodeList);

}
