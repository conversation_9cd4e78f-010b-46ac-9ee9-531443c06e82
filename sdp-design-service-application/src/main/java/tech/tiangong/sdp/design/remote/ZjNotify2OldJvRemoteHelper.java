package tech.tiangong.sdp.design.remote;

import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.net.DataResponse;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.qy.vo.req.ZjNotify2OldJvReq;

/**
 * <p>
 *   字典服务
 * </p>
 *
 **/
@Service
@Slf4j
@AllArgsConstructor
public class ZjNotify2OldJvRemoteHelper {

    private final ZjNotify2OldJvClient zjNotify2OldJvClient;


    /**
     * 转发致景通知到旧JV
     * @param mqMessageReq 入参
     */
    public void notify2OldJv(MqMessageReq mqMessageReq) {
        SdpDesignException.notNull(mqMessageReq, "通知消息为空! ");
        ZjNotify2OldJvReq req = new ZjNotify2OldJvReq();
        req.setSystemCode("SDP");
        req.setExchange(mqMessageReq.getExchange());
        if (StrUtil.isNotBlank(mqMessageReq.getRoutingKey())) {
            req.setRoutingKey(mqMessageReq.getRoutingKey());
        }
        req.setMqContent(mqMessageReq.getMqContent());
        log.info("=== 转发致景通知到旧JV-req:{}", JSON.toJSONString(req));
        DataResponse<Void> response = zjNotify2OldJvClient.fromNewJv(req);
        log.info("=== 转发致景通知到旧JV-response:{}", JSON.toJSONString(response));
        if (!response.isSuccessful()) {
            throw new SdpDesignException("转发致景通知到旧JV:"+response.getMessage());
        }
    }

}
