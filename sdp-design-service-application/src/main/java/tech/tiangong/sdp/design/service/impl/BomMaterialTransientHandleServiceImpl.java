package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import com.yibuyun.scm.common.dto.accessories.response.CategoryTreeMapVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSkuVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import com.yibuyun.scm.common.dto.fabric.response.CommoditySkuCollectionRespVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierExtVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.BomOrderMaterialConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.remote.ProductRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.BomMaterialCommonService;
import tech.tiangong.sdp.design.service.BomMaterialTransientHandleService;
import tech.tiangong.sdp.design.service.DesignRemarksService;
import tech.tiangong.sdp.design.vo.dto.bom.BomMaterialInfoDto;
import tech.tiangong.sdp.design.vo.req.bom.BomCraftTransientHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialTransientHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderUpdateV3Req;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandSaveV3Req;
import tech.tiangong.sdp.design.vo.req.material.MaterialSnapshotCreateReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 * bom暂存 物料处理服务
 * <AUTHOR>
 * @date 2022/11/17 11:00
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BomMaterialTransientHandleServiceImpl implements BomMaterialTransientHandleService {

    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final ProductRemoteHelper productRemoteHelper;
    private final BomOrderMaterialTransientRepository bomOrderMaterialTransientRepository;

    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final DesignRemarksService designRemarksService;
    private final BomMaterialCommonService bomMaterialCommonService;
    private final SpecialAccessoriesRepository specialAccessoriesRepository;
    private final SpecialAccessoriesTransientRepository specialAccessoriesTransientRepository;
    private final CraftDemandInfoTransientRepository craftDemandInfoTransientRepository;




    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftTransientHandleReq firstTransient(BomMaterialTransientHandleReq req) {
        //待提交,首次暂存
        BomOrder bomOrder = req.getBomOrder();
        BomOrderTransient transientBom = req.getTransientBom();
        Long bomTransientId = transientBom.getBomTransientId();
        //1, 从履约查询物料信息并校验
        BomMaterialInfoDto materialInfoDto = bomMaterialCommonService.queryAndCheckMaterialInfoTransient(req, bomOrder, transientBom);

        //收集暂存 添加/删除的工艺(历史数据可能有删除的工艺(2.0时工艺维护中拆板中, 拆板提交物料确认后,, 待提交的bom就有物料与工艺了))
        List<CraftDemandSaveV3Req> addCraftDemandReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);

        //3, 物料新增处理; //新增的物料保存到暂存表中;
        this.addMaterialHandle(req, bomOrder, materialInfoDto, addCraftDemandReqList, bomTransientId);

        //4, 历史面辅料更新与删除处理: 兼容2.0数据(bom待提交时已经有了物料); 从原表复制到暂存表更新
        Map<Long, Long> oldNewMaterialIdMap = this.historyMaterialHandle(req, bomOrder, bomTransientId, addCraftDemandReqList, delCraftIdSet, bomTransientId,materialInfoDto);

        //5, 原有特辅更新,更新原特辅: 特辅更新; 从原表复制到暂存表更新
        this.specialAccessoriesUpdateHandle(req, bomTransientId,materialInfoDto);

        //6, 引用新增的特殊辅料处理(新增暂存状态的特殊辅料与备注)
        this.addQuoteSpecialAccessoriesTransient(transientBom, req, bomOrder);
        //特辅删除处理; //引用场景,导致原有特辅删除; 从原表复制到暂存表,标记删除
        this.specialAccessoriesDelHandle4WaitSubmit(req, bomTransientId);

        //7,工艺处理req
        return BomCraftTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .transientBomId(transientBom.getBomId())
                .craftSourceTypeEnum(CraftSourceTypeEnum.CHOOSE)
                .addCraftDemandList(addCraftDemandReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldNewDemandIdMap(Collections.emptyMap())
                .oldTransientCraftMap(Collections.emptyMap())
                .build();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftTransientHandleReq reTransient(BomMaterialTransientHandleReq req) {
        //待提交,再次暂存
        BomOrder bomOrder = req.getBomOrder();
        BomOrderTransient transientBom = req.getTransientBom();
        Long bomTransientId = transientBom.getBomTransientId();
        //1, 从履约查询物料信息并校验
        BomMaterialInfoDto materialInfoDto = bomMaterialCommonService.queryAndCheckMaterialInfoTransient(req, bomOrder, transientBom);

        //收集暂存 添加/删除的工艺
        List<CraftDemandSaveV3Req> addCraftDemandReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);

        //3, 物料新增(); //新增的物料都是暂存的
        this.addMaterialHandle(req, bomOrder, materialInfoDto, addCraftDemandReqList, bomTransientId);

        //4, 物料更新(); //更新暂存表数据
        this.updateMaterialHandle4ReTransient(req, bomTransientId, addCraftDemandReqList, delCraftIdSet,materialInfoDto);

        //5, 面辅料删除(); //更新暂存表数据; 暂存数据逻辑删除; 原数据标记删除, 收集删除的工艺
        this.delMaterialHandle4ReTransient(req, bomTransientId, delCraftIdSet);

        //6, 引用新增的特殊辅料处理(新增暂存状态的特殊辅料与备注)
        this.addQuoteSpecialAccessoriesTransient(transientBom, req, bomOrder);
        //特辅删除处理; //引用场景,导致原特辅删除; 暂存数据逻辑删除; 原数据标记删除
        this.delSpecial4ReTransient(req);

        //8,工艺处理req
        return BomCraftTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .transientBomId(transientBom.getBomId())
                .craftSourceTypeEnum(CraftSourceTypeEnum.CHOOSE)
                .addCraftDemandList(addCraftDemandReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldTransientCraftMap(Collections.emptyMap())
                .oldNewMaterialIdMap(Collections.emptyMap())
                .oldNewDemandIdMap(Collections.emptyMap())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftTransientHandleReq firstTransient4Submitted(BomMaterialTransientHandleReq req) {
        //已提交-首次暂存: 要将原物料复制更新到暂存表中
        BomOrder bomOrder = req.getBomOrder();
        BomOrderTransient transientBom = req.getTransientBom();
        Long bomTransientId = transientBom.getBomTransientId();
        //1, 从履约查询物料信息并校验
        BomMaterialInfoDto materialInfoDto = bomMaterialCommonService.queryAndCheckMaterialInfoTransient(req, bomOrder, transientBom);

        //收集暂存: 添加/删除的工艺; 新旧物料映射Map
        List<CraftDemandSaveV3Req> addCraftDemandReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);
        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();

        //3, 物料新增(); //新增的物料都是暂存的,新增物料快照同步履约
        this.addMaterialHandle(req, bomOrder, materialInfoDto, addCraftDemandReqList, bomTransientId);

        //4, 物料更新(); //复制原有数据,更新后添加到暂存表中; 并收集增删工艺与新旧物料id映射Map
        this.updateFirstTransient4Submitted(req, materialInfoDto, bomOrder, transientBom, addCraftDemandReqList, delCraftIdSet, oldNewMaterialIdMap);

        //5, 面辅料删除(); //删除的都是原物料, 复制到暂存表中标记删除, 收集删除的工艺与新旧物料id映射Map
        this.delMaterialFirstTransient4Submitted(req, bomOrder, bomTransientId, delCraftIdSet, oldNewMaterialIdMap);

        //6, 引用新增特辅处理,
        this.addQuoteSpecialAccessoriesTransient(transientBom, req, bomOrder);

        //7, 特辅删除处理; //引用场景,导致原有特辅删除; 从原表复制到暂存表,标记删除; 收集新旧物料id映射Map
        this.delSpecialFirstTransient4Submitted(req, bomTransientId, oldNewMaterialIdMap);

        //8, 工艺处理req
        return BomCraftTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .transientBomId(transientBom.getBomId())
                .craftSourceTypeEnum(CraftSourceTypeEnum.CHOOSE)
                .addCraftDemandList(addCraftDemandReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldTransientCraftMap(Collections.emptyMap())
                .oldNewDemandIdMap(Collections.emptyMap())
                .build();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftTransientHandleReq reTransient4Submitted(BomMaterialTransientHandleReq req) {
        //已提交,再次暂存
        BomOrder bomOrder = req.getBomOrder();
        BomOrderTransient transientBom = req.getTransientBom();
        Long bomTransientId = transientBom.getBomTransientId();
        //1, 从履约查询物料信息并校验
        BomMaterialInfoDto materialInfoDto = bomMaterialCommonService.queryAndCheckMaterialInfoTransient(req, bomOrder, transientBom);

        //收集暂存 添加/删除的工艺
        List<CraftDemandSaveV3Req> addCraftDemandReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);

        //3, 物料新增(); //新增的物料都是暂存的
        this.addMaterialHandle(req, bomOrder, materialInfoDto, addCraftDemandReqList, bomTransientId);

        //4, 物料更新(); //更新暂存表数据;
        this.updateMaterialHandle4ReTransient(req, bomTransientId, addCraftDemandReqList, delCraftIdSet,materialInfoDto);

        //5, 面辅料删除(); //更新暂存表数据; 暂存数据逻辑删除; 原数据标记删除, 收集删除的工艺
        this.delMaterialHandle4ReTransient(req, bomTransientId, delCraftIdSet);

        //6, 引用新增特辅处理
        this.addQuoteSpecialAccessoriesTransient(transientBom, req, bomOrder);

        //7, 特辅删除处理; //引用场景,暂存数据逻辑删除; 原数据标记删除,
        this.delSpecial4ReTransient(req);

        //8,工艺处理req
        return BomCraftTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .transientBomId(transientBom.getBomId())
                .craftSourceTypeEnum(CraftSourceTypeEnum.CHOOSE)
                .addCraftDemandList(addCraftDemandReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldTransientCraftMap(Collections.emptyMap())
                .oldNewDemandIdMap(Collections.emptyMap())
                .oldNewMaterialIdMap(Collections.emptyMap())
                .build();
    }

    private void delSpecial4ReTransient(BomMaterialTransientHandleReq req) {
        // 再次暂存:  特辅删除处理; //引用场景,导致有特辅删除;暂存数据逻辑删除; 原数据标记删除,
        if (CollUtil.isEmpty(req.getDelBomMaterialIds())) {
            return;
        }
        List<SpecialAccessoriesTransient> specialTransientDelList = specialAccessoriesTransientRepository.listByIds(req.getDelBomMaterialIds());
        if (CollUtil.isEmpty(specialTransientDelList)) {
            return;
        }
        List<SpecialAccessoriesTransient> updateSpecialTransientList = new LinkedList<>();
        List<Long> logicDelIdList = new LinkedList<>();

        specialTransientDelList.forEach(delSpecial -> {
            //删除暂存添加的特辅, 逻辑删除
            if (Objects.isNull(delSpecial.getOriginSpecialId())) {
                logicDelIdList.add(delSpecial.getOriginSpecialId());
                return;
            }
            //原有特辅
            SpecialAccessoriesTransient updateSpecialTransient = new SpecialAccessoriesTransient();
            updateSpecialTransient.setSpecialAccessoriesId(delSpecial.getSpecialAccessoriesId());
            updateSpecialTransient.setState(BomMaterialStateEnum.CLOSED.getCode());
            updateSpecialTransientList.add(updateSpecialTransient);
        });

        //原有物料删除, 更新为关闭
        if (CollUtil.isNotEmpty(updateSpecialTransientList)) {
            specialAccessoriesTransientRepository.updateBatchById(updateSpecialTransientList);
        }

        //暂存添加的物料删除, 直接逻辑删除
        if (CollUtil.isNotEmpty(logicDelIdList)) {
            specialAccessoriesTransientRepository.removeByIds(logicDelIdList);
        }
    }

    private void specialAccessoriesDelHandle4WaitSubmit(BomMaterialTransientHandleReq req, Long bomTransientId) {
        // 待提交:  特辅删除处理; //引用场景,导致原有特辅删除; 从原表复制到暂存表,标记删除
        if (CollUtil.isEmpty(req.getDelBomMaterialIds())) {
            return;
        }
        List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.listByIds(req.getDelBomMaterialIds());
        if (CollUtil.isEmpty(specialAccessoriesList)) {
            return;
        }
        Map<Long, SpecialAccessories> specialMap = specialAccessoriesList.stream().collect(Collectors.toMap(SpecialAccessories::getSpecialAccessoriesId, Function.identity(), (k1, k2) -> k1));
        List<SpecialAccessoriesTransient> newSpecialTransientList = new LinkedList<>();

        specialAccessoriesList.forEach(item -> {
            SpecialAccessories oldSpecial = specialMap.get(item.getSpecialAccessoriesId());
            if (Objects.isNull(oldSpecial)) {
                return;
            }
            SpecialAccessoriesTransient newSpecialTransient = new SpecialAccessoriesTransient();
            //暂存特辅id使用原主键id, 因为待提交bom提交后不升版本
            BeanUtils.copyProperties(oldSpecial, newSpecialTransient);
            newSpecialTransient.setOriginSpecialId(oldSpecial.getSpecialAccessoriesId());
            newSpecialTransient.setState(SpecialAccessoriesStateEnum.CLOSED.getCode());
            newSpecialTransient.setBomTransientId(bomTransientId);
            newSpecialTransientList.add(newSpecialTransient);
        });

        //保存到暂存表
        specialAccessoriesTransientRepository.saveBatch(newSpecialTransientList);
    }

    private void delMaterialHandle4ReTransient(BomMaterialTransientHandleReq req,
                                               Long bomTransientId,
                                               Set<Long> delCraftIdSet) {
        // 待提交-再次暂存 面辅料删除处理(特辅删除在后续方法处理)
        if (CollUtil.isEmpty(req.getDelBomMaterialIds())) {
            return;
        }
        List<BomOrderMaterialTransient> delMaterialTransientList = bomOrderMaterialTransientRepository.listByIds(req.getDelBomMaterialIds());
        if (CollUtil.isEmpty(delMaterialTransientList)) {
            return;
        }
        //查询工艺暂存表中的工艺
        Map<Long, List<CraftDemandInfoTransient>> craftTransientMap = craftDemandInfoTransientRepository.listByBomTransientId(bomTransientId).stream()
                    .filter(item -> !Objects.equals(CraftDemandStateEnum.CLOSED.getCode(), item.getState()))
                    .collect(Collectors.groupingBy(CraftDemandInfoTransient::getBomMaterialId));

        List<BomOrderMaterialTransient> updateMaterialTransientList = new LinkedList<>();
        List<Long> logicDelIdList = new LinkedList<>();

        delMaterialTransientList.forEach(item -> {
            //删除物料对应的工艺(收集起来提供给工艺环节处理)
            if (CollUtil.isNotEmpty(craftTransientMap) && CollUtil.isNotEmpty(craftTransientMap.get(item.getBomMaterialId()))) {
                Set<Long> craftIdSet = craftTransientMap.get(item.getBomMaterialId()).stream().map(CraftDemandInfoTransient::getCraftDemandId).collect(Collectors.toSet());
                delCraftIdSet.addAll(craftIdSet);
            }

            //暂存添加的物料
            if (Objects.isNull(item.getOriginMaterialId())) {
                logicDelIdList.add(item.getBomMaterialId());
                return;
            }
            //原有物料
            BomOrderMaterialTransient updateMaterialTransient = new BomOrderMaterialTransient();
            updateMaterialTransient.setBomMaterialId(item.getBomMaterialId());
            updateMaterialTransient.setMaterialState(BomMaterialStateEnum.CLOSED.getCode());
            updateMaterialTransientList.add(updateMaterialTransient);

        });

        //原有物料删除, 更新为关闭
        if (CollUtil.isNotEmpty(updateMaterialTransientList)) {
            bomOrderMaterialTransientRepository.updateBatchById(updateMaterialTransientList);
        }

        //暂存添加的物料删除, 直接逻辑删除
        if (CollUtil.isNotEmpty(logicDelIdList)) {
            bomOrderMaterialTransientRepository.removeByIds(logicDelIdList);
        }
    }

    private void delSpecialFirstTransient4Submitted(BomMaterialTransientHandleReq req,
                                                    Long bomTransientId,
                                                    Map<Long, Long> oldNewMaterialIdMap) {
        // 已提交-首次暂存 特辅删除处理
        if (CollUtil.isEmpty(req.getDelBomMaterialIds())) {
            return;
        }
        List<SpecialAccessories> delSpecialList = specialAccessoriesRepository.listByIds(req.getDelBomMaterialIds());
        if (CollUtil.isEmpty(delSpecialList)) {
            return;
        }
        List<SpecialAccessoriesTransient> addSpecialTransientList = new LinkedList<>();

        //已提交的首次暂存, 删除的面辅料,都是原有物料, 复制到暂存表中, 标记删除;
        delSpecialList.forEach(item -> {
            Long newSpecialTransientId = IdPool.getId();
            Long originSpecialId = item.getSpecialAccessoriesId();
            SpecialAccessoriesTransient addSpecialTransient = new SpecialAccessoriesTransient();
            BeanUtils.copyProperties(item, addSpecialTransient);
            addSpecialTransient.setSpecialAccessoriesId(newSpecialTransientId);
            addSpecialTransient.setBomTransientId(bomTransientId);
            addSpecialTransient.setOriginSpecialId(originSpecialId);
            addSpecialTransient.setState(BomMaterialStateEnum.CLOSED.getCode());
            addSpecialTransientList.add(addSpecialTransient);
            oldNewMaterialIdMap.put(originSpecialId, newSpecialTransientId);

        });

        //复制删除的特辅到暂存表中
        if (CollUtil.isNotEmpty(addSpecialTransientList)) {
            specialAccessoriesTransientRepository.saveBatch(addSpecialTransientList);
        }
    }

    private void delMaterialFirstTransient4Submitted(BomMaterialTransientHandleReq req,
                                                     BomOrder bomOrder,
                                                     Long bomTransientId,
                                                     Set<Long> delCraftIdSet,
                                                     Map<Long, Long> oldNewMaterialIdMap) {
        // 已提交-首次暂存 面辅料删除处理(特辅删除在后续方法处理)
        if (CollUtil.isEmpty(req.getDelBomMaterialIds())) {
            return;
        }
        List<BomOrderMaterial> delMaterialList = bomOrderMaterialRepository.listByIds(req.getDelBomMaterialIds());
        if (CollUtil.isEmpty(delMaterialList)) {
            return;
        }
        Long oldBomId = bomOrder.getBomId();
        //查询原有工艺
        Map<Long, List<CraftDemandInfo>> oldCraftMap = craftDemandInfoRepository.getListByBomIdsAndState(List.of(oldBomId), CraftDemandStateEnum.SUBMIT.getCode()).stream()
                    .filter(item -> !Objects.equals(CraftDemandStateEnum.CLOSED.getCode(), item.getState()))
                    .collect(Collectors.groupingBy(CraftDemandInfo::getBomMaterialId));

        List<BomOrderMaterialTransient> addMaterialTransientList = new LinkedList<>();

        //已提交的首次暂存, 删除的面辅料,都是原有物料, 复制到暂存表中, 标记删除;
        delMaterialList.forEach(item -> {
            Long newMaterialTransientId = IdPool.getId();
            Long originMaterialId = item.getBomMaterialId();
            BomOrderMaterialTransient addMaterialTransient = new BomOrderMaterialTransient();
            BeanUtils.copyProperties(item, addMaterialTransient);
            addMaterialTransient.setBomMaterialId(newMaterialTransientId);
            addMaterialTransient.setBomTransientId(bomTransientId);
            addMaterialTransient.setOriginMaterialId(originMaterialId);
            addMaterialTransient.setMaterialState(BomMaterialStateEnum.CLOSED.getCode());
            addMaterialTransientList.add(addMaterialTransient);
            oldNewMaterialIdMap.put(originMaterialId, newMaterialTransientId);

            //删除物料对应的工艺(收集起来提供给工艺环节处理)
            if (CollUtil.isNotEmpty(oldCraftMap) && CollUtil.isNotEmpty(oldCraftMap.get(item.getBomMaterialId()))) {
                Set<Long> craftIdSet = oldCraftMap.get(item.getBomMaterialId()).stream().map(CraftDemandInfo::getCraftDemandId).collect(Collectors.toSet());
                delCraftIdSet.addAll(craftIdSet);
            }
        });

        //复制删除的原有物料到暂存表中
        if (CollUtil.isNotEmpty(addMaterialTransientList)) {
            bomOrderMaterialTransientRepository.saveBatch(addMaterialTransientList);
        }
    }

    private void specialAccessoriesUpdateHandle(BomMaterialTransientHandleReq req,
                                                Long bomTransientId,BomMaterialInfoDto materialInfoDto) {
        //待提交, 首次暂存, 原特辅更新处理: 复制到暂存表; 由于不升版本, 暂存特辅id与原特辅id一样
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        if (CollUtil.isEmpty(req.getUpdateBomMaterials())) {
            return;
        }
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();

        List<Long> materialIdList = req.getUpdateBomMaterials().stream().map(BomOrderUpdateV3Req.UpdateBomMaterialReq::getBomMaterialId).collect(Collectors.toList());
        if (CollUtil.isEmpty(materialIdList)) {
            return;
        }
        List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.listByIds(materialIdList);
        if (CollUtil.isEmpty(specialAccessoriesList)) {
            return;
        }
        Map<Long, SpecialAccessories> specialMap = specialAccessoriesList.stream().collect(Collectors.toMap(SpecialAccessories::getSpecialAccessoriesId, Function.identity(), (k1, k2) -> k1));
        List<SpecialAccessoriesTransient> newSpecialTransientList = new LinkedList<>();

        req.getUpdateBomMaterials().stream()
                .filter(updateBomMaterialReq -> Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode()))
                .forEach(updateBomMaterialReq -> {
                    SpecialAccessories oldSpecial = specialMap.get(updateBomMaterialReq.getBomMaterialId());
                    if (Objects.isNull(oldSpecial)) {
                        return;
                    }
                    SpecialAccessoriesTransient newSpecialTransient = new SpecialAccessoriesTransient();
                    BeanUtils.copyProperties(oldSpecial, newSpecialTransient);
                    newSpecialTransient.setOriginSpecialId(oldSpecial.getSpecialAccessoriesId());
                    newSpecialTransient.setPartUse(updateBomMaterialReq.getPartUse());
                    newSpecialTransient.setCuttingMethod(updateBomMaterialReq.getCuttingMethod());
                    newSpecialTransient.setBomTransientId(bomTransientId);
                    if (StringUtils.isNotBlank(updateBomMaterialReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, oldSpecial.getSpecialAccessoriesId(), updateBomMaterialReq.getRemark()));
                    }

                    //获取最新的价格和价格有效期
                    BomOrderMaterialConverter.buildResetTransientBomOrderMaterialPrice(updateBomMaterialReq.getDemandType(),
                            null,newSpecialTransient, accessoriesSkuMap.get(updateBomMaterialReq.getSkuId()),
                            null);
                    newSpecialTransientList.add(newSpecialTransient);

                });
        //保存到暂存表
        specialAccessoriesTransientRepository.saveBatch(newSpecialTransientList);

        //暂存物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void addMaterialHandle(BomMaterialTransientHandleReq req,
                                   BomOrder bomOrder,
                                   BomMaterialInfoDto materialInfoDto,
                                   List<CraftDemandSaveV3Req> addCraftDemandReqList,
                                   Long bomTransientId) {
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = materialInfoDto.getAccessoriesSpuMap();
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = materialInfoDto.getFabricSkuMap();
        Map<Long, CategoryTreeMapVo> categoryTreeMap = materialInfoDto.getCategoryTreeMap();
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        List<MaterialSnapshotCreateReq> materialSnapshotReqList = new LinkedList<>();

        //新增暂存物料处理: 新增的物料保存到暂存表中;
        List<BomOrderMaterialTransient> newBomMaterialTransients = req.getAddBomMaterials().stream()
                .filter(updateBomMaterialReq -> !Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode()))
                .map(addMaterialReq -> {
                    BomOrderMaterialTransient newMaterialTransient = BomOrderMaterialConverter.buildAddBomOrderMaterialTransientV3(addMaterialReq, bomOrder.getBomId(),null);
                    //设置暂存bomId
                    newMaterialTransient.setBomTransientId(bomTransientId);

                    if (Objects.equals(addMaterialReq.getDemandType(), MaterialDemandTypeEnum.ACCESSORIES.getCode())) {
                        ProductSkuVo productSkuVo = accessoriesSkuMap.get(addMaterialReq.getSkuId());
                        SdpDesignException.notNull(productSkuVo, "辅料sku信息不存在! skuCode:{}", addMaterialReq.getSkuCode());
                        ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(addMaterialReq.getCommodityId());
                        SdpDesignException.notNull(productSpuInfoVo, "辅料SPU信息不存在! skuCode:{}", addMaterialReq.getSkuCode());
                        materialSnapshotReqList.add(BomOrderMaterialConverter.buildAccessoriesMaterialSnapshotV3(newMaterialTransient.getBomMaterialId(), productSkuVo, productSpuInfoVo, categoryTreeMap));
                    }

                    if (Objects.equals(addMaterialReq.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
                        CommoditySkuCollectionRespVo.Sku productSkuVo = fabricSkuMap.get(addMaterialReq.getSpuSkuId());
                        SdpDesignException.notNull(productSkuVo, "面料sku信息不存在! skuCode:{}", addMaterialReq.getSkuCode());
                        materialSnapshotReqList.add(BomOrderMaterialConverter.buildFabricMaterialSnapshotV3(newMaterialTransient.getBomMaterialId(), productSkuVo));
                    }

                    //bom新增二次工艺
                    List<CraftDemandSaveV3Req> addCraftReqList = addMaterialReq.getAddCraftDemandList();
                    if (CollectionUtil.isNotEmpty(addCraftReqList)) {
                        addCraftReqList.forEach(item -> item.setBomMaterialId(newMaterialTransient.getBomMaterialId()));
                        addCraftDemandReqList.addAll(addCraftReqList);
                    }
                    //物料备注
                    if (StringUtils.isNotBlank(addMaterialReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, newMaterialTransient.getBomMaterialId(), addMaterialReq.getRemark()));
                    }

                    //获取最新的价格和价格有效期
                    BomOrderMaterialConverter.buildResetTransientBomOrderMaterialPrice(addMaterialReq.getDemandType(),
                            newMaterialTransient,null, accessoriesSkuMap.get(addMaterialReq.getSkuId()),
                            fabricSkuMap.get(addMaterialReq.getSpuSkuId()));

                    return newMaterialTransient;
                }).collect(Collectors.toList());

        //保存物料快照, 暂存时不需要将新增物料推送给履约
        bomMaterialCommonService.handlerMaterialSnapshotGenerateTransient(materialSnapshotReqList, newBomMaterialTransients, addCraftDemandReqList);

        //新增物料
        if (CollUtil.isNotEmpty(newBomMaterialTransients)) {
            bomOrderMaterialTransientRepository.saveBatch(newBomMaterialTransients);
        }
        //物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private Map<Long, Long> historyMaterialHandle(BomMaterialTransientHandleReq req,
                                                  BomOrder bomOrder,
                                                  Long transientBomId,
                                                  List<CraftDemandSaveV3Req> addCraftDemandReqList,
                                                  Set<Long> delCraftIdSet,
                                                  Long bomTransientId,BomMaterialInfoDto materialInfoDto) {
        if (CollectionUtil.isEmpty(req.getUpdateBomMaterials()) && CollectionUtil.isEmpty(req.getDelBomMaterialIds())) {
            return Collections.emptyMap();
        }

        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = materialInfoDto.getFabricSkuMap();

        List<CraftDemandSaveV3Req> updateMaterialAddCraftDemandList = new LinkedList<>();
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();

        //待提交-首次暂存: 更新面辅料 与删除面辅料处理; 该方法是为了-兼容2.0数据(bom待提交时已经有了物料)
        //如果存在更新的面辅料, 需从原表复制到暂存表
        //如果删除原面辅料, 对应工艺提交给工艺环节处理

        //历史数据的工艺
        List<CraftDemandInfo> craftDemandInfosList = craftDemandInfoRepository.getListByBomIdsAndState(List.of(bomOrder.getBomId()), CraftDemandStateEnum.SUBMIT.getCode());
        Map<Long, List<CraftDemandInfo>> craftDemandMap = craftDemandInfosList.stream().collect(Collectors.groupingBy(CraftDemandInfo::getBomMaterialId));
        //历史数据的面辅料
        Map<Long, BomOrderMaterial> bomMaterialMap = bomOrderMaterialRepository.getListByBomId(bomOrder.getBomId()).stream()
                .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));

        List<BomOrderMaterialTransient> newBomMaterialTransients = new LinkedList<>();
        List<CraftDemandInfo> updateCraftDemands = new LinkedList<>();

        //记录新旧物料id映射Map<旧物料id, 新物料id>
        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>(req.getUpdateBomMaterials().size());

        //历史面料与辅料更新
        req.getUpdateBomMaterials().stream()
                .filter(updateBomMaterialReq -> !Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode()))
                .forEach(updateBomMaterialReq -> {
            //复制原物料到暂存表中(待提交首次暂存, 暂存物料id与原物料id一致,因为提交时不升版本)
            BomOrderMaterial oldBomMaterial = bomMaterialMap.get(updateBomMaterialReq.getBomMaterialId());
            BomOrderMaterialTransient materialTransient = new BomOrderMaterialTransient();
            BeanUtils.copyProperties(oldBomMaterial, materialTransient);
            //暂存物料id与原物料id一致,因为提交时不升版本
            materialTransient.setOriginMaterialId(oldBomMaterial.getBomMaterialId());
            materialTransient.setBomId(bomOrder.getBomId());
            materialTransient.setPartUse(updateBomMaterialReq.getPartUse());
            materialTransient.setCuttingMethod(updateBomMaterialReq.getCuttingMethod());
            materialTransient.setPrototypeMaterialName(updateBomMaterialReq.getPrototypeMaterialName());
            materialTransient.setIsNoCraft(updateBomMaterialReq.getIsNoCraft());
            materialTransient.setIdentifyMaterialId(updateBomMaterialReq.getIdentifyMaterialId());
            materialTransient.setColorMatchMaterialState(updateBomMaterialReq.getColorMatchMaterialState());
            materialTransient.setColorMatchMaterialName(this.getColorMatchName(updateBomMaterialReq));
            materialTransient.setBomTransientId(bomTransientId);

            //获取最新的价格和价格有效期
            BomOrderMaterialConverter.buildResetTransientBomOrderMaterialPrice(updateBomMaterialReq.getDemandType(),
                    materialTransient,null, accessoriesSkuMap.get(updateBomMaterialReq.getSkuId()),
                    fabricSkuMap.get(updateBomMaterialReq.getSpuSkuId()));

            newBomMaterialTransients.add(materialTransient);
            oldNewMaterialIdMap.put(oldBomMaterial.getBomMaterialId(), oldBomMaterial.getBomMaterialId());

            //历史工艺维护最新版单ID
            List<CraftDemandInfo> oldCrafts = craftDemandMap.get(oldBomMaterial.getBomMaterialId());
            if (CollectionUtil.isNotEmpty(oldCrafts)) {
                oldCrafts.forEach(oldCraft -> {
                    //维护最新版单ID
                    CraftDemandInfo updateCraft = new CraftDemandInfo();
                    updateCraft.setCraftDemandId(oldCraft.getCraftDemandId());
                    updateCraft.setPrototypeId(bomOrder.getPrototypeId());
                    updateCraftDemands.add(updateCraft);
                });
            }
            //更新删除工艺
            Set<Long> delCraftDemandIds = updateBomMaterialReq.getDelCraftDemandIds();
            if (CollectionUtil.isNotEmpty(delCraftDemandIds)) {
                delCraftIdSet.addAll(delCraftDemandIds);
            }
            //bom新增二次工艺
            List<CraftDemandSaveV3Req> addCraftReqList = updateBomMaterialReq.getAddCraftDemandList();
            if (CollectionUtil.isNotEmpty(addCraftReqList)) {
                //关联bom物料id
                addCraftReqList.forEach(item -> item.setBomMaterialId(oldBomMaterial.getBomMaterialId()));
                updateMaterialAddCraftDemandList.addAll(addCraftReqList);
            }

            if (StringUtils.isNotBlank(updateBomMaterialReq.getRemark())) {
                designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(transientBomId, materialTransient.getBomMaterialId(), updateBomMaterialReq.getRemark()));
            }
        });

        //删除的物料(兼容历史v2.0数据, 待提交时就已经有物料与工艺了)
        req.getDelBomMaterialIds().forEach(bomMaterialId -> {
            BomOrderMaterial oldBomMaterial = bomMaterialMap.get(bomMaterialId);
            if (Objects.isNull(oldBomMaterial)) {
                return;
            }
            //复制到暂存表, 标记删除
            BomOrderMaterialTransient materialTransient = new BomOrderMaterialTransient();
            BeanUtils.copyProperties(oldBomMaterial, materialTransient);
            //暂存物料id与原物料id一致,因为提交时不升版本
            materialTransient.setOriginMaterialId(oldBomMaterial.getBomMaterialId());
            materialTransient.setMaterialState(BomMaterialStateEnum.CLOSED.getCode());
            materialTransient.setBomTransientId(bomTransientId);
            newBomMaterialTransients.add(materialTransient);
            oldNewMaterialIdMap.put(oldBomMaterial.getBomMaterialId(), oldBomMaterial.getBomMaterialId());

            //删除物料下的工艺(交给工艺环节处理)
            List<CraftDemandInfo> oldCraftDemands = craftDemandMap.get(bomMaterialId);
            if (CollectionUtil.isNotEmpty(oldCraftDemands)) {
                delCraftIdSet.addAll(oldCraftDemands.stream().map(CraftDemandInfo::getCraftDemandId).collect(Collectors.toSet()));
            }
        });

        //历史物料的新增工艺(提交给工艺环节处理)
        if (CollUtil.isNotEmpty(updateMaterialAddCraftDemandList)) {
            addCraftDemandReqList.addAll(updateMaterialAddCraftDemandList);
        }
        //更新历史数据的工艺
        if (CollUtil.isNotEmpty(updateCraftDemands)) {
            craftDemandInfoRepository.updateBatchById(updateCraftDemands);
        }

        //复制物料到暂存表
        if (CollUtil.isNotEmpty(newBomMaterialTransients)) {
            bomOrderMaterialTransientRepository.saveBatch(newBomMaterialTransients);
        }
        //暂存物料备注,提交时逻辑删除暂存状态备注,只记录提交时的备注
        this.addDesignRemark(designRemarksReqList);

        return oldNewMaterialIdMap;
    }

    private String getColorMatchName(BomOrderUpdateV3Req.UpdateBomMaterialReq updateBomMaterialReq) {
        String colorMatchMaterialName = Objects.equals(updateBomMaterialReq.getColorMatchMaterialState(), BomColorMaterialStateEnum.NO_THING.getCode())
                ? null : updateBomMaterialReq.getColorMatchMaterialName();
        colorMatchMaterialName = StringUtils.isBlank(colorMatchMaterialName) ? null : colorMatchMaterialName;
        return colorMatchMaterialName;
    }

    // private void addDesignRemarkTransient(List<DesignRemarksReq> designRemarksReqList) {
    //     //新增暂存状态物料备注
    //     designRemarksReqList.stream()
    //             .filter(designRemark -> StringUtils.isNotBlank(designRemark.getRemark()))
    //             .forEach(item -> {
    //                 //设置暂存状态
    //                 item.setTransientState(Bool.YES.getCode());
    //                 designRemarksService.create(item);
    //             });
    // }

    private void addDesignRemark(List<DesignRemarksReq> designRemarksReqList) {
        //新增物料备注
        designRemarksReqList.stream()
                .filter(designRemark -> StringUtils.isNotBlank(designRemark.getRemark()))
                .forEach(designRemarksService::create);
    }

    private void addQuoteSpecialAccessoriesTransient(BomOrderTransient transientBom,
                                                     BomMaterialTransientHandleReq req,
                                                     BomOrder bomOrder) {
        List<BomOrderUpdateV3Req.AddBomMaterialReq> specialAccessoriesReqList = req.getAddBomMaterials().stream()
                .filter(item -> Objects.equals(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode(), item.getDemandType())
                        && Objects.nonNull(item.getBomMaterialIdCopy()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(specialAccessoriesReqList)){
            return;
        }
        //查询最新的特殊辅料信息
        Set<Long> accessoriesSkuIds = specialAccessoriesReqList.stream().map(BomOrderUpdateV3Req.AddBomMaterialReq::getSkuId).collect(Collectors.toSet());
        List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
        Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus).flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream().collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
        //校验暂存添加的特殊辅料
        this.checkSpecialAccessoriesReqTransient(specialAccessoriesReqList, accessoriesSkuMap, accessoriesSpuMap);

        Set<Long> specialAccessoriesIdList = specialAccessoriesReqList.stream()
                .map(BomOrderUpdateV3Req.AddBomMaterialReq::getBomMaterialIdCopy).collect(Collectors.toSet());
        Map<Long, SpecialAccessories> accessoriesMap = specialAccessoriesRepository.listByIds(specialAccessoriesIdList).stream()
                .collect(Collectors.toMap(SpecialAccessories::getSpecialAccessoriesId, Function.identity(), (k1, k2) -> k1));

        List<DesignRemarksReq> designRemarksReqList = new ArrayList<>(16);
        List<SpecialAccessoriesTransient> specialTransientList = bomMaterialCommonService.resetSpecialAccessoriesTransient(bomOrder, transientBom, specialAccessoriesReqList,
                accessoriesSkuMap, accessoriesSpuMap, accessoriesMap, designRemarksReqList);

        if (CollUtil.isEmpty(specialTransientList)) {
            return;
        }

        //新增引用的特殊辅料
        //查询供应商信息
        List<Long> supplierIdList = specialTransientList.stream().filter(item -> Objects.nonNull(item.getSupplierId()))
                .map(SpecialAccessoriesTransient::getSupplierId).distinct().collect(Collectors.toList());
        Map<Long, CommoditySupplierInfoVo> supplierMap = bomMaterialCommonService.getSupplierInfo(supplierIdList);
        //设置供应商合作关系(履约的开票状态)
        specialTransientList.forEach(item -> {
            if (Objects.nonNull(item.getSupplierId()) && Objects.nonNull(supplierMap.get(item.getSupplierId()))) {
                CommoditySupplierInfoVo supplierInfoVo = supplierMap.get(item.getSupplierId());
                CommoditySupplierExtVo supplierExtVo = supplierInfoVo.getExt();
                if (Objects.nonNull(supplierExtVo)) {
                    item.setInvoiceState(supplierExtVo.getInvoiceState());
                }
            }
        });
        specialAccessoriesTransientRepository.saveBatch(specialTransientList);

        //新增物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void checkSpecialAccessoriesReqTransient(List<BomOrderUpdateV3Req.AddBomMaterialReq> specialAccessoriesReqList, Map<Long, ProductSkuVo> accessoriesSkuMap, Map<Long, ProductSpuInfoVo> accessoriesSpuMap) {
        //暂存, 特辅不用校验上下架
        specialAccessoriesReqList.stream().filter(materialReq -> Objects.equals(materialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())).forEach(materialReq -> {
            ProductSkuVo productSkuVo = accessoriesSkuMap.get(materialReq.getSkuId());
            SdpDesignException.notNull(productSkuVo, "履约辅料sku不存在! {}; skuCode:{}; skuId:{}", materialReq.getPrototypeMaterialName(), materialReq.getSkuCode(), materialReq.getSkuId());
            ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(materialReq.getCommodityId());
            SdpDesignException.notNull(productSpuInfoVo, "履约辅料spu不存在! {}; skuCode:{}; skuId:{}", materialReq.getPrototypeMaterialName(), materialReq.getCommodityCode(), materialReq.getCommodityId());
        });
    }

    private void updateMaterialHandle4ReTransient(BomMaterialTransientHandleReq req,
                                                  Long bomTransientId,
                                                  List<CraftDemandSaveV3Req> addCraftDemandReqList,
                                                  Set<Long> delCraftIdSet,
                                                  BomMaterialInfoDto materialInfoDto) {

        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = materialInfoDto.getFabricSkuMap();

        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        //再次暂存, 面辅料更新处理
        List<BomOrderMaterialTransient> updateBomMaterialTransients = req.getUpdateBomMaterials().stream()
                .filter(updateBomMaterialReq -> !Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode()))
                .map(updateMaterialReq -> {
                    BomOrderMaterialTransient updateMaterialTransient = new BomOrderMaterialTransient();
                    updateMaterialTransient.setBomMaterialId(updateMaterialReq.getBomMaterialId());
                    updateMaterialTransient.setPartUse(updateMaterialReq.getPartUse());
                    updateMaterialTransient.setCuttingMethod(updateMaterialReq.getCuttingMethod());
                    updateMaterialTransient.setPrototypeMaterialName(updateMaterialReq.getPrototypeMaterialName());
                    updateMaterialTransient.setIsNoCraft(updateMaterialReq.getIsNoCraft());
                    updateMaterialTransient.setIdentifyMaterialId(updateMaterialReq.getIdentifyMaterialId());
                    updateMaterialTransient.setColorMatchMaterialState(updateMaterialReq.getColorMatchMaterialState());
                    updateMaterialTransient.setColorMatchMaterialName(this.getColorMatchName(updateMaterialReq));
                    //bom新增二次工艺
                    List<CraftDemandSaveV3Req> addCraftReqList = updateMaterialReq.getAddCraftDemandList();
                    if (CollectionUtil.isNotEmpty(addCraftReqList)) {
                        addCraftReqList.forEach(item -> item.setBomMaterialId(updateMaterialReq.getBomMaterialId()));
                        addCraftDemandReqList.addAll(addCraftReqList);
                    }
                    //删除工艺
                    Set<Long> delCraftDemandIds = updateMaterialReq.getDelCraftDemandIds();
                    if (CollectionUtil.isNotEmpty(delCraftDemandIds)) {
                        delCraftIdSet.addAll(delCraftDemandIds);
                    }
                    //物料备注
                    if (StringUtils.isNotBlank(updateMaterialReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId,
                                updateMaterialReq.getBomMaterialId(), updateMaterialReq.getRemark()));
                    }

                    //获取最新的价格和价格有效期
                    BomOrderMaterialConverter.buildResetTransientBomOrderMaterialPrice(updateMaterialReq.getDemandType(),
                            updateMaterialTransient,null,
                            accessoriesSkuMap.get(updateMaterialReq.getSkuId()),fabricSkuMap.get(updateMaterialReq.getSpuSkuId()));

                    return updateMaterialTransient;
                }).collect(Collectors.toList());

        //特殊辅料更新
        List<SpecialAccessoriesTransient> specialUpdateList = new LinkedList<>();
        req.getUpdateBomMaterials().stream()
                .filter(updateBomMaterialReq -> Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode()))
                .forEach(updateBomMaterialReq -> {

                    SpecialAccessoriesTransient updateSpecialAccessories = new SpecialAccessoriesTransient();
                    updateSpecialAccessories.setSpecialAccessoriesId(updateBomMaterialReq.getBomMaterialId());
                    //获取最新的价格和失效时间
                    BomOrderMaterialConverter.buildResetTransientBomOrderMaterialPrice(updateBomMaterialReq.getDemandType(),
                            null,updateSpecialAccessories,accessoriesSkuMap.get(updateBomMaterialReq.getSkuId()),null);
                    specialUpdateList.add(updateSpecialAccessories);

                    if (StringUtils.isNotBlank(updateBomMaterialReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId,
                                updateBomMaterialReq.getBomMaterialId(), updateBomMaterialReq.getRemark()));
                    }
                });

        //更新暂存面辅料
        if (CollUtil.isNotEmpty(updateBomMaterialTransients)) {
            this.updateMaterialTransient(updateBomMaterialTransients, UserContentHolder.get());
        }

        //更新特辅
        this.updateSpecialTransient(specialUpdateList, UserContentHolder.get());

        //物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void updateSpecialTransient(List<SpecialAccessoriesTransient> specialUpdateList, UserContent userContent) {
        if (CollUtil.isEmpty(specialUpdateList)) {
            return;
        }
        specialUpdateList.forEach(item -> {
            specialAccessoriesTransientRepository.lambdaUpdate()
                    .set(SpecialAccessoriesTransient::getMinPrice, item.getMinPrice())
                    .set(SpecialAccessoriesTransient::getPriceInvalidTime, item.getPriceInvalidTime())
                    .set(SpecialAccessoriesTransient::getSamplePurchasingCycle, item.getSamplePurchasingCycle())
                    .set(SpecialAccessoriesTransient::getBulkPurchasingCycle, item.getBulkPurchasingCycle())
                    .set(SpecialAccessoriesTransient::getRevisedTime, LocalDateTime.now())
                    .set(Objects.nonNull(userContent), SpecialAccessoriesTransient::getReviserId, userContent.getCurrentUserId())
                    .set(Objects.nonNull(userContent), SpecialAccessoriesTransient::getReviserName, userContent.getCurrentUserName())
                    .eq(SpecialAccessoriesTransient::getSpecialAccessoriesId, item.getSpecialAccessoriesId())
                    .update();
        });
    }

    public void updateMaterialTransient(List<BomOrderMaterialTransient> updateBomMaterialTransients, UserContent userContent) {
        if (CollUtil.isEmpty(updateBomMaterialTransients)) {
            return;
        }
        //不能用updateById来更新, 有些属性是可以更新为空的
        updateBomMaterialTransients.forEach(item -> {
            String colorMatchMaterialName = Objects.equals(item.getColorMatchMaterialState(), BomColorMaterialStateEnum.NO_THING.getCode())
                    ? null : item.getColorMatchMaterialName();
            bomOrderMaterialTransientRepository.lambdaUpdate()
                    .set(BomOrderMaterialTransient::getPartUse, item.getPartUse())
                    .set(BomOrderMaterialTransient::getCuttingMethod, item.getCuttingMethod())
                    .set(BomOrderMaterialTransient::getPrototypeMaterialName, item.getPrototypeMaterialName())
                    .set(BomOrderMaterialTransient::getIsNoCraft, item.getIsNoCraft())
                    .set(BomOrderMaterialTransient::getColorMatchMaterialState, item.getColorMatchMaterialState())
                    .set(BomOrderMaterialTransient::getColorMatchMaterialName, StringUtils.isBlank(colorMatchMaterialName) ? null : colorMatchMaterialName)
                    .set(BomOrderMaterialTransient::getMeterPrice, item.getMeterPrice())
                    .set(BomOrderMaterialTransient::getMinPrice, item.getMinPrice())
                    .set(BomOrderMaterialTransient::getPriceInvalidTime, item.getPriceInvalidTime())
                    .set(BomOrderMaterialTransient::getSamplePurchasingCycle, item.getSamplePurchasingCycle())
                    .set(BomOrderMaterialTransient::getBulkPurchasingCycle, item.getBulkPurchasingCycle())
                    .set(BomOrderMaterialTransient::getRevisedTime, LocalDateTime.now())
                    .set(Objects.nonNull(userContent), BomOrderMaterialTransient::getReviserId, userContent.getCurrentUserId())
                    .set(Objects.nonNull(userContent), BomOrderMaterialTransient::getReviserName, userContent.getCurrentUserName())
                    .set(BomOrderMaterialTransient::getIdentifyMaterialId, item.getIdentifyMaterialId())
                    .eq(BomOrderMaterialTransient::getBomMaterialId, item.getBomMaterialId())
                    .update();
        });
    }

    private void updateFirstTransient4Submitted(BomMaterialTransientHandleReq req,
                                                BomMaterialInfoDto materialInfoDto,
                                                BomOrder bomOrder,
                                                BomOrderTransient transientBom,
                                                List<CraftDemandSaveV3Req> addCraftDemandReqList,
                                                Set<Long> delCraftIdSet,
                                                Map<Long, Long> oldNewMaterialIdMap) {
        //已提交的首次暂存,更新物料处理: 根据更新物料id,查询原表物料, 然后更新数据, 保存的暂存表
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();

        List<BomOrderUpdateV3Req.UpdateBomMaterialReq> updateBomMaterialList = req.getUpdateBomMaterials();
        if (CollUtil.isEmpty(updateBomMaterialList)) {
            return;
        }

        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = materialInfoDto.getFabricSkuMap();

        //当前bom的原有面辅料
        Map<Long, BomOrderMaterial> oldBomMaterialMap = bomOrderMaterialRepository.getListByBomId(bomOrder.getBomId()).stream()
                .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
        SdpDesignException.isTrue(CollUtil.isNotEmpty(oldBomMaterialMap), "已提交的首次暂存, 原物料信息查询失败! bomId:{}", bomOrder.getBomId());
        //当前bom的原有特辅
        Map<Long, SpecialAccessories> oldBomSpecialMap = specialAccessoriesRepository.getListByBomId(bomOrder.getBomId()).stream()
                .collect(Collectors.toMap(SpecialAccessories::getSpecialAccessoriesId, Function.identity(), (k1, k2) -> k1));

        List<BomOrderMaterialTransient> newBomMaterialTransients = new LinkedList<>();
        List<SpecialAccessoriesTransient> newSpecialTransients = new LinkedList<>();
        Long bomTransientId = transientBom.getBomTransientId();

        //已提交的首次暂存, 面辅料更新处理: 找到对应原物料, 更新信息后添加到暂存表中
        updateBomMaterialList.stream()
                .filter(updateBomMaterialReq -> !Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode()))
                .forEach(updateMaterialReq -> {
                    Long oldMaterialId = updateMaterialReq.getBomMaterialId();
                    if (CollUtil.isEmpty(oldBomMaterialMap) || Objects.isNull(oldBomMaterialMap.get(oldMaterialId))) {
                        return ;
                    }
                    //找到对应原物料, 更新信息后添加到暂存表中
                    BomOrderMaterial oldMaterial = oldBomMaterialMap.get(oldMaterialId);
                    Long newTransientMaterialId = IdPool.getId();
                    BomOrderMaterialTransient addMaterialTransient = new BomOrderMaterialTransient();
                    BeanUtils.copyProperties(oldMaterial, addMaterialTransient);
                    addMaterialTransient.setBomMaterialId(newTransientMaterialId);
                    addMaterialTransient.setOriginMaterialId(oldMaterialId);

                    addMaterialTransient.setBomTransientId(bomTransientId);
                    addMaterialTransient.setPartUse(updateMaterialReq.getPartUse());
                    addMaterialTransient.setCuttingMethod(updateMaterialReq.getCuttingMethod());
                    addMaterialTransient.setPrototypeMaterialName(updateMaterialReq.getPrototypeMaterialName());
                    addMaterialTransient.setIsNoCraft(updateMaterialReq.getIsNoCraft());
                    addMaterialTransient.setIdentifyMaterialId(updateMaterialReq.getIdentifyMaterialId());

                    addMaterialTransient.setColorMatchMaterialState(updateMaterialReq.getColorMatchMaterialState());
                    addMaterialTransient.setColorMatchMaterialName(this.getColorMatchName(updateMaterialReq));


                    //获取最新的价格和价格有效期
                    BomOrderMaterialConverter.buildResetTransientBomOrderMaterialPrice(updateMaterialReq.getDemandType(),
                            addMaterialTransient,null, accessoriesSkuMap.get(updateMaterialReq.getSkuId()),
                            fabricSkuMap.get(updateMaterialReq.getSpuSkuId()));

                    newBomMaterialTransients.add(addMaterialTransient);
                    oldNewMaterialIdMap.put(oldMaterialId, newTransientMaterialId);

                    //bom新增暂存二次工艺
                    List<CraftDemandSaveV3Req> addCraftReqList = updateMaterialReq.getAddCraftDemandList();
                    if (CollectionUtil.isNotEmpty(addCraftReqList)) {
                        //关联暂存表物料id
                        addCraftReqList.forEach(item -> item.setBomMaterialId(newTransientMaterialId));
                        addCraftDemandReqList.addAll(addCraftReqList);
                    }
                    //删除工艺(原有工艺删除, 工艺处理环节要将工艺对应的物料id更新为新物料id
                    Set<Long> delCraftDemandIds = updateMaterialReq.getDelCraftDemandIds();
                    if (CollectionUtil.isNotEmpty(delCraftDemandIds)) {
                        delCraftIdSet.addAll(delCraftDemandIds);
                    }
                    //物料备注
                    if (StringUtils.isNotBlank(updateMaterialReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, newTransientMaterialId, updateMaterialReq.getRemark()));
                    }
                });

        //已提交的首次暂存, 特辅更新处理:找到对应原物料,更新信息后添加到暂存表中; 特殊辅料备注更新, 物料id使用暂存物料的id
        req.getUpdateBomMaterials().stream()
                .filter(updateBomMaterialReq -> Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode()))
                .forEach(specialReq -> {
                    Long oldSpecialId = specialReq.getBomMaterialId();
                    if (CollUtil.isEmpty(oldBomSpecialMap) || Objects.isNull(oldBomSpecialMap.get(oldSpecialId))) {
                        return ;
                    }
                    SpecialAccessories oldSpecial = oldBomSpecialMap.get(oldSpecialId);

                    //找到对应原物料, 复制信息添加到暂存表中
                    SpecialAccessoriesTransient addSpecialTransient = new SpecialAccessoriesTransient();
                    Long newSpecialId = IdPool.getId();
                    BeanUtils.copyProperties(oldSpecial, addSpecialTransient);
                    addSpecialTransient.setBomTransientId(bomTransientId);
                    addSpecialTransient.setSpecialAccessoriesId(newSpecialId);
                    addSpecialTransient.setOriginSpecialId(oldSpecialId);

                    //获取最新的价格和价格有效期
                    BomOrderMaterialConverter.buildResetTransientBomOrderMaterialPrice(specialReq.getDemandType(),
                            null,addSpecialTransient, accessoriesSkuMap.get(specialReq.getSkuId()), null);

                    newSpecialTransients.add(addSpecialTransient);
                    oldNewMaterialIdMap.put(oldSpecialId, newSpecialId);

                    //物料备注
                    if (StringUtils.isNotBlank(specialReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, newSpecialId, specialReq.getRemark()));
                    }
                });

        //复制面辅料, 特辅信息到暂存表
        if (CollUtil.isNotEmpty(newBomMaterialTransients)) {
            bomOrderMaterialTransientRepository.saveBatch(newBomMaterialTransients);
        }
        if (CollUtil.isNotEmpty(newSpecialTransients)) {
            specialAccessoriesTransientRepository.saveBatch(newSpecialTransients);
        }

        //物料备注(已提交后的暂存, 物料备注不需要暂存)
        this.addDesignRemark(designRemarksReqList);
    }

}
