package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.util.Json;
import com.yibuyun.scm.common.dto.accessories.response.CategoryTreeMapVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSkuVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import com.yibuyun.scm.common.dto.fabric.response.CommoditySkuCollectionRespVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierExtVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierInfoVo;
import com.zjkj.scf.bundle.common.dto.demand.dto.request.MaterialDetailReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.BomOrderMaterialConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.remote.DemandRemoteHelper;
import tech.tiangong.sdp.design.remote.ProductRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.BomMaterialCommonService;
import tech.tiangong.sdp.design.service.BomMaterialSubmitHandleService;
import tech.tiangong.sdp.design.service.DesignRemarksService;
import tech.tiangong.sdp.design.vo.dto.bom.BomMaterialInfoDto;
import tech.tiangong.sdp.design.vo.req.bom.BomCraftSubmitHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialSubmitHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderUpdateV3Req;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandSaveV3Req;
import tech.tiangong.sdp.design.vo.req.material.MaterialSnapshotCreateReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 * bom提交 物料处理服务
 * <AUTHOR>
 * @date 2022/11/17 10:31
 */


@Slf4j
@Service
@RequiredArgsConstructor
public class BomMaterialSubmitHandleServiceImpl implements BomMaterialSubmitHandleService {

    private final SpecialAccessoriesRepository specialAccessoriesRepository;
    private final SpecialAccessoriesTransientRepository specialAccessoriesTransientRepository;
    private final ProductRemoteHelper productRemoteHelper;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final PrototypeRepository prototypeRepository;
    private final DemandRemoteHelper demandRemoteHelper;
    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final CraftDemandInfoTransientRepository craftDemandInfoTransientRepository;
    private final DesignRemarksService designRemarksService;
    private final DesignRemarksRepository designRemarksRepository;

    private final BomMaterialCommonService bomMaterialCommonService;
    private final BomOrderTransientRepository bomOrderTransientRepository;

    private final BomOrderMaterialTransientRepository materialTransientRepository;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftSubmitHandleReq firstSubmitNoTransient(BomMaterialSubmitHandleReq req) {
        BomOrder bomOrder = req.getBomOrder();
        //1, 从履约查询物料信息并校验
        BomMaterialInfoDto materialInfoDto = bomMaterialCommonService.queryAndCheckMaterialInfo(req, bomOrder.getBomId());

        //提交新增的工艺
        List<CraftDemandSaveV3Req> addCraftDemandReqList = new LinkedList<>();
        //历史数据可能有删除的工艺(2.0时工艺维护中拆板中, 拆板提交物料确认后,, 待提交的bom就有物料与工艺了)
        Set<Long> delCraftIdSet = new HashSet<>(64);

        //3, 物料新增处理; //新增的物料保存到原表中,不升版本;
        this.handleAddMaterial(req, bomOrder, materialInfoDto, addCraftDemandReqList);

        //4, 面辅料更新-兼容2.0数据(bom待提交时已经有了物料);
        this.handlerBomWaitUpdateMaterial(req, bomOrder, addCraftDemandReqList, delCraftIdSet,materialInfoDto);

        //5, 原有特辅更新,更新原特辅
        this.handleSpecialAccessoriesUpdateReq(req, bomOrder,materialInfoDto);

        //6, 引用新增特殊辅料处理
        this.addQuoteSpecialAccessories(bomOrder, req);

        //7, 如果使用引用的特辅, 删除原有特辅
        this.handleQuoteSpecial(req);

        //8, 将物料快照id设置到工艺入参中, 传给工艺处理
        return BomCraftSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .craftSourceTypeEnum(CraftSourceTypeEnum.CHOOSE)
                .addCraftDemandList(addCraftDemandReqList)
                //历史数据首次提交也会有删除工艺
                .delCraftDemandIds(delCraftIdSet)
                .oldNewMaterialIdMap(Collections.emptyMap())
                .oldTransientCraftMap(Collections.emptyMap())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftSubmitHandleReq reSubmitNoTransient(BomMaterialSubmitHandleReq req) {
        BomOrder oldBomOrder = req.getBomOrder();
        BomOrder newBomOrder = req.getNewBomOrder();
        //1, 从履约查询物料信息并校验
        BomMaterialInfoDto materialInfoDto = bomMaterialCommonService.queryAndCheckMaterialInfo(req, oldBomOrder.getBomId());

        //收集新增/删除的工艺
        List<CraftDemandSaveV3Req> addCraftDemandReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);
        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();

        //2, 物料新增处理; 新增的物料保存到新bom中,收集新增的工艺
        this.handleAddMaterial(req, newBomOrder, materialInfoDto, addCraftDemandReqList);

        //3, 面辅料更新:复制一份到新bom中; 收集新增,删除的工艺
        this.updateMaterial4ReSubmit(req, oldBomOrder, newBomOrder, addCraftDemandReqList, delCraftIdSet, oldNewMaterialIdMap,materialInfoDto);

        //4, 删除物料不需要维护到新版本bom中, 但对应的工艺需要做删除处理, 提交给工艺环节
        this.handleDelMaterial4ReSubmit(req, oldBomOrder, delCraftIdSet);

        //5, 特辅更新:复制一份到新bom中
        this.handleSpecialUpdate4ReSubmit(req, oldBomOrder, newBomOrder,materialInfoDto);

        //6, 引用新增特殊辅料, 新增到新bom中
        this.addQuoteSpecialAccessories(newBomOrder, req);

        //7, 将物料快照id设置到工艺入参中, 传给工艺处理
        return BomCraftSubmitHandleReq.builder()
                .bomOrder(oldBomOrder)
                .newBomOrderId(newBomOrder.getBomId())
                .craftSourceTypeEnum(CraftSourceTypeEnum.CHOOSE)
                .addCraftDemandList(addCraftDemandReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldTransientCraftMap(Collections.emptyMap())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftSubmitHandleReq firstSubmitWithTransient(BomMaterialSubmitHandleReq req) {
        BomOrder bomOrder = req.getBomOrder();
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomOrder.getBomId());

        //物料已经被再次暂存过了, 复制一份暂存信息到原表中

        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();
        //1, 将暂存表中新增与更新的物料拷贝到新bom的原物料表中(bom不升版本)
        this.copyMaterialFromTransient(bomOrder, transientBom, oldNewMaterialIdMap, req, true);

        //2, 将特辅暂存表中新增与更新的拷贝到新bom的原物料表中(bom不升版本)
        this.copySpecialFromTransient(bomOrder, transientBom, true);


        //收集新增/删除的工艺 (待提交-有暂存 的提交, 不会有删除的工艺)
        List<CraftDemandSaveV3Req> addCraftDemandReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);
        Map<Long, CraftDemandInfoTransient> oldTransientCraftMap = new HashMap<>();

        this.handleCraftInfo(transientBom, addCraftDemandReqList, delCraftIdSet, oldTransientCraftMap);

        //3, 已经重新封装了工艺信息, 模拟为无暂存的首次提交参数, 调用工艺首次提交方法处理
        return BomCraftSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .craftSourceTypeEnum(CraftSourceTypeEnum.CHOOSE)
                .addCraftDemandList(addCraftDemandReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldTransientCraftMap(oldTransientCraftMap)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftSubmitHandleReq reSubmitWithTransient(BomMaterialSubmitHandleReq req) {
        BomOrder bomOrder = req.getBomOrder();
        BomOrder newBomOrder = req.getNewBomOrder();
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomOrder.getBomId());

        //物料已经被再次暂存过了, 复制一份暂存信息到原表中

        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();

        //1, 将暂存表中新增与更新的物料拷贝到新bom的原物料表中(升版本)
        this.copyMaterialFromTransient(newBomOrder, transientBom, oldNewMaterialIdMap, req, false);

        //2, 将特辅暂存表中新增与更新的拷贝到新bom的原物料表中(升版本)
        this.copySpecialFromTransient(newBomOrder, transientBom, false);

        //收集新增/删除的工艺 (待提交-有暂存 的提交, 不会有删除的工艺)
        List<CraftDemandSaveV3Req> addCraftDemandReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);
        Map<Long, CraftDemandInfoTransient> oldTransientCraftMap = new HashMap<>();

        this.handleCraftInfo(transientBom, addCraftDemandReqList, delCraftIdSet, oldTransientCraftMap);

        //3, 已经重新封装了工艺信息, 模拟为无暂存的再次提交参数, 调用工艺首次提交方法处理
        return BomCraftSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .newBomOrderId(newBomOrder.getBomId())
                .craftSourceTypeEnum(CraftSourceTypeEnum.CHOOSE)
                .addCraftDemandList(addCraftDemandReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldTransientCraftMap(oldTransientCraftMap)
                .build();
    }



    private void handleCraftInfo(BomOrderTransient transientBom,
                                 List<CraftDemandSaveV3Req> addCraftDemandReqList,
                                 Set<Long> delCraftIdSet,
                                 Map<Long, CraftDemandInfoTransient> oldTransientCraftMap
                                 ) {
        //从工艺暂存表中收集新增 删除的工艺信息, 模拟成无暂存,首次提交的入参, 提交给工艺环节处理

        //因为工艺也已经再次暂存了, 从工艺暂存表获取最终的结果
        List<CraftDemandInfoTransient> craftTransientList = craftDemandInfoTransientRepository.listByBomTransientId(transientBom.getBomTransientId());
        if (CollUtil.isEmpty(craftTransientList)) {
            return;
        }
        List<CraftDemandInfoTransient> addCraftTransient = new LinkedList<>();
        craftTransientList.forEach(transientCraft -> {
            //新增的工艺
            if (Objects.isNull(transientCraft.getOriginCraftDemandId())) {
                addCraftTransient.add(transientCraft);
                return;
            }
            //删除的工艺
            if (Objects.nonNull(transientCraft.getOriginCraftDemandId())) {
                if (Objects.equals(transientCraft.getState(), CraftDemandStateEnum.CLOSED.getCode())) {
                    //收集删除的原工艺id
                    delCraftIdSet.add(transientCraft.getOriginCraftDemandId());
                }
                //收集原有工艺id与暂存工艺的关系
                oldTransientCraftMap.put(transientCraft.getOriginCraftDemandId(), transientCraft);
            }
        });
        if (CollUtil.isEmpty(addCraftTransient)) {
            return;
        }
        List<Long> materialTransientIdList = addCraftTransient.stream().map(CraftDemandInfoTransient::getBomMaterialId).collect(Collectors.toList());
        Map<Long, BomOrderMaterialTransient> materialTransientMap = materialTransientRepository.listByIds(materialTransientIdList).stream()
                .collect(Collectors.toMap(BomOrderMaterialTransient::getBomMaterialId, Function.identity(), (k1, k2) -> k1));

        //新增工艺转换为CraftDemandSaveV3Req
        addCraftTransient.forEach(item -> {
            CraftDemandSaveV3Req craftReq = new CraftDemandSaveV3Req();
            BeanUtils.copyProperties(item, craftReq);
            craftReq.setCraftDemandTransientId(item.getCraftDemandId());
            BomOrderMaterialTransient materialTransient = materialTransientMap.get(item.getBomMaterialId());
            //升版本时, 原物料id与暂存物料id对应
            craftReq.setBomMaterialId(materialTransient.getBomMaterialId());
            craftReq.setMaterialSnapshotId(materialTransient.getMaterialSnapshotId());
            addCraftDemandReqList.add(craftReq);
        });
    }

    private void copyMaterialFromTransient(BomOrder bomOrder,
                                           BomOrderTransient transientBom,
                                           Map<Long, Long> oldNewMaterialIdMap,
                                           BomMaterialSubmitHandleReq req,
                                           Boolean firstSubmit) {
        //1, 先从暂存表中获取最终增删改的面辅料(要过滤掉需求的物料)
        List<BomOrderMaterialTransient> finalMaterialTransientList = materialTransientRepository.listByBomTransientId(transientBom.getBomTransientId())
                .stream().filter(item -> Objects.isNull(item.getBomMaterialDemandId())).collect(Collectors.toList());

        //有场景是只提交找料需求,没有物料, 这时暂存表中是没有选料的物料的
        if (CollUtil.isEmpty(finalMaterialTransientList)) {
            log.info("=== 有暂存提交时,无选料物料-copyMaterialFromTransient ======");
            return;
        }
        // PlmDesignException.notEmpty(finalMaterialTransientList, "bom暂存物料信息不存在! bomId:{}", bomOrder.getBomId());

        List<BomOrderMaterialTransient> addTransientMaterialList = new LinkedList<>();
        List<BomOrderMaterialTransient> updateTransientMaterialList = new LinkedList<>();
        List<BomOrderMaterialTransient> delTransientMaterialList = new LinkedList<>();
        finalMaterialTransientList.forEach(item -> {
            //暂存新增的物料
            if (Objects.isNull(item.getOriginMaterialId())) {
                addTransientMaterialList.add(item);
                return;
            }
            //删除的物料
            if (Objects.equals(item.getMaterialState(), BomMaterialStateEnum.CLOSED.getCode())) {
                delTransientMaterialList.add(item);
                return;
            }
            //更新的物料
            updateTransientMaterialList.add(item);
        });

        //2, 从暂存表复制到bom原表中
        List<BomOrderMaterial> addMaterialList = new LinkedList<>();
        List<BomOrderMaterial> updateMaterialList = new LinkedList<>();
        //新增物料复制
        addTransientMaterialList.forEach(item -> {
            //物料id使用暂存表中的物料id
            BomOrderMaterial material = new BomOrderMaterial();
            BeanUtils.copyProperties(item, material);
            material.setBomId(bomOrder.getBomId());
            addMaterialList.add(material);
        });

        //如果是首次提交, 设计2.0有历史数据,更新的物料直接更新到原表中; 再次提交,新增到新bom中
        if (firstSubmit) {
            //更新物料
            updateTransientMaterialList.forEach(item -> {
                //物料id使用暂存表中的物料id
                BomOrderMaterial updateMaterial = new BomOrderMaterial();
                BeanUtils.copyProperties(item, updateMaterial);
                updateMaterialList.add(updateMaterial);
                //维护新旧物料id映射
                oldNewMaterialIdMap.put(item.getOriginMaterialId(), item.getBomMaterialId());
            });
        }else {
            //更新物料复制
            updateTransientMaterialList.forEach(item -> {
                //物料id使用暂存表中的物料id
                BomOrderMaterial material = new BomOrderMaterial();
                BeanUtils.copyProperties(item, material);
                material.setBomId(bomOrder.getBomId());
                addMaterialList.add(material);
                //维护新旧物料id映射
                oldNewMaterialIdMap.put(item.getOriginMaterialId(), item.getBomMaterialId());
            });
        }

        //更新旧bom物料的最新采购周期(暂存表中已维护了最新的采购周期信息)
        List<BomOrderMaterial> oldMaterialUpdateList = updateTransientMaterialList.stream()
                .filter(item -> Objects.nonNull(item.getOriginMaterialId()))
                .map(item -> BomOrderMaterial.builder()
                        .bomMaterialId(item.getOriginMaterialId())
                        .samplePurchasingCycle(item.getSamplePurchasingCycle())
                        .bulkPurchasingCycle(item.getBulkPurchasingCycle())
                        .build())
                .collect(Collectors.toList());
        this.updateBomMaterialCycle(oldMaterialUpdateList);

        //新增物料推送 物料快照给履约
        // bomMaterialCommonService.createMaterialToSupplyChain(bomOrder, addTransientMaterialList);

        if (CollUtil.isNotEmpty(addMaterialList)) {
            bomOrderMaterialRepository.saveBatch(addMaterialList);
        }
        if (CollUtil.isNotEmpty(updateMaterialList)) {
            bomOrderMaterialRepository.updateBatchById(updateMaterialList);
        }

        //物料备注处理
        this.handleMaterialRemark(bomOrder, req, addMaterialList);
    }

    private void handleMaterialRemark(BomOrder bomOrder,
                                      BomMaterialSubmitHandleReq req,
                                      List<BomOrderMaterial> addMaterialList) {
        //物料备注处理: 由于先调用了再次暂存,备注中的bzId使用的是暂存bomId,要重新生成一份
        Map<String, String> nameRemarkMap = new HashMap<>();
        if (CollUtil.isNotEmpty(req.getAddBomMaterials())) {
            Map<String, String> addNameRemarkMap = req.getAddBomMaterials().stream()
                    .collect(Collectors.toMap(BomOrderUpdateV3Req.AddBomMaterialReq::getPrototypeMaterialName, BomOrderUpdateV3Req.AddBomMaterialReq::getRemark, (k1, k2) -> k1));
            nameRemarkMap.putAll(addNameRemarkMap);
        }
        if (CollUtil.isNotEmpty(req.getUpdateBomMaterials())) {
            Map<String, String> updateNameRemarkMap = req.getUpdateBomMaterials().stream()
                    .collect(Collectors.toMap(BomOrderUpdateV3Req.UpdateBomMaterialReq::getPrototypeMaterialName, BomOrderUpdateV3Req.UpdateBomMaterialReq::getRemark, (k1, k2) -> k1));
            nameRemarkMap.putAll(updateNameRemarkMap);
        }

        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        if (CollUtil.isNotEmpty(addMaterialList)) {
            addMaterialList.forEach(item -> {
                String remark = nameRemarkMap.get(item.getPrototypeMaterialName());
                if (StringUtils.isNotBlank(remark)) {
                    designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomOrder.getBomId(), item.getBomMaterialId(), remark));
                }
            });
        }
        this.addDesignRemark(designRemarksReqList);
    }

    private void copySpecialFromTransient(BomOrder bomOrder, BomOrderTransient transientBom, Boolean firstSubmit) {
        //1, 先从暂存表中获取最终增删改的特辅
        List<SpecialAccessoriesTransient> finalSpecialTransientList = specialAccessoriesTransientRepository.listByBomTransientId(transientBom.getBomTransientId());
        if (CollUtil.isEmpty(finalSpecialTransientList)) {
            return;
        }

        List<SpecialAccessoriesTransient> addTransientSpecialList = new LinkedList<>();
        List<SpecialAccessoriesTransient> updateTransientSpecialList = new LinkedList<>();
        List<SpecialAccessoriesTransient> delTransientSpecialList = new LinkedList<>();
        finalSpecialTransientList.forEach(item -> {
            //暂存新增的物料
            if (Objects.isNull(item.getOriginSpecialId())) {
                addTransientSpecialList.add(item);
                return;
            }
            //删除的原物料
            if (Objects.equals(item.getState(), SpecialAccessoriesStateEnum.CLOSED.getCode())) {
                delTransientSpecialList.add(item);
                return;
            }
            //更新的物料
            updateTransientSpecialList.add(item);
        });

        //2, 从暂存表复制到bom原表中
        List<SpecialAccessories> addSpecialList = new LinkedList<>();
        List<SpecialAccessories> updateSpecialList = new LinkedList<>();
        List<SpecialAccessories> closeSpecialList = new LinkedList<>();
        //新增特辅复制
        addTransientSpecialList.forEach(item -> {
            //物料id使用暂存表中的物料id
            SpecialAccessories specialAccessories = new SpecialAccessories();
            BeanUtils.copyProperties(item, specialAccessories);
            specialAccessories.setBomId(bomOrder.getBomId());
            specialAccessories.setPrototypeId(bomOrder.getPrototypeId());
            specialAccessories.setDesignCode(bomOrder.getDesignCode());
            addSpecialList.add(specialAccessories);
        });

        //如果是首次提交, 更新的特辅直接更新到原表中; 再次提交,新增到新bom中
        if (firstSubmit) {
            //原特辅更新
            updateTransientSpecialList.forEach(item -> {
                //物料id使用暂存表中的物料id
                SpecialAccessories updateSpecial = new SpecialAccessories();
                BeanUtils.copyProperties(item, updateSpecial);
                updateSpecial.setSpecialAccessoriesId(item.getOriginSpecialId());
                updateSpecialList.add(updateSpecial);
            });

            //暂存时关闭的原特辅(只考虑首次提交的场景,再次提交生成新bom,新bom中没有原特辅)
            delTransientSpecialList.forEach(item -> {
                //物料id使用暂存表中的物料id
                SpecialAccessories closeSpecial = new SpecialAccessories();
                closeSpecial.setSpecialAccessoriesId(item.getOriginSpecialId());
                closeSpecial.setState(SpecialAccessoriesStateEnum.CLOSED.getCode());
                closeSpecialList.add(closeSpecial);
            });

        }else {
            //更新物料复制
            updateTransientSpecialList.forEach(item -> {
                //物料id使用暂存表中的物料id
                SpecialAccessories specialAccessories = new SpecialAccessories();
                BeanUtils.copyProperties(item, specialAccessories);
                specialAccessories.setBomId(bomOrder.getBomId());
                addSpecialList.add(specialAccessories);
            });
        }

        //更新旧bom物料的最新采购周期(暂存表中已维护了最新的采购周期信息)
        List<SpecialAccessories> oldMaterialUpdateList = updateTransientSpecialList.stream()
                .filter(item -> Objects.nonNull(item.getOriginSpecialId()))
                .map(item -> SpecialAccessories.builder()
                        .specialAccessoriesId(item.getOriginSpecialId())
                        .samplePurchasingCycle(item.getSamplePurchasingCycle())
                        .bulkPurchasingCycle(item.getBulkPurchasingCycle())
                        .build())
                .collect(Collectors.toList());
        this.updateSpecialCycle(oldMaterialUpdateList);


        if (CollUtil.isNotEmpty(addSpecialList)) {
            specialAccessoriesRepository.saveBatch(addSpecialList);
        }
        if (CollUtil.isNotEmpty(updateSpecialList)) {
            this.updateSpecial(updateSpecialList, UserContentHolder.get());
        }
        if (CollUtil.isNotEmpty(closeSpecialList)) {
            specialAccessoriesRepository.updateBatchById(closeSpecialList);
        }

        //物料备注处理: 由于先调用了再次暂存,备注中的bzId使用的是暂存bomId,要重新生成一份
        List<Long> specialTransientIdList = finalSpecialTransientList.stream()
                .map(SpecialAccessoriesTransient::getSpecialAccessoriesId).collect(Collectors.toList());
        List<DesignRemarks> remarksList = designRemarksRepository.getListByBizChildIds(specialTransientIdList);
        if (CollUtil.isEmpty(remarksList)) {
            return;
        }
        List<DesignRemarks> addRemarkList = remarksList.stream().map(item -> {
            DesignRemarks remark = new DesignRemarks();
            BeanUtils.copyProperties(item, remark);
            remark.setDesignRemarksId(IdPool.getId());
            remark.setBizId(bomOrder.getBomId());
            remark.setBizVersionNum(bomOrder.getVersionNum());
            //bizChildId还是暂存表的主键, 只是更换bomId
            remark.setBizChildId(item.getBizChildId());
            remark.setTransientState(Bool.NO.getCode());
            return remark;
        }).collect(Collectors.toList());

        designRemarksRepository.saveBatch(addRemarkList);
    }

    /**
     * 无暂存-再次提交, 删除物料处理: 由于生成新bom, 删除的物料不需要带到新bom中,
     * 但删除物料对应的工艺需要带到新bom中, 打版流程需要查询;要提交给工艺处理环节
     */
    private void handleDelMaterial4ReSubmit(BomMaterialSubmitHandleReq req, BomOrder oldBomOrder, Set<Long> delCraftIdSet) {
        if (CollUtil.isEmpty(req.getDelBomMaterialIds())) {
            return;
        }
        //旧bom物料
        Map<Long, BomOrderMaterial> oldBomMaterialMap = bomOrderMaterialRepository.getListByBomId(oldBomOrder.getBomId()).stream()
                .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
        if (CollUtil.isEmpty(oldBomMaterialMap)) {
            return;
        }
        //工艺信息
        List<CraftDemandInfo> craftDemandInfosList = craftDemandInfoRepository.getListByBomIdsAndState(List.of(oldBomOrder.getBomId()), CraftDemandStateEnum.SUBMIT.getCode());
        Map<Long, List<CraftDemandInfo>> craftDemandMap = craftDemandInfosList.stream().collect(Collectors.groupingBy(CraftDemandInfo::getBomMaterialId));

        req.getDelBomMaterialIds().forEach(bomMaterialId -> {
            //删除的物料可能是特辅, 不在bomMaterial表中, 特辅没有工艺
            BomOrderMaterial oldMaterial = oldBomMaterialMap.get(bomMaterialId);
            if (Objects.nonNull(oldMaterial) && CollUtil.isNotEmpty(craftDemandMap)) {
                List<CraftDemandInfo> oldCraftList = craftDemandMap.get(oldMaterial.getBomMaterialId());
                if (CollUtil.isNotEmpty(oldCraftList)) {
                    delCraftIdSet.addAll(oldCraftList.stream().map(CraftDemandInfo::getCraftDemandId).collect(Collectors.toSet()));
                }
            }
        });
    }

    private void handleQuoteSpecial(BomMaterialSubmitHandleReq req) {
        if (CollUtil.isNotEmpty(req.getDelBomMaterialIds())) {
            List<SpecialAccessories> closedSpecialList = specialAccessoriesRepository.listByIds(req.getDelBomMaterialIds()).stream()
                    .filter(item -> Objects.equals(Bool.NO.getCode(), item.getTransientState())
                            && Objects.equals(SpecialAccessoriesStateEnum.SUBMIT.getCode(), item.getState()))
                    .map(special -> SpecialAccessories.builder().specialAccessoriesId(special.getSpecialAccessoriesId())
                            .state(SpecialAccessoriesStateEnum.CLOSED.getCode())
                            .build())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(closedSpecialList)) {
                log.info("=== bom首次提交,关闭原有特辅: closedSpecialList:{} === ", Json.serialize(closedSpecialList));
                specialAccessoriesRepository.updateBatchById(closedSpecialList);
            }
        }
    }

    private void handleSpecialAccessoriesUpdateReq(BomMaterialSubmitHandleReq req, BomOrder bomOrder,BomMaterialInfoDto materialInfoDto) {
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        List<SpecialAccessories> updateSpecialAccessoriesList = req.getUpdateBomMaterials().stream().filter(updateBomMaterialReq -> Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())).map(updateBomMaterialReq -> {
            SpecialAccessories updateSpecialAccessories = new SpecialAccessories();
            updateSpecialAccessories.setSpecialAccessoriesId(updateBomMaterialReq.getBomMaterialId());
            updateSpecialAccessories.setPartUse(updateBomMaterialReq.getPartUse());
            // updateSpecialAccessories.setCuttingMethod(updateBomMaterialReq.getCuttingMethod());
            if (StringUtils.isNotBlank(updateBomMaterialReq.getRemark())) {
                designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomOrder.getBomId(), updateSpecialAccessories.getSpecialAccessoriesId(), updateBomMaterialReq.getRemark()));
            }

            //获取最新的价格和失效时间
            BomOrderMaterialConverter.buildResetBomOrderMaterialPrice(updateBomMaterialReq.getDemandType(),null,updateSpecialAccessories,accessoriesSkuMap.get(updateBomMaterialReq.getSkuId()),null);


            return updateSpecialAccessories;
        }).collect(Collectors.toList());

        //更新特辅
        this.updateSpecial(updateSpecialAccessoriesList, UserContentHolder.get());

        //物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void updateSpecial(List<SpecialAccessories> specialUpdateList, UserContent userContent) {
        if (CollUtil.isEmpty(specialUpdateList)) {
            return;
        }
        specialUpdateList.forEach(item -> {
            specialAccessoriesRepository.lambdaUpdate()
                    .set(StringUtils.isNotBlank(item.getPartUse()), SpecialAccessories::getPartUse, item.getPartUse())
                    .set(SpecialAccessories::getMinPrice, item.getMinPrice())
                    .set(SpecialAccessories::getPriceInvalidTime, item.getPriceInvalidTime())
                    .set(SpecialAccessories::getSamplePurchasingCycle, item.getSamplePurchasingCycle())
                    .set(SpecialAccessories::getBulkPurchasingCycle, item.getBulkPurchasingCycle())
                    .set(SpecialAccessories::getRevisedTime, LocalDateTime.now())
                    .set(Objects.nonNull(userContent), SpecialAccessories::getReviserId, userContent.getCurrentUserId())
                    .set(Objects.nonNull(userContent), SpecialAccessories::getReviserName, userContent.getCurrentUserName())
                    .eq(SpecialAccessories::getSpecialAccessoriesId, item.getSpecialAccessoriesId())
                    .update();
        });
    }

    private void handleSpecialUpdate4ReSubmit(BomMaterialSubmitHandleReq req, BomOrder oldBomOrder, BomOrder newBomOrder, BomMaterialInfoDto materialInfoDto) {
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<Long, SpecialAccessories> specialAccessoriesMap = specialAccessoriesRepository.getListByBomId(oldBomOrder.getBomId()).stream()
                .collect(Collectors.toMap(SpecialAccessories::getSpecialAccessoriesId, Function.identity(), (k1, k2) -> k1));

        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        List<SpecialAccessories> oldSpecialUpdateList = new LinkedList<>();
        List<SpecialAccessories> newSpecialAccessoriesList = req.getUpdateBomMaterials().stream().filter(updateBomMaterialReq -> Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())).map(updateBomMaterialReq -> {
            SpecialAccessories oldSpecialAccessories = specialAccessoriesMap.get(updateBomMaterialReq.getBomMaterialId());
            SpecialAccessories newSpecialAccessories = new SpecialAccessories();
            BeanUtils.copyProperties(oldSpecialAccessories, newSpecialAccessories);
            newSpecialAccessories.setSpecialAccessoriesId(IdPool.getId());
            newSpecialAccessories.setBomId(newBomOrder.getBomId());
            newSpecialAccessories.setPartUse(updateBomMaterialReq.getPartUse());
            newSpecialAccessories.setCuttingMethod(updateBomMaterialReq.getCuttingMethod());
            //获取最新的价格和失效时间
            BomOrderMaterialConverter.buildResetBomOrderMaterialPrice(updateBomMaterialReq.getDemandType(),null,newSpecialAccessories,accessoriesSkuMap.get(updateBomMaterialReq.getSkuId()),null);

            //旧bom更新的物料也要更新最新的采购周期信息
            oldSpecialAccessories.setSamplePurchasingCycle(newSpecialAccessories.getSamplePurchasingCycle());
            oldSpecialAccessories.setBulkPurchasingCycle(newSpecialAccessories.getBulkPurchasingCycle());
            oldSpecialUpdateList.add(oldSpecialAccessories);

            if (StringUtils.isNotBlank(updateBomMaterialReq.getRemark())) {
                designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(newBomOrder.getBomId(), newSpecialAccessories.getSpecialAccessoriesId(), updateBomMaterialReq.getRemark()));
            }
            return newSpecialAccessories;
        }).collect(Collectors.toList());

        //更新特辅新增到新bom中
        specialAccessoriesRepository.saveBatch(newSpecialAccessoriesList);

        //更新旧bom的物料采购周期信息
        this.updateSpecialCycle(oldSpecialUpdateList);

        //物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void updateSpecialCycle(List<SpecialAccessories> oldSpecialUpdateList) {
        if (CollUtil.isEmpty(oldSpecialUpdateList)) {
            return;
        }
        UserContent userContent = UserContentHolder.get();
        oldSpecialUpdateList.forEach(item -> {
            specialAccessoriesRepository.lambdaUpdate()
                    .set(SpecialAccessories::getSamplePurchasingCycle, item.getSamplePurchasingCycle())
                    .set(SpecialAccessories::getBulkPurchasingCycle, item.getBulkPurchasingCycle())
                    .set(SpecialAccessories::getRevisedTime, LocalDateTime.now())
                    .set(Objects.nonNull(userContent), SpecialAccessories::getReviserId, userContent.getCurrentUserId())
                    .set(Objects.nonNull(userContent), SpecialAccessories::getReviserName, userContent.getCurrentUserName())
                    .eq(SpecialAccessories::getSpecialAccessoriesId, item.getSpecialAccessoriesId())
                    .update();
        });
    }

    private void handleAddMaterial(BomMaterialSubmitHandleReq req,
                                                     BomOrder bomOrder,
                                                     BomMaterialInfoDto materialInfoDto,
                                                     List<CraftDemandSaveV3Req> addCraftDemandReqList) {
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = materialInfoDto.getAccessoriesSpuMap();
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = materialInfoDto.getFabricSkuMap();
        Map<Long, CategoryTreeMapVo> categoryTreeMap = materialInfoDto.getCategoryTreeMap();
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        List<MaterialSnapshotCreateReq> materialSnapshotReqList = new LinkedList<>();

        List<BomOrderMaterial> newBomMaterials = req.getAddBomMaterials().stream()
                .filter(updateBomMaterialReq -> !Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode()))
                .map(addMaterialReq -> {
                    BomOrderMaterial newBomMaterial = BomOrderMaterialConverter.buildAddBomOrderMaterialV3(addMaterialReq, bomOrder.getBomId());

                    if (Objects.equals(addMaterialReq.getDemandType(), MaterialDemandTypeEnum.ACCESSORIES.getCode())) {
                        ProductSkuVo productSkuVo = accessoriesSkuMap.get(addMaterialReq.getSkuId());
                        SdpDesignException.notNull(productSkuVo, "辅料sku信息不存在! skuCode:{}", addMaterialReq.getSkuCode());
                        ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(addMaterialReq.getCommodityId());
                        SdpDesignException.notNull(productSpuInfoVo, "辅料SPU信息不存在! skuCode:{}", addMaterialReq.getSkuCode());
                        materialSnapshotReqList.add(BomOrderMaterialConverter.buildAccessoriesMaterialSnapshotV3(newBomMaterial.getBomMaterialId(), productSkuVo, productSpuInfoVo, categoryTreeMap));
                    }

                    if (Objects.equals(addMaterialReq.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())) {
                        CommoditySkuCollectionRespVo.Sku productSkuVo = fabricSkuMap.get(addMaterialReq.getSpuSkuId());
                        SdpDesignException.notNull(productSkuVo, "面料sku信息不存在! skuCode:{}", addMaterialReq.getSkuCode());
                        materialSnapshotReqList.add(BomOrderMaterialConverter.buildFabricMaterialSnapshotV3(newBomMaterial.getBomMaterialId(), productSkuVo));
                    }

                    //bom新增二次工艺
                    List<CraftDemandSaveV3Req> addCraftReqList = addMaterialReq.getAddCraftDemandList();
                    if (CollectionUtil.isNotEmpty(addCraftReqList)) {
                        addCraftReqList.forEach(item -> item.setBomMaterialId(newBomMaterial.getBomMaterialId()));
                        addCraftDemandReqList.addAll(addCraftReqList);
                    }
                    //物料备注
                    if (StringUtils.isNotBlank(addMaterialReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomOrder.getBomId(), newBomMaterial.getBomMaterialId(), addMaterialReq.getRemark()));
                    }

                    //获取最新的价格和失效时间
                    BomOrderMaterialConverter.buildResetBomOrderMaterialPrice(addMaterialReq.getDemandType(),newBomMaterial,null,accessoriesSkuMap.get(addMaterialReq.getSkuId()),fabricSkuMap.get(addMaterialReq.getSpuSkuId()));

                    return newBomMaterial;
                }).collect(Collectors.toList());

        //保存物料快照
        List<MaterialDetailReq> supplyChainMaterialReqs = new LinkedList<>();
        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        bomMaterialCommonService.handlerMaterialSnapshotGenerate(materialSnapshotReqList, newBomMaterials, addCraftDemandReqList, supplyChainMaterialReqs, prototype);
        //bom提交同步至商品服务
        // demandRemoteHelper.createMaterialToSupplyChain(supplyChainMaterialReqs);

        //新增物料
        if (CollUtil.isNotEmpty(newBomMaterials)) {
            bomOrderMaterialRepository.saveBatch(newBomMaterials);
        }
        //物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void addDesignRemark(List<DesignRemarksReq> designRemarksReqList) {
        //新增物料备注
        designRemarksReqList.stream()
                .filter(designRemark -> StringUtils.isNotBlank(designRemark.getRemark()))
                .forEach(designRemarksService::create);
    }

    private void updateMaterial4ReSubmit(BomMaterialSubmitHandleReq req,
                                         BomOrder oldBomOrder,
                                         BomOrder newBomOrder,
                                         List<CraftDemandSaveV3Req> addCraftDemandReqList,
                                         Set<Long> delCraftIdSet,
                                         Map<Long, Long> oldNewMaterialIdMap,
                                         BomMaterialInfoDto materialInfoDto) {
        if (CollectionUtil.isEmpty(req.getUpdateBomMaterials())) {
            return;
        }
        List<BomOrderMaterial> newBomOrderMaterials = new LinkedList<>();
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = materialInfoDto.getAccessoriesSpuMap();
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = materialInfoDto.getFabricSkuMap();
        Map<Long, CategoryTreeMapVo> categoryTreeMap = materialInfoDto.getCategoryTreeMap();

        //查询旧bom物料
        Map<Long, BomOrderMaterial> bomMaterialMap = bomOrderMaterialRepository.getListByBomId(oldBomOrder.getBomId()).stream()
                .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
        // materialInfoDto.setOldBomMaterialMap(bomMaterialMap);

        List<BomOrderMaterial> oldMaterialUpdateList = new LinkedList<>();

        //面料与辅料更新
        req.getUpdateBomMaterials().stream().filter(updateBomMaterialReq -> !Objects.equals(updateBomMaterialReq.getDemandType(),
                MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())).forEach(updateBomMaterialReq -> {
            BomOrderMaterial oldBomMaterial = bomMaterialMap.get(updateBomMaterialReq.getBomMaterialId());

            long newBomMaterialId = IdPool.getId();
            BomOrderMaterial newBomMaterial = new BomOrderMaterial();
            BeanUtils.copyProperties(oldBomMaterial, newBomMaterial);
            newBomMaterial.setBomMaterialId(newBomMaterialId);
            newBomMaterial.setBomId(newBomOrder.getBomId());
            newBomMaterial.setPartUse(updateBomMaterialReq.getPartUse());
            newBomMaterial.setCuttingMethod(updateBomMaterialReq.getCuttingMethod());
            newBomMaterial.setColorMatch(updateBomMaterialReq.getColorMatch());
            newBomMaterial.setPrototypeMaterialName(updateBomMaterialReq.getPrototypeMaterialName());
            newBomMaterial.setIsNoCraft(updateBomMaterialReq.getIsNoCraft());
            //newBomMaterial.setIdentifyMaterialId(updateBomMaterialReq.getIdentifyMaterialId());
            newBomMaterial.setColorMatchMaterialState(updateBomMaterialReq.getColorMatchMaterialState());
            newBomMaterial.setColorMatchMaterialName(this.getColorMatchName(updateBomMaterialReq));
            //获取最新的价格和失效时间
            BomOrderMaterialConverter.buildResetBomOrderMaterialPrice(updateBomMaterialReq.getDemandType(),
                    newBomMaterial,null, accessoriesSkuMap.get(updateBomMaterialReq.getSkuId()),
                    fabricSkuMap.get(updateBomMaterialReq.getSpuSkuId()));

            //旧bom更新的物料也维护最新的采购周期信息
            oldBomMaterial.setSamplePurchasingCycle(newBomMaterial.getSamplePurchasingCycle());
            oldBomMaterial.setBulkPurchasingCycle(newBomMaterial.getBulkPurchasingCycle());
            oldMaterialUpdateList.add(oldBomMaterial);

            newBomOrderMaterials.add(newBomMaterial);
            oldNewMaterialIdMap.put(oldBomMaterial.getBomMaterialId(), newBomMaterialId);

            //删除的工艺
            Set<Long> delCraftDemandIds = updateBomMaterialReq.getDelCraftDemandIds();
            if (CollectionUtil.isNotEmpty(delCraftDemandIds)) {
                delCraftIdSet.addAll(delCraftDemandIds);
            }
            //新增的二次工艺
            List<CraftDemandSaveV3Req> addCraftReqList = updateBomMaterialReq.getAddCraftDemandList();
            if (CollectionUtil.isNotEmpty(addCraftReqList)) {
                //关联bom物料id
                addCraftReqList.forEach(item -> item.setBomMaterialId(newBomMaterialId));
                addCraftDemandReqList.addAll(addCraftReqList);
            }

            if (StringUtils.isNotBlank(updateBomMaterialReq.getRemark())) {
                designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(newBomOrder.getBomId(), newBomMaterialId, updateBomMaterialReq.getRemark()));
            }
        });

        //更新物料新增到新bom中
        bomOrderMaterialRepository.saveBatch(newBomOrderMaterials);

        //更新旧bom的最新采购周期
        this.updateBomMaterialCycle(oldMaterialUpdateList);

        //物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void updateBomMaterialCycle(List<BomOrderMaterial> updateMaterialList) {
        if (CollUtil.isEmpty(updateMaterialList)) {
            return;
        }
        UserContent userContent = UserContentHolder.get();
        updateMaterialList.forEach(updateMaterial -> {
            bomOrderMaterialRepository.lambdaUpdate()
                    .set(BomOrderMaterial::getSamplePurchasingCycle, updateMaterial.getSamplePurchasingCycle())
                    .set(BomOrderMaterial::getBulkPurchasingCycle, updateMaterial.getBulkPurchasingCycle())
                    .set(BomOrderMaterial::getRevisedTime, LocalDateTime.now())
                    .set(Objects.nonNull(userContent), BomOrderMaterial::getReviserId, userContent.getCurrentUserId())
                    .set(Objects.nonNull(userContent), BomOrderMaterial::getReviserName, userContent.getCurrentUserName())
                    .eq(BomOrderMaterial::getBomMaterialId, updateMaterial.getBomMaterialId())
                    .update();
        });
    }

    private String getColorMatchName(BomOrderUpdateV3Req.UpdateBomMaterialReq updateBomMaterialReq) {
        String colorMatchMaterialName = Objects.equals(updateBomMaterialReq.getColorMatchMaterialState(), BomColorMaterialStateEnum.NO_THING.getCode())
                ? null : updateBomMaterialReq.getColorMatchMaterialName();
        colorMatchMaterialName = StringUtils.isBlank(colorMatchMaterialName) ? null : colorMatchMaterialName;
        return colorMatchMaterialName;
    }

    private void handlerBomWaitUpdateMaterial(BomMaterialSubmitHandleReq req,
                                              BomOrder bomOrder,
                                              List<CraftDemandSaveV3Req> addCraftDemandReqList,
                                              Set<Long> delCraftIdSet,
                                              BomMaterialInfoDto materialInfoDto) {
        if (CollectionUtil.isEmpty(req.getUpdateBomMaterials()) && CollectionUtil.isEmpty(req.getDelBomMaterialIds())) {
            return;
        }

        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = materialInfoDto.getAccessoriesSpuMap();
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = materialInfoDto.getFabricSkuMap();
        Map<Long, CategoryTreeMapVo> categoryTreeMap = materialInfoDto.getCategoryTreeMap();

        List<CraftDemandSaveV3Req> updateMaterialAddCraftDemandList = new LinkedList<>();
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();

        // 兼容设计2.0历史数据(2.0bom待提交状态就存在了面辅料信息)

        List<CraftDemandInfo> craftDemandInfosList = craftDemandInfoRepository.getListByBomIdsAndState(List.of(bomOrder.getBomId()), CraftDemandStateEnum.SUBMIT.getCode());
        Map<Long, List<CraftDemandInfo>> craftDemandMap = craftDemandInfosList.stream().collect(Collectors.groupingBy(CraftDemandInfo::getBomMaterialId));
        Map<Long, BomOrderMaterial> bomMaterialMap = bomOrderMaterialRepository.getListByBomId(bomOrder.getBomId()).stream()
                .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));

        List<BomOrderMaterial> updateBomMaterials = new LinkedList<>();
        List<CraftDemandInfo> updateCraftDemands = new LinkedList<>();

        //历史面料与辅料更新
        req.getUpdateBomMaterials().stream()
                .filter(updateBomMaterialReq -> !Objects.equals(updateBomMaterialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode()))
                .forEach(updateBomMaterialReq -> {
            BomOrderMaterial oldBomMaterial = bomMaterialMap.get(updateBomMaterialReq.getBomMaterialId());
            BomOrderMaterial updateBomMaterial = new BomOrderMaterial();
            updateBomMaterial.setBomMaterialId(updateBomMaterialReq.getBomMaterialId());
            updateBomMaterial.setBomId(bomOrder.getBomId());
            updateBomMaterial.setPartUse(updateBomMaterialReq.getPartUse());
            updateBomMaterial.setCuttingMethod(updateBomMaterialReq.getCuttingMethod());
            updateBomMaterial.setColorMatch(updateBomMaterialReq.getColorMatch());
            updateBomMaterial.setPrototypeMaterialName(updateBomMaterialReq.getPrototypeMaterialName());
            updateBomMaterial.setIsNoCraft(updateBomMaterialReq.getIsNoCraft());
            //updateBomMaterial.setIdentifyMaterialId(updateBomMaterialReq.getIdentifyMaterialId());
            updateBomMaterial.setColorMatchMaterialState(updateBomMaterialReq.getColorMatchMaterialState());
            updateBomMaterial.setColorMatchMaterialName(this.getColorMatchName(updateBomMaterialReq));

            //历史工艺维护最新版单ID
            List<CraftDemandInfo> oldCrafts = craftDemandMap.get(updateBomMaterial.getBomMaterialId());
            if (CollectionUtil.isNotEmpty(oldCrafts)) {
                oldCrafts.forEach(oldCraft -> {
                    CraftDemandInfo updateCraft = new CraftDemandInfo();
                    updateCraft.setCraftDemandId(oldCraft.getCraftDemandId());
                    updateCraft.setPrototypeId(bomOrder.getPrototypeId());
                    updateCraftDemands.add(updateCraft);
                });
            }
            //更新删除工艺
            Set<Long> delCraftDemandIds = updateBomMaterialReq.getDelCraftDemandIds();
            if (CollectionUtil.isNotEmpty(delCraftDemandIds)) {
                delCraftIdSet.addAll(delCraftDemandIds);
                delCraftDemandIds.forEach(craftId -> {
                    CraftDemandInfo updateCraft = new CraftDemandInfo();
                    updateCraft.setCraftDemandId(craftId);
                    updateCraft.setState(CraftDemandStateEnum.CLOSED.getCode());
                    updateCraft.setPrototypeId(bomOrder.getPrototypeId());
                    updateCraftDemands.add(updateCraft);
                });
            }
            //bom新增二次工艺
            List<CraftDemandSaveV3Req> addCraftReqList = updateBomMaterialReq.getAddCraftDemandList();
            if (CollectionUtil.isNotEmpty(addCraftReqList)) {
                //关联bom物料id
                addCraftReqList.forEach(item -> item.setBomMaterialId(updateBomMaterial.getBomMaterialId()));
                updateMaterialAddCraftDemandList.addAll(addCraftReqList);
            }

            //获取最新的价格和失效时间
            BomOrderMaterialConverter.buildResetBomOrderMaterialPrice(updateBomMaterialReq.getDemandType(),
                    updateBomMaterial,null,accessoriesSkuMap.get(updateBomMaterialReq.getSkuId()),
                    fabricSkuMap.get(updateBomMaterialReq.getSpuSkuId()));

            updateBomMaterials.add(updateBomMaterial);
            if (StringUtils.isNotBlank(updateBomMaterialReq.getRemark())) {
                designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomOrder.getBomId(),
                        updateBomMaterial.getBomMaterialId(), updateBomMaterialReq.getRemark()));
            }
        });

        //删除的物料(兼容历史v2.0数据, 待提交时就已经有物料与工艺了)
        req.getDelBomMaterialIds().forEach(bomMaterialId -> {
            BomOrderMaterial updateBomMaterial = new BomOrderMaterial();
            updateBomMaterial.setBomMaterialId(bomMaterialId);
            updateBomMaterial.setIsDeleted(Bool.YES.getCode());
            bomOrderMaterialRepository.removeById(bomMaterialId);
            //删除物料下的工艺
            List<CraftDemandInfo> oldCraftDemands = craftDemandMap.get(bomMaterialId);
            if (CollectionUtil.isNotEmpty(oldCraftDemands)) {
                delCraftIdSet.addAll(oldCraftDemands.stream().map(CraftDemandInfo::getCraftDemandId).collect(Collectors.toSet()));
            }
        });

        //历史物料的新增工艺
        if (CollUtil.isNotEmpty(updateMaterialAddCraftDemandList)) {
            addCraftDemandReqList.addAll(updateMaterialAddCraftDemandList);
        }
        //更新历史数据的工艺
        if (CollUtil.isNotEmpty(updateCraftDemands)) {
            craftDemandInfoRepository.updateBatchById(updateCraftDemands);
        }
        //更新物料
        if (CollUtil.isNotEmpty(updateBomMaterials)) {
            this.updateMaterial(updateBomMaterials, UserContentHolder.get());
        }
        //物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    public void updateMaterial(List<BomOrderMaterial> updateBomMaterials, UserContent userContent) {
        if (CollUtil.isEmpty(updateBomMaterials)) {
            return;
        }
        //不能用updateById来更新, 有些属性是可以更新为空的
        updateBomMaterials.forEach(item -> {
            String colorMatchMaterialName = Objects.equals(item.getColorMatchMaterialState(), BomColorMaterialStateEnum.NO_THING.getCode())
                    ? null : item.getColorMatchMaterialName();
            bomOrderMaterialRepository.lambdaUpdate()
                    .set(BomOrderMaterial::getBomId, item.getBomId())
                    .set(BomOrderMaterial::getPartUse, item.getPartUse())
                    .set(BomOrderMaterial::getCuttingMethod, item.getCuttingMethod())
                    .set(BomOrderMaterial::getPrototypeMaterialName, item.getPrototypeMaterialName())
                    .set(BomOrderMaterial::getIsNoCraft, item.getIsNoCraft())
                    .set(BomOrderMaterial::getColorMatchMaterialState, item.getColorMatchMaterialState())
                    .set(BomOrderMaterial::getColorMatchMaterialName, StringUtils.isBlank(colorMatchMaterialName) ? null : colorMatchMaterialName)
                    .set(BomOrderMaterial::getMeterPrice, item.getMeterPrice())
                    .set(BomOrderMaterial::getMinPrice, item.getMinPrice())
                    .set(BomOrderMaterial::getPriceInvalidTime, item.getPriceInvalidTime())
                    .set(BomOrderMaterial::getSamplePurchasingCycle, item.getSamplePurchasingCycle())
                    .set(BomOrderMaterial::getBulkPurchasingCycle, item.getBulkPurchasingCycle())
                    .set(BomOrderMaterial::getRevisedTime, LocalDateTime.now())
                    .set(Objects.nonNull(userContent), BomOrderMaterial::getReviserId, userContent.getCurrentUserId())
                    .set(Objects.nonNull(userContent), BomOrderMaterial::getReviserName, userContent.getCurrentUserName())
                    //.set(BomOrderMaterial::getIdentifyMaterialId, item.getIdentifyMaterialId())
                    .eq(BomOrderMaterial::getBomMaterialId, item.getBomMaterialId())
                    .update();
        });
    }

    private void addQuoteSpecialAccessories(BomOrder bomOrder, BomMaterialSubmitHandleReq req) {
        List<BomOrderUpdateV3Req.AddBomMaterialReq> specialAccessoriesReqList = req.getAddBomMaterials().stream()
                .filter(item -> Objects.equals(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode(), item.getDemandType())
                        && Objects.nonNull(item.getBomMaterialIdCopy()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(specialAccessoriesReqList)){
            return;
        }
        //查询最新的特殊辅料信息
        Set<Long> accessoriesSkuIds = specialAccessoriesReqList.stream().map(BomOrderUpdateV3Req.AddBomMaterialReq::getSkuId).collect(Collectors.toSet());
        List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
        Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus).flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream().collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
        //校验特殊辅料
        this.checkSpecialAccessoriesReq(specialAccessoriesReqList, accessoriesSkuMap, accessoriesSpuMap);

        Set<Long> specialAccessoriesIdList = specialAccessoriesReqList.stream()
                .map(BomOrderUpdateV3Req.AddBomMaterialReq::getBomMaterialIdCopy).collect(Collectors.toSet());
        Map<Long, SpecialAccessories> accessoriesMap = specialAccessoriesRepository.listByIds(specialAccessoriesIdList).stream()
                .collect(Collectors.toMap(SpecialAccessories::getSpecialAccessoriesId, Function.identity(), (k1, k2) -> k1));

        List<DesignRemarksReq> designRemarksReqList = new ArrayList<>(16);
        List<SpecialAccessories> newSaList = bomMaterialCommonService.resetSpecialAccessories(bomOrder, specialAccessoriesReqList,
                accessoriesSkuMap, accessoriesSpuMap, accessoriesMap, designRemarksReqList);

        //新增引用的特殊辅料
        if (CollUtil.isNotEmpty(newSaList)) {
            //查询供应商信息
            List<Long> supplierIdList = newSaList.stream().filter(item -> Objects.nonNull(item.getSupplierId()))
                    .map(SpecialAccessories::getSupplierId).distinct().collect(Collectors.toList());
            Map<Long, CommoditySupplierInfoVo> supplierMap = bomMaterialCommonService.getSupplierInfo(supplierIdList);
            //设置供应商合作关系(履约的开票状态)
            newSaList.forEach(item -> {
                if (Objects.nonNull(item.getSupplierId()) && Objects.nonNull(supplierMap.get(item.getSupplierId()))) {
                    CommoditySupplierInfoVo supplierInfoVo = supplierMap.get(item.getSupplierId());
                    CommoditySupplierExtVo supplierExtVo = supplierInfoVo.getExt();
                    if (Objects.nonNull(supplierExtVo)) {
                        item.setInvoiceState(supplierExtVo.getInvoiceState());
                    }
                }
            });
            specialAccessoriesRepository.saveBatch(newSaList);
        }

        //物料备注
        this.addDesignRemark(designRemarksReqList);

    }

    private void checkSpecialAccessoriesReq(List<BomOrderUpdateV3Req.AddBomMaterialReq> specialAccessoriesReqList, Map<Long, ProductSkuVo> accessoriesSkuMap, Map<Long, ProductSpuInfoVo> accessoriesSpuMap) {
        //校验新添加辅料sku必有属性
        specialAccessoriesReqList.stream().filter(materialReq -> Objects.equals(materialReq.getDemandType(), MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode())).forEach(materialReq -> {
            ProductSkuVo productSkuVo = accessoriesSkuMap.get(materialReq.getSkuId());
            SdpDesignException.notNull(productSkuVo, "履约辅料sku不存在! skuCode:{}; skuId:{}", materialReq.getSkuCode(), materialReq.getSkuId());
            ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(materialReq.getCommodityId());
            SdpDesignException.notNull(productSpuInfoVo, "履约辅料spu不存在! skuCode:{}; skuId:{}", materialReq.getCommodityCode(), materialReq.getCommodityId());
            // PlmDesignException.isTrue(Objects.equals(productSpuInfoVo.getOnShelf(), Bool.YES.getCode()), "{} 的辅料商品 {} 已下架,请重新添加", materialReq.getPrototypeMaterialName(), productSkuVo.getSkuCode());
            // PlmDesignException.isTrue(Objects.equals(productSpuInfoVo.getEnabled(), Bool.YES.getCode()), "{} 的辅料商品 {} 未启用,请重新添加", materialReq.getPrototypeMaterialName(), productSkuVo.getSkuCode());
            SdpDesignException.isTrue(Objects.nonNull(productSkuVo.getMinPrice()) && StringUtils.isNotBlank(productSkuVo.getMinUnit()), "{} 辅料商品 {} 缺少价格最小价格与单位,请联系物料管理员完善后再添加", materialReq.getPrototypeMaterialName(), productSkuVo.getSkuCode());
        });
    }

}
