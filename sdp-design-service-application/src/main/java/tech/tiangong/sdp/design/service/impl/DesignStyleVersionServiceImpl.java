package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.design.entity.DesignStyleVersion;
import tech.tiangong.sdp.design.repository.DesignStyleVersionRepository;
import tech.tiangong.sdp.design.service.DesignStyleVersionService;
import tech.tiangong.sdp.design.vo.base.prototype.OpsObject;
import tech.tiangong.sdp.design.vo.query.style.DesignStyleVersionQuery;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleVersionVo;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SPU_版本表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DesignStyleVersionServiceImpl implements DesignStyleVersionService {
    private final DesignStyleVersionRepository designStyleVersionRepository;

    @Override
    public PageRespVo<DesignStyleVersionVo> page(DesignStyleVersionQuery query) {
        IPage<DesignStyleVersion> page = designStyleVersionRepository.findPage(query);

        List<DesignStyleVersion> records = page.getRecords();
        List<DesignStyleVersionVo> voList = List.of();
        if (CollUtil.isNotEmpty(records)) {
            voList = records.stream().map(this::entity2Vo).collect(Collectors.toList());
        }

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), voList);
    }

    @Override
    public DesignStyleVersionVo getById(Long id) {
        DesignStyleVersion entity = designStyleVersionRepository.getById(id);
        return this.entity2Vo(entity);
    }

    private DesignStyleVersionVo entity2Vo(DesignStyleVersion entity) {
        DesignStyleVersionVo vo = new DesignStyleVersionVo();
        if (Objects.isNull(entity)) {
            return vo;
        }
        BeanUtils.copyProperties(entity, vo);
        this.resetValues(entity, vo);
        return vo;
    }

    private void resetValues(DesignStyleVersion entity, DesignStyleVersionVo vo) {
        //季节
        String styleSeason = entity.getStyleSeason();
        if (StringUtils.isNotBlank(styleSeason)) {
            vo.setStyleSeasonList(JSON.parseArray(styleSeason, OpsObject.class));
        }

    }

}
