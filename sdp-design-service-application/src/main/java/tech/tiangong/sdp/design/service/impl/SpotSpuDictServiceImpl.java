package tech.tiangong.sdp.design.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.service.SpotSpuDictService;
import tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo;
import tech.tiangong.sdp.qy.converter.DictTreeConverter;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 货通商品字典服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpotSpuDictServiceImpl implements SpotSpuDictService {
    private final static long CACHE_EXPIRE_MINUTES = 5L;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    
    /**
     * 使用 Guava Cache，5分钟过期
     */
    private final Cache<String, Map<String, String>> dictCache = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build();
            
    /**
     * DictVo对象缓存，用于智能匹配
     */
    private final Cache<String, Map<String, DictVo>> dictVoCache = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build();
            
    /**
     * 颜色信息映射缓存，5分钟过期
     */
    private final Cache<String, Map<String, ColorInfoVo>> colorInfoCache = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
            .maximumSize(10)
            .build();

    @Override
    public String getCodeByName(String dictCode, String name) {
        return getCodeByName(dictCode, name, null);
    }

    @Override
    public String getCodeByName(String dictCode, String name, String defaultCode) {
        if (name == null || name.trim().isEmpty()) {
            return defaultCode;
        }
        
        try {
            Map<String, String> nameToCodeMap = dictCache.get(dictCode, () -> loadDictMap(dictCode));
            String code = nameToCodeMap.get(name.trim());
            return code != null ? code : defaultCode;
        } catch (Exception e) {
            log.warn("获取字典 {} 中名称 '{}' 的编码失败: {}", dictCode, name, e.getMessage());
            return defaultCode;
        }
    }

    @Override
    public Map<String, String> getNameToCodeMap(String dictCode) {
        try {
            Map<String, String> map = dictCache.get(dictCode, () -> loadDictMap(dictCode));
            return new HashMap<>(map);
        } catch (Exception e) {
            log.warn("获取字典 {} 失败: {}", dictCode, e.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, String> getCodeToNameMap(String dictCode) {
        try {
            Map<String, String> nameToCodeMap = dictCache.get(dictCode, () -> loadDictMap(dictCode));
            // 构建反向映射
            return nameToCodeMap.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v1));
        } catch (Exception e) {
            log.warn("获取字典 {} 反向映射失败: {}", dictCode, e.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Map<String, String>> getBatchNameToCodeMaps(String... dictCodes) {
        Map<String, Map<String, String>> result = new HashMap<>();
        for (String dictCode : dictCodes) {
            result.put(dictCode, getNameToCodeMap(dictCode));
        }
        return result;
    }

    @Override
    public void refreshCache() {
        dictCache.invalidateAll();
        dictVoCache.invalidateAll();
        colorInfoCache.invalidateAll();
        log.info("字典缓存已清空，下次查询时将重新加载");
    }

    @Override
    public DictVo getDictVoByName(String dictCode, String name) {
        return getDictVoByName(dictCode, name, null);
    }

    @Override
    public DictVo getDictVoByName(String dictCode, String name, DictVo defaultVo) {
        if (name == null || name.trim().isEmpty()) {
            return defaultVo;
        }
        
        try {
            Map<String, DictVo> nameToVoMap = dictVoCache.get(dictCode, () -> loadDictVoMap(dictCode));
            
            // 1. 先进行精确匹配
            DictVo exactMatch = nameToVoMap.get(name.trim());
            if (exactMatch != null) {
                log.debug("字典 {} 中名称 '{}' 精确匹配成功", dictCode, name);
                return exactMatch;
            }
            
            // 2. 精确匹配失败，进行包含匹配
            for (Map.Entry<String, DictVo> entry : nameToVoMap.entrySet()) {
                String dictName = entry.getKey();
                // 双向包含匹配：要么dictName包含查询名称，要么查询名称包含dictName
                if (dictName.contains(name.trim()) || name.trim().contains(dictName)) {
                    log.debug("字典 {} 中名称 '{}' 包含匹配成功，匹配到：'{}'", dictCode, name, dictName);
                    return entry.getValue();
                }
            }
            
            log.debug("字典 {} 中未找到名称 '{}' 的匹配项", dictCode, name);
            return defaultVo;
            
        } catch (Exception e) {
            log.warn("获取字典 {} 中名称 '{}' 的DictVo失败: {}", dictCode, name, e.getMessage());
            return defaultVo;
        }
    }

    /**
     * 加载单个字典的映射关系
     */
    private Map<String, String> loadDictMap(String dictCode) {
        try {
            List<DictVo> dictList = dictValueRemoteHelper.listByDictCodes(Collections.singletonList(dictCode));
            if (dictList.isEmpty()) {
                log.warn("loadDictMap 字典 {} 未找到数据", dictCode);
                return new HashMap<>();
            }
            
            DictVo dictVo = dictList.getFirst();
            Map<String, String> nameToCodeMap = DictTreeConverter.convertToMap(dictVo, "");
            
            if (!nameToCodeMap.isEmpty()) {
                log.debug("成功加载字典 {}，包含 {} 个映射", dictCode, nameToCodeMap.size());
                return nameToCodeMap;
            } else {
                log.warn("字典 {} 转换后为空", dictCode);
                return new HashMap<>();
            }
        } catch (Exception e) {
            log.error("加载字典 {} 失败", dictCode, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 加载单个字典的DictVo对象映射关系
     */
    private Map<String, DictVo> loadDictVoMap(String dictCode) {
        try {
            List<DictVo> dictList = dictValueRemoteHelper.listByDictCodes(Collections.singletonList(dictCode));
            if (dictList.isEmpty()) {
                log.warn("loadDictVoMap 字典 {} 未找到数据", dictCode);
                return new HashMap<>();
            }
            
            DictVo dictVo = dictList.getFirst();
            Map<String, DictVo> nameToVoMap = flattenDictVoTree(dictVo);
            
            if (!nameToVoMap.isEmpty()) {
                log.debug("成功加载字典DictVo {} ，包含 {} 个映射", dictCode, nameToVoMap.size());
                return nameToVoMap;
            } else {
                log.warn("字典DictVo {} 转换后为空", dictCode);
                return new HashMap<>();
            }
        } catch (Exception e) {
            log.error("加载字典DictVo {} 失败", dictCode, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 扁平化DictVo树结构，构建名称到DictVo的映射
     */
    private Map<String, DictVo> flattenDictVoTree(DictVo root) {
        Map<String, DictVo> result = new HashMap<>();
        flattenDictVoTreeRecursive(root, result, true);
        return result;
    }
    
    /**
     * 递归扁平化DictVo树结构
     */
    private void flattenDictVoTreeRecursive(DictVo node, Map<String, DictVo> result, boolean isRoot) {
        if (node == null) {
            return;
        }
        
        // 跳过根节点，只处理子节点
        if (!isRoot && !node.getDictName().trim().isEmpty()) {
            result.put(node.getDictName().trim(), node);
        }
        
        // 递归处理子节点
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (DictVo child : node.getChildren()) {
                flattenDictVoTreeRecursive(child, result, false);
            }
        }
    }
    
    @Override
    public Map<String, ColorInfoVo> getColorInfoMap() {
        try {
            return colorInfoCache.get(DictConstant.CLOTHING_COLOR, this::loadColorInfoMap);
        } catch (Exception e) {
            log.error("获取颜色信息映射失败", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 加载颜色信息映射
     */
    private Map<String, ColorInfoVo> loadColorInfoMap() {
        try {
            List<DictVo> dictList = dictValueRemoteHelper.listByDictCodes(Collections.singletonList(DictConstant.CLOTHING_COLOR));
            if (dictList.isEmpty()) {
                log.warn("颜色字典 {} 未找到数据", DictConstant.CLOTHING_COLOR);
                return new HashMap<>();
            }
            
            DictVo colorDictVo = dictList.getFirst();
            Map<String, ColorInfoVo> colorInfoMap = DictTreeConverter.convertColorInfoMap(colorDictVo);
            
            if (!colorInfoMap.isEmpty()) {
                log.debug("成功加载颜色信息映射，包含 {} 个颜色", colorInfoMap.size());
                return colorInfoMap;
            } else {
                log.warn("颜色字典转换后为空");
                return new HashMap<>();
            }
        } catch (Exception e) {
            log.error("加载颜色信息映射异常", e);
            return new HashMap<>();
        }
    }
}