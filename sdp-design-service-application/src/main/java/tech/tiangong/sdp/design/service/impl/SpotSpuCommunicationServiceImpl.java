package tech.tiangong.sdp.design.service.impl;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.pop.common.enums.PublishAttributeShowTypeEnum;
import tech.tiangong.pop.common.enums.PublishAttributeValueStateEnum;
import tech.tiangong.pop.common.resp.CategoryAttributeResp;
import tech.tiangong.pop.common.resp.CategoryAttributeValueResp;
import tech.tiangong.pop.common.resp.ShopResp;
import tech.tiangong.sdp.design.config.SpotConfig;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.entity.SpotSkc;
import tech.tiangong.sdp.design.entity.SpotSpu;
import tech.tiangong.sdp.design.entity.SpotSpuDetail;
import tech.tiangong.sdp.design.enums.PlanningTypeEnum;
import tech.tiangong.sdp.design.enums.spot.SpotPopPushStatusEnum;
import tech.tiangong.sdp.design.enums.spot.SpotResourceStateEnum;
import tech.tiangong.sdp.design.enums.spot.SpotSourceTypeEnum;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.mq.rocketmq.entity.MurmurationTaskStatusEnum;
import tech.tiangong.sdp.design.remote.ZjApsRemoteHelper;
import tech.tiangong.sdp.design.repository.SpotSkcRepository;
import tech.tiangong.sdp.design.repository.SpotSpuDetailRepository;
import tech.tiangong.sdp.design.repository.SpotSpuRepository;
import tech.tiangong.sdp.design.repository.SpotSpuSupplierRepository;
import tech.tiangong.sdp.design.service.SpotSkcService;
import tech.tiangong.sdp.design.service.SpotSpuCommunicationService;
import tech.tiangong.sdp.design.service.SpotSpuDictService;
import tech.tiangong.sdp.design.vo.base.AttributeVo;
import tech.tiangong.sdp.design.vo.base.prototype.OpsObject;
import tech.tiangong.sdp.design.vo.dto.spot.SpotSpuCommunicationDto;
import tech.tiangong.sdp.design.vo.req.zj.ZjSupplierReq;
import tech.tiangong.sdp.design.vo.resp.spot.ApplyAiResultsVo;
import tech.tiangong.sdp.design.vo.resp.spot.AttributeConversionResultVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuCommunicationBatchPushResult;
import tech.tiangong.sdp.design.vo.resp.zj.aps.SupplierSimpleResp;
import tech.tiangong.sdp.utils.Bool;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 货通商品推送POP服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpotSpuCommunicationServiceImpl implements SpotSpuCommunicationService {
    private final SpotSpuRepository spotSpuRepository;
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final SpotSpuSupplierRepository spotSpuSupplierRepository;
    private final SpotSkcRepository spotSkcRepository;
    private final SpotSkcService spotSkcService;
    private final SpotConfig spotConfig;
    private final SpotSpuDictService spotSpuDictService;
    private final PopProductHelper popProductHelper;
    private final ZjApsRemoteHelper zjApsRemoteHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processCommunicationProductsToPop() {
        log.info("定时处理货通商品推送POP任务开始");

        // 检查功能是否启用
        if (!spotConfig.getCommunication().getPushEnabled()) {
            log.info("定时处理货通商品，推送POP功能已禁用，跳过执行");
            return;
        }

        // 1. 查询待推送的商品
        Integer batchSize = spotConfig.getCommunication().getBatchSize();
        List<SpotSpuCommunicationDto> pendingSpus = queryPendingCommunicationProducts(batchSize);

        log.info("定时处理货通商品查询到{}个待推送货通商品", pendingSpus.size());

        if (pendingSpus.isEmpty()) {
            log.info("定时处理货通商品，没有待推送的货通商品，任务结束");
            return;
        }

        // 2. 预处理默认值（避免在每个循环中重复处理）
        DefaultValueCache defaultValueCache = preProcessDefaultValues(spotConfig.getCommunication().getDefaultValues());

        // 3. 逐个处理
        int successCount = 0;
        int failCount = 0;
        int skipCount = 0;

        for (SpotSpuCommunicationDto spuDto : pendingSpus) {
            log.info("定时处理货通商品：{}", spuDto.getStyleCode());
            
            SpuProcessResult result = processSingleSpuToPop(spuDto, defaultValueCache);
            
            if (result.isSuccess()) {
                successCount++;
                if (result.getWarningMessage() != null) {
                    log.warn("SPU {} 处理成功但有警告: {}", result.getStyleCode(), result.getWarningMessage());
                }
            } else if (result.isFailed()) {
                failCount++;
            } else if (result.isSkipped()) {
                skipCount++;
            }
        }

        log.info("定时处理货通商品推送POP任务完成，成功：{}，失败：{}，跳过：{}", successCount, failCount, skipCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpotSpuCommunicationBatchPushResult batchPushSpuToPop(List<String> styleCodes) {
        log.info("批量推送{}个SPU到POP", styleCodes != null ? styleCodes.size() : 0);

        if (styleCodes == null || styleCodes.isEmpty()) {
            log.warn("styleCodes为空，无法执行推送");
            return new SpotSpuCommunicationBatchPushResult(0, 0, 0, 0,
                    Collections.emptyList(), Collections.emptyList(), Collections.emptyList());
        }

        List<String> successStyleCodes = Lists.newArrayList();
        List<String> failedStyleCodes = Lists.newArrayList();
        List<String> skippedStyleCodes = Lists.newArrayList();

        // 预处理默认值缓存，避免重复查询
        DefaultValueCache defaultValueCache = preProcessDefaultValues(spotConfig.getCommunication().getDefaultValues());

        for (String styleCode : styleCodes) {
            log.debug("开始推送SPU：{}", styleCode);
            
            // 1. 查询货通商品详情（从spot_spu_detail.ai_attribute_result获取AI结果）
            SpotSpuCommunicationDto spuDto = spotSpuDetailRepository.queryPendingCommunicationProductByStyleCode(styleCode);
            
            if (spuDto == null) {
                log.info("推送SPU，未找到StyleCode {} 对应的货通商品，可能是暂不满足推送条件", styleCode);
                skippedStyleCodes.add(styleCode);
                continue;
            }

            // 2. 验证是否为货通商品且满足推送条件
            if (!spuDto.isReadyForPopPush()) {
                log.warn("推送SPU，{} 不满足推送条件：{}", styleCode, spuDto.getPushStatusDesc());
                skippedStyleCodes.add(styleCode);
                continue;
            }

            // 3. 验证AI结果完整性
            if (!validateAiResults(spuDto)) {
                log.warn("推送SPU，{} AI结果验证失败", styleCode);
                skippedStyleCodes.add(styleCode);
                continue;
            }

            // 4. 使用核心处理逻辑
            SpuProcessResult result = processSingleSpuToPop(spuDto, defaultValueCache);
            
            if (result.isSuccess()) {
                successStyleCodes.add(styleCode);
                log.info("推送SPU，{} 推送成功", styleCode);
                if (result.getWarningMessage() != null) {
                    log.warn("推送SPU {} 成功但有警告: {}", styleCode, result.getWarningMessage());
                }
            } else if (result.isFailed()) {
                failedStyleCodes.add(styleCode);
                log.error("推送SPU，{} 推送失败: {}", styleCode, result.getErrorMessage());
            } else if (result.isSkipped()) {
                skippedStyleCodes.add(styleCode);
            }
        }

        SpotSpuCommunicationBatchPushResult result = new SpotSpuCommunicationBatchPushResult(
                styleCodes.size(),
                successStyleCodes.size(),
                failedStyleCodes.size(),
                skippedStyleCodes.size(),
                successStyleCodes,
                failedStyleCodes,
                skippedStyleCodes
        );

        log.info("批量推送完成：{}", result);
        return result;
    }

    @Override
    @Async("asyncTaskExecutor")
    public void asyncBatchPushSpuToPop(List<String> styleCodes) {
        log.info("异步批量推送{}个SPU到POP", styleCodes != null ? styleCodes.size() : 0);
        batchPushSpuToPop(styleCodes);
    }

    @Override
    public List<SpotSpuCommunicationDto> queryPendingCommunicationProducts(Integer batchSize) {
        log.info("查询待推送的货通商品，批次大小：{}", batchSize);

        try {
            // 使用新的基于spot_spu_detail.ai_attribute_result的查询方式
            List<SpotSpuCommunicationDto> candidateSpuDtos = spotSpuDetailRepository.queryPendingCommunicationProducts(batchSize);

            if (candidateSpuDtos.isEmpty()) {
                log.info("未找到待推送的货通商品");
                return Collections.emptyList();
            }

            log.info("通过spot_spu_detail.ai_attribute_result主驱动查询找到{}个待推送商品", candidateSpuDtos.size());

            // 进行最终业务验证，过滤出真正符合推送条件的商品
            List<SpotSpuCommunicationDto> validSpuDtos = new ArrayList<>();

            for (SpotSpuCommunicationDto spuDto : candidateSpuDtos) {
                // DTO已经包含了基本的筛选条件，进行最终业务验证
                if (spuDto.isReadyForPopPush()) {
                    validSpuDtos.add(spuDto);
                    log.debug("SPU {} 验证通过，AI结果完整性：{}",
                            spuDto.getStyleCode(), spuDto.hasValidAiResult());
                } else {
                    log.debug("SPU {} 不满足推送条件：{}",
                            spuDto.getStyleCode(), spuDto.getPushStatusDesc());
                }
            }

            log.info("最终筛选出{}个符合推送条件的商品", validSpuDtos.size());
            return validSpuDtos;

        } catch (Exception e) {
            log.error("查询待推送货通商品异常", e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean validateAiResults(SpotSpuCommunicationDto spuDto) {
        log.debug("验证SPU {} 的AI结果完整性", spuDto.getStyleCode());

        if (spuDto.getStyleCode() == null) {
            log.warn("SPU或StyleCode为空，验证失败");
            return false;
        }

        // 1. 验证SPU的AI结果是否完整
        boolean spuValid = spuDto.isReadyForPopPush();
        if (!spuValid) {
            log.debug("SPU {} AI结果验证未通过：{}",
                    spuDto.getStyleCode(), spuDto.getPushStatusDesc());
            return false;
        }

        // 2. 验证该SPU下所有未取消的SKC的颜色识别是否都成功
        List<SpotSkc> skcList = spotSkcRepository.listByStyleCode(spuDto.getStyleCode())
                .stream().filter(v -> Objects.equals(v.getIsCanceled(), Bool.NO.getCode())).toList();
        if (skcList.isEmpty()) {
            log.warn("SPU {} 没有找到关联的SKC，验证失败", spuDto.getStyleCode());
            return false;
        }

        // 检查识别任务完成状态和至少一个SKC已完善
        boolean allTasksCompleted = true;
        boolean hasFinishedSkc = false;

        for (SpotSkc skc : skcList) {
            var murmurationTaskStatus = MurmurationTaskStatusEnum.getByCode(skc.getAiColorStatus());
            // 检查AI颜色识别任务是否完成
            if (murmurationTaskStatus == null || !murmurationTaskStatus.isFinished()) {
                log.debug("SKC {} 颜色识别任务未完成，状态：{}", skc.getDesignCode(), skc.getAiColorStatus());
                allTasksCompleted = false;
                break;
            }

            // 检查是否有已完善的SKC
            if (Objects.equals(skc.getResourceStatus(), SpotResourceStateEnum.FINISH.code)) {
                hasFinishedSkc = true;
                log.debug("SKC {} 资料状态为已完善", skc.getDesignCode());
            }
        }

        if (!allTasksCompleted) {
            log.debug("SPU {} 存在未完成颜色识别任务的SKC，验证失败", spuDto.getStyleCode());
            return false;
        }

        if (!hasFinishedSkc) {
            log.debug("SPU {} 没有已完善状态的SKC，验证失败", spuDto.getStyleCode());
            return false;
        }

        log.debug("SPU {} AI结果验证通过，所有识别任务已完成，存在已完善状态的SKC", spuDto.getStyleCode());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApplyAiResultsVo applyAiResults(SpotSpuCommunicationDto spuDto) {
        // 兼容方法，为调用提供支持
        DefaultValueCache defaultValueCache = preProcessDefaultValues(spotConfig.getCommunication().getDefaultValues());
        return applyAiResults(spuDto, defaultValueCache);
    }

    @Override
    public boolean pushSingleSpuToPop(SpotSpuCommunicationDto spuDto) {
        return spotSkcService.pushPopProductBySpotSpu(spuDto);
    }

    @Override
    public void updatePushStatus(SpotSpuCommunicationDto spuDto, Integer status, String failReason) {
        if (spuDto == null || spuDto.getSpotSpuId() == null) {
            log.warn("更新推送状态失败：spuDto 或 spotSpuId 为空");
            return;
        }
        spotSpuRepository.updatePushStatusBySpuId(spuDto.getSpotSpuId(), status, failReason);
    }

    // ============ AI结果处理辅助方法 ============

    /**
     * 转换AI属性为POP属性格式
     * 使用智能匹配策略：先精确匹配，失败后使用包含匹配
     *
     * @param categoryName 品类名称
     * @param aiAttributes AI识别属性映射
     * @param styleCode    SPU编码
     * @return 属性转换结果（包含完整性状态和属性列表）
     */
    private AttributeConversionResultVo convertAiAttributesToPopAttributes(String categoryName,
                                                                           Map<String, String> aiAttributes, String styleCode) {
        List<AttributeVo> popAttributes = Lists.newArrayList();

        try {
            // 获取品类属性（利用PopProductHelper的缓存机制）
            List<CategoryAttributeResp> categoryAttributes = popProductHelper.listAttributeByCategoryNameWithCache(categoryName);

            if (categoryAttributes.isEmpty()) {
                log.warn("SPU[{}] 品类 '{}' 未找到对应的属性定义", styleCode, categoryName);
                return AttributeConversionResultVo.success(popAttributes);
            }

            log.debug("SPU[{}] 品类 '{}' 共有 {} 个属性定义", styleCode, categoryName, categoryAttributes.size());

            // 遍历品类属性，处理AI匹配和必填属性默认值
            for (CategoryAttributeResp categoryAttr : categoryAttributes) {
                String matchedAiAttrName = findMatchingAiAttribute(categoryAttr.getAttributeName(), aiAttributes.keySet());
                // 预先过滤一次有效属性值，避免重复计算
                List<CategoryAttributeValueResp> activeValues = filterActiveAttributeValues(categoryAttr.getAttributeValueList());
                AttributeVo popAttr = null;

                if (matchedAiAttrName != null) {
                    // AI匹配成功，使用AI值
                    String aiValue = aiAttributes.get(matchedAiAttrName);
                    popAttr = convertSingleAttribute(categoryAttr, aiValue);
                    if (popAttr != null) {
                        log.debug("SPU[{}] AI属性匹配成功：{} = {}", styleCode, matchedAiAttrName, aiValue);
                    } else {
                        // AI属性值无法匹配且使用默认值
                        CategoryAttributeValueResp defaultValue = findDefaultAttributeValue(activeValues);

                        if (defaultValue != null) {
                            popAttr = convertSingleAttributeWithDefault(categoryAttr, defaultValue);
                            log.debug("SPU[{}] AI属性 '{}' 值 '{}' 无法匹配，属性使用默认值：{}",
                                    styleCode, matchedAiAttrName, aiValue, defaultValue.getAttributeValue());
                        } else {
                            log.debug("SPU[{}] AI属性 '{}' 值 '{}' 无法匹配，属性无默认值", styleCode, matchedAiAttrName, aiValue);
                        }
                    }
                } else {
                    // AI属性值无法匹配且使用默认值
                    CategoryAttributeValueResp defaultValue = findDefaultAttributeValue(activeValues);

                    if (defaultValue != null) {
                        popAttr = convertSingleAttributeWithDefault(categoryAttr, defaultValue);
                        log.debug("SPU[{}] 属性使用默认值：{} = {}", styleCode, categoryAttr.getAttributeName(), defaultValue.getAttributeValue());
                    } else {
                        log.warn("SPU[{}] 属性 '{}' 无AI匹配且无默认值", styleCode, categoryAttr.getAttributeName());
                    }
                }

                if (popAttr != null) {
                    popAttributes.add(popAttr);
                }
            }

            // 验证必填属性是否完整
            String missingRequired = validateRequiredAttributes(categoryAttributes, popAttributes);
            if (missingRequired != null) {
                log.debug("SPU[{}] 品类 '{}' 存在缺失的必填属性 {}，但继续返回已转换属性", styleCode, categoryName, missingRequired);
                return AttributeConversionResultVo.incomplete(popAttributes, missingRequired);
            }

            log.debug("SPU[{}] 品类 '{}' 成功转换 {} 个POP属性，必填属性完整", styleCode, categoryName, popAttributes.size());
            return AttributeConversionResultVo.success(popAttributes);

        } catch (Exception e) {
            log.error("转换AI属性为POP属性时发生异常，品类：{}", categoryName, e);
            return AttributeConversionResultVo.success(popAttributes); // 发生异常时返回已转换的属性
        }
    }

    /**
     * 查找匹配的AI属性名称
     * 使用智能匹配策略：精确匹配 → 包含匹配
     */
    private String findMatchingAiAttribute(String categoryAttrName, java.util.Set<String> aiAttrNames) {
        if (categoryAttrName == null || CollectionUtils.isEmpty(aiAttrNames)) {
            return null;
        }

        String catNorm = StringUtils.trimToEmpty(categoryAttrName).toLowerCase();

        // 1. 精确匹配（忽略大小写和首尾空白）
        for (String aiAttrName : aiAttrNames) {
            String aiNorm = StringUtils.trimToEmpty(aiAttrName).toLowerCase();
            if (catNorm.equals(aiNorm)) {
                return aiAttrName; // 返回原始键名，保持下游取值一致
            }
        }

        // 2. 包含匹配（双向，忽略大小写）
        /*for (String aiAttrName : aiAttrNames) {
            String aiNorm = StringUtils.trimToEmpty(aiAttrName).toLowerCase();
            if (catNorm.contains(aiNorm) || aiNorm.contains(catNorm)) {
                log.debug("属性名包含匹配成功：'{}' ↔ '{}'", categoryAttrName, aiAttrName);
                return aiAttrName;
            }
        }*/

        return null;
    }

    /**
     * 过滤有效的属性值（排除state=0的无效值）
     */
    private List<CategoryAttributeValueResp> filterActiveAttributeValues(List<CategoryAttributeValueResp> attributeValues) {
        if (CollectionUtils.isEmpty(attributeValues)) {
            return Collections.emptyList();
        }

        return attributeValues.stream()
                // 仅保留有效(启用)的属性值
                .filter(value -> Objects.equals(PublishAttributeValueStateEnum.ENABLE.code, value.getState()))
                .collect(Collectors.toList());
    }

    /**
     * 查找默认属性值
     */
    private CategoryAttributeValueResp findDefaultAttributeValue(List<CategoryAttributeValueResp> attributeValues) {
        if (CollectionUtils.isEmpty(attributeValues)) {
            return null;
        }

        return attributeValues.stream()
                .filter(value -> Objects.equals(Bool.YES.getCode(), value.getDefaultFlag()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 验证必填属性是否完整
     */
    private String validateRequiredAttributes(List<CategoryAttributeResp> categoryAttributes,
                                              List<AttributeVo> popAttributes) {
        List<String> missingRequired = Lists.newArrayList();

        for (CategoryAttributeResp categoryAttr : categoryAttributes) {
            // 检查是否为必填属性
            if (Objects.equals(Bool.YES.getCode(), categoryAttr.getRequestFlag())) {
                // 检查是否在转换结果中
                boolean found = popAttributes.stream()
                        .anyMatch(popAttr -> Objects.equals(popAttr.getAttributeId(), categoryAttr.getAttributeId()));

                if (!found) {
                    missingRequired.add(categoryAttr.getAttributeName());
                }
            }
        }

        if (!missingRequired.isEmpty()) {
            return String.join(", ", missingRequired);
        }

        return null;
    }

    /**
     * 使用默认值转换单个属性
     */
    private AttributeVo convertSingleAttributeWithDefault(CategoryAttributeResp categoryAttr,
                                                          CategoryAttributeValueResp defaultValue) {
        AttributeVo popAttr = new AttributeVo();
        popAttr.setAttributeId(categoryAttr.getAttributeId());
        popAttr.setAttributeName(categoryAttr.getAttributeName());
        popAttr.setAttributeCode(categoryAttr.getAttributeCode());
        popAttr.setShowType(categoryAttr.getShowType());
        popAttr.setRequestFlag(categoryAttr.getRequestFlag());
        popAttr.setGroupName(categoryAttr.getGroupName());
        popAttr.setCategoryId(categoryAttr.getCategoryId());

        // 使用默认值
        List<AttributeVo.AttributeValue> values = Lists.newArrayList();
        values.add(createAttributeValue(defaultValue.getAttributeValueId(),
                defaultValue.getAttributeValue(), defaultValue.getUnit()));
        popAttr.setValues(values);

        return popAttr;
    }

    /**
     * 转换单个属性
     */
    private AttributeVo convertSingleAttribute(CategoryAttributeResp categoryAttr, String aiValue) {
        if (StringUtils.isBlank(aiValue)) {
            return null;
        }

        AttributeVo popAttr = new AttributeVo();
        popAttr.setAttributeId(categoryAttr.getAttributeId());
        popAttr.setAttributeName(categoryAttr.getAttributeName());
        popAttr.setAttributeCode(categoryAttr.getAttributeCode());
        popAttr.setShowType(categoryAttr.getShowType());
        popAttr.setRequestFlag(categoryAttr.getRequestFlag());
        popAttr.setGroupName(categoryAttr.getGroupName());
        popAttr.setCategoryId(categoryAttr.getCategoryId());

        // 根据showType处理属性值
        List<AttributeVo.AttributeValue> values = convertAttributeValues(categoryAttr, aiValue);
        popAttr.setValues(values);

        return values.isEmpty() ? null : popAttr;
    }

    /**
     * 根据不同的showType转换属性值
     */
    private List<AttributeVo.AttributeValue> convertAttributeValues(CategoryAttributeResp categoryAttr, String aiValue) {
        List<AttributeVo.AttributeValue> values = Lists.newArrayList();
        aiValue = StringUtils.trimToEmpty(aiValue);
        PublishAttributeShowTypeEnum showType;
        if (categoryAttr.getShowType() != null) {
            showType = PublishAttributeShowTypeEnum.getByCode(categoryAttr.getShowType());
        } else {
            return values;
        }

        // 过滤有效的属性值（排除state=0）
        List<CategoryAttributeValueResp> activeAttributeValues = filterActiveAttributeValues(categoryAttr.getAttributeValueList());

        switch (showType) {
            case INPUT:
            case UPLOAD_FILE:
                // 输入框类型：直接使用AI值
                values.add(createAttributeValue(null, aiValue, null));
                break;

            case SINGLE_SELECT:
            case RADIO:
                // 单选类型：匹配单个属性值
                CategoryAttributeValueResp matchedValue = findMatchingAttributeValue(
                        activeAttributeValues, aiValue);
                if (matchedValue != null) {
                    values.add(createAttributeValue(matchedValue.getAttributeValueId(),
                            matchedValue.getAttributeValue(), matchedValue.getUnit()));
                } else {
                    // 未匹配到，使用AI原始值
                    log.debug("属性 '{}' 的值 '{}' 未能匹配到选项",
                            categoryAttr.getAttributeName(), aiValue);
                }
                break;

            case MULTI_SELECT:
                // 多选类型：支持逗号分隔的多值
                String[] multiValues = aiValue.split("[,，\\s]+");
                for (String singleValue : multiValues) {
                    singleValue = singleValue.trim();
                    if (StringUtils.isNotBlank(singleValue)) {
                        CategoryAttributeValueResp matchedMultiValue = findMatchingAttributeValue(
                                activeAttributeValues, singleValue);
                        if (matchedMultiValue != null) {
                            values.add(createAttributeValue(matchedMultiValue.getAttributeValueId(),
                                    matchedMultiValue.getAttributeValue(), matchedMultiValue.getUnit()));
                        } else {
                            values.add(createAttributeValue(null, singleValue, null));
                        }
                    }
                }
                break;

            case NUM:
                // 数值类型：匹配带单位的属性值
                CategoryAttributeValueResp numValue = findMatchingNumAttributeValue(
                        activeAttributeValues, aiValue);
                if (numValue != null) {
                    values.add(createAttributeValue(numValue.getAttributeValueId(),
                            aiValue, numValue.getUnit()));
                } else {
                    // 未匹配到单位，直接使用AI值
                    values.add(createAttributeValue(null, aiValue, null));
                }
                break;

            case PROPERTY_CHOOSE_AND_NUM:
                // 属性勾选和数值录入：按"-"分割，第一部分匹配选项
                String[] parts = aiValue.split("-", 2);
                if (parts.length >= 1) {
                    String optionPart = parts[0].trim();
                    CategoryAttributeValueResp optionValue = findMatchingAttributeValue(
                            activeAttributeValues, optionPart);
                    if (optionValue != null) {
                        values.add(createAttributeValue(optionValue.getAttributeValueId(),
                                aiValue, optionValue.getUnit()));
                    } else {
                        values.add(createAttributeValue(null, aiValue, null));
                    }
                }
                break;

            case INPUT_AND_SINGLE_SELECT:
                // 输入框和下拉选项：按"-"分割，第二部分匹配选项
                String[] selectParts = aiValue.split("-", 2);
                if (selectParts.length >= 2) {
                    String selectPart = selectParts[1].trim();
                    CategoryAttributeValueResp selectValue = findMatchingAttributeValue(
                            activeAttributeValues, selectPart);
                    if (selectValue != null) {
                        values.add(createAttributeValue(selectValue.getAttributeValueId(),
                                aiValue, selectValue.getUnit()));
                    } else {
                        values.add(createAttributeValue(null, aiValue, null));
                    }
                } else {
                    values.add(createAttributeValue(null, aiValue, null));
                }
                break;

            default:
                // 未知类型：直接使用AI值
                log.warn("未知的属性显示类型：{}，属性：{}", categoryAttr.getShowType(),
                        categoryAttr.getAttributeName());
                values.add(createAttributeValue(null, aiValue, null));
                break;
        }

        return values;
    }

    /**
     * 查找匹配的属性值（智能匹配）
     */
    private CategoryAttributeValueResp findMatchingAttributeValue(List<CategoryAttributeValueResp> attributeValueList,
                                                                  String aiValue) {
        if (attributeValueList == null || attributeValueList.isEmpty()) {
            return null;
        }

        // 1. 精确匹配
        for (CategoryAttributeValueResp attrValue : attributeValueList) {
            if (aiValue.equals(attrValue.getAttributeValue())) {
                return attrValue;
            }
        }

        // 2. 包含匹配（双向）
        for (CategoryAttributeValueResp attrValue : attributeValueList) {
            if (attrValue.getAttributeValue() != null && (aiValue.contains(attrValue.getAttributeValue()) ||
                    attrValue.getAttributeValue().contains(aiValue))) {
                log.debug("属性值包含匹配成功：'{}' ↔ '{}'", aiValue, attrValue.getAttributeValue());
                return attrValue;
            }
        }

        return null;
    }

    /**
     * 查找匹配的数值型属性值（通过单位后缀匹配）
     */
    private CategoryAttributeValueResp findMatchingNumAttributeValue(List<CategoryAttributeValueResp> attributeValueList,
                                                                     String aiValue) {
        if (attributeValueList == null || attributeValueList.isEmpty()) {
            return null;
        }

        // 寻找单位后缀匹配的属性值
        for (CategoryAttributeValueResp attrValue : attributeValueList) {
            if (attrValue.getUnit() != null && aiValue.endsWith(attrValue.getUnit())) {
                return attrValue;
            }
        }

        return null;
    }

    /**
     * 创建属性值对象
     */
    private AttributeVo.AttributeValue createAttributeValue(Long attributeValueId, String attributeValue, String unit) {
        AttributeVo.AttributeValue value = new AttributeVo.AttributeValue();
        value.setAttributeValueId(attributeValueId);
        value.setAttributeValue(attributeValue);
        value.setUnit(unit);
        return value;
    }

    /**
     * 解析AI识别属性结果
     *
     * @param aiResult AI识别结果JSON
     * @return 解析后的属性Map
     */
    private Map<String, String> parseAiAttributes(Object aiResult, String styleCode) throws JsonProcessingException {
        Map<String, String> attributeMap = new HashMap<>();

        String jsonStr;
        if (aiResult == null) {
            log.warn("SPU[{}] AI识别结果为空", styleCode);
            return attributeMap;
        }

        if (aiResult instanceof String) {
            jsonStr = (String) aiResult;
        } else {
            jsonStr = new ObjectMapper().writeValueAsString(aiResult);
        }

        // 解析JSON字符串
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> jsonMap = mapper.readValue(jsonStr, new TypeReference<Map<String, Object>>() {
        });

        // 转换为String映射
        for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
            String value = entry.getValue() != null ? entry.getValue().toString() : null;
            attributeMap.put(entry.getKey(), value);
        }

        log.debug("SPU[{}] 成功解析AI属性，共{}个字段", styleCode, attributeMap.size());

        return attributeMap;
    }

    /**
     * 将AI属性映射到SPU实体字段
     * 基于<a href="https://alidocs.dingtalk.com/i/nodes/pYLaezmVNeZKe0MGU4e5GN4jWrMqPxX6">1688货通项目-会议沟通</a>在线文档映射规则进行字段赋值
     *
     * @param spuDto            SPU DTO
     * @param aiAttributes      AI属性映射
     * @param defaultValueCache 预处理的默认值缓存
     */
    private void mapAiAttributesToSpotSpu(SpotSpuCommunicationDto spuDto,
                                          Map<String, String> aiAttributes,
                                          DefaultValueCache defaultValueCache) {

        var aiMapping = spotConfig.getCommunication().getAiAttributeMapping();

        // 风格属性映射
        var styleConfig = aiMapping.getStyle();
        if (aiAttributes.containsKey(styleConfig.getChinese()) || aiAttributes.containsKey(styleConfig.getEnglish())) {
            String style = getValueFromAi(aiAttributes, styleConfig.getChinese(), styleConfig.getEnglish());
            if (style != null) {
                // 使用智能匹配获取字典对象
                DictVo styleDict = spotSpuDictService.getDictVoByName(DictConstant.JV_STYLE, style);
                if (styleDict != null) {
                    spuDto.setClothingStyleName(styleDict.getDictName());
                    spuDto.setClothingStyleCode(styleDict.getDictCode());
                } else {
                    log.debug("AI属性'{}={}' 未能匹配到风格字典项", styleConfig.getChinese(), style);
                }
            }
        }

        // 季节字段会在detail中处理，暂时存储到临时变量
        var seasonConfig = aiMapping.getSeason();
        if (aiAttributes.containsKey(seasonConfig.getChinese()) || aiAttributes.containsKey(seasonConfig.getEnglish())) {
            String season = getValueFromAi(aiAttributes, seasonConfig.getChinese(), seasonConfig.getEnglish());
            if (season != null) {
                // 将季节信息存储到aiAttributes中，稍后在applyAiResults中处理
                log.debug("获取到季节信息：{}", season);
            }
        }

        // 编织工艺属性映射
        var weaveConfig = aiMapping.getWeave();
        if (aiAttributes.containsKey(weaveConfig.getChinese1()) || aiAttributes.containsKey(weaveConfig.getChinese2())) {
            String weaveMode = getValueFromAi(aiAttributes, weaveConfig.getChinese1(), weaveConfig.getChinese2());
            if (weaveMode != null) {
                // 使用智能匹配获取字典对象
                DictVo weaveModeDict = spotSpuDictService.getDictVoByName(DictConstant.WEAVE_MODE, weaveMode);
                if (weaveModeDict != null) {
                    spuDto.setWeaveMode(weaveModeDict.getDictName());
                    spuDto.setWeaveModeCode(weaveModeDict.getDictCode());
                } else {
                    log.debug("AI属性'{}={}' 未能匹配到编织工艺字典项", weaveConfig.getChinese1(), weaveMode);
                }
            }
        }

        // 面料弹性属性映射
        var elasticConfig = aiMapping.getElastic();
        if (aiAttributes.containsKey(elasticConfig.getChinese1()) || aiAttributes.containsKey(elasticConfig.getChinese2())) {
            String elastic = getValueFromAi(aiAttributes, elasticConfig.getChinese1(), elasticConfig.getChinese2());
            if (elastic != null) {
                // 使用智能匹配获取字典对象
                DictVo elasticDict = spotSpuDictService.getDictVoByName(DictConstant.PLM_ELASTIC_REQUIREMENT, elastic);
                if (elasticDict != null) {
                    spuDto.setElasticName(elasticDict.getDictName());
                    spuDto.setElasticCode(elasticDict.getDictCode());
                } else {
                    log.debug("AI属性'{}={}' 未能匹配到弹性字典项", elasticConfig.getChinese1(), elastic);
                }
            }
        }

        // 图案元素属性映射
        var elementConfig = aiMapping.getElement();
        if (aiAttributes.containsKey(elementConfig.getChinese())) {
            String elements = aiAttributes.get(elementConfig.getChinese());
            if (elements != null) {
                // 使用智能匹配获取字典对象
                DictVo elementsDict = spotSpuDictService.getDictVoByName(DictConstant.STYLE_ELEMENTS, elements);
                if (elementsDict != null) {
                    spuDto.setElementName(elementsDict.getDictName());
                    spuDto.setElementCode(elementsDict.getDictCode());
                } else {
                    log.debug("AI属性'{}={}' 未能匹配到图案元素字典项", elementConfig.getChinese(), elements);
                }
            }
        }

        // 应用默认值配置（标记为"默认参数"的字段）
        applyDefaultValuesToSpotSpu(spuDto, defaultValueCache);
    }

    /**
     * 从AI属性中获取值，支持多个键名匹配
     */
    private String getValueFromAi(Map<String, String> aiAttributes, String... keys) {
        for (String key : keys) {
            String value = aiAttributes.get(key);
            if (value != null && !value.trim().isEmpty()) {
                return value.trim();
            }
        }
        return null;
    }

    /**
     * 创建季节OpsObject列表
     *
     * @param seasonStr 季节字符串，可能包含多个季节，用逗号分隔
     * @return OpsObject列表
     */
    private List<OpsObject> createSeasonOpsObjectList(String seasonStr) {
        List<OpsObject> seasonList = Lists.newArrayList();

        if (StringUtils.isNotBlank(seasonStr)) {
            // 处理可能的多个季节，用逗号、空格或其他分隔符分隔
            String[] seasons = seasonStr.split("[,，\\s]+");

            for (String season : seasons) {
                season = season.trim();
                if (StringUtils.isNotBlank(season)) {
                    // 尝试通过字典获取季节编码
                    DictVo seasonDict = spotSpuDictService.getDictVoByName(DictConstant.PLM_REFERENCE_SEASON, season);

                    OpsObject seasonObj = new OpsObject();
                    if (seasonDict != null) {
                        seasonObj.setCode(seasonDict.getDictCode());
                        seasonObj.setName(seasonDict.getDictName());
                    } else {
                        // 如果字典匹配失败
                        log.debug("季节 '{}' 未能匹配到字典项", season);
                    }
                    seasonList.add(seasonObj);
                }
            }
        }

        return seasonList;
    }

    /**
     * 更新SpuDetail的季节信息
     *
     * @param spotSpuId  SPU ID
     * @param seasonList 季节对象列表
     */
    private void updateSpuDetailSeasonInfo(Long spotSpuId, List<OpsObject> seasonList) {
        spotSpuDetailRepository.lambdaUpdate()
                .set(SpotSpuDetail::getStyleSeasonList, JSON.toJSONString(seasonList))
                .set(SpotSpuDetail::getRevisedTime, LocalDateTime.now())
                .eq(SpotSpuDetail::getSpotSpuId, spotSpuId)
                .update();
    }

    /**
     * 应用默认值到SPU实体 - 使用预处理的默认值缓存
     */
    private void applyDefaultValuesToSpotSpu(SpotSpuCommunicationDto spuDto,
                                             DefaultValueCache defaultValueCache) {

        // 设置默认值字段
        if (StringUtils.isBlank(spuDto.getSupplyModeCode()) && defaultValueCache.getSupplyModeDict() != null) {
            DictVo supplyModeDict = defaultValueCache.getSupplyModeDict();
            spuDto.setSupplyModeName(supplyModeDict.getDictName());
            spuDto.setSupplyModeCode(supplyModeDict.getDictCode());
        }

        if (StringUtils.isBlank(spuDto.getPalletTypeCode()) && defaultValueCache.getPalletTypeDict() != null) {
            DictVo palletTypeDict = defaultValueCache.getPalletTypeDict();
            spuDto.setPalletTypeName(palletTypeDict.getDictName());
            spuDto.setPalletTypeCode(palletTypeDict.getDictCode());
        }

        // 市场和市场系列级联设置
        if (StringUtils.isBlank(spuDto.getMarketCode()) && defaultValueCache.getMarketStyleDict() != null) {
            spuDto.setMarketCode(defaultValueCache.getMarketStyleDict().getDictCode());

        }
        // 设置市场系列编码 - 优先使用预查询的默认系列
        if (StringUtils.isBlank(spuDto.getMarketSeriesCode()) && ObjectUtils.isNotEmpty(defaultValueCache.getMarketSeriesDict())) {
            spuDto.setMarketSeriesCode(defaultValueCache.getMarketSeriesDict().getDictCode());
        }

        if (StringUtils.isBlank(spuDto.getCountrySiteCode()) && defaultValueCache.getCountryDict() != null) {
            DictVo countryDict = defaultValueCache.getCountryDict();
            spuDto.setCountrySiteCode(countryDict.getDictCode());
            spuDto.setCountrySiteName(countryDict.getDictName());
        }

        if (StringUtils.isBlank(spuDto.getProductType()) && defaultValueCache.getProductTypeDict() != null) {
            DictVo productTypeDict = defaultValueCache.getProductTypeDict();
            spuDto.setProductType(productTypeDict.getDictName());
            spuDto.setProductTypeCode(productTypeDict.getDictCode());
        }

        if (spuDto.getPlanningType() == null && defaultValueCache.getPlanningTypeCode() != null) {
            spuDto.setPlanningType(defaultValueCache.getPlanningTypeCode());
        }

        if (StringUtils.isBlank(spuDto.getQualityLevel()) && defaultValueCache.getQualityLevelDict() != null) {
            DictVo qualityLevelDict = defaultValueCache.getQualityLevelDict();
            spuDto.setQualityLevel(qualityLevelDict.getDictName());
            spuDto.setQualityLevelCode(qualityLevelDict.getDictCode());
        }

        if (StringUtils.isBlank(spuDto.getSceneCode()) && defaultValueCache.getSceneDict() != null) {
            DictVo sceneDict = defaultValueCache.getSceneDict();
            spuDto.setSceneName(sceneDict.getDictName());
            spuDto.setSceneCode(sceneDict.getDictCode());
        }

        if (StringUtils.isBlank(spuDto.getSpotTypeCode()) && defaultValueCache.getStockGoodsType() != null) {
            DictVo stockGoodsTypeDict = defaultValueCache.getStockGoodsType();
            spuDto.setSpotTypeName(stockGoodsTypeDict.getDictName());
            spuDto.setSpotTypeCode(stockGoodsTypeDict.getDictCode());
        }

        // 设置推荐店铺
        if (StringUtils.isBlank(spuDto.getStoreName()) && defaultValueCache.getRecommendShop() != null) {
            ShopResp shop = defaultValueCache.getRecommendShop();
            spuDto.setStoreId(shop.getShopId());
            spuDto.setStoreName(shop.getShopName());
            log.debug("设置推荐店铺：{} (ID: {})", shop.getShopName(), shop.getShopId());
        }
    }

    /**
     * 预处理默认值，避免在循环中重复查询
     *
     * @param defaultValues 默认值配置
     * @return 默认值缓存对象
     */
    private DefaultValueCache preProcessDefaultValues(SpotConfig.Communication.DefaultValues defaultValues) {
        DefaultValueCache cache = new DefaultValueCache();

        // 预查询所有字典项
        if (StringUtils.isNotBlank(defaultValues.getSupplyMode())) {
            cache.setSupplyModeDict(spotSpuDictService.getDictVoByName(DictConstant.SUPPLY_MODE, defaultValues.getSupplyMode()));
        }

        if (StringUtils.isNotBlank(defaultValues.getTrayType())) {
            cache.setPalletTypeDict(spotSpuDictService.getDictVoByName(DictConstant.TRAY_TYPE, defaultValues.getTrayType()));
        }

        // 市场字典处理 - 直接使用SpotSpuDictService获取完整字典对象
        if (StringUtils.isNotBlank(defaultValues.getMarket())) {
            // 获取市场字典对象（包含完整的层级结构）
            DictVo marketStyleDict = spotSpuDictService.getDictVoByName(DictConstant.MARKET_STYLE, defaultValues.getMarket());
            cache.setMarketStyleDict(marketStyleDict);

            // 从字典对象中查找默认系列编码
            if (marketStyleDict != null) {
                cache.setMarketSeriesDict(findDefaultMarketSeriesFromDict(marketStyleDict, defaultValues));
            }
        }

        if (StringUtils.isNotBlank(defaultValues.getCountry())) {
            cache.setCountryDict(spotSpuDictService.getDictVoByName(DictConstant.NATIONAL, defaultValues.getCountry()));
        }

        if (StringUtils.isNotBlank(defaultValues.getProductType())) {
            cache.setProductTypeDict(spotSpuDictService.getDictVoByName(DictConstant.PRODUCT_TYPE, defaultValues.getProductType()));
        }

        // 企划类型特殊处理
        if (StringUtils.isNotBlank(defaultValues.getPlanningType())) {
            Integer planningTypeCode = PlanningTypeEnum.getEntries().stream()
                    .filter(v -> v.getDesc().contains(defaultValues.getPlanningType()))
                    .findFirst().map(PlanningTypeEnum::getCode).orElse(null);
            cache.setPlanningTypeCode(planningTypeCode);
        }

        if (StringUtils.isNotBlank(defaultValues.getQualityLevel())) {
            cache.setQualityLevelDict(spotSpuDictService.getDictVoByName(DictConstant.PLM_QUALITY_LEVEL, defaultValues.getQualityLevel()));
        }

        if (StringUtils.isNotBlank(defaultValues.getApplicableScene())) {
            cache.setSceneDict(spotSpuDictService.getDictVoByName(DictConstant.JV_SCENE, defaultValues.getApplicableScene()));
        }

        if (StringUtils.isNotBlank(defaultValues.getStockGoodsType())) {
            cache.setStockGoodsType(spotSpuDictService.getDictVoByName(DictConstant.STOCK_GOODS_TYPE, defaultValues.getStockGoodsType()));
        }

        if (StringUtils.isNotBlank(defaultValues.getPayeeName())) {
            cache.setSupplierSimpleResp(querySupplierInfo(defaultValues.getPayeeName()));
        }

        // 预查询推荐店铺
        if (StringUtils.isNotBlank(defaultValues.getRecommendShop())) {
            List<ShopResp> shopList = popProductHelper.listShop();
            if (shopList != null && !shopList.isEmpty()) {
                ShopResp matchedShop = shopList.stream()
                        .filter(shop -> defaultValues.getRecommendShop().equals(shop.getShopName()))
                        .findFirst()
                        .orElse(null);
                cache.setRecommendShop(matchedShop);
                if (matchedShop != null) {
                    log.debug("预处理推荐店铺：{} (ID: {})", matchedShop.getShopName(), matchedShop.getShopId());
                } else {
                    log.debug("未找到匹配的推荐店铺：{}", defaultValues.getRecommendShop());
                }
            }
        }

        // 输出市场字典预处理统计信息
        if (cache.getMarketStyleDict() != null && StringUtils.isNotBlank(defaultValues.getMarket())) {
            DictVo marketDict = cache.getMarketStyleDict();
            int seriesCount = marketDict.getChildren() != null ? marketDict.getChildren().size() : 0;
            log.info("市场字典预处理完成 - 市场：{}, 下级系列数量：{}, 默认系列编码：{}",
                    defaultValues.getMarket(),
                    seriesCount,
                    cache.getMarketSeriesDict());
        }

        log.debug("默认值预处理完成");
        return cache;
    }

    /**
     * 应用供应商信息
     * 创建或更新spot_spu_supplier记录
     * 通过APS服务获取真实供应商信息，支持降级策略
     *
     * @param spuDto SPU DTO
     * @return 处理是否成功
     */
    private boolean applySupplierInfo(SpotSpuCommunicationDto spuDto,
                                      DefaultValueCache defaultValueCache) {
        var defaultValues = spotConfig.getCommunication().getDefaultValues();

        // 获取最大采购价（使用现有的Repository方法）
        BigDecimal maxPrice = spotSpuSupplierRepository.getMaxPurchasePrice(spuDto.getStyleCode());

        SupplierSimpleResp supplierInfo = defaultValueCache.supplierSimpleResp;

        Long productId = NumberUtils.isParsable(spuDto.getOriginalSpuId()) ? Long.valueOf(spuDto.getOriginalSpuId()) : null;
        // 保存或更新供应商记录
        boolean saved = spotSpuSupplierRepository.saveOrUpdateForCommunication(
                spuDto.getStyleCode(),
                SpotSourceTypeEnum.PICK_STYLE.code, // 数据来源: 20-选款（货通商品）
                defaultValues.getSupplierName(),
                spuDto.getOriginalSpuId(), // 供应商款号使用1688商品ID
                defaultValues.getPayeeName(), // 默认"阿里"
                maxPrice, // 采购价
                productId, // 1688商品ID
                supplierInfo // 供应商信息对象（可能为null）
        );

        if (saved) {
            String supplierName = Optional.ofNullable(supplierInfo).map(SupplierSimpleResp::getSupplierName).orElse(defaultValues.getSupplierName());
            String supplierCode = Optional.ofNullable(supplierInfo).map(SupplierSimpleResp::getSupplierCode).orElse("N/A");
            log.debug("保存SPU {} 供应商信息完成，供应商：{} ({})",
                    spuDto.getStyleCode(), supplierName, supplierCode);
        }

        return saved;
    }

    /**
     * 查询供应商信息
     *
     * @param supplierName 供应商名称
     * @return 供应商信息对象
     */
    private SupplierSimpleResp querySupplierInfo(String supplierName) {
        ZjSupplierReq req = new ZjSupplierReq();
        req.setSupplierName(supplierName);
        log.debug("查询APS供应商信息：{}", supplierName);

        long startTime = Instant.now().toEpochMilli();
        List<SupplierSimpleResp> suppliers = zjApsRemoteHelper.queryApsSupplier(req);
        log.debug("APS供应商查询耗时：{} ms", Instant.now().toEpochMilli() - startTime);

        if (suppliers == null || suppliers.isEmpty()) {
            log.debug("APS未查询到匹配的供应商：{}", supplierName);
            return null;
        }

        SupplierSimpleResp supplier = suppliers.stream()
                .filter(v -> Objects.equals(Bool.YES.getCode(), v.getSupplierState()))
                .findFirst()
                .orElse(null);

        if (supplier == null) {
            log.debug("没有找到合适的供应商：{}", supplierName);
            return null;
        }

        log.debug("APS查询到有效供应商：{}", supplier.getSupplierName());
        return supplier;
    }

    /**
     * 从市场字典对象中查找默认系列编码
     * 直接遍历字典的子级结构，查找"其他"系列
     *
     * @param marketStyleDict 市场风格字典对象
     * @return 默认系列编码，如果未找到返回null
     */
    private DictVo findDefaultMarketSeriesFromDict(DictVo marketStyleDict, SpotConfig.Communication.DefaultValues defaultValues) {
        if (marketStyleDict == null || marketStyleDict.getChildren() == null) {
            return null;
        }

        // 遍历二级字典（系列级别）
        for (DictVo seriesDict : marketStyleDict.getChildren()) {
            if (seriesDict == null) continue;

            // 查找名称为"其他"的系列
            if (defaultValues.getMarketSeries().equals(seriesDict.getDictName())) {
                log.debug("找到默认系列：{} -> {}", seriesDict.getDictName(), seriesDict.getDictCode());
                return seriesDict;
            }
        }

        // 如果没有找到"其他"，返回第一个系列作为默认值
        if (!marketStyleDict.getChildren().isEmpty()) {
            DictVo firstSeries = marketStyleDict.getChildren().getFirst();
            log.debug("未找到'其他'系列，使用第一个系列作为默认：{} -> {}",
                    firstSeries.getDictName(), firstSeries.getDictCode());
            return firstSeries;
        }

        return null;
    }

    /**
     * 应用AI结果（优化版本，使用预处理的默认值）
     */
    private ApplyAiResultsVo applyAiResults(SpotSpuCommunicationDto spuDto, DefaultValueCache defaultValueCache) {
        log.debug("应用SPU {} 的AI识别结果", spuDto.getStyleCode());
        ApplyAiResultsVo response = ApplyAiResultsVo.success();
        if (spuDto.getStyleCode() == null) {
            response.addError("SPU信息或StyleCode为空，无法应用AI结果");
        }
        if (StringUtils.isBlank(spuDto.getCategoryName())) {
            response.addError("SPU " + spuDto.getStyleCode() + " 品类名称为空，无法应用AI结果");
        }

        if (!response.isSuccess()) {
            return response;
        }

        try {
            // 1. 解析AI识别属性结果
            Map<String, String> aiAttributes = parseAiAttributes(spuDto.getAiResult(), spuDto.getStyleCode());
            if (aiAttributes.isEmpty()) {
                return ApplyAiResultsVo.failure("SPU " + spuDto.getStyleCode() + " AI属性结果为空或解析失败");
            }

            // 2. 应用AI属性映射到SPU（包含默认值）
            mapAiAttributesToSpotSpu(spuDto, aiAttributes, defaultValueCache);

            // 3. 更新spot_spu表（保存SPU基本信息的修改）
            spotSpuRepository.updateById(spuDto);
            log.debug("更新SPU {} 基本信息完成", spuDto.getStyleCode());

            // 4. 处理季节信息到SpotSpuDetail（非关键，失败不影响整体）
            var seasonConfig = spotConfig.getCommunication().getAiAttributeMapping().getSeason();
            String seasonStr = getValueFromAi(aiAttributes, seasonConfig.getChinese(), seasonConfig.getEnglish());
            if (seasonStr != null) {
                List<OpsObject> seasonList = createSeasonOpsObjectList(seasonStr);
                if (!seasonList.isEmpty()) {
                    updateSpuDetailSeasonInfo(spuDto.getSpotSpuId(), seasonList);
                }
            }

            // 4.2 转换AI属性为POP属性格式并保存到SpotSpuDetail
            AttributeConversionResultVo conversionResult = convertAiAttributesToPopAttributes(
                    spuDto.getCategoryName(), aiAttributes, spuDto.getStyleCode());

            // 处理必填属性不完整的情况
            if (!conversionResult.isComplete()) {
                String warningMsg = "品类 '" + spuDto.getCategoryName() + "' 缺失必填属性: " + conversionResult.getMissingAttributes();
                log.warn("SPU[{}] {}", spuDto.getStyleCode(), warningMsg);
                response.addError(warningMsg);
            }

            List<AttributeVo> popAttributes = conversionResult.getAttributes();
            if (!popAttributes.isEmpty()) {
                spotSpuDetailRepository.lambdaUpdate()
                        .set(SpotSpuDetail::getAttributes, JSON.toJSONString(popAttributes))
                        .set(SpotSpuDetail::getRevisedTime, LocalDateTime.now())
                        .eq(SpotSpuDetail::getSpotSpuId, spuDto.getSpotSpuId())
                        .update();

                log.debug("成功保存SPU {} 的 {} 个POP属性到SpotSpuDetail",
                        spuDto.getStyleCode(), popAttributes.size());
            } else {
                log.debug("SPU {} 未生成任何POP属性", spuDto.getStyleCode());
            }
            log.debug("回填SPU {} Detail AI属性结果完成", spuDto.getStyleCode());

            // 5. SKC颜色处理已迁移到MurmurationTaskStatusConsumer中实时处理

            // 6. 创建或更新供应商信息
            boolean supplierSaved = applySupplierInfo(spuDto, defaultValueCache);
            if (!supplierSaved) {
                return response.addError("保存SPU " + spuDto.getStyleCode() + " 供应商信息失败");
            }

            log.info("SPU {} AI结果应用完成", spuDto.getStyleCode());
            return response;

        } catch (Exception e) {
            log.error("应用SPU {} AI结果时发生异常", spuDto.getStyleCode(), e);
            return ApplyAiResultsVo.failure("应用SPU " + spuDto.getStyleCode() + " AI结果时发生异常: " + e.getMessage());
        }
    }

    /**
     * 校验SPU推送到POP前的必填字段是否已填充
     * 必填清单：供给方式、货盘类型、市场、品质等级、国家、品类、尺码组、现货类型、企划类型、商品类型、季节、面料弹性
     *
     * @param spuDto SPU数据
     * @return 错误消息列表（空列表表示通过）
     */
    private List<String> validateRequiredSpuFields(SpotSpuCommunicationDto spuDto) {
        List<String> errors = Lists.newArrayList();
        if (spuDto == null) {
            errors.add("SPU数据为空");
            return errors;
        }
        if (StringUtils.isBlank(spuDto.getSupplyModeCode())) {
            errors.add("供给方式不能为空");
        }
        if (StringUtils.isBlank(spuDto.getPalletTypeCode())) {
            errors.add("货盘类型不能为空");
        }
        if (StringUtils.isBlank(spuDto.getMarketCode())) {
            errors.add("市场不能为空");
        }
        if (StringUtils.isBlank(spuDto.getQualityLevelCode())) {
            errors.add("品质等级不能为空");
        }
        if (StringUtils.isBlank(spuDto.getCountrySiteCode())) {
            errors.add("国家不能为空");
        }
        if (StringUtils.isAnyBlank(spuDto.getCategory(), spuDto.getCategoryName())) {
            errors.add("品类不能为空");
        }
        if (StringUtils.isBlank(spuDto.getSizeStandardCode())) {
            errors.add("尺码组不能为空");
        }
        if (StringUtils.isBlank(spuDto.getSpotTypeCode())) {
            errors.add("现货类型不能为空");
        }
        if (spuDto.getPlanningType() == null) {
            errors.add("企划类型不能为空");
        }
        if (StringUtils.isBlank(spuDto.getProductTypeCode())) {
            errors.add("商品类型不能为空");
        }
        if (Objects.isNull(spuDto.getStoreId())) {
            errors.add("店铺不能为空");
        }
        // 季节、弹性 暂不校验
        /*if (StringUtils.isBlank(spuDto.getElasticCode())) {
            errors.add("面料弹性不能为空");
        }*/
        // 季节（在SpotSpuDetail中）
        /*SpotSpuDetail detail = spotSpuDetailRepository.lambdaQuery().select(SpotSpuDetail::getStyleSeasonList)
                .eq(SpotSpuDetail::getSpotSpuId, spuDto.getSpotSpuId()).orderByDesc(SpotSpuDetail::getCreatedTime)
                .last("LIMIT 1").one();
        if (detail == null || detail.getStyleSeasonList() == null || detail.getStyleSeasonList().isEmpty()) {
            errors.add("季节不能为空");
        }*/

        return errors;
    }

    /**
     * 将 SPU 资料标记为已完善
     * 如果状态从待补充变更为已完善，则回填开发人信息
     */
    private void markSpuAsFinished(SpotSpuCommunicationDto spuDto) {
        if (!Objects.equals(spuDto.getResourceStatus(), SpotResourceStateEnum.FINISH.getCode())) {
            // 资料状态变为已完善; SPU资料由待补充变更为已完善时，记录对应操作用户为开发人；
            UserContent userContent = UserContentHolder.get();
            var updateSpotSpu = new SpotSpu();
            spuDto.setResourceStatus(SpotResourceStateEnum.FINISH.getCode());
            spuDto.setDeveloperId(userContent.getCurrentUserId());
            spuDto.setDeveloperName(userContent.getCurrentUserName());
            updateSpotSpu.setSpotSpuId(spuDto.getSpotSpuId());
            updateSpotSpu.setResourceStatus(spuDto.getResourceStatus());
            updateSpotSpu.setDeveloperId(spuDto.getDeveloperId());
            updateSpotSpu.setDeveloperName(spuDto.getDeveloperName());
            spotSpuRepository.updateById(updateSpotSpu);
        }
    }

    /**
     * 处理单个SPU推送到POP的核心逻辑
     * 统一了定时处理和批量推送的处理流程
     *
     * @param spuDto SPU数据
     * @param defaultValueCache 预处理的默认值缓存
     * @return 处理结果
     */
    private SpuProcessResult processSingleSpuToPop(SpotSpuCommunicationDto spuDto, DefaultValueCache defaultValueCache) {
        String styleCode = spuDto.getStyleCode();
        
        try {
            log.debug("开始处理SPU: {}", styleCode);

            // 1. 验证AI结果完整性
            if (!spuDto.hasValidAiResult()) {
                String reason = "AI结果未完整";
                log.info("SPU {} {}，跳过处理", styleCode, reason);
                return SpuProcessResult.skipped(styleCode, reason);
            }

            // 2. 应用AI结果
            ApplyAiResultsVo aiResult = applyAiResults(spuDto, defaultValueCache);
            if (!aiResult.isSuccess()) {
                String errorMsg = String.join("; ", aiResult.getErrorMessages());
                log.error("SPU {} 应用AI结果失败: {}", styleCode, errorMsg);
                updatePushStatus(spuDto, SpotPopPushStatusEnum.PUSHED_FAILED.getCode(), errorMsg);
                return SpuProcessResult.failed(styleCode, errorMsg);
            }

            // 记录警告信息（如季节处理失败等）
            String warningMsg = null;
            if (aiResult.hasWarnings()) {
                warningMsg = String.join("; ", aiResult.getWarningMessages());
                log.warn("SPU {} AI结果应用有警告: {}", styleCode, warningMsg);
            }

            // 3. 校验SPU必填字段
            List<String> requiredFieldErrors = validateRequiredSpuFields(spuDto);
            if (!requiredFieldErrors.isEmpty()) {
                String errorMsg = "必填字段缺失: " + String.join(", ", requiredFieldErrors);
                log.warn("SPU {} 校验失败: {}", styleCode, errorMsg);
                updatePushStatus(spuDto, SpotPopPushStatusEnum.PUSHED_FAILED.getCode(), errorMsg);
                return SpuProcessResult.failed(styleCode, errorMsg);
            }

            // 4. 标记资料为已完善
            markSpuAsFinished(spuDto);

            // 5. 推送到POP
            boolean success = pushSingleSpuToPop(spuDto);

            if (success) {
                updatePushStatus(spuDto, SpotPopPushStatusEnum.PUSHED_SUCCESS.getCode(), null);
                log.info("SPU {} 推送POP成功", styleCode);
                SpuProcessResult result = SpuProcessResult.success(styleCode);
                if (warningMsg != null) {
                    result.withWarning(warningMsg);
                }
                return result;
            } else {
                String errorMsg = "推送POP失败";
                updatePushStatus(spuDto, SpotPopPushStatusEnum.PUSHED_FAILED.getCode(), errorMsg);
                log.error("SPU {} 推送POP失败", styleCode);
                return SpuProcessResult.failed(styleCode, errorMsg);
            }

        } catch (Exception e) {
            String errorMsg = "处理异常: " + e.getMessage();
            log.error("处理SPU {} 异常", styleCode, e);
            updatePushStatus(spuDto, SpotPopPushStatusEnum.PUSHED_FAILED.getCode(), errorMsg);
            return SpuProcessResult.failed(styleCode, errorMsg);
        }
    }

    /**
     * 默认值缓存类，用于避免在循环中重复查询字典和店铺信息
     */
    @Data
    private static class DefaultValueCache {
        private DictVo supplyModeDict;
        private DictVo palletTypeDict;
        private DictVo marketStyleDict;
        private DictVo marketSeriesDict; // 市场系列编码（二级市场）
        private DictVo countryDict;
        private DictVo productTypeDict;
        private Integer planningTypeCode;
        private DictVo qualityLevelDict;
        private DictVo sceneDict;
        private ShopResp recommendShop;
        private DictVo stockGoodsType;
        private SupplierSimpleResp supplierSimpleResp;
    }

    /**
     * SPU处理结果枚举
     */
    private enum SpuProcessStatus {
        SUCCESS, FAILED, SKIPPED
    }

    /**
     * 单个SPU处理结果类
     */
    @Data
    private static class SpuProcessResult {
        private String styleCode;
        private SpuProcessStatus status;
        private String errorMessage;
        private String warningMessage;

        public static SpuProcessResult success(String styleCode) {
            SpuProcessResult result = new SpuProcessResult();
            result.setStyleCode(styleCode);
            result.setStatus(SpuProcessStatus.SUCCESS);
            return result;
        }

        public static SpuProcessResult failed(String styleCode, String errorMessage) {
            SpuProcessResult result = new SpuProcessResult();
            result.setStyleCode(styleCode);
            result.setStatus(SpuProcessStatus.FAILED);
            result.setErrorMessage(errorMessage);
            return result;
        }

        public static SpuProcessResult skipped(String styleCode, String reason) {
            SpuProcessResult result = new SpuProcessResult();
            result.setStyleCode(styleCode);
            result.setStatus(SpuProcessStatus.SKIPPED);
            result.setErrorMessage(reason);
            return result;
        }

        public SpuProcessResult withWarning(String warningMessage) {
            this.warningMessage = warningMessage;
            return this;
        }

        public boolean isSuccess() {
            return status == SpuProcessStatus.SUCCESS;
        }

        public boolean isFailed() {
            return status == SpuProcessStatus.FAILED;
        }

        public boolean isSkipped() {
            return status == SpuProcessStatus.SKIPPED;
        }
    }
}
