package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.BomOrderMaterialTransient;
import tech.tiangong.sdp.design.enums.BomMaterialStateEnum;
import tech.tiangong.sdp.design.mapper.BomOrderMaterialTransientMapper;
import tech.tiangong.sdp.design.vo.query.bom.BomOrderMaterialTransientQuery;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderMaterialTransientVo;

import java.util.List;
import java.util.Objects;

/**
 * bom物料_暂存表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class BomOrderMaterialTransientRepository extends BaseRepository<BomOrderMaterialTransientMapper, BomOrderMaterialTransient> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<BomOrderMaterialTransientVo> findPage(BomOrderMaterialTransientQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    /**
     * 根据暂存需求id集合查询暂存物料
     */
    public List<BomOrderMaterialTransient> listByTransientDemandIdList(List<Long> delBomMaterialDemandIds) {
        if (CollUtil.isEmpty(delBomMaterialDemandIds)) {
            return List.of();
        }
        return lambdaQuery()
                .in(BomOrderMaterialTransient::getBomMaterialDemandId, delBomMaterialDemandIds)
                .list();
    }

    /**
     * 查询暂存bomId下的物料
     */
    public List<BomOrderMaterialTransient> listByBomTransientId(Long bomTransientId) {
        if (Objects.isNull(bomTransientId)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(BomOrderMaterialTransient::getBomTransientId, bomTransientId)
                .list();
    }

    /**
     * 查询需求下找料中的物料
     */
    public BomOrderMaterialTransient getSearchingOne(Long bomMaterialDemandTransientId) {
        if (Objects.isNull(bomMaterialDemandTransientId)) {
            return null;
        }
        return lambdaQuery()
                .eq(BomOrderMaterialTransient::getBomMaterialDemandId, bomMaterialDemandTransientId)
                .eq(BomOrderMaterialTransient::getMaterialState, BomMaterialStateEnum.SEARCHING.getCode())
                .one();
    }

    public BomOrderMaterialTransient getByDemandIdAndSnapshotId(Long bomMaterialDemandId, Long materialSnapshotId) {
        if (Objects.isNull(bomMaterialDemandId) || Objects.isNull(materialSnapshotId)) {
            return null;
        }
        return lambdaQuery()
                .eq(BomOrderMaterialTransient::getBomMaterialDemandId, bomMaterialDemandId)
                .eq(BomOrderMaterialTransient::getMaterialSnapshotId, materialSnapshotId)
                .one();
    }
}
