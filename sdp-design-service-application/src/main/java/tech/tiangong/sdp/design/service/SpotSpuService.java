package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import org.springframework.web.multipart.MultipartFile;
import tech.tiangong.sdp.design.entity.SpuVisualDemandRecord;
import tech.tiangong.sdp.design.vo.dto.SpotSpuUploadPictureDto;
import tech.tiangong.sdp.design.vo.query.spot.SpotSpuQuery;
import tech.tiangong.sdp.design.vo.req.spot.*;
import tech.tiangong.sdp.design.vo.resp.prototype.VisualDemandInfoVo;
import tech.tiangong.sdp.design.vo.resp.spot.*;
import tech.tiangong.sdp.design.vo.resp.visual.SpuImageMaterial;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskListExtraVo;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * (SpotSpu)服务接口
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:41
 */
public interface SpotSpuService {

    // ============ inner 接口 =======================
    /**
     * 选款创建
     *
     * @param req 入参
     * @return SpotPickStyleAddVo
     */
    List<SpotPickStyleAddResultVo> pickStyleAdd(PickStyleAddReq req);

    /**
     * 根据spu查询现货信息
     * @param styleCode
     * @return
     */
    SpotSpuInfoVo getInfoByStyleCode(String styleCode);




    // ============== web 接口 ======================

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<SpotManagePageVo> page(SpotSpuQuery query);


    /**
     * 核价_tryOn信息查询
     *
     * @param req 查询对象
     * @return 核价与tryOn信息
     */
    List<SpotPriceTryOnVo> priceTryOnList(SpotPriceTryOnReq req);

    /**
     * 新建款号
     *
     * @param req 请求参数
     * @return 响应结果
     */
    SpotSpuSelfCreateVo selfCreate(SpotSpuSelfCreateReq req);

    /**
     * 导入SPU
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    SpotSpuImportVo importSpu(SpotSpuImportReq req);

    /**
     * excel导入
     *
     * @param inputStream excel文件
     * @return 响应结果
     */
    List<SpotSpuImportVo> excelImport(InputStream inputStream);

    /**
     * 详情
     *
     * @param styleCode spu编码
     * @return 响应结果
     */
    SpotWebDetailVo getWebDetail(String styleCode);

    /**
     * SPU信息
     *
     * @param styleCode spu编码
     * @return 响应结果
     */
    SpotSpuVo getSpuInfo(String styleCode);

    /**
     * 编辑SPU+SKC
     *
     * @param req 请求参数
     */
    void updateSpuSkc(SpotSpuUpdateReq req);

    /**
     * 批量更新货通商品属性到POP
     *
     * @param styleCodes spu编码集合
     */
    void batchUpdateProductAttribute2Pop(List<String> styleCodes);

    /**
     * 复色
     *
     * @param req 请求参数
     * @return 响应结果
     */
    SpotColorMakingVo colorMaking(SpotColorMakingReq req);

    /**
     * 取消spu
     * @param cancelSpuList 入参
     */
    void cancelSpu(List<String> cancelSpuList);

    /**
     * 根据不同的匹配方式，上传商品图或者tryOn图
     * @param req
     * @return
     */
    List<SpotSpuUploadPictureResultVo> uploadPictureByMatchingType(UploadPictureReq req);


    List<SpotSpuExportVo> spotSpuExportExcel(SpotSpuQuery query);


    List<SpotSpuUploadExcelResultVo> uploadExcelSync(UploadExcelReq req);

    /**
     * 获取复核预估核价信息
     * @param styleCode
     * @return
     */
    SpotSpuReEstimateCheckPriceVo getReEstimateCheckPriceInfo(String styleCode);
    /**
     * 更新预估核价状态
     * @param req
     */
    void updateEstimateCheckPriceStatus(UpdatePredictCheckPriceStatusReq req);

    /**
     * SPU供应商查询
     *
     * @param req 查询对象
     * @return 供应商信息
     */
    List<SpotSupplierVo> listSupplier(SpotSupplierListReq req);

    /**
     * 更新tryOn人-跳过分配发起tryOn时(tryOn人是tryOn分配时维护的)
     * @param styleCode spu
     */
    void updateTryOnUser(String styleCode);

    /**
     * 视觉弹框校验-spu编辑/复色
     *
     * @param req 入参
     * @return 异常信息集合(为空时可以发起视觉需求, 非空时不提交视觉需求)
     */
    List<String> visualCheck(SpotVisualCheckReq req);

    /**
     * 视觉弹框校验-批量提交
     *
     * @param styleCodeList spu编码集合
     * @return 异常信息集合(为空时可以发起视觉需求, 非空时不能批量提交)
     */
    List<String> batchCommitVisualCheck(List<String> styleCodeList);

    /**
     * 批量提交
     * @param req 入参
     * @return 异常信息
     */
    List<String> batchCommit(BatchCommitReq req);

    /**
     * 款式素材-视觉需求
     * @param styleCode spu
     * @return 款式图片信息
     */
    SpuImageMaterial buildSpuImageMaterial(String styleCode);

    /**
     * 发起视觉需求
     *
     * @param req 请求对象
     * @return 视觉需求id(若已创建)
     */
    Long submitVisualTask(SpotVisualSubmitReq req);

    SpuVisualDemandRecord submitSpuVisualDemandRecord(SpotVisualSubmitReq req, Long visualDemandId);
    /**
     * 查询视觉需求信息(现货)
     *
     * @param styleCode spu
     * @return 响应结果
     */
    VisualDemandInfoVo queryVisualTaskForEdit(String styleCode);

    void updateSpotSpuDetailPicture(Integer pictureType, List<String> styleCodes, Map<String, SpotSpuUploadPictureDto> fileNameMap);

    void updateSpuStateByUploadPicture(Integer pictureType, List<String> styleCodes);

    void initHisData(MultipartFile file);

    void initHisDataV2(MultipartFile file);

    /**
     * 视觉需求列表-额外信息查询
     * @param styleCodes spu编码
     * @return 响应结果
     */
    List<VisualTaskListExtraVo> visualListExtra(List<String> styleCodes);

}
