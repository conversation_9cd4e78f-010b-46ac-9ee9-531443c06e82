package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.design.entity.MaterialPurchaseFollow;
import tech.tiangong.sdp.design.mapper.MaterialPurchaseFollowMapper;
import tech.tiangong.sdp.design.vo.req.purchase.MaterialPurchaseFollowPageReq;
import tech.tiangong.sdp.design.vo.resp.material.PurchaseApplyFollowCountVO;
import tech.tiangong.sdp.design.vo.resp.purchase.MaterialPurchaseFollowPageVO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
* 面辅料采购跟进表
* <br>CreateDate August 10,2021
* <AUTHOR>
* @since 1.0
*/

@AllArgsConstructor
@Slf4j
@Repository
public class MaterialPurchaseFollowRepository extends BaseRepository<MaterialPurchaseFollowMapper, MaterialPurchaseFollow> {

    private final MaterialPurchaseFollowMapper materialPurchaseFollowMapper;

    public List<MaterialPurchaseFollowPageVO> pageList(MaterialPurchaseFollowPageReq queryDTO){
        return materialPurchaseFollowMapper.pageList(queryDTO);
    }

    public List<PurchaseApplyFollowCountVO> purchaseCountByMaterialSnapshotIds(Set<Long> materialSnapshotIdSet) {
        if (CollectionUtils.isEmpty(materialSnapshotIdSet)) {
            return Collections.emptyList();
        }
        return materialPurchaseFollowMapper.purchaseCountByMaterialSnapshotIds(materialSnapshotIdSet);
    }

    public List<MaterialPurchaseFollow> listByOrderCodes(List<String> orderCodes) {
        if (CollectionUtils.isEmpty(orderCodes)) {
            return new ArrayList<>();
        }
        return list(new QueryWrapper<MaterialPurchaseFollow>().lambda().in(MaterialPurchaseFollow::getCuttingCode, orderCodes));
    }

    public List<MaterialPurchaseFollow> listByBomMaterialIds(List<Long> bomMaterialIds) {
        if (CollectionUtils.isEmpty(bomMaterialIds)) {
            return new ArrayList<>();
        }
        return list(new QueryWrapper<MaterialPurchaseFollow>().lambda().in(MaterialPurchaseFollow::getBomMaterialId, bomMaterialIds));
    }

    public String selectLatestFabricPurchaseCode() {
        return materialPurchaseFollowMapper.selectLatestFabricPurchaseCode();
    }

    public String selectLatestAccessoriesPurchaseCode() {
        return materialPurchaseFollowMapper.selectLatestAccessoriesPurchaseCode();
    }
    public List<MaterialPurchaseFollow> listByMaterialKittingCodes(List<String> materialKittingCodes) {
        if (CollectionUtils.isEmpty(materialKittingCodes)) {
            return new ArrayList<>();
        }
        return list(new QueryWrapper<MaterialPurchaseFollow>().lambda().in(MaterialPurchaseFollow::getMaterialKittingCode, materialKittingCodes));
    }
}