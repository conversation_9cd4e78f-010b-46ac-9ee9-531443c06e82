package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.MaterialPurchaseFollowService;
import tech.tiangong.sdp.design.vo.req.purchase.CancelMaterialReq;
import tech.tiangong.sdp.design.vo.req.purchase.MaterialPurchaseFollowPageReq;
import tech.tiangong.sdp.design.vo.resp.purchase.MaterialPurchaseFollowPageVO;

/**
*
* 面辅料采购跟进-web
* <br>CreateDate August 10,2021
* <AUTHOR>
* @since 1.0
*/
@RestController
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/material/purchase")
public class MaterialPurchaseFollowController extends BaseController {

    @Autowired
    private MaterialPurchaseFollowService materialpurchaseFollowService;

    /**
     * 面辅料采购跟进列表
     *
     * @param req 入参
     * @return MaterialPurchaseFollowPageVO
     */
    @PostMapping("/page-list")
    public DataResponse<PageRespVo<MaterialPurchaseFollowPageVO>> pageList(@RequestBody MaterialPurchaseFollowPageReq req){
        return DataResponse.ok(materialpurchaseFollowService.pageList(req));
    }

    /**
     * 取消物料
     *
     * 选中的数据状态由【有效】变为【取消】；同时会会给供应链履约传数据，包括取消人，取消时间，取消原因。
     * @param req 入参
     * @return Void
     */
    @PostMapping("/cancel-material")
    @NoRepeatSubmitLock
    public DataResponse<Void> cancelMaterial(@RequestBody @Validated CancelMaterialReq req){
        materialpurchaseFollowService.cancelMaterial(req);
        return DataResponse.ok();
    }

}
