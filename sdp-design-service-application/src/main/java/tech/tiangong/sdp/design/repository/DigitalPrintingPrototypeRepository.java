package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.DigitalPrintingPrototype;
import tech.tiangong.sdp.design.enums.DigitalPrintingPushStatusEnum;
import tech.tiangong.sdp.design.mapper.DigitalPrintingPrototypeMapper;
import tech.tiangong.sdp.utils.Bool;

import java.util.Collections;
import java.util.List;

/**
 * 数码印花_SKC表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class DigitalPrintingPrototypeRepository extends BaseRepository<DigitalPrintingPrototypeMapper, DigitalPrintingPrototype> {

    public List<DigitalPrintingPrototype> listFail() {
        return lambdaQuery().eq(DigitalPrintingPrototype::getPushStatus, Bool.NO.getCode())
                .eq(DigitalPrintingPrototype::getIsDeleted, Bool.NO.getCode())
                .list();
    }
    public List<DigitalPrintingPrototype> listUnSuccessBySpuList(List<String> styleCodeList) {
        return lambdaQuery()
                .in(DigitalPrintingPrototype::getStyleCode, styleCodeList)
                .eq(DigitalPrintingPrototype::getIsDeleted, Bool.NO.getCode())
                .in(DigitalPrintingPrototype::getPushStatus, List.of(DigitalPrintingPushStatusEnum.FAIL.getCode(), DigitalPrintingPushStatusEnum.PUSHING.getCode()))
                // .and(wrapper -> wrapper
                //         .eq(DigitalPrintingPrototype::getPushStatus, 0)
                //         .or()
                //         .isNull(DigitalPrintingPrototype::getPushStatus)
                // )
                .list();
    }

    public List<DigitalPrintingPrototype> listBySpuList(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(DigitalPrintingPrototype::getStyleCode, styleCodeList).list();
    }

    public List<DigitalPrintingPrototype> listBySpu(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            return Collections.emptyList();
        }
        return lambdaQuery().eq(DigitalPrintingPrototype::getStyleCode, styleCode).list();
    }

    public void fixFailState(List<Long> printingPrototypeIdList) {
        if (CollUtil.isEmpty(printingPrototypeIdList)) {
            return;
        }
        lambdaUpdate()
                .set(DigitalPrintingPrototype::getPushStatus, DigitalPrintingPushStatusEnum.SUCCESS.getCode())
                .set(DigitalPrintingPrototype::getErrorMsg, null)
                .in(DigitalPrintingPrototype::getPrintingPrototypeId, printingPrototypeIdList)
                .update();
    }

    public void rePushUpdate(List<Long> printingPrototypeIdList) {
        if (CollUtil.isEmpty(printingPrototypeIdList)) {
            return;
        }
        lambdaUpdate()
                .in(DigitalPrintingPrototype::getPrintingPrototypeId, printingPrototypeIdList)
                .eq(DigitalPrintingPrototype::getPushStatus, DigitalPrintingPushStatusEnum.FAIL.getCode())
                .set(DigitalPrintingPrototype::getPushStatus, DigitalPrintingPushStatusEnum.PUSHING.getCode())
                .update();
    }
}
