package tech.tiangong.sdp.design.controller.open;

import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.SpotSpuService;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/openapi" + UrlVersionConstant.VERSION_V1 + "/spot-spu")
public class SpotSpuOpenController extends BaseController {

    private final SpotSpuService spotSpuService;


    /**
     * 选款创建
     *
     * @param req 入参
     * @return response
     */
   /* @NoRepeatSubmitLock
    @PostMapping("/pick-style/add")
    DataResponse<List<SpotPickStyleAddVo>> pickStyleAdd(@RequestBody @Validated PickStyleAddReq req) {
        log.info("===选款创建 req: {} ===", JSON.toJSONString(req));
        UserContent userContent = new UserContent();
        userContent.setCurrentUserId(Objects.isNull(req.getOperateId()) ? 0L : req.getOperateId());
        userContent.setCurrentUserCode("0");
        userContent.setCurrentUserName(StringUtils.isBlank(req.getOperateName()) ? "系统" : req.getOperateName());
        userContent.setTenantId(2L);
        userContent.setSystemCode("SDP");
        UserContentHolder.set(userContent);

        log.info("pickStyleAdd userContent: {}", JSON.toJSONString(userContent));
        return DataResponse.ok(spotSpuService.pickStyleAdd(req));
    }*/

}
