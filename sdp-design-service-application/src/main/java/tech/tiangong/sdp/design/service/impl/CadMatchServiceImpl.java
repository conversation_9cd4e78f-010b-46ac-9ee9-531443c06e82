package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.CadMatch;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.repository.CadMatchRepository;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.service.CadMatchService;
import tech.tiangong.sdp.design.vo.req.cadMatch.CadMatchInfoReq;
import tech.tiangong.sdp.design.vo.req.cadMatch.CadMatchOperateReq;
import tech.tiangong.sdp.design.vo.req.cadMatch.CadMatchUpdateReq;
import tech.tiangong.sdp.design.vo.resp.cadMatch.CadMatchInfoResp;
import tech.tiangong.sdp.design.vo.resp.cadMatch.CadMatchVo;
import tech.tiangong.sdp.material.enums.Bool;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description：
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CadMatchServiceImpl implements CadMatchService {

    private final CadMatchRepository cadMatchRepository;
    private final PrototypeRepository prototypeRepository;
    private final MqProducer mqProducer;

    @Override

    public CadMatchVo getById(Long cadMatchId) {
        CadMatch byId = cadMatchRepository.getById(cadMatchId);
        CadMatchVo cadMatchVo=new CadMatchVo();
        if (Objects.nonNull(byId)){
            BeanUtils.copyProperties(byId,cadMatchVo);
        }
        return cadMatchVo;
    }

    @Override
    public CadMatchVo getLatestVersionByDesignCode(String designCode) {
        CadMatch latestVersionByDesignCode = cadMatchRepository.getLatestVersionByDesignCode(designCode);
        CadMatchVo cadMatchVo=new CadMatchVo();
        if (Objects.nonNull(latestVersionByDesignCode)){
            BeanUtils.copyProperties(latestVersionByDesignCode,cadMatchVo);
        }
        return cadMatchVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operateCadMatch(CadMatchOperateReq req) {
        //如果cadStyleId不存在，则新增一个匹配关系
        CadMatch latestVersionCadMatch = cadMatchRepository.getLatestVersionByCadStyleId(req.getStyleId());
        if (Objects.isNull(latestVersionCadMatch)){
            if (!StrUtil.isNotBlank(req.getDesignCode())){
                return;
            }
            Prototype byDesignCode = prototypeRepository.getByDesignCode(req.getDesignCode());
            if (Objects.isNull(byDesignCode)){
                log.info("不存在的设计款号，请检查！");
                return;
            }
            //判断是否换绑新款式id，是
            CadMatch latestVersionByDesignCode = cadMatchRepository.getLatestVersionByDesignCode(req.getDesignCode());
            if (Objects.nonNull(latestVersionByDesignCode) && !Objects.equals(req.getStyleId(), latestVersionByDesignCode.getCadStyleId())) {
                //旧数据逻辑删除
                latestVersionByDesignCode.setIsLatest(0);
                cadMatchRepository.updateById(latestVersionByDesignCode);
            }
            CadMatch cadMatch=new CadMatch();
            BeanUtils.copyProperties(req,cadMatch);
            cadMatch.setCadMatchId(IdPool.getId());
            cadMatch.setIsLatest(1);
            cadMatch.setVersionNumber(1);
            cadMatch.setCadStyleId(req.getStyleId());
            cadMatchRepository.save(cadMatch);
        }else {
            if (Objects.equals(req.getDesignCode(), latestVersionCadMatch.getDesignCode())) {
                log.info("该设计款号已绑定该CAD款式，请勿重复绑定！");
                return;
            }
            //如果cadStyleId存在，且有新skc,则进行新skc绑定，原skc匹配数据逻辑删除
            if (StrUtil.isNotBlank(req.getDesignCode())){
                latestVersionCadMatch.setIsLatest(0);
                cadMatchRepository.updateById(latestVersionCadMatch);
                CadMatch cadMatch=new CadMatch();
                BeanUtils.copyProperties(req,cadMatch);
                cadMatch.setCadMatchId(IdPool.getId());
                cadMatch.setIsLatest(1);
                cadMatch.setVersionNumber(1);
                cadMatch.setCadStyleId(req.getStyleId());
                cadMatchRepository.save(cadMatch);
            }else {
                //如果cadStyleId存在，未传新skc,直接让原skc匹配数据逻辑删除
                latestVersionCadMatch.setIsLatest(0);
                cadMatchRepository.updateById(latestVersionCadMatch);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cadMatchUpdate(CadMatchUpdateReq req) {
        CadMatch latestVersionByDesignCode = cadMatchRepository.getLatestVersionByDesignCode(req.getDesignCode());
        if (Objects.isNull(latestVersionByDesignCode)) {
            throw new SdpDesignException("不存在的CAD文件与设计款号的绑定关系");
        }
        if (CadMatchUpdateReq.ActType.CONFIRM.getCode() == req.getActType().getCode()) {
            latestVersionByDesignCode.setIsConfirm(Bool.YES.getCode());
            cadMatchRepository.updateById(latestVersionByDesignCode);
        } else if (CadMatchUpdateReq.ActType.UPDATE.getCode() == req.getActType().getCode()) {
            //更新cad文件，版本升级
            latestVersionByDesignCode.setVersionNumber(latestVersionByDesignCode.getVersionNumber() + 1);
            latestVersionByDesignCode.setRefreshTime(LocalDateTime.now());
            cadMatchRepository.updateById(latestVersionByDesignCode);
        }
        //MQ通知核价模块
        /*
        CadInfoUpdateDto cadInfoUpdateDto = new CadInfoUpdateDto();
        cadInfoUpdateDto.setDesignCode(req.getDesignCode());
        cadInfoUpdateDto.setUpdateType(req.getActType().getCode());
        MqMessageReq messageReq = MqMessageReq.build(
                MqBizTypeEnum.CAD_MATCH_INFO_UPDATE,
                DesignMqConstant.SDP_DESIGN_CAD_UPDATE_EXCHANGE,
                JSON.toJSONString(cadInfoUpdateDto));
        log.info("===cad文件变更【换绑/更新】后发送mq: mqDto: [{}] ===", JSON.toJSONString(cadInfoUpdateDto));
        mqProducer.sendOnAfterCommit(messageReq);

        log.info("===cad文件变更【换绑/更新】后_mq消息发送完毕===");

         */
    }

    @Override
    public CadMatchInfoResp cadMatchInfo(CadMatchInfoReq req) {
        List<CadMatch> cadMatchList = cadMatchRepository.getLatestVersionByDesignCodes(req.getDesignCodes());
        List<CadMatchInfoResp.CadInfo> cadInfoList = cadMatchList.stream().map(item -> {
            CadMatchInfoResp.CadInfo cadInfo = new CadMatchInfoResp.CadInfo();
            BeanUtils.copyProperties(item, cadInfo);
            return cadInfo;
        }).collect(Collectors.toList());
        CadMatchInfoResp cadMatchInfoResp = new CadMatchInfoResp();
        cadMatchInfoResp.setCadInfoList(cadInfoList);
        return cadMatchInfoResp;
    }
}
