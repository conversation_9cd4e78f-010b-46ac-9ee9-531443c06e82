package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.util.Json;
import com.alibaba.fastjson.JSON;
import com.yibuyun.scm.common.dto.accessories.response.CategoryTreeMapVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSkuVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import com.yibuyun.scm.common.enums.houliu.DemandTypeEnum;
import com.yibuyun.scm.common.enums.houliu.OppositeColorEnum;
import com.zjkj.scf.bundle.common.dto.ordermaterial.enums.DemandCategory1Enum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.converter.BomOrderMaterialConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.BomDemandSubmitHandleService;
import tech.tiangong.sdp.design.service.BomMaterialCommonService;
import tech.tiangong.sdp.design.service.DesignRemarksService;
import tech.tiangong.sdp.design.service.MaterialSnapshotService;
import tech.tiangong.sdp.design.vo.dto.bom.BomHouliuDemandDeleteDto;
import tech.tiangong.sdp.design.vo.dto.bom.BomMaterialInfoDto;
import tech.tiangong.sdp.design.vo.req.bom.BomCraftSubmitHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomDemandSubmitHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderUpdateV3Req;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandSaveV3Req;
import tech.tiangong.sdp.design.vo.req.material.MaterialSnapshotCreateReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;
import tech.tiangong.sdp.design.vo.req.zj.demand.AccessoryDemandCreateOpenV2Req;
import tech.tiangong.sdp.design.vo.resp.zj.demand.AccessoryDemandCreateOpenV2Resp;
import tech.tiangong.sdp.utils.Bool;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static tech.tiangong.sdp.design.remote.DemandRemoteHelper.MATERIAL_DEMAND_ID_NAME;

;

/**
 *
 * bom提交 需求处理服务
 * <AUTHOR>
 * @date 2022/11/17 11:00
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BomDemandSubmitHandleServiceImpl implements BomDemandSubmitHandleService {

    private final BomMaterialDemandRepository materialDemandRepository;
    private final BomMaterialDemandTransientRepository materialDemandTransientRepository;
    private final BomOrderMaterialRepository orderMaterialRepository;
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final BomOrderMaterialTransientRepository materialTransientRepository;
    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final CraftDemandInfoTransientRepository craftDemandInfoTransientRepository;
    private final BomOrderTransientRepository bomOrderTransientRepository;
    private final PrototypeRepository prototypeRepository;
    private final DesignRemarksService designRemarksService;
    private final BomMaterialCommonService bomMaterialCommonService;
    private final MaterialSnapshotService materialSnapshotService;
    private final MqProducer mqProducer;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;


    private static final String DEMAND_CLOSED_REASON = "设计师删除";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftSubmitHandleReq firstSubmitNoTransient(BomDemandSubmitHandleReq req) {
        // 首次提交,只会有新增的需求,维护到原表,不升版本
        BomOrder bomOrder = req.getBomOrder();
        List<BomOrderUpdateV3Req.AddBomMaterialDemandReq> demandAddReqList = req.getAddBomMaterialDemandList();
        BomCraftSubmitHandleReq craftSubmitHandleReq = BomCraftSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .craftSourceTypeEnum(CraftSourceTypeEnum.DEMAND)
                .addCraftDemandList(Collections.emptyList())
                .delCraftDemandIds(Collections.emptySet())
                .oldNewMaterialIdMap(Collections.emptyMap())
                .oldTransientCraftMap(Collections.emptyMap())
                .build();
        if (CollUtil.isEmpty(demandAddReqList)) {
            return craftSubmitHandleReq;
        }
        //收集新增的工艺
        List<CraftDemandSaveV3Req> addCraftReqList = new LinkedList<>();

        //1, 新增需求处理, 新增一个无物料快照的bom物料对象
        this.addDemandHandle(bomOrder, demandAddReqList, addCraftReqList);

        //2, 处理工艺
        craftSubmitHandleReq.setAddCraftDemandList(addCraftReqList);
        return craftSubmitHandleReq;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftSubmitHandleReq reSubmitNoTransient(BomDemandSubmitHandleReq req) {
        BomOrder oldBomOrder = req.getBomOrder();
        BomOrder newBomOrder = req.getNewBomOrder();

        //收集新增/删除的工艺
        List<CraftDemandSaveV3Req> addCraftReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);
        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();

        //注: 要先处理需求更新, 保证更新的需求新增到新bom中, 创建需求时需要查询对色的物料信息

        //1, 需求更新:复制旧bom需求(删除的不用)与需求修的最新物料, 更新数据后,新增到新版本bom下;
        this.updateDemandHandle4ReSubmit(req, oldBomOrder, newBomOrder, addCraftReqList, delCraftIdSet, oldNewMaterialIdMap);

        //2, 新增需求处理, 新增一个无物料快照的bom物料对象
        this.addDemandHandle(newBomOrder, req.getAddBomMaterialDemandList(), addCraftReqList);

        //3, 需求删除: 删除的需求不带到新bom中, 但需要收集需求下所有物料未关闭的工艺,提交给工艺处理; 通知履约关闭需求;
        Set<Long> delCraftIdByDemand = this.delDemandHandle(oldBomOrder, req.getDelBomMaterialDemandIds());
        log.info(" ===== bom无暂存-再次提交, 删除需求下的工艺: {}; bomId:{} =====", Json.serialize(delCraftIdByDemand), newBomOrder.getBomId());
        if (CollUtil.isNotEmpty(delCraftIdByDemand)) {
            delCraftIdSet.addAll(delCraftIdByDemand);
        }

        //4, 处理工艺
        return BomCraftSubmitHandleReq.builder()
                .bomOrder(oldBomOrder)
                .newBomOrderId(newBomOrder.getBomId())
                .craftSourceTypeEnum(CraftSourceTypeEnum.DEMAND)
                .addCraftDemandList(addCraftReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldTransientCraftMap(Collections.emptyMap())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftSubmitHandleReq firstSubmitWithTransient(BomDemandSubmitHandleReq req) {
        // 有暂存-首次提交 -需求处理
        BomOrder bomOrder = req.getBomOrder();
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomOrder.getBomId());

        //已经再次暂存过了

        //收集新增/删除的工艺
        List<CraftDemandSaveV3Req> addCraftReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);
        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();
        Map<Long, CraftDemandInfoTransient> oldTransientCraftMap = new HashMap<>();

        //2, 将暂存表中新增与更新的需求与物料 拷贝到新bom的原表中
        this.copyDemandAndMaterialFromTransient(bomOrder, transientBom, addCraftReqList, delCraftIdSet, oldNewMaterialIdMap, oldTransientCraftMap, req);

        //3, 处理工艺: 已经重新封装了工艺信息, 模拟为无暂存的首次提交参数, 调用工艺首次提交方法处理
        return BomCraftSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .craftSourceTypeEnum(CraftSourceTypeEnum.DEMAND)
                .addCraftDemandList(addCraftReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldTransientCraftMap(Collections.emptyMap())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftSubmitHandleReq reSubmitWithTransient(BomDemandSubmitHandleReq req) {
        // 有暂存-再次提交 -需求处理
        BomOrder bomOrder = req.getBomOrder();
        BomOrder newBomOrder = req.getNewBomOrder();
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomOrder.getBomId());

        //已经执行了再次暂存

        //收集新增/删除的工艺
        List<CraftDemandSaveV3Req> addCraftReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);
        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();
        Map<Long, CraftDemandInfoTransient> oldTransientCraftMap = new HashMap<>();

        //1, 将暂存表中新增与更新的需求与物料 拷贝到新bom的原表中
        this.copyDemandAndMaterialFromTransient(newBomOrder, transientBom, addCraftReqList, delCraftIdSet, oldNewMaterialIdMap, oldTransientCraftMap, req);

        //2, 处理工艺: 已经重新封装了工艺信息, 模拟为无暂存的再次提交参数, 调用工艺首次提交方法处理
        return BomCraftSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .craftSourceTypeEnum(CraftSourceTypeEnum.DEMAND)
                .addCraftDemandList(addCraftReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldTransientCraftMap(Collections.emptyMap())
                .build();
    }


    private void copyDemandAndMaterialFromTransient(BomOrder bomOrder,
                                                    BomOrderTransient transientBom,
                                                    List<CraftDemandSaveV3Req> addCraftReqList,
                                                    Set<Long> delCraftIdSet,
                                                    Map<Long, Long> oldNewMaterialIdMap,
                                                    Map<Long, CraftDemandInfoTransient> oldTransientCraftMap,
                                                    BomDemandSubmitHandleReq req
                                                    ) {

        Long bomTransientId = transientBom.getBomTransientId();
        //1, 先从暂存表中获取最终增删改的需求
        List<BomMaterialDemandTransient> finalDemandTransientList = materialDemandTransientRepository.listByBomTransientId(bomTransientId);
        if (CollUtil.isEmpty(finalDemandTransientList)) {
            return;
        }
        //2, 找出增删改的需求
        List<BomMaterialDemandTransient> addDemandTransientList = new LinkedList<>();
        List<BomMaterialDemandTransient> updateDemandTransientList = new LinkedList<>();
        List<BomMaterialDemandTransient> delDemandTransientList = new LinkedList<>();
        List<Long> delOriginDemandId = new LinkedList<>();
        finalDemandTransientList.forEach(item -> {
            //暂存新增的需求(要将需求状态更新为已提交,因为暂存时需求时)
            if (Objects.isNull(item.getOriginDemandId())) {
                addDemandTransientList.add(item);
                return;
            }
            //删除的需求
            if (Objects.nonNull(item.getOriginDemandId())
                    && Objects.equals(item.getDemandState(), BomMaterialStateEnum.CLOSED.getCode())) {
                delDemandTransientList.add(item);
                delOriginDemandId.add(item.getOriginDemandId());
                return;
            }
            //更新的需求
            updateDemandTransientList.add(item);
        });

        //3, 将需求从暂存表复制到bom原表中
        List<BomMaterialDemand> addDemandList = this.getAddCopyDemandList(bomOrder, addDemandTransientList, updateDemandTransientList);

        //4, 查出需求下的物料
        List<BomOrderMaterialTransient> finalMaterialTransientList = materialTransientRepository.listByBomTransientId(transientBom.getBomTransientId()).stream()
                .filter(item -> Objects.nonNull(item.getBomMaterialDemandId())).collect(Collectors.toList());

        //5, 将暂存表中新增更新的物料复制到原表, 并同步物料快照给履约
        List<BomOrderMaterial> addMaterialList = this.getAddCopyMaterials(bomOrder, finalMaterialTransientList, oldNewMaterialIdMap);

        //6, 对需求物料下对应的增删工艺进行封装 (因为工艺已经再次暂存过,得到最终的数据, 根据物料id查询工艺暂存表即可)
        //注: 物料处理的时候已经收集过一遍了, 需求处理时不需要再次收集
        // this.resetCraftReqInfo(addCraftReqList, delCraftIdSet, oldTransientCraftMap, finalMaterialTransientList);

        //7, 先保存物料到原表(推送需求时需要根据物料名称获取对应的对色物料id)
        if (CollUtil.isNotEmpty(addMaterialList)) {
            orderMaterialRepository.saveBatch(addMaterialList);
        }

        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        SdpDesignException.notNull(prototype, "skc不存在!:{}", bomOrder.getDesignCode());

        //封装创建履约需求req
        List<AccessoryDemandCreateOpenV2Req.AccessoryDemandInfo> demandCreateReqList = new LinkedList<>();
        this.buildSupplyDemandCreateReq(addDemandList, demandCreateReqList, bomOrder, prototype);

        if (CollUtil.isNotEmpty(demandCreateReqList)) {
            //调用履约接口创建需求
            AccessoryDemandCreateOpenV2Req demandBatchCreateReq = new AccessoryDemandCreateOpenV2Req();
            demandBatchCreateReq.setAccessoryDemandInfos(demandCreateReqList);
            demandBatchCreateReq.setBizChannel(prototype.getBizChannel());
            List<AccessoryDemandCreateOpenV2Resp.DemandInfo> demandInfoRespList = zjDesignRemoteHelper.accessoryDemandCreate(demandBatchCreateReq);

            //需求创建成功后, 维护履约需求id, 需求编号, 需求创建时间; 将需求信息添加到工艺req对象中
            this.resetDemandAndCraftAddInfo(addDemandList, addCraftReqList, demandInfoRespList);
        }

        //8, 保存需求到原表
        if (CollUtil.isNotEmpty(addDemandList)) {
            materialDemandRepository.saveBatch(addDemandList);
        }

        //9, 物料备注处理
        this.handleMaterialRemark(bomOrder, req, addMaterialList);

        //10, 通知履约关闭需求: 有暂存,再次提交删除的需求可能是暂存的需求, 只需要推送已创建的需求
        this.sendBomHouLiuDemandDeleteMqFanout(bomOrder, delOriginDemandId);

        //更新需求状态为删除
        this.updateDemandClose(delOriginDemandId);

    }

    private void handleMaterialRemark(BomOrder bomOrder,
                                      BomDemandSubmitHandleReq req,
                                      List<BomOrderMaterial> addMaterialList) {
        if (CollUtil.isEmpty(addMaterialList)) {
            return;
        }
        //物料备注处理: 由于先调用了再次暂存,备注中的bzId使用的是暂存bomId,要重新生成一份
        Map<String, String> nameRemarkMap = new HashMap<>();
        List<BomOrderUpdateV3Req.AddBomMaterialDemandReq> addDemandList = req.getAddBomMaterialDemandList();
        List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateDemandList = req.getUpdateBomMaterialDemandList();

        //新增需求下 物料的备注
        if (CollUtil.isNotEmpty(addDemandList)) {
            addDemandList.forEach(demandReq -> {
                BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq = demandReq.getMaterialAddReq();
                if (Objects.nonNull(materialAddReq) && StringUtils.isNotBlank(materialAddReq.getRemark())) {
                    Map<String, String> remarkMap = new HashMap<>();
                    remarkMap.put(materialAddReq.getPrototypeMaterialName(), materialAddReq.getRemark());
                    nameRemarkMap.putAll(remarkMap);
                }
            });
        }

        //更新需求下 物料的备注
        if (CollUtil.isNotEmpty(updateDemandList)) {
            updateDemandList.forEach(demandReq -> {
                List<BomOrderUpdateV3Req.DemandMaterialUpdateReq> updateReqList = demandReq.getMaterialUpdateReqList();
                List<BomOrderUpdateV3Req.DemandMaterialAddReq> addReqList = demandReq.getMaterialAddReqList();
                //更新物料
                if (CollUtil.isNotEmpty(updateReqList)) {
                    BomOrderUpdateV3Req.DemandMaterialUpdateReq updateReq = updateReqList.get(0);
                    if (Objects.nonNull(updateReq) && StringUtils.isNotBlank(updateReq.getRemark())) {
                        Map<String, String> remarkMap = new HashMap<>();
                        remarkMap.put(updateReq.getPrototypeMaterialName(), updateReq.getRemark());
                        nameRemarkMap.putAll(remarkMap);
                    }
                }
                //更换物料
                if (CollUtil.isNotEmpty(addReqList)) {
                    BomOrderUpdateV3Req.DemandMaterialAddReq addReq = addReqList.get(0);
                    if (Objects.nonNull(addReq) && StringUtils.isNotBlank(addReq.getRemark())) {
                        Map<String, String> remarkMap = new HashMap<>();
                        remarkMap.put(addReq.getPrototypeMaterialName(), addReq.getRemark());
                        nameRemarkMap.putAll(remarkMap);
                    }
                }
            });
        }

        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        if (CollUtil.isNotEmpty(addMaterialList)) {
            addMaterialList.forEach(item -> {
                String remark = nameRemarkMap.get(item.getPrototypeMaterialName());
                if (StringUtils.isNotBlank(remark)) {
                    designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomOrder.getBomId(), item.getBomMaterialId(), remark));
                }
            });
        }
        this.addDesignRemark(designRemarksReqList);
    }

    private void resetCraftReqInfo(List<CraftDemandSaveV3Req> addCraftReqList, Set<Long> delCraftIdSet, Map<Long, CraftDemandInfoTransient> oldTransientCraftMap, List<BomOrderMaterialTransient> finalMaterialTransientList) {
        List<CraftDemandInfoTransient> addCraftTransientList = new LinkedList<>();

        Map<Long, BomOrderMaterialTransient> materialTransientMap = finalMaterialTransientList.stream()
                .collect(Collectors.toMap(BomOrderMaterialTransient::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
        List<Long> materialTransientIdList = finalMaterialTransientList.stream().map(BomOrderMaterialTransient::getBomMaterialId).collect(Collectors.toList());
        List<CraftDemandInfoTransient> craftDemandInfoTransients = craftDemandInfoTransientRepository.listByTransientMaterialIds(materialTransientIdList);
        if (CollUtil.isNotEmpty(craftDemandInfoTransients)) {
            craftDemandInfoTransients.forEach(transientCraft -> {
                //暂存新增的工艺
                if (Objects.isNull(transientCraft.getOriginCraftDemandId())) {
                    addCraftTransientList.add(transientCraft);
                    return;
                }
                //删除的工艺
                if (Objects.nonNull(transientCraft.getOriginCraftDemandId())) {
                    if (Objects.equals(transientCraft.getState(), CraftDemandStateEnum.CLOSED.getCode())) {
                        //收集删除的原工艺id, 提交给工艺环节处理
                        delCraftIdSet.add(transientCraft.getOriginCraftDemandId());
                    }
                    //收集原有工艺id与暂存工艺的关系
                    oldTransientCraftMap.put(transientCraft.getOriginCraftDemandId(), transientCraft);
                }
            });

            //封装暂存新增的工艺req: 新增工艺转换为CraftDemandSaveV3Req
            addCraftTransientList.forEach(item -> {
                CraftDemandSaveV3Req craftReq = new CraftDemandSaveV3Req();
                BeanUtils.copyProperties(item, craftReq);
                craftReq.setCraftDemandTransientId(item.getCraftDemandId());
                BomOrderMaterialTransient materialTransient = materialTransientMap.get(item.getBomMaterialId());
                //升版本时, 原物料id与暂存物料id对应
                craftReq.setBomMaterialId(materialTransient.getBomMaterialId());
                craftReq.setMaterialSnapshotId(materialTransient.getMaterialSnapshotId());
                addCraftReqList.add(craftReq);
            });
        }
    }

    private List<BomOrderMaterial> getAddCopyMaterials(BomOrder newBomOrder,
                                                       List<BomOrderMaterialTransient> finalMaterialTransientList,
                                                       Map<Long, Long> oldNewMaterialIdMap) {
        //需求下对应物料的增删改
        List<BomOrderMaterialTransient> addTransientMaterialList = new LinkedList<>();
        List<BomOrderMaterialTransient> updateTransientMaterialList = new LinkedList<>();
        List<BomOrderMaterialTransient> delTransientMaterialList = new LinkedList<>();
        finalMaterialTransientList.forEach(item -> {
            //暂存新增的物料
            if (Objects.isNull(item.getOriginMaterialId()) && !Objects.equals(item.getMaterialState(), BomMaterialStateEnum.CLOSED.getCode())) {
                addTransientMaterialList.add(item);
                return;
            }
            //删除的物料
            if (Objects.equals(item.getMaterialState(), BomMaterialStateEnum.CLOSED.getCode())) {
                delTransientMaterialList.add(item);
                return;
            }
            //更新的物料
            updateTransientMaterialList.add(item);
        });
        //5, 从暂存表复制到bom原表中
        List<BomOrderMaterial> addMaterialList = new LinkedList<>();
        //新增物料复制
        addTransientMaterialList.forEach(item -> {
            //物料id使用暂存表中的物料id
            BomOrderMaterial material = new BomOrderMaterial();
            BeanUtils.copyProperties(item, material);
            material.setBomId(newBomOrder.getBomId());
            addMaterialList.add(material);
        });

        //更新物料复制(按创建时间顺序处理, 包装oldNewMaterialIdMap中拿到的是需求下最新创建的物料)
        updateTransientMaterialList.stream()
                .sorted(Comparator.comparing(BomOrderMaterialTransient::getCreatedTime))
                .forEach(item -> {
                    //物料id使用暂存表中的物料id
                    BomOrderMaterial material = new BomOrderMaterial();
                    BeanUtils.copyProperties(item, material);
                    material.setBomId(newBomOrder.getBomId());
                    addMaterialList.add(material);
                    //维护新旧物料id映射
                    oldNewMaterialIdMap.put(item.getOriginMaterialId(), item.getBomMaterialId());
                });

        //新增物料推送 物料快照给履约
        // bomMaterialCommonService.createMaterialToSupplyChain(newBomOrder, addTransientMaterialList);

        //更新旧bom物料的最新采购周期(暂存表中已维护了最新的采购周期信息)
        List<BomOrderMaterial> oldMaterialUpdateList = updateTransientMaterialList.stream()
                .filter(item -> Objects.nonNull(item.getOriginMaterialId()))
                .map(item -> BomOrderMaterial.builder()
                        .bomMaterialId(item.getOriginMaterialId())
                        .samplePurchasingCycle(item.getSamplePurchasingCycle())
                        .bulkPurchasingCycle(item.getBulkPurchasingCycle())
                        .build())
                .collect(Collectors.toList());
        this.updateBomMaterialCycle(oldMaterialUpdateList);

        return addMaterialList;
    }

    private List<BomMaterialDemand> getAddCopyDemandList(BomOrder newBomOrder, List<BomMaterialDemandTransient> addDemandTransientList, List<BomMaterialDemandTransient> updateDemandTransientList) {
        List<BomMaterialDemand> addDemandList = new LinkedList<>();
        //新增需求复制
        addDemandTransientList.forEach(item -> {
            //物料id使用暂存表中的物料id
            BomMaterialDemand demand = new BomMaterialDemand();
            BeanUtils.copyProperties(item, demand);
            demand.setBomId(newBomOrder.getBomId());
            //设置需求状态为已提交
            demand.setDemandState(BomDemandStateEnum.SUBMIT.getCode());
            addDemandList.add(demand);
        });
        //更新需求复制
        updateDemandTransientList.forEach(item -> {
            //物料id使用暂存表中的物料id
            BomMaterialDemand demand = new BomMaterialDemand();
            BeanUtils.copyProperties(item, demand);
            demand.setBomId(newBomOrder.getBomId());
            addDemandList.add(demand);
        });
        return addDemandList;
    }

    /**
     * 无暂存,再次提交删除需求
     */
    private Set<Long> delDemandHandle(BomOrder bomOrder, List<Long> delDemandIdList) {
        Set<Long> craftIdSet = new HashSet<>();

        if (CollUtil.isEmpty(delDemandIdList)) {
            return craftIdSet;
        }

        //通知履约关闭需求: 无暂存,再次提交删除的需求都是已推送履约的需求
        this.sendBomHouLiuDemandDeleteMqFanout(bomOrder, delDemandIdList);

        //更新需求状态为删除
        this.updateDemandClose(delDemandIdList);

        List<BomOrderMaterial> delMaterialList = orderMaterialRepository.listByDemandIds(delDemandIdList);
        if (CollUtil.isEmpty(delMaterialList)) {
            return craftIdSet;
        }
        List<Long> delMaterialIdList = delMaterialList.stream().map(BomOrderMaterial::getBomMaterialId).collect(Collectors.toList());
        //查询需求下未关闭的工艺id
        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getListByBomMaterialIds(delMaterialIdList, CraftDemandStateEnum.SUBMIT.getCode());
        if (CollUtil.isEmpty(craftDemandInfoList)) {
            return craftIdSet;
        }
        return craftDemandInfoList.stream().map(CraftDemandInfo::getCraftDemandId).collect(Collectors.toSet());
    }

    private void updateDemandClose(List<Long> delDemandIdList) {
        if (CollUtil.isEmpty(delDemandIdList)) {
            return;
        }
        List<BomMaterialDemand> delDemandEoList = materialDemandRepository.listByIds(delDemandIdList);
        if (CollUtil.isEmpty(delDemandEoList)) {
            return;
        }
        List<BomMaterialDemand> updateDemandList = delDemandEoList.stream().map(item -> {
            BomMaterialDemand updateDemand = new BomMaterialDemand();
            updateDemand.setBomMaterialDemandId(item.getBomMaterialDemandId());
            updateDemand.setDemandState(BomDemandStateEnum.CLOSED.getCode());
            return updateDemand;
        }).collect(Collectors.toList());
        materialDemandRepository.updateBatchById(updateDemandList);
    }


    private void sendBomHouLiuDemandDeleteMqFanout(BomOrder bomOrder, List<Long> delDemandIdList) {
        if (CollUtil.isEmpty(delDemandIdList)) {
            log.info("=== bom单提交无删除的已推送的需求,bomId:{} ===", bomOrder.getBomId());
            return;
        }

        //查询履约需求id
        List<BomMaterialDemand> delDemandEoList = materialDemandRepository.listByIds(delDemandIdList);
        if (CollUtil.isEmpty(delDemandEoList)) {
            log.info("=== bom单提交删除需求, 需求信息不存在,bomId:{}; delDemandIdList:{} ===", bomOrder.getBomId(), Json.serialize(delDemandIdList));
            return;
        }
        BomHouliuDemandDeleteDto deleteDto = new BomHouliuDemandDeleteDto();

        LocalDateTime closedTime = LocalDateTime.now();

        //封装需求删除dto
        List<BomHouliuDemandDeleteDto.DeleteDemandInfo> deleteDemandInfoList = delDemandEoList.stream().distinct().map(item -> {
            BomHouliuDemandDeleteDto.DeleteDemandInfo delInfo = new BomHouliuDemandDeleteDto.DeleteDemandInfo();
            delInfo.setDemandId(item.getSupplyChainDemandId());
            delInfo.setExtBomMaterialDemandId(item.getBomMaterialDemandId());
            //bom中现有需求都是辅料
            delInfo.setDemandCategory1(DemandCategory1Enum.ACCESSORIES);
            delInfo.setCloseReason(DEMAND_CLOSED_REASON);
            delInfo.setClosedTime(closedTime);
            return delInfo;
        }).collect(Collectors.toList());

        deleteDto.setHandledDTOs(deleteDemandInfoList);

        MqMessageReq messageReq = MqMessageReq.build(
                MqBizTypeEnum.BOM_HOULIU_DEMAND_DELETE,
                DesignMqConstant.BOM_HOULIU_DEMAND_DELETE_EXCHANGE,
                JSON.toJSONString(deleteDto));
        // final Map<String, String> messageHeaders = new HashMap<>(16);
        // messageHeaders.put("acs_code", "HOULIU_ACCOUNT_BE_REMOVED_DEMAND");

        log.info("===bom单删除需求-发送mq: mqDto:{} ===", JSON.toJSONString(deleteDto));
        mqProducer.sendOnAfterCommit(messageReq);

        log.info("===bom单删除需求-mq消息发送完毕===");
    }

    private void updateDemandHandle4ReSubmit(BomDemandSubmitHandleReq req,
                                             BomOrder oldBomOrder,
                                             BomOrder newBomOrder,
                                             List<CraftDemandSaveV3Req> addCraftReqList,
                                             Set<Long> delCraftIdSet,
                                             Map<Long, Long> oldNewMaterialIdMap) {
        List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateBomMaterialDemandList = req.getUpdateBomMaterialDemandList();
        if (CollUtil.isEmpty(updateBomMaterialDemandList)) {
            return;
        }
        //获取旧bom中已提交的需求信息(包含履约删除的)
        List<Integer> demandSubmitStateList = BomDemandStateEnum.submitStateList().stream().map(BomDemandStateEnum::getCode).collect(Collectors.toList());
        List<BomMaterialDemand> oldDemandList = materialDemandRepository.listByBomIdAndState(oldBomOrder.getBomId(), demandSubmitStateList);
        Map<Long, BomMaterialDemand> oldDemandMap = oldDemandList.stream().collect(Collectors.toMap(BomMaterialDemand::getBomMaterialDemandId, Function.identity(), (k1, k2) -> k1));
        //需求中最新的物料Map
        Map<Long, BomOrderMaterial> latestMaterialMap = this.getLatestMaterialMap(oldDemandList);

        List<BomMaterialDemand> newBomDemandList = new LinkedList<>();
        List<BomOrderMaterial> newBomMaterialList = new LinkedList<>();
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        List<BomOrderMaterial> oldMaterialUpdateList = new LinkedList<>();

        //更换新增的物料
        List<BomOrderUpdateV3Req.DemandMaterialAddReq> replaceMaterialList = new LinkedList<>();

        //有匹配回复的需求更新,查询对应的辅料信息
        BomMaterialInfoDto updateMaterialInfoDto = this.getUpdateMaterialInfoDto(updateBomMaterialDemandList);

        updateBomMaterialDemandList.forEach(updateDemand -> {
            //直接更新物料, 不是更换物料
            List<BomOrderUpdateV3Req.DemandMaterialUpdateReq> materialUpdateReqList = updateDemand.getMaterialUpdateReqList();
            if (CollUtil.isNotEmpty(materialUpdateReqList) && Objects.nonNull(materialUpdateReqList.get(0))) {
                BomOrderUpdateV3Req.DemandMaterialUpdateReq materialUpdateReq = materialUpdateReqList.get(0);
                //复制需求信息到新bom中
                BomMaterialDemand oldDemand = oldDemandMap.get(updateDemand.getBomMaterialDemandId());
                long newBomDemandId = IdPool.getId();
                BomMaterialDemand newDemand = new BomMaterialDemand();
                BeanUtils.copyProperties(oldDemand, newDemand);
                newDemand.setBomMaterialDemandId(newBomDemandId);
                newDemand.setBomId(newBomOrder.getBomId());
                newDemand.setPrototypeMaterialName(updateDemand.getPrototypeMaterialName());
                newDemand.setColorMatchMaterialState(updateDemand.getColorMatchMaterialState());
                newDemand.setColorMatchMaterialName(this.getDemandColorMatchName(updateDemand));

                //只是更新物料, 将更新后的物料复制到新bom中,收集增删的工艺
                if (CollUtil.isNotEmpty(latestMaterialMap) && Objects.nonNull(latestMaterialMap.get(oldDemand.getBomMaterialDemandId()))) {
                    //复制物料
                    BomOrderMaterial oldMaterial = latestMaterialMap.get(oldDemand.getBomMaterialDemandId());
                    long newBomMaterialId = IdPool.getId();

                    BomOrderMaterial newBomMaterial = new BomOrderMaterial();
                    BeanUtils.copyProperties(oldMaterial, newBomMaterial);
                    newBomMaterial.setBomMaterialId(newBomMaterialId);
                    newBomMaterial.setBomMaterialDemandId(newBomDemandId);
                    newBomMaterial.setBomId(newBomOrder.getBomId());
                    newBomMaterial.setPartUse(materialUpdateReq.getPartUse());
                    newBomMaterial.setCuttingMethod(materialUpdateReq.getCuttingMethod());
                    newBomMaterial.setPrototypeMaterialName(materialUpdateReq.getPrototypeMaterialName());
                    newBomMaterial.setColorMatchMaterialState(materialUpdateReq.getColorMatchMaterialState());
                    newBomMaterial.setColorMatchMaterialName(this.getColorMatchName(materialUpdateReq.getColorMatchMaterialState(), materialUpdateReq.getColorMatchMaterialName()));
                    //最新的价格与采购周期信息
                    this.resetMaterialInfo(updateMaterialInfoDto, materialUpdateReq, newBomMaterial);

                    //旧bom更新的物料也维护最新的采购周期信息
                    oldMaterial.setSamplePurchasingCycle(newBomMaterial.getSamplePurchasingCycle());
                    oldMaterial.setBulkPurchasingCycle(newBomMaterial.getBulkPurchasingCycle());
                    oldMaterialUpdateList.add(oldMaterial);

                    newBomMaterialList.add(newBomMaterial);
                    oldNewMaterialIdMap.put(oldMaterial.getBomMaterialId(), newBomMaterialId);

                    //需求关联最新的物料
                    newDemand.setLatestBomMaterialId(newBomMaterialId);
                    newBomDemandList.add(newDemand);

                    //删除的工艺
                    Set<Long> delCraftDemandIds = materialUpdateReq.getDelCraftDemandIds();
                    if (CollectionUtil.isNotEmpty(delCraftDemandIds)) {
                        delCraftIdSet.addAll(delCraftDemandIds);
                    }
                    //新增的二次工艺
                    List<CraftDemandSaveV3Req> addCraftDemandReqList = materialUpdateReq.getAddCraftDemandList();
                    if (CollectionUtil.isNotEmpty(addCraftDemandReqList)) {
                        //关联bom物料id, 供应链需求信息
                        addCraftDemandReqList.forEach(item -> {
                            item.setBomMaterialId(newBomMaterialId);
                            item.setBomMaterialDemandId(newDemand.getBomMaterialDemandId());
                            item.setSupplyChainDemandId(newDemand.getSupplyChainDemandId());
                            item.setSupplyChainDemandCode(newDemand.getSupplyChainDemandCode());
                        });
                        addCraftReqList.addAll(addCraftDemandReqList);
                    }
                    //物料备注
                    if (StringUtils.isNotBlank(materialUpdateReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(newBomOrder.getBomId(), newBomMaterialId, materialUpdateReq.getRemark()));
                    }
                }
            }//更换物料的场景
            else {
                List<BomOrderUpdateV3Req.DemandMaterialAddReq> materialAddReqList = updateDemand.getMaterialAddReqList();
                if (CollUtil.isNotEmpty(materialAddReqList) && Objects.nonNull(materialAddReqList.get(0))) {
                    BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq = materialAddReqList.get(0);
                    //收集后统一处理
                    replaceMaterialList.add(materialAddReq);
                }
            }
        });

        //需求更换物料处理
        if (CollUtil.isNotEmpty(replaceMaterialList)) {
            //根据skuId查询物料信息
            BomMaterialInfoDto materialInfoDto = this.getMaterialInfoDto(replaceMaterialList);

            //查询被替换的原有物料
            List<Long> replaceMaterialIdList = replaceMaterialList.stream()
                    .map(BomOrderUpdateV3Req.DemandMaterialAddReq::getBomMaterialIdChange).collect(Collectors.toList());
            Map<Long, BomOrderMaterial> replaceMaterialMap = orderMaterialRepository.listByIds(replaceMaterialIdList).stream()
                    .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));

            updateBomMaterialDemandList.forEach(updateDemand -> {
                //可能是物料更换, 新增一个物料, 需求关联最新的物料
                List<BomOrderUpdateV3Req.DemandMaterialAddReq> materialAddReqList = updateDemand.getMaterialAddReqList();

                if (CollUtil.isNotEmpty(materialAddReqList)&& Objects.nonNull(materialAddReqList.get(0))
                        && Objects.nonNull(materialAddReqList.get(0).getBomMaterialIdChange())) {
                    BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq = materialAddReqList.get(0);
                    //复制需求信息到新bom中
                    BomMaterialDemand oldDemand = oldDemandMap.get(updateDemand.getBomMaterialDemandId());
                    long newBomDemandId = IdPool.getId();
                    BomMaterialDemand newDemand = new BomMaterialDemand();
                    BeanUtils.copyProperties(oldDemand, newDemand);
                    newDemand.setBomMaterialDemandId(newBomDemandId);
                    newDemand.setBomId(newBomOrder.getBomId());
                    newDemand.setPrototypeMaterialName(updateDemand.getPrototypeMaterialName());
                    newDemand.setColorMatchMaterialState(updateDemand.getColorMatchMaterialState());
                    newDemand.setColorMatchMaterialName(this.getDemandColorMatchName(updateDemand));

                    //新增替换的物料到新Bom
                    BomOrderMaterial oldMaterial = replaceMaterialMap.get(materialAddReq.getBomMaterialIdChange());
                    long newBomMaterialId = IdPool.getId();
                    BomOrderMaterial newBomMaterial = this.replaceNewMaterial(newBomOrder, materialAddReq, newBomDemandId,
                            oldMaterial, newBomMaterialId, materialInfoDto);

                    newBomMaterialList.add(newBomMaterial);
                    oldNewMaterialIdMap.put(oldMaterial.getBomMaterialId(), newBomMaterialId);
                    //需求关联替换后的物料id
                    newDemand.setLatestBomMaterialId(newBomMaterial.getBomMaterialId());
                    newBomDemandList.add(newDemand);

                    //新增的二次工艺
                    List<CraftDemandSaveV3Req> addCraftDemandReqList = materialAddReq.getAddCraftDemandList();
                    if (CollectionUtil.isNotEmpty(addCraftDemandReqList)) {
                        //关联bom物料id, 供应链需求信息
                        addCraftDemandReqList.forEach(item -> {
                            item.setBomMaterialId(newBomMaterialId);
                            item.setBomMaterialDemandId(newDemand.getBomMaterialDemandId());
                            item.setSupplyChainDemandId(newDemand.getSupplyChainDemandId());
                            item.setSupplyChainDemandCode(newDemand.getSupplyChainDemandCode());
                        });
                        addCraftDemandReqList.forEach(item -> item.setBomMaterialId(newBomMaterialId));
                        addCraftReqList.addAll(addCraftDemandReqList);
                    }
                    //物料备注
                    if (StringUtils.isNotBlank(materialAddReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(newBomOrder.getBomId(), newBomMaterialId, materialAddReq.getRemark()));
                    }
                    //更换物料删除的工艺
                    Set<Long> delCraftDemandIds = materialAddReq.getDelCraftDemandIds();
                    //删除工艺
                    if (CollUtil.isNotEmpty(delCraftDemandIds)) {
                        delCraftIdSet.addAll(delCraftDemandIds);
                    }
                }
            });
            //找料中, 更换物料,不会把工艺全部删除; 找料中的工艺与需求绑定
            // //删除替换物料下的工艺: 根据物料id集合查询暂存工艺信息
            // List<Long> craftDemandIdList = craftDemandInfoRepository.getListByMaterialIds(replaceMaterialIdList).stream()
            //         .filter(item -> !Objects.equals(CraftDemandStateEnum.CLOSED.getCode(), item.getState()))
            //         .map(CraftDemandInfo::getCraftDemandId)
            //         .collect(Collectors.toList());
            // //删除工艺
            // if (CollUtil.isNotEmpty(craftDemandIdList)) {
            //     delCraftIdSet.addAll(craftDemandIdList);
            // }
        }

        //需求与物料新增到新bom中
        materialDemandRepository.saveBatch(newBomDemandList);
        orderMaterialRepository.saveBatch(newBomMaterialList);

        //更新旧bom的最新采购周期
        this.updateBomMaterialCycle(oldMaterialUpdateList);

        //添加物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void updateBomMaterialCycle(List<BomOrderMaterial> updateMaterialList) {
        if (CollUtil.isEmpty(updateMaterialList)) {
            return;
        }
        UserContent userContent = UserContentHolder.get();
        updateMaterialList.forEach(updateMaterial -> {
            orderMaterialRepository.lambdaUpdate()
                    .set(BomOrderMaterial::getSamplePurchasingCycle, updateMaterial.getSamplePurchasingCycle())
                    .set(BomOrderMaterial::getBulkPurchasingCycle, updateMaterial.getBulkPurchasingCycle())
                    .set(BomOrderMaterial::getRevisedTime, LocalDateTime.now())
                    .set(Objects.nonNull(userContent), BomOrderMaterial::getReviserId, userContent.getCurrentUserId())
                    .set(Objects.nonNull(userContent), BomOrderMaterial::getReviserName, userContent.getCurrentUserName())
                    .eq(BomOrderMaterial::getBomMaterialId, updateMaterial.getBomMaterialId())
                    .update();
        });
    }

    private String getDemandColorMatchName(BomOrderUpdateV3Req.UpdateBomMaterialDemandReq updateBomDemandReq) {
        String colorMatchMaterialName = Objects.equals(updateBomDemandReq.getColorMatchMaterialState(),
                BomColorMaterialStateEnum.NO_THING.getCode())
                ? null : updateBomDemandReq.getColorMatchMaterialName();
        colorMatchMaterialName = StringUtils.isBlank(colorMatchMaterialName) ? null : colorMatchMaterialName;
        return colorMatchMaterialName;
    }

    private String getColorMatchName(Integer colorMatchMaterialState, String colorMatchMaterialName) {
        String colorMatchName = Objects.equals(colorMatchMaterialState, BomColorMaterialStateEnum.NO_THING.getCode()) ? null : colorMatchMaterialName;
        colorMatchName = StringUtils.isBlank(colorMatchName) ? null : colorMatchName;
        return colorMatchName;
    }

    private BomMaterialInfoDto getUpdateMaterialInfoDto(List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateDemandReqList) {
        //有匹配回复的需求更新,查询对应的辅料信息
        Set<Long> updateSkuIdSet = new HashSet<>();
        updateDemandReqList.forEach(updateDemandReq -> {
            //直接更新物料, 不是更换物料
            List<BomOrderUpdateV3Req.DemandMaterialUpdateReq> materialUpdateReqList = updateDemandReq.getMaterialUpdateReqList();
            if (CollUtil.isNotEmpty(materialUpdateReqList) && Objects.nonNull(materialUpdateReqList.get(0))) {
                BomOrderUpdateV3Req.DemandMaterialUpdateReq materialUpdateReq = materialUpdateReqList.get(0);
                if (Objects.nonNull(materialUpdateReq.getSkuId())) {
                    updateSkuIdSet.add(materialUpdateReq.getSkuId());
                }
            }
        });
        //根据skuId查询物料信息
        if (CollUtil.isEmpty(updateSkuIdSet)) {
            return null;
        }
        return bomMaterialCommonService.queryMaterialInfoBySkuId(updateSkuIdSet, MaterialDemandTypeEnum.ACCESSORIES);
    }



    private void resetMaterialInfo(BomMaterialInfoDto updateMaterialInfoDto,
                                   BomOrderUpdateV3Req.DemandMaterialUpdateReq materialUpdateReq,
                                   BomOrderMaterial updateMaterial) {
        //最新的价格与采购周期信息
        if (Objects.isNull(materialUpdateReq.getSkuId()) || Objects.isNull(updateMaterialInfoDto)) {
            return;
        }
        Map<Long, ProductSkuVo> accessoriesSkuMap = updateMaterialInfoDto.getAccessoriesSkuMap();
        if (CollUtil.isEmpty(accessoriesSkuMap) || Objects.isNull(accessoriesSkuMap.get(materialUpdateReq.getSkuId()))) {
            return;
        }
        ProductSkuVo productSkuVo = accessoriesSkuMap.get(materialUpdateReq.getSkuId());

        updateMaterial.setMinPrice(productSkuVo.getMinPrice());
        //价格有效期结束时间
        updateMaterial.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());

        //样衣与大货周期
        updateMaterial.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
        updateMaterial.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
    }


    private BomOrderMaterial replaceNewMaterial(BomOrder newBomOrder,
                                                BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq,
                                                long newBomDemandId,
                                                BomOrderMaterial oldMaterial,
                                                long newBomMaterialId,
                                                BomMaterialInfoDto materialInfoDto) {

        BomOrderMaterial newBomMaterial = new BomOrderMaterial();
        BeanUtils.copyProperties(oldMaterial, newBomMaterial);
        newBomMaterial.setBomMaterialId(newBomMaterialId);
        //关联新需求id
        newBomMaterial.setBomMaterialDemandId(newBomDemandId);
        newBomMaterial.setReplaceBomMaterialId(oldMaterial.getBomMaterialId());
        newBomMaterial.setBomId(newBomOrder.getBomId());
        newBomMaterial.setPartUse(materialAddReq.getPartUse());
        newBomMaterial.setCuttingMethod(materialAddReq.getCuttingMethod());
        newBomMaterial.setPrototypeMaterialName(materialAddReq.getPrototypeMaterialName());
        newBomMaterial.setColorMatchMaterialState(materialAddReq.getColorMatchMaterialState());
        newBomMaterial.setColorMatchMaterialName(this.getColorMatchName(materialAddReq.getColorMatchMaterialState(), materialAddReq.getColorMatchMaterialName()));

        //辅料信息
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Long skuId = materialAddReq.getSkuId();
        SdpDesignException.isTrue(CollUtil.isNotEmpty(accessoriesSkuMap), "辅料spu信息不存在! skuId:{}", skuId);
        ProductSkuVo productSkuVo = accessoriesSkuMap.get(skuId);
        SdpDesignException.notNull(productSkuVo, "辅料spu信息不存在! skuId:{}", skuId);
        newBomMaterial.setMinPrice(productSkuVo.getMinPrice());
        //价格有效期结束时间
        newBomMaterial.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());

        //样衣与大货周期
        newBomMaterial.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
        newBomMaterial.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());

        //创建物料快照
        Long materialSnapshotId = this.createSnapshot(materialAddReq, materialInfoDto, newBomMaterialId);

        //新增物料关联快照id
        newBomMaterial.setMaterialSnapshotId(materialSnapshotId);

        return newBomMaterial;
    }


    private Long createSnapshot(BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq, BomMaterialInfoDto materialInfoDto, long bomMaterialTransientId) {
        Long skuId = materialAddReq.getSkuId();
        String skuCode = materialAddReq.getSkuCode();
        String prototypeMaterialName = materialAddReq.getPrototypeMaterialName();

        //根据skuId查询履约信息
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = materialInfoDto.getAccessoriesSpuMap();
        Map<Long, CategoryTreeMapVo> categoryTreeMap = materialInfoDto.getCategoryTreeMap();

        //生成物料快照, 暂存不需要同步履约
        ProductSkuVo productSkuVo = accessoriesSkuMap.get(skuId);
        SdpDesignException.notNull(productSkuVo, "更换物料-辅料sku信息不存在! skuCode:{}; 物料项目:{}", skuCode, prototypeMaterialName);
        ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(productSkuVo.getSpuId());
        SdpDesignException.notNull(productSpuInfoVo, "更换物料-辅料SPU信息不存在! skuCode:{};物料项目:{}",  skuCode, prototypeMaterialName);

        MaterialSnapshotCreateReq snapshotCreateReq = BomOrderMaterialConverter.buildAccessoriesMaterialSnapshotV3(bomMaterialTransientId, productSkuVo, productSpuInfoVo, categoryTreeMap);

        return materialSnapshotService.create(snapshotCreateReq);
    }


    private BomMaterialInfoDto getMaterialInfoDto(List<BomOrderUpdateV3Req.DemandMaterialAddReq> replaceMaterialList) {
        //根据skuId查询物料信息
        Set<Long> skuIdSet = replaceMaterialList.stream()
                .map(BomOrderUpdateV3Req.DemandMaterialAddReq::getSkuId).collect(Collectors.toSet());
        SdpDesignException.notEmpty(skuIdSet, "辅料需求更换物料入参异常, 缺少skuId! ");

        return bomMaterialCommonService.queryMaterialInfoBySkuId(skuIdSet, MaterialDemandTypeEnum.ACCESSORIES);
    }

    private void addDesignRemark(List<DesignRemarksReq> designRemarksReqList) {
        //新增物料备注
        designRemarksReqList.stream()
                .filter(designRemark -> StringUtils.isNotBlank(designRemark.getRemark()))
                .forEach(designRemarksService::create);
    }

    // private Map<Long, BomOrderMaterial> getLatestMaterialMap(List<BomMaterialDemand> oldDemandList) {
    //     List<Long> latestMaterialIdList = oldDemandList.stream().map(BomMaterialDemand::getLatestBomMaterialId).collect(Collectors.toList());
    //     Map<Long, BomOrderMaterial> materialMap = new HashMap<>();
    //     if (CollUtil.isNotEmpty(latestMaterialIdList)) {
    //         materialMap = orderMaterialRepository.listByIds(latestMaterialIdList).stream()
    //                 .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
    //     }
    //     return materialMap;
    // }

    private Map<Long, BomOrderMaterial> getLatestMaterialMap(List<BomMaterialDemand> oldDemandList) {
        List<Long> demandIdList = oldDemandList.stream().map(BomMaterialDemand::getBomMaterialDemandId).collect(Collectors.toList());
        Map<Long, BomOrderMaterial> materialMap = new HashMap<>();
        if (CollUtil.isNotEmpty(demandIdList)) {
            materialMap = orderMaterialRepository.listByDemandIds(demandIdList).stream()
                    .filter(item -> !Objects.equals(item.getMaterialState(), BomMaterialStateEnum.CLOSED.getCode()))
                    .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialDemandId, Function.identity(), (k1, k2) -> k1));
        }
        return materialMap;
    }

    private void addDemandHandle(BomOrder bomOrder,
                                 List<BomOrderUpdateV3Req.AddBomMaterialDemandReq> demandAddReqList,
                                 List<CraftDemandSaveV3Req> addCraftReqList) {
        if (CollUtil.isEmpty(demandAddReqList)) {
            return;
        }
        //新增需求, 无快照的物料, 工艺
        List<BomMaterialDemand> addDemandList = new LinkedList<>();
        List<BomOrderMaterial> addMaterialList = new LinkedList<>();
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();

        Long bomId = bomOrder.getBomId();
        //1, 新增需求处理
        demandAddReqList.forEach(item -> {
            //创建需求
            BomMaterialDemand demand = this.buildDemandCreateEo(bomId, item);
            //预先创建一个无物料快照的bom物料
            BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq = item.getMaterialAddReq();
            BomOrderMaterial bomOrderMaterial = this.buildSearchingMaterial(bomId, demand, materialAddReq);
            addMaterialList.add(bomOrderMaterial);
            //关联最新物料id
            demand.setLatestBomMaterialId(bomOrderMaterial.getBomMaterialId());
            addDemandList.add(demand);
            //工艺
            if (Objects.nonNull(materialAddReq) && CollUtil.isNotEmpty(materialAddReq.getAddCraftDemandList())) {
                List<CraftDemandSaveV3Req> addCraftDemandList = materialAddReq.getAddCraftDemandList();
                //设置物料id
                addCraftDemandList.forEach(craft -> {
                    craft.setBomMaterialDemandId(demand.getBomMaterialDemandId());
                    craft.setBomMaterialId(bomOrderMaterial.getBomMaterialId());
                });
                addCraftReqList.addAll(materialAddReq.getAddCraftDemandList());
            }

            //物料备注
            if (StringUtils.isNotBlank(materialAddReq.getRemark())) {
                designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomId, bomOrderMaterial.getBomMaterialId(), materialAddReq.getRemark()));
            }
        });

        //先保存物料
        if (CollUtil.isNotEmpty(addMaterialList)) {
            orderMaterialRepository.saveBatch(addMaterialList);
        }

        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        SdpDesignException.notNull(prototype, "skc不存在!:{}", bomOrder.getDesignCode());

        //封装创建履约需求req
        List<AccessoryDemandCreateOpenV2Req.AccessoryDemandInfo> demandCreateReqList = new LinkedList<>();
        this.buildSupplyDemandCreateReq(addDemandList, demandCreateReqList, bomOrder, prototype);

        //2, 调用履约接口创建需求
        if (CollUtil.isNotEmpty(demandCreateReqList)) {
            AccessoryDemandCreateOpenV2Req demandBatchCreateReq = new AccessoryDemandCreateOpenV2Req();
            demandBatchCreateReq.setAccessoryDemandInfos(demandCreateReqList);
            demandBatchCreateReq.setBizChannel(prototype.getBizChannel());
            List<AccessoryDemandCreateOpenV2Resp.DemandInfo> demandInfoRespList = zjDesignRemoteHelper.accessoryDemandCreate(demandBatchCreateReq);

            //3, 需求创建成功后, 维护履约需求id, 需求编号, 需求创建时间; 将需求信息添加到工艺req对象中
            this.resetDemandAndCraftAddInfo(addDemandList, addCraftReqList, demandInfoRespList);
        }


        //4, 新增需求
        if (CollUtil.isNotEmpty(addDemandList)) {
            materialDemandRepository.saveBatch(addDemandList);
        }

        //添加物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void buildSupplyDemandCreateReq(List<BomMaterialDemand> addDemandList,
                                            List<AccessoryDemandCreateOpenV2Req.AccessoryDemandInfo> demandCreateReqList,
                                            BomOrder bomOrder,
                                            Prototype prototype) {

        if (CollUtil.isEmpty(addDemandList)) {
            return;
        }

        //查询物料名查询对色物料信息(选料的面辅料要先入库; 如果是再次提交,会升版本, 更新需求下的物料也要先入库)
        List<BomOrderMaterial> bomOrderMaterialList = orderMaterialRepository.getListByBomId(bomOrder.getBomId());
        Map<String, BomOrderMaterial> materialNameMap = bomOrderMaterialList.stream()
                .collect(Collectors.toMap(BomOrderMaterial::getPrototypeMaterialName, Function.identity(), (k1, k2) -> k1));

        Map<Long, MaterialSnapshot> materialSnapshotMap = materialSnapshotRepository.listByIds(bomOrderMaterialList.stream()
                .map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(MaterialSnapshot::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));

        UserContent userContent = UserContentHolder.get();
        addDemandList.forEach(demand -> {
            //已经创建过了
            if (Objects.nonNull(demand.getSupplyChainDemandId())) {
                return;
            }
            AccessoryDemandCreateOpenV2Req.AccessoryDemandInfo demandInnerCreateReq = new AccessoryDemandCreateOpenV2Req.AccessoryDemandInfo();
            demandInnerCreateReq.setDemandType(DemandTypeEnum.ACCESSORY.getCode());
            demandInnerCreateReq.setCreatorName(userContent.getCurrentUserName());
            // demandInnerCreateReq.setExtDesignId(prototype.getFakeId());
            demandInnerCreateReq.setExtDesignCode(prototype.getDesignCode());
            demandInnerCreateReq.setDemandPicture(StrUtil.splitTrim(demand.getDemandPicture(), StrUtil.COMMA));
            demandInnerCreateReq.setDemandDesc(demand.getDemandRemark());
            int isOppositeColors = Objects.equals(BomColorMaterialStateEnum.NO_THING.getCode(), demand.getColorMatchMaterialState())
                    ? Bool.NO.getCode() : Bool.YES.getCode();
            demandInnerCreateReq.setIsOppositeColors(isOppositeColors);
            Integer oppositeColor = null;
            if (Objects.equals(isOppositeColors, Bool.YES.getCode())) {
                if (Objects.equals(BomColorMaterialStateEnum.OPPOSITE_COLORS.getCode(), demand.getColorMatchMaterialState())) {
                    oppositeColor = OppositeColorEnum.OPPOSITE_COLOR.getCode();
                } else if (Objects.equals(BomColorMaterialStateEnum.INCLUDE_MATERIAL.getCode(), demand.getColorMatchMaterialState())) {
                    oppositeColor = OppositeColorEnum.CONTRACT_MATERIAL.getCode();
                }
            }
            demandInnerCreateReq.setOppositeColor(oppositeColor);
            demandInnerCreateReq.setOppositeMaterialProject(demand.getColorMatchMaterialName());
            //是否复色
            Integer isCompoundColorsMaking = Objects.equals(SkcTypeEnum.COMPOUND_COLORS.getCode(), prototype.getSkcType()) ? Bool.YES.getCode() : Bool.NO.getCode();
            demandInnerCreateReq.setIsCompoundColorsMaking(isCompoundColorsMaking);
            // demandInnerCreateReq.setStyleReferType(PrototypeStyleReferEnum.DERIVE.getCode());
            demandInnerCreateReq.setMaterialProject(demand.getPrototypeMaterialName());
            demandInnerCreateReq.setDemandNum(demand.getDemandNum());
            //履约说需求数量单位传中文名
            // demandInnerCreateReq.setDemandNumUnit(demand.getDemandNumUnit());
            demandInnerCreateReq.setDemandNumUnit(demand.getDemandNumUnitName());
            demandInnerCreateReq.setMaterialProject(demand.getPrototypeMaterialName());
            // demandInnerCreateReq.setRegionId(prototype.getRegionId()); //todo 对接致景
            // demandInnerCreateReq.setRegionName(prototype.getRegionName());

            //设置对色关联信息
            this.resetRelationInfo4Demand(materialNameMap, materialSnapshotMap, demand, demandInnerCreateReq);

            Map<String, Object> extra = new HashMap<>();
            extra.put(MATERIAL_DEMAND_ID_NAME, demand.getBomMaterialDemandId());
            demandInnerCreateReq.setExtra(extra);
            demandCreateReqList.add(demandInnerCreateReq);
        });
    }

    private void resetRelationInfo4Demand(Map<String, BomOrderMaterial> materialNameMap,
                                          Map<Long, MaterialSnapshot> materialSnapshotMap,
                                          BomMaterialDemand demand,
                                          AccessoryDemandCreateOpenV2Req.AccessoryDemandInfo demandInnerCreateReq) {
        String colorMatchMaterialName = demand.getColorMatchMaterialName();
        if (StringUtils.isNotBlank(colorMatchMaterialName)) {
            if (CollUtil.isNotEmpty(materialNameMap) && Objects.nonNull(materialNameMap.get(colorMatchMaterialName))) {
                Long materialSnapshotId = materialNameMap.get(colorMatchMaterialName).getMaterialSnapshotId();
                if (CollUtil.isNotEmpty(materialSnapshotMap) && Objects.nonNull(materialSnapshotMap.get(materialSnapshotId))) {
                    MaterialSnapshot snapshot = materialSnapshotMap.get(materialSnapshotId);

                    demandInnerCreateReq.setRelatedCommodityId(snapshot.getCommodityId());
                    demandInnerCreateReq.setRelatedCommodityCode(snapshot.getCommodityCode());
                    // demandInnerCreateReq.setRelatedCommodityType("FLOWER");
                    demandInnerCreateReq.setRelatedCommodityType(snapshot.getCommodityType());
                    demandInnerCreateReq.setRelatedSkuId(snapshot.getSkuId());
                    demandInnerCreateReq.setRelatedSkuCode(snapshot.getSkuCode());
                }
            }
        }
    }

    private void resetDemandAndCraftAddInfo(List<BomMaterialDemand> addDemandList,
                                            List<CraftDemandSaveV3Req> addCraftReqList,
                                            List<AccessoryDemandCreateOpenV2Resp.DemandInfo> houLiuDemandRespList
                                            ) {
        Map<Long, List<CraftDemandSaveV3Req>> craftDemandMap = addCraftReqList.stream().filter(item -> Objects.nonNull(item.getBomMaterialDemandId())).collect(Collectors.groupingBy(CraftDemandSaveV3Req::getBomMaterialDemandId));
        Map<Object, AccessoryDemandCreateOpenV2Resp.DemandInfo> demandRespMap = houLiuDemandRespList.stream().collect(Collectors.toMap(item -> item.getExtra().get(MATERIAL_DEMAND_ID_NAME), Function.identity(), (k1, k2) -> k1));

        addDemandList.forEach(demand -> {
            //已经创建过了
            if (Objects.nonNull(demand.getSupplyChainDemandId())) {
                return;
            }
            AccessoryDemandCreateOpenV2Resp.DemandInfo demandResp = demandRespMap.get(demand.getBomMaterialDemandId());
            SdpDesignException.notNull(demandResp, "辅料需求创建异常, 没有返回需求信息! 物料项目名: {}", demand.getPrototypeMaterialName());
            Long supplyChainDemandId = demandResp.getDemandId();
            String supplyChainDemandCode = demandResp.getDemandCode();

            demand.setSupplyChainDemandId(supplyChainDemandId);
            demand.setSupplyChainDemandCode(supplyChainDemandCode);
            demand.setDemandCreatedTime(LocalDateTime.now());
            if (CollUtil.isNotEmpty(craftDemandMap) && CollUtil.isNotEmpty(craftDemandMap.get(demand.getBomMaterialDemandId()))) {
                List<CraftDemandSaveV3Req> craftReqList = craftDemandMap.get(demand.getBomMaterialDemandId());
                craftReqList.forEach(craft -> {
                    craft.setSupplyChainDemandId(supplyChainDemandId);
                    craft.setSupplyChainDemandCode(supplyChainDemandCode);
                });
            }
        });
    }

    private BomOrderMaterial buildSearchingMaterial(Long bomId, BomMaterialDemand demand,
                                                    BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq) {
        long bomMaterialId = IdPool.getId();

        return BomOrderMaterial.builder()
                .bomMaterialId(bomMaterialId)
                .bomMaterialDemandId(demand.getBomMaterialDemandId())
                .bomId(bomId)
                .bomMaterialType(MaterialDemandTypeEnum.ACCESSORIES.getCode())
                .partUse(materialAddReq.getPartUse())
                .prototypeMaterialName(materialAddReq.getPrototypeMaterialName())
                .colorMatchMaterialState(materialAddReq.getColorMatchMaterialState())
                .colorMatchMaterialName(materialAddReq.getColorMatchMaterialName())
                .materialState(BomMaterialStateEnum.SEARCHING.getCode())
                .materialContextId(IdPool.getId())
                .build();
    }

    private BomMaterialDemand buildDemandCreateEo(Long bomId,
                                                  BomOrderUpdateV3Req.AddBomMaterialDemandReq item) {
        long bomMaterialDemandId = IdPool.getId();
        BomMaterialDemand demand = new BomMaterialDemand();
        BeanUtils.copyProperties(item, demand);
        demand.setBomMaterialDemandId(bomMaterialDemandId)
                .setBomId(bomId)
                .setMaterialDemandType(MaterialDemandTypeEnum.ACCESSORIES.getCode())
                .setDemandState(BomDemandStateEnum.SUBMIT.getCode())
                //新建的需求默认找料中
                .setMaterialSearchState(Bool.YES.getCode());
        if (CollUtil.isNotEmpty(item.getDemandPictureList())) {
            demand.setDemandPicture(StrUtil.join(StrUtil.COMMA, item.getDemandPictureList()));
        }

        return demand;
    }

}
