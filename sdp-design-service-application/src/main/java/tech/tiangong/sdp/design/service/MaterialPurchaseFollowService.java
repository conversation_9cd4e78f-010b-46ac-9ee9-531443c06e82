package tech.tiangong.sdp.design.service;


import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.entity.MaterialPurchaseFollow;
import tech.tiangong.sdp.design.vo.req.material.OtherProcessCancelMaterialListReq;
import tech.tiangong.sdp.design.vo.req.ordermaterial.OrderMaterialStatusInnerReq;
import tech.tiangong.sdp.design.vo.req.purchase.CancelMaterialReq;
import tech.tiangong.sdp.design.vo.req.purchase.DesignCodePurchaseOrderReq;
import tech.tiangong.sdp.design.vo.req.purchase.MaterialPurchaseFollowPageReq;
import tech.tiangong.sdp.design.vo.resp.material.MaterialColorCardPictureVo;
import tech.tiangong.sdp.design.vo.resp.material.MaterialPurchaseFollowVo;
import tech.tiangong.sdp.design.vo.resp.material.PurchaseApplyFollowCountVO;
import tech.tiangong.sdp.design.vo.resp.purchase.MaterialPurchaseFollowPageVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* 面辅料采购跟进表
* <br>CreateDate August 10,2021
* <AUTHOR>
* @since 1.0
*/
public interface MaterialPurchaseFollowService {

    /**
     * 面辅料采购跟进列表
     *
     * @param req
     * @return
     */
    PageRespVo<MaterialPurchaseFollowPageVO> pageList(MaterialPurchaseFollowPageReq req);

    /**
     * 取消物料
     *
     * @param req
     * @return
     */
    void cancelMaterial(CancelMaterialReq req);


    /**
     * 取消物料(上游流程取消的物料采购)
     * 1、 关闭物料不需要调用供应履约SDK取消物料采购，而是通过MQ的方式发起取消采购通知信息
     * 2、 关闭物料需要调用供应履约SDK取消物料采购
     *
     * @param req
     * @return
     */
    void closeMaterialPurchaseWithOther(OtherProcessCancelMaterialListReq req);

    void updateBatchById(List<MaterialPurchaseFollow> materialPurchaseFollows);

   /**
     * 根据加工单号查询是否已经下过采购单
     *
     * @param req
     * @return boolean
     */
    boolean isPurchase(OrderMaterialStatusInnerReq req);


    Map<String, List<MaterialPurchaseFollowVo>> materialPurchaseByDesignCode(DesignCodePurchaseOrderReq req);

    /**
     * 根据匹配id集合批量查询匹配的采购次数（自选物料版本）
     * @param materialSnapshotIdSet 物料快照id集合
     * @return List<PurchaseApplyFollowCountVO>
     */
    List<PurchaseApplyFollowCountVO> purchaseCountByMaterialSnapshotIds(Set<Long> materialSnapshotIdSet);

    /**
     * 获取采购物料最新维护的色卡图片
     *
     * @param materialSnapshotIds 物料快照id
     * @return List<MaterialPurchaseFollow>
     */
    List<MaterialColorCardPictureVo> materialColorCardPictureLatest(List<Long> materialSnapshotIds);
}