package tech.tiangong.sdp.design.business.bom.notification;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import tech.tiangong.sdp.clothes.mq.CraftDemandMqConstant;
import tech.tiangong.sdp.clothes.mq.body.BomOrderCraftDemandSyncMessage;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.enums.CraftDemandStateEnum;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.repository.CraftDemandInfoRepository;
import tech.tiangong.sdp.design.service.OrderMaterialFollowService;
import tech.tiangong.sdp.design.vo.dto.bom.SyncCraftClothesDto;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 将Bom二次工艺同步给样衣打版,原因：样衣车版后，物料跟进又重新确认生成新的Bom单并添加工艺，如果不同步致二次工艺-补充工艺环节，履约那边的裁后工艺会进行卡流程
 */
@Slf4j
public class SyncCraftClothesNotification implements Notification<SyncCraftClothesDto> {
    private List<SyncCraftClothesDto> syncCraftClothesDtoList = new LinkedList<>();
    private final CraftDemandInfoRepository craftDemandInfoRepository = SpringUtil.getBean(CraftDemandInfoRepository.class);

    private final MqProducer mqProducer = SpringUtil.getBean(MqProducer.class);
    private final OrderMaterialFollowService orderMaterialFollowService = SpringUtil.getBean(OrderMaterialFollowService.class);

    @Override
    public void add(SyncCraftClothesDto syncCraftClothesDto) {
        if (syncCraftClothesDto != null) {
            syncCraftClothesDtoList.add(syncCraftClothesDto);
        }
    }

    @Override
    public void addBatch(List<SyncCraftClothesDto> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            syncCraftClothesDtoList.addAll(list);
        }
    }

    @Override
    public List<SyncCraftClothesDto> getAll() {
        return syncCraftClothesDtoList;
    }

    @Override
    public void send() {
        if (CollectionUtil.isEmpty(syncCraftClothesDtoList)) {
            log.info("Bom二次工艺同步给样衣打版为空");
            return;
        }
        Map<BomOrder, List<SyncCraftClothesDto>> map = syncCraftClothesDtoList.stream().collect(Collectors.groupingBy(SyncCraftClothesDto::getBomOrder));
        map.forEach((bomOrder, syncCraftClothesDtos) -> {
            List<SyncCraftClothesDto.Craft> addCraftDemandList = syncCraftClothesDtos.stream().flatMap(list -> list.getCrafts().stream())
                    .filter(e->Objects.equals(CraftDemandStateEnum.SUBMIT.getCode(),e.getState())).collect(Collectors.toList());

            List<SyncCraftClothesDto.Craft> updateCraftDemandList = syncCraftClothesDtos.stream().flatMap(list -> list.getCrafts().stream())
                    .filter(e->Objects.equals(CraftDemandStateEnum.CLOSED.getCode(),e.getState())).collect(Collectors.toList());
            handlerCraftDemandSyncClothes(bomOrder, addCraftDemandList, updateCraftDemandList);
        });
    }


    /**
     * Bom二次工艺同步给样衣打版
     *
     * @param addCraftDemandList
     * @param delCraftDemandList
     * @param saveBomOrder
     */
    private void handlerCraftDemandSyncClothes(BomOrder saveBomOrder, List<SyncCraftClothesDto.Craft> addCraftDemandList, List<SyncCraftClothesDto.Craft> delCraftDemandList) {
        if (CollectionUtil.isEmpty(addCraftDemandList) && CollectionUtil.isEmpty(delCraftDemandList)) {
            return;
        }

        log.info("Bom二次工艺同步给样衣打版,addCraftDemandList:{},delCraftDemandList:{}", JSON.toJSONString(addCraftDemandList), JSON.toJSONString(delCraftDemandList));

        BomOrderCraftDemandSyncMessage demandSyncMessage = new BomOrderCraftDemandSyncMessage();
        demandSyncMessage.setPrototypeId(saveBomOrder.getPrototypeId());
        demandSyncMessage.setDesignCode(saveBomOrder.getDesignCode());
        demandSyncMessage.setBomId(saveBomOrder.getBomId());
        demandSyncMessage.setCreator(saveBomOrder.getCreatorId());
        demandSyncMessage.setCreatedName(saveBomOrder.getCreatorName());
        demandSyncMessage.setCreatedTime(saveBomOrder.getCreatedTime());

        List<Long> thirdPartyCraftDemandIdList = delCraftDemandList.stream().map(SyncCraftClothesDto.Craft::getThirdPartyCraftDemandId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(thirdPartyCraftDemandIdList)) {
            List<Long> delCraftDemandIdList = craftDemandInfoRepository.getListByThirdPartyCraftDemandIds(thirdPartyCraftDemandIdList)
                    .stream().map(CraftDemandInfo::getCraftDemandId).collect(Collectors.toList());
            demandSyncMessage.setDelCraftDemandIdList(delCraftDemandIdList);
        }

        if (CollectionUtil.isNotEmpty(addCraftDemandList)) {
            //v4.3 Bom新增工艺同步样衣从只推送裁后工艺改成推送全部的新增工艺需求，打版环节再分工艺类型处理不同的逻辑
            List<BomOrderCraftDemandSyncMessage.CraftInfo> craftInfoList = addCraftDemandList.stream()/*.filter(craft ->
                            Objects.equals(craft.getCraftsRequire(), DemandCraftsRequireEnum.POST_CUTTING_CRAFTS.getCode()))*/
                    .map(craft -> {
                        BomOrderCraftDemandSyncMessage.CraftInfo craftInfo = new BomOrderCraftDemandSyncMessage.CraftInfo();
                        BeanUtils.copyProperties(craft, craftInfo);
                        return craftInfo;
                    }).collect(Collectors.toList());
            demandSyncMessage.setAddCraftInfoList(craftInfoList);
        }
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.BOM_CRAFT_DEMAND_SYNC_CLOTHES,
                CraftDemandMqConstant.BOM_ORDER_CRAFT_SYNC_EXCHANGE, CraftDemandMqConstant.BOM_ORDER_CRAFT_SYNC_ROUTING_KEY,
                JSON.toJSONString(demandSyncMessage));
        //发送消息
        mqProducer.sendOnAfterCommit(mqMessageReq);

    }
}