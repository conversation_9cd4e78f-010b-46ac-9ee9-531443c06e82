package tech.tiangong.sdp.design.converter.visual;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.design.entity.VisualSpu;
import tech.tiangong.sdp.design.entity.VisualTaskNodeState;
import tech.tiangong.sdp.design.enums.visual.VisualTaskHandleStateEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskNodeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskStateEnum;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.repository.VisualSpuRepository;
import tech.tiangong.sdp.design.repository.VisualTaskNodeStateRepository;
import tech.tiangong.sdp.design.vo.resp.visual.VisualSpuVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskInfoVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskListVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskNodeStateVo;

import java.util.*;
import java.util.stream.Collectors;

@AllArgsConstructor
@Component
public class VisualTaskConverter {
    private final VisualSpuRepository visualSpuRepository;
    private final VisualTaskHelper visualTaskHelper;
    private final VisualTaskNodeStateRepository visualTaskNodeStateRepository;

    public List<VisualTaskListVo> trans2VisualTaskListVo(List<VisualTaskInfoVo> list) {
        if(CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<VisualTaskListVo> result = new ArrayList<>();
        List<Long> taskIds = list.stream().map(VisualTaskInfoVo::getTaskId).toList();
        List<VisualTaskNodeState> nodeStates = visualTaskNodeStateRepository.listByVisualTaskIds(taskIds);
        Map<Long,List<VisualTaskNodeState>> nodeStateMap = nodeStates.stream().collect(Collectors.groupingBy(VisualTaskNodeState::getTaskId));

        Map<String,List<VisualTaskInfoVo>> map = list.stream()
                .collect(Collectors.groupingBy(
                        VisualTaskInfoVo::getStyleCode,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                tasks -> tasks.stream()
                                        .collect(Collectors.toMap(
                                                VisualTaskInfoVo::getTaskId,
                                                v -> v,
                                                (existing, replacement) -> existing))
                                        .values()
                                        .stream()
                                        .toList()
                        )
                ));



        Set<String> styleCodes = map.keySet();
        List<VisualSpu> spuList = visualSpuRepository.listByStyleCodes(styleCodes);
        Map<String,VisualSpu> spuMap = spuList.stream().collect(Collectors.toMap(VisualSpu::getStyleCode,v->v,(k1,k2)->k2));
        map.forEach((styleCode,tasks)->{
            VisualTaskListVo vo = new VisualTaskListVo();
            VisualSpuVo visualSpuVo = new VisualSpuVo();
            VisualSpu spu = spuMap.get(styleCode);
            if(spu != null) {
                BeanUtils.copyProperties(spu,visualSpuVo);
            }
            vo.setVisualSpu(visualSpuVo);
            tasks.forEach(task->{
                List<VisualTaskNodeState> tempNodeStates = nodeStateMap.get(task.getTaskId());
                List<VisualTaskNodeStateVo> respNodeStates = visualTaskHelper.trans2LatestVisualTaskNodeStateVo(tempNodeStates);
                task.setNodeStates(respNodeStates);
                if(VisualTaskStateEnum.FINISH.getCode().equals(task.getState())){
                    task.setCurrentStateDesc("已完成");
                }else if(VisualTaskStateEnum.CANCEL.getCode().equals(task.getState())){
                    task.setCurrentStateDesc("已取消");
                }else if(VisualTaskStateEnum.WAITING.getCode().equals(task.getState())){
                    task.setCurrentStateDesc("待分配");
                }else if(CollectionUtil.isNotEmpty(tempNodeStates)){
                    for(VisualTaskNodeState visualTaskNodeState : tempNodeStates){
                        //已流转到tryOn环节,且是进行中，则返回
                        if(VisualTaskNodeEnum.TRYON_HANDLE.getCode().equals(visualTaskNodeState.getProcessNode())
                            && VisualTaskHandleStateEnum.DOING.getCode().equals(visualTaskNodeState.getProcessNodeState())){
                            task.setCurrentStateDesc("tryOn中");
                            break;
                        }
                        //已流转到修图环节,且是进行中，则返回
                        if(VisualTaskNodeEnum.ON_SHELF_HANDLE.getCode().equals(visualTaskNodeState.getProcessNode())
                                && VisualTaskHandleStateEnum.DOING.getCode().equals(visualTaskNodeState.getProcessNodeState())){
                            task.setCurrentStateDesc("修图中");
                            break;
                        }
                    }
                }else{
                    task.setCurrentStateDesc("进行中");
                }
               if(StringUtils.isNotBlank(task.getDemandImages())){
                   task.setDemandImageList(StrUtil.splitTrim(task.getDemandImages(), StrUtil.COMMA));
               }
            });
            vo.setVisualTaskInfos(tasks);
            result.add(vo);
        });
        return result;
    }
}
