package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.Json;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Sets;
import com.yibuyun.scm.common.dto.accessories.request.CategoryNameListReq;
import com.yibuyun.scm.common.dto.accessories.request.SpuDetailPageReq;
import com.yibuyun.scm.common.dto.accessories.response.*;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierExtVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierInfoVo;
import com.yibuyun.scm.common.enums.accessory.ProductStateEnum;
import com.yibuyun.scm.open.client.accessories.SCMAccessoriesCategoryClient;
import com.yibuyun.scm.open.client.accessories.SCMAccessoriesSpuClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.DesignRemarks;
import tech.tiangong.sdp.design.entity.SpecialAccessories;
import tech.tiangong.sdp.design.enums.MaterialDemandTypeEnum;
import tech.tiangong.sdp.design.enums.SpecialAccessoriesStateEnum;
import tech.tiangong.sdp.design.remote.ProductRemoteHelper;
import tech.tiangong.sdp.design.remote.SupplierInfoRemoteHelper;
import tech.tiangong.sdp.design.repository.BomOrderRepository;
import tech.tiangong.sdp.design.repository.DesignRemarksRepository;
import tech.tiangong.sdp.design.repository.SpecialAccessoriesRepository;
import tech.tiangong.sdp.design.service.SpecialAccessoriesService;
import tech.tiangong.sdp.design.vo.req.SpecialAccessoriesReq;
import tech.tiangong.sdp.design.vo.req.special.accessories.SpecialAccessoriesProductReq;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderMaterialVo;
import tech.tiangong.sdp.design.vo.resp.material.MaterialRemarkVo;
import tech.tiangong.sdp.design.vo.resp.special.accessories.SpecialAccessoriesDetailResp;
import tech.tiangong.sdp.design.vo.resp.special.accessories.SpecialAccessoriesProductResp;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 干什么
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/4 19:09
 */
@Slf4j
@AllArgsConstructor
@Service
public class SpecialAccessoriesServiceImpl implements SpecialAccessoriesService {

	private static final String SPECIAL_ACCESSORIES_CATEGORY_NAME = "特殊辅料";

	private final SCMAccessoriesSpuClient accessoriesSpuClient;
	private final SCMAccessoriesCategoryClient categoryClient;
	private final BomOrderRepository bomOrderRepository;
	private final SpecialAccessoriesRepository specialAccessoriesRepository;
	private final DesignRemarksRepository designRemarksRepository;
	private final ProductRemoteHelper productRemoteHelper;
	private final SupplierInfoRemoteHelper supplierInfoRemoteHelper;
	private final RedisTemplate redisTemplate;


	@Override
	public PageRespVo<SpecialAccessoriesProductResp> productPage(SpecialAccessoriesProductReq req) {
		List<Long> productCategoryIds = getSpecialAccessoriesProductCategoryIds();
		if (CollectionUtil.isEmpty(productCategoryIds)) {
			log.warn("无特殊辅料类目");
			return PageRespVoHelper.of(req.getPageNum(), 0, Collections.emptyList());
		}
		SpuDetailPageReq spuDetailPageReq = new SpuDetailPageReq();
		spuDetailPageReq.setProductName(req.productName);
		spuDetailPageReq.setCategoryIds(productCategoryIds);
		spuDetailPageReq.setProductState(ProductStateEnum.CERTIFIED.getCode());
		spuDetailPageReq.setPageNum(0);
		spuDetailPageReq.setPageSize(Integer.MAX_VALUE);

		log.info("【查询特殊辅料商品】请求参数:{}", JSON.toJSONString(spuDetailPageReq));
		DataResponse<PageRespVo<ProductSpuInfoVo>> response = accessoriesSpuClient.detailPage(spuDetailPageReq);
		//log.info("【查询特殊辅料商品】响应结果:{}", JSON.toJSONString(response));
		SdpDesignException.isTrue(response.isSuccessful(),"查询特殊辅料商品失败");
		if (Objects.isNull(response.getData()) || CollectionUtil.isEmpty(response.getData().getList())) {
			return PageRespVoHelper.of(req.getPageNum(), 0, Collections.emptyList());
		}

		//根据skc进行内存分页
		Collection<ProductSpuInfoVo> productSpuInfoVoList = response.getData().getList();
		List<ProductSkuVo> productSkuVoList = productSpuInfoVoList.stream().map(ProductSpuInfoVo::getSkus).flatMap(Collection::stream).collect(Collectors.toList());
		if (CollectionUtil.isEmpty(productSkuVoList)) {
			return PageRespVoHelper.of(req.getPageNum(), 0, Collections.emptyList());
		}

		Map<Long, ProductSpuInfoVo> productSpuInfoVoMap = productSpuInfoVoList.stream().collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));

		int size = (req.getPageNum() - 1) * req.getPageSize();
		List<SpecialAccessoriesProductResp> accessoriesRespList = productSkuVoList.stream().skip(size).limit(req.getPageSize()).map(productSkuVo -> {

			ProductSpuInfoVo productSpuInfoVo = productSpuInfoVoMap.get(productSkuVo.getSpuId());
			SpecialAccessoriesProductResp accessoriesResp = new SpecialAccessoriesProductResp();

			accessoriesResp.setSpuId(productSpuInfoVo.getSpuId());
			accessoriesResp.setSpuCode(productSpuInfoVo.getSpuCode());
			accessoriesResp.setSpuName(productSpuInfoVo.getProductName());
			accessoriesResp.setSkuId(productSkuVo.getSkuId());
			accessoriesResp.setSkuCode(productSkuVo.getSkuCode());
			accessoriesResp.setSupplierId(productSpuInfoVo.getSupplierId());
			accessoriesResp.setSupplierName(productSpuInfoVo.getSupplierName());
			accessoriesResp.setSupplierCode(productSpuInfoVo.getSupplierCode());
			accessoriesResp.setSpuCityName(productSpuInfoVo.getSpuCityName());
			accessoriesResp.setSkuAttrs(JSON.toJSONString(productSkuVo.getSkuAttrs()));
			accessoriesResp.setCommodityNumber(productSkuVo.getProductNumber());
			if (CollectionUtil.isNotEmpty(productSkuVo.getPrices())) {
				ProductSkuPriceVo skuPriceVo = productSkuVo.getPrices().get(0);
				accessoriesResp.setSaleUnit(skuPriceVo.getValuationUnitName());
				accessoriesResp.setPurchasePrice(skuPriceVo.getPurchasePrice());
				accessoriesResp.setSkuPrice(skuPriceVo.getSkuPrice());
				//价格更新时间
				accessoriesResp.setPriceReplyTime(skuPriceVo.getRevisedTime());
				//价格失效时间
				accessoriesResp.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());
			}

			if (CollectionUtil.isNotEmpty(productSkuVo.getPictures())) {
				accessoriesResp.setPictureList(productSkuVo.getPictures().stream().map(ProductPictureWebVo::getPicturePath).collect(Collectors.toList()));
			}
			//若sku图为空,取商品图 --v3.5.2_汶俊;
			else if (CollectionUtil.isNotEmpty(productSpuInfoVo.getPictures())){
				accessoriesResp.setPictureList(productSpuInfoVo.getPictures().stream().map(ProductPictureVo::getPicturePath).collect(Collectors.toList()));
			}

			//包装数量单位; 最小价格单位
			accessoriesResp.setPackNumber(productSkuVo.getPackNumber());
			accessoriesResp.setPackUnitName(productSkuVo.getPackUnitName());
			accessoriesResp.setPackAssistantUnitName(productSkuVo.getPackAssistantUnitName());
			//采购周期
			accessoriesResp.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
			accessoriesResp.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());

			return accessoriesResp;
		}).collect(Collectors.toList());

		//设置供应商合作关系/最小价格信息
		this.setInvoiceStateAndMinPrice(accessoriesRespList);

		return PageRespVoHelper.of(req.getPageNum(), productSkuVoList.size(), accessoriesRespList);
	}

	private void setInvoiceStateAndMinPrice(List<SpecialAccessoriesProductResp> accessoriesRespList) {
		if (CollUtil.isEmpty(accessoriesRespList)) {
			return;
		}
		//查询供应商信息
		List<Long> supplierIds = accessoriesRespList.stream().map(SpecialAccessoriesProductResp::getSupplierId).collect(Collectors.toList());
		Map<Long, CommoditySupplierInfoVo> supplierMap = this.getSupplierInfo(supplierIds);
		if (CollUtil.isEmpty(supplierMap)) {
			return;
		}

		//查询sku详情获取最小价格信息
		Set<Long> skuIdSet = accessoriesRespList.stream().map(SpecialAccessoriesProductResp::getSkuId).collect(Collectors.toSet());
		List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(skuIdSet);
		Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus)
				.flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));


		//设置供应商合作关系(履约的开票状态); 最小价格单位
		accessoriesRespList.forEach(item -> {
			if (Objects.nonNull(item.getSupplierId()) && Objects.nonNull(supplierMap.get(item.getSupplierId()))) {
				CommoditySupplierInfoVo supplierInfoVo = supplierMap.get(item.getSupplierId());
				CommoditySupplierExtVo supplierExtVo = supplierInfoVo.getExt();
				if (Objects.nonNull(supplierExtVo)) {
					item.setInvoiceState(supplierExtVo.getInvoiceState());
				}
			}
			if (CollUtil.isNotEmpty(accessoriesSkuMap) && Objects.nonNull(accessoriesSkuMap.get(item.getSkuId()))) {
				ProductSkuVo productSkuVo = accessoriesSkuMap.get(item.getSkuId());
				item.setMinPrice(productSkuVo.getMinPrice());
				item.setMinPriceUnit(productSkuVo.getMinUnit());
			}
		});
	}

	/**
	 * 获取供应商信息
	 *
	 * @param supplierIds
	 */
	private Map<Long, CommoditySupplierInfoVo> getSupplierInfo(List<Long> supplierIds) {
		if (CollectionUtil.isEmpty(supplierIds)) {
			return Collections.emptyMap();
		}

		Map<Long, CommoditySupplierInfoVo> supplierInfoVoMap = new HashMap<>();
		try {
			List<CommoditySupplierInfoVo> supplierInfoList = supplierInfoRemoteHelper.getSupplierBaseInfo(supplierIds);
			supplierInfoVoMap = supplierInfoList.stream().collect(Collectors.toMap(CommoditySupplierInfoVo::getSupplierId, Function.identity(), (k1, k2) -> k1));
		} catch (Exception e) {
			log.warn("获取供应商异常", e);
		}
		return supplierInfoVoMap;
	}


	/**
	 * 根据key获取bom缓存在redis中的set值
	 */
	private Set<Long> getBomSetByKey(String bomCraftDelKey) {
		Set<Long> finalIdSet = new HashSet<>();
		Set<Set<Long>> setIds = redisTemplate.opsForSet().members(bomCraftDelKey);
		if (CollUtil.isNotEmpty(setIds)) {
			for (Set<Long> set : setIds) {
				finalIdSet.addAll(set);
			}
		}
		return finalIdSet;
	}

	@Override
	public List<SpecialAccessoriesDetailResp> latestDetail(String designCode) {
		List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.getListByDesignCode(designCode);
		if (CollectionUtil.isEmpty(specialAccessoriesList)) {
			return Collections.emptyList();
		}
		//因之前删除特殊辅料是复制一份再进行删除
		Set<Long> closeAccessoriesFlagIds = specialAccessoriesList.stream()
				.filter(special -> Objects.equals(special.getState(),SpecialAccessoriesStateEnum.CLOSED.getCode()))
				.map(SpecialAccessories::getAccessoriesFlagId).collect(Collectors.toSet());

		BomOrder bomOrder = bomOrderRepository.getLatestBomByDesignCode(designCode);
		//设计款号有BOM则查最新的BOM单下的特殊辅料,无则查当前设计款号下的特殊辅料
		if (Objects.nonNull(bomOrder)) {
			specialAccessoriesList = specialAccessoriesList.stream()
					.filter(special -> Objects.isNull(special.getBomId()) || Objects.equals(bomOrder.getBomId(), special.getBomId()))
					.collect(Collectors.toList());
		}

		if (CollectionUtil.isEmpty(specialAccessoriesList)) {
			return Collections.emptyList();
		}

		//备注
		List<DesignRemarks> designRemarksList = designRemarksRepository.getListByBizChildIds(specialAccessoriesList.stream().map(SpecialAccessories::getSpecialAccessoriesId).collect(Collectors.toList()));
		Map<Long, List<DesignRemarks>> designRemarkMap = designRemarksList.stream().filter(remark -> Objects.nonNull(remark.getBizChildId()))
				.collect(Collectors.groupingBy(DesignRemarks::getBizChildId));

		List<SpecialAccessoriesDetailResp> detailRespList = specialAccessoriesList.stream()
				.filter(special -> Objects.equals(special.getState(), SpecialAccessoriesStateEnum.SUBMIT.getCode()))
				.filter(special -> !closeAccessoriesFlagIds.contains(special.getAccessoriesFlagId()))
				.map(special -> {

					SpecialAccessoriesDetailResp detailResp = new SpecialAccessoriesDetailResp();
					BeanUtils.copyProperties(special, detailResp);

					if (StringUtils.isNotBlank(special.getSkuPicture())) {
						detailResp.setPictureList(Arrays.stream(special.getSkuPicture().split(",")).collect(Collectors.toList()));
					}

					// 特殊辅料备注
					List<DesignRemarks> specialRemarksList = designRemarkMap.get(special.getSpecialAccessoriesId());
					if (CollectionUtil.isNotEmpty(specialRemarksList)) {
						List<MaterialRemarkVo> materialRemarkList = specialRemarksList.stream().sorted(Comparator.comparing(DesignRemarks::getCreatedTime).reversed())
								.map(remark -> {
									MaterialRemarkVo materialRemark = new MaterialRemarkVo();
									BeanUtils.copyProperties(remark, materialRemark);
									return materialRemark;
								}).collect(Collectors.toList());
						detailResp.setMaterialRemarkList(materialRemarkList);
					}

					return detailResp;
				}).sorted(Comparator.comparing(SpecialAccessoriesDetailResp::getName)).collect(Collectors.toList());

		return detailRespList;
	}

	@Override
	public List<BomOrderMaterialVo> getSpecialAccessoriesNuclearPrice(SpecialAccessoriesReq req) {
		List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.getListBySpecialAccessoriesIds(req.getSpecialAccessoriesIds());
		if (CollectionUtil.isEmpty(specialAccessoriesList)) {
			return Collections.emptyList();
		}

		List<BomOrderMaterialVo> orderMaterialVoList = specialAccessoriesList.stream()
				.map(special -> {
					BomOrderMaterialVo bomOrderMaterialVo = new BomOrderMaterialVo();
					BeanUtils.copyProperties(special, bomOrderMaterialVo);
					bomOrderMaterialVo.setBomMaterialId(special.getSpecialAccessoriesId());
					bomOrderMaterialVo.setPrototypeMaterialName(special.getName());
					bomOrderMaterialVo.setDemandType(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode());
					bomOrderMaterialVo.setCommodityType(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.name());
					bomOrderMaterialVo.setCommodityName(special.getSpuName());
					bomOrderMaterialVo.setCommodityId(special.getSpuId());
					if (StringUtils.isNotBlank(special.getSkuPicture())) {
						bomOrderMaterialVo.setMatchPictureList(Arrays.stream(special.getSkuPicture().split(",")).collect(Collectors.toList()));
					}
					bomOrderMaterialVo.setCommodityCode(special.getSpuCode());
					return bomOrderMaterialVo;
				}).sorted(Comparator.comparing(BomOrderMaterialVo::getPrototypeMaterialName)).collect(Collectors.toList());

		return orderMaterialVoList;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void purgeCommodityNumber() {
		List<SpecialAccessories> accessoriesList = specialAccessoriesRepository.list(new QueryWrapper<SpecialAccessories>().lambda().isNull(SpecialAccessories::getCommodityNumber));
		if (CollectionUtil.isEmpty(accessoriesList)) {
			log.info("清洗特殊辅料无商品货号 无满足条件数据");
		}

		Map<Long, ProductSkuVo> totalSkuMap = new HashMap<>(4000);
		Set<Long> skuIds = accessoriesList.stream().map(SpecialAccessories::getSkuId).collect(Collectors.toSet());
		CollectionUtil.split(skuIds,200).stream().parallel().forEach(splitSkuIds -> {
			List<ProductSpuInfoVo> spuInfoVos = productRemoteHelper.getAccessoriesSkuInfo(Sets.newHashSet(splitSkuIds));
			Map<Long, ProductSkuVo> skuVoMap = spuInfoVos.stream().map(ProductSpuInfoVo::getSkus).flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
			if (CollectionUtil.isNotEmpty(skuVoMap)) {
				totalSkuMap.putAll(skuVoMap);
			}
		});

		List<SpecialAccessories> updateSpecials = accessoriesList.stream().map(special -> {
			ProductSkuVo productSkuVo = totalSkuMap.get(special.getSkuId());
			SpecialAccessories updateSpecial = SpecialAccessories.builder().specialAccessoriesId(special.getSpecialAccessoriesId()).build();
			if (Objects.nonNull(productSkuVo)) {
				updateSpecial.setCommodityNumber(productSkuVo.getProductNumber());
			}
			return updateSpecial;
		}).collect(Collectors.toList());

		CollectionUtil.split(updateSpecials,200).forEach(specialAccessoriesRepository::updateBatchById);
	}

	@Override
	public void purgeSpecialAccessoriesMinPrice() {
		List<SpecialAccessories> accessoriesList = specialAccessoriesRepository.list(
				new QueryWrapper<SpecialAccessories>().lambda().isNull(SpecialAccessories::getMinPrice)
		);
		if (CollectionUtil.isEmpty(accessoriesList)) {
			log.info("清洗特殊辅料无最小价格 无满足条件数据");
		}

		Map<Long, ProductSkuVo> totalSkuMap = new HashMap<>(4000);
		Set<Long> skuIds = accessoriesList.stream().map(SpecialAccessories::getSkuId).collect(Collectors.toSet());
		CollectionUtil.split(skuIds,200).stream().parallel().forEach(splitSkuIds -> {
			List<ProductSpuInfoVo> spuInfoVos = productRemoteHelper.getAccessoriesSkuInfo(Sets.newHashSet(splitSkuIds));
			Map<Long, ProductSkuVo> skuVoMap = spuInfoVos.stream().map(ProductSpuInfoVo::getSkus).flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
			if (CollectionUtil.isNotEmpty(skuVoMap)) {
				totalSkuMap.putAll(skuVoMap);
			}
		});

		List<SpecialAccessories> updateSpecials = accessoriesList.stream().map(special -> {
			ProductSkuVo productSkuVo = totalSkuMap.get(special.getSkuId());
			SpecialAccessories updateSpecial = SpecialAccessories.builder().specialAccessoriesId(special.getSpecialAccessoriesId()).build();
			if (Objects.nonNull(productSkuVo) && Objects.nonNull(special.getSupplierId())) {
				updateSpecial.setSupplierId(special.getSupplierId());
				//包装数量单位; 最小价格单位
				updateSpecial.setPackNumber(productSkuVo.getPackNumber());
				updateSpecial.setPackUnitName(productSkuVo.getPackUnitName());
				updateSpecial.setPackAssistantUnitName(productSkuVo.getPackAssistantUnitName());
				updateSpecial.setMinPrice(productSkuVo.getMinPrice());
				updateSpecial.setMinPriceUnit(productSkuVo.getMinUnit());
			}
			return updateSpecial;
		}).collect(Collectors.toList());

		//查询供应商信息
		List<Long> supplierIds = updateSpecials.stream().filter(item -> Objects.nonNull(item.getSupplierId()))
				.map(SpecialAccessories::getSupplierId).distinct().collect(Collectors.toList());
		Map<Long, CommoditySupplierInfoVo> supplierMap = this.getSupplierInfo(supplierIds);
		if (CollUtil.isEmpty(supplierMap)) {
			return;
		}
		//设置供应商合作关系(履约的开票状态)
		updateSpecials.forEach(item -> {
			if (Objects.nonNull(item.getSupplierId()) && Objects.nonNull(supplierMap.get(item.getSupplierId()))) {
				CommoditySupplierInfoVo supplierInfoVo = supplierMap.get(item.getSupplierId());
				CommoditySupplierExtVo supplierExtVo = supplierInfoVo.getExt();
				if (Objects.nonNull(supplierExtVo)) {
					item.setInvoiceState(supplierExtVo.getInvoiceState());
				}
			}
		});

		CollectionUtil.split(updateSpecials,200).forEach(specialAccessoriesRepository::updateBatchById);
	}

    @Override
    public void updateMatchPicture(Integer handleSize) {
		//2022年10月20日之后提交的数据，需要重新拉一次图片
		String dateStr = "2022-10-20 00:00:00";
		LocalDateTime updateBeginDate = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
		List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.listAfterDate(updateBeginDate);
		if (CollUtil.isEmpty(specialAccessoriesList)) {
			return;
		}
		if (Objects.isNull(handleSize) || handleSize < 0) {
			handleSize = 200;
		}
		Set<Long> noUpdateIdAccessories = new HashSet<>();
		List<SpecialAccessories> updateSpecialList = new LinkedList<>();
		for (List<SpecialAccessories> specialChildList : CollectionUtil.split(specialAccessoriesList, handleSize)) {
			Set<Long> accessoriesSkuIds = specialChildList.stream().map(SpecialAccessories::getSkuId).collect(Collectors.toSet());
			//查询辅料信息
			List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
			Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream()
					.collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
			if (CollUtil.isEmpty(accessoriesSpuMap)) {
				noUpdateIdAccessories.addAll(accessoriesSkuIds);
				continue;
			}
			Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus)
					.flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
			if (CollUtil.isEmpty(accessoriesSkuMap)) {
				noUpdateIdAccessories.addAll(accessoriesSkuIds);
				continue;
			}
			specialChildList.forEach(item -> {
				ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(item.getSpuId());
				if (Objects.isNull(productSpuInfoVo)) {
					return;
				}
				ProductSkuVo productSkuVo = accessoriesSkuMap.get(item.getSkuId());
				if (Objects.isNull(productSkuVo)) {
					return;
				}

				if (CollectionUtil.isNotEmpty(productSkuVo.getPictures())){
					List<String> pictureList = productSkuVo.getPictures().stream().map(ProductPictureWebVo::getPicturePath).collect(Collectors.toList());
					SpecialAccessories updateSpecial = new SpecialAccessories();
					updateSpecial.setSpecialAccessoriesId(item.getSpecialAccessoriesId());
					updateSpecial.setSkuPicture(Json.serialize(pictureList));
					updateSpecialList.add(updateSpecial);
				}
				//若sku图为空,取商品图 --v3.5.2_汶俊;
				else if (CollectionUtil.isNotEmpty(productSpuInfoVo.getPictures())){
					List<String> pictureList = productSpuInfoVo.getPictures().stream().map(ProductPictureVo::getPicturePath).collect(Collectors.toList());
					SpecialAccessories updateSpecial = new SpecialAccessories();
					updateSpecial.setSpecialAccessoriesId(item.getSpecialAccessoriesId());
					updateSpecial.setSkuPicture(Json.serialize(pictureList));
					updateSpecialList.add(updateSpecial);
				}
			});
		}
		if (CollUtil.isNotEmpty(updateSpecialList)) {
			specialAccessoriesRepository.updateBatchById(updateSpecialList);
		}

		log.info(" ==== 需要更新的面辅料skuId数量: {} ====", updateSpecialList.size());
		log.info(" ==== 未更新的辅料skuId: {} ====", Json.serialize(noUpdateIdAccessories));
    }


    /**
	 * 获取特殊辅料商品类目ID
	 * @return
	 */
	private List<Long> getSpecialAccessoriesProductCategoryIds() {
		CategoryNameListReq categoryNameListReq = new CategoryNameListReq();
		categoryNameListReq.setCategoryName(SPECIAL_ACCESSORIES_CATEGORY_NAME);
		log.info("【查询特殊辅料类目ID】请求参数:{}", JSON.toJSONString(categoryNameListReq));
		DataResponse<List<CategoryTreeMapVo>> response = categoryClient.listByName(categoryNameListReq);
		log.info("【查询特殊辅料类目ID】响应结果:{}", JSON.toJSONString(response));
		SdpDesignException.isTrue(response.isSuccessful(),"查询特殊辅料类目ID失败");

		List<CategoryTreeMapVo> categoryTreeMapVoList = response.getData();
		if (CollectionUtil.isEmpty(categoryTreeMapVoList)) {
			return Collections.emptyList();
		}

		List<Long> categoryIds = new ArrayList<>(100);
		for (CategoryTreeMapVo categoryTreeMapVo : categoryTreeMapVoList) {
			categoryIds.add(categoryTreeMapVo.getCategoryId());
			CategoryTreeVo categoryTreeVo = categoryTreeMapVo.getTree();

			while (Objects.nonNull(categoryTreeVo)) {
				categoryIds.add(categoryTreeVo.getCategoryId());
				categoryTreeVo = categoryTreeVo.getChild();
			}

		}

		return categoryIds.stream().distinct().collect(Collectors.toList());
	}
}
