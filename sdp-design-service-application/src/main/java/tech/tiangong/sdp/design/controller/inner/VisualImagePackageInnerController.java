package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.converter.visual.VisualImagePackageConverter;
import tech.tiangong.sdp.design.service.VisualImagePackageService;
import tech.tiangong.sdp.design.vo.req.visual.MigrateImagePackageReq;
import tech.tiangong.sdp.design.vo.resp.visual.MigrateImagePackageResp;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageResp;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageVo;


/**
 * 视觉图包处理-inner
 *
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/visual-image-package")
public class VisualImagePackageInnerController extends BaseController {
    private final VisualImagePackageService visualImagePackageService;
    private final VisualImagePackageConverter visualImagePackageConverter;
    /**
     * 根据spu获取图包
     */
    @GetMapping("/get-image-package-by-style-code/{styleCode}")
    public DataResponse<VisualImagePackageResp> getImagePackageByStyleCode(@PathVariable(value = "styleCode") String styleCode) {
        VisualImagePackageVo vo = visualImagePackageService.getVisualImagePackageVoByStyleCode(styleCode);
        return DataResponse.ok(visualImagePackageConverter.trans2VisualImagePackageResp(vo));
    }





    /**
     * 迁移图包数据根据主图比例
     */
    @PostMapping("/migrate-by-ratio")
    public DataResponse<MigrateImagePackageResp> migrateImagePackageByRatio(@RequestBody MigrateImagePackageReq req) {
        setDefaultUser();
        return DataResponse.ok(visualImagePackageService.migrateImagePackageByRatio(req));
    }




    /**
     * 刷历史图包裁剪图
     */
    @PostMapping("/data-brush-batch-crop")
    public DataResponse<Void> dataBrushBatchCrop(@RequestBody MigrateImagePackageReq req) {
        setDefaultUser();
        visualImagePackageService.dataBrushBatchCrop(req);
        return DataResponse.ok();
    }



    public void setDefaultUser() {
        UserContentHolder.set(new UserContent()
                .setSystemCode("SDP")
                .setCurrentUserId(0L)
                .setCurrentUserName("系统")
                .setCurrentUserCode("Hello")
                .setTenantId(1L));
    }
}
