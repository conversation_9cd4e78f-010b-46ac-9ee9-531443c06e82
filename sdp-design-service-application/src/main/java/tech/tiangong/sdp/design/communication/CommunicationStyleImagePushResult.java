package tech.tiangong.sdp.design.communication;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 货通款式图库推送结果
 *
 * <AUTHOR>
 * @since 2025-09-16
 */
@Data
public class CommunicationStyleImagePushResult {

    /**
     * SPU编码
     */
    @TableId
    private String spuCode;

    /**
     * 推送状态
     */
    private PushStatus status;

    /**
     * 失败原因
     */
    private String failedReason;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
}
