package tech.tiangong.sdp.design.remote;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.net.DataResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tech.tiangong.sdp.clothes.client.DigitalPaintingClient;
import tech.tiangong.sdp.clothes.client.EstimateCheckPriceClient;
import tech.tiangong.sdp.clothes.client.SampleClothesClient;
import tech.tiangong.sdp.clothes.client.SampleClothesExceptionClient;
import tech.tiangong.sdp.clothes.enums.ClothesStepEnum;
import tech.tiangong.sdp.clothes.vo.req.*;
import tech.tiangong.sdp.clothes.vo.req.digitalpainting.PushSupplierByOnSaleReq;
import tech.tiangong.sdp.clothes.vo.resp.ClothesProcessInfoVo;
import tech.tiangong.sdp.clothes.vo.resp.SampleClothesVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.EstimateCheckPriceVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.entity.SpotSpu;
import tech.tiangong.sdp.design.enums.SupplyModeEnum;
import tech.tiangong.sdp.design.enums.spot.SpotPriceStateEnum;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.vo.dto.NoticeEstimateCheckPriceCancelSpotSpuDto;
import tech.tiangong.sdp.design.vo.dto.NoticeEstimateCheckPriceUpdateSpotSpuDto;
import tech.tiangong.sdp.design.vo.dto.SpotCreateEstimateCheckPriceDTO;
import tech.tiangong.sdp.design.vo.dto.spot.SpotSpuEstimateCheckPriceDto;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 样衣打版服务_远程接口调用
 *
 * @author: cenlijin
 * @date: 2021/9/22 15:23
 */

@Service
@Slf4j
@AllArgsConstructor
public class SampleClothesRemoteHelper {

    /**
     * 样衣打版服务_feign
     */
    private final SampleClothesClient sampleClothesClient;

    /**
     * 打版异常接口
     */
    private final SampleClothesExceptionClient sampleClothesExceptionClient;

    /**
     * 数码描稿任务
     */
    private final DigitalPaintingClient digitalPaintingClient;

    /**
     * 预估核价
     */
    private final EstimateCheckPriceClient estimateCheckPriceClient;

    private final MqProducer mqProducer;


    /**
     * 发起数码描稿任务
     * @param designCode skc
     */
    public void digitalPainting(String designCode){
        SdpDesignException.notBlank(designCode,"skc为空! ");
        PushSupplierByOnSaleReq req = new PushSupplierByOnSaleReq();
        req.setDesignCode(designCode);
        log.info("=== 发起数码描稿 入参：{} ===", JSONObject.toJSONString(req));
        try {
            DataResponse<Boolean> response = digitalPaintingClient.pushSupplierByOnSale(req);
            log.info("=== 发起数码描稿 response:{}", JSON.toJSONString(response));
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
        } catch (Exception e) {
            throw new SdpDesignException("发起数码描稿调用服务失败:"+e.getMessage(), e);
        }
    }

    /**
     * 判断版单在车版环节是否存在未完结的异常
     * @param clothesId 加工单id
     */
    public Boolean checkSewHasNotEndException(Long clothesId){
        Assert.notNull(clothesId,"clothesId为空! ");
        GuardSampleClothingExceptionReq exceptionReq = new GuardSampleClothingExceptionReq();
        exceptionReq.setClothesId(clothesId);
        exceptionReq.setClothesStep(ClothesStepEnum.SEW);
        log.info("=== 打版异常查询 req：{}; ===", JSONObject.toJSONString(exceptionReq));
        try {
            DataResponse<Boolean> response = sampleClothesExceptionClient.checkHasNotEndException(exceptionReq);
            log.info("=== 打版异常查询 response:{}", JSON.toJSONString(response));
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            return response.getData();
        } catch (Exception e) {
            throw new SdpDesignException("打版异常查询调用服务失败:"+e.getMessage(), e);
        }
    }


    /**
     * 发起打版
     * @param req 入参
     */
    public SampleClothesVo createSampleClothes(CreateSampleClothesReq req){
        Assert.notNull(req,"发起打版入参为空! ");
        log.info("=== 发起打版 入参：{} ===", JSONObject.toJSONString(req));
        try {
            DataResponse<SampleClothesVo> response = sampleClothesClient.createSampleClothes(req);
            log.info("=== 发起打版 response:{}", JSON.toJSONString(response));
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            return response.getData();
        } catch (Exception e) {
            throw new SdpDesignException("发起打版调用服务失败:"+e.getMessage(), e);
        }
    }


    /**
     * 查询版单_环节信息列表
     * @param designCodeList 入参
     */
    public Map<String, List<ClothesProcessInfoVo>> getClothesProcessInfo(List<String> designCodeList){
        Assert.notEmpty(designCodeList,"设计款号列表为空! ");
        DesignCodeListReq req = new DesignCodeListReq();
        req.setDesignCodeList(designCodeList);
        log.info("===【查询版单_纸样_尺寸信息】，入参：{} ===", JSONObject.toJSONString(req));
        try {
            DataResponse<Map<String, List<ClothesProcessInfoVo>>> response = sampleClothesClient.getClothesProcessInfo(req);
            log.info("【查询版单_纸样_尺寸信息】response:{}", JSON.toJSONString(response));
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            return response.getData();
        } catch (Exception e) {
            throw new SdpDesignException("【查询版单_纸样_尺寸信息】调用服务失败:"+e.getMessage(), e);
        }
    }

    /**
     * 设计拆版更新样衣属性
     * @param updateSampleClothesReq 入参
     */
    public void demolitionUpdateSampleClothes(DemolitionUpdateSampleClothesReq updateSampleClothesReq){
        log.info("【设计拆版更新样衣属性】请求参数 {} ", JSONObject.toJSONString(updateSampleClothesReq));
        try {
            DataResponse<Void> response = sampleClothesClient.demolitionUpdateSampleClothes(updateSampleClothesReq);
            log.info("【设计拆版更新样衣属性】response:{}", JSON.toJSONString(response));
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
        } catch (Exception e) {
            throw new SdpDesignException("【设计拆版更新样衣属性】调用服务失败:" + e.getMessage(), e);
        }
    }

    /**
     * spu编辑更新款式品类信息
     * @param req 入参
     */
    public void spuUpdateSampleClothes(SpuUpdateSampleClothesReq req){
        log.info("SPU编辑更新款式品类信息-请求参数 {} ", JSONObject.toJSONString(req));
        SdpDesignException.notNull(req, "入参为空! ");
        SdpDesignException.notEmpty(req.getSpuUpdateList(), "spu更新新为空! ");
        try {
            DataResponse<Void> response = sampleClothesClient.spuUpdateSampleClothes(req);
            log.info("SPU编辑更新款式品类信息-response:{}", JSON.toJSONString(response));
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
        } catch (Exception e) {
            throw new SdpDesignException("SPU编辑更新款式品类信息-调用服务失败:" + e.getMessage(), e);
        }
    }

    // public SampleClothesInfoVo designerInfoByDesignCode(String designCode){
    //     SdpDesignException.notBlank(designCode,"设计款号为空! ");
    //
    //     DataResponse<List<SampleClothesVo>> sampleClothesResponse = sampleClothesClient.findByDesignCode(designCode);
    //     if(!sampleClothesResponse.isSuccessful() || CollectionUtil.isEmpty(sampleClothesResponse.getData())){
    //
    //         return null;
    //     }
    //     SampleClothesVo sampleClothesVo = sampleClothesResponse.getData().get(0);
    //     DataResponse<SampleClothesInfoVo> clothesInfoResponse = sampleClothesClient.getClothesInfoById(sampleClothesVo.getClothesId());
    //     if(!clothesInfoResponse.isSuccessful() || ObjectUtil.isEmpty(clothesInfoResponse.getData())){
    //
    //         return null;
    //     }
    //     return clothesInfoResponse.getData();
    // }


    /**
     * 根据SPU获取样衣打版信息(不包含已取消的打版任务)
     *
     * @param styleCode
     * @return
     */
    public List<SampleClothesVo> findByStyleCode(String styleCode) {
        DataResponse<List<SampleClothesVo>> response = sampleClothesClient.findByStyleCode(styleCode);
        if (!response.isSuccessful()) {
            throw new SdpDesignException("调用打版服务sampleClothesClient.findByStyleCode接口失败，message = " + response.getMessage());
        }
        return response.getData();
    }

    /**
     * 根据SPU获取样衣打版信息(包含已取消的打版任务)
     *
     * @param styleCode
     * @return
     */
    public List<SampleClothesVo> listByStyleCode(String styleCode) {
        DataResponse<List<SampleClothesVo>> response = sampleClothesClient.listByStyleCode(styleCode);
        if (!response.isSuccessful()) {
            throw new SdpDesignException("调用打版服务sampleClothesClient.listByStyleCode接口失败，message = " + response.getMessage());
        }
        return response.getData();
    }

    /**
     * 根据设计款号获取样衣打版信息
     *
     * @param designCode 设计款号
     * @return List<SampleClothesVo>
     */
    public List<SampleClothesVo> findByDesignCode(String designCode) {
        DataResponse<List<SampleClothesVo>> response = sampleClothesClient.findByDesignCode(designCode);
        if (!response.isSuccessful()) {
            throw new SdpDesignException("调用打版服务【根据设计款号获取样衣打版信息】接口失败，message = " + response.getMessage());
        }
        return response.getData();
    }

    /**
     * 根据设计款号判断是否是首单
     *
     * @param designCode 设计款号
     * @return Boolean
     */
    public Boolean isFirstOrder(String designCode) {
        DataResponse<Boolean> response = sampleClothesClient.isFirstOrder(designCode);
        if (!response.isSuccessful()) {
            throw new SdpDesignException("调用打版服务【根据设计款号判断是否是首单】接口失败，message = " + response.getMessage());
        }
        return response.getData();
    }

    // /**
    //  * 根据设计款号获取样衣打版信息（批量）
    //  *
    //  * @param designCodeList
    //  * @return
    //  */
    // public List<SampleClothesVo> findByDesignCodes(List<String> designCodeList) {
    //     if (CollectionUtil.isEmpty(designCodeList)) {
    //         return new ArrayList<>();
    //     }
    //     DataResponse<List<SampleClothesVo>> response = sampleClothesClient.findByDesignCode(designCodeList);
    //     if (!response.isSuccessful()) {
    //         throw new SdpDesignException("调用打版服务【根据设计款号获取样衣打版信息】接口失败，message = " + response.getMessage());
    //     }
    //     return response.getData();
    // }


    /**
     * 根据设计款号获取样衣最新打版信息
     * @param designCode
     * @return
     */
    public SampleClothesVo getLatestSampleClothes(String designCode) {
        DataResponse<List<SampleClothesVo>> sampleClothesVoResponse = sampleClothesClient.findByDesignCode(designCode);
        if (sampleClothesVoResponse.isSuccessful() && CollectionUtil.isNotEmpty(sampleClothesVoResponse.getData())) {
            List<SampleClothesVo> vo = sampleClothesVoResponse.getData().stream()
                    .sorted(Comparator.comparing(SampleClothesVo::getCreatedTime).reversed()).collect(Collectors.toList());
            return vo.get(0);
        }
        return null;
    }

    /**
     * 通知创建现货tryOn的预估核价
     */
    @Transactional(rollbackFor = Exception.class)
    public void noticeCreateSpotTryOnEstimateCheckPrice(SpotSpu spotSpu) {
        log.info("通知创建现货tryOn的预估核价styleCode:{}",spotSpu.getStyleCode());
        //SPU的供给方式=现货try on
        if(Objects.equals(spotSpu.getSupplyModeCode(), SupplyModeEnum.TRY_ON.getCode())
            && spotSpu.getPredictCheckPriceId()==null){
            SpotCreateEstimateCheckPriceDTO mqDTO = new SpotCreateEstimateCheckPriceDTO();
            mqDTO.setStyleCode(spotSpu.getStyleCode());
            MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.CREATE_SPOT_ESTIMATE_CHECK_PRICE,
                    DesignMqConstant.SPOT_CREATE_ESTIMATE_CHECK_PRICE_EXCHANGE,
                    DesignMqConstant.SPOT_CREATE_ESTIMATE_CHECK_PRICE_ROUTING_KEY,
                    JSONUtil.toJsonStr(mqDTO));
            mqProducer.sendOnAfterCommit(mqMessageReq);
        }else {
            log.info("不需要通知创建现货tryOn的预估核价styleCode:{}，原因：供给方式不是tryOn或者已经有对应的核价任务",spotSpu.getStyleCode());
        }

    }

    /**
     * 通知预估核价，现货SPU已取消
     */
    @Transactional(rollbackFor = Exception.class)
    public void noticeEstimateCheckPriceCancelSpotSpu(List<String> styleCodes) {
        log.info("通知预估核价，现货SPU已取消styleCodes:{}",styleCodes);
        NoticeEstimateCheckPriceCancelSpotSpuDto mqDTO = new NoticeEstimateCheckPriceCancelSpotSpuDto();
        mqDTO.setStyleCodes(styleCodes);
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.NOTICE_ESTIMATE_CHECK_PRICE_CANCEL_SPOT_SPU,
                DesignMqConstant.CANCEL_SPOT_SPU_TO_ESTIMATE_CHECK_PRICE_EXCHANGE,
                DesignMqConstant.CANCEL_SPOT_SPU_TO_ESTIMATE_CHECK_PRICE_ROUTING_KEY,
                JSONUtil.toJsonStr(mqDTO));
        mqProducer.sendOnAfterCommit(mqMessageReq);

    }

    /**
     * 通知预估核价，现货SPU已更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void noticeEstimateCheckPriceUpdateSpotSpu(List<String> styleCodes) {
        log.info("通知预估核价，现货SPU已更新styleCodes:{}",styleCodes);
        NoticeEstimateCheckPriceUpdateSpotSpuDto mqDTO = new NoticeEstimateCheckPriceUpdateSpotSpuDto();
        mqDTO.setStyleCodes(styleCodes);
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.NOTICE_ESTIMATE_CHECK_PRICE_UPDATE_SPOT_SPU,
                DesignMqConstant.SPOT_SPU_UPDATE_TO_ESTIMATE_CHECK_PRICE_EXCHANGE,
                DesignMqConstant.SPOT_SPU_UPDATE_TO_ESTIMATE_CHECK_PRICE_ROUTING_KEY,
                JSONUtil.toJsonStr(mqDTO));
        mqProducer.sendOnAfterCommit(mqMessageReq);

    }

    /**
     * 通过核价ID批次查询预估核价
     */
    public List<EstimateCheckPriceVo> listEstimateCheckPriceByIds(List<Long> estimateCheckPriceIds){
        log.info("【通过核价ID批次查询预估核价】调用sdp-sample-clothes服务 请求参数 estimateCheckPriceIds:{}", JSON.toJSONString(estimateCheckPriceIds));
        try {
            DataResponse<List<EstimateCheckPriceVo>> response = estimateCheckPriceClient.listById(estimateCheckPriceIds);
            log.info("【通过核价ID批次查询预估核价】响应结果:{}", JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                throw new SdpDesignException("【通过核价ID批次查询预估核价】失败:"+response.getMessage());
            }
            return response.getData();
        } catch (Exception e) {
            log.error("【通过核价ID批次查询预估核价】异常",e);
//            throw new SdpDesignException("【通过核价ID批次查询预估核价】异常");
        }
        return Collections.emptyList();
    }

    public SpotSpuEstimateCheckPriceDto getEstimateCheckPriceBySpotSpu(SpotSpu spotSpu){
        if(spotSpu == null || spotSpu.getPredictCheckPriceId() == null){
            return null;
        }
        List<EstimateCheckPriceVo> list = listEstimateCheckPriceByIds(Arrays.asList(spotSpu.getPredictCheckPriceId()));
        if(CollectionUtil.isNotEmpty(list)){
            EstimateCheckPriceVo estimateCheckPriceVo = list.get(0);
            SpotSpuEstimateCheckPriceDto dto = new SpotSpuEstimateCheckPriceDto();
            dto.setPredictCheckPriceId(estimateCheckPriceVo.getEstimateCheckPriceId());
            dto.setTotalCost(estimateCheckPriceVo.getTotalCost());
            dto.setPriceType(estimateCheckPriceVo.getPriceType());
            dto.setPredictCheckPriceTime(estimateCheckPriceVo.getFinishTime());
            dto.setStyleCode(estimateCheckPriceVo.getStyleCode());
            //100待核价 110已核价 120驳回
            Integer state = estimateCheckPriceVo.getState();
            switch (state){
                case 100: dto.setSpotPriceState(SpotPriceStateEnum.WAIT_CHECK);break;
                case 110: {
                        dto.setSpotPriceState(SpotPriceStateEnum.CHECKED);

                        if(estimateCheckPriceVo.getIsReview()!=null && estimateCheckPriceVo.getIsReview().equals(1)){
                            dto.setSpotPriceState(SpotPriceStateEnum.RE_CHECK_PASS);
                        }
                    }
                    break;
                case 120: dto.setSpotPriceState(SpotPriceStateEnum.REJECT);break;
                default: dto.setSpotPriceState(SpotPriceStateEnum.WAIT_CHECK);break;
            }
            dto.setPricerId(estimateCheckPriceVo.getPricerId());
            dto.setPricerName(estimateCheckPriceVo.getPricerName());
            dto.setDisapprovalReason(estimateCheckPriceVo.getDisapprovalReason());
            return dto;
        }
        return null;
    }
}

