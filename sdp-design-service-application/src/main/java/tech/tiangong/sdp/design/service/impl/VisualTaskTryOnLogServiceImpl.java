package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zjkj.aigc.DataResponse;
import com.zjkj.aigc.common.dto.*;
import com.zjkj.aigc.common.resp.VirtualDressingTaskV2VO;
import com.zjkj.aigc.common.task.req.virtualdressing.VirtualDressingTaskAddV2Req;
import com.zjkj.aigc.common.task.req.virtualdressing.VirtualDressingTaskDetailBatchReq;
import com.zjkj.aigc.common.task.resp.VirtualDressingTaskDetailV2VO;
import com.zjkj.aigc.feign.client.saas.VirtualDressingTaskV2Client;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.visual.*;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.req.visual.*;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskTryOnBatchDetailVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskTryOnDetailVo;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VisualTaskTryOnLogServiceImpl implements VisualTaskTryOnLogService {
    @Resource
    private VisualTaskTryOnLogRepository visualTaskTryOnLogRepository;
    @Resource
    private VisualTaskTryOnRepository visualTaskTryOnRepository;
    @Resource
    private VisualTaskNodeStateRepository visualTaskNodeStateRepository;
    @Resource
    private VisualTaskNodeStateService nodeStateService;
    @Resource
    private VisualQcRepository qcRepository;
    @Resource
    private VirtualDressingTaskV2Client virtualDressingTaskV2Client;
    @Resource
    private VisualTaskHelper visualTaskHelper;
    @Resource
    private VisualTaskRepository visualTaskRepository;
    @Resource
    private VisualTaskTryOnImageRepository tryOnImageRepository;
    @Resource
    private VisualTaskHandleService visualTaskHandleService;
    @Resource
    private VisualTaskService visualTaskService;
    @Resource
    private VisualTaskTryOnImageService taskTryOnImageService;
    @Resource
    private  VisualDemandRepository visualDemandRepository;
    @Resource
    private VisualTaskDetailRepository visualTaskDetailRepository;
    @Resource
    private VisualTaskOnShelfRepository visualTaskOnShelfRepository;
    @Resource
    private VisualImagePackageRepository visualImagePackageRepository;




    @Override
    public Map<String, List<String>> listTryOnTaskImage(Long taskId) {
        List<VisualTaskTryOnLog> logByVisualTaskId = visualTaskTryOnLogRepository.getValidLogByVisualTaskId(taskId);
        Map<String, List<String>> result = new HashMap<>();
        if (CollectionUtil.isNotEmpty(logByVisualTaskId)) {
            return result;
        }
        for (VisualTaskTryOnLog visualTaskTryOnLog : logByVisualTaskId) {
            List<VisualTaskTryOnImage> visualTaskTryOnImages = tryOnImageRepository.listByTryOnTaskId(Collections.singletonList(visualTaskTryOnLog.getTryOnTaskId()));
            result.put(visualTaskTryOnLog.getTryOnTaskCode(), visualTaskTryOnImages.stream().map(VisualTaskTryOnImage::getGeneratedImg).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public List<VisualTaskTryOnDetailVo> listTryOnTaskImageDetail(Long taskId) {
        List<VisualTaskTryOnDetailVo> result = new ArrayList<>();
        List<VisualTaskTryOnLog> logByVisualTaskId = visualTaskTryOnLogRepository.getValidLogByVisualTaskId(taskId);
        if (CollectionUtil.isEmpty(logByVisualTaskId)){
            return result;
        }
        List<Long> tryOnTaskIdList = logByVisualTaskId.stream().map(VisualTaskTryOnLog::getTryOnTaskId).toList();
        List<VisualTaskTryOnImage> visualTaskTryOnImages = tryOnImageRepository.listByTryOnTaskId(tryOnTaskIdList);

        Map<Long, List<VisualTaskTryOnImage>> tryOnTaskIdMap = visualTaskTryOnImages.stream().collect(Collectors.groupingBy(VisualTaskTryOnImage::getTryOnTaskId));
        List<VisualTaskTryOnDetailVo> resultList = new ArrayList<>();
        logByVisualTaskId.forEach(e -> {
            Optional.ofNullable(tryOnTaskIdMap.get(e.getTryOnTaskId())).ifPresent(list -> {
                resultList.addAll(
                        list.stream().map(img -> {
                            VisualTaskTryOnDetailVo vo = new VisualTaskTryOnDetailVo();
                            BeanUtil.copyProperties(img, vo, "remarkImage");
                            vo.setRemarkImage(JSON.parseObject(img.getRemarkImage(), ImageFile.class));
                            vo.setTryOnTaskId(e.getTryOnTaskId().toString());
                            vo.setTryOnTaskCode(e.getTryOnTaskCode());
                            return  vo;
                        }).toList()
                );
            });
        });
        return resultList;
    }

    @Override
    public List<VisualTaskTryOnBatchDetailVo> batchGetTryOnTaskImageDetail(List<Long> taskId) {
        List<VisualTaskTryOnBatchDetailVo> result = new ArrayList<>();
        List<VisualTaskTryOnLog> logByVisualTaskId = visualTaskTryOnLogRepository.getValidLogByVisualTaskIdBatch(taskId);
        if (CollectionUtil.isEmpty(logByVisualTaskId)){
            return result;
        }
        List<Long> tryOnTaskIdList = logByVisualTaskId.stream().map(VisualTaskTryOnLog::getTryOnTaskId).toList();
        List<VisualTaskTryOnImage> visualTaskTryOnImages = tryOnImageRepository.listByTryOnTaskId(tryOnTaskIdList);
        List<VisualTask> visualTaskList = visualTaskRepository.listByIds(taskId);
        if (CollectionUtil.isEmpty(visualTaskList)) {
            return new ArrayList<>();
        }
        Map<Long, VisualDemand> demandIdMap = visualDemandRepository.listByIds(visualTaskList.stream().map(VisualTask::getLatestDemandId).toList()).stream().collect(Collectors.toMap(VisualDemand::getDemandId, Function.identity(), (k1, k2) -> k1));
        Map<Long, VisualTaskOnShelf> taskIdOnShelfMap = visualTaskOnShelfRepository.listLatestByTaskId(taskId).stream().collect(Collectors.toMap(VisualTaskOnShelf::getTaskId, Function.identity(), (k1, k2) -> k1));
        Map<Long, VisualTaskTryOn> taskIdTryOnMap = visualTaskTryOnRepository.getLatestByTaskIdList(taskId).stream().collect(Collectors.toMap(VisualTaskTryOn::getTaskId, Function.identity(), (k1, k2) -> k1));
        Map<Long, List<VisualTaskTryOnImage>> tryOnTaskIdMap = visualTaskTryOnImages.stream().collect(Collectors.groupingBy(VisualTaskTryOnImage::getTryOnTaskId));
        Map<Long, List<VisualTaskTryOnLog>> taskIdLogMap = logByVisualTaskId.stream().collect(Collectors.groupingBy(VisualTaskTryOnLog::getTaskId));
        Map<String, VisualImagePackage> spuPackageMap = visualImagePackageRepository.listLatestByStyleCodes(visualTaskList.stream().map(VisualTask::getStyleCode).toList()).stream().collect(Collectors.toMap(VisualImagePackage::getStyleCode, v -> v, (k1, k2) -> k2));

        List<VisualTaskTryOnBatchDetailVo> resultList = new ArrayList<>();
        for (VisualTask visualTask : visualTaskList) {
            VisualTaskTryOn visualTaskTryOn = taskIdTryOnMap.get(visualTask.getTaskId());
            if (visualTaskTryOn == null) {
                continue;
            }
            VisualTaskTryOnBatchDetailVo resultVo = new VisualTaskTryOnBatchDetailVo();
            List<VisualTaskTryOnDetailVo> detailList = new ArrayList<>();
            resultVo.setTryOnHandleVersion(visualTaskTryOn.getVersionNum());
            Optional.ofNullable(demandIdMap.get(visualTask.getLatestDemandId())).ifPresent(demand -> resultVo.setDemandVersion(demand.getVersionNum()));
            Optional.ofNullable(taskIdOnShelfMap.get(visualTask.getTaskId())).ifPresent(onShelf -> resultVo.setOnShelfHandleVersion(onShelf.getVersionNum()));
            Optional.ofNullable(spuPackageMap.get(visualTask.getStyleCode())).ifPresent(spuPackage -> resultVo.setImagePackageVersion(spuPackage.getVersionNum()));
            Optional.ofNullable(taskIdLogMap.get(visualTask.getTaskId())).ifPresent(logList -> {
                logList.forEach(e -> {
                    Optional.ofNullable(tryOnTaskIdMap.get(e.getTryOnTaskId())).ifPresent(list -> {
                        detailList.addAll(
                                list.stream().map(img -> {
                                    VisualTaskTryOnDetailVo vo = new VisualTaskTryOnDetailVo();
                                    BeanUtil.copyProperties(img, vo, "remarkImage");
                                    vo.setRemarkImage(JSON.parseObject(img.getRemarkImage(), ImageFile.class));
                                    vo.setTryOnTaskId(e.getTryOnTaskId().toString());
                                    vo.setTryOnTaskCode(e.getTryOnTaskCode());
                                    return  vo;
                                }).toList()
                        );
                    });
                    resultVo.setVisualTaskId(e.getTaskId().toString());
                    resultVo.setProcessCode(e.getTaskCode());
                    resultVo.setTryOnList(detailList);

                });
            });
            resultList.add(resultVo);
        }
        return resultList;
    }

    @Transactional
    @Override
    public void doSubmitTryOn(AtomicInteger successCount, StringBuilder failProcessCode, VisualTask visualTask, BatchTryOnReq req) {
        if(!VisualTaskStateEnum.DOING.getCode().equals(visualTask.getState())) {
            log.info("batch try on task state is not doing, taskId:{}", visualTask.getTaskId());
            return;
        }
        VisualTaskTryOn tryOnDetail =  visualTaskTryOnRepository.getOne(new LambdaQueryWrapper<VisualTaskTryOn>()
                .eq(VisualTaskTryOn::getTaskId, visualTask.getTaskId())
                .eq(VisualTaskTryOn::getIsLatest, 1)
                .last("limit 1"));
        // try on已经取消或者已经质检完成，不允许再提交任务
        if (tryOnDetail == null ||  VisualTaskHandleStateEnum.CANCEL.getCode().equals(tryOnDetail.getHandleState()) || VisualTaskHandleStateEnum.FINISH.getCode().equals(tryOnDetail.getHandleState())) {
            log.info("try on task current state can't handle, taskId:{}", visualTask.getTaskId());
            return;
        }
        VisualTaskNodeState tryOnHandleNodeState = visualTaskNodeStateRepository.getNodeByProcessNode(visualTask.getTaskId(), VisualTaskNodeEnum.TRYON_HANDLE);
        VisualTaskNodeState tryOnQCNode = visualTaskNodeStateRepository.getNodeByProcessNode(visualTask.getTaskId(), VisualTaskNodeEnum.TRY_ON_QC);
        if (tryOnHandleNodeState == null) {
            log.error("batch try on task is not exist, taskId:{}", visualTask.getTaskId());
            return;
        }
        // 1.try on 进行中
        boolean tryOnHandling= VisualTaskHandleStateEnum.DOING.getCode().equals(tryOnHandleNodeState.getProcessNodeState());
        // 2.try on已完成，try on质检中，重新发起try on
        boolean tryOnDoneOrQc = VisualTaskHandleStateEnum.FINISH.getCode().equals(tryOnHandleNodeState.getProcessNodeState()) &&
                VisualTaskHandleStateEnum.getProcessingStatus().contains(tryOnQCNode.getProcessNodeState());
        // 以上情况都不是 不允许发起try on任务
        if (!tryOnHandling && !tryOnDoneOrQc) {
            log.error("current step is not allow to submit try on task, taskId:{}", visualTask.getTaskId());
            return;
        }

        VirtualDressingTaskAddV2Req taskAddV2Req = buildVirtualDressingTaskAddV2Req(req);
        DataResponse<VirtualDressingTaskV2VO> response = virtualDressingTaskV2Client.save(taskAddV2Req);
        if (!response.isSuccessful() || response.getData() == null) {
            log.error("batch try on request aigc-server fail,taskId:{}", visualTask.getTaskId());
            failProcessCode.append(" ");
            failProcessCode.append(visualTask.getProcessCode());
        }else {
            List<VisualTaskTryOnLog> tryOnLogs = new ArrayList<>();
            for (Long taskId : Objects.requireNonNull(response.getData().getTaskIds())) {
                // 创建try on log
                VisualTaskTryOnLog visualTaskTryOnLog = new VisualTaskTryOnLog();
                visualTaskTryOnLog.setVisualTaskTryOnLogId(IdPool.getId());
                visualTaskTryOnLog.setTryOnDetailId(tryOnDetail.getTryOnDetailId());
                visualTaskTryOnLog.setTaskId(visualTask.getTaskId());
                visualTaskTryOnLog.setTaskCode(visualTask.getProcessCode());
                visualTaskTryOnLog.setTryOntTaskState(VisualTaskHandleStateEnum.WAITING_START.getCode());
                visualTaskTryOnLog.setTryOnTaskId(taskId);
                tryOnLogs.add(visualTaskTryOnLog);
            }
            visualTaskTryOnLogRepository.saveBatch(tryOnLogs);
            // 修改try on任务状态
            tryOnDetail.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
            tryOnHandleNodeState.setProcessNodeState(VisualTaskHandleStateEnum.DOING.getCode());
            tryOnHandleNodeState.setRemark(VisualTaskNodeEnum.TRYON_HANDLE.getDesc() + ":" + VisualTaskHandleStateEnum.DOING.getDesc());

            visualTaskTryOnRepository.updateById(tryOnDetail);
            visualTaskNodeStateRepository.updateById(tryOnHandleNodeState);
            // 如果已经开始try on质检了 要转回到try on进行中状态
            if (tryOnQCNode != null) {
                tryOnQCNode.setProcessNodeState(VisualTaskHandleStateEnum.WAITING_START.getCode());
                tryOnQCNode.setRemark(VisualTaskNodeEnum.TRY_ON_QC.getDesc() + ":" +VisualTaskHandleStateEnum.WAITING_START.getDesc());
                visualTaskNodeStateRepository.updateById(tryOnQCNode);
            }
            successCount.getAndIncrement();

        }

        visualTaskHelper.addLog(visualTask.getStyleCode(), visualTask.getTaskId(), "发起线上try on任务");

    }

    @Override
    public void syncVisualTaskTryOnState(SyncVisualTaskTryOnStateReq req) {
        List<VisualTaskTryOnLog>  visualTaskTryOnLogList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(req.getTaskIdList())) {
            visualTaskTryOnLogList = visualTaskTryOnLogRepository.list(new LambdaQueryWrapper<VisualTaskTryOnLog>()
                    .in(VisualTaskTryOnLog::getTaskId, req.getTaskIdList()));
        }else if (CollectionUtil.isNotEmpty(req.getTryOnTaskIdList())) {
            visualTaskTryOnLogList = visualTaskTryOnLogRepository.list(new LambdaQueryWrapper<VisualTaskTryOnLog>()
                    .in(VisualTaskTryOnLog::getTryOnTaskId, req.getTryOnTaskIdList()));
        }else {
            //  忽略任务的状态，直接靠try on 任务的状态去拉数据
            visualTaskTryOnLogList = visualTaskTryOnLogRepository.list(new LambdaQueryWrapper<VisualTaskTryOnLog>()
                    .in(VisualTaskTryOnLog::getTryOntTaskState, Arrays.asList(VisualTaskHandleStateEnum.WAITING_START.getCode(), VisualTaskHandleStateEnum.DOING.getCode())));
        }
        if (CollectionUtil.isNotEmpty(visualTaskTryOnLogList)){
            Set<Long> tryOnTaskIdList = visualTaskTryOnLogList.stream().map(VisualTaskTryOnLog::getTryOnTaskId).collect(Collectors.toSet());

            VirtualDressingTaskDetailBatchReq taskDetailBatchReq = new VirtualDressingTaskDetailBatchReq();
            taskDetailBatchReq.setTaskIds(tryOnTaskIdList.stream().toList());
            DataResponse<List<VirtualDressingTaskDetailV2VO>> dataResponse = virtualDressingTaskV2Client.queryDetailByTaskIds(taskDetailBatchReq);
            if (!dataResponse.isSuccessful()) {
                log.error("查询虚拟试衣任务详情失败:{}", dataResponse.getMessage());
                return;
            }
            if (dataResponse.getData() == null || CollectionUtil.isEmpty(dataResponse.getData())) {
                log.error("查询虚拟试衣任务详情为空");
                return;
            }
            List<VirtualDressingTaskDetailV2VO> virtualDressingTaskDetailV2VOList = dataResponse.getData();
            Map<Long, VirtualDressingTaskDetailV2VO> taskIdMap = virtualDressingTaskDetailV2VOList.stream().collect(Collectors.toMap(VirtualDressingTaskDetailV2VO::getTaskId, Function.identity(), (v1, v2) -> v1));

            // 大概写一下处理逻辑
            for (VisualTaskTryOnLog visualTaskTryOnLog : visualTaskTryOnLogList) {
                Optional.ofNullable(taskIdMap.get(visualTaskTryOnLog.getTryOnTaskId())).ifPresent(e -> {
                    try {
                        taskTryOnImageService.handleTryOnResult(e, visualTaskTryOnLog);
                    }catch (Exception ex){
                        log.error("syncVisualTaskTryOnState catch error, visualTaskId: {},  message:{}",visualTaskTryOnLog.getTaskId(), ex.getMessage());
                    }
                });

            }
        }
    }

    @Transactional
    @Override
    public void doSubmitTryOnQC(BatchSubmitTryOnReq req) {
        VisualTask visualTask = visualTaskRepository.getById(req.getTaskId());
        Assert.notNull(visualTask, "视觉任务不存在！");
        Assert.isTrue(VisualTaskStateEnum.DOING.getCode().equals(visualTask.getState()), "视觉任务非进行中！");
        // 更新try on 图片记录
        List<BatchSubmitTryOnReq.ImageQcResult> imageQcResultList = req.getImageQcResultList();
        List<VisualTaskTryOnImage> visualTaskTryOnImages = tryOnImageRepository.listByIds(
                imageQcResultList.stream().map(BatchSubmitTryOnReq.ImageQcResult::getTryOnTaskImageId).collect(Collectors.toList()));
        Map<Long, BatchSubmitTryOnReq.ImageQcResult> imageIdResultMap = imageQcResultList.stream().collect(Collectors.toMap(BatchSubmitTryOnReq.ImageQcResult::getTryOnTaskImageId, Function.identity(), (v1, v2) -> v1));
        for (VisualTaskTryOnImage visualTaskTryOnImage : visualTaskTryOnImages) {
            Optional.ofNullable(imageIdResultMap.get(visualTaskTryOnImage.getTryOnTaskImageId())).ifPresent(imageQcResult -> {
                visualTaskTryOnImage.setAvailable(imageQcResult.getAvailable());
                visualTaskTryOnImage.setRemarkContext(imageQcResult.getRemarkContext());
                visualTaskTryOnImage.setRemarkImage(imageQcResult.getRemarkImage() == null ? null :JSON.toJSONString(imageQcResult.getRemarkImage()));
                visualTaskTryOnImage.setIsMain(imageQcResult.getIsMain());
                if (Objects.equals(imageQcResult.getIsMain(), 1)) {
                    visualTaskTryOnImage.setGeneratedImgName(taskTryOnImageService.processImageNameToMainImage(visualTaskTryOnImage.getGeneratedImgName()));
                }
            });
        }
        tryOnImageRepository.updateBatchById(visualTaskTryOnImages);

        VisualQc latestByTaskId = qcRepository.getLatestByTaskId(req.getTaskId(), VisualQcTypeEnum.TRY_ON);
        Assert.notNull(latestByTaskId, "try on任务不存在!");
        VisualTaskDetail visualTaskDetail = visualTaskDetailRepository.getByTaskId(req.getTaskId());
        Assert.notNull(visualTaskDetail, "视觉任务详情不存在！");
        VisualTaskTryOn tryOnDetail = visualTaskTryOnRepository.getById(visualTaskDetail.getLatestTryOnDetailId());

        visualTaskHelper.addLog(visualTask.getStyleCode(), visualTask.getTaskId(), "提交try on质检");
        VisualTaskQcResultEnum qcResult = VisualTaskQcResultEnum.WAITING_QC;
        if (imageQcResultList.stream().allMatch(imageQcResult -> imageQcResult.getAvailable() == 0)) {
            qcResult = VisualTaskQcResultEnum.NO_PASS;

            tryOnDetail.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
            visualTaskTryOnRepository.updateById(tryOnDetail);
            //打回try on处理
            nodeStateService.saveVisualTaskNodeStepState(tryOnDetail.getTaskId(), VisualTaskStepEnum.HANDLE, VisualTaskNodeEnum.TRYON_HANDLE, tryOnDetail.getHandleState());

        } else if (imageQcResultList.stream().anyMatch(imageQcResult -> imageQcResult.getAvailable() == 1)) {
            qcResult = VisualTaskQcResultEnum.PASS;

            TryOnHandleReq tryOnHandleReq = new TryOnHandleReq();
            tryOnHandleReq.setTaskId(req.getTaskId());
            VisualDemand visualDemand = visualDemandRepository.getById(visualTask.getLatestDemandId());
            Assert.notNull(visualDemand, "视觉需求不存在！");
            tryOnHandleReq.setDemandVersion(req.getDemandVersion());

            Assert.notNull(tryOnDetail, "try on 详情不存在！");
            tryOnHandleReq.setTryOnHandleVersion(req.getTryOnHandleVersion());
            tryOnHandleReq.setImagePackageVersion(req.getImagePackageVersion());
            tryOnHandleReq.setOnShelfHandleVersion(req.getOnShelfHandleVersion());


//            OnShelfImagePackage onShelfImagePackage = JSON.parseObject(tryOnDetail.getOnShelfImages(), OnShelfImagePackage.class);
//            if (onShelfImagePackage == null) {
//                onShelfImagePackage = new OnShelfImagePackage();
//            }
//            onShelfImagePackage.setStyleCode(visualTask.getStyleCode());
//            List<OnShelfImagePackage.SpuImage> spuImages = onShelfImagePackage.getSpuImages();
//            if (spuImages == null) {
//                spuImages = new ArrayList<>();
//            }
            List<ImageFile> passQcTryOnList = visualTaskTryOnImages.stream().filter(imageQcResult -> Objects.equals(imageQcResult.getAvailable(), 1))
                    .map(imageQcResult -> {
                        ImageFile imageFile = new ImageFile();
                        imageFile.setOssImageUrl(imageQcResult.getGeneratedImg());
                        imageFile.setOrgImgName(imageQcResult.getGeneratedImgName());
                        return imageFile;
                    }).toList();
            List<ImageFile> mainImageList = visualTaskTryOnImages.stream().filter(imageQcResult -> Objects.equals(imageQcResult.getIsMain(), 1) && Objects.equals(imageQcResult.getAvailable(), 1))
                    .map(imageQcResult -> {
                        ImageFile imageFile = new ImageFile();
                        imageFile.setOssImageUrl(imageQcResult.getGeneratedImg());
                        // 记得改名字
                        imageFile.setOrgImgName(taskTryOnImageService.processImageNameToMainImage(imageQcResult.getGeneratedImgName()));
                        return imageFile;
                    }).toList();
            List<ImageFile> otherImageList = visualTaskTryOnImages.stream().filter(imageQcResult -> Objects.equals(imageQcResult.getIsMain(), 0) && Objects.equals(imageQcResult.getAvailable(), 1))
                    .map(imageQcResult -> {
                        ImageFile imageFile = new ImageFile();
                        imageFile.setOssImageUrl(imageQcResult.getGeneratedImg());
                        imageFile.setOrgImgName(imageQcResult.getGeneratedImgName());
                        return imageFile;
                    }).toList();

//            List<OnShelfImagePackage.SpuImage> existOtherImages = spuImages.stream().filter(spuImage -> Objects.equals(spuImage.getImageType(), VisualTaskImageTypeEnum.OTHER.getCode())).toList();
//            if (CollectionUtil.isNotEmpty(existOtherImages)) {
//                existOtherImages.get(0).getImages().addAll(otherImageList);
//            }else {
//                OnShelfImagePackage.SpuImage spuImage = new OnShelfImagePackage.SpuImage();
//                spuImage.setImageType(VisualTaskImageTypeEnum.OTHER.getCode());
//                spuImage.setImages(otherImageList);
//                spuImages.add(spuImage);
//                onShelfImagePackage.setSpuImages(spuImages);
//            }
//
//            List<OnShelfImagePackage.SpuImage> existMainImages = spuImages.stream().filter(spuImage -> Objects.equals(spuImage.getImageType(), VisualTaskImageTypeEnum.PRODUCT_MAIN.getCode())).toList();
//            if (CollectionUtil.isNotEmpty(existMainImages)) {
//                existMainImages.get(0).getImages().addAll(mainImageList);
//            }else {
//                OnShelfImagePackage.SpuImage spuImage = new OnShelfImagePackage.SpuImage();
//                spuImage.setImageType(VisualTaskImageTypeEnum.PRODUCT_MAIN.getCode());
//                spuImage.setImages(mainImageList);
//                spuImages.add(spuImage);
//                onShelfImagePackage.setSpuImages(spuImages);
//            }


            // 如果之前就有的话要追加上去 而不是覆盖
            //            if (CharSequenceUtil.isNotBlank(tryOnDetail.getTryOnImages())) {
//                List<ImageFile> imageFiles = JSON.parseArray(tryOnDetail.getTryOnImages(), ImageFile.class);
//                tryOnImages.addAll(imageFiles);
//            }

            // 暂时不做追加 直接覆盖

            OnShelfImagePackage onShelfImagePackage = new OnShelfImagePackage();
            onShelfImagePackage.setStyleCode(visualTask.getStyleCode());
            List<OnShelfImagePackage.SpuImage> spuImages = new ArrayList<>();
            OnShelfImagePackage.SpuImage spuOtherImage = new OnShelfImagePackage.SpuImage();
            OnShelfImagePackage.SpuImage spuMainImage = new OnShelfImagePackage.SpuImage();
            spuMainImage.setImageType(VisualTaskImageTypeEnum.PRODUCT_MAIN.getCode());
            spuMainImage.setImageTypeDesc(VisualTaskImageTypeEnum.PRODUCT_MAIN.getDesc());
            spuMainImage.setImages(mainImageList);

            spuOtherImage.setImageType(VisualTaskImageTypeEnum.OTHER.getCode());
            spuOtherImage.setImageTypeDesc(VisualTaskImageTypeEnum.OTHER.getDesc());
            spuOtherImage.setImages(otherImageList);

            spuImages.add(spuMainImage);
            spuImages.add(spuOtherImage);
            onShelfImagePackage.setSpuImages(spuImages);

            tryOnHandleReq.setTryOnImages(passQcTryOnList);
            tryOnHandleReq.setOnShelfImages(onShelfImagePackage);
            //记录环节状态
            nodeStateService.saveVisualTaskNodeStepState(visualTask.getTaskId(), VisualTaskStepEnum.QC, VisualTaskNodeEnum.TRY_ON_QC,VisualTaskHandleStateEnum.FINISH.getCode());
            // 如果需要修图 则任务重新分配
            if (Objects.equals(visualTask.getProcessType(), VisualTaskProcessTypeEnum.TRY_ON.getCode()) && req.getNeedFixOn() && req.getOnShelfHandlerId() != null) {
                AllocateTaskReq allocateTaskReq = new AllocateTaskReq();
                allocateTaskReq.setTaskIds(List.of(req.getTaskId()));
                allocateTaskReq.setProcessType(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.getCode());
                allocateTaskReq.setOnShelfHandlerId(req.getOnShelfHandlerId());
                allocateTaskReq.setOnShelfHandlerName(req.getOnShelfHandlerName());
                allocateTaskReq.setTryOnHandlerId(tryOnDetail.getTryOnHandlerId());
                allocateTaskReq.setTryOnHandlerName(tryOnDetail.getTryOnHandlerName());
                log.info("try on 任务重新分配，taskDetail:{}", JSON.toJSONString(visualTaskDetail));
                visualTaskService.allocate(allocateTaskReq);
                // taskDetail被更新了 需要重新获取
                visualTaskDetail = visualTaskDetailRepository.getByTaskId(req.getTaskId());

            }
            if(visualTaskDetail.getLatestOnShelfDetailId()!=null){
                VisualTaskOnShelf visualTaskOnShelf = visualTaskOnShelfRepository.getById(visualTaskDetail.getLatestOnShelfDetailId());
                tryOnHandleReq.setOnShelfHandleVersion(visualTaskOnShelf.getVersionNum());
            }
            // 转回原来的try on 处理流程
            log.info("try on 任务质检完成，流转到 try on 处理 tryOnHandleReq:{}", JSON.toJSONString(tryOnHandleReq));
            visualTaskHandleService.tryOnHandle(tryOnHandleReq);
        }
        latestByTaskId.setQcResult(qcResult.getCode());
        latestByTaskId.setQcState(VisualTaskQcStateEnum.FINISH.getCode());
        qcRepository.updateById(latestByTaskId);

    }

    @NotNull
    private static VirtualDressingTaskAddV2Req buildVirtualDressingTaskAddV2Req(BatchTryOnReq req) {
        VirtualDressingTaskAddV2Req taskAddV2Req = new VirtualDressingTaskAddV2Req();
        taskAddV2Req.setOutfitType(req.getOutfitType());
        taskAddV2Req.setAiModelTypes(req.getAiModelTypes().stream().map(modelTypeDTo -> new AiModelTypeDTO(modelTypeDTo.getAiModelType(), modelTypeDTo.getGenerateCount())).toList());
        taskAddV2Req.setClothingImg(req.getClothingImg());
        taskAddV2Req.setModelReferenceImages(req.getModelReferenceImages().stream().map(modelRefImgDTo -> BeanUtil.copyProperties(modelRefImgDTo, ModelReferenceImageDTO.class)).toList());
        taskAddV2Req.setSource(req.getSource());
        SdpDesignStyleV2DTO designStyleV2DTO = new SdpDesignStyleV2DTO(req.getStyleInfo().getStyleCode(), req.getStyleInfo().getCategory());
        BeanUtil.copyProperties(req.getStyleInfo(), designStyleV2DTO);
        taskAddV2Req.setStyleInfo(designStyleV2DTO);
        taskAddV2Req.setMoodboardId(req.getMoodboardId());
        taskAddV2Req.setTryonRuleCode(req.getTryonRuleCode());
        taskAddV2Req.setModelMjInfos(req.getModelMjInfos());
        taskAddV2Req.setModelFace(BeanUtil.copyProperties(req.getModelFace(), ModelFaceDTO.class));
        taskAddV2Req.setBackground(BeanUtil.copyProperties(req.getBackground(), BackgroundDTO.class));
        taskAddV2Req.setPostureFissionList(req.getPostureFissionList().stream().map(postureFissionDTo -> BeanUtil.copyProperties(postureFissionDTo, PostureFissionVo.class)).toList());
        return taskAddV2Req;
    }

}
