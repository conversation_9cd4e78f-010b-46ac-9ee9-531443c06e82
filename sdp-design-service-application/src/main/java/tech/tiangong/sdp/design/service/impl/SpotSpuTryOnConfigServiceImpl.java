package tech.tiangong.sdp.design.service.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.SpotSpuTryOnConfigConverter;
import tech.tiangong.sdp.design.entity.SpotSpu;
import tech.tiangong.sdp.design.entity.SpotSpuTryOnConfig;
import tech.tiangong.sdp.design.enums.DesignLogBizTypeEnum;
import tech.tiangong.sdp.design.enums.DesignLogContentEnum;
import tech.tiangong.sdp.design.enums.spot.SpotTryOnStateEnum;
import tech.tiangong.sdp.design.repository.SpotSpuRepository;
import tech.tiangong.sdp.design.repository.SpotSpuTryOnConfigRepository;
import tech.tiangong.sdp.design.service.DesignLogService;
import tech.tiangong.sdp.design.service.SpotSpuTryOnConfigService;
import tech.tiangong.sdp.design.vo.req.log.DesignLogSdpSaveReq;
import tech.tiangong.sdp.design.vo.req.spot.SpotSpuUpdateReq;
import tech.tiangong.sdp.design.vo.req.tryOnConfig.TryOnConfigAddReq;
import tech.tiangong.sdp.design.vo.req.tryOnConfig.TryOnConfigEditReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuTryOnConfigVo;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * tryOn任务分配配置(SpotSpuTryOnConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-25 11:52:48
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpotSpuTryOnConfigServiceImpl implements SpotSpuTryOnConfigService {
    private final SpotSpuTryOnConfigRepository tryOnConfigRepository;
    private final SpotSpuRepository spotSpuRepository;
    private final DesignLogService designLogService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(TryOnConfigAddReq req) {
        List<SpotSpu> list = spotSpuRepository.getAllocationListByStyleCodes(req.getStyleCodes());
        //校验
        checkIsCanCreate(req, list);

        //已存在的数据，如果前端传值了就进行更新，否则不更新，产品说的
        List<SpotSpuTryOnConfig> exitList = tryOnConfigRepository.listByStyleCodes(req.getStyleCodes());
        List<SpotSpuTryOnConfig> updateList = exitList.stream().map(t -> {
            return SpotSpuTryOnConfigConverter.buildUpdateList(req, t);
        }).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(updateList)) {
            tryOnConfigRepository.updateBatchById(updateList);
        }
        List<String> allStyleCodes = req.getStyleCodes();
        List<String> exitStyleCodes = exitList.stream().map(SpotSpuTryOnConfig::getStyleCode).collect(Collectors.toList());
        List<String> notExitStyleCodes = allStyleCodes.stream().filter(styleCode -> !exitStyleCodes.contains(styleCode)).collect(Collectors.toList());

        List<SpotSpuTryOnConfig> batchAddList = notExitStyleCodes.stream().map(styleCode -> {
            return SpotSpuTryOnConfigConverter.buildAddList(req, styleCode);
        }).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(batchAddList)) {
            tryOnConfigRepository.saveBatch(batchAddList);
        }
        //更新spu主表表信息
        updateSpotSpu(list, req);
        //新增日志
        list.forEach(t -> {
            this.addLog(t.getStyleCode(), t.getSpotSpuId(), DesignLogContentEnum.SPOT_ALLOCATE_TRY_ON.getDesc());
        });
    }

    private void addLog(String styleCode, Long bizId, String logContent) {
        DesignLogSdpSaveReq logSdpSaveReq = DesignLogSdpSaveReq.builder()
                .bizId(bizId)
                .bizType(DesignLogBizTypeEnum.SPOT)
                .styleCode(styleCode)
                .content(logContent)
                .build();
        designLogService.sdpSave(logSdpSaveReq);
    }

    private void updateSpotSpu(List<SpotSpu> list, TryOnConfigAddReq req) {

        List<SpotSpuTryOnConfig> allList = tryOnConfigRepository.listByStyleCodes(req.getStyleCodes());
        Map<String, SpotSpuTryOnConfig> spotSpuTryOnConfigMap = StreamUtil.list2Map(allList, SpotSpuTryOnConfig::getStyleCode);

        LocalDateTime allocatedTime = LocalDateTime.now();
        list.forEach(t -> {
            //记录分配时间
            t.setTryOnAllocatedTime(allocatedTime);
            // 更新tryon状态为待创建
            t.setTryOnStatus(SpotTryOnStateEnum.WAIT_CREATE.getCode());
            t.setTryOnUserId(req.getTryOnUserId());
            t.setTryOnUserName(req.getTryOnUserName());
            t.setTryOnBizId(spotSpuTryOnConfigMap.get(t.getStyleCode()).getTryOnConfigId());
            t.setSceneCode((StringUtils.isBlank(req.getSceneCode()) ? t.getSceneCode() : req.getSceneCode()));
            t.setSceneName(StringUtils.isBlank(req.getSceneName()) ? t.getSceneName() : req.getSceneName());
            t.setProductThemeName(StringUtils.isBlank(req.getProductThemeName()) ? t.getProductThemeName() : req.getProductThemeName());
            t.setProductThemeCode(StringUtils.isBlank(req.getProductThemeCode()) ? t.getProductThemeCode() : req.getProductThemeCode());
            t.setClothingStyleCode(StringUtils.isBlank(req.getClothingStyleCode()) ? t.getClothingStyleCode() : req.getClothingStyleCode());
            t.setClothingStyleName(StringUtils.isBlank(req.getClothingStyleName()) ? t.getClothingStyleName() : req.getClothingStyleName());
        });
        spotSpuRepository.updateBatchById(list);
    }

    @Override
    public SpotSpuTryOnConfigVo getDetail(Long tryOnConfigId) {
        if (null == tryOnConfigId) {
            throw new SdpDesignException("Id必传");
        }
        SpotSpuTryOnConfig spotSpuTryOnConfig = tryOnConfigRepository.getById(tryOnConfigId);
        if (null == spotSpuTryOnConfig) {
            throw new SdpDesignException("任务信息不存在，请检查！");
        }
        SpotSpuTryOnConfigVo vo = new SpotSpuTryOnConfigVo();
        BeanUtils.copyProperties(spotSpuTryOnConfig, vo);

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(TryOnConfigEditReq req) {
        Long tryOnConfigId = req.getTryOnConfigId();
        SpotSpuTryOnConfig spotSpuTryOnConfig = tryOnConfigRepository.getById(tryOnConfigId);
        if (null == spotSpuTryOnConfig) {
            throw new SdpDesignException("任务信息不存在，请检查！");
        }
        BeanUtils.copyProperties(req, spotSpuTryOnConfig);
        tryOnConfigRepository.updateById(spotSpuTryOnConfig);
    }

    @Override
    public void updateBySpuMessage(SpotSpuUpdateReq req, SpotSpu spotSpu) {
        if (Objects.nonNull(spotSpu.getTryOnBizId())) {
            SpotSpuTryOnConfig SpotSpuTryOnConfig = SpotSpuTryOnConfigConverter.buildUpdateBySpu(req, spotSpu);
            tryOnConfigRepository.updateById(SpotSpuTryOnConfig);
        }
    }


    private void checkIsCanCreate(TryOnConfigAddReq req, List<SpotSpu> list) {

        if (CollectionUtils.isEmpty(list)) {
            throw new SdpDesignException("Spu信息不存在，请检查！");
        }
        /*
        // 去掉供给方式的校验；所有款号都可以进行任务分配 v1.0.5
        List<SpotSpu> supplyModeList = list.stream().filter(t ->
                Objects.equals(t.getSupplyModeCode(), SupplyModeEnum.MANUFACTURER.getCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(supplyModeList)) {
            throw new SdpDesignException("请勿勾选ODM的SPU");
        }
         */
        List<SpotSpu> finishList = list.stream().filter(t ->
                Objects.equals(t.getTryOnStatus(), SpotTryOnStateEnum.PASS.getCode())
                        || Objects.equals(t.getTryOnStatus(), SpotTryOnStateEnum.NO_PASS.getCode()) ||
                        Objects.equals(t.getTryOnStatus(), SpotTryOnStateEnum.WAIT_AUDIT.getCode())
                        || Objects.equals(t.getTryOnStatus(), SpotTryOnStateEnum.CREATED.getCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(finishList)) {
            throw new SdpDesignException("存在已创建任务的SPU，无需分配任务，请勿重复勾选");
        }
        if (!Objects.equals(list.size(), req.getStyleCodes().size())) {
            throw new SdpDesignException("spu信息不存在，请检查！");
        }

    }
}
