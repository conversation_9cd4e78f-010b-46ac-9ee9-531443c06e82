package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.entity.ImageCroppingTask;
import tech.tiangong.sdp.design.entity.OnShelfImagePackage;
import tech.tiangong.sdp.design.vo.query.visual.VisualImagePackageQuery;
import tech.tiangong.sdp.design.vo.req.visual.*;
import tech.tiangong.sdp.design.vo.resp.visual.ImageBatchUploadErrorResp;
import tech.tiangong.sdp.design.vo.resp.visual.MigrateImagePackageResp;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageListVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageVo;

import java.util.List;

/**
 * (VisualImagePackage)服务接口
 */
public interface VisualImagePackageService {
    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<VisualImagePackageListVo> page(VisualImagePackageQuery query);

    /**
     * 保存图包
     * @param onShelfImagePackage
     * @return
     */
    Boolean saveImagePackage(OnShelfImagePackage onShelfImagePackage);
    /**
     * 保存上架图包
     * @param req
     */
    Boolean saveOnShelfImagePackage(SaveOnShelfImagePackageReq req);

    /**
     * 数码印花-新增图包(存在则不创建)
     * @param imagePackageReq 入参
     */
    void save4DigitalPrinting(SaveOnShelfImagePackageReq imagePackageReq);

    /**
     * 通过图包ID获取图包
     * @param packageId
     * @return
     */
    VisualImagePackageVo getVisualImagePackageVoByPackageId(Long packageId);

    /**
     * 通过SPU获取图包
     * @param styleCode
     * @return
     */
    VisualImagePackageVo getVisualImagePackageVoByStyleCode(String styleCode);

    /**
     * 清空图包
     * @param req
     * @return
     */
    Boolean cleanImagePackage(CleanImagePackageReq req);

    /**
     * 提交下载SPU上架图包任务
     * @param req
     * @return
     */
    Long submitSpuImagePackageDownloadTask(DownloadSpuImagePackageReq req);

    /**
     * 批量更新图片
     */
    List<ImageBatchUploadErrorResp> batchUpdateImages(ImageBatchUpdateReq req);

    /**
     * 文件夹批量更新图包
     */
    List<ImageBatchUploadErrorResp> batchUpdateImageByFolder(ImageBatchUpdateFolderReq req);




    /**
     * 保存上架图包
     * @param req
     */
    Boolean saveOnShelfImagePackageV2(CreateVisualImagePackageRequest req);


    /**
     * 裁剪任务完成更新图包
     * @param croppingTasks
     */
     void updateVisualImagePackageWithCroppedImages(List<ImageCroppingTask> croppingTasks);



    /**
     * 迁移图包数据根据主图比例
     * @param req 迁移请求
     * @return 迁移结果信息
     */
    MigrateImagePackageResp migrateImagePackageByRatio(MigrateImagePackageReq req);


    /**
     * 刷数据
     * @param req
     */
    void dataBrushBatchCrop(MigrateImagePackageReq req);

}
