package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.PushZjLogService;
import tech.tiangong.sdp.design.service.TryOnService;
import tech.tiangong.sdp.design.vo.query.PushZjLogQuery;
import tech.tiangong.sdp.design.vo.req.tryon.CheckForCreateReq;
import tech.tiangong.sdp.design.vo.req.tryon.GetParamsForCreateReq;
import tech.tiangong.sdp.design.vo.resp.PushZjLogVo;
import tech.tiangong.sdp.design.vo.resp.tryon.CreateTryOnParamVo;

import java.util.List;


/**
 * tryOn相关管理-web
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/try-on")
public class TryOnController extends BaseController {
    private final TryOnService tryOnService;
    /**
     * 发起tryOn任务前校验并返回参数
     * @param req
     * @return
     */
    @PostMapping("/check-for-create")
    public DataResponse<Boolean> checkForCreate(@RequestBody CheckForCreateReq req) {
        return DataResponse.ok(tryOnService.checkForCreate(req));
    }

    /**
     * 获取发起tryOn任务参数
     * @param req
     * @return
     */
    @PostMapping("/get-params-for-create")
    public DataResponse<List<CreateTryOnParamVo>> getParamsForCreate(@RequestBody GetParamsForCreateReq req) {
        return DataResponse.ok(tryOnService.getParamsForCreate(req));
    }
}
