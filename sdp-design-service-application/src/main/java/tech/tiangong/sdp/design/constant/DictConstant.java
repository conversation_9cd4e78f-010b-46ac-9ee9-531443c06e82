package tech.tiangong.sdp.design.constant;

/**
 * 字典编号常量
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/20 20:32
 */
public interface DictConstant {

	/**
	 * 使用部位编号
	 */
	String PART_USE_CODE= "plm_purchase_ylbw";

	/**
	 * Bom裁剪方法编号
	 */
	String BOM_CUTTING_METHOD_CODE= "plm_cutting_method";

	/**
	 * 旧JV-Bom裁前方法编号
	 */
	String OLD_JV_BOM_CUTTING_METHOD_CODE= "bom_cutting_method";

	/**
	 * PLM-特殊辅料序号
	 */
	String PLM_SPECIAL_ACCESSORIES_NUMBER_CODE= "plm_special_accessories_number";

	/**
	 * 标签信息
	 */
	String PLM_TAG_CODE = "plm_tag_material";

	/**
	 * 廓形编号
	 */
	String PLM_CONTOUR_STYLE_CODE = "CONTOUR_STYLE";

	/**
	 * 合作方式 -开票状态
	 */
	String INVOICE_DICT = "invoice_dict";

	/**
	 * 执行标准
	 */
	String PLM_CLOTHING_EXECUTIVE_STANDARDS = "plm_clothing_executive_standards";

	/**
	 * 安全类别
	 */
	String PLM_CLOTHING_SECURITY_TYPE = "plm_clothing_security_type";

	/**
	 * 参考季节
	 */
	String PLM_REFERENCE_SEASON = "plm_reference_season";

	/**
	 * 合作模式
	 */
	String COOPERATION_MODE = "cooperation_mode";

	/**
	 * 图案元素
	 */
	String PATTERN_ELEMENTS = "pattern_elements";

	/**
	 * 尺码组
	 */
	String PLM_STANDARD_SIZE = "plm_standard_size";

	/**
	 * 尺码组-字母码编码
	 */
	String STANDARD_TIANGONG_CODE = "tiangong_code_standard";

	/**
	 * 尺码组-字母码名称
	 */
	String STANDARD_TIANGONG_NAME = "字母码";

	/**
	 * 尺码组-DUNNALUNA
	 */
	String PLM_STANDARD_SIZE_DUNNALUNA = "DUNNALUNA";

	/**
	 * 款式件数
	 */
	String SETS_NUMBER = "sets_number";
	/**
	 * 品质等级
	 */
	String PLM_QUALITY_LEVEL = "plm_quality_level";

	/**
	 * 品质等级-准二线
	 */
	String QUALITY_LEVEL_ZEX_CODE = "plm_quality_level_zex";

	/**
	 * 品质等级-准二线名称
	 */
	String QUALITY_LEVEL_ZEX_NAME = "准二线";

	/**
	 * 织造方式
	 */
	String WEAVE_MODE = "aps_category_type";

	/**
	 * 织造方式-针/梭织编码
	 */
	String WEAVE_MODE_01_CODE = "01";

	/**
	 * 织造方式-针/梭织名称
	 */
	String WEAVE_MODE_01_NAME = "针/梭织";


	/**
	 * 供给方式
	 */
	String SUPPLY_MODE= "supply_mode";

	/**
	 * 供给方式-数码印花
	 */
	String SUPPLY_MODE_DIGITAL_PRINTING= "digital_printing";

	/**
	 * 运营版型表图片
	 */
	String OPERATE_MODEL_PICTURE = "operate_model_picture";

	/**
	 * 散剪倍率
	 */
	String SMALL_ORDER_RATIO = "small_order_ratio";

	/**
	 * 现货类型
	 */
	String STOCK_GOODS_TYPE = "stockgoods_type";

	/**
	 * 品类
	 */
	String CLOTHING_CATEGORY = "clothing_category";

	/**
	 * 颜色
	 */
	String CLOTHING_COLOR = "clothing_color";

	/**
	 * 货盘类型
	 */
	String TRAY_TYPE = "tray_type";

	/**
	 * 商品类型
	 */
	String PRODUCT_TYPE = "product_type";

	/**
	 * 商品主题
	 */
	String PRODUCT_THEME = "Product Theme";

	/**
	 * 场景
	 */
	String JV_SCENE = "JV_scene";

	/**
	 * 风格
	 */
	String JV_STYLE = "jv-style";

	/**
	 * 波段
	 */
	String PLM_CLOTHING_BAND = "plm_clothing_band";

	/**
	 * 灵感源品牌
	 */
	String INSPIRATION_BRAND = "inspiration_brand";

	/**
	 * 灵感图来源
	 */
	String INSPIRATION_IMAGE_SOURCE = "Inspiration_Image_Source";

	/**
	 * 企划来源
	 */
	String PLANNING_SOURCE = "planning_source";

	/**
	 * 国家站点
	 */
	String NATIONAL = "national";
	/**
	 * 市场风格库
	 */
	String MARKET_STYLE = "Market_Style";

	/**
	 * 弹性需求
	 */
	String PLM_ELASTIC_REQUIREMENT = "plm_elastic_requirement";

	/**
	 * 款式元素
	 */
	String STYLE_ELEMENTS = "style_elements";
}
