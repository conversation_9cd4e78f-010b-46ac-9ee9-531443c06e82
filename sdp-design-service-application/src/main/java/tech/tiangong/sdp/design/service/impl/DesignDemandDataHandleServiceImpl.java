package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import tech.tiangong.sdp.design.entity.DesignDemand;
import tech.tiangong.sdp.design.repository.DesignDemandRepository;
import tech.tiangong.sdp.design.service.DesignDemandDataHandleService;

import java.util.ArrayList;
import java.util.List;

/**
 * SPU表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DesignDemandDataHandleServiceImpl implements DesignDemandDataHandleService {

    private final DesignDemandRepository designDemandRepository;

    @Override
    public void updateSubmitUser(String designDemandId) {
        //查询缺少提交人的数据
        List<DesignDemand> noSubmitUserDemandList = designDemandRepository.noSubmitUserList(designDemandId);
        if (CollUtil.isEmpty(noSubmitUserDemandList)) {
            log.info("无需要更新的数据");
            return;
        }

        //分批次处理
        StopWatch sw = new StopWatch();
        sw.start("平台名称更新");
        int handleSize = 300;
        CollectionUtil.split(noSubmitUserDemandList, handleSize).forEach(demandList -> {
                    //刷数提交人信息
            List<DesignDemand> updateList = new ArrayList<>(demandList.size());
            for (DesignDemand item : demandList) {
                DesignDemand updateDemand = new DesignDemand();
                updateDemand.setDesignDemandId(item.getDesignDemandId());
                // updateDemand.setSubmitUserId(shopResp.getPlatformName());
                // updateDemand.setSubmitUserName(shopResp.getPlatformName());
                updateList.add(updateDemand);
            }

            if (CollUtil.isNotEmpty(updateList)) {
                designDemandRepository.updateBatchById(updateList);
            }
            updateList = null;
        });
    }


}
