package tech.tiangong.sdp.design.business.bom.notification;

import java.util.List;

/**
 * 通知
 * @param <T>
 */
public interface Notification<T> {

    /**
     * 添加需通知的内容
     * @param t
     */
    void add(T t);

    /**
     * 批量添加需通知的内容
     * @param list
     */
    void addBatch(List<T> list);

    /**
     * 获取所有需通知的内容
     * @return
     */
    List<T> getAll();

    /**
     * 发送通知
     */
    void send();
}
