package tech.tiangong.sdp.design.remote;

import cn.yibuyun.plm.prod.enums.StyleInfoStateEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import team.aikero.blade.core.protocol.DataResponse;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.fob.client.SdpStyleInfoClient;
import tech.tiangong.sdp.prod.vo.req.StyleInfoBaseReq;
import tech.tiangong.sdp.prod.vo.resp.StyleInfoBaseVo;
import tech.tiangong.sdp.prod.vo.resp.StyleSizeInfoVo;

import java.util.Collections;
import java.util.List;

/**
 * 大货资料相关接口
 */
@Service
@Slf4j
@AllArgsConstructor
public class SdpOrderHelper {
    private final SdpStyleInfoClient sdpStyleInfoClient;

    public List<StyleSizeInfoVo> listSizeInfoByStyleCode(List<String> styleCodes){
        log.info("=== 通过styleCode查询尺寸表信息 req：{}; ===", JSONObject.toJSONString(styleCodes));
        try {
            DataResponse<List<StyleSizeInfoVo>> response = sdpStyleInfoClient.listSizeInfo(styleCodes);
            log.info("=== 通过styleCode查询尺寸表信息 response:{}", JSON.toJSONString(response));
            return response.getData();
        } catch (Exception e) {
            //throw new SdpDesignException("通过styleCode查询尺寸表信息,调用服务失败:"+e.getMessage(), e);
            log.error("通过styleCode查询尺寸表信息失败", e);
        }
        return Collections.emptyList();
    }

    public StyleInfoBaseVo latestSubmitOrder(String styleCode){
        StyleInfoBaseReq req = new StyleInfoBaseReq();
        req.setStyleCode(styleCode);
        req.setState(StyleInfoStateEnum.SUBMITTED.getCode());
        log.info("=== 最新大货资料查询 req：{}; ===", JSONObject.toJSONString(req));
        try {
            DataResponse<StyleInfoBaseVo> response = sdpStyleInfoClient.latestBaseInfo(req);
            return response.getData();
        } catch (Exception e) {
            throw new SdpDesignException("最新大货资料查询,调用服务失败:"+e.getMessage(), e);
        }
    }
}
