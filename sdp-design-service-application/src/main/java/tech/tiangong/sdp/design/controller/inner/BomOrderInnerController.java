package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.BomOrderService;
import tech.tiangong.sdp.design.vo.req.*;
import tech.tiangong.sdp.design.vo.req.bom.LyMaterialQueryReq;
import tech.tiangong.sdp.design.vo.req.material.OppositeColorMaterialReq;
import tech.tiangong.sdp.design.vo.resp.bom.*;
import tech.tiangong.sdp.design.vo.resp.material.OppositeColorLatestVo;
import tech.tiangong.sdp.design.vo.resp.material.OppositeColorMaterialVo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * BOM内部接口 BomOrderInnerController
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/9 17:35
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/bom")
public class BomOrderInnerController {

	private final BomOrderService bomOrderService;

	/**
	 * 根据bom单物料id批量查询物料信息(核算查询)
	 *
	 * @param req 入参
	 * @return 响应结果
	 */
	@PostMapping("/material/check-count")
	public DataResponse<List<BomMaterialCheckCountVo>> listBomMaterialCheckCount(@RequestBody @Validated BomMaterialReq req) {
		return DataResponse.ok(bomOrderService.listBomMaterialCheckCount(req));
	}

	/**
	 * 根据设计款号查询最新已提交的工艺信息(过滤找料中的bom-v3.11)
	 *
	 * @param req 设计款号列表
	 * @return 响应结果
	 */
	@PostMapping("/craft/latest")
	public DataResponse<List<CraftDemandInfoVo>> getLatestCraftInfoList(@Validated @RequestBody DesignCodeReq req){
		req.setNoSearch(Optional.ofNullable(req.getNoSearch()).orElse(Boolean.TRUE));
		List<CraftDemandInfoVo> list = bomOrderService.getLatestCraftInfoList(req, req.getNoSearch());
		return DataResponse.ok(list);
	}

	/**
	 * 根据设计款号查询最新已提交的Bom信息(过滤找料中的bom-v3.11)
	 *
	 * @param req 设计款号
	 * @return 响应结果
	 */
	@PostMapping("/design-code/latest")
	DataResponse<List<BomOrderVo>> getLatestBomOrder(@Validated @RequestBody DesignCodeReq req) {
		req.setNoSearch(Optional.ofNullable(req.getNoSearch()).orElse(Boolean.TRUE));
		List<BomOrderVo> list = bomOrderService.getLatestBomOrder(req);
		return DataResponse.ok(list);
	}

	/**
	 * 根据BomID查询Bom基础信息
	 *
	 * @param bomId bom主键
	 * @return 响应结果
	 */
	@GetMapping("/get-by-id")
	DataResponse<BomOrderVo> getBomById(@RequestParam(value = "bomId") Long bomId) {
		BomOrderVo bomOrderVo = bomOrderService.getBomById(bomId);
		return DataResponse.ok(bomOrderVo);
	}

	/**
	 * 根据designCode查询Bom基础信息(含取消款)
	 *
	 * @param designCode bom主键
	 * @return 响应结果
	 */
	@GetMapping("/get-by-skc")
	DataResponse<BomOrderVo> getBomBySkc(@RequestParam(value = "designCode") String designCode){
		BomOrderVo bomOrderVo = bomOrderService.getBomBySkc(designCode);
		return DataResponse.ok(bomOrderVo);
	}


	/**
	 * 根据设计款号查询最新已提交的Bom详情(过滤找料中的bom-v3.11)
	 *
	 * @param designCode 设计款号
	 * @return
	 */
	@GetMapping("/detail/latest")
	DataResponse<BomOrderDetailVo> getLatestBomOrderDetail(@RequestParam(value = "designCode") String designCode) throws Exception {
		BomOrderDetailVo bomOrderDetailVo = bomOrderService.getLatestBomOrderDetail4Inner(designCode, true);
		return DataResponse.ok(bomOrderDetailVo);
	}


	/**
	 * 根据设计款号查询最新已提交的Bom详情 -- v3.11
	 *
	 * @param req 入参
	 */
	@PostMapping("/detail/latest-inner")
	DataResponse<BomOrderDetailVo> getLatestBomOrderDetail4Inner(@Validated @RequestBody BomDetailReq req) throws Exception {
		req.setNoSearch(Optional.ofNullable(req.getNoSearch()).orElse(Boolean.TRUE));
		BomOrderDetailVo bomOrderDetailVo = bomOrderService.getLatestBomOrderDetail4Inner(req.getDesignCode(), req.getNoSearch());
		return DataResponse.ok(bomOrderDetailVo);
	}

	/**
	 * 根据设计款号列表查询最新已提交的Bom详情(过滤找料中的bom-v3.11)
	 *
	 * @param req 设计款号
	 * @return
	 */
	@PostMapping("/detail/latest/list")
	public DataResponse<List<BomOrderDetailVo>> getLatestBomOrderDetailList(@Validated @RequestBody DesignCodeReq req) {
		List<BomOrderDetailVo> bomOrderDetailVoList = bomOrderService.getLatestBomOrderDetailList4Inner(req);
		return DataResponse.ok(bomOrderDetailVoList);
	}



	/**
	 * 根据设计款号查询最新已提交的Bom详情,查询的时候触发更新物料最新的价格和有效期（3.20.2）
	 *
	 * @param designCode 设计款号
	 * @return
	 */
	@GetMapping("/detail/latest-update")
	DataResponse<BomOrderDetailVo> getLatestBomOrderDetailUpdate(@RequestParam(value = "designCode") String designCode) throws Exception {
		bomOrderService.updateLatestPriceByDesignCode(designCode);
		BomOrderDetailVo bomOrderDetailVo = bomOrderService.getLatestBomOrderDetail4Inner(designCode, true);
		return DataResponse.ok(bomOrderDetailVo);
	}


	/**
	 * 根据BomID查询工艺信息
	 *
	 * @param bomId
	 * @return 响应结果
	 */
	@GetMapping("/craft/list")
	DataResponse<List<CraftDemandInfoVo>> getCraftDemandInfoList(@RequestParam(value = "bomId") Long bomId) {
		List<CraftDemandInfoVo> list = bomOrderService.getCraftDemandInfoList(bomId);
		return DataResponse.ok(list);
	}

	/**
	 * Bom物料清单核算用量
	 *
	 * @param req
	 * @return 响应结果
	 */
	@PostMapping("/material/dosage-account")
	public DataResponse<Void> bomMaterialDosageAccount(@Validated @RequestBody BomMaterialDosageAccountReq req) {
		bomOrderService.updateBomMaterialDosageAccount(req);
		return DataResponse.ok();
	}

	/**
	 * 根据BomID查詢Bom详情
	 *
	 * @param bomId
	 * @return
	 */
	@GetMapping("/detail/{bomId}")
	DataResponse<BomOrderDetailVo> getBomOrderDetail(@PathVariable(value = "bomId") Long bomId) throws Exception {
		BomOrderDetailVo bomOrderDetailVo = bomOrderService.getBomOrderDetailBySdk(bomId);
		return DataResponse.ok(bomOrderDetailVo);
	}

	/**
	 * 根据款式SPU获取Bom信息
	 * @param styleCode
	 * @return
	 */
	@GetMapping("/spu/info")
	public DataResponse<List<SpuContainBomInfoVo>> getSpuContainBomInfo(@RequestParam String styleCode) {
		List<SpuContainBomInfoVo> list = bomOrderService.getSpuContainBomInfo(styleCode);
		return DataResponse.ok(list);
	}

	/**
	 * 根据设计款号列表打印最新已提交的Bom
	 *
	 * @param req 设计款号
	 * @return
	 */
	@PostMapping("/print/list")
	public DataResponse<List<BomOrderPrintVo>> getBomOrderPrintList(@Validated @RequestBody DesignCodeReq req) {
		List<BomOrderPrintVo> list = bomOrderService.getBomOrderPrintListSdk(req);
		return DataResponse.ok(list);
	}

	/**
	 * 根据履约工艺请求获取Bom工艺信息
	 *
	 * @param req
	 * @return 响应结果
	 */
	@PostMapping("craft/list/supply-chain")
	DataResponse<List<CraftDemandInfoVo>> getCraftDemandInfoBySupplyChain(@Validated @RequestBody SupplyChainCraftDemandReq req) {
		List<CraftDemandInfoVo> list = bomOrderService.getCraftDemandInfoBySupplyChain(req);
		return DataResponse.ok(list);
	}

	/**
	 * 根据设计款号获取工艺信息(排除暂存工艺)
	 * @param designCode
	 * @return
	 */
	@GetMapping("/craft/list/by-design-code")
	public DataResponse<List<CraftDemandInfoVo>> getCraftDemandListByDesignCode(@RequestParam(value = "designCode") String designCode) {
		List<CraftDemandInfoVo> list = bomOrderService.getCraftDemandListByDesignCode(designCode);
		return DataResponse.ok(list);
	}

	/**
	 * 根据BOM物料ID获取BOM单(排除暂存物料)
	 * @param bomMaterialReq
	 * @return
	 */
	@PostMapping("/material/ids")
	public DataResponse<Map<Long, BomOrderVo>> getBomOrderByMaterialIds(@Validated @RequestBody BomMaterialReq bomMaterialReq) {
		Map<Long, BomOrderVo> orderVoMap = bomOrderService.getBomOrderByMaterialIds(bomMaterialReq);
		return DataResponse.ok(orderVoMap);
	}

	/**
	 * 获取工艺创建时间之后的裁后工艺需求数据(已推送至履约)
	 *
	 * @param craftCreateTime 工艺创建时间
	 * @return
	 */
	@GetMapping("/craft/after/time")
	public DataResponse<List<CraftDemandInfoVo>> getCraftDemandListAfterTime(@RequestParam(value = "craftCreateTime") @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") LocalDateTime craftCreateTime) {
		List<CraftDemandInfoVo> list = bomOrderService.getCraftDemandListAfterTime(craftCreateTime);
		return DataResponse.ok(list);
	}

	/**
	 * 根据设计款号列表查询最新拆版已提交的工艺信息
	 * @param req
	 * @return
	 */
	@PostMapping("/latest/demolition/craft")
	public DataResponse<List<CraftDemandInfoVo>> getLatestDemolitionCraftList(@Validated @RequestBody DesignCodeReq req) {
		List<CraftDemandInfoVo> list = bomOrderService.getLatestDemolitionCraftList(req);
		return DataResponse.ok(list);
	}

	/**
	 * 根据物料id或者需求id批量查询最新的物料详情信息
	 *
	 * @param req
	 * @return 响应结果
	 */
	@PostMapping("/latest/material/list")
	public DataResponse<List<OppositeColorLatestVo>> getLatestMaterialList(@Validated @RequestBody MaterialInfoReq req) {
		return DataResponse.ok(bomOrderService.getLatestMaterialList(req));
	}

	/**
	 * 根据剪版/配版单号批量查询最新的物料详情信息（及其被对色的物料信息）
	 *
	 * @param req
	 * @return 响应结果
	 */
	@PostMapping("/opposite-color/material-list")
	public DataResponse<List<OppositeColorMaterialVo>> getOppositeColorMaterialList(@Validated @RequestBody OppositeColorMaterialReq req) {
		return DataResponse.ok(bomOrderService.getOppositeColorMaterialList(req));
	}

	/**
	 * 根据设计款号获取已提交的bom版本列表
	 * @param designCode 设计款号
	 * @return List<BomOrderDetailVo>
	 */
	@GetMapping("/submitted/bom-version/list")
	public DataResponse<List<BomOrderDetailVo>> getSubmittedBomVersionList(@RequestParam(value = "designCode") String designCode) {
		return DataResponse.ok(bomOrderService.getSubmittedBomVersionList(designCode));
	}

	/**
	 * skuId查询好料网面辅料信息(该接口用来查询履约返回的物料信息, 不做业务对接使用)
	 *
	 * @param req 入参
	 * @return GoodMaterialInfoResp
	 */
	@PostMapping("/ly-material")
	public DataResponse<LyMaterialQueryResp> queryLyMaterial(@Validated @RequestBody LyMaterialQueryReq req) {
		LyMaterialQueryResp resp = bomOrderService.queryLyMaterial(req);
		return DataResponse.ok(resp);
	}

}
