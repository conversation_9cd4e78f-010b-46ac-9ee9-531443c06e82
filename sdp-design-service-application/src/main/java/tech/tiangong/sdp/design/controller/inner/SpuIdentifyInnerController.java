package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.SpuIdentifyService;
import tech.tiangong.sdp.design.vo.req.identify.SpuIdentifyRetryReq;

/**
 * 款式识别管理-inner
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/sdp-identify")
public class SpuIdentifyInnerController {

	private final SpuIdentifyService spuIdentifyService;


	/**
	 * 识别重试
	 *
	 * @param req 请求
	 * @return 响应结果
	 */
	@PostMapping("/retry")
	@NoRepeatSubmitLock(lockTime = 10L)
	DataResponse<Void> operateMaterial(@Validated @RequestBody SpuIdentifyRetryReq req) {
		spuIdentifyService.retry(req);
		return DataResponse.ok();
	}


}
