package tech.tiangong.sdp.design.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.entity.PrototypeSampleRecode;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.repository.PrototypeSampleRecodeRepository;
import tech.tiangong.sdp.design.service.PrototypeSampleRecodeService;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeSampleRecodeReq;
import tech.tiangong.sdp.design.vo.req.prototype.SampleRecodeCreateReq;
import tech.tiangong.sdp.design.vo.resp.prototype.PrototypeSampleRecodeVo;

import java.util.List;

/**
 * SKC_打版记录表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrototypeSampleRecodeServiceImpl implements PrototypeSampleRecodeService {
    private final PrototypeSampleRecodeRepository prototypeSampleRecodeRepository;
    private final PrototypeRepository prototypeRepository;


    @Override
    public PrototypeSampleRecodeVo getById(Long id) {
        PrototypeSampleRecode entity = prototypeSampleRecodeRepository.getById(id);
        PrototypeSampleRecodeVo vo = new PrototypeSampleRecodeVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    public Long create(SampleRecodeCreateReq req) {
        Prototype prototype = prototypeRepository.getByDesignCode(req.getDesignCode());
        SdpDesignException.notNull(prototype, "skc信息不存在! designCode:{}", req.getDesignCode());

        List<PrototypeSampleRecode> recodeList = prototypeSampleRecodeRepository.getByDesignCode(req.getDesignCode());

        PrototypeSampleRecode entity = PrototypeSampleRecode.builder()
                .styleCode(prototype.getStyleCode())
                .prototypeId(prototype.getPrototypeId())
                .designCode(prototype.getDesignCode())
                .color(prototype.getColor())
                // .colorCode(prototype.getColorCode())
                .makeClothesNum(recodeList.size() + 1)
                .build();
        BeanUtils.copyProperties(req, entity);
        entity.setPrototypeSampleRecodeId(IdPool.getId());

        prototypeSampleRecodeRepository.save(entity);

        return entity.getPrototypeSampleRecodeId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(PrototypeSampleRecodeReq req) {
        PrototypeSampleRecode entity = new PrototypeSampleRecode();
        BeanUtils.copyProperties(req, entity);
        prototypeSampleRecodeRepository.updateById(entity);
    }

}
