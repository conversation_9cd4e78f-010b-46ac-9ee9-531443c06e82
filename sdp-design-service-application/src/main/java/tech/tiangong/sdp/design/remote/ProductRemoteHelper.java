package tech.tiangong.sdp.design.remote;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.net.DataResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yibuyun.scm.common.dto.accessories.request.CategoryTreeReq;
import com.yibuyun.scm.common.dto.accessories.request.SkuIdsReq;
import com.yibuyun.scm.common.dto.accessories.response.CategoryTreeMapVo;
import com.yibuyun.scm.common.dto.accessories.response.EsAttribute;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import com.yibuyun.scm.common.dto.fabric.response.CommoditySkuCollectionRespVo;
import com.yibuyun.scm.open.client.accessories.SCMAccessoriesCategoryClient;
import com.yibuyun.scm.open.client.accessories.SCMAccessoriesSpuClient;
import com.yibuyun.scm.open.client.fabric.SCMFabricClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.BomOrderConverter;
import tech.tiangong.sdp.design.vo.resp.material.ProductEsAttributeVo;
import tech.tiangong.sdp.material.enums.Bool;
import tech.tiangong.sdp.utils.StreamUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 履约商品服务_远程接口调用
 *
 * @author: cenlijin
 * @date: 2021/10/25 17:23
 */

@Service
@Slf4j
@AllArgsConstructor
public class ProductRemoteHelper {

    /**
     * 商品服务_feign
     */
    private final SCMAccessoriesSpuClient spuIndexFeign;
    private final SCMAccessoriesSpuClient accessoriesSpuClient;
    private final SCMFabricClient commoditySkuFeignClient;
    private final SCMAccessoriesCategoryClient categoryClient;


    /**
     * 查询商品销售属性信息
     * @param spuId
     */
    public List<ProductEsAttributeVo> getSaleAttrs(Long spuId){
        Assert.notNull(spuId, "spuId为空! ");
        log.info("===【查询商品销售属性信息】，入参：{} ===", JSONObject.toJSONString(spuId));
        try {
            DataResponse<List<EsAttribute>> response = spuIndexFeign.saleAttrs(spuId);

            log.info("【查询商品销售属性信息】，response:{}", JSON.toJSONString(response));
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            List<EsAttribute> dataList = response.getData();
            if (CollectionUtils.isEmpty(dataList)) {
                return Collections.emptyList();
            }
            List<ProductEsAttributeVo> resultList = new ArrayList<>(dataList.size());
            dataList.forEach(item -> {
                ProductEsAttributeVo vo = new ProductEsAttributeVo();
                BeanUtils.copyProperties(item,vo);
                resultList.add(vo);
            });
            return resultList;
        } catch (Exception e) {
            throw new SdpDesignException(e.getMessage(), e);
        }
    }

    /**
     * 获取辅料sku信息
     * @param skuIds
     */
    public List<ProductSpuInfoVo> getAccessoriesSkuInfo(Set<Long> skuIds) {

        if (CollectionUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        SkuIdsReq skuIdsReq = new SkuIdsReq();
        skuIdsReq.setSkuIds(skuIds);
        log.info("【获取辅料sku信息】调用商品服务 请求参数 :{}", JSON.toJSONString(skuIdsReq));

        DataResponse<List<ProductSpuInfoVo>> dataResponse = accessoriesSpuClient.detailSku(skuIdsReq);

        // log.debug("【获取辅料sku信息】调用商品服务 响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            log.error("【获取辅料sku信息】调用商品服务 失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException(" 【获取辅料sku信息】调用商品服务 失败 " + dataResponse.getMessage());
        }
        return dataResponse.getData();
    }


    /**
     * 获取面料sku信息
     * @param skuIds
     */
    public List<CommoditySkuCollectionRespVo.Sku> getFabricSkuInfo(Set<Long> skuIds) {

        if (CollectionUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        log.info("【获取面料sku信息】调用商品服务 请求参数 :{}", JSON.toJSONString(skuIds));

        DataResponse<CommoditySkuCollectionRespVo> dataResponse = commoditySkuFeignClient.getSkuBySkuIds(new ArrayList<>(skuIds));

        // log.debug("【获取面料sku信息】调用商品服务 响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            log.error("【获取面料sku信息】调用商品服务 失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException(" 【获取面料sku信息】调用商品服务 失败 " + dataResponse.getMessage());
        }
        return dataResponse.getData().getSkuList();
    }

    /**
     * 获取面料sku信息
     * @param skuIds 需要查询的面料skuId
     * @param querySpuSkuMap 根据spuId+skuId, 需要返回的面料
     */
    public List<CommoditySkuCollectionRespVo.Sku> getFabricSkuInfo(Set<Long> skuIds, Map<String, Long> querySpuSkuMap) {

        if (CollectionUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        log.info("【获取面料sku信息】调用商品服务 请求参数 :{}", JSON.toJSONString(skuIds));

        DataResponse<CommoditySkuCollectionRespVo> dataResponse = commoditySkuFeignClient.getSkuBySkuIds(new ArrayList<>(skuIds));

        log.debug("【获取面料sku信息】调用商品服务 响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            log.error("【获取面料sku信息】调用商品服务 失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException(" 【获取面料sku信息】调用商品服务 失败 " + dataResponse.getMessage());
        }
        List<CommoditySkuCollectionRespVo.Sku> skuList = dataResponse.getData().getSkuList();
        if (CollUtil.isEmpty(querySpuSkuMap)) {
            return skuList;
        }
        return skuList.stream()
                //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
                .filter(item -> Objects.nonNull(querySpuSkuMap.get(BomOrderConverter.getFabricSpuSkuId(item))))
                .collect(Collectors.toList());
    }

    /**
     * 获取面料sku信息, 若对应的spu未启用, 返回的sku也设置为未启用
     * @param skuIds 需要查询的面料skuId
     * @param querySpuSkuMap 根据spuId+skuId, 需要返回的面料
     */
    public List<CommoditySkuCollectionRespVo.Sku> getFabricSkuSetEnableState(Set<Long> skuIds, Map<String, Long> querySpuSkuMap) {

        if (CollectionUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        log.info("【获取面料sku信息】调用商品服务 请求参数 :{}", JSON.toJSONString(skuIds));

        DataResponse<CommoditySkuCollectionRespVo> dataResponse = commoditySkuFeignClient.getSkuBySkuIds(new ArrayList<>(skuIds));

        log.debug("【获取面料sku信息】调用商品服务 响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            log.error("【获取面料sku信息】调用商品服务 失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException(" 【获取面料sku信息】调用商品服务 失败 " + dataResponse.getMessage());
        }
        List<CommoditySkuCollectionRespVo.Sku> skuList = dataResponse.getData().getSkuList();
        if (CollUtil.isEmpty(querySpuSkuMap)) {
            return skuList;
        }

        List<CommoditySkuCollectionRespVo.Spu> spuList = dataResponse.getData().getSpuList();
        Map<Long, CommoditySkuCollectionRespVo.Spu> spuMap = StreamUtil.list2Map(spuList,
                CommoditySkuCollectionRespVo.Spu::getCommodityId);

        List<CommoditySkuCollectionRespVo.Sku> skuVoList = skuList.stream()
                //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
                .filter(item -> Objects.nonNull(querySpuSkuMap.get(BomOrderConverter.getFabricSpuSkuId(item))))
                .collect(Collectors.toList());

        //若spu未启用, 设置面料状态未未启用
        skuVoList.forEach(sku -> {
            if (CollUtil.isNotEmpty(spuMap) && Objects.nonNull(spuMap.get(sku.getCommodityId()))) {
                CommoditySkuCollectionRespVo.Spu spu = spuMap.get(sku.getCommodityId());
                if (Objects.equals(spu.getIsEnable(), String.valueOf(Bool.NO.getCode()))) {
                    sku.setIsEnable(spu.getIsEnable());
                }
            }
        });
        return skuVoList;
    }


    /**
     * 获取类目信息
     * @param categoryIds
     */
    public List<CategoryTreeMapVo> getCategoryInfo(Set<Long> categoryIds) {

        if (CollectionUtil.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }
        CategoryTreeReq treeReq = new CategoryTreeReq();
        treeReq.setCategoryIds(categoryIds);

        log.info("【获取类目信息】调用商品服务 请求参数 :{}", JSON.toJSONString(categoryIds));
        DataResponse<List<CategoryTreeMapVo>> response = categoryClient.treeMap(treeReq);
        log.info("【获取类目信息】调用商品服务 响应结果 response:{}", JSON.toJSONString(response));
        if (!response.isSuccessful()) {
            log.error("【获取类目信息】调用商品服务 失败 message:{}", response.getMessage());
            throw new SdpDesignException(" 【获取类目信息】调用商品服务 失败 " + response.getMessage());
        }
        return response.getData();
    }




}

