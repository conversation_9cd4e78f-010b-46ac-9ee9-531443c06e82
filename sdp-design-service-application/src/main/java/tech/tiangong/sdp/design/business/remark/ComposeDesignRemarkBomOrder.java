package tech.tiangong.sdp.design.business.remark;

import cn.hutool.core.lang.Assert;
import cn.hutool.extra.spring.SpringUtil;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.BomOrderTransient;
import tech.tiangong.sdp.design.entity.DesignRemarks;
import tech.tiangong.sdp.design.entity.PrototypeHistory;
import tech.tiangong.sdp.design.repository.BomOrderRepository;
import tech.tiangong.sdp.design.repository.BomOrderTransientRepository;
import tech.tiangong.sdp.design.repository.PrototypeHistoryRepository;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;

import java.util.Objects;

/**
 * Bom订单备注
 * <AUTHOR>
 * @create 2021/8/19
 */
public class ComposeDesignRemarkBomOrder implements ComposeDesignRemark{
    @Override
    public DesignRemarks compose(DesignRemarksReq req,DesignRemarks designRemarks) {
        BomOrderRepository bomOrderRepository = SpringUtil.getBean(BomOrderRepository.class);
        BomOrder bomOrder = bomOrderRepository.getById(req.getBizId());
        Integer bomOrderVersionNum = null;
        Long prototypeId = null;

        if (Objects.isNull(bomOrder)) {
            BomOrderTransientRepository bomOrderTransientRepository = SpringUtil.getBean(BomOrderTransientRepository.class);
            BomOrderTransient bomOrderTransient = bomOrderTransientRepository.getById(req.getBizId());
            Assert.notNull(bomOrderTransient,"不存在此Bom订单! ");
            bomOrderVersionNum = bomOrderTransient.getVersionNum();
            prototypeId = bomOrderTransient.getPrototypeId();
        }else {
            bomOrderVersionNum = bomOrder.getVersionNum();
            prototypeId = bomOrder.getPrototypeId();
        }

        designRemarks.setBizId(req.getBizId());
        designRemarks.setBizChildId(req.getBizChildId());
        designRemarks.setTransientState(req.getTransientState());
        designRemarks.setBizType(req.getBizType());
        designRemarks.setBizVersionNum(bomOrderVersionNum);
        //将暂存备注移到原始物料清单ID上
        //BomOrderMaterialRepository bomOrderMaterialRepository = SpringUtil.getBean(BomOrderMaterialRepository.class);
        //List<BomOrderMaterial> bomOrderMaterialList = bomOrderMaterialRepository.list(new QueryWrapper<BomOrderMaterial>()
        //        .lambda().eq(BomOrderMaterial::getBomId, req.getBizId()));

        //Map<Long, List<BomOrderMaterial>> bomOrderMaterialMap = bomOrderMaterialList
        //        .stream().collect(Collectors.groupingBy(BomOrderMaterial::getTrackResultId));
        //bomOrderMaterialList.forEach(bomOrderMaterial -> {
        //    //BOM有暂存
        //    if (Objects.equals(bomOrderMaterial.getBomMaterialId(), req.getBizChildId())
        //            && Objects.equals(bomOrderMaterial.getIsTransient(), Bool.YES.getCode())) {
        //        Optional<Long> bomMaterialId = bomOrderMaterialMap.get(bomOrderMaterial.getTrackResultId())
        //                .stream().filter(material -> Objects.equals(material.getIsTransient(), Bool.NO.getCode()))
        //                .map(BomOrderMaterial::getBomMaterialId).findFirst();
        //
        //        designRemarks.setBizChildId(bomMaterialId.orElse(null));
        //    }
        //});

        PrototypeHistoryRepository prototypeRepository = SpringUtil.getBean(PrototypeHistoryRepository.class);
        PrototypeHistory prototype = prototypeRepository.getById(prototypeId);
        Assert.notNull(prototype,"设计款号不存在! ");
        designRemarks.setStyleCode(prototype.getStyleCode());
        designRemarks.setDesignCode(prototype.getDesignCode());

        return designRemarks;
    }
}
