package tech.tiangong.sdp.design.service.download.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.design.entity.DesignAsyncTask;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.service.download.DownloadTaskStrategy;
import tech.tiangong.sdp.design.vo.dto.download.FileUploadDTO;

import java.util.List;

/**
 * 下载策略-测试类
 * <AUTHOR>
 * @date 2025/6/3 10:48
 */

@Slf4j
@Component
public class DownloadStrategyTestImpl implements DownloadTaskStrategy {

    @Override
    public DesignAsyncTaskTypeEnum getTaskType() {
        return DesignAsyncTaskTypeEnum.TEST_DOWNLOAD;
    }

    @Override
    public List<FileUploadDTO> processDownloadTask(DesignAsyncTask task) {
        log.info("==== DownloadStrategyTestImpl processDownloadTask 下载测试:{}", task);

        return List.of();
    }


}
