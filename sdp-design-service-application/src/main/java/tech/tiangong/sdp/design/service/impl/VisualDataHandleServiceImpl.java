package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zjkj.booster.common.constant.DatePatternConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import team.aikero.blade.uacs.sdk.vo.UserVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.enums.PrototypeStatusEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskImageTypeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskProcessTypeEnum;
import tech.tiangong.sdp.design.excel.VisualDemandCreateData;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.remote.UacsRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.VisualDataHandleService;
import tech.tiangong.sdp.design.service.VisualDemandService;
import tech.tiangong.sdp.design.service.VisualImagePackageService;
import tech.tiangong.sdp.design.service.VisualSpuService;
import tech.tiangong.sdp.design.service.download.DownloadTaskService;
import tech.tiangong.sdp.design.service.download.impl.VisualDemandAsyncTaskServiceImpl;
import tech.tiangong.sdp.design.vo.dto.ImageDTO;
import tech.tiangong.sdp.design.vo.req.visual.SaveOnShelfImagePackageReq;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualDemandBySpuReq;
import tech.tiangong.sdp.design.vo.req.visual.VisualImageCreateReq;
import tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo;
import tech.tiangong.sdp.design.vo.resp.spot.StylePushPopVo;
import tech.tiangong.sdp.design.vo.resp.visual.BatchCreateVisualDemandResp;
import tech.tiangong.sdp.design.vo.resp.visual.InitVisualSpuVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImageCreateVo;
import tech.tiangong.sdp.utils.Bool;
import tech.tiangong.sdp.utils.StreamUtil;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025/6/20 14:47
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class VisualDataHandleServiceImpl implements VisualDataHandleService {

    private final VisualTaskHelper visualTaskHelper;
    private final VisualImagePackageRepository visualImagePackageRepository;
    private final DesignStyleRepository designStyleRepository;
    private final PrototypeRepository prototypeRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final VisualSpuRepository visualSpuRepository;

    private final VisualSpuService visualSpuService;
    private final VisualDemandService visualDemandService;
    private final DownloadTaskService downloadTaskService;
    private final UacsRemoteHelper uacsRemoteHelper;
    private final VisualImagePackageService visualImagePackageService;

    private final DateTimeFormatter PURE_DATETIME_PATTERN = DateTimeFormatter.ofPattern(DatePatternConstants.PURE_DATETIME_PATTERN);

    private final VisualDemandAsyncTaskServiceImpl visualDemandAsyncTaskService;


    @Override
    public String getPopImgUrl(String styleCode) {
        return visualImagePackageRepository.getPopImgUrl(styleCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushPop(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return;
        }
        SdpDesignException.isTrue(styleCodeList.size() <= 200, "推送数量不能大于200");

        List<VisualImagePackage> imagePackageList = visualImagePackageRepository.listLatestByStyleCodes(styleCodeList);
        SdpDesignException.notEmpty(imagePackageList, "图包不存在!");

        //图包创建推送pop
        imagePackageList.forEach(imagePackage -> {
            visualTaskHelper.noticePopUpdateImage(imagePackage.getPackageId(), imagePackage.getStyleCode());
        });
    }

    @Override
    public List<BatchCreateVisualDemandResp> batchCreateVisualDemand(InputStream inputStream) {
        //读取excel
        List<VisualDemandCreateData> excelDataList =  EasyExcel.read(inputStream)
                .head(VisualDemandCreateData.class)
                .sheet()
                .doReadSync();
        SdpDesignException.notEmpty(excelDataList, "数据不能为空!");

        //根据表格SPU关联对应的图片（未取消状态第一个SKC的所有图片）
        List<String> spuList = StreamUtil.convertListAndDistinct(excelDataList, VisualDemandCreateData::getStyleCode);
        SdpDesignException.notEmpty(excelDataList, "SPU不能为空!");

        //设置需求创建人
        //this.setCreateUser(creatorCode);

        //查询设计款spu,skc信息
        List<DesignStyle> styleList = designStyleRepository.listByStyleCodes(spuList);
        Map<String, DesignStyle> styleMap = StreamUtil.list2Map(styleList, DesignStyle::getStyleCode);
        SdpDesignException.notEmpty(excelDataList, "SPU不存在!");
        List<Prototype> notCancelSkcList = prototypeRepository.getNotCancelByStyleCodes(spuList);
        Map<String, List<Prototype>> skcGroupMap = StreamUtil.groupingBy(notCancelSkcList, Prototype::getStyleCode);
        Map<Long, PrototypeDetail> prototypeDetailMap = this.getPrototypeDetailMap(notCancelSkcList);

        //校验数据: 若SPU不存于系统/SPU没有上传图片，则创建任务失败，存在的SPU则正常创建任务，Excel返回失败/成功处理结果；
        // 若全量成功创建，则不需求返回数据结果
        List<BatchCreateVisualDemandResp> demandRespList = Collections.synchronizedList(new ArrayList<>());
        List<String> failSpuList = new ArrayList<>();
        List<SaveVisualDemandBySpuReq> reqs = new ArrayList<>();
        excelDataList.forEach(item -> {
            String styleCode = item.getStyleCode();
            //校验数据
            BatchCreateVisualDemandResp failResp = this.checkFailData(styleMap, styleCode, item, skcGroupMap, prototypeDetailMap);
            if (Objects.nonNull(failResp)) {
                demandRespList.add(failResp);
                failSpuList.add(styleCode);
                return;
            }
            //创建视觉需求实体
            SaveVisualDemandBySpuReq demandReq = new SaveVisualDemandBySpuReq();
            demandReq.setStyleCode(styleCode);
            demandReq.setDemandType(item.getDemandType());
            demandReq.setRealObjectColorState(item.getRealObjectColorState());
            demandReq.setDemandImages(item.getDemandImages());
            reqs.add(demandReq);
        });

        //批量保存
        visualDemandAsyncTaskService.saveAsyncTaskList(reqs);
        //触发异步处理
        CompletableFuture.runAsync(visualDemandAsyncTaskService::handle);

        //创建异常信息下载任务
        if (CollUtil.isNotEmpty(failSpuList)) {
            DesignAsyncTaskTypeEnum asyncTaskType = DesignAsyncTaskTypeEnum.EXCEL_BATCH_CREATE_VISUAL_DEMAND_FAIL;
            String taskName = asyncTaskType.getDesc()+"_"+PURE_DATETIME_PATTERN.format(LocalDateTime.now());
            String parameters = JSONObject.toJSONString(demandRespList);
            downloadTaskService.createTask(taskName,asyncTaskType,parameters);

            return demandRespList;
        }
        return null;
    }

    @Override
    public List<InitVisualSpuVo> initVisualSpu(List<String> styleCodeList) {
        SdpDesignException.notEmpty(styleCodeList, "spu为空!");
        List<VisualSpu> visualSpuList = visualSpuRepository.listByStyleCodes(styleCodeList);
        Map<String, VisualSpu> visualSpuMap = StreamUtil.list2Map(visualSpuList, VisualSpu::getStyleCode);

        //根据spu新增还没有的视觉spu
        List<InitVisualSpuVo> resultList = new ArrayList<>();
        styleCodeList.forEach(styleCode -> {
            InitVisualSpuVo spuVo = InitVisualSpuVo.builder().styleCode(styleCode).state(1).build();
            if (Objects.isNull(visualSpuMap.get(styleCode))) {
                try {
                    visualSpuService.initVisualSpu(styleCode);
                } catch (Exception e) {
                    log.info("新增视觉spu失败, spu:{}", styleCode, e);
                    spuVo.setState(0);
                    spuVo.setFailDesc(e.getMessage());
                }
            }else {
                spuVo.setState(0);
                spuVo.setFailDesc("视觉SPU已存在");
            }
            resultList.add(spuVo);
        });

        return resultList;
    }

    @Override
    public List<VisualImageCreateVo> createImageByDesignStyle(VisualImageCreateReq req) {
        //根据设计款spu创建视觉图包
        List<String> styleCodeList = req.getStyleCodeList();
        List<DesignStyle> styleList = designStyleRepository.listByStyleCodes(styleCodeList);
        Map<String, DesignStyle> styleMap = StreamUtil.list2Map(styleList, DesignStyle::getStyleCode);

        //根据spu查询设计款已提交, 未取消的skc信息, 收集不存在数据的spu作为异常数据
        List<Prototype> prototypeList = prototypeRepository.listByStyleCodes(styleCodeList, PrototypeStatusEnum.DECOMPOSED.getCode(), Boolean.FALSE);
        Map<String, List<Prototype>> prototypeSpuMap = StreamUtil.groupingBy(prototypeList, Prototype::getStyleCode);

        //skc详情
        List<Long> prototypeIds = StreamUtil.convertList(prototypeList, Prototype::getPrototypeId);
        List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIds);
        Map<Long, PrototypeDetail> detailMap = StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);

        //校验数据是否存在异常
        List<StylePushPopVo> errorList = this.getStylePushPopErrorList(styleCodeList, styleMap, prototypeSpuMap, detailMap);
        SdpDesignException.isEmpty(errorList, "存在异常数据: {}", JSON.toJSONString(errorList));

        //已存在图包的spu 已存在则不创建
        List<VisualImagePackage> oldPackageList = visualImagePackageRepository.listLatestByStyleCodes(styleCodeList);
        Map<String, VisualImagePackage> oldPackageMap = StreamUtil.list2Map(oldPackageList, VisualImagePackage::getStyleCode);

        //视觉spu
        List<VisualSpu> visualSpuList = visualSpuRepository.listByStyleCodes(styleCodeList);
        Map<String, VisualSpu> oldVisualSpuMap = StreamUtil.list2Map(visualSpuList, VisualSpu::getStyleCode);


        StopWatch sw = new StopWatch();
        sw.start("未核价设计款新增图包");

        List<VisualImageCreateVo> resultList = new ArrayList<>();
        styleList.forEach(style ->{
            String styleCode = style.getStyleCode();
            //图包存在则不创建
            if (Objects.nonNull(oldPackageMap.get(styleCode)) && !Objects.equals(req.getForceUpdate(), Boolean.TRUE)) {
                log.info("=== 未核价设计款新增图包 图包已存在 spu:{}; ====", styleCode);
                resultList.add(VisualImageCreateVo.builder().styleCode(styleCode).errorMsg("spu图包已存在").build());
                return;
            }
            List<Prototype> skcList = prototypeSpuMap.get(styleCode);

            //符合取图规则的SKC
            Prototype selectedSkc = this.getSelectedSkc(skcList, detailMap);
            if (selectedSkc == null) {
                log.info("=== 未核价设计款新增图包 无符合取图规则的skc存在 spu:{}; ====", styleCode);
                resultList.add(VisualImageCreateVo.builder().styleCode(styleCode).errorMsg("无符合取图规则的skc").build());
                return;
            }

            //封装图包参数
            SaveOnShelfImagePackageReq packageReq = new SaveOnShelfImagePackageReq();
            packageReq.setStyleCode(styleCode);
            // List<ImageDTO> allSkuRaw = new ArrayList<>();
            // List<ImageDTO> firstSkuRaw = new ArrayList<>();
            // List<SaveOnShelfImagePackageReq.SkcImage> skcImageList = this.buildVisualSkcImage(skcList, detailMap, allSkuRaw, firstSkuRaw);

            //skc图片
            List<SaveOnShelfImagePackageReq.SkcImage> skcImageList = this.buildVisualSkcImage(selectedSkc, detailMap);
            packageReq.setSkcImages(skcImageList);
            //SPU图片
            this.buildVisualSpuImageInfo(style, skcList, detailMap, packageReq);

            log.info("=== 未核价设计款新增图包 spu:{}; 新增图包参数:{} ====", styleCode, JSON.toJSONString(packageReq));

            //不保存, 只验证图包新增逻辑
            if (!Objects.equals(req.getSaveData(), Boolean.TRUE)) {
                return;
            }

            //新增视觉spu
            if (Objects.isNull(oldVisualSpuMap.get(styleCode))) {
                visualSpuService.initVisualSpu(packageReq.getStyleCode());
            }

            //新增图包(并通知pop)
            visualImagePackageService.saveOnShelfImagePackage(packageReq);

            resultList.add(VisualImageCreateVo.builder().styleCode(styleCode).build());
        });

        List<String> addSpuList = StreamUtil.filterAndCovertList(resultList, item -> StrUtil.isBlank(item.getErrorMsg()), VisualImageCreateVo::getStyleCode);
        List<String> existSpuList = StreamUtil.filterAndCovertList(resultList, item -> StrUtil.isNotBlank(item.getErrorMsg()), VisualImageCreateVo::getStyleCode);
        sw.stop();
        log.info(sw.prettyPrint());
        log.info("=== 未核价设计款新增图包 耗时:{}s; 新增图包SPU:{}; 图包已存在的SPU:{} ===", sw.getTotalTimeSeconds(),
                JSON.toJSONString(addSpuList), JSON.toJSONString(existSpuList));

        return resultList;
    }


    /**
     * 构建设计款-视觉spu图片信息
     *
     * 图片命名规则:
     * 系统第一张图：SPU-101
     * 系统第二张图：SPU-888
     * 系统第三张图：SPU-999
     * 系统第四张图：SPU-501
     * 系统第五张图：SPU-601
     * 系统第六张图：SPU-401
     * 系统第七张图：SPU-201
     * 系统第八张图：SPU-301
     * 系统第九张图：颜色（英文命名）
     *
     */
    private void buildVisualSpuImageInfo(DesignStyle designStyle, List<Prototype> skcList, Map<Long, PrototypeDetail> detailMap, SaveOnShelfImagePackageReq packageReq) {
        String styleCode = designStyle.getStyleCode();
        List<SaveOnShelfImagePackageReq.SpuImage> spuImageList = new ArrayList<>();

        // 根据规则选择合适的SKC
        Prototype selectedSkc = this.getSelectedSkc(skcList, detailMap);
        if (selectedSkc == null) {
            packageReq.setSpuImages(spuImageList);
            return;
        }

        PrototypeDetail selectedSkcDetail = detailMap.get(selectedSkc.getPrototypeId());
        if (selectedSkcDetail == null || StrUtil.isBlank(selectedSkcDetail.getDesignPicture())) {
            packageReq.setSpuImages(spuImageList);
            return;
        }

        List<String> selectedPictures = StrUtil.splitTrim(selectedSkcDetail.getDesignPicture(), StrUtil.COMMA);

        // 系统第一张图：SPU-101   VisualTaskImageTypeEnum.SIZE_CHART
        if (!selectedPictures.isEmpty()) {
            this.setVisualSpuImage(styleCode, selectedPictures.getFirst(), VisualTaskImageTypeEnum.SIZE_CHART, spuImageList, "101");
        }

        List<ImageFile> otherFileList = new ArrayList<>();
        // 系统第二张图：SPU-888   VisualTaskImageTypeEnum.OTHER
        if (selectedPictures.size() > 1) {
            // this.setVisualSpuImage(styleCode, selectedPictures.get(1), VisualTaskImageTypeEnum.OTHER, spuImageList, "888");
            otherFileList.add(new ImageFile("888", selectedPictures.get(1)));
        }

        // 系统第三张图：SPU-999   VisualTaskImageTypeEnum.OTHER
        if (selectedPictures.size() > 2) {
            // this.setVisualSpuImage(styleCode, selectedPictures.get(2), VisualTaskImageTypeEnum.OTHER, spuImageList, "999");
            otherFileList.add(new ImageFile("999", selectedPictures.get(2)));
        }
        if (CollUtil.isNotEmpty(otherFileList)) {
            this.setVisualSpuImages(styleCode, otherFileList, VisualTaskImageTypeEnum.OTHER, spuImageList);
        }

        // 系统第四张图：SPU-501   VisualTaskImageTypeEnum.PRODUCT_DETAIL
        if (selectedPictures.size() > 3) {
            this.setVisualSpuImage(styleCode, selectedPictures.get(3), VisualTaskImageTypeEnum.PRODUCT_DETAIL, spuImageList, "501");
        }

        // 系统第五张图：SPU-601   VisualTaskImageTypeEnum.FABRIC
        if (selectedPictures.size() > 4) {
            this.setVisualSpuImage(styleCode, selectedPictures.get(4), VisualTaskImageTypeEnum.FABRIC, spuImageList, "601");
        }

        // 系统第六张图：SPU-401   VisualTaskImageTypeEnum.SIZING_TABLE
        if (selectedPictures.size() > 5) {
            this.setVisualSpuImage(styleCode, selectedPictures.get(5), VisualTaskImageTypeEnum.SIZING_TABLE, spuImageList, "401");
        }

        // 系统第七张图：SPU-201   VisualTaskImageTypeEnum.CATEGORY_CHART
        if (selectedPictures.size() > 6) {
            this.setVisualSpuImage(styleCode, selectedPictures.get(6), VisualTaskImageTypeEnum.CATEGORY_CHART, spuImageList, "201");
        }

        // 系统第八张图：SPU-301   VisualTaskImageTypeEnum.PRODUCT_MAIN
        if (selectedPictures.size() > 7) {
            this.setVisualSpuImage(styleCode, selectedPictures.get(7), VisualTaskImageTypeEnum.PRODUCT_MAIN, spuImageList, "301");
        }

        // 系统第九张图：颜色（英文命名）  VisualTaskImageTypeEnum.OTHER (归属到skc图了, 不放在spu图)
        /*
        if (selectedPictures.size() > 8) {
            // 获取颜色英文名
            if (CollUtil.isNotEmpty(selectedSkcDetail.getColorInfoList())) {
                List<String> englishNameList = StreamUtil.convertList(selectedSkcDetail.getColorInfoList(), ColorInfoVo::getColorEnglishName);
                String colorEnglishName = StrUtil.join(StrUtil.SPACE, englishNameList);
                this.setVisualSpuImage(styleCode, selectedPictures.get(8), VisualTaskImageTypeEnum.OTHER, spuImageList, colorEnglishName);
            } else {
                this.setVisualSpuImage(styleCode, selectedPictures.get(8), VisualTaskImageTypeEnum.OTHER, spuImageList, "color");
            }
        }
         */

        packageReq.setSpuImages(spuImageList);
    }

    /**
     * skc图片获取规则:
     * 单个SKC
     * ～～场景1:少于9张，按9张前处理命名
     * ～～场景2:大于等于9张，按9张前处理命名；超出9张的不命名
     *
     * 同个SPU下多个SKC
     * ～～场景1:所有SKC都少于9张，就取第一个最大图片数量的SKC（避免存在存在相同数量）
     * ～～场景2:第一个SKC大于等于9张，第二个SKC有N张；按第一个SKC的排序命名。其他SKC与第一个SKC非重复的图不命名
     * ～～场景3:第一个SKC少于9张，第二个SKC大于等于9张；按第二个SKC的排序命名。其他SKC与第一个SKC非重复的图不命名
     *
     */

    private Prototype getSelectedSkc(List<Prototype> skcList, Map<Long, PrototypeDetail> detailMap) {
        //1. 单个SKC
        if (skcList.size() == 1) {
            // 单个SKC的情况
            return skcList.getFirst();
        }

        //2. 若多个SKC
        Prototype selectedSkc = null;

        // 获取所有SKC的图片信息
        List<List<String>> allSkcPictures = new ArrayList<>();
        List<Prototype> sortedSkcList = skcList.stream()
                .sorted(Comparator.comparing(Prototype::getCreatedTime))
                .toList();

        for (Prototype skc : sortedSkcList) {
            PrototypeDetail detail = detailMap.get(skc.getPrototypeId());
            if (detail != null && StrUtil.isNotBlank(detail.getDesignPicture())) {
                List<String> pictures = StrUtil.splitTrim(detail.getDesignPicture(), StrUtil.COMMA);
                allSkcPictures.add(pictures);
            }
        }

        if (!allSkcPictures.isEmpty()) {
            List<String> firstSkcPics = allSkcPictures.getFirst();
            Prototype firstSkc = sortedSkcList.getFirst();
            PrototypeDetail firstSkcDetail = detailMap.get(firstSkc.getPrototypeId());
            List<String> firstPictures = new ArrayList<>();
            if (firstSkcDetail != null && StrUtil.isNotBlank(firstSkcDetail.getDesignPicture())) {
                firstPictures = StrUtil.splitTrim(firstSkcDetail.getDesignPicture(), StrUtil.COMMA);
            }

            // 2.1, 若所有SKC都少于9张，就取第一个最大图片数量的SKC
            boolean allLessThan9 = allSkcPictures.stream()
                    .allMatch(pics -> pics.size() < 9);

            if (allLessThan9) {
                // 找到图片数量最多的SKC（避免相同数量的情况，取第一个）
                int maxPictureCount = allSkcPictures.stream()
                        .mapToInt(List::size)
                        .max()
                        .orElse(0);

                // 取第一个具有最大图片数量的SKC
                for (int i = 0; i < allSkcPictures.size(); i++) {
                    List<String> pics = allSkcPictures.get(i);
                    if (pics.size() == maxPictureCount) {
                        selectedSkc = sortedSkcList.get(i);
                        break;
                    }
                }
            }
            // 2.2, 第一个SKC的图片大于等于9张，就取第一个SKC
            else if (firstPictures.size() >= 9) {
                selectedSkc = firstSkc;
            }
            // 2.3, 第一个SKC少于9张，就取后面第一个大于9张的SKC
            else {
                // 寻找第一个图片数量>=9的SKC
                for (int i = 0; i < allSkcPictures.size(); i++) {
                    List<String> pics = allSkcPictures.get(i);
                    if (pics.size() >= 9) {
                        selectedSkc = sortedSkcList.get(i);
                        break;
                    }
                }
                // 如果没有找到图片数量>=9的SKC，则使用第一个SKC
                if (selectedSkc == null) {
                    selectedSkc = firstSkc;
                }
            }
        }

        // 如果没有找到合适的SKC，则返回第一个SKC
        if (selectedSkc == null && !skcList.isEmpty()) {
            selectedSkc = skcList.stream()
                    .min(Comparator.comparing(Prototype::getCreatedTime))
                    .orElse(skcList.getFirst());
        }

        return selectedSkc;
    }

    private void setVisualSpuImage(String styleCode, String ossImageUrl, VisualTaskImageTypeEnum imageTypeEnum, List<SaveOnShelfImagePackageReq.SpuImage> spuImageList, String serialNum) {
        SaveOnShelfImagePackageReq.SpuImage spuImage = new SaveOnShelfImagePackageReq.SpuImage();
        spuImage.setImageType(imageTypeEnum.getCode());
        spuImage.setImageTypeDesc(imageTypeEnum.getDesc());
        List<ImageFile> imageFileList = new ArrayList<>();

        String extName = FileNameUtil.extName(ossImageUrl);
        ImageFile imageFile = new ImageFile();
        imageFile.setOssImageUrl(ossImageUrl);

        //重命名文件名: spu-数字号
        String orgImgName = styleCode + StrUtil.DASHED + serialNum + StrUtil.DOT + extName;

        imageFile.setOrgImgName(orgImgName);

        imageFileList.add(imageFile);
        spuImage.setImages(imageFileList);

        spuImageList.add(spuImage);
    }
    private void setVisualSpuImages(String styleCode, List<ImageFile> originFileList, VisualTaskImageTypeEnum imageTypeEnum, List<SaveOnShelfImagePackageReq.SpuImage> spuImageList) {
        if (CollUtil.isEmpty(originFileList)) {
            return;
        }
        SaveOnShelfImagePackageReq.SpuImage spuImage = new SaveOnShelfImagePackageReq.SpuImage();
        spuImage.setImageType(imageTypeEnum.getCode());
        spuImage.setImageTypeDesc(imageTypeEnum.getDesc());
        List<ImageFile> imageFileList = new ArrayList<>();

        originFileList.forEach(originFile -> {
            String extName = FileNameUtil.extName(originFile.getOssImageUrl());
            ImageFile imageFile = new ImageFile();
            imageFile.setOssImageUrl(originFile.getOssImageUrl());

            //重命名文件名: spu-数字号
            String orgImgName = styleCode + StrUtil.DASHED + originFile.getOrgImgName() + StrUtil.DOT + extName;
            imageFile.setOrgImgName(orgImgName);

            imageFileList.add(imageFile);

        });
        spuImage.setImages(imageFileList);

        spuImageList.add(spuImage);
    }

    private List<SaveOnShelfImagePackageReq.SkcImage> buildVisualSkcImage(Prototype selectedSkc,
                                                                          Map<Long, PrototypeDetail> detailMap) {

        PrototypeDetail prototypeDetail = detailMap.get(selectedSkc.getPrototypeId());
        List<ColorInfoVo> colorInfoList = prototypeDetail.getColorInfoList();
        List<String> englishNameList = StreamUtil.convertList(colorInfoList, ColorInfoVo::getColorEnglishName);
        List<String> colorNameList = StreamUtil.convertList(colorInfoList, ColorInfoVo::getColor);

        // ["Botanical Print", "Green"] 转换为  colorEnglishName, 拼接元素使用空格分开 "Botanical Print Green"
        String colorEnglishName = StrUtil.join(StrUtil.SPACE, englishNameList);
        List<String> pictures = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);

        //取第9张图作为颜色图
        if (pictures.size() < 9) {
            return null;
        }
        String skcPicture = pictures.get(8);
        if (StrUtil.isBlank(skcPicture)) {
            return null;
        }
        ImageDTO imageDTO = this.buildImagesForColorCode(selectedSkc, colorEnglishName, skcPicture);
        if (Objects.isNull(imageDTO)) {
            return null;
        }

        //skc图信息
        SaveOnShelfImagePackageReq.SkcImage skcImage = new SaveOnShelfImagePackageReq.SkcImage();
        skcImage.setDesignCode(selectedSkc.getDesignCode());
        skcImage.setColor(StrUtil.join(StrUtil.SPACE, colorNameList));
        skcImage.setColorEn(colorEnglishName);

        ImageFile imageFile = new ImageFile();
        imageFile.setOrgImgName(imageDTO.getOrgImgName());
        imageFile.setOssImageUrl(imageDTO.getOssImageUrl());

        skcImage.setImages(List.of(imageFile));

        return List.of(skcImage);
    }

    /*
    private List<SaveOnShelfImagePackageReq.SkcImage> buildVisualSkcImage(List<Prototype> prototypeList,
                                                                        Map<Long, PrototypeDetail> detailMap,
                                                                        List<ImageDTO> allSkuRaw, List<ImageDTO> firstSkuRaw) {
        List<SaveOnShelfImagePackageReq.SkcImage> skcImageList = new ArrayList<>();
        prototypeList.forEach(skc -> {
            PrototypeDetail prototypeDetail = detailMap.get(skc.getPrototypeId());
            List<ColorInfoVo> colorInfoList = prototypeDetail.getColorInfoList();
            List<String> englishNameList = StreamUtil.convertList(colorInfoList, ColorInfoVo::getColorEnglishName);
            List<String> colorNameList = StreamUtil.convertList(colorInfoList, ColorInfoVo::getColor);

            // ["Botanical Print", "Green"] 转换为  colorEnglishName, 拼接元素使用空格分开 "Botanical Print Green"
            String colorEnglishName = StrUtil.join(StrUtil.SPACE, englishNameList);
            List<String> pictures = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);

            //取第9张图昨晚颜色图
            if (pictures.size() < 9) {
                return;
            }
            String skcPicture = pictures.get(8);
            if (StrUtil.isBlank(skcPicture)) {
                return;
            }
            ImageDTO imageDTO = this.buildImagesForColorCode(skc, colorEnglishName, skcPicture);
            if (Objects.nonNull(imageDTO)) {
                SaveOnShelfImagePackageReq.SkcImage skcImage = new SaveOnShelfImagePackageReq.SkcImage();
                skcImage.setDesignCode(skc.getDesignCode());
                skcImage.setColor(StrUtil.join(StrUtil.SPACE, colorNameList));
                skcImage.setColorEn(colorEnglishName);

                ImageFile imageFile = new ImageFile();
                imageFile.setOrgImgName(imageDTO.getOrgImgName());
                imageFile.setOssImageUrl(imageDTO.getOssImageUrl());

                skcImage.setImages(List.of(imageFile));

                skcImageList.add(skcImage);
            }
            // if (CollectionUtil.isNotEmpty(imageDTOS)) {
            //     allSkuRaw.addAll(imageDTOS);
            //     firstSkuRaw.add(imageDTOS.getFirst());
            //
            //     SaveOnShelfImagePackageReq.SkcImage skcImage = new SaveOnShelfImagePackageReq.SkcImage();
            //     skcImage.setDesignCode(skc.getDesignCode());
            //     skcImage.setColor(StrUtil.join(StrUtil.SPACE, colorNameList));
            //     skcImage.setColorEn(colorEnglishName);
            //     List<ImageFile> images = imageDTOS.stream().map(imageDTO -> {
            //         ImageFile imageFile = new ImageFile();
            //         imageFile.setOrgImgName(imageDTO.getOrgImgName());
            //         imageFile.setOssImageUrl(imageDTO.getOssImageUrl());
            //         return imageFile;
            //     }).toList();
            //     skcImage.setImages(images);
            //
            //     skcImageList.add(skcImage);
            // }
        });
        return skcImageList;
    }

     */

    /**
     * 根据颜色代码和图片URL生成ImageAniVo对象列表 (复制from pop)
     * 第一张图片使用颜色代码作为名称，后续图片使用颜色代码-索引格式
     */
    private ImageDTO buildImagesForColorCode(Prototype skc, String colorCode, String skcPictureUrl) {
        if (StrUtil.isBlank(skcPictureUrl)) {
            return null;
        }
        if (StringUtils.isBlank(skcPictureUrl)) {
            return null;
        }
        int dotIndex = skcPictureUrl.lastIndexOf(".");
        // 跳过没有后缀的 url
        if (dotIndex < 0 || dotIndex == skcPictureUrl.length() - 1) {
            return null;
        }
        String suffix = skcPictureUrl.substring(dotIndex);
        // String imgName = skc.getDesignCode()+ StrUtil.DASHED + colorCode + suffix;
        String imgName = colorCode + suffix;
        ImageDTO imageDTO = new ImageDTO();
        imageDTO.setOrgImgName(imgName);
        imageDTO.setOssImageUrl(skcPictureUrl);
        return imageDTO;
    }

    /*
    private List<ImageDTO> buildImagesForColorCode(String colorCode, List<String> imageUrls) {
        if (CollUtil.isEmpty(imageUrls)) {
            return null;
        }
        return IntStream.range(0, imageUrls.size())
                .mapToObj(i -> {
                    String imageUrl = imageUrls.get(i);
                    if (StringUtils.isBlank(imageUrl)) {
                        return null;
                    }
                    int dotIndex = imageUrl.lastIndexOf(".");
                    // 跳过没有后缀的 url
                    if (dotIndex < 0 || dotIndex
                    == imageUrl.length() - 1) {
                        return null;
                    }
                    String suffix = imageUrl.substring(dotIndex);
                    String imgName = (i == 0) ? colorCode + suffix : colorCode + StrUtil.DASHED + i + suffix;
                    ImageDTO imageDTO = new ImageDTO();
                    imageDTO.setOrgImgName(imgName);
                    imageDTO.setOssImageUrl(imageUrl);
                    return imageDTO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

     */

    private List<StylePushPopVo> getStylePushPopErrorList(List<String> styleCodeList,
                                                          Map<String, DesignStyle> styleMap,
                                                          Map<String, List<Prototype>> prototypeSpuMap,
                                                          Map<Long, PrototypeDetail> detailMap) {
        List<StylePushPopVo> errorList = new ArrayList<>(styleCodeList.size());
        styleCodeList.forEach(styleCode -> {
            DesignStyle designStyle = styleMap.get(styleCode);
            //spu与成本价校验
            if (Objects.isNull(designStyle)) {
                errorList.add(StylePushPopVo.builder().styleCode(styleCode).errorMsg("spu不存在").build());
                return;
            }
            //skc校验
            List<Prototype> prototypeList = prototypeSpuMap.get(styleCode);
            if (CollUtil.isEmpty(prototypeList)) {
                errorList.add(StylePushPopVo.builder().styleCode(styleCode).errorMsg("spu下无已提交的skc").build());
                return;
            }
            //设计图校验
            prototypeList.forEach(skc -> {
                PrototypeDetail prototypeDetail = detailMap.get(skc.getPrototypeId());
                if (Objects.isNull(prototypeDetail)) {
                    errorList.add(StylePushPopVo.builder().styleCode(styleCode).designCode(skc.getDesignCode()).errorMsg("skc详情信息为空").build());
                }else if (StrUtil.isBlank(prototypeDetail.getDesignPicture())) {
                    errorList.add(StylePushPopVo.builder().styleCode(styleCode).designCode(skc.getDesignCode()).errorMsg("skc设计图为空").build());
                }
            });
        });
        return errorList;
    }

    private void setCreateUser(String creatorCode) {
        if (StrUtil.isBlank(creatorCode)) {
            return;
        }
        //查询创建人信息
        List<UserVo> userList = uacsRemoteHelper.findByCodes(Collections.singletonList(creatorCode));
        SdpDesignException.notEmpty(userList, "创建人不存在!");
        UserVo userVo = userList.getFirst();
        SdpDesignException.notNull(userVo.getId(), "创建人id为空!");
        SdpDesignException.notBlank(userVo.getCode(), "创建人编码为空!");

        UserContent userContent = UserContentHolder.get();
        userContent.setCurrentUserId(userVo.getId());
        userContent.setCurrentUserCode(userVo.getCode());
        userContent.setCurrentUserName(userVo.getName());
    }

    private BatchCreateVisualDemandResp checkFailData(Map<String, DesignStyle> styleMap,
                                                      String styleCode,
                                                      VisualDemandCreateData createData,
                                                      Map<String, List<Prototype>> skcGroupMap,
                                                      Map<Long, PrototypeDetail> prototypeDetailMap) {
        //入参校验
        String errorDataDesc = null;
        if (StrUtil.isBlank(createData.getStyleCode())) {
            errorDataDesc = "SPU为空";
        }
        if (StrUtil.isBlank(createData.getDemandTypeDesc())) {
            errorDataDesc = "需求类型为空";
        }
        if (StrUtil.isBlank(createData.getRealObjectColorStateDesc())) {
            errorDataDesc = "对实物色为空";
        }
        String demandTypeDesc = createData.getDemandTypeDesc();
        if (VisualTaskProcessTypeEnum.NO_FIX.getDesc().equals(demandTypeDesc)) {
            createData.setDemandType(VisualTaskProcessTypeEnum.NO_FIX.getCode());
        }else if (VisualTaskProcessTypeEnum.TRY_ON.getDesc().equals(demandTypeDesc)) {
            createData.setDemandType(VisualTaskProcessTypeEnum.TRY_ON.getCode());
        }else if (VisualTaskProcessTypeEnum.FIX_ON_SHELF.getDesc().equals(demandTypeDesc)) {
            createData.setDemandType(VisualTaskProcessTypeEnum.FIX_ON_SHELF.getCode());
        }else if (VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.getDesc().equals(demandTypeDesc)) {
            createData.setDemandType(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.getCode());
        }else {
            errorDataDesc = "未知需求类型; ";
        }
        String realObjectColorStateDesc = createData.getRealObjectColorStateDesc();
        if (Objects.equals(realObjectColorStateDesc, "是")) {
            createData.setRealObjectColorState(Bool.YES.getCode());
        }else if (Objects.equals(realObjectColorStateDesc, "否")) {
            createData.setRealObjectColorState(Bool.NO.getCode());
        }else {
            errorDataDesc += "未知对实物色状态";
        }
        BatchCreateVisualDemandResp errorResp = BatchCreateVisualDemandResp.builder()
                .styleCode(styleCode)
                .handleState("失败")
                .demandTypeDesc(createData.getDemandTypeDesc()).realObjectColorStateDesc(createData.getRealObjectColorStateDesc()).demandDesc(createData.getDemandDesc()).build();
        if (StrUtil.isNotBlank(errorDataDesc)) {
            return errorResp.setFailDesc(errorDataDesc);
        }

        //数据校验
        DesignStyle designStyle = styleMap.get(styleCode);
        if (Objects.isNull(designStyle)) {
            return errorResp.setFailDesc("SPU不存在");
        }
        List<Prototype> skcList = skcGroupMap.get(styleCode);
        if (CollUtil.isEmpty(skcList)) {
            return errorResp.setFailDesc("SPU下无skc");
        }
        //第一个skc, skcList按时间排序, 第一个
        Prototype firstSkc = skcList.stream().min(Comparator.comparing(Prototype::getCreatedTime)).orElse(null);
        if (Objects.isNull(firstSkc)) {
            return errorResp.setFailDesc("SPU下无skc");
        }
        if (Objects.equals(firstSkc.getPrototypeStatus(), PrototypeStatusEnum.WAIT_DECOMPOSE.getCode())) {
            return errorResp.setFailDesc("第一个skc未提交,无设计图; " + firstSkc.getDesignCode());
        }
        PrototypeDetail prototypeDetail = prototypeDetailMap.get(firstSkc.getPrototypeId());
        if (Objects.isNull(prototypeDetail)) {
            return errorResp.setFailDesc("skc详情不存在; " + firstSkc.getDesignCode());
        }
        String designPicture = prototypeDetail.getDesignPicture();
        if (StrUtil.isBlank(designPicture)) {
            return errorResp.setFailDesc("skc设计图为空; " + firstSkc.getDesignCode());
        }
        //取skc设计图作为需求图片
        List<String> designPictureList = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);
        createData.setDemandImages(designPictureList);

        return null;
    }

    private Map<Long, PrototypeDetail> getPrototypeDetailMap(List<Prototype> notCancelSkcList) {
        List<Long> prototypeIdList = StreamUtil.convertListAndDistinct(notCancelSkcList, Prototype::getPrototypeId);
        if (CollUtil.isEmpty(prototypeIdList)) {
            return Collections.emptyMap();
        }
        List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIdList);
        if (CollUtil.isEmpty(prototypeIdList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);
    }
}
