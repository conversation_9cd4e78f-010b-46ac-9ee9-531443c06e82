package tech.tiangong.sdp.design.service;


import cn.yibuyun.cloud.room.sdk.resp.PrototypeResp;
import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.query.prototype.PrototypeManageExcelQuery;
import tech.tiangong.sdp.design.vo.query.prototype.PrototypeManageQuery;
import tech.tiangong.sdp.design.vo.req.manage.*;
import tech.tiangong.sdp.design.vo.req.prototype.ChgDesignerReq;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeManageInfoBatchListReq;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderDetailVo;
import tech.tiangong.sdp.design.vo.resp.prototype.*;

import java.util.List;

/**
 * 设计款管理-服务接口
 *
 * <AUTHOR>
 * @since 2021-08-09 14:43:17
 */
public interface PrototypeManageService {

    // ================================= web =======================================

    /**
     * 设计款号管理 条件分页查询
     *
     * @param queryDTO 请求参数
     * @return 返回
     */
    PageRespVo<PrototypeManageQueryResp> page(PrototypeManageQuery queryDTO);


    /**
     * 样衣开发_核价-批量查询
     *
     * @param req 请求参数
     *@throws Exception 异常
     * @return 返回
     */
    List<PrototypeManageInfoQueryResp> clothesPriceList(PrototypeManageInfoBatchListReq req);

    /**
     * 设计款详情-首页(SPU + SKC)
     * @param designCode 设计款号
     * @param isEdit 是否查询编辑页: 0-否; 1-是;(默认否)
     * @return 返回
     */
    PrototypeTagVo spuSkcInfo(String designCode, Integer isEdit);

    /**
     * 取消设计款
     *
     * @param cancelReq 请求参数
     * @return 返回
     */
    PrototypeVo cancelDesign(PrototypeCancelReq cancelReq);

    /**
     * 标记-紧急
     *
     * @param urgentReq 请求参数
     */
    void markUrgent(PrototypeUrgentReq urgentReq);

    /**
     * 发起打版
     *
     * @param req 请求对象
     * @return PrototypeMakeClothesVo
     */
    PrototypeMakeClothesVo makeClothes(PrototypeMakeClothesReq req);

    /**
     * 设计款详情_bom标签页信息
     *
     * @param designCode 设计款号
     * @return 最新已提交的Bom详情
     * @throws Exception 异常
     */
    BomOrderDetailVo getBomInfo(String designCode)throws Exception;

    /**
     * 复色
     * @param req 入参
     * @return 复色skcId
     */
    Long colorsMaking(ColorsMakingReq req);

    /**
     * 变更设计师
     * @param chgDesignerReq 设计款
     */
    void designerChange(ChgDesignerReq chgDesignerReq);

    /**
     * 齐套预占位-测试/刷数预留接口
     * @param designCode 设计款号
     */
    void prePlacementCompleteMaterials(String designCode);


    /**
     * 根据designCode导出款式信息
     * @param queryDTO
     * */
    List<PrototypeExcelResp> prototypeManageExportExcel(PrototypeManageExcelQuery queryDTO);

    /**
     * 根据designCode导出款式图片
     * @param req
     * */
    List<PrototypeZipResp> prototypeManageExportZip(PrototypeExcelReq req);
}