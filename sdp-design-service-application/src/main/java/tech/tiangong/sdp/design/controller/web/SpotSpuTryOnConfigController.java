package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.SpotSpuTryOnConfigService;
import tech.tiangong.sdp.design.vo.req.tryOnConfig.TryOnConfigAddReq;
import tech.tiangong.sdp.design.vo.req.tryOnConfig.TryOnConfigEditReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuTryOnConfigVo;

/**
 * try on任务分配
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/try-on-config")
public class SpotSpuTryOnConfigController extends BaseController {
    private final SpotSpuTryOnConfigService spotSpuTryOnConfigService;


    /**
     * try on任务分配-新建
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/save")
    public DataResponse<Void> save(@RequestBody @Validated TryOnConfigAddReq req) {
        spotSpuTryOnConfigService.save(req);
        return DataResponse.ok();
    }

    /**
     * try-on任务详情
     *
     * @param tryOnConfigId
     * @return 响应结果
     */
    @GetMapping("/detail/{tryOnConfigId}")
    public DataResponse<SpotSpuTryOnConfigVo> getDetail(@PathVariable(value = "tryOnConfigId") Long tryOnConfigId) {
        return DataResponse.ok(spotSpuTryOnConfigService.getDetail(tryOnConfigId));
    }




    /**
     *  try-on任务编辑
     *      PS: 前端没用到这个接口, 更新逻辑也是在save接口处理
     *  @param req
     */
    @PostMapping("/edit")
    public DataResponse<Void> edit(@RequestBody @Validated TryOnConfigEditReq req) {
        spotSpuTryOnConfigService.edit(req);
        return DataResponse.ok();
    }



}
