package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.collection.Collections;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.SpecialAccessories;
import tech.tiangong.sdp.design.enums.SpecialAccessoriesStateEnum;
import tech.tiangong.sdp.design.mapper.SpecialAccessoriesMapper;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialExcelResp;
import tech.tiangong.sdp.utils.Bool;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

;

/**
 * 干什么
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/4 19:05
 */
@Repository
public class SpecialAccessoriesRepository extends BaseRepository<SpecialAccessoriesMapper, SpecialAccessories> {

	/**
	 * 根据BomID获取特殊辅料信息
	 */
	public List<SpecialAccessories> listByBomIds(List<Long> bomIdList, Integer transientState) {
		return baseMapper.selectList(new QueryWrapper<SpecialAccessories>().lambda()
				.in(SpecialAccessories::getBomId, bomIdList)
				.eq(SpecialAccessories::getTransientState, transientState));
	}

	/**
	 * 根据BomID获取特殊辅料信息(不包含暂存的)
	 */
	public List<SpecialAccessories> getListByBomId(Long bomId) {
		return baseMapper.selectList(new QueryWrapper<SpecialAccessories>().lambda()
				.eq(SpecialAccessories::getBomId, bomId)
				.eq(SpecialAccessories::getTransientState, Bool.NO.getCode()));
	}
	/**
	 * 根据BomID获取特殊辅料信息(包含暂存的)
	 */
	public List<SpecialAccessories> listByBomId(Long bomId) {
		return baseMapper.selectList(new QueryWrapper<SpecialAccessories>().lambda()
				.eq(SpecialAccessories::getBomId, bomId));
	}

	/**
	 * 根据BomID与暂存状态获取已提交的特殊辅料信息
	 */
	public List<SpecialAccessories> listByBomIdAndTransient(Long bomId, Integer transientState) {
		return baseMapper.selectList(new QueryWrapper<SpecialAccessories>().lambda()
				.eq(SpecialAccessories::getBomId, bomId)
				.eq(SpecialAccessories::getState, SpecialAccessoriesStateEnum.SUBMIT.getCode())
				.eq(Objects.nonNull(transientState), SpecialAccessories::getTransientState, transientState));
	}

	/**
	 * 根据id获取非暂存的特辅
	 * @param specialAccessoriesIds
	 * @return
	 */
	public List<SpecialAccessories> getListBySpecialAccessoriesIds(List<Long> specialAccessoriesIds) {
        if (CollUtil.isEmpty(specialAccessoriesIds)) {
            return new ArrayList<>();
        }
		return baseMapper.selectList(new QueryWrapper<SpecialAccessories>().lambda()
				.eq(SpecialAccessories::getTransientState, Bool.NO.getCode())
				.in(SpecialAccessories::getSpecialAccessoriesId, specialAccessoriesIds));
	}

	/**
	 * 根据id获取暂存的特辅
	 */
	public List<SpecialAccessories> getTransientListByIds(List<Long> specialAccessoriesIds) {
		return baseMapper.selectList(new QueryWrapper<SpecialAccessories>().lambda()
				.eq(SpecialAccessories::getTransientState, Bool.YES.getCode())
				.in(SpecialAccessories::getSpecialAccessoriesId, specialAccessoriesIds));
	}

	/**
	 * 根据designCode获取特殊辅料信息
	 * @param designCode
	 * @return
	 */
	public List<SpecialAccessories> getListByDesignCode(String designCode) {
		return baseMapper.selectList(new QueryWrapper<SpecialAccessories>().lambda()
				.eq(SpecialAccessories::getDesignCode, designCode)
				.eq(SpecialAccessories::getTransientState, Bool.NO.getCode()));
	}

	/**
	 * 根据designCode获取特殊辅料信息
	 *
	 * @param designCode
	 * @param specialAccessoriesState 状态
	 * @return
	 */
	public List<SpecialAccessories> getListByDesignCode(String designCode, SpecialAccessoriesStateEnum specialAccessoriesState) {
		return baseMapper.selectList(new QueryWrapper<SpecialAccessories>().lambda()
				.eq(SpecialAccessories::getDesignCode, designCode)
				.eq(SpecialAccessories::getState, specialAccessoriesState.getCode())
				.eq(SpecialAccessories::getTransientState, Bool.NO.getCode()));
	}

    public List<SpecialAccessories> listAfterDate(LocalDateTime updateBeginDate) {
		if (Objects.isNull(updateBeginDate)) {
			return List.of();
		}
		return lambdaQuery()
				.ge(SpecialAccessories::getCreatedTime, updateBeginDate)
				.groupBy(SpecialAccessories::getSkuId).list();
    }

    public List<Long> getAllSkuId() {
		return baseMapper.getAllSkuId();
    }

	public void batchUpdatePurchaseCycle(List<SpecialAccessories> updateList) {
		if (CollUtil.isEmpty(updateList)) {
			return;
		}
		baseMapper.batchUpdatePurchaseCycle(updateList);
	}

	public List<BomMaterialExcelResp> listMaterialExcel(List<Long> bomIdList) {
		if (CollUtil.isEmpty(bomIdList)) {
			return Collections.emptyList();
		}
		return baseMapper.listMaterialExcel(bomIdList);
	}
}
