package tech.tiangong.sdp.design.listener.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.Ability;
import tech.tiangong.sdp.design.enums.TaskStatus;
import tech.tiangong.sdp.design.enums.visual.*;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.listener.AIBoxTaskHandler;
import tech.tiangong.sdp.design.listener.entity.AIBoxTaskNotification;
import tech.tiangong.sdp.design.listener.entity.DifyTryOnAttribute;
import tech.tiangong.sdp.design.listener.entity.Tag;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.utils.UserHolderUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视觉AI修图处理器（tryon中和truon质检发起）
 * tryon质检 更名为 AI修图质检
 * 将原发起tryon的逻辑拆成三部分，原：tech.tiangong.sdp.design.controller.web.VisualTaskController#batchTryOn(java.util.List)
 * 现：前端先调用 tech.tiangong.sdp.design.controller.web.VisualTaskController#beforeInvokeAiBox(java.util.List)
 *      再调用创建AIBox的接口（murmuration那边）
 *      完成创建后会到本处理器的 handleTaskCreation 方法做业务处理
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VisualAiRetouchingHandler implements AIBoxTaskHandler {

    private final ObjectMapper objectMapper;
    private final VisualTaskRepository visualTaskRepository;
    private final VisualTaskTryOnRepository visualTaskTryOnRepository;
    private final VisualTaskTryOnLogRepository visualTaskTryOnLogRepository;
    private final VisualTaskTryOnImageRepository visualTaskTryOnImageRepository;
    private final VisualTaskNodeStateRepository visualTaskNodeStateRepository;
    private final VisualTaskHelper visualTaskHelper;
    private final VisualQcRepository qcRepository;

    @Override
    public Tag getSupportedTag() {
        return Tag.VISUAL_AI_RETOUCH;
    }

    @Override
    public void handleTaskCreation(AIBoxTaskNotification taskNotification) {
        log.info("创建视觉AI修图任务: taskId={}", taskNotification.aiBoxTaskId());

        // 获取用于关联的实体
        var visualTaskTryOn = visualTaskTryOnRepository.getById(taskNotification.taskSourceId());
        var visualTask = visualTaskRepository.getById(visualTaskTryOn.getTaskId());
        VisualTaskNodeState tryOnHandleNodeState = visualTaskNodeStateRepository.getNodeByProcessNode(visualTask.getTaskId(), VisualTaskNodeEnum.TRYON_HANDLE);
        VisualTaskNodeState tryOnQCNode = visualTaskNodeStateRepository.getNodeByProcessNode(visualTask.getTaskId(), VisualTaskNodeEnum.TRY_ON_QC);

        VisualTaskTryOnLog visualTaskTryOnLog = VisualTaskTryOnLog.builder()
                .visualTaskTryOnLogId(IdPool.getId())
                .tryOnDetailId(visualTaskTryOn.getTryOnDetailId())
                .taskId(visualTaskTryOn.getTaskId())
                .taskCode(visualTask.getProcessCode())
                .tryOntTaskState(VisualTaskHandleStateEnum.DOING.getCode())
                .tryOnTaskId(taskNotification.aiBoxTaskId())
                .build();
        // 设置系统用户信息
        UserHolderUtil.setSystemUserContent();

        visualTaskTryOn.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
        tryOnHandleNodeState.setProcessNodeState(VisualTaskHandleStateEnum.DOING.getCode());
        tryOnHandleNodeState.setRemark(VisualTaskNodeEnum.TRYON_HANDLE.getDesc() + ":" + VisualTaskHandleStateEnum.DOING.getDesc());

        visualTaskTryOnLogRepository.save(visualTaskTryOnLog);
        visualTaskTryOnRepository.updateById(visualTaskTryOn);
        visualTaskNodeStateRepository.updateById(tryOnHandleNodeState);
        // 如果已经开始try on质检了 要转回到try on进行中状态
        if (tryOnQCNode != null) {
            tryOnQCNode.setProcessNodeState(VisualTaskHandleStateEnum.WAITING_START.getCode());
            tryOnQCNode.setRemark(VisualTaskNodeEnum.TRY_ON_QC.getDesc() + ":" +VisualTaskHandleStateEnum.WAITING_START.getDesc());
            visualTaskNodeStateRepository.updateById(tryOnQCNode);
        }
        visualTaskHelper.addLog(visualTask.getStyleCode(), visualTask.getTaskId(), "发起线上try on任务");

        log.info("视觉AI修图任务创建成功: taskId={}", taskNotification.aiBoxTaskId());
    }

    @Override
    public void handleTaskUpdate(AIBoxTaskNotification taskNotification) {
        log.info("更新视觉AI修图任务: taskId={}, status={}",
                taskNotification.aiBoxTaskId(), taskNotification.status());

        var status = taskNotification.status() == TaskStatus.COMPLETED
                ? VisualTaskHandleStateEnum.FINISH.getCode()
                : VisualTaskHandleStateEnum.FAIL.getCode();
        var visualTaskTryOnLog = visualTaskTryOnLogRepository
                .lambdaQuery()
                .eq(VisualTaskTryOnLog::getTryOnTaskId, taskNotification.aiBoxTaskId())
                .one()
                .setTryOntTaskState(status);
        UserHolderUtil.setSystemUserContent();
        visualTaskTryOnLogRepository.updateById(visualTaskTryOnLog);
        if (taskNotification.status() == TaskStatus.COMPLETED && CollectionUtil.isNotEmpty(taskNotification.results())) {
            List<VisualTaskTryOnImage> visualTaskTryOnImageList = taskNotification.results().stream().map(result -> {
                        VisualTaskTryOnImage visualTaskTryOnImage = VisualTaskTryOnImage.builder()
                                .tryOnTaskImageId(IdPool.getId())
                                .tryOnLogId(visualTaskTryOnLog.getVisualTaskTryOnLogId())
                                .taskId(visualTaskTryOnLog.getTaskId())
                                .tryOnTaskId(visualTaskTryOnLog.getTryOnTaskId())
                                .ability(taskNotification.ability())
                                .generatedImg(result.url())
                                .generatedImgName(getFileNameFromOssUrl(result.url()))
                                .build();
                        // 处理TRY_ON类型的特殊属性
                        if (Objects.equals(taskNotification.ability(), Ability.TRY_ON.name()) && !result.attributes().isNull()) {
                            try {
                                DifyTryOnAttribute difyTryOnAttribute = objectMapper.treeToValue(result.attributes(), DifyTryOnAttribute.class);
                                visualTaskTryOnImage.setImageNode(Objects.nonNull(difyTryOnAttribute)? difyTryOnAttribute.imageResultType() : null);
                                visualTaskTryOnImage.setImageTryOnType(Objects.nonNull(difyTryOnAttribute)? difyTryOnAttribute.modelName() : null);
                            } catch (JsonProcessingException e) {
                                log.error("VisualAiRetouchingHandler handleTaskUpdate parse attributes error:{}", e.getMessage());
                            }
                        }

                        return visualTaskTryOnImage;
                    }
            ).toList();
            visualTaskTryOnImageRepository.saveBatch(visualTaskTryOnImageList);
        }

        VisualTaskNodeState nodeState = visualTaskNodeStateRepository.getNodeByProcessNode(visualTaskTryOnLog.getTaskId(), VisualTaskNodeEnum.TRYON_HANDLE);
        List<VisualTaskTryOnLog> processingLogList = visualTaskTryOnLogRepository.getProcessingAndFinishLogByTryOnDetailId(visualTaskTryOnLog.getTryOnDetailId());
        Map<Boolean, List<VisualTaskTryOnLog>> logMap = processingLogList.stream()
                .collect(Collectors.partitioningBy(e ->
                        VisualTaskHandleStateEnum.FINISH.getCode().equals(e.getTryOntTaskState())
                ));
        List<VisualTaskTryOnLog> finishList = logMap.get(true);
        List<VisualTaskTryOnLog> processingList = logMap.get(false);
        // 只有节点在等待开始或者进行中时 且没有待开始进行中 且有已完成（成功）的tryon（ai修图）日志，才更新状态为完成
        if (nodeState != null
                && Arrays.asList(VisualTaskHandleStateEnum.DOING.getCode(), VisualTaskHandleStateEnum.WAITING_START.getCode()).contains(nodeState.getProcessNodeState())
                && CollectionUtil.isEmpty(processingList)
                && CollectionUtil.isNotEmpty(finishList)) {
            nodeState.setProcessNodeState(VisualTaskHandleStateEnum.FINISH.getCode());
            nodeState.setRemark(VisualTaskNodeEnum.TRYON_HANDLE.getDesc()+":"+ VisualTaskHandleStateEnum.FINISH.getDesc());
            visualTaskNodeStateRepository.updateById(nodeState);

            // 新增try on质检任务
            VisualTaskNodeState tryOnQc = visualTaskNodeStateRepository.getNodeByProcessNode(visualTaskTryOnLog.getTaskId(), VisualTaskNodeEnum.TRY_ON_QC);
            if (tryOnQc == null) {
                // 新增节点状态
                tryOnQc = new VisualTaskNodeState();
                tryOnQc.setNodeStateId(IdPool.getId());
                tryOnQc.setTaskId(visualTaskTryOnLog.getTaskId());
                tryOnQc.setProcessNode(VisualTaskNodeEnum.TRY_ON_QC.getCode());
                tryOnQc.setProcessStep(VisualTaskStepEnum.QC.getCode());
                tryOnQc.setProcessNodeState(VisualTaskHandleStateEnum.WAITING_ALLOCATE.getCode());
                tryOnQc.setRemark(VisualTaskNodeEnum.TRY_ON_QC.getDesc()+":"+ VisualTaskQcStateEnum.WAITING_QC.getDesc());
            }else {
                // 更新节点状态
                tryOnQc.setProcessNodeState(VisualTaskHandleStateEnum.WAITING_ALLOCATE.getCode());
                tryOnQc.setRemark(VisualTaskNodeEnum.TRY_ON_QC.getDesc()+":"+ VisualTaskQcStateEnum.WAITING_QC.getDesc());
            }
            visualTaskNodeStateRepository.saveOrUpdate(tryOnQc);

            List<VisualQc> updateQcList = new ArrayList<>();
            VisualQc oldVisualQc = qcRepository.getLatestByTaskId(visualTaskTryOnLog.getTaskId(), VisualQcTypeEnum.TRY_ON);
            //已有视觉质检任务未提交过
            if(oldVisualQc!=null
                    && StringUtils.isBlank(oldVisualQc.getOnShelfImages())
                    && VisualTaskQcStateEnum.WAITING_QC.getCode().equals(oldVisualQc.getQcState())){
                oldVisualQc.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
//                        oldVisualQc.setQcRecord(JSONObject.toJSONString(visualQcRecords));
                oldVisualQc.setTaskHandlerId(visualTaskTryOnLog.getCreatorId());
                oldVisualQc.setTaskHandlerName(visualTaskTryOnLog.getCreatorName());
                oldVisualQc.setTaskHandleTime(LocalDateTime.now());
                updateQcList.add(oldVisualQc);
            }else{//创建视觉质检任务
                VisualQc visualQc = new VisualQc();
                visualQc.setQcRecordId(IdPool.getId());
                visualQc.setTaskId(visualTaskTryOnLog.getTaskId());
                visualQc.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
//                        visualQc.setQcRecord(JSONObject.toJSONString(visualQcRecords));
                visualQc.setVersionNum(oldVisualQc!=null ? oldVisualQc.getVersionNum()+1 : 1);
                visualQc.setQcState(VisualTaskQcStateEnum.WAITING_QC.getCode());
                visualQc.setQcType(VisualQcTypeEnum.TRY_ON.getCode());
                visualQc.setTaskHandlerId(visualTaskTryOnLog.getCreatorId());
                visualQc.setTaskHandlerName(visualTaskTryOnLog.getCreatorName());
                visualQc.setTaskHandleTime(LocalDateTime.now());
                visualQc.setIsLatest(1);
                updateQcList.add(visualQc);
                //将旧版本任务过期掉
                if(oldVisualQc!=null){
                    oldVisualQc.setIsLatest(0);
                    updateQcList.add(oldVisualQc);
                }
            }
            qcRepository.saveOrUpdateBatch(updateQcList);
        }


        log.info("视觉AI修图任务更新成功: taskId={}", taskNotification.aiBoxTaskId());

    }

    private String getFileNameFromOssUrl(String ossUrl) {
        if (StringUtil.isBlank(ossUrl)) {
            return "";
        }
        // 提取文件名
        int lastSlashIndex = ossUrl.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < ossUrl.length() - 1) {
            return ossUrl.substring(lastSlashIndex + 1);
        }

        // 如果没有找到斜杠，返回空字符串
        return "";
    }
}