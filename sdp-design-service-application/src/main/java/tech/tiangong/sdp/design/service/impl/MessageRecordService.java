package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.entity.BaseWithReviserEntity;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.design.entity.MessageRecord;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.enums.MqStateEnum;
import tech.tiangong.sdp.design.mq.enums.MqTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.repository.MessageRecordRepository;
import tech.tiangong.sdp.design.vo.req.mq.MqRetryReq;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageRecordService {
    private final MessageRecordRepository logRepository;
    private final MqProducer mqProducer;

    public void retryReq(MqRetryReq req) {
        log.info("=========================>mq重试请求:{}", req);
        List<MessageRecord> records;
        //指定消息某条记录
        if (CollUtil.isNotEmpty(req.getLogIds())) {
            records = logRepository.listByIds(req.getLogIds());
        } else {
            records = logRepository.page(new Page<>(1, req.getPageSize(), false), Wrappers.<MessageRecord>lambdaQuery()
                    .eq(Objects.nonNull(req.getType()), MessageRecord::getType, Optional.ofNullable(req.getType()).map(MqTypeEnum::getCode).orElse(null))
                    .eq(MessageRecord::getState, MqStateEnum.FAIL_HANDLE.getCode())
                    .lt(MessageRecord::getRetry, req.getMaxRetry())
                    //只处理最近2天内的，历史的不管了
                    .gt(BaseWithReviserEntity::getRevisedTime, LocalDateTime.now().minusDays(2).with(LocalTime.MIN))
                    .orderByAsc(BaseWithReviserEntity::getRevisedTime)).getRecords();
        }
        if (records.isEmpty()) {
            log.info("-------------------->没有需要重试的mq");
            return;
        }
        log.info("---------------------------->开始重新消费:{}", records.stream().map(MessageRecord::getMessageId).collect(Collectors.toList()));

        records.forEach(record -> {
            MqBizTypeEnum bizType = MqBizTypeEnum.fromCode(record.getBizType());
            if (bizType == null) {
                log.info("找不到匹配的业务类型:{}", record.getMessageId());
                logRepository.updateSate(record.getMessageId(), MqStateEnum.SUCC_HANDLE, "找不到匹配的业务类型");
                return;
            }
            log.info("-------------------->重新消费-{}----id={},重试次数:{}", bizType.getDesc(), record.getMessageId(), record.getRetry() + 1);
            try {
                UserContentHolder.set(new UserContent()
                        .setCurrentUserId(record.getCreatorId())
                        .setCurrentUserName(record.getCreatorName())
                        .setCurrentUserCode(record.getCreatorId()+"")
                        .setCurrentDepartmentId(0L)
                        .setTenantId(2L)
                        .setSystemCode("SDP"));

                if (MqTypeEnum.CONSUMER.getCode().equals(record.getType())) {
                    //消费重试
                    doBizConsumer(bizType, record);
                } else {
                    //发送重试
                    mqProducer.doSendMq(MqMessageReq.of(record));
                }
            } finally {
                UserContentHolder.clean();
            }
        });
    }

    public void doBizConsumer(MqBizTypeEnum bizType, MessageRecord record) {
        try {
            //消费重试
            bizType.bizConsumer(MqMessageReq.of(record));

            logRepository.updateSate(record.getMessageId(), MqStateEnum.SUCC_HANDLE, null);
            log.info("成功消费-{}----id={}", bizType.getDesc(), record.getMessageId());
        } catch (Exception e) {
            logRepository.updateSate(record.getMessageId(), MqStateEnum.FAIL_HANDLE, e.getMessage());
            log.info("失败消费-{}----id={}", bizType.getDesc(), record.getMessageId(), e);
        }
    }

}
