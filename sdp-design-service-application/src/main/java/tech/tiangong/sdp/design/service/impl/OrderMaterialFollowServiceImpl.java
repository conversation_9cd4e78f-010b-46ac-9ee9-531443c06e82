package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.base.Strings;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import cn.yibuyun.plm.design.open.vo.req.purchase.SignMaterialOrderToDesignCodeOpenReq;
import cn.yibuyun.tg.open.client.plm.design.OrderMaterialFollowOpenClient;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.zjkj.scf.bundle.common.dto.ordermaterial.req.SignByMaterialOrderReq;
import com.zjkj.scf.bundle.common.dto.ordermaterial.resp.OrderMaterialResp;
import com.zjkj.scf.bundle.sdk.client.ordermaterial.feign.OrderMaterialFeign;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.clothes.vo.resp.SampleClothesDetailVo;
import tech.tiangong.sdp.clothes.vo.resp.SampleClothesVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.OrderMaterialMqContant;
import tech.tiangong.sdp.design.converter.PurchasePrototypeInfoConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DesignLogBizTypeEnum;
import tech.tiangong.sdp.design.enums.MaterialDemandTypeEnum;
import tech.tiangong.sdp.design.enums.MaterialKittingStateEnum;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.SampleClothesRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjNotify2OldJvRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.DesignLogService;
import tech.tiangong.sdp.design.service.OrderMaterialFollowService;
import tech.tiangong.sdp.design.vo.dto.ChangeDesignerDto;
import tech.tiangong.sdp.design.vo.req.log.DesignLogReq;
import tech.tiangong.sdp.design.vo.req.ordermaterial.*;
import tech.tiangong.sdp.design.vo.resp.material.MaterialPurchaseFollowVo;
import tech.tiangong.sdp.design.vo.resp.material.OrderMaterialInfoVo;
import tech.tiangong.sdp.design.vo.resp.ordermaterial.MaterialDemandVo;
import tech.tiangong.sdp.design.vo.resp.ordermaterial.OrderMaterialFollowPageVo;
import tech.tiangong.sdp.design.vo.resp.purchase.PrototypeEncapsulationVO;
import tech.tiangong.sdp.design.vo.resp.remark.DesignRemarksVO;
import tech.tiangong.sdp.design.vo.resp.zj.design.PrototypeOrderMaterialOpenResp;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 *
 * 物料齐套跟进
 * <br>CreateDate August 10,2021
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderMaterialFollowServiceImpl implements OrderMaterialFollowService {

    private final OrderMaterialFollowRepository orderMaterialFollowRepository;
    private final MaterialPurchaseFollowRepository materialPurchaseFollowRepository;
    private final OrderMaterialFeign orderMaterialFeign;
    private final OrderMaterialFollowOpenClient orderMaterialFollowOpenClient;
    private final MqProducer mqProducer;
    private final PrototypeRepository prototypeRepository;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final DesignLogService designLogService;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final PurchasePrototypeInfoRepository purchasePrototypeInfoRepository;
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final ZjNotify2OldJvRemoteHelper zjNotify2OldJvRemoteHelper;


    /**
     * 物料齐套跟进列表信息（分页）
     *
     * @param req 分页的查询参数
     * @return
     */
    @Override
    public PageRespVo<OrderMaterialFollowPageVo> pageList(OrderMaterialFollowPageReq req) {

        Page<OrderMaterialFollowPageVo> page;
        if (req.getPageSize() != -1) {
            page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        }else{
            page = new Page<>();
        }
        page = PageHelper.startPage(req.getPageNum(), req.getPageSize())
                .doSelectPage(() -> orderMaterialFollowRepository.pageList(req));
        List<OrderMaterialFollowPageVo> list = page.getResult();

        List<MaterialPurchaseFollow> materialPurchaseFollowList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(list)){
            // 通过齐套单号  materialKittingCode
            List<String> materialKittingCodeList = list.stream().map(OrderMaterialFollowPageVo::getMaterialCode).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(materialKittingCodeList)){
                materialPurchaseFollowList = materialPurchaseFollowRepository.list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                        .in(MaterialPurchaseFollow::getMaterialKittingCode, materialKittingCodeList)
                        .eq(MaterialPurchaseFollow::getStatus,Bool.YES.getCode()));
            }
            //设计图
            List<Long> prototypeIdList = StreamUtil.convertListAndDistinct(list, OrderMaterialFollowPageVo::getPrototypeId);
            List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIdList);
            Map<Long, PrototypeDetail> prototypeDetailMap = StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);
            list.forEach(item -> {
                PrototypeDetail prototypeDetail = prototypeDetailMap.get(item.getPrototypeId());
                if (Objects.nonNull(prototypeDetail)) {
                    item.setDesignPicture(prototypeDetail.getDesignPicture());
                }
            });
        }
        // 取消的物料不展示二次工艺
        cuttingProcessData(materialPurchaseFollowList,list);
        PageInfo<OrderMaterialFollowPageVo> pageInfo = new PageInfo<>(page);
        Assert.isTrue(pageInfo != null, "分页返回参数为空");

        return PageRespVoHelper.of(pageInfo.getPageNum(), pageInfo.getTotal(), pageInfo.getList());
    }

    /**
     * 新增物料齐套或者更新状态跟进信息
     *
     * @param req          物料齐套跟进新增信息参数
     * @param mqMessageReq
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncOrderMaterialCreateOrUpdateState(OrderMaterialFollowAddReq req, MqMessageReq mqMessageReq) {
        log.info("===============================> 齐套单信息新增或者更新状态req:{}",JSON.toJSONString(req));
        UserContent userContent = UserContentHolder.get();
        if(StringUtil.isNotEmpty(req.getSigner())){
            userContent.setCurrentUserId(req.getSignerId());
            userContent.setCurrentUserName(req.getSigner());
        }
        Prototype prototype = prototypeRepository.getByDesignCode(req.getDesignCode());
        if (Objects.isNull(prototype)) {
            log.info("=== 齐套单通知-转发旧JV; skc:{} ===", req.getDesignCode());
            zjNotify2OldJvRemoteHelper.notify2OldJv(mqMessageReq);
            return;
        }

        List<OrderMaterialFollow> orderMaterialFollowList = orderMaterialFollowRepository.list(
                Wrappers.<OrderMaterialFollow>lambdaQuery()
                        .eq(OrderMaterialFollow::getMaterialCode, req.getMaterialCode())
                        .eq(OrderMaterialFollow::getDesignCode,req.getDesignCode()));
        // 供应履约的加工单号为空时默认存的1
        if("1".equals(req.getProcessCode())){
            req.setProcessCode("");
        }
        if(CollectionUtil.isEmpty(orderMaterialFollowList)){
            // 判断是否补料的操作 1.如果加工单号为空则证明是首单（用设计款号查询）
            List<OrderMaterialFollow> orderMaterialFollows = new ArrayList<>();
            Boolean firstOrder = sampleClothesRemoteHelper.isFirstOrder(req.getDesignCode());
            if(firstOrder){
                orderMaterialFollows = orderMaterialFollowRepository.list(
                        Wrappers.<OrderMaterialFollow>lambdaQuery()
                                .eq(OrderMaterialFollow::getDesignCode, req.getDesignCode()));
            } else {
                orderMaterialFollows = orderMaterialFollowRepository.list(
                        Wrappers.<OrderMaterialFollow>lambdaQuery()
                                .eq(OrderMaterialFollow::getDesignCode, req.getDesignCode())
                                .eq(StringUtil.isNotEmpty(req.getProcessCode()),OrderMaterialFollow::getProcessCode,req.getProcessCode()));
            }

            if (orderMaterialFollows.stream().anyMatch(it -> MaterialKittingStateEnum.ALREADY_SIGN_FOR.getCode().equals(it.getMaterialState()))
                && !MaterialKittingStateEnum.ALREADY_SIGN_FOR.getCode().equals(req.getMaterialState())
                    && !MaterialKittingStateEnum.CLOSED.getCode().equals(req.getMaterialState())) {
                // 发送信息给打版需求服务.通知打上补料标签
                MqMessageReq build = MqMessageReq.build(MqBizTypeEnum.ORDER_MATERIAL_IS_ADD_MATERIAL
                        , OrderMaterialMqContant.SDP_DESIGN_ORDER_MATERIAL_IS_ADD_MATERIAL_EXCHANGE
                        , OrderMaterialMqContant.SDP_DESIGN_ORDER_MATERIAL_IS_ADD_MATERIAL_ROUTING_KEY
                        , JSONUtil.toJsonStr(req));
                mqProducer.sendOnAfterCommit(build);
            }

            Long purchasePrototypeInfoId = IdPool.getId();
            OrderMaterialFollow orderMaterialFollow = new OrderMaterialFollow();
            BeanUtils.copyProperties(req,orderMaterialFollow);
            orderMaterialFollow.setOrderMaterialFollowId(IdPool.getId());
            orderMaterialFollow.setReceivingTime(req.getRevisedTime());
            orderMaterialFollow.setCreatorName(ObjectUtil.isNotEmpty(userContent) ? userContent.getCurrentUserName():"1009");
            if(StringUtil.isNotEmpty(req.getDesignCode())){
                PrototypeEncapsulationVO prototypeEncapsulationVO = prototypeEncapsulation(req);
                log.info("prototypeEncapsulationVO ： ==========================>  req={},prototype={},prototypeDetailList={},sampleClothesVo={}",
                        JSONUtil.toJsonStr(req),JSONUtil.toJsonStr(prototypeEncapsulationVO.getPrototype()),
                        JSONUtil.toJsonStr(prototypeEncapsulationVO.getPrototypeDetailList()),
                        JSONUtil.toJsonStr(prototypeEncapsulationVO.getSampleClothesVo()));

                // 保存采购信息关联版单信息
                purchasePrototypeInfoId = savePurchasePrototypeInfo(req,prototypeEncapsulationVO.getSampleClothesVo(),
                        purchasePrototypeInfoId,prototypeEncapsulationVO.getPrototype(),
                        prototypeEncapsulationVO.getPrototypeDetailList());

                orderMaterialFollow.setPrototypeId(purchasePrototypeInfoId);
            }

            orderMaterialFollowRepository.save(orderMaterialFollow);
            // 根据齐套单编号查询采购单的创建人
            materialCodeToUser(userContent,req);
            saveDesignLog(orderMaterialFollow,"创建【物料齐套信息】");

        } else {
            List<OrderMaterialFollow> omfs = orderMaterialFollowRepository.list(
                    Wrappers.<OrderMaterialFollow>lambdaQuery()
                            .eq(OrderMaterialFollow::getMaterialCode, req.getMaterialCode()));
            if(CollectionUtil.isNotEmpty(omfs)){
                OrderMaterialFollow orderMaterialFollow = omfs.get(0);
                log.info("================> 齐套单当前状态：{},齐套单最新状态：{}",orderMaterialFollow.getMaterialState(),req.getMaterialState());
                if(orderMaterialFollow.getMaterialState().equals(req.getMaterialState())){
                    return;
                }
            }
            List<OrderMaterialFollow> orderMaterialFollows = orderMaterialFollowRepository.list(
                    Wrappers.<OrderMaterialFollow>lambdaQuery()
                            .eq(OrderMaterialFollow::getMaterialCode, req.getMaterialCode())
                            .lt(OrderMaterialFollow::getReceivingTime, req.getRevisedTime()));//小于
            if(CollectionUtil.isNotEmpty(orderMaterialFollows)){
                OrderMaterialFollow orderMaterialFollow = orderMaterialFollows.get(0);
                OrderMaterialFollow orderMaterialFollow1 = new OrderMaterialFollow();
                orderMaterialFollow1.setOrderMaterialFollowId(orderMaterialFollow.getOrderMaterialFollowId());
                orderMaterialFollow1.setMaterialState(req.getMaterialState());
                orderMaterialFollow1.setProcessCode(req.getProcessCode());
                orderMaterialFollow1.setReceivingTime(req.getRevisedTime());

                orderMaterialFollow.setMaterialState(req.getMaterialState());
                orderMaterialFollow.setProcessCode(req.getProcessCode());
                orderMaterialFollow.setReceivingTime(req.getRevisedTime());
                if(MaterialKittingStateEnum.ALREADY_SIGN_FOR.getCode().equals(req.getMaterialState())){
                    //如果是jv-plm web端发起的签收, 签收人不取履约回复的
                    if (Objects.nonNull(orderMaterialFollow.getSigner()) && Objects.nonNull(orderMaterialFollow.getSignerId())) {
                        req.setSigner(orderMaterialFollow.getSigner());
                        req.setSignerId(orderMaterialFollow.getSignerId());
                        req.setSigningTime(orderMaterialFollow.getSigningTime());
                    }
                    orderMaterialFollow1.setSigningTime(req.getSigningTime());
                    orderMaterialFollow1.setSignerId(req.getSignerId());
                    orderMaterialFollow1.setSigner(req.getSigner());

                    orderMaterialFollow.setSigningTime(req.getSigningTime());
                    orderMaterialFollow.setSignerId(req.getSignerId());
                    orderMaterialFollow.setSigner(req.getSigner());
                    // 记录签收成功的日志
                    saveDesignLog(orderMaterialFollow,"完成了【齐套签收】");

                    // 发送信息给打版需求服务
                    log.info("==================================> 同步齐套单信息给打版需求服务");
                    MqMessageReq build = MqMessageReq.build(MqBizTypeEnum.ORDER_MATERIAL_PATTERN_MAKING_STATUS
                            , OrderMaterialMqContant.SDP_DESIGN_ORDER_MATERIAL_PATTERN_MAKING_STATUS_EXCHANGE
                            , OrderMaterialMqContant.SDP_DESIGN_ORDER_MATERIAL_PATTERN_MAKING_STATUS_ROUTING_KEY
                            , JSONUtil.toJsonStr(req));
                    mqProducer.sendOnAfterCommit(build);

                }
                orderMaterialFollowRepository.updateById(orderMaterialFollow1);
            }


        }

    }

    /**
     * 物料齐套跟进新增  同步履约的齐套信息
     *
     * @param req          物料齐套跟进新增信息参数
     * @param mqMessageReq
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncOrderMaterialDetailInfo(OrderMaterialDetailReq req, MqMessageReq mqMessageReq) {
        log.info("===============================> 齐套单明细信息更新req:{}",JSON.toJSONString(req));
        String cuttingOrderCode = StringUtil.isNotEmpty(req.getAccessoriesMatchOrderCode())
                ? req.getAccessoriesMatchOrderCode()
                :req.getCuttingOrderCode();
        SdpDesignException.isTrue(StringUtil.isNotEmpty(cuttingOrderCode),
                "供应履约创建齐套单明细同步到云板房信息异常req：{}",JSONUtil.toJsonStr(req));
        List<MaterialPurchaseFollow> materialPurchaseFollowList = materialPurchaseFollowRepository.list(
                Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                .eq(MaterialPurchaseFollow::getCuttingCode, cuttingOrderCode));
        if(CollectionUtil.isNotEmpty(materialPurchaseFollowList)){
            List<MaterialPurchaseFollow> list = new ArrayList<>();
            materialPurchaseFollowList.stream().forEach(materialPurchaseFollow -> {
                MaterialPurchaseFollow mf = new MaterialPurchaseFollow();
                mf.setMaterialPurchaseFollowId(materialPurchaseFollow.getMaterialPurchaseFollowId());
                mf.setMaterialKittingCode(req.getMaterialCode());
                list.add(mf);
            });
            if(CollectionUtil.isNotEmpty(list)){
                materialPurchaseFollowRepository.updateBatchById(list);
            }
        } else {
            //转发到致景
            log.info("=== 齐套单明细通知-转发旧JV ====");
            zjNotify2OldJvRemoteHelper.notify2OldJv(mqMessageReq);
            // 对生成剪版单事务未完成时做的补偿机制
            int retryNum = 0;
            int retryMaxNum = 4;
            List<MaterialPurchaseFollow> materialPurchaseFollows = null;
            do{
                try {
                    retryNum++;
                    if(retryNum == 5){
                        SdpDesignException.isTrue(false,"生成剪版单事务未完成时做的补偿机制获取失败");
                    }
                    Thread.sleep(3000);
                    materialPurchaseFollows = materialPurchaseFollowRepository.list(
                            Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                                    .eq(MaterialPurchaseFollow::getCuttingCode, cuttingOrderCode));
                    log.info("生成剪版单事务未完成时做的补偿机制,retryNum:{}",retryNum);
                } catch (InterruptedException e) {
                    log.error("生成剪版单事务未完成时做的补偿机制,Message:{},retryNum:{}",e.getMessage(),retryNum);
                }

            }while(CollectionUtil.isEmpty(materialPurchaseFollows) && retryNum <= retryMaxNum);

            if(CollectionUtil.isNotEmpty(materialPurchaseFollows)){
                List<MaterialPurchaseFollow> list = new ArrayList<>();
                materialPurchaseFollows.stream().forEach(materialPurchaseFollow -> {
                    MaterialPurchaseFollow mf = new MaterialPurchaseFollow();
                    mf.setMaterialPurchaseFollowId(materialPurchaseFollow.getMaterialPurchaseFollowId());
                    mf.setMaterialKittingCode(req.getMaterialCode());
                    list.add(mf);
                });
                if(CollectionUtil.isNotEmpty(list)){
                    materialPurchaseFollowRepository.updateBatchById(list);
                }
            } else {
                // 如果为空则保存在消息记录表中
            /*OrderMaterialMessageRecord record = copyVo(req, OrderMaterialMessageRecordTypeEnum.STATE_SYNC.getCode(),
                    MqStateEnum.WAIT_HANDLE.getCode());
            orderMaterialMessageRecordRepository.save(record);*/
            }
        }
    }

    /**
     * 扫码管理--齐套签收  -- 齐套签收列表查询
     * @param designCode 设计款号
     * @return List<MaterialPurchaseFollow>
     */
    @Override
    public List<MaterialPurchaseFollowVo> allSetSignMaterialList(String designCode) {
        SdpDesignException.isTrue(StringUtil.isNotEmpty(designCode),"设计款号为空");
        //校验加工单车版环节是否存在异常
        this.checkSewException(designCode);
        List<OrderMaterialFollow> orderMaterialFollowList = orderMaterialFollowRepository.list(
                Wrappers.<OrderMaterialFollow>lambdaQuery()
                        .eq(OrderMaterialFollow::getDesignCode, designCode)
                        .eq(OrderMaterialFollow::getMaterialState, MaterialKittingStateEnum.TO_BE_SIGNED.getCode()));

        SdpDesignException.isTrue(CollectionUtil.isNotEmpty(orderMaterialFollowList),"无待签收的齐套单信息");
        List<String> materialCodeList = orderMaterialFollowList.stream()
                .map(OrderMaterialFollow::getMaterialCode).collect(Collectors.toList());

        List<MaterialPurchaseFollowVo> materialPurchaseFollows = new ArrayList<>();
        List<MaterialPurchaseFollow> materialPurchaseFollowList = materialPurchaseFollowRepository.list(
                Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                        .in(MaterialPurchaseFollow::getMaterialKittingCode, materialCodeList)
                        .eq(MaterialPurchaseFollow::getStatus,Bool.YES.getCode()));

        if(CollectionUtil.isEmpty(materialPurchaseFollowList)){
            //面辅料采购跟进表
            SdpDesignException.isTrue(CollectionUtil.isNotEmpty(materialPurchaseFollowList),"面辅料采购跟进为空");
        }
        Map<Long, List<BomOrderMaterial>> bomOrderMaterialMap = null;
        Set<Long> bomMaterialIds = materialPurchaseFollowList.stream().map(MaterialPurchaseFollow::getBomMaterialId).collect(Collectors.toSet());
        if(CollectionUtil.isNotEmpty(bomMaterialIds)){
            List<BomOrderMaterial> list = bomOrderMaterialRepository.list(Wrappers.<BomOrderMaterial>lambdaQuery()
                    .in(BomOrderMaterial::getBomMaterialId, bomMaterialIds));
            bomOrderMaterialMap = list.stream().collect(Collectors.groupingBy(BomOrderMaterial::getBomMaterialId));
        }
        // materialKittingState 齐套状态 100:待齐套 110:待发货 120:待签收 130:已签收 140:已关闭
        for(MaterialPurchaseFollow materialPurchaseFollow : materialPurchaseFollowList){
            MaterialPurchaseFollowVo vo = new MaterialPurchaseFollowVo();
            // 只显示待签收的数据
            BeanUtils.copyProperties(materialPurchaseFollow,vo);
            if(StringUtil.isNotEmpty(materialPurchaseFollow.getMaterialRemark())){
                List<DesignRemarksVO> designRemarksVOS = JSON.parseArray(materialPurchaseFollow.getMaterialRemark(), DesignRemarksVO.class);
                DesignRemarksVO designRemarksVO = designRemarksVOS.stream().max(Comparator.comparing(DesignRemarksVO::getCreatedTime)).get();
                vo.setMaterialRemark(designRemarksVO.getRemark());
            }
            /*if(ObjectUtil.isNotEmpty(bomOrderMaterialMap)){
                List<BomOrderMaterial> bomOrderMaterials = bomOrderMaterialMap.get(materialPurchaseFollow.getBomMaterialId());
                vo.setSingleDosage(CollectionUtil.isNotEmpty(bomOrderMaterials)? bomOrderMaterials.get(0).getSingleDosage():null);
            }*/
//            PurchasePrototypeInfo prototypeInfo = purchasePrototypeInfoRepository.getById(materialPurchaseFollow.getPrototypeId());
//            if(ObjectUtil.isNotEmpty(prototypeInfo) && ObjectUtil.isNotEmpty(prototypeInfo.getClothesId())){
//                List<Long> list = new ArrayList<>();
//                // pricingCostId
//                list.add(materialPurchaseFollow.getPrototypeMaterialId());
//                if (MaterialDemandTypeEnum.FABRIC.getCode().equals(materialPurchaseFollow.getDemandType())) {
//                    DataResponse<List<PricingFabricCostVo>> listDataResponse = pricingFabricCostClient.listByIds(list);
//                    if(listDataResponse.isSuccessful()){
//                        vo.setSingleDosage(CollectionUtil.isNotEmpty(listDataResponse.getData())? listDataResponse.getData().get(0).getPerPieceAmount():null);
//                    }
//                } else {
//                    DataResponse<List<PricingAccessoriesCostVo>> listDataResponse = pricingAccessoriesCostClient.listByIds(list);
//                    if(listDataResponse.isSuccessful()){
//                        vo.setSingleDosage(CollectionUtil.isNotEmpty(listDataResponse.getData())? listDataResponse.getData().get(0).getPerPieceAmount():null);
//                    }
//                }
//            } else {
//                if(ObjectUtil.isNotEmpty(bomOrderMaterialMap)){
//                    List<BomOrderMaterial> bomOrderMaterials = bomOrderMaterialMap.get(materialPurchaseFollow.getBomMaterialId());
//                    vo.setSingleDosage(CollectionUtil.isNotEmpty(bomOrderMaterials)? bomOrderMaterials.get(0).getSingleDosage():null);
//                }
//            }

            materialPurchaseFollows.add(vo);
        }
        return materialPurchaseFollows;
    }

    private void checkSewException(String designCode) {
        List<SampleClothesVo> clothesVoList = sampleClothesRemoteHelper.findByDesignCode(designCode);
        if(CollectionUtil.isNotEmpty(clothesVoList)){
            SampleClothesVo sampleClothes = clothesVoList.stream()
                    .filter(item -> Objects.equals(item.getIsLatest(), Bool.YES.getCode()))
                    .findFirst().orElse(null);
            if(Objects.equals(Bool.YES.getCode(), sampleClothes.getIsAbnormal())){
                //判断是否车版环节异常
                Boolean sewException = sampleClothesRemoteHelper.checkSewHasNotEndException(sampleClothes.getClothesId());
                SdpDesignException.isFalse(sewException,"样衣打版信息处于异常状态，请先处理异常，再进行签收");
            }

        }
    }

    /**
     * 齐套签收
     *
     * @param designCode 设计款号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sign(String designCode) {
        SdpDesignException.isTrue(StringUtil.isNotEmpty(designCode),"设计款号为空");
        UserContent userContent = UserContentHolder.get();
        List<OrderMaterialFollow> orderMaterialFollows = new ArrayList<>();
        List<OrderMaterialFollow> orderMaterialFollowList = orderMaterialFollowRepository.list(
                Wrappers.<OrderMaterialFollow>lambdaQuery().eq(OrderMaterialFollow::getDesignCode, designCode));
        SdpDesignException.isTrue(CollectionUtil.isNotEmpty(orderMaterialFollowList),"签收失败，当前款没有有效齐套单号");

        orderMaterialFollowList.stream().forEach(orderMaterialFollow -> {
            if(MaterialKittingStateEnum.TO_BE_SIGNED.getCode().equals(orderMaterialFollow.getMaterialState())){
                // 只显示待签收的数据
                orderMaterialFollows.add(orderMaterialFollow);
            }
        });

        SdpDesignException.isTrue(CollectionUtil.isNotEmpty(orderMaterialFollows),"当前齐套单不处于待签收状态，不可进行签收操作");

        //本地先更新签收状态, 在调用履约签收接口
        orderMaterialFollows.forEach(item -> {
            OrderMaterialFollow updateFollow = new OrderMaterialFollow();
            updateFollow.setOrderMaterialFollowId(item.getOrderMaterialFollowId());
            updateFollow.setSigner(userContent.getCurrentUserName());
            updateFollow.setSignerId(userContent.getCurrentUserId());
            updateFollow.setSigningTime(LocalDateTime.now());
            orderMaterialFollowRepository.updateById(updateFollow);
        });
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        SdpDesignException.notNull(prototype, "skc不存在!:{}", designCode);
        log.info("========================> 物料齐套跟进信息:{}", JSON.toJSONString(orderMaterialFollowList));
        SignMaterialOrderToDesignCodeOpenReq signReq = new SignMaterialOrderToDesignCodeOpenReq();
        signReq.setDesignCode(designCode);
        signReq.setBizChannel(prototype.getBizChannel());
        signReq.setSignerId(userContent != null?userContent.getCurrentUserId():1L);
        signReq.setSignerName(userContent != null?userContent.getCurrentUserName():"1");
        signReq.setSignerCode(userContent != null?userContent.getCurrentUserCode():"1");
        log.info("========================> SignReq:{}", JSON.toJSONString(signReq));

        DataResponse<Void> response = orderMaterialFollowOpenClient.sign(signReq);
        log.info("==============【齐套签收完成】，data={},message={}",JSONUtil.toJsonStr(response.getData()),response.getMessage());
        SdpDesignException.isTrue(response.isSuccessful(),"签收失败{}",response.getMessage());
    }

    /**
     * 根据设计款号和加工单号查询齐套单状态
     *
     * @param req
     * @return boolean  false-待签收、true-已签收
     */
    @Override
    public boolean getOrderMaterialStatus(OrderMaterialStatusInnerReq req) {
        SdpDesignException.isTrue(StringUtil.isNotEmpty(req.getDesignCode()), "设计款号为空");
        SdpDesignException.isTrue(StringUtil.isNotEmpty(req.getProcessCode()), "加工单号为空");
        SdpDesignException.isTrue(ObjectUtil.isNotEmpty(req.getFirstOrder()), "是否首单为空");

        List<OrderMaterialFollow> list = orderMaterialFollowRepository.list(Wrappers.<OrderMaterialFollow>lambdaQuery()
                .eq(OrderMaterialFollow::getDesignCode, req.getDesignCode())
                .eq(!req.getFirstOrder(), OrderMaterialFollow::getProcessCode, req.getProcessCode())
        );
        if(CollectionUtil.isEmpty(list)){
            return false;
        }
        if (list.stream().anyMatch(it -> MaterialKittingStateEnum.ALREADY_SIGN_FOR.getCode().equals(it.getMaterialState()))) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String signByOrderMaterialCode(SignMaterialOrderReq req) {
        log.info("====================> 齐套签收(app端) : {}", JSON.toJSONString(req));
        Long materialOrderId ;
        try{
            materialOrderId = Long.valueOf(req.getMaterialOrderId());
        }catch (Exception e){
            throw new SdpDesignException("签收失败，当前二维码不是齐套单号: " + req.getMaterialOrderId());
        }
        SignByMaterialOrderReq sreq = new SignByMaterialOrderReq();
        BeanUtils.copyProperties(req,sreq);
        log.info("根据齐套单号签收 req：{}", JSON.toJSONString(req));

        //  通过齐套单id查询齐套单号
        com.zjkj.booster.common.protocol.DataResponse<OrderMaterialResp> orderMaterialRespDataResponse = orderMaterialFeign.fetchOrderMaterialDetailBy(materialOrderId);
        log.info("====================> 齐套签收(app端) :根据齐套单id查询齐套信息-> response {}", JSON.toJSONString(orderMaterialRespDataResponse));
        // 由于履约判断如果查不到就抛异常，所以这边就这样提示
        SdpDesignException.isTrue(orderMaterialRespDataResponse.isSuccessful(),"签收失败，当前二维码不是齐套单号");
        OrderMaterialResp data = orderMaterialRespDataResponse.getData();

        SdpDesignException.isTrue(ObjectUtil.isNotEmpty(data),"签收失败，当前二维码不是齐套单号");
        sreq.setMaterialOrderCode(data.getMaterialCode());
        List<OrderMaterialFollow> orderMaterialFollowList = orderMaterialFollowRepository.list(Wrappers.<OrderMaterialFollow>lambdaQuery()
                .eq(OrderMaterialFollow::getMaterialCode, data.getMaterialCode()));
        if(CollectionUtil.isNotEmpty(orderMaterialFollowList)){
            OrderMaterialFollow orderMaterialFollow = orderMaterialFollowList.get(0);
            String designCode = orderMaterialFollow.getDesignCode();
            this.checkSewException(designCode);
        }
        SdpDesignException.isTrue(!MaterialKittingStateEnum.ALREADY_SIGN_FOR.getCode().equals(data.getMaterialState()),"当前齐套单已经签收");
        if(!MaterialKittingStateEnum.TO_BE_SIGNED.getCode().equals(data.getMaterialState())){
            SdpDesignException.isTrue(false,"签收失败，当前齐套单不可签收");
        }

        com.zjkj.booster.common.protocol.DataResponse<Void> voidDataResponse = orderMaterialFeign.signBy(sreq);
        log.info("====================> 齐套签收(app端) :signBy-> response {}", JSON.toJSONString(voidDataResponse));
        SdpDesignException.isTrue(voidDataResponse.isSuccessful(),"签收失败，齐套签收异常");
        UserContent userContent = UserContentHolder.get();
        if(StringUtil.isNotEmpty(req.getSignerName())){
            userContent.setCurrentUserId(req.getSignerId());
            userContent.setCurrentUserName(req.getSignerName());
        }
        // 记录签收成功的日志
        if(CollectionUtil.isNotEmpty(orderMaterialFollowList)){
            saveDesignLog(orderMaterialFollowList.get(0),"完成了【齐套签收】");
        }
        // MotRecordAddReq motRecordAddReq = new MotRecordAddReq();
        // motRecordAddReq.setBizType(MotRecordBizTypeEnum.SIGN_ALL_SETS.getCode());
        // motRecordAddReq.setCreatedTime(LocalDateTime.now());
        // motRecordAddReq.setCreatorId(req.getSignerId());
        // motRecordAddReq.setCreatorName(req.getSignerName());
        // motRecordAddReq.setDesignCode(data.getPrototypeCode());
        // motRecordClient.save(motRecordAddReq);
        return data.getPrototypeCode();
    }

    @Override
    public List<MaterialDemandVo> materialOrderToCraft(MaterialOrderToCraftReq req) {
        log.info("================> 查询剪版单或者齐套单下的工艺信息的请求参数：{}",JSONUtil.toJsonStr(req));

        // 1、根据设计款号查询齐套单（待齐套、待发货）
        List<OrderMaterialFollow> orderMaterialFollowList = orderMaterialFollowRepository.list(Wrappers.<OrderMaterialFollow>lambdaQuery()
                .eq(OrderMaterialFollow::getDesignCode, req.getDesignCode()).in(OrderMaterialFollow::getMaterialState,
                        MaterialKittingStateEnum.WAIT_FOR_ALL.getCode(),MaterialKittingStateEnum.TO_BE_DELIVERED.getCode()));

        log.info("================> 齐套单：{}",JSONUtil.toJsonStr(orderMaterialFollowList));

        if(CollectionUtil.isEmpty(orderMaterialFollowList)){
            return null;
        }
        Set<String> materialCodes = orderMaterialFollowList.stream().map(OrderMaterialFollow::getMaterialCode).collect(Collectors.toSet());
        // 1、查询齐套单下的物料明细
        List<MaterialPurchaseFollow> materialPurchaseFollowList = materialPurchaseFollowRepository.list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                .in(MaterialPurchaseFollow::getMaterialKittingCode, materialCodes));

        log.info("================> 齐套单下的物料明细：{}",JSONUtil.toJsonStr(materialPurchaseFollowList));

        if(CollectionUtil.isEmpty(materialPurchaseFollowList)){
            return null;
        }

        Map<Long, List<MaterialPurchaseFollow>> materialPurchaseFollowMapBySnapshotId = materialPurchaseFollowList.stream().filter(mf ->ObjectUtil.isNotEmpty(mf.getMaterialSnapshotId()))
                .collect(Collectors.groupingBy(MaterialPurchaseFollow::getMaterialSnapshotId));

        List<MaterialDemandVo> materialDemandVoList = new ArrayList<>();
        List<MaterialDemandReq> materialDemandList = req.getMaterialDemandList();
        // 组装返回数据
        for (MaterialDemandReq materialDemand:materialDemandList) {
            if(ObjectUtil.isNotEmpty(materialDemand.getMaterialSnapshotId())){
                Long materialSnapshotId = materialDemand.getMaterialSnapshotId();
                List<Long> materialSnapshotIdList = new ArrayList<>();
                materialSnapshotIdList.add(materialDemand.getMaterialSnapshotId());
                List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(materialSnapshotIdList);
                if (CollectionUtils.isNotEmpty(snapshotList)) {
                    for (MaterialSnapshot snapshot : snapshotList) {
                        materialSnapshotId = snapshot.getMaterialSnapshotId();
                        //历史物料(走履约匹配的), 取matchId
                        if (Objects.equals(snapshot.getNewMaterial(), Bool.NO.getCode()) && Objects.nonNull(snapshot.getMatchId())) {
                            materialSnapshotId = snapshot.getMatchId();
                        }
                    }
                }

                List<MaterialPurchaseFollow> mpfList = materialPurchaseFollowMapBySnapshotId.get(materialSnapshotId);
                if(CollectionUtil.isEmpty(mpfList)){
                    continue;
                }
                mpfList.forEach(materialPurchaseFollow -> {
                    MaterialDemandVo vo = new MaterialDemandVo();
                    vo.setThirdPartyCraftDemandIdList(materialDemand.getThirdPartyCraftDemandIdList());
                    if(materialPurchaseFollow.getDemandType().equals(MaterialDemandTypeEnum.FABRIC.getCode())){
                        vo.setCuttingOrderCode(materialPurchaseFollow.getCuttingCode());
                    } else {
                        vo.setMatchOrderCode(materialPurchaseFollow.getCuttingCode());
                    }
                    //添加匹配id
                    // vo.setMatchId(materialPurchaseFollow.getMatchId());
                    vo.setMaterialSnapshotId(materialDemand.getMaterialSnapshotId());
                    materialDemandVoList.add(vo);
                });
            }

        }
        return materialDemandVoList;
    }

    /**
     * 裁前
     *
     * 二次工艺
     * @param materialPurchaseFollowList
     * @param list
     */
    public void cuttingProcessData(List<MaterialPurchaseFollow> materialPurchaseFollowList,List<OrderMaterialFollowPageVo> list){
        if(CollectionUtil.isNotEmpty(materialPurchaseFollowList)){
            Map<String,List<MaterialPurchaseFollow>> groupBy = materialPurchaseFollowList.stream()
                    .collect(Collectors.groupingBy(MaterialPurchaseFollow::getMaterialKittingCode));
            for(OrderMaterialFollowPageVo vo : list){
                if(StringUtil.isNotEmpty(vo.getMaterialCode()) && CollectionUtil.isNotEmpty(groupBy.get(vo.getMaterialCode()))){
                    List<MaterialPurchaseFollow> materialPurchaseFollows = groupBy.get(vo.getMaterialCode());
                    List<String> cuttingProcessList = materialPurchaseFollows.stream().map(MaterialPurchaseFollow::getCuttingProcess)
                            .filter(Strings::isNotBlank).collect(Collectors.toList());
                    vo.setCuttingProcess(CollectionUtil.isNotEmpty(cuttingProcessList) ? CollectionUtil.join(cuttingProcessList, StringPool.COMMA) : null);
                }
            }
        }
    }

    /**
     * 添加操作日志
     *
     * @param orderMaterialFollow
     */
    public void saveDesignLog(OrderMaterialFollow orderMaterialFollow,String content){
        log.info("=================================> 操作日志记录：{},orderMaterialFollow:{}",content,JSON.toJSONString(orderMaterialFollow));
        if(ObjectUtil.isEmpty(orderMaterialFollow)){
            return;
        }
        List<PurchasePrototypeInfo> list = purchasePrototypeInfoRepository.list(Wrappers.<PurchasePrototypeInfo>lambdaQuery()
                .eq(PurchasePrototypeInfo::getPurchasePrototypeInfoId, orderMaterialFollow.getPrototypeId()));
        DesignLogReq designLogReq = DesignLogReq.builder().bizId(orderMaterialFollow.getOrderMaterialFollowId())
                .bizType(DesignLogBizTypeEnum.ORDER_MATERIAL_FOLLOW)
                .designCode(orderMaterialFollow.getDesignCode())
                .bizVersionNum(Bool.NO.getCode())
                .content(content)
                .build();
        designLogService.saveDesignLog(designLogReq,list.get(0));
    }

    /**
     * 根据齐套单编号查询采购单的创建人
     * @param userContent
     * @param req
     */
    public void materialCodeToUser(UserContent userContent,OrderMaterialFollowAddReq req){
        log.info("==========================> 根据齐套单编号查询采购单的创建人 <==========================");
        int retryNum = 0;
        int retryMaxNum = 3;
        List<MaterialPurchaseFollow> materialPurchaseFollows = null;
        do{
            try {
                Thread.sleep(2000);
                retryNum++;
                materialPurchaseFollows = materialPurchaseFollowRepository.list(
                        Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                                .eq(MaterialPurchaseFollow::getMaterialKittingCode, req.getMaterialCode()));
                if(CollectionUtil.isNotEmpty(materialPurchaseFollows)){
                    MaterialPurchaseFollow materialPurchaseFollow = materialPurchaseFollows.get(0);
                    userContent.setCurrentUserId(materialPurchaseFollow.getCreatorId());
                    userContent.setCurrentUserName(materialPurchaseFollow.getCreatorName());
                    log.info("==========================> 根据齐套单编号查询采购单的创建人 ," +
                            "creatorId : {},creatorName : {}",materialPurchaseFollow.getCreatorId(),materialPurchaseFollow.getCreatorName());
                }
            } catch (InterruptedException e) {}
        }while(CollectionUtil.isEmpty(materialPurchaseFollows) && retryNum <= retryMaxNum);
    }


    /**
     * 保存采购信息关联版单信息
     *
     * @param purchasePrototypeInfoId
     * @param prototype
     * @param prototypeDetailList
     */
    public Long savePurchasePrototypeInfo(OrderMaterialFollowAddReq req,
                                          SampleClothesVo sampleClothesVo,
                                          Long purchasePrototypeInfoId,
                                          Prototype prototype,
                                          List<PrototypeDetail> prototypeDetailList){
        List<PurchasePrototypeInfo> list = new ArrayList<>();
        Long clothesId = null;
        String processCode = sampleClothesVo.getProcessCode();
        // 打版类型（0：普通；1：大货）
        if(0 == req.getPrototypeType()){
            list = purchasePrototypeInfoRepository.list(Wrappers.<PurchasePrototypeInfo>lambdaQuery()
                    .eq(PurchasePrototypeInfo::getPrototypeId, prototype.getPrototypeId()));
            // 考虑到大货样衣如果选择走普通样衣的情况
            if(CollectionUtil.isEmpty(list)){
                clothesId = prototype.getPrototypeId();
                prototype.setPrototypeId(null);
                list = purchasePrototypeInfoRepository.list(Wrappers.<PurchasePrototypeInfo>lambdaQuery()
                        .eq(PurchasePrototypeInfo::getClothesId, clothesId));
            }
        } else if(1 == req.getPrototypeType()) {
            clothesId = prototype.getPrototypeId();
            prototype.setPrototypeId(null);
            list = purchasePrototypeInfoRepository.list(Wrappers.<PurchasePrototypeInfo>lambdaQuery()
                    .eq(PurchasePrototypeInfo::getClothesId, clothesId));
        }
        if(CollectionUtil.isEmpty(list)){
            PrototypeDetail prototypeDetail = null;
            if (CollectionUtil.isNotEmpty(prototypeDetailList)){
                prototypeDetail = prototypeDetailList.get(0);
            }
            PrototypeHistory prototypeHistory = new PrototypeHistory();
            BeanUtils.copyProperties(prototype,prototypeHistory);
            PurchasePrototypeInfo purchasePrototypeInfo = PurchasePrototypeInfoConverter.of(
                    purchasePrototypeInfoId,prototypeHistory, prototypeDetail,processCode,clothesId);
            if(ObjectUtil.isNotEmpty(purchasePrototypeInfo)){
                purchasePrototypeInfoRepository.save(purchasePrototypeInfo);
            }
        } else {
            purchasePrototypeInfoId = list.get(0).getPurchasePrototypeInfoId();
        }
        return purchasePrototypeInfoId;
    }

    public PrototypeEncapsulationVO prototypeEncapsulation(OrderMaterialFollowAddReq req){
        Prototype prototype = new Prototype();
        List<PrototypeDetail> prototypeDetailList = new ArrayList<>();
        SampleClothesVo sampleClothesVo = new SampleClothesVo();
        String designCode = req.getDesignCode();
        // 打版类型（0：普通；1：大货）
        if(ObjectUtil.isEmpty(req.getPrototypeType()) || 0 == req.getPrototypeType()){
            prototype = prototypeRepository.getByDesignCode(designCode);
            prototypeDetailList = prototypeDetailRepository.list(Wrappers.<PrototypeDetail>lambdaQuery()
                    .eq(ObjectUtil.isNotEmpty(prototype),PrototypeDetail::getPrototypeId, prototype.getPrototypeId()));
        }
        PrototypeEncapsulationVO vo = new PrototypeEncapsulationVO();
        vo.setPrototype(prototype);
        vo.setPrototypeDetailList(prototypeDetailList);
        vo.setSampleClothesVo(sampleClothesVo);

        return vo;
    }

    public void prototypeAssignment(PrototypeHistory prototypeHistory, PrototypeDetail prototypeDetail
            , SampleClothesVo sampleClothesVo, SampleClothesDetailVo sampleClothesDetailVo){
        // prototypeDetail.setCustomerPicture(sampleClothesVo.getCustomerPicture());
        prototypeHistory.setIsMakeMore(Boolean.FALSE);
        // prototypeHistory.setCategory(sampleClothesVo.getCategory());
        // prototypeHistory.setCategoryName(sampleClothesVo.getCategoryName());
        prototypeHistory.setDesignCode(sampleClothesVo.getDesignCode());
        prototypeHistory.setDesignerId(sampleClothesDetailVo.getDesignerId());
        prototypeHistory.setDesignerCode(sampleClothesDetailVo.getDesignerCode());
        prototypeHistory.setDesignerGroup(sampleClothesDetailVo.getDesignerGroup());
        prototypeHistory.setDesignerGroupCode(sampleClothesDetailVo.getDesignerGroupCode());
        prototypeHistory.setDesignerName(sampleClothesDetailVo.getDesignerName());
        prototypeHistory.setDesignerName(sampleClothesDetailVo.getDesignerName());
        // prototypeHistory.setPurchaserCode(sampleClothesVo.getPurchaserCode());
        // prototypeHistory.setPurchaserName(sampleClothesVo.getPurchaserName());
        prototypeHistory.setCreatedTime(sampleClothesVo.getCreatedTime());
        // prototypeHistory.setSampleType(sampleClothesVo.getSampleType());
        prototypeHistory.setStyleCode(sampleClothesVo.getStyleCode());
        prototypeHistory.setVersionNum(sampleClothesVo.getVersionNum());
        prototypeHistory.setPrototypeId(sampleClothesVo.getClothesId());
        // prototypeHistory.setRegionId(sampleClothesVo.getRegionId());
        // prototypeHistory.setRegionName(sampleClothesVo.getRegionName());
        // prototypeHistory.setPurchaserId(sampleClothesVo.getPurchaserId());
    }


    /**
     * 齐套签收(app端使用)
     *
     * @param req 齐套签收 app端（手输设计款号签收）参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String signByApp(SignMaterialOrderToDesignCodeReq req) {
        log.info("==============【APP端手输设计款号进行齐套签收】，req={}",JSONUtil.toJsonStr(req));

        List<OrderMaterialFollow> orderMaterialFollows = new ArrayList<>();
        List<OrderMaterialFollow> orderMaterialFollowList = orderMaterialFollowRepository.list(
                Wrappers.<OrderMaterialFollow>lambdaQuery().eq(OrderMaterialFollow::getDesignCode, req.getDesignCode()));
        SdpDesignException.isTrue(CollectionUtil.isNotEmpty(orderMaterialFollowList),"签收失败，当前款没有有效齐套单号");

        orderMaterialFollowList.stream().forEach(orderMaterialFollow -> {
            if(MaterialKittingStateEnum.TO_BE_SIGNED.getCode().equals(orderMaterialFollow.getMaterialState())){
                // 只显示待签收的数据
                orderMaterialFollows.add(orderMaterialFollow);
            }
        });

        SdpDesignException.isTrue(CollectionUtil.isNotEmpty(orderMaterialFollows),"当前齐套单不处于待签收状态，不可进行签收操作");

        Prototype prototype = prototypeRepository.getByDesignCode(req.getDesignCode());
        SdpDesignException.notNull(prototype, "skc不存在!:{}", req.getDesignCode());

        SignMaterialOrderToDesignCodeOpenReq signReq = new SignMaterialOrderToDesignCodeOpenReq();
        signReq.setBizChannel(prototype.getBizChannel());
        signReq.setSignerId(req.getSignerId());
        signReq.setSignerName(req.getSignerName());
        signReq.setDesignCode(req.getDesignCode());
        signReq.setSignerCode(req.getSignerCode());
        log.info("========================> SignReq:{}", JSON.toJSONString(signReq));
        DataResponse<Void> response = orderMaterialFollowOpenClient.sign(signReq);

        log.info("==============【APP端手输设计款号进行齐套签收完成】，data={},message={}",JSONUtil.toJsonStr(response.getData()),response.getMessage());

        SdpDesignException.isTrue(response.isSuccessful(),"签收失败{}",response.getMessage());

        // 记录签收成功的日志
        if(CollectionUtil.isNotEmpty(orderMaterialFollows)){
            orderMaterialFollows.forEach(orderMaterialFollow -> {
                saveDesignLog(orderMaterialFollow,"完成了【齐套签收】");
            });
        }
        // MotRecordAddReq motRecordAddReq = new MotRecordAddReq();
        // motRecordAddReq.setBizType(MotRecordBizTypeEnum.SIGN_ALL_SETS.getCode());
        // motRecordAddReq.setCreatedTime(LocalDateTime.now());
        // motRecordAddReq.setCreatorId(req.getSignerId());
        // motRecordAddReq.setCreatorName(req.getSignerName());
        // motRecordAddReq.setDesignCode(req.getDesignCode());
        // motRecordClient.save(motRecordAddReq);

        return req.getDesignCode();
    }

    /**
     * 变更设计师 -- 以设计款号的维度
     * @param dto 设计师信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void orderMaterialChangeDesigner(ChangeDesignerDto dto){
        log.info("=====================>【更新设计师信息】,changeDesignerDto={}",JSONUtil.toJsonStr(dto));
        SdpDesignException.notEmpty(dto.getDesignCodeList(),"设计款号为空");
        SdpDesignException.notBlank(dto.getDesignerCode(),"设计师为空");
        SdpDesignException.notBlank(dto.getDesignerGroupCode(),"设计师组别为空");

        List<PurchasePrototypeInfo> list = purchasePrototypeInfoRepository.list(Wrappers.<PurchasePrototypeInfo>lambdaQuery()
                .in(PurchasePrototypeInfo::getDesignCode, dto.getDesignCodeList()));

        log.info("=====================>【更新设计师信息】,list={}",JSONUtil.toJsonStr(list));
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        List<PurchasePrototypeInfo> purchasePrototypeInfoList = new ArrayList<>();
        list.forEach(info -> {
            PurchasePrototypeInfo vo = new PurchasePrototypeInfo();
            vo.setPurchasePrototypeInfoId(info.getPurchasePrototypeInfoId());
            vo.setDesignerName(dto.getDesignerName());
            vo.setDesignerId(dto.getDesignerId());
            vo.setDesignerCode(dto.getDesignerCode());
            vo.setDesignerGroupCode(dto.getDesignerGroupCode());
            vo.setDesignerGroup(dto.getDesignerGroupName());
            purchasePrototypeInfoList.add(vo);
        });
        log.info("=====================>【更新设计师信息】,purchasePrototypeInfoList={}",JSONUtil.toJsonStr(purchasePrototypeInfoList));
        purchasePrototypeInfoRepository.updateBatchById(purchasePrototypeInfoList);
    }

    @Override
    public List<OrderMaterialInfoVo> listByDesignCode(OrderMaterialInnerReq req) {

        List<OrderMaterialFollow> list = orderMaterialFollowRepository.listByDesignCode(req.getDesignCode());

        return list.stream().map(item -> {
            OrderMaterialInfoVo infoVo = new OrderMaterialInfoVo();
            BeanUtils.copyProperties(item, infoVo);
            return infoVo;
        }).collect(Collectors.toList());
    }

    @Override
    public String queryZjOrder(String designCode) {
        if (StrUtil.isBlank(designCode)) {
            return null;
        }
        List<PrototypeOrderMaterialOpenResp> latestMaterialList = zjDesignRemoteHelper.findLatestMaterial(Collections.singletonList(designCode), false);
        if (CollUtil.isEmpty(latestMaterialList)) {
            return null;
        }
        return JSON.toJSONString(latestMaterialList);
    }

}
