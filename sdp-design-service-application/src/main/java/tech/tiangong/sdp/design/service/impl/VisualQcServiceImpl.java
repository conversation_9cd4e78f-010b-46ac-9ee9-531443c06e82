package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.SpuIdentifyConverter;
import tech.tiangong.sdp.design.converter.visual.VisualQcConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.SpuIdentifySourceEnum;
import tech.tiangong.sdp.design.enums.visual.*;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.query.visual.VisualQcQuery;
import tech.tiangong.sdp.design.vo.req.identify.SpuIdentifyAddReq;
import tech.tiangong.sdp.design.vo.req.visual.BatchSubmitTryOnReq;
import tech.tiangong.sdp.design.vo.req.visual.BatchSubmitVisualQcReq;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualQcReq;
import tech.tiangong.sdp.design.vo.req.visual.VisualQcRecordReq;
import tech.tiangong.sdp.design.vo.resp.visual.*;
import tech.tiangong.sdp.utils.Bool;
import tech.tiangong.sdp.utils.StreamUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视觉任务质检服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VisualQcServiceImpl implements VisualQcService {
    private final VisualQcRepository visualQcRepository;
    private final VisualQcConverter visualQcConverter;

    private final VisualTaskRepository visualTaskRepository;
    private final VisualTaskDetailRepository visualTaskDetailRepository;
    private final VisualTaskTryOnRepository visualTaskTryOnRepository;
    private final VisualTaskOnShelfRepository visualTaskOnShelfRepository;
    private final VisualSpuRepository visualSpuRepository;
    private final VisualImagePackageService visualImagePackageService;
    private final VisualTaskHelper visualTaskHelper;

    private final VisualTaskNodeStateService visualTaskNodeStateService;
    private final VisualDemandRepository visualDemandRepository;
    private final VisualQcAiBoxImageRepository visualQcAiBoxImageRepository;
    private final VisualQcAiBoxLogRepository visualQcAiBoxLogRepository;
    private final SpuIdentifyRepository spuIdentifyRepository;
    private final SpuIdentifyService spuIdentifyService;

    @Lazy
    @Resource
    private VisualTaskTryOnLogService visualTaskTryOnLogService;


    @Override
    public PageRespVo<VisualTaskQcListVo> page(VisualQcQuery query) {
        visualTaskHelper.handleBaseQuery(query);
        //如果是查返修的任务，则只返回不被取消的任务
        if(query.getQcResult()!=null && Objects.equals(query.getQcResult(),VisualTaskQcResultEnum.NO_PASS.getCode())){
            query.setShowCancel(false);
        }
        IPage<VisualTaskQcListVo> page = visualQcRepository.queryByPage(query);
        List<VisualTaskQcListVo> list = visualQcConverter.trans2VisualTaskQcListVo(page.getRecords(), query.getProcessNode(), query.getAiBoxTaskStateList());

        //款式识别信息
        this.setSpuIdentify(list);
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), list);
    }

    private void setSpuIdentify(List<VisualTaskQcListVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<String> taskIdList = list.stream()
                .filter(item -> Objects.nonNull(item.getTaskId()))
                .map(item -> String.valueOf(item.getTaskId())).toList();

        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizCodeSource(taskIdList, SpuIdentifySourceEnum.VISUAL_QC.getCode(), null);
        Map<String, List<SpuIdentify>> spuIdentifyGroupMap = StreamUtil.groupingBy(spuIdentifyList, SpuIdentify::getBizCode);

        list.forEach(item -> {
            item.setMuseIdentifyInfo(SpuIdentifyConverter.buildIdentifyInfo(String.valueOf(item.getTaskId()), spuIdentifyGroupMap));
        });
    }

    @Override
    public VisualTaskQcListCountVo countState(VisualQcQuery query) {
        visualTaskHelper.handleBaseQuery(query);
        VisualTaskQcListCountVo vo = new VisualTaskQcListCountVo();
        vo.setTaskStepNodeStateCount(visualQcRepository.getVisualTaskStepNodeStateCountVo(query));
        vo.setTaskStateCount(visualQcRepository.getVisualQcTaskStateCountVo(query));
        vo.setQcResultCountVos(visualQcRepository.getVisualQcResultCountVo(query));

        //特殊处理，统计返修的任务，只统计不被取消的任务
        query.setShowCancel(false);
        List<VisualTaskQcResultCountVo> notContainCanceledVisualTaskQcResultCount = visualQcRepository.getVisualQcResultCountVo(query);
        if(CollectionUtil.isNotEmpty(notContainCanceledVisualTaskQcResultCount)){
            VisualTaskQcResultCountVo noPassCount = notContainCanceledVisualTaskQcResultCount.stream()
                    .filter(v->v.getQcResultCode()!=null
                            && v.getQcResultCode().equals(VisualTaskQcResultEnum.NO_PASS.getCode()))
                    .findFirst().orElse(null);
            if(noPassCount!=null){
                //替换返修的数量
                if(CollectionUtil.isNotEmpty(vo.getQcResultCountVos())){
                    if(vo.getQcResultCountVos().stream().anyMatch(v->v.getQcResultCode()!=null && v.getQcResultCode().equals(VisualTaskQcResultEnum.NO_PASS.getCode()))) {
                        vo.getQcResultCountVos().forEach(v -> {
                            if (v.getQcResultCode().equals(VisualTaskQcResultEnum.NO_PASS.getCode())) {
                                v.setCount(noPassCount.getCount());
                            }
                        });
                    }else{
                        vo.getQcResultCountVos().add(noPassCount);
                    }
                }else{
                    vo.setQcResultCountVos(Arrays.asList(noPassCount));
                }
            }
            //如果非取消状态的任务，没有返修，则不返回返修数量
            else if(CollectionUtil.isNotEmpty(vo.getQcResultCountVos())){
                vo.setQcResultCountVos(vo.getQcResultCountVos().stream().filter(v->v.getQcResultCode()!=null && !v.getQcResultCode().equals(VisualTaskQcResultEnum.NO_PASS.getCode())).toList());
            }
        }
        return vo;
    }

    @Override
    public VisualQcDetailVo getLatestQcTypeByTaskId(Long taskId){
        VisualTask visualTask = visualTaskRepository.getById(taskId);
        Assert.isTrue(visualTask!=null,"任务不存在");
        VisualTaskDetail visualTaskDetail = visualTaskDetailRepository.getByTaskId(taskId);
        VisualSpu visualSpu = visualSpuRepository.getByStyleCode(visualTask.getStyleCode());
        VisualDemand visualDemand = visualDemandRepository.getById(visualTask.getLatestDemandId());
        VisualTaskTryOn visualTaskTryOn = null;
        VisualTaskOnShelf visualTaskOnShelf = null;
        //将最新的tryOn图处理版本号返回给前端，用于提交质检时后台做校验
        if(visualTaskDetail.getLatestTryOnDetailId()!=null){
            visualTaskTryOn = visualTaskTryOnRepository.getById(visualTaskDetail.getLatestTryOnDetailId());
        }
        //将最新的修图处理版本号返回给前端，用于提交质检时后台做校验
        if(visualTaskDetail.getLatestOnShelfDetailId()!=null){
            visualTaskOnShelf = visualTaskOnShelfRepository.getById(visualTaskDetail.getLatestOnShelfDetailId());
        }

        //视觉质检
        VisualQc visualQc = visualQcRepository.getLatestByTaskId(taskId,VisualQcTypeEnum.VISUAL);
        //买手质检
        VisualQc buyerQc = visualQcRepository.getLatestByTaskId(taskId,VisualQcTypeEnum.BUYER);

        //上次返修的质检内容
        VisualQc noPassQc = visualQcRepository.latestQcResult(taskId,VisualTaskQcResultEnum.NO_PASS);

        //款式识别信息
        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizCodeSource(Collections.singleton(String.valueOf(taskId)), SpuIdentifySourceEnum.VISUAL_QC.getCode(), null);
        Map<String, List<SpuIdentify>> spuIdentifyGroupMap = StreamUtil.groupingBy(spuIdentifyList, SpuIdentify::getBizCode);

        //如果有未处理的视觉质检，优先回显未处理的质检任务
        if(visualQc!=null && VisualTaskQcStateEnum.WAITING_QC.getCode().equals(visualQc.getQcState())){
            List<VisualQcAiBoxImage> visualQcAiBoxImageList = visualQcAiBoxImageRepository.listByQcRecordId(visualQc.getQcRecordId());
            return visualQcConverter.tran2VisualQcDetailVo(visualQc,noPassQc,visualTask,visualSpu,visualDemand,visualTaskTryOn,visualTaskOnShelf, visualQcAiBoxImageList, spuIdentifyGroupMap);
        }

        //其他情况按质检最流程，优先回显最后环节内容（不管是否已执行质检）
        if(buyerQc!=null){
            List<VisualQcAiBoxImage> visualQcAiBoxImageList = visualQcAiBoxImageRepository.listByQcRecordId(buyerQc.getQcRecordId());
            return visualQcConverter.tran2VisualQcDetailVo(buyerQc,noPassQc,visualTask,visualSpu,visualDemand,visualTaskTryOn,visualTaskOnShelf, visualQcAiBoxImageList, spuIdentifyGroupMap);
        }else{
            List<VisualQcAiBoxImage> visualQcAiBoxImageList = visualQcAiBoxImageRepository.listByQcRecordId(visualQc.getQcRecordId());
            return visualQcConverter.tran2VisualQcDetailVo(visualQc,noPassQc,visualTask,visualSpu,visualDemand,visualTaskTryOn,visualTaskOnShelf, visualQcAiBoxImageList, spuIdentifyGroupMap);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean saveVisualQc(SaveVisualQcReq req) {
        VisualQc visualQc = visualQcRepository.getById(req.getQcRecordId());
        Assert.isTrue(visualQc!=null,"当前质检任务不存在");
        Assert.isTrue(!VisualTaskQcResultEnum.PASS.getCode().equals(visualQc.getQcResult()),"当前质检任务已通过，不能再提交");

        VisualTask visualTask = visualTaskRepository.getById(visualQc.getTaskId());
        Assert.isTrue(visualTask!=null,"任务不存在");
        Assert.isTrue(!VisualTaskStateEnum.FINISH.getCode().equals(visualTask.getState())
                && Bool.NO.getCode()==visualTask.getIsCancel(),
                "任务当前状态不能质检");
        boolean noPass = req.getQcRecords().stream()
                .anyMatch(r -> VisualTaskQcResultEnum.NO_PASS.getCode().equals(r.getQcResult()));
        if (noPass && Objects.equals(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.getCode(), visualTask.getProcessType())) {
            SdpDesignException.notNull(req.getRejectType(), "打回环节不能为空!");
        }
        VisualTaskDetail visualTaskDetail = visualTaskDetailRepository.getByTaskId(visualQc.getTaskId());
        VisualSpu visualSpu = visualSpuRepository.getByStyleCode(visualTask.getStyleCode());
        VisualDemand visualDemand = visualDemandRepository.getById(visualTask.getLatestDemandId());
        VisualTaskTryOn visualTaskTryOn = null;
        VisualTaskOnShelf visualTaskOnShelf = null;
        //判断tryOn图处理是否被更新
        if(visualTaskDetail.getLatestTryOnDetailId()!=null){
            visualTaskTryOn = visualTaskTryOnRepository.getById(visualTaskDetail.getLatestTryOnDetailId());
            Assert.isTrue(visualTaskTryOn.getVersionNum().equals(req.getTryOnHandleVersion()),VisualErrorCodeEnum.ERROR_98100.getCode());
        }
        //判断修图处理是否被更新
        if(visualTaskDetail.getLatestOnShelfDetailId()!=null){
            visualTaskOnShelf = visualTaskOnShelfRepository.getById(visualTaskDetail.getLatestOnShelfDetailId());
            Assert.isTrue(visualTaskOnShelf.getVersionNum().equals(req.getOnShelfHandleVersion()),VisualErrorCodeEnum.ERROR_98101.getCode());
        }
        //判断需求是否被更新
        if(visualDemand!=null){
            Assert.isTrue(visualDemand.getVersionNum().equals(req.getDemandVersion()),VisualErrorCodeEnum.ERROR_98102.getCode());
        }
        req.getQcRecords().forEach(qcRecord -> {
            ImageFile imageFile = qcRecord.getImageFile();
            if(imageFile!=null){
                Assert.isTrue(visualTaskHelper.isValidFileName(imageFile.getOrgImgName()),"文件名不能包含特殊字符"+imageFile.getOrgImgName());
            }
        });
        doSubmitVisualQc(visualSpu,visualQc,
                req.getQcRecords(),
                req.getRejectType(),
                visualTaskTryOn,
                visualTaskOnShelf,
                visualTask,
                visualTaskDetail,false);

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchSubmitVisualQc(BatchSubmitVisualQcReq req){
        req.getTaskIds().forEach(taskId->{
            VisualQcDetailVo visualQcDetailVo = getLatestQcTypeByTaskId(taskId);
            Assert.isTrue(visualQcDetailVo!=null,"当前质检任务不存在");
            VisualQc visualQc = visualQcRepository.getById(visualQcDetailVo.getQcRecordId());
            Assert.isTrue(visualQc!=null,"当前质检任务不存在");
            Assert.isTrue(!VisualTaskQcResultEnum.PASS.getCode().equals(visualQc.getQcResult()),"当前质检任务已通过，不能再提交");

            VisualTask visualTask = visualTaskRepository.getById(visualQc.getTaskId());
            Assert.isTrue(visualTask!=null,"任务不存在");
            Assert.isTrue(!VisualTaskStateEnum.FINISH.getCode().equals(visualTask.getState())
                            && Bool.NO.getCode()==visualTask.getIsCancel(),
                    "任务状态为"+VisualTaskStateEnum.findByCode(visualTask.getState()).getDesc()+"不能再质检");
            VisualTaskDetail visualTaskDetail = visualTaskDetailRepository.getByTaskId(visualQc.getTaskId());
            VisualSpu visualSpu = visualSpuRepository.getByStyleCode(visualTask.getStyleCode());
            VisualTaskTryOn visualTaskTryOn = null;
            VisualTaskOnShelf visualTaskOnShelf = null;
            //判断tryOn图处理是否被更新
            if(visualTaskDetail.getLatestTryOnDetailId()!=null){
                visualTaskTryOn = visualTaskTryOnRepository.getById(visualTaskDetail.getLatestTryOnDetailId());
            }
            //判断修图处理是否被更新
            if(visualTaskDetail.getLatestOnShelfDetailId()!=null){
                visualTaskOnShelf = visualTaskOnShelfRepository.getById(visualTaskDetail.getLatestOnShelfDetailId());
            }
            List<VisualQcRecordReq> qcRecordReqs = visualQcDetailVo.getQcRecords().stream().map(v-> {
                VisualQcRecordReq qcRecordReq = new VisualQcRecordReq();
                qcRecordReq.setImageFile(v.getImageFile());
                qcRecordReq.setQcResult(req.getQcResult());
                return qcRecordReq;
            }).collect(Collectors.toList());

            qcRecordReqs.forEach(qcRecord -> {
                ImageFile imageFile = qcRecord.getImageFile();
                if(imageFile!=null){
                    Assert.isTrue(visualTaskHelper.isValidFileName(imageFile.getOrgImgName()),"文件名不能包含特殊字符"+imageFile.getOrgImgName());
                }
            });

            doSubmitVisualQc(visualSpu,visualQc,
                    qcRecordReqs, null,
                    visualTaskTryOn,
                    visualTaskOnShelf,
                    visualTask,visualTaskDetail,true);
        });
        return true;
    }

    @Override
    public Boolean batchSubmitTryOn(List<BatchSubmitTryOnReq> reqList) {
        Assert.notEmpty(reqList, "质检结果 can not be empty");
        for (BatchSubmitTryOnReq req : reqList) {
            visualTaskTryOnLogService.doSubmitTryOnQC(req);
        }
        return true;
    }

    @Override
    public void saveOrUpdateSpuIdentify(List<VisualQc> qcList) {
        log.info("=== 质检图片识别发起 ===");
        if (CollUtil.isEmpty(qcList)) {
            return;
        }
        //存在图片的质检
        List<VisualQc> qcRecordList = qcList.stream().filter(item -> StrUtil.isNotBlank(item.getQcRecord())).toList();
        if (CollUtil.isEmpty(qcRecordList)) {
            log.info("=== 无质检图片, 不处理 ===");
            return;
        }
        //根据taskId查询识别任务
        List<String> taskIdList = qcRecordList.stream().map(item -> String.valueOf(item.getTaskId())).toList();
        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizCodeSource(taskIdList, SpuIdentifySourceEnum.VISUAL_QC.getCode(), null);
        Map<String, List<SpuIdentify>> spuIdentifyGroupMap = StreamUtil.groupingBy(spuIdentifyList, SpuIdentify::getBizCode);

        //判断识别记录是否存在
        List<SpuIdentifyAddReq> identifyAddList = new ArrayList<>();
        List<SpuIdentify> updateIdentifyList = new ArrayList<>();
        qcRecordList.forEach(item -> {
            //处理需要新增或更新的识别记录
            this.classifyIdentifySaveUpdate(item, spuIdentifyGroupMap, identifyAddList, updateIdentifyList);
        });

        if (CollUtil.isNotEmpty(identifyAddList)) {
            spuIdentifyService.batchAddAsync(identifyAddList);
        }
        if (CollUtil.isNotEmpty(updateIdentifyList)) {
            spuIdentifyRepository.updateBatchById(updateIdentifyList);
        }
    }
    private void classifyIdentifySaveUpdate(VisualQc item, Map<String,
            List<SpuIdentify>> spuIdentifyGroupMap, List<SpuIdentifyAddReq> identifyAddList,
                                            List<SpuIdentify> updateIdentifyList) {
        //301图存在时才发起识别
        List<VisualQcRecordVo> recordVoList = JSONObject.parseArray(item.getQcRecord(), VisualQcRecordVo.class);
        if (CollUtil.isEmpty(recordVoList)) {
            return;
        }
        //过滤出recordVoList中各元素imageFile中orgImgName中包含301的元素, 例如 112-301.png, PG122-301.jpg, 301.png
        VisualQcRecordVo mainImageRecord = recordVoList.stream()
                .filter(recordVo -> recordVo.getImageFile().getOrgImgName().contains("-301.")
                        || recordVo.getImageFile().getOrgImgName().startsWith("301."))
                .findFirst().orElse(null);
        if (Objects.isNull(mainImageRecord)) {
            log.info("=== 质检图片不存在301图, 不处理, qcRecordId:{} ===", item.getQcRecordId());
            return;
        }

        String qcMainImageUrl = mainImageRecord.getImageFile().getOssImageUrl();

        List<SpuIdentify> identifyList = spuIdentifyGroupMap.get(item.getTaskId()+"");

        //当前视觉任务还没有识别记录-发起识别
        if (CollUtil.isEmpty(identifyList) || Objects.isNull(identifyList.getFirst())) {
            identifyAddList.add(this.buildQcIdentifyReq(item, qcMainImageUrl));
            return;
        }

        //当前视觉任务已有识别记录, 但301图片更新-发起识别; 旧记录latest更新为0
        String identifyUrl = identifyList.getFirst().getIdentifyUrl();
        if (!Objects.equals(identifyUrl, mainImageRecord.getImageFile().getOssImageUrl())) {
            //新的识别记录
            identifyAddList.add(this.buildQcIdentifyReq(item, qcMainImageUrl));
            // 旧记录latest更新为0
            updateIdentifyList.addAll(identifyList.stream()
                    .map(identify -> SpuIdentify.builder().identifyId(identify.getIdentifyId()).latestState(Bool.NO.getCode()).build())
                    .toList());
            log.info("=== 视觉301图片更新 发起款式识别 qcId:{} ===", item.getQcRecordId());
        }
    }

    private SpuIdentifyAddReq buildQcIdentifyReq(VisualQc item, String qcMainImageUrl) {
        return SpuIdentifyAddReq.builder()
                .sourceType(SpuIdentifySourceEnum.VISUAL_QC.getCode())
                .bizId(item.getQcRecordId()).bizCode(String.valueOf(item.getTaskId()))
                .identifyUrl(qcMainImageUrl)
                .build();
    }

    private void doSubmitVisualQc(VisualSpu visualSpu,
                                  VisualQc visualQc,
                                  List<VisualQcRecordReq> qcRecords,
                                  Integer rejectType,
                                  VisualTaskTryOn visualTaskTryOn,
                                  VisualTaskOnShelf visualTaskOnShelf,
                                  VisualTask visualTask,
                                  VisualTaskDetail visualTaskDetail,
                                  Boolean isBatchSubmit){
        //记录提交内容
        List<VisualQcRecord> visualQcRecords = qcRecords.stream().map(qcRecord -> {
            VisualQcRecord visualQcRecord = new VisualQcRecord();
            visualQcRecord.setQcResult(qcRecord.getQcResult());
            visualQcRecord.setRemark(qcRecord.getRemark());
            visualQcRecord.setImageFile(qcRecord.getImageFile());
            if(CollectionUtil.isNotEmpty(qcRecord.getRemarkImageList())){
                visualQcRecord.setRemarkImages(String.join(",", qcRecord.getRemarkImageList()));
            }
            return visualQcRecord;
        }).collect(Collectors.toList());

        String visualQcRecordsJson = JSONObject.toJSONString(visualQcRecords);
        //更新包图内容（分类图片）
        List<ImageFile> imageFiles = visualQcRecords.stream().map(VisualQcRecord::getImageFile).collect(Collectors.toList());
        OnShelfImagePackage onShelfImagePackage = visualTaskHelper.classifyImage(visualSpu,imageFiles);
        String onShelfImagePackageJson = JSONObject.toJSONString(onShelfImagePackage);

        //记录质检结果
        VisualTaskQcResultEnum result = VisualTaskQcResultEnum.WAITING_QC;
        if(visualQcRecords.stream().allMatch(r -> VisualTaskQcResultEnum.PASS.getCode().equals(r.getQcResult()))){
            result = VisualTaskQcResultEnum.PASS;
        }else if(visualQcRecords.stream().anyMatch(r -> VisualTaskQcResultEnum.NO_PASS.getCode().equals(r.getQcResult()))){
            result = VisualTaskQcResultEnum.NO_PASS;
        }

        UserContent userContent = UserContentHolder.get();
        VisualQcTypeEnum visualQcType = VisualQcTypeEnum.findByCode(visualQc.getQcType());
        //第一次提交
        if(StringUtils.isBlank(visualQc.getOnShelfImages())
                && VisualTaskQcStateEnum.WAITING_QC.getCode().equals(visualQc.getQcState())){
            visualQc.setQcRecord(visualQcRecordsJson);
            visualQc.setQcResult(result.getCode());
            visualQc.setRejectType(rejectType);
            //记录质检人
            visualQc.setInspectorId(userContent.getCurrentUserId());
            visualQc.setInspectorName(userContent.getCurrentUserName());
            //更新质检状态
            visualQc.setQcState(result.equals(VisualTaskQcResultEnum.WAITING_QC) ? VisualTaskQcStateEnum.WAITING_QC.getCode() : VisualTaskQcStateEnum.FINISH.getCode());
            visualQc.setOnShelfImages(onShelfImagePackageJson);

            visualQcRepository.updateById(visualQc);
        }else{
            VisualQc newVisualQc = new VisualQc();
            newVisualQc.setQcRecordId(IdPool.getId());
            newVisualQc.setTaskId(visualQc.getTaskId());
            newVisualQc.setIsLatest(1);
            newVisualQc.setQcType(visualQcType.getCode());
            newVisualQc.setVersionNum(visualQc.getVersionNum() + 1);
            newVisualQc.setTaskHandleTime(visualQc.getTaskHandleTime());
            newVisualQc.setTaskHandlerId(visualQc.getTaskHandlerId());
            newVisualQc.setTaskHandlerName(visualQc.getTaskHandlerName());
            newVisualQc.setQcRecord(visualQcRecordsJson);
            newVisualQc.setRejectType(rejectType);
            //记录质检人
            newVisualQc.setInspectorId(userContent.getCurrentUserId());
            newVisualQc.setInspectorName(userContent.getCurrentUserName());
            newVisualQc.setQcResult(result.getCode());
            //更新质检状态
            newVisualQc.setQcState(result.equals(VisualTaskQcResultEnum.WAITING_QC) ? VisualTaskQcStateEnum.WAITING_QC.getCode() : VisualTaskQcStateEnum.FINISH.getCode());

            newVisualQc.setOnShelfImages(onShelfImagePackageJson);
            visualQcRepository.save(newVisualQc);
            //过期掉当前版本
            visualQc.setIsLatest(0);
            visualQcRepository.updateById(visualQc);
        }

        //质检不通过，同步最终结果到视觉任务
        if(VisualTaskQcResultEnum.NO_PASS.equals(result)){
            visualTask.setQcResult(VisualTaskQcResultEnum.NO_PASS.getCode());
            visualTask.setIsRepair(Bool.YES.getCode());
            visualTaskRepository.updateById(visualTask);
            //同步结果到视觉子任务中,质检不通过子任务状态需要设为进行中继续处理
            updateHandleTaskByQcResult(visualTask, rejectType,
                    visualTaskDetail,
                    visualTaskTryOn,
                    visualTaskOnShelf,
                    onShelfImagePackage,
                    result);
        }
        //质检通过，同步最终结果到视觉任务，判断是否需要走买手质检流程，同步图包到SPU图包库
        else if(VisualTaskQcResultEnum.PASS.equals(result)){
            visualTask.setQcResult(VisualTaskQcResultEnum.PASS.getCode());
            visualTask.setIsRepair(Bool.NO.getCode());
            //同步结果到视觉子任务中
            updateHandleTaskByQcResult(visualTask, null,
                    visualTaskDetail,
                    visualTaskTryOn,
                    visualTaskOnShelf,
                    onShelfImagePackage,
                    result);

            //如果视觉任务通过质检，则判断是否需要继续走买手质检流程
            if(VisualQcTypeEnum.VISUAL.equals(visualQcType)){
                //供给方式是AIGC,并且是上新任务
                if(StringUtils.isNotBlank(visualSpu.getSupplyModeName())
                        && visualSpu.getSupplyModeName().equals("AIGC")
                        && VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode().equals(visualTask.getTaskType())){

                    List<VisualQcRecord> buyerQcVisualQcRecords = visualQcRecords.stream().map(v->{
                        VisualQcRecord visualQcRecord = new VisualQcRecord();
                        visualQcRecord.setImageFile(v.getImageFile());
                        visualQcRecord.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
                        return visualQcRecord;
                    }).collect(Collectors.toList());
                    //已有买手质检任务
                    VisualQc oldBuyerQc = visualQcRepository.getLatestByTaskId(visualTask.getTaskId(),VisualQcTypeEnum.BUYER);
                    //已有买手质检任务未提交过
                    if(oldBuyerQc!=null
                            && StringUtils.isBlank(oldBuyerQc.getOnShelfImages())
                            && VisualTaskQcStateEnum.WAITING_QC.getCode().equals(oldBuyerQc.getQcState())){
                        oldBuyerQc.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
                        oldBuyerQc.setQcRecord(JSONObject.toJSONString(buyerQcVisualQcRecords));
                        visualQcRepository.updateById(oldBuyerQc);
                    }else{
                        //新建买手质检任务
                        VisualQc newBuyerQc = new VisualQc();
                        newBuyerQc.setQcRecordId(IdPool.getId());
                        newBuyerQc.setTaskId(visualTask.getTaskId());
                        newBuyerQc.setQcState(VisualTaskQcStateEnum.WAITING_QC.getCode());
                        newBuyerQc.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
                        newBuyerQc.setQcType(VisualQcTypeEnum.BUYER.getCode());
                        newBuyerQc.setTaskHandleTime(visualQc.getTaskHandleTime());
                        newBuyerQc.setTaskHandlerName(visualQc.getTaskHandlerName());
                        newBuyerQc.setTaskHandlerId(visualQc.getTaskHandlerId());
                        newBuyerQc.setQcRecord(JSONObject.toJSONString(buyerQcVisualQcRecords));
                        newBuyerQc.setVersionNum(oldBuyerQc!=null ? oldBuyerQc.getVersionNum() + 1 : 1);
                        newBuyerQc.setIsLatest(1);
                        visualQcRepository.save(newBuyerQc);
                        if(oldBuyerQc!=null){
                            oldBuyerQc.setIsLatest(0);
                            visualQcRepository.updateById(oldBuyerQc);
                        }
                    }
                    //todo 主图变更或还没有款式识别记录则发起款式识别

                    //记录环节状态
                    visualTaskNodeStateService.saveVisualTaskNodeStepState(visualTask.getTaskId(), VisualTaskStepEnum.QC, VisualTaskNodeEnum.BUYER_QC,VisualTaskQcStateEnum.WAITING_QC.getCode());
                }else{
                    //视觉任务完成
                    visualTask.setState(VisualTaskStateEnum.FINISH.getCode());
                    visualTaskRepository.updateById(visualTask);
                    //同步结果到SPU图库
                    visualImagePackageService.saveImagePackage(onShelfImagePackage);

                    //视觉任务完成通知POP
                    visualTaskHelper.noticePopTaskState(visualTask.getStyleCode(), visualTask.getProcessCode(), VisualTaskStateEnum.findByCode(visualTask.getState()));
                }
            }else{
                //视觉任务完成
                visualTask.setState(VisualTaskStateEnum.FINISH.getCode());
                visualTaskRepository.updateById(visualTask);
                //同步结果到SPU图库
                visualImagePackageService.saveImagePackage(onShelfImagePackage);
                //视觉任务完成通知POP
                visualTaskHelper.noticePopTaskState(visualTask.getStyleCode(), visualTask.getProcessCode(), VisualTaskStateEnum.findByCode(visualTask.getState()));
            }
        }
        //记录环节状态
        VisualTaskNodeEnum visualTaskNode = VisualQcTypeEnum.VISUAL.equals(visualQcType) ? VisualTaskNodeEnum.VISUAL_QC : VisualTaskNodeEnum.BUYER_QC;
        visualTaskNodeStateService.saveVisualTaskNodeStepState(visualTask.getTaskId(), VisualTaskStepEnum.QC, visualTaskNode,VisualTaskQcStateEnum.FINISH.getCode());
        //记录日志
        String logContent = (isBatchSubmit ? "批量" : "")+ "提交" +visualQcType.getDesc();
        visualTaskHelper.addLog(visualTask.getStyleCode(),visualTask.getTaskId(),logContent);
    }

    private void updateHandleTaskByQcResult(VisualTask visualTask,
                                            Integer rejectType,
                                            VisualTaskDetail visualTaskDetail,
                                            VisualTaskTryOn visualTaskTryOn,
                                            VisualTaskOnShelf visualTaskOnShelf,
                                            OnShelfImagePackage onShelfImagePackage,
                                            VisualTaskQcResultEnum visualTaskQcResult){
        //同步图包结果到对应的任务
        //tryOn任务
        if(visualTaskTryOn!=null){
            OnShelfImagePackage taskOnShelfImagePackage = null;
            try{
                taskOnShelfImagePackage = JSONObject.parseObject(visualTaskTryOn.getOnShelfImages(),OnShelfImagePackage.class);
            }catch (Exception e){
                log.error("视觉tryOn任务上架图JSON转对象失败",e);
            }
            //如果是tryOn+修图, 但质检打回时选择了修图, 不处理tryOn环节;
            boolean noPassTryON = true;
            if (Objects.equals(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.getCode(), visualTask.getProcessType())
                    && Objects.equals(rejectType, VisualRejectTypeEnum.FIX_ON_SHELF.getCode())) {
                noPassTryON = false;
                log.debug("质检不通过选择修图不修改tryOn");
            }
            if(VisualTaskQcResultEnum.NO_PASS.equals(visualTaskQcResult) && noPassTryON){
                VisualTaskTryOn newVisualTaskTryOn = new VisualTaskTryOn();
                newVisualTaskTryOn.setTryOnDetailId(IdPool.getId());
                newVisualTaskTryOn.setTaskId(visualTaskTryOn.getTaskId());
                newVisualTaskTryOn.setTryOnHandlerId(visualTaskTryOn.getTryOnHandlerId());
                newVisualTaskTryOn.setTryOnHandlerName(visualTaskTryOn.getTryOnHandlerName());
                newVisualTaskTryOn.setTryOnImages(visualTaskTryOn.getTryOnImages());
                newVisualTaskTryOn.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
                //如果质检不通过，则任务需要继续进行
                newVisualTaskTryOn.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
                newVisualTaskTryOn.setIsLatest(1);
                newVisualTaskTryOn.setVersionNum(visualTaskTryOn.getVersionNum() + 1);
                visualTaskTryOnRepository.save(newVisualTaskTryOn);
                visualTaskTryOn.setIsLatest(0);
                visualTaskTryOnRepository.updateById(visualTaskTryOn);

                visualTaskDetail.setLatestTryOnDetailId(newVisualTaskTryOn.getTryOnDetailId());
                //记录环节状态
                visualTaskNodeStateService.saveVisualTaskNodeStepState(visualTaskTryOn.getTaskId(), VisualTaskStepEnum.HANDLE, VisualTaskNodeEnum.TRYON_HANDLE, newVisualTaskTryOn.getHandleState());
            }
            //如果通过，且内容有变更，且是仅tryOn则升级任务版本
            else if(hasChangeOnShelfImagePackage(taskOnShelfImagePackage,onShelfImagePackage)
                    && visualTask.getProcessType()!=null && VisualTaskProcessTypeEnum.TRY_ON.getCode().equals(visualTask.getProcessType())){
                VisualTaskTryOn newVisualTaskTryOn = new VisualTaskTryOn();
                newVisualTaskTryOn.setTryOnDetailId(IdPool.getId());
                newVisualTaskTryOn.setTaskId(visualTaskTryOn.getTaskId());
                newVisualTaskTryOn.setTryOnHandlerId(visualTaskTryOn.getTryOnHandlerId());
                newVisualTaskTryOn.setTryOnHandlerName(visualTaskTryOn.getTryOnHandlerName());
                newVisualTaskTryOn.setTryOnImages(visualTaskTryOn.getTryOnImages());
                newVisualTaskTryOn.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
                //状态不变
                newVisualTaskTryOn.setHandleState(visualTaskTryOn.getHandleState());
                newVisualTaskTryOn.setIsLatest(1);
                newVisualTaskTryOn.setVersionNum(visualTaskTryOn.getVersionNum() + 1);
                visualTaskTryOnRepository.save(newVisualTaskTryOn);
                visualTaskTryOn.setIsLatest(0);
                visualTaskTryOnRepository.updateById(visualTaskTryOn);

                visualTaskDetail.setLatestTryOnDetailId(newVisualTaskTryOn.getTryOnDetailId());
            }
        }
        //修图任务
        if(visualTaskOnShelf!=null){
            OnShelfImagePackage taskOnShelfImagePackage = null;
            try{
                taskOnShelfImagePackage = JSONObject.parseObject(visualTaskOnShelf.getOnShelfImages(),OnShelfImagePackage.class);
            }catch (Exception e){
                log.error("视觉修图任务上架图JSON转对象失败",e);
            }
            if(VisualTaskQcResultEnum.NO_PASS.equals(visualTaskQcResult)){
                VisualTaskOnShelf newVisualTaskOnShelf = new VisualTaskOnShelf();
                newVisualTaskOnShelf.setOnShelfDetailId(IdPool.getId());
                newVisualTaskOnShelf.setTaskId(visualTaskOnShelf.getTaskId());
                newVisualTaskOnShelf.setOnShelfHandlerId(visualTaskOnShelf.getOnShelfHandlerId());
                newVisualTaskOnShelf.setOnShelfHandlerName(visualTaskOnShelf.getOnShelfHandlerName());
                newVisualTaskOnShelf.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
                if(visualTask.getProcessType()!=null && VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.getCode().equals(visualTask.getProcessType())){
                    //如果质检不通过，则任务需要继续进行，如果是tryOn+修图则状态为待开始，因为需要等待tryOn重新提交后才可以编辑
                    newVisualTaskOnShelf.setHandleState(VisualTaskHandleStateEnum.WAITING_START.getCode());
                    //打回类型为tryOn(如果打回类型是修图, 继续进行)
                    if (Objects.equals(rejectType, VisualRejectTypeEnum.FIX_ON_SHELF.getCode())) {
                        newVisualTaskOnShelf.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
                    }
                }else{
                    //如果质检不通过，则任务需要继续进行
                    newVisualTaskOnShelf.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
                }
                newVisualTaskOnShelf.setIsLatest(1);
                newVisualTaskOnShelf.setVersionNum(visualTaskOnShelf.getVersionNum() + 1);
                visualTaskOnShelfRepository.save(newVisualTaskOnShelf);
                visualTaskOnShelf.setIsLatest(0);
                visualTaskOnShelfRepository.updateById(visualTaskOnShelf);

                visualTaskDetail.setLatestOnShelfDetailId(newVisualTaskOnShelf.getOnShelfDetailId());

                //记录环节状态
                visualTaskNodeStateService.saveVisualTaskNodeStepState(visualTaskOnShelf.getTaskId(), VisualTaskStepEnum.HANDLE, VisualTaskNodeEnum.ON_SHELF_HANDLE, newVisualTaskOnShelf.getHandleState());
            }
            //如果通过，且内容有变更，则升级任务版本
            else if(hasChangeOnShelfImagePackage(taskOnShelfImagePackage,onShelfImagePackage)){
                VisualTaskOnShelf newVisualTaskOnShelf = new VisualTaskOnShelf();
                newVisualTaskOnShelf.setOnShelfDetailId(IdPool.getId());
                newVisualTaskOnShelf.setTaskId(visualTaskOnShelf.getTaskId());
                newVisualTaskOnShelf.setOnShelfHandlerId(visualTaskOnShelf.getOnShelfHandlerId());
                newVisualTaskOnShelf.setOnShelfHandlerName(visualTaskOnShelf.getOnShelfHandlerName());
                newVisualTaskOnShelf.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
                //状态不变
                newVisualTaskOnShelf.setHandleState(visualTaskOnShelf.getHandleState());
                newVisualTaskOnShelf.setIsLatest(1);
                newVisualTaskOnShelf.setVersionNum(visualTaskOnShelf.getVersionNum() + 1);
                visualTaskOnShelfRepository.save(newVisualTaskOnShelf);
                visualTaskOnShelf.setIsLatest(0);
                visualTaskOnShelfRepository.updateById(visualTaskOnShelf);

                visualTaskDetail.setLatestOnShelfDetailId(newVisualTaskOnShelf.getOnShelfDetailId());
            }
        }
        visualTaskDetailRepository.updateById(visualTaskDetail);
    }

    /**
     * 判断质检结果是否对任务图片有改动
     * @param taskOnShelfImagePackage 任务图片
     * @param qcOnShelfImagePackage 质检图片
     * @return
     */
    private boolean hasChangeOnShelfImagePackage(OnShelfImagePackage taskOnShelfImagePackage,
            OnShelfImagePackage qcOnShelfImagePackage){
        log.info("判断质检结果是否对任务图片有改动，任务图对象:{}，质检图对象:{}",JSONObject.toJSONString(taskOnShelfImagePackage),JSONObject.toJSONString(qcOnShelfImagePackage));
        Set<String> taskSpuImages = new HashSet<>();
        //判断质检时是否有对结果进行变更，如果有则同步到对应的任务里
        Set<String> qcSpuImages = new HashSet<>();
        if(qcOnShelfImagePackage!=null && qcOnShelfImagePackage.getSpuImages()!=null){
            qcOnShelfImagePackage.getSpuImages().forEach(spuImage -> {
                spuImage.getImages().forEach(image -> {
                    qcSpuImages.add(spuImage.getImageType()+image.getOrgImgName()+image.getOssImageUrl());
                });
            });
        }
        Set<String> taskSkcImages = new HashSet<>();
        Set<String> qcSkcImages = new HashSet<>();
        if(qcOnShelfImagePackage!=null && qcOnShelfImagePackage.getSkcImages()!=null){
            qcOnShelfImagePackage.getSkcImages().forEach(skcImage -> {
                skcImage.getImages().forEach(image -> {
                    qcSkcImages.add(skcImage.getDesignCode()+skcImage.getColor()+image.getOrgImgName()+image.getOssImageUrl());
                });
            });
        }
        if(taskOnShelfImagePackage!=null){
            if(taskOnShelfImagePackage.getSpuImages()!=null){
                taskOnShelfImagePackage.getSpuImages().forEach(spuImage -> {
                    spuImage.getImages().forEach(image -> {
                        taskSpuImages.add(spuImage.getImageType()+image.getOrgImgName()+image.getOssImageUrl());
                    });
                });
            }
            if(taskOnShelfImagePackage.getSkcImages()!=null){
                taskOnShelfImagePackage.getSkcImages().forEach(skcImage -> {
                    skcImage.getImages().forEach(image -> {
                        taskSkcImages.add(skcImage.getDesignCode()+skcImage.getColor()+image.getOrgImgName()+image.getOssImageUrl());
                    });
                });
            }
        }
        //质检任务的SPU图，SKC图和任务内的SPU图，SKC图都没有不一样，则表示质检时没有对内容进行改动
        return !Sets.symmetricDifference(qcSpuImages,taskSpuImages).isEmpty() || !Sets.symmetricDifference(qcSkcImages,taskSkcImages).isEmpty();
    }
}
