package tech.tiangong.sdp.design.business.remark;

import cn.hutool.core.lang.Assert;
import cn.hutool.extra.spring.SpringUtil;
import tech.tiangong.sdp.design.entity.DesignRemarks;
import tech.tiangong.sdp.design.entity.PurchaseApplyFollow;
import tech.tiangong.sdp.design.entity.PurchasePrototypeInfo;
import tech.tiangong.sdp.design.repository.PurchaseApplyFollowRepository;
import tech.tiangong.sdp.design.repository.PurchasePrototypeInfoRepository;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;

/**
 * <AUTHOR>
 * @create 2021/8/19
 */
public class ComposeDesignRemarkPurchase implements ComposeDesignRemark{
    @Override
    public DesignRemarks compose(DesignRemarksReq req, DesignRemarks designRemarks) {
        PurchaseApplyFollowRepository purchaseApplyFollowRepository = SpringUtil.getBean(PurchaseApplyFollowRepository.class);
        PurchaseApplyFollow purchaseApplyFollow = purchaseApplyFollowRepository.getById(req.getBizId());
        Assert.notNull(purchaseApplyFollow,"不存在此采购申请单! ");
        designRemarks.setBizId(purchaseApplyFollow.getPurchaseApplyFollowId());
        designRemarks.setBizType(req.getBizType());
        designRemarks.setBizVersionNum(purchaseApplyFollow.getPurchaseCount());

        /*PrototypeHistoryRepository prototypeRepository = SpringUtil.getBean(PrototypeHistoryRepository.class);
        PrototypeHistory prototype = prototypeRepository.getById(purchaseApplyFollow.getPrototypeId());
        Assert.notNull(prototype,"设计款号不存在! ");
        designRemarks.setPrototypeId(prototype.getPrototypeId());
        designRemarks.setStyleCode(prototype.getStyleCode());
        designRemarks.setDesignCode(prototype.getDesignCode());
        designRemarks.setVersionNum(prototype.getVersionNum());*/

        PurchasePrototypeInfoRepository prototypeInfoRepository = SpringUtil.getBean(PurchasePrototypeInfoRepository.class);
        PurchasePrototypeInfo purchasePrototypeInfo = prototypeInfoRepository.getById(purchaseApplyFollow.getPrototypeId());
        Assert.notNull(purchasePrototypeInfo,"设计款号不存在! ");
        designRemarks.setStyleCode(purchasePrototypeInfo.getStyleCode());
        designRemarks.setDesignCode(purchasePrototypeInfo.getDesignCode());

        return designRemarks;
    }
}
