package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import team.aikero.murmuration.common.enums.SearchDimension;
import team.aikero.murmuration.common.vo.SearchInfo;
import team.aikero.murmuration.common.vo.SearchSimilarityVo;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.DesignDemand;
import tech.tiangong.sdp.design.entity.SpuIdentify;
import tech.tiangong.sdp.design.enums.SpuIdentifySourceEnum;
import tech.tiangong.sdp.design.enums.SpuIdentifyStateEnum;
import tech.tiangong.sdp.design.enums.SpuIdentifyTypeEnum;
import tech.tiangong.sdp.design.remote.MurmurationRemoteHelper;
import tech.tiangong.sdp.design.repository.SpuIdentifyRepository;
import tech.tiangong.sdp.design.service.SpuIdentifyService;
import tech.tiangong.sdp.design.vo.req.identify.SpuIdentifyAddReq;
import tech.tiangong.sdp.design.vo.req.identify.SpuIdentifyRetryReq;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleCreateResp;
import tech.tiangong.sdp.utils.Bool;
import tech.tiangong.sdp.utils.StreamUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 款式图片识别表(SpuIdentify)服务
 *
 * <AUTHOR>
 * @since 2025-08-07 17:30:30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpuIdentifyServiceImpl implements SpuIdentifyService {
    private final SpuIdentifyRepository spuIdentifyRepository;
    private final MurmurationRemoteHelper murmurationClientExternal;
    private final AsyncTaskExecutor asyncTaskExecutor;


    /**
     * 批量新增-异步识别
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddAsync(List<SpuIdentifyAddReq> addReqList) {
        log.info("=== 批量新增款式图片识别 ===");
        //1,新增识别发起记录(待发起)
        List<SpuIdentify> spuIdentifies = this.saveData(addReqList);
        if (spuIdentifies.isEmpty()) {
            log.info("=== 识别记录为空, 不处理 ===");
            return;
        }

        //2,发起侵权/同款识别 (异步执行)
        Set<String> identifyUrlSet = addReqList.stream()
                .map(SpuIdentifyAddReq::getIdentifyUrl)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                asyncTaskExecutor.execute(() -> handleIdentify(spuIdentifies, identifyUrlSet, "调用识别服务失败"));
            }
        });
    }

    /**
     * 处理识别逻辑
     */
    private void handleIdentify(List<SpuIdentify> spuIdentifies, Set<String> identifyUrlSet, String errorDesc) {
        log.info("=== 开始图片识别 ===");
        //所有记录标记为识别中
        List<SpuIdentify> dongingList = StreamUtil.convertList(spuIdentifies, item -> SpuIdentify.builder()
                .identifyId(item.getIdentifyId())
                .identifyState(SpuIdentifyStateEnum.DOING.getCode())
                .build());
        spuIdentifyRepository.updateBatchById(dongingList);

        Map<String, Map<SearchDimension, SearchInfo>> museSimilarityMap;
        try {
            //发起识别
            museSimilarityMap = murmurationClientExternal.batchSearchSimilarity(identifyUrlSet)
                    .stream()
                    .collect(Collectors.toMap(
                            SearchSimilarityVo::getUrl,
                            SearchSimilarityVo::getDimensions
                    ));
        } catch (Exception e) {
            log.error("{}: {}", errorDesc, e.getMessage(), e);
            // 如果调用失败，所有记录都标记为失败
            List<SpuIdentify> erroList = StreamUtil.convertList(spuIdentifies, item -> {
                String errorMsg = errorDesc;
                if (StrUtil.isNotBlank(e.getMessage())) {
                    errorMsg = (errorDesc + ": " + e.getMessage()).substring(0,
                            Math.min(1024, (errorDesc + ": " + e.getMessage()).length()));
                }
                return SpuIdentify.builder()
                        .identifyId(item.getIdentifyId())
                        .identifyState(SpuIdentifyStateEnum.FAIL.getCode())
                        .errorMsg(errorMsg)
                        .build();
            });
            spuIdentifyRepository.updateBatchById(erroList);
            return;
        }

        // 根据url匹配识别结果,更新识别信息到记录表
        if (CollUtil.isNotEmpty(museSimilarityMap)) {
            this.updateIdentifyResult(spuIdentifies, museSimilarityMap);
        }
    }

    @Override
    public void retry(SpuIdentifyRetryReq req) {
        //1, 根据req中identifyIdList, sourceType,identifyState 条件查询识别记录
        List<SpuIdentify> spuIdentifies = spuIdentifyRepository.retryQuery(
                req.getIdentifyIdList(), req.getSourceType(), req.getIdentifyState());
        if (spuIdentifies.isEmpty()) {
            return;
        }

        //2, 获取识别记录中的url
        Set<String> identifyUrlSet = spuIdentifies.stream()
                .map(SpuIdentify::getIdentifyUrl)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (identifyUrlSet.isEmpty()) {
            return;
        }
        this.handleIdentify(spuIdentifies, identifyUrlSet, "重试调用识别服务失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add4DesignDemand(DesignDemand designDemand, DesignStyleCreateResp spuSkcCreateResp) {
        SdpDesignException.notNull(designDemand, "灵感需求信息为空!");
        SdpDesignException.notNull(spuSkcCreateResp, "开款信息为空!");

        //获取灵感需求下的识别记录
        List<SpuIdentify> demandSpuIdentifyList = spuIdentifyRepository.listByBizIdSource(Collections.singleton(designDemand.getDesignDemandId()), SpuIdentifySourceEnum.DESIGN_DEMAND.getCode(), null);

        //历史数据为空, 不处理
        if (CollUtil.isEmpty(demandSpuIdentifyList) || Objects.isNull(demandSpuIdentifyList.getFirst())) {
            return;
        }

        //灵感需求的识别url作为开款spu的识别url
        String identifyUrl = demandSpuIdentifyList.getFirst().getIdentifyUrl();
        SpuIdentifyAddReq identifyAddInfo = SpuIdentifyAddReq.builder()
                .sourceType(SpuIdentifySourceEnum.DESIGN_STYLE.getCode())
                .bizId(spuSkcCreateResp.getDesignStyleId()).bizCode(spuSkcCreateResp.getStyleCode())
                .identifyUrl(identifyUrl)
                .build();

        //新增款式识别
        this.batchAddAsync(Collections.singletonList(identifyAddInfo));
    }

    private List<SpuIdentify> saveData(List<SpuIdentifyAddReq> addReqList) {
        boolean hasInvalidSourceType = addReqList.stream()
                .map(SpuIdentifyAddReq::getSourceType)
                .anyMatch(sourceType -> SpuIdentifySourceEnum.findByCode(sourceType) == null);

        if (hasInvalidSourceType) {
            throw new SdpDesignException("款式识别类型sourceType错误");
        }

        //根据bizId查询现有记录
        Set<Long> bizIdSet = StreamUtil.convertSet(addReqList, SpuIdentifyAddReq::getBizId);

        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizIdSource(bizIdSet, null, null);
        Map<Long, SpuIdentify> existingRecordsMap = StreamUtil.list2Map(spuIdentifyList, SpuIdentify::getBizId);

        //构建新增记录
        List<SpuIdentify> spuIdentifies = this.buildAddSpuIdentifyByType(addReqList, existingRecordsMap);

        //如果是设计款,现货款, 视觉质检, 根据bizCode更新旧最新记录为0
        List<Integer> sourceCodeList = List.of(SpuIdentifySourceEnum.DESIGN_STYLE.getCode(),
                SpuIdentifySourceEnum.SPOT_SPU.getCode(), SpuIdentifySourceEnum.VISUAL_QC.getCode());
        List<SpuIdentifyAddReq> updateReqList = addReqList.stream().filter(item -> sourceCodeList.contains(item.getSourceType())).toList();
        if (CollUtil.isNotEmpty(updateReqList)) {
            Set<String> bizCodeSet = StreamUtil.convertSet(updateReqList, SpuIdentifyAddReq::getBizCode);
            List<SpuIdentify> oldSpuIdentifyList = spuIdentifyRepository.listByBizCodeSource(bizCodeSet, null, null);
            if (CollUtil.isNotEmpty(oldSpuIdentifyList)) {
                List<SpuIdentify> updateList = StreamUtil.convertList(oldSpuIdentifyList,
                        item -> SpuIdentify.builder()
                                .identifyId(item.getIdentifyId()).latestState(Bool.NO.getCode())
                                .build());
                spuIdentifyRepository.updateBatchById(updateList);
            }
        }

        //新增记录入库
        if (CollUtil.isNotEmpty(spuIdentifies)) {
            spuIdentifyRepository.saveBatch(spuIdentifies);
        }
        return spuIdentifies;
    }

    private List<SpuIdentify> buildAddSpuIdentifyByType(List<SpuIdentifyAddReq> addReqList, Map<Long, SpuIdentify> existingRecordsMap) {
        List<SpuIdentify> spuIdentifies = new ArrayList<>();
        for (SpuIdentifyAddReq req : addReqList) {
            // 非 设计款,现货款, 如果已存在相同记录，则跳过创建
            if (existingRecordsMap.containsKey(req.getBizId())
                    && (!Objects.equals(req.getSourceType(), SpuIdentifySourceEnum.SPOT_SPU.getCode())
                    && !Objects.equals(req.getSourceType(), SpuIdentifySourceEnum.DESIGN_STYLE.getCode()))) {
                log.info("已存在相同记录, 跳过创建款式识别");
                continue;
            }

            if (req.getIdentifyType() == null) {
                // identifyType为null时生成同款/侵权识别记录
                spuIdentifies.add(SpuIdentify.builder()
                        .identifyId(IdPool.getId())
                        .sourceType(req.getSourceType()).bizCode(req.getBizCode()).bizId(req.getBizId())
                        .latestState(Bool.YES.getCode())
                        .identifyType(SpuIdentifyTypeEnum.SAME_STYLE.getCode())
                        .identifyState(SpuIdentifyStateEnum.WAIT.getCode())
                        .identifyUrl(req.getIdentifyUrl())
                        .build()
                );
                spuIdentifies.add(SpuIdentify.builder()
                        .identifyId(IdPool.getId())
                        .sourceType(req.getSourceType()).bizCode(req.getBizCode()).bizId(req.getBizId())
                        .latestState(Bool.YES.getCode())
                        .identifyType(SpuIdentifyTypeEnum.INFRINGEMENT.getCode())
                        .identifyState(SpuIdentifyStateEnum.WAIT.getCode())
                        .identifyUrl(req.getIdentifyUrl())
                        .build()
                );

            } else {
                // 根据identifyType创建对应记录
                spuIdentifies.add(SpuIdentify.builder()
                        .identifyId(IdPool.getId())
                        .sourceType(req.getSourceType()).bizCode(req.getBizCode()).bizId(req.getBizId())
                        .latestState(Bool.YES.getCode())
                        .identifyType(req.getIdentifyType())
                        .identifyState(SpuIdentifyStateEnum.WAIT.getCode())
                        .identifyUrl(req.getIdentifyUrl())
                        .build()
                );
            }
        }
        return spuIdentifies;
    }

    private void updateIdentifyResult(
            List<SpuIdentify> spuIdentifies,
            Map<String, Map<SearchDimension, SearchInfo>> museSimilarityMap) {

        List<SpuIdentify> successUpdates = new ArrayList<>();
        List<SpuIdentify> failedUpdates = new ArrayList<>();

        for (SpuIdentify spuIdentify : spuIdentifies) {
            String url = spuIdentify.getIdentifyUrl();
            SpuIdentify updateIdentify = SpuIdentify.builder().identifyId(spuIdentify.getIdentifyId()).build();
            if (StrUtil.isNotBlank(url) && museSimilarityMap.containsKey(url)) {
                Map<SearchDimension, SearchInfo> identifyMap = museSimilarityMap.get(url);

                // 根据identifyType设置不同的识别信息
                SearchInfo searchInfo = null;
                if (spuIdentify.getIdentifyType().equals(SpuIdentifyTypeEnum.SAME_STYLE.getCode())) {
                    searchInfo = identifyMap.get(SearchDimension.MUSE_SAME_STYLE);
                } else if (spuIdentify.getIdentifyType().equals(SpuIdentifyTypeEnum.INFRINGEMENT.getCode())) {
                    searchInfo = identifyMap.get(SearchDimension.INFRINGEMENT);
                }
                if (Objects.nonNull(searchInfo)) {
                    updateIdentify.setIdentifyLevel(searchInfo.getLevel());
                    // updateIdentify.setIdentifyScore(new BigDecimal(searchInfo.getSimilarity()+""));
                    updateIdentify.setIdentifyScore(searchInfo.getSimilarity());
                }

                // 只有当identifyLevel成功设置时才加入成功列表，否则加入失败列表
                if (Objects.nonNull(updateIdentify.getIdentifyLevel())) {
                    updateIdentify.setIdentifyState(SpuIdentifyStateEnum.SUCCESS.getCode());
                    successUpdates.add(updateIdentify);
                } else {
                    updateIdentify.setIdentifyState(SpuIdentifyStateEnum.FAIL.getCode());
                    updateIdentify.setErrorMsg("不支持的识别类型或无法获取识别结果");
                    failedUpdates.add(updateIdentify);
                }
            } else {
                // 识别调用失败
                updateIdentify.setIdentifyState(SpuIdentifyStateEnum.FAIL.getCode());
                updateIdentify.setErrorMsg("识别接口无返回");
                failedUpdates.add(updateIdentify);
            }
        }

        //4,批量更新识别结果
        if (CollUtil.isNotEmpty(successUpdates)) {
            spuIdentifyRepository.updateBatchById(successUpdates);
        }
        if (CollUtil.isNotEmpty(failedUpdates)) {
            spuIdentifyRepository.updateBatchById(failedUpdates);
        }
    }


}
