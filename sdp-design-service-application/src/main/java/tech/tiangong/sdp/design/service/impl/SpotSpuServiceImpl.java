package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.cache.commands.CacheCommands;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import team.aikero.admin.common.vo.DictVo;
import team.aikero.blade.core.exception.BusinessException;
import tech.tiangong.id.IdPool;
import tech.tiangong.pop.common.resp.ShopResp;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.EstimateCheckPriceVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignRedisConstants;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.converter.SpuIdentifyConverter;
import tech.tiangong.sdp.design.converter.spot.SpotConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.enums.spot.*;
import tech.tiangong.sdp.design.enums.visual.VisualErrorCodeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskTypeEnum;
import tech.tiangong.sdp.design.event.CancelSpotSpuEvent;
import tech.tiangong.sdp.design.excel.SpotSpuImportData;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.helper.TryOnHelper;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.remote.SampleClothesRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.dto.SpotSpuUploadFileDto;
import tech.tiangong.sdp.design.vo.dto.SpotSpuUploadPictureDto;
import tech.tiangong.sdp.design.vo.dto.spot.*;
import tech.tiangong.sdp.design.vo.dto.visual.BackgroundDTO;
import tech.tiangong.sdp.design.vo.dto.visual.ModelFaceDTO;
import tech.tiangong.sdp.design.vo.dto.visual.TryOnModelReferenceImageDTO;
import tech.tiangong.sdp.design.vo.query.spot.SpotSpuQuery;
import tech.tiangong.sdp.design.vo.req.identify.SpuIdentifyAddReq;
import tech.tiangong.sdp.design.vo.req.log.DesignLogSdpSaveReq;
import tech.tiangong.sdp.design.vo.req.mq.demand.ProductUpdateMqDto;
import tech.tiangong.sdp.design.vo.req.spot.*;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualDemandBySpuReq;
import tech.tiangong.sdp.design.vo.req.visual.SpuVisualDemandRecordSaveReq;
import tech.tiangong.sdp.design.vo.resp.prototype.VisualDemandInfoVo;
import tech.tiangong.sdp.design.vo.resp.spot.*;
import tech.tiangong.sdp.design.vo.resp.visual.SpuImageMaterial;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskListExtraVo;
import tech.tiangong.sdp.qy.converter.DictTreeConverter;
import tech.tiangong.sdp.utils.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (SpotSpu)服务
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpotSpuServiceImpl implements SpotSpuService {
    private final SpotSpuRepository spotSpuRepository;
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final SpotSpuSupplierService spotSpuSupplierService;
    private final SpotSkcService spotSkcService;
    private final SpotSkcRepository spotSkcRepository;
    private final SpotSkcDetailRepository spotSkcDetailRepository;
    private final DesignLogService designLogService;
    private final SpuIdentifyService spuIdentifyService;
    private final BusinessCodeGenerator businessCodeGenerator;
    private final SpotSpuSupplierRepository spotSpuSupplierRepository;
    private final SpuIdentifyRepository spuIdentifyRepository;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final PopProductHelper popProductHelper;
    private final TryOnHelper tryOnHelper;
    private final ApplicationContext applicationContext;
    private final SpotSpuTryOnConfigService spotSpuTryOnConfigService;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final CacheCommands cacheCommands;
    private final RedisTemplate redisTemplate;
    private final VisualSpuService visualSpuService;
    private final VisualDemandRepository visualDemandRepository;
    private final VisualTaskRepository visualTaskRepository;
    private final SpuVisualDemandRecordService spuVisualDemandRecordService;
    private final SpuVisualDemandRecordRepository spuVisualDemandRecordRepository;
    private final SpotSpuImportTaskRepository spotSpuImportTaskRepository;
    private final SpotSpuImportTaskDetailRepository spotSpuImportTaskDetailRepository;
    @Resource
    @Lazy
    private VisualDemandService visualDemandService;
    private final SpotSpuMurmurationService spotSpuMurmurationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SpotPickStyleAddResultVo> pickStyleAdd(PickStyleAddReq req) {

        //转为为之前请求得skc数组
        PickStyleInnerAddReq innerReq = SpotConverter.buildSpotPickStyleAddVo(req);
        List<PickStyleInnerAddReq.SkcInfo> skcInfoList = innerReq.getSkcInfoList();

        //解析构建选款创建参数
        List<SpotPickStyleCreateDto> createDtoList = this.buildSpotPickStyleCreateDtos(skcInfoList);

        List<String> supplierNameList = StreamUtil.convertListAndDistinct(skcInfoList, PickStyleInnerAddReq.SkcInfo::getSupplierName);
        List<String> supplierStyleList = StreamUtil.convertListAndDistinct(skcInfoList, PickStyleInnerAddReq.SkcInfo::getSupplierStyle);

        List<SpotSpuSupplier> oldSupplierList = spotSpuSupplierRepository.listBySupplierNameStyle(supplierNameList, supplierStyleList);
        Map<String, SpotSpuSupplier> oldSupplierMap = StreamUtil.list2Map(oldSupplierList, SpotSpuSupplier::getSupplierNameStyle);

        List<SpotPickStyleAddResultVo> addVoList = new ArrayList<>();

        Map<Long, List<SpotPickStyleCreateDto>> spuMap = StreamUtil.groupingBy(createDtoList, SpotPickStyleCreateDto::getPickBizSpuId);

        for (Map.Entry<Long, List<SpotPickStyleCreateDto>> entry : spuMap.entrySet()) {
            List<SpotPickStyleCreateDto> list = entry.getValue();
            for (int i = 0; i < list.size(); i++) {
                SpotPickStyleCreateDto skcReq = list.get(i);
                boolean isLast = i == list.size() - 1;
                SpotSkc spotSkc = spotSkcRepository.ByStyleBizId(skcReq.getPickBizSkcId());
                if (Objects.nonNull(spotSkc) && isLast) {
                    SpotPickStyleAddResultVo addVo = new SpotPickStyleAddResultVo();
                    addVo.setPickBizId(skcReq.getPickBizSpuId());
                    addVo.setStyleCode(spotSkc.getStyleCode());
                    addVo.setSpotSkcId(spotSkc.getSpotSkcId());
                    addVo.setDesignCode(spotSkc.getDesignCode());
                    addVo.setSuccessful(true);
                    addVo.setMessage("成功");
                    addVoList.add(addVo);
                } else {
                    //供应商名称+款号加锁
                    String pickStyleKey = DesignRedisConstants.getPickStyleKey(skcReq.getSupplierNameStyle());
                    Lock lock = cacheCommands.getDistributedMutexLock(pickStyleKey);
                    boolean isLock = false;
                    try {
                        isLock = lock.tryLock(3, TimeUnit.SECONDS);
                        SdpDesignException.isTrue(isLock, "现货款创建请求频繁，请稍后再试");

                        this.handlePickStyleAdd(skcReq, oldSupplierMap, addVoList, isLast);
                    } catch (Exception e) {
                        log.info("=== 选款创建异常,msg:{}; skcReq:{} ====", e.getMessage(), JSONObject.toJSONString(skcReq));
                        SpotPickStyleAddResultVo addVo = new SpotPickStyleAddResultVo();
                        addVo.setPickBizId(skcReq.getPickBizSpuId());
                        addVo.setSuccessful(false);
                        addVo.setMessage(e.getMessage());
                        addVoList.add(addVo);
                        // 🔥 错误、立即释放锁
                    } finally {
                        if (isLock) {
                            this.releaseLockSafely(lock);
                        }
                    }
                }
            }


        }
        log.info("=== 选款创建, addVoList:{} ====", JSONObject.toJSONString(addVoList));

        TransactionUtil.afterCommit(()->{
            // 处理货通商品AI任务提交
            spotSpuMurmurationService.submitMurmurationTasksForCommunicationProducts(addVoList);
        });

        return addVoList;
    }

    private void releaseLockSafely(Lock lock) {
        try {
            lock.unlock();
        } catch (Exception e) {
            log.warn("释放锁异常: {}", e.getMessage());
        }
    }

    private List<SpotPickStyleCreateDto> buildSpotPickStyleCreateDtos(List<PickStyleInnerAddReq.SkcInfo> skcInfoList) {
        //字典查询
        Map<String, DictVo> dictValueMap = this.getPickStyleDictMap();
        //颜色 CLOTHING_COLOR
        DictVo colorDictVo = dictValueMap.get(DictConstant.CLOTHING_COLOR);
        Map<String, tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo> colorInfoVoMap = DictTreeConverter.convertColorInfoMap(colorDictVo);
        //品类 CLOTHING_CATEGORY
        DictVo categoryDictVo = dictValueMap.get(DictConstant.CLOTHING_CATEGORY);
        Map<String, String> categoryMap = DictTreeConverter.convertToMap(categoryDictVo, "-");
        //末级品类
        Map<String, SpotCategoryDto> endCategoryMap = this.getEndCategoryMap(categoryMap);
        //风格 CLOTHING_CATEGORY
        DictVo styleDictVo = dictValueMap.get(DictConstant.JV_STYLE);
        Map<String, String> styleMap = DictTreeConverter.convertToMap(styleDictVo, "-");
        // 查询店铺平台信息
        List<ShopResp> shopList = popProductHelper.listShop();
        Map<String, ShopResp> shopNameMap = StreamUtil.list2Map(shopList, ShopResp::getShopName);

        List<SpotPickStyleCreateDto> createDtoList = skcInfoList.stream().map(item -> {
            SdpDesignException.notBlank(item.getSupplierName(), "供应商名称不能为空");
            SdpDesignException.notBlank(item.getSupplierStyle(), "供应商款号不能为空");

            SpotPickStyleCreateDto createDto = new SpotPickStyleCreateDto();
            BeanUtils.copyProperties(item, createDto);
            //字典映射
            this.mapDictCode(item, dictValueMap, createDto, categoryMap, colorInfoVoMap, styleMap, endCategoryMap);
            //尺码处理: S/M/L => S,M,L;  如果上游推送尺码为空，则默认尺码为XS-XL；
            if (StrUtil.isNotBlank(item.getSampleSize())) {
                createDto.setSampleSize(item.getSampleSize().replaceAll("\\s+", "").replace(StrUtil.SLASH, StrUtil.COMMA));
            }

            //原始尺码处理: S/M/L => S,M,L;
            if (StrUtil.isNotBlank(item.getOriginalSampleSize())) {
                createDto.setOriginalSampleSize(item.getOriginalSampleSize().replaceAll("\\s+", "").replace(StrUtil.SLASH, StrUtil.COMMA));
            }

            //店铺处理
            ShopResp shopResp = shopNameMap.get(item.getStoreName());
            if (Objects.nonNull(shopResp)) {
                createDto.setStoreId(shopResp.getShopId());
            }
            return createDto;
        }).toList();
        return createDtoList;
    }

    private Map<String, SpotCategoryDto> getEndCategoryMap(Map<String, String> categoryMap) {
        Map<String, SpotCategoryDto> endCategoryMap = new HashMap<>();
        categoryMap.forEach((name, code) -> {
            String[] split = name.split("-");
            String endCategory = split[split.length - 1];
            endCategoryMap.put(endCategory, SpotCategoryDto.builder().category(code).categoryName(name).build());
        });
        return endCategoryMap;
    }

    private Map<String, DictVo> getPickStyleDictMap() {
        List<String> dictCodeList = Arrays.asList(DictConstant.SUPPLY_MODE, DictConstant.STOCK_GOODS_TYPE,
                DictConstant.CLOTHING_CATEGORY, DictConstant.CLOTHING_COLOR, DictConstant.TRAY_TYPE,
                DictConstant.PRODUCT_TYPE, DictConstant.JV_SCENE, DictConstant.JV_STYLE,
                DictConstant.PLM_CLOTHING_BAND, DictConstant.INSPIRATION_BRAND, DictConstant.INSPIRATION_IMAGE_SOURCE,
                DictConstant.PLANNING_SOURCE,DictConstant.STANDARD_TIANGONG_CODE, DictConstant.NATIONAL
        );
        List<DictVo> dictResp = dictValueRemoteHelper.listByDictCodes(dictCodeList);
        return dictResp.stream().collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));
    }


    private void mapDictCode(PickStyleInnerAddReq.SkcInfo item,
                             Map<String, DictVo> dictValueMap,
                             SpotPickStyleCreateDto createDto,
                             Map<String, String> categoryMap,
                             Map<String, tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo> colorInfoVoMap,
                             Map<String, String> styleMap, Map<String, SpotCategoryDto> endCategoryMap) {
        //尺码组: 默认字母码
        createDto.setSizeStandard(DictConstant.STANDARD_TIANGONG_NAME);
        createDto.setSizeStandardCode(DictConstant.STANDARD_TIANGONG_CODE);
        //织造方式：默认「针/梭织」 01
        createDto.setWeaveMode(DictConstant.WEAVE_MODE_01_NAME);
        createDto.setWeaveModeCode(DictConstant.WEAVE_MODE_01_CODE);
        //品质等级：默认「准二线」 plm_quality_level_zex
        createDto.setQualityLevel(DictConstant.QUALITY_LEVEL_ZEX_NAME);
        createDto.setQualityLevelCode(DictConstant.QUALITY_LEVEL_ZEX_CODE);
        //供给方式
        String supplyModeCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.SUPPLY_MODE), PickStyleInnerAddReq.SkcInfo::getSupplyModeName);
        createDto.setSupplyModeCode(supplyModeCode);
        //现货类型
        String spotTypeCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.STOCK_GOODS_TYPE), PickStyleInnerAddReq.SkcInfo::getSpotTypeName);
        createDto.setSpotTypeCode(spotTypeCode);
        //货盘类型
        String palletTypeCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.TRAY_TYPE), PickStyleInnerAddReq.SkcInfo::getPalletTypeName);
        createDto.setPalletTypeCode(palletTypeCode);
        //国家站点
        String countrySiteCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.NATIONAL), PickStyleInnerAddReq.SkcInfo::getCountrySiteName);
        createDto.setCountrySiteCode(countrySiteCode);
        //商品类型
        String productTypeCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.PRODUCT_TYPE), PickStyleInnerAddReq.SkcInfo::getProductType);
        createDto.setProductTypeCode(productTypeCode);
        //场景
        String sceneCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.JV_SCENE), PickStyleInnerAddReq.SkcInfo::getSceneName);
        createDto.setSceneCode(sceneCode);
        //风格
        String clothingStyleCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.JV_STYLE), PickStyleInnerAddReq.SkcInfo::getClothingStyleName);
        createDto.setClothingStyleCode(clothingStyleCode);
        //波段
        String waveBandCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.PLM_CLOTHING_BAND), PickStyleInnerAddReq.SkcInfo::getWaveBandName);
        createDto.setWaveBandCode(waveBandCode);
        //灵感源品牌
        String inspirationBrandCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.INSPIRATION_BRAND), PickStyleInnerAddReq.SkcInfo::getInspirationBrandName);
        createDto.setInspirationBrandCode(inspirationBrandCode);
        //灵感图来源
        String inspirationImageSourceCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.INSPIRATION_IMAGE_SOURCE), PickStyleInnerAddReq.SkcInfo::getInspirationImageSourceName);
        createDto.setInspirationImageSourceCode(inspirationImageSourceCode);
        //企划来源
        String planningSourceCode = this.getDictCodeByName(item, dictValueMap.get(DictConstant.PLANNING_SOURCE), PickStyleInnerAddReq.SkcInfo::getPlanningSourceName);
        createDto.setPlanningSourceCode(planningSourceCode);
        //品类 CLOTHING_CATEGORY
        this.setPickStyleCategory(item, createDto, categoryMap, endCategoryMap);
        //颜色 CLOTHING_COLOR 根据名称精确匹配
        colorInfoVoMap.forEach((color, colorInfo) -> {
            if (Objects.equals(color, item.getColor())) {
                createDto.setColor(color);
                createDto.setColorEnglishName(colorInfo.getColorEnglishName());
                createDto.setColorInfoVo(colorInfo);
            }
        });
        //风格(二级)
        if (StrUtil.isNotBlank(createDto.getClothingStyleName()) && StrUtil.isBlank(createDto.getClothingStyleCode())) {
            styleMap.forEach((name, code) -> {
                if (name.contains(item.getClothingStyleName())) {
                    createDto.setClothingStyleCode(code);
                    createDto.setClothingStyleName(name);
                }
            });
        }
    }

    private void setPickStyleCategory(PickStyleInnerAddReq.SkcInfo item, SpotPickStyleCreateDto createDto, Map<String, String> categoryMap, Map<String, SpotCategoryDto> endCategoryMap) {
        //先全路径精确匹配
        categoryMap.forEach((name, code) -> {
            if (Objects.equals(name, item.getCategoryName())) {
                createDto.setCategory(code);
                createDto.setCategoryName(name);
            }
        });
        //再末级精确匹配
        if (StrUtil.isNotBlank(createDto.getCategoryName()) && StrUtil.isBlank(createDto.getCategory())) {
            endCategoryMap.forEach((name, categoryDto) -> {
                if (Objects.equals(name, item.getCategoryName())) {
                    createDto.setCategory(categoryDto.getCategory());
                    createDto.setCategoryName(categoryDto.getCategoryName());
                }
            });
        }
        //最后模糊匹配
        if (StrUtil.isNotBlank(createDto.getCategoryName()) && StrUtil.isBlank(createDto.getCategory())) {
            categoryMap.forEach((name, code) -> {
                if (name.contains(item.getCategoryName())) {
                    createDto.setCategory(code);
                    createDto.setCategoryName(name);
                }
            });
        }
    }

    private String getDictCodeByName(PickStyleInnerAddReq.SkcInfo skcInfo, DictVo dictVo, Function<PickStyleInnerAddReq.SkcInfo, String> nameGetter) {
        if (Objects.isNull(skcInfo) || Objects.isNull(dictVo)) {
            return null;
        }
        String dictCode = null;
        if (CollectionUtil.isNotEmpty(dictVo.getChildren())) {
            String valueName = nameGetter.apply(skcInfo);
            Map<String, DictVo> dictNameMap = StreamUtil.list2Map(dictVo.getChildren(), DictVo::getDictName);
            DictVo childDictVo = dictNameMap.get(valueName);
            if (Objects.nonNull(childDictVo)) {
                dictCode = childDictVo.getDictCode();
            }
        }
        return dictCode;
    }

    private void handlePickStyleAdd(SpotPickStyleCreateDto skcReq, Map<String, SpotSpuSupplier> oldSupplierMap, List<SpotPickStyleAddResultVo> addVoList, boolean isLast) {
        checkPickStyleAdd(skcReq);
        SpotSpuSupplier oldSupplier = oldSupplierMap.get(skcReq.getSupplierNameStyle());

        String styleCode = null;
        if (Objects.isNull(oldSupplier)) {
            //再查下redis
            String pickStyleNewSpuKey = DesignRedisConstants.getPickStyleNewSpuKey(skcReq.getSupplierNameStyle());
            String spuCode = (String) redisTemplate.opsForValue().get(pickStyleNewSpuKey);
            if (StrUtil.isNotBlank(spuCode)) {
                styleCode = spuCode;
            }
        } else {
            // 1688来源：优先用productId定位，排除货通商品
            if (Objects.equals(skcReq.getSourceType(), ProductSourceTypeEnum.PRODUCT_POOL.code)
                    && ObjectUtils.notEqual(skcReq.getCommunication(), ProductCommunicationEnum.YES.getCode())) {
                if (null != skcReq.getProductId()) {
                    //现货类型+商品id再次查询
                    SpotSpuVo spotSpuVo = spotSpuRepository.selectBySpotTypeNameAndProductId(skcReq.getSpotTypeName(), skcReq.getProductId());
                    if (null != spotSpuVo) {
                        styleCode = spotSpuVo.getStyleCode();
                    }
                }
            } else {
                styleCode = oldSupplier.getStyleCode();
            }
        }
        SpotSkc skcEo = new SpotSkc();
        //1, 新建SPU, 资料状态: 待补充; 商品图状态: 待补充;  供给方式为tryOn, 发起核价
        if (StrUtil.isBlank(styleCode)) {
            long spotSpuId = IdPool.getId();
            skcEo = this.creatSpuSkc4PickStyle(skcReq, spotSpuId, oldSupplierMap);
            styleCode = skcEo.getStyleCode();
            log.info("=== 选款创建spu: {} =====", styleCode);
            //操作日志
            this.addLog(skcEo.getStyleCode(), spotSpuId, DesignLogContentEnum.SPOT_PICK_STYLE_CREATE.getDesc());
        }
        //2, SPU已存在, 判断是否需要创建skc: 选款的颜色是否与该SPU下的颜色不重复, 不重复则根据颜色生成skc: 资料状态: 待补充;
        else {
            skcEo = spotSkcService.create4PickStyleWithSpu(styleCode, skcReq);

            log.info("=== 选款更新spu: {} =====", styleCode);
        }
        if (isLast) {
            SpotPickStyleAddResultVo addVo = new SpotPickStyleAddResultVo();
            addVo.setPickBizId(skcReq.getPickBizSpuId());
            addVo.setStyleCode(skcEo.getStyleCode());
            addVo.setSpotSkcId(skcReq.getPickBizSkcId());
            addVo.setDesignCode(skcEo.getDesignCode());
            addVo.setSuccessful(true);
            addVo.setMessage("成功");
            addVoList.add(addVo);
        }
    }

    private static void checkPickStyleAdd(SpotPickStyleCreateDto skcReq) {
        // 判断是否为货通商品
        boolean isCommunication = Objects.equals(skcReq.getCommunication(), ProductCommunicationEnum.YES.getCode());
        
        // 核心字段校验（货通和非货通商品都需要）
        SdpDesignException.notNull(skcReq.getPickBizSkcId(), "选款业务id不能为空");
        
        // 非货通商品保持原有完整校验
        if (!isCommunication) {
            SdpDesignException.notBlank(skcReq.getSupplyModeName(), "供给方式不能为空");
            SdpDesignException.notBlank(skcReq.getSpotTypeName(), "现货类型名称不能为空");
            SdpDesignException.notBlank(skcReq.getCategoryName(), "款式品类名不能为空");
            SdpDesignException.notBlank(skcReq.getStoreName(), "店铺名称不能为空");
            SdpDesignException.notBlank(skcReq.getPalletTypeName(), "货盘类型名称不能为空");
            SdpDesignException.notBlank(skcReq.getProductType(), "商品类型不能为空");
            SdpDesignException.notBlank(skcReq.getColor(), "颜色名称不能为空");
            SdpDesignException.notEmpty(skcReq.getSkcPictureList(), "skcPictureList");
            SdpDesignException.notBlank(skcReq.getPickStyleNo(), "选款编号不能为空");
            SdpDesignException.notNull(skcReq.getPickStyleUserId(), "选款人id不能为空");
            SdpDesignException.notNull(skcReq.getPickStyleUserName(), "选款人名称不能为空");
            SdpDesignException.notBlank(skcReq.getSupplierName(), "供应商名称不能为空");
            SdpDesignException.notBlank(skcReq.getSupplierStyle(), "供应商款号不能为空");
            SdpDesignException.notNull(skcReq.getPurchasePrice(), "采购价不能为空");

            //只要tryOn与ODM款
            if (!(Objects.equals(skcReq.getSupplyModeName(), SupplyModeEnum.TRY_ON.getDesc())
                    || Objects.equals(skcReq.getSupplyModeName(), SupplyModeEnum.MANUFACTURER.getDesc())
                    || Objects.equals(skcReq.getSupplyModeName(), SupplyModeEnum.IMITATION.getDesc()))) {
                throw new SdpDesignException("供给方式需要是现货 try on或ODM或仿款");
            }
        }
        // 货通商品放宽校验，先建款，后续由AI补充属性
    }

    private SpotSkc creatSpuSkc4PickStyle(SpotPickStyleCreateDto skcReq, long spotSpuId, Map<String, SpotSpuSupplier> oldSupplierMap) {
        String styleCode = businessCodeGenerator.generate(CodeRuleEnum.SPOT_SPU_CODE, spotSpuRepository::selectLatestStyleCode);
        Integer spuVersionNum = 1;

        SpotSpu spotSpuEo = SpotConverter.buildPickStyleSpuEo(skcReq, spotSpuId, spuVersionNum, styleCode);
        SpotSpuDetail spuDetailEo = SpotConverter.buildPickStyleSpuDetailEo(skcReq, spotSpuId);

        spotSpuRepository.save(spotSpuEo);
        spotSpuDetailRepository.save(spuDetailEo);

        //创建skc
        SpotSkc spotSkcService4PickStyle = spotSkcService.create4PickStyle(styleCode, skcReq);

        //创建供应商
        SpotSpuSupplier supplier = spotSpuSupplierService.create4PickStyle(styleCode, skcReq);

        //供给方式为tryOn, 发起核价
        if (Objects.equals(skcReq.getSupplyModeCode(), SupplyModeEnum.TRY_ON.getCode())) {
            SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
            sampleClothesRemoteHelper.noticeCreateSpotTryOnEstimateCheckPrice(spotSpu);
        }

        //新增spu记录的spu供应商映射map中, 下个数据判断是否有对应spu
        oldSupplierMap.put(supplier.getSupplierNameStyle(), supplier);

        //发起款式识别, 现货拿spu图第一张（导入作为spu图，没有就不识别）
        if (CollUtil.isNotEmpty(skcReq.getProductPictureList())) {
            this.addSpuIdentify(skcReq.getProductPictureList().getFirst(), spotSpuEo);
        }


        //存到redis中
        String pickStyleNewSpuKey = DesignRedisConstants.getPickStyleNewSpuKey(skcReq.getSupplierNameStyle());
        redisTemplate.opsForValue().setIfAbsent(pickStyleNewSpuKey, styleCode, 30L, TimeUnit.SECONDS);
        return spotSkcService4PickStyle;
    }

    @Override
    public SpotSpuInfoVo getInfoByStyleCode(String styleCode) {
        if (StringUtils.isBlank(styleCode)) {
            return null;
        }
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        if (spotSpu == null) {
            return null;
        }
        //现货SPU基础信息
        SpotSpuInfoVo vo = new SpotSpuInfoVo();
        SpotSpuBaseInfoVo baseInfoVo = new SpotSpuBaseInfoVo();
        BeanUtils.copyProperties(spotSpu, baseInfoVo);
        vo.setSpotSpuBaseInfoVo(baseInfoVo);

        //现货SPU明细信息
        SpotSpuDetail spotSpuDetail = spotSpuDetailRepository.getBySpotSpuId(spotSpu.getSpotSpuId());
        if (spotSpuDetail != null) {
            SpotSpuDetailBaseInfoVo detailBaseInfoVo = new SpotSpuDetailBaseInfoVo();
            BeanUtils.copyProperties(spotSpuDetail, detailBaseInfoVo);
            vo.setSpotSpuDetailBaseInfoVo(detailBaseInfoVo);
        }
        //现货SPU供应商信息
        List<SpotSpuSupplier> supplierList = spotSpuSupplierRepository.listByStyleCodes(Collections.singletonList(spotSpu.getStyleCode()));
        if (!CollectionUtil.isEmpty(supplierList)) {
            vo.setSupplierBaseInfoVoList(supplierList.stream().map(v -> {
                SpotSpuSupplierBaseInfoVo supplierBaseInfoVo = new SpotSpuSupplierBaseInfoVo();
                BeanUtils.copyProperties(v, supplierBaseInfoVo);
                return supplierBaseInfoVo;
            }).collect(Collectors.toList()));
        }

        //现货SKC基本信息
        List<SpotSkcBaseInfoVo> skcBaseInfoVoList = spotSkcRepository.listByStyleCode(styleCode).stream().map(v -> {
            SpotSkcBaseInfoVo spotSkcBaseInfoVo = new SpotSkcBaseInfoVo();
            BeanUtils.copyProperties(v, spotSkcBaseInfoVo);
            return spotSkcBaseInfoVo;
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(skcBaseInfoVoList)) {
            vo.setSkcBaseInfoVoList(skcBaseInfoVoList);
            //现货SKC明细信息
            List<Long> spotSkcIds = skcBaseInfoVoList.stream().map(SpotSkcBaseInfoVo::getSpotSkcId).collect(Collectors.toList());
            List<SpotSkcDetail> skcDetailList = spotSkcDetailRepository.listBySkcIds(spotSkcIds);
            List<SpotSkcDetailBaseInfoVo> skcDetailBaseInfoVoList = skcDetailList.stream().map(v -> {
                SpotSkcDetailBaseInfoVo skcDetailBaseInfoVo = new SpotSkcDetailBaseInfoVo();
                BeanUtils.copyProperties(v, skcDetailBaseInfoVo);
                //现货SKC明细信息中的颜色
                if (!CollectionUtils.isEmpty(v.getColorInfoList())) {
                    skcDetailBaseInfoVo.setColorInfoList(v.getColorInfoList().stream().map(c -> {
                        ColorInfoVo colorInfoVo = new ColorInfoVo();
                        BeanUtils.copyProperties(c, colorInfoVo);
                        return colorInfoVo;
                    }).collect(Collectors.toList()));
                }
                return skcDetailBaseInfoVo;
            }).collect(Collectors.toList());
            vo.setSkcDetailBaseInfoVoList(skcDetailBaseInfoVoList);
        }
        return vo;
    }


    @Override
    public List<SpotSpuExportVo> spotSpuExportExcel(SpotSpuQuery query) {
        List<SpotSpuExportVo> list = spotSpuRepository.findList(query);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        //spu详情
        List<Long> spuIdList = StreamUtil.convertListAndDistinct(list, SpotSpuExportVo::getSpotSpuId);
        List<SpotSpuDetail> spuDetailList = spotSpuDetailRepository.listBySpotSpuIds(spuIdList);
        Map<Long, SpotSpuDetail> spuDetailMap = StreamUtil.list2Map(spuDetailList, SpotSpuDetail::getSpotSpuId);
        //skc详情
        List<Long> skcIdList = StreamUtil.convertListAndDistinct(list, SpotSpuExportVo::getSpotSkcId);
        List<SpotSkcDetail> skcDetailList = spotSkcDetailRepository.listBySkcIds(skcIdList);
        Map<Long, SpotSkcDetail> skcDetailMap = StreamUtil.list2Map(skcDetailList, SpotSkcDetail::getSpotSkcId);
        // 市场风格
        Map<String, String> marketMap = dictValueRemoteHelper.getMarketNodeMap(1);
        Map<String, String> marketSeriesMap = dictValueRemoteHelper.getMarketNodeMap(2);
        Map<String, String> marketStyleMap = dictValueRemoteHelper.getMarketNodeMap(3);
        list.forEach(item -> {

            SpotSpuDetail spuDetail = spuDetailMap.get(item.getSpotSpuId());
            if (Objects.nonNull(spuDetail)) {
                item.setProductLink(spuDetail.getProductLink());
            }
            SpotSkcDetail skcDetail = skcDetailMap.get(item.getSpotSkcId());
            if (Objects.nonNull(skcDetail)) {
                item.setSampleSize(skcDetail.getSampleSize());
            }
            if(StringUtils.isNotBlank(item.getMarketCode())){
                item.setMarketName(marketMap.get(item.getMarketCode()));
            }
            if(StringUtils.isNotBlank(item.getMarketSeriesCode())){
                item.setMarketSeriesName(marketSeriesMap.get(item.getMarketSeriesCode()));
            }
            if(StringUtils.isBlank(item.getClothingStyleName()) && StringUtils.isNotBlank(item.getClothingStyleCode())){
                item.setClothingStyleName(marketStyleMap.get(item.getClothingStyleCode()));
            }
            if(item.getPlanningType() !=null){
                item.setPlanningTypeName(PlanningTypeEnum.getDescByCode(item.getPlanningType()));
            }
        });

        //供应商信息填充
        fillSupplier(list);
        //核价信息填充
        fillEstimateCheckPrice(list);
        return list;
    }

    private void fillEstimateCheckPrice(List<SpotSpuExportVo> list) {
        List<Long> predictCheckPriceIds = list.stream().map(SpotSpuExportVo::getPredictCheckPriceId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(predictCheckPriceIds)) {
            //核价信息查询
            List<EstimateCheckPriceVo> estimateCheckPriceVoList = sampleClothesRemoteHelper.listEstimateCheckPriceByIds(predictCheckPriceIds);
            Map<Long, List<EstimateCheckPriceVo>> estimateCheckPriceVoMap = StreamUtil.groupingBy(estimateCheckPriceVoList, EstimateCheckPriceVo::getEstimateCheckPriceId);
            if (!CollectionUtils.isEmpty(estimateCheckPriceVoMap)) {
                list.forEach(t -> {
                    //核价信息
                    List<EstimateCheckPriceVo> estimateCheckPriceList = estimateCheckPriceVoMap.get(t.getPredictCheckPriceId());
                    if (!CollectionUtils.isEmpty(estimateCheckPriceList)) {
                        EstimateCheckPriceVo estimateCheckPriceVo = estimateCheckPriceList.getFirst();
                        t.setTotalCost(estimateCheckPriceVo.getTotalCost());
                        if (null != estimateCheckPriceVo.getPriceType()) {
                            t.setPriceType(estimateCheckPriceVo.getPriceType() == 1 ? "按返单定价" : "按不返单定价");
                        }
                    }
                });
            }
        }
    }

    private void fillSupplier(List<SpotSpuExportVo> list) {
        List<String> styleCodes = list.stream().map(SpotSpuExportVo::getStyleCode).collect(Collectors.toList());
        if (!CollUtil.isEmpty(styleCodes)) {
            List<SpotSpuSupplier> spuSupplierList = spotSpuSupplierRepository.listByStyleCodes(styleCodes);
            Map<String, List<SpotSpuSupplier>> supplierMap = StreamUtil.groupingBy(spuSupplierList, SpotSpuSupplier::getStyleCode);
            list.forEach(t -> {
                //拿第一个供应商导出就行
                List<SpotSpuSupplier> spotSpuSupplierList = supplierMap.get(t.getStyleCode());
                if (!CollectionUtils.isEmpty(spotSpuSupplierList)) {
                    SpotSpuSupplier spotSpuSupplier = spotSpuSupplierList.getFirst();
                    t.setSupplierName(spotSpuSupplier.getSupplierName());
                    t.setSupplierStyle(spotSpuSupplier.getSupplierStyle());
                    t.setPayeeName(spotSpuSupplier.getPayeeName());
                    t.setPurchasePrice(spotSpuSupplier.getPurchasePrice());
                }
            });
        }
    }

    @Override
    public PageRespVo<SpotManagePageVo> page(SpotSpuQuery query) {
        //spu列表查询
        IPage<SpotSpuVo> page = spotSpuRepository.findPage(query);
        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.empty();
        }
        List<SpotSpuVo> records = page.getRecords();
        List<String> spuList = StreamUtil.convertListAndDistinct(records, SpotSpuVo::getStyleCode);

        //spu_detail
        List<Long> spotSpuIdList = StreamUtil.convertListAndDistinct(records, SpotSpuVo::getSpotSpuId);
        List<SpotSpuDetail> spotSpuDetailList = spotSpuDetailRepository.listBySpotSpuIds(spotSpuIdList);
        Map<Long, SpotSpuDetail> spotSpuDetailMap = StreamUtil.list2Map(spotSpuDetailList, SpotSpuDetail::getSpotSpuId);

        //供应商
        List<SpotSpuSupplier> spuSupplierList = spotSpuSupplierRepository.listByStyleCodes(spuList);
        Map<String, List<SpotSpuSupplier>> supplierMap = StreamUtil.groupingBy(spuSupplierList, SpotSpuSupplier::getStyleCode);

        //skc
        List<SpotSkc> skcList = spotSkcRepository.listByStyleCodes(spuList);
        Map<String, List<SpotSkc>> skcMap = StreamUtil.groupingBy(skcList, SpotSkc::getStyleCode);
        List<Long> skcIdList = StreamUtil.convertListAndDistinct(skcList, SpotSkc::getSpotSkcId);
        List<SpotSkcDetail> skcDetails = spotSkcDetailRepository.listBySkcIds(skcIdList);
        Map<Long, SpotSkcDetail> skcDetailMap = StreamUtil.list2Map(skcDetails, SpotSkcDetail::getSpotSkcId);

        // 查询POP店铺信息
        List<String> allShopNames = records.stream().map(SpotSpuVo::getStoreName).toList();
        List<ShopResp> shopList = popProductHelper.getShopList(allShopNames);

        List<SpotManagePageVo> pageVoList = records.stream()
                .map(record ->
                        this.buildPageVo(query, record, spotSpuDetailMap, skcMap, skcDetailMap, supplierMap, shopList)
                ).toList();
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), pageVoList);
    }



    private SpotManagePageVo buildPageVo(SpotSpuQuery query,
                                         SpotSpuVo record,
                                         Map<Long, SpotSpuDetail> spotSpuDetailMap,
                                         Map<String, List<SpotSkc>> skcMap,
                                         Map<Long, SpotSkcDetail> skcDetailMap,
                                         Map<String, List<SpotSpuSupplier>> supplierMap,
                                         List<ShopResp> shopList) {
        SpotManagePageVo pageVo = new SpotManagePageVo();
        BeanUtils.copyProperties(record, pageVo);

        // 平台名称
        String platformName = shopList.stream()
                .filter(s -> StringUtils.equals(record.getStoreName(), s.getShopName()))
                .findFirst()
                .map(ShopResp::getPlatformName)
                .orElse(null);
        pageVo.setPlatformName(platformName);

        //详情
        SpotSpuDetail spuDetail = spotSpuDetailMap.get(record.getSpotSpuId());
        if (Objects.nonNull(spuDetail)) {
            pageVo.setProductPictureList(spuDetail.getProductPictureList());
            pageVo.setTryOnPictureList(spuDetail.getTryOnPictureList());
        }
        //spu商品图
        this.setSpuProductPicture(record.getStyleCode(), spuDetail, pageVo, skcMap, skcDetailMap);

        //供应商
        List<SpotSpuSupplier> spuSuppliers = supplierMap.get(record.getStyleCode());
        if (CollUtil.isNotEmpty(spuSuppliers)) {
            List<SpotManagePageVo.SupplierInfo> supplierInfoList = spuSuppliers.stream().map(supplier -> {
                SpotManagePageVo.SupplierInfo supplierInfo = new SpotManagePageVo.SupplierInfo();
                BeanUtils.copyProperties(supplier, supplierInfo);
                return supplierInfo;
            }).toList();
            pageVo.setSupplierInfoList(supplierInfoList);
        }

        //skc
        List<SpotSkc> spotSkcList = skcMap.get(record.getStyleCode());
        if (CollUtil.isNotEmpty(spotSkcList)) {
            //采购状态与skc取消状态过滤
            List<SpotSkc> filterSkcList = this.getFilterSkcList(query, spotSkcList);
            List<SpotSkcPageVo> skcPageVoList = filterSkcList.stream()
                    .sorted(Comparator.comparing(SpotSkc::getDesignCode))
                    .map(skc -> {
                        SpotSkcPageVo skcPageVo = new SpotSkcPageVo();
                        BeanUtils.copyProperties(skc, skcPageVo);
                        SpotSkcDetail skcDetail = skcDetailMap.get(skc.getSpotSkcId());
                        if (Objects.nonNull(skcDetail)) {
                            if (CollUtil.isNotEmpty(skcDetail.getProductPictureList())) {
                                skcPageVo.setProductPicture(skcDetail.getProductPictureList().getFirst());
                            }
                            skcPageVo.setSampleSize(skcDetail.getSampleSize());
                            skcPageVo.setOriginalSampleSize(skcDetail.getOriginalSampleSize());
                            skcPageVo.setCancelReason(skcDetail.getCancelReason());
                            skcPageVo.setCancelTime(skcDetail.getCancelTime());
                            skcPageVo.setCancelUserName(skcDetail.getCancelUserName());
                        }
                        return skcPageVo;
                    }).toList();
            pageVo.setSkcInfoList(skcPageVoList);
        }
        return pageVo;
    }

    private List<SpotSkc> getFilterSkcList(SpotSpuQuery query, List<SpotSkc> spotSkcList) {
        //采购状态与skc取消状态过滤
        List<SpotSkc> filterSkcList = new ArrayList<>();
        if (Objects.nonNull(query.getPurchaseOrderStatus()) && Objects.nonNull(query.getIsCanceled())) {
            for (SpotSkc spotSkc : spotSkcList) {
                if (Objects.equals(spotSkc.getPurchaseOrderStatus(), query.getPurchaseOrderStatus())
                        && Objects.equals(spotSkc.getIsCanceled(), query.getIsCanceled())) {
                    filterSkcList.add(spotSkc);
                }
            }
            return filterSkcList;
        }
        if (Objects.nonNull(query.getPurchaseOrderStatus())) {
            for (SpotSkc spotSkc : spotSkcList) {
                if (Objects.equals(spotSkc.getPurchaseOrderStatus(), query.getPurchaseOrderStatus())) {
                    filterSkcList.add(spotSkc);
                }
            }
            return filterSkcList;
        }
        if (Objects.nonNull(query.getIsCanceled())) {
            for (SpotSkc spotSkc : spotSkcList) {
                if (Objects.equals(spotSkc.getIsCanceled(), query.getIsCanceled())) {
                    filterSkcList.add(spotSkc);
                }
            }
            return filterSkcList;
        }
        return spotSkcList;
    }

    private void setSpuProductPicture(String styleCode, SpotSpuDetail spuDetail, SpotManagePageVo pageVo, Map<String, List<SpotSkc>> skcMap, Map<Long, SpotSkcDetail> skcDetailMap) {
        if(null != spuDetail) {
            pageVo.setAiAlibabaDistributionSyncStatus(spuDetail.getAiAlibabaDistributionSyncStatus());
        }
        //spu图 如果SPU存在商品图，则展示首张商品图; 如果SPU不存在商品图，则展示第一个SKC的图片
        if (null != spuDetail && CollUtil.isNotEmpty(spuDetail.getProductPictureList())) {
            pageVo.setProductPicture(spuDetail.getProductPictureList().getFirst());
        } else {
            List<SpotSkc> skcList = skcMap.get(styleCode);
            if (CollUtil.isNotEmpty(skcList)) {
                SpotSkc firstSkc = skcList.getFirst();
                SpotSkcDetail skcDetail = skcDetailMap.get(firstSkc.getSpotSkcId());
                if (Objects.nonNull(skcDetail) && CollUtil.isNotEmpty(skcDetail.getProductPictureList())) {
                    pageVo.setProductPicture(skcDetail.getProductPictureList().getFirst());
                }
            }
        }
    }

    @Override
    public List<SpotPriceTryOnVo> priceTryOnList(SpotPriceTryOnReq req) {
        List<String> styleCodeList = req.getStyleCodeList();
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(spotSpuList)) {
            return Collections.emptyList();
        }
        //核价信息查询
        Map<Long, EstimateCheckPriceVo> estimateCheckPriceVoMap = this.getCheckPriceMap(spotSpuList);

        //款式识别信息
        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizCodeSource(styleCodeList, SpuIdentifySourceEnum.SPOT_SPU.getCode(), null);
        Map<String, List<SpuIdentify>> spuIdentifyGroupMap = StreamUtil.groupingBy(spuIdentifyList, SpuIdentify::getBizCode);

        //出参封装
        Map<String, SpotSpu> spuMap = StreamUtil.list2Map(spotSpuList, SpotSpu::getStyleCode);
        List<SpotPriceTryOnVo> respList = styleCodeList.stream().map(spu -> {
            SpotPriceTryOnVo priceTryOnVo = new SpotPriceTryOnVo();
            priceTryOnVo.setStyleCode(spu);
            SpotSpu spotSpu = spuMap.get(spu);
            //tryOn信息
            if (Objects.nonNull(spotSpu)) {
                SpotPriceTryOnVo.TryOnInfo tryOnInfo = new SpotPriceTryOnVo.TryOnInfo();
                BeanUtils.copyProperties(spotSpu, tryOnInfo);
                priceTryOnVo.setTryOnInfo(tryOnInfo);
            }
            //核价信息
            EstimateCheckPriceVo checkPriceVo = estimateCheckPriceVoMap.get(spotSpu.getPredictCheckPriceId());
            if (Objects.nonNull(checkPriceVo)) {
                SpotPriceTryOnVo.CheckPriceInfo priceInfo = new SpotPriceTryOnVo.CheckPriceInfo();
                priceInfo.setPredictCheckPriceId(checkPriceVo.getEstimateCheckPriceId());
                Integer priceStatus = priceInfo.getStatByPrice(checkPriceVo);
                priceInfo.setPredictCheckPriceStatus(priceStatus);
                priceInfo.setPredictCheckPriceTime(checkPriceVo.getFinishTime());
                priceInfo.setPricingType(checkPriceVo.getPriceType());
                priceInfo.setCheckPriceCost(checkPriceVo.getTotalCost());
                priceInfo.setCheckPricerName(checkPriceVo.getPricerName());
                priceTryOnVo.setCheckPriceInfo(priceInfo);
            }
            //款式识别
            priceTryOnVo.setMuseIdentifyInfo(SpuIdentifyConverter.buildIdentifyInfo(spu, spuIdentifyGroupMap));
            return priceTryOnVo;
        }).toList();

        //视觉需求信息
        List<String> spotStyleCodeList = StreamUtil.convertListAndDistinct(spotSpuList, SpotSpu::getStyleCode);
        this.buildPageVisualDemandInfo(spotStyleCodeList, respList);

        return respList;
    }


    private void buildPageVisualDemandInfo(List<String> styleCodeList, List<SpotPriceTryOnVo> respList) {
        if (CollUtil.isEmpty(styleCodeList) || CollUtil.isEmpty(respList)) {
            return;
        }
        //spu批量查询下最新的【上新、优化】视觉任务
        List<Integer> taskTypeList = List.of(VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode(), VisualTaskTypeEnum.OPTIMIZE_TASK.getCode());
        List<VisualTask> visualTaskList = visualTaskRepository.listLatestBySpuTaskType(styleCodeList, taskTypeList);
        Map<String, VisualTask> visualTaskMap = new HashMap<>(styleCodeList.size());
        List<Long> visualDemandIdList = new ArrayList<>(styleCodeList.size());
        StreamUtil.groupingBy(visualTaskList, VisualTask::getStyleCode).forEach((styleCode, taskList) -> {
            //获取最新的视觉任务
            VisualTask latestVisualTask = taskList.stream().max(Comparator.comparing(VisualTask::getCreatedTime)).orElse(null);
            if (Objects.nonNull(latestVisualTask)) {
                visualTaskMap.put(styleCode, latestVisualTask);
                visualDemandIdList.add(latestVisualTask.getLatestDemandId());
            }
        });
        if (CollUtil.isEmpty(visualDemandIdList)) {
            return;
        }
        List<VisualDemand> visualDemands = visualDemandRepository.listByIds(visualDemandIdList);
        Map<Long, VisualDemand> visualDemandMap = StreamUtil.list2Map(visualDemands, VisualDemand::getDemandId);

        respList.forEach(item -> {
            VisualTask visualTask = visualTaskMap.get(item.getStyleCode());
            if (Objects.isNull(visualTask)) {
                return;
            }
            VisualDemand visualDemand = visualDemandMap.get(visualTask.getLatestDemandId());
            if (Objects.isNull(visualDemand)) {
                return;
            }
            VisualDemandInfoVo demandInfoVo = new VisualDemandInfoVo();
            BeanUtils.copyProperties(visualDemand, demandInfoVo);
            demandInfoVo.setModelReferenceImageList(JSON.parseArray(visualDemand.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
            demandInfoVo.setBackgroundImageList(JSON.parseArray(visualDemand.getBackgroundImage(), BackgroundDTO.class));
            demandInfoVo.setModelFaceImageList(JSON.parseArray(visualDemand.getModelFaceImage(), ModelFaceDTO.class));
            this.setDemandPicInfo(visualDemand, demandInfoVo);
            //任务信息
            demandInfoVo.setState(visualTask.getState());
            demandInfoVo.setProcessCode(visualTask.getProcessCode());
            item.setVisualDemandInfo(demandInfoVo);
        });
    }

    private void setDemandPicInfo(VisualDemand visualDemand, VisualDemandInfoVo infoVo) {
        if (Objects.isNull(visualDemand)) {
            return;
        }
        if(StringUtils.isNotBlank(visualDemand.getDemandImages())){
            infoVo.setDemandImageList(StrUtil.splitTrim(visualDemand.getDemandImages(), StrUtil.COMMA));
        }
        if(StringUtils.isNotBlank(visualDemand.getModelPic())){
            infoVo.setModelPicList(StrUtil.splitTrim(visualDemand.getModelPic(), StrUtil.COMMA));
        }
        if(StringUtils.isNotBlank(visualDemand.getBackgroundPic())){
            infoVo.setBackgroundPicList(StrUtil.splitTrim(visualDemand.getBackgroundPic(), StrUtil.COMMA));
        }
        if(StringUtils.isNotBlank(visualDemand.getPosturePic())){
            infoVo.setPosturePicList(StrUtil.splitTrim(visualDemand.getPosturePic(), StrUtil.COMMA));
        }
    }

    private Map<Long, EstimateCheckPriceVo> getCheckPriceMap(List<SpotSpu> spotSpuList) {
        Map<Long, EstimateCheckPriceVo> estimateCheckPriceVoMap = new HashMap<>();
        List<Long> predictCheckPriceIds = StreamUtil.convertListAndDistinct(spotSpuList, SpotSpu::getPredictCheckPriceId);
        if (!CollectionUtils.isEmpty(predictCheckPriceIds)) {
            List<EstimateCheckPriceVo> estimateCheckPriceVoList = sampleClothesRemoteHelper.listEstimateCheckPriceByIds(predictCheckPriceIds);
            estimateCheckPriceVoMap = StreamUtil.list2Map(estimateCheckPriceVoList, EstimateCheckPriceVo::getEstimateCheckPriceId);
        }
        return estimateCheckPriceVoMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpotSpuSelfCreateVo selfCreate(SpotSpuSelfCreateReq req) {
        //供应商校验
        this.selfCreateSupplierCheck(req);

        //创建SPU与详情
        long spotSpuId = IdPool.getId();
        String styleCode = businessCodeGenerator.generate(CodeRuleEnum.SPOT_SPU_CODE, spotSpuRepository::selectLatestStyleCode);
        // String styleCode = clothingCodeGenerateService.generateSpuCode(ClothingCodeSourceEnum.DESIGN);
        Integer spuVersionNum = 1;
        UserContent userContent = UserContentHolder.get();

        SpotSpu spotSpuEo = SpotConverter.buildSelfCreateSpuEo(req, spotSpuId, spuVersionNum, styleCode, userContent);
        SpotSpuDetail spuDetailEo = SpotConverter.buildSelfCreateSpuDetailEo(req, spotSpuId);

        spotSpuRepository.save(spotSpuEo);
        spotSpuDetailRepository.save(spuDetailEo);

        //创建供应商
        spotSpuSupplierService.create4SelfCreate(styleCode, req.getSupplierAddList());

        //创建SKC
        List<SpotSkc> skcEoList = spotSkcService.create4SelfCreate(styleCode, req);

        //新建款号成功，将SPU&SKC的基本信息，同步到运营平台
        spotSkcService.pushPopProductBySpotSpu(spotSpuEo);

        //创建视觉需求
        this.addSpotVisualDemandForSelfCreate(req.getVisualDemandInfo(), styleCode);

        //操作日志
        this.addLog(styleCode, spotSpuId, DesignLogContentEnum.SPOT_SELF_CREATE.getDesc());

        List<SpotSpuSelfCreateVo.SkcInfo> skcInfoVoList = new ArrayList<>();
        skcEoList.forEach(item -> {
            SpotSpuSelfCreateVo.SkcInfo skcInfo = SpotSpuSelfCreateVo.SkcInfo.builder()
                    .spotSkcId(item.getSpotSkcId())
                    .designCode(item.getDesignCode())
                    .build();
            skcInfoVoList.add(skcInfo);
        });
        SpotSpuSelfCreateVo createVo = SpotSpuSelfCreateVo.builder()
                .spotSpuId(spotSpuId)
                .styleCode(styleCode)
                .skcInfoList(skcInfoVoList)
                .build();
        log.info("=== 新建款号成功: {} ===", JSONObject.toJSONString(createVo));

        return createVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpotSpuImportVo importSpu(SpotSpuImportReq req) {
        log.info("===  导入现货SPU req:{} ===", JSON.toJSONString(req));
        SpotSourceTypeEnum sourceTypeEnum = SpotSourceTypeEnum.SPU_IMPORT;

        String styleCode = businessCodeGenerator.generate(CodeRuleEnum.SPOT_SPU_CODE, spotSpuRepository::selectLatestStyleCode);
        UserContent userContent = UserContentHolder.get();

        //创建待提交SPU
        SpotSpu spotSpuEo = SpotConverter.buildImportSpuEo(req, styleCode, userContent, sourceTypeEnum);
        SpotSpuDetail spuDetailEo = SpotSpuDetail.builder()
                .spotSpuDetailId(IdPool.getId()).spotSpuId(spotSpuEo.getSpotSpuId()).build();
        spuDetailEo.setProductPictureList(Collections.singletonList(req.getStylePicture()));

        spotSpuRepository.save(spotSpuEo);
        spotSpuDetailRepository.save(spuDetailEo);

        //创建待提交skc
        spotSkcService.create4ImportSpu(spotSpuEo);

        //操作日志
        this.addLog(styleCode, spotSpuEo.getSpotSpuId(), DesignLogContentEnum.SPOT_IMPORT_CREATE.getDesc());

        //发起款式识别
        this.addSpuIdentify(req.getStylePicture(), spotSpuEo);

        log.info("===  导入现货SPU 新增spu:{} ===", JSON.toJSONString(spotSpuEo.getStyleCode()));
        return SpotSpuImportVo.builder().categoryName(req.getCategoryName()).styleCode(styleCode).build();
    }


    /**
     * 添加图片识别任务
     */
    private void addSpuIdentify(String stylePicture,SpotSpu spotSpuEo) {
        if (StrUtil.isBlank(stylePicture)) {
            log.info("=== 图片为空,不发起图片识别 ===");
            return;
        }
        SpuIdentifyAddReq identifyAddInfo = SpuIdentifyAddReq.builder()
                .sourceType(SpuIdentifySourceEnum.SPOT_SPU.getCode())
                .bizId(spotSpuEo.getSpotSpuId()).bizCode(spotSpuEo.getStyleCode())
                .identifyUrl(stylePicture)
                .build();

        spuIdentifyService.batchAddAsync(Collections.singletonList(identifyAddInfo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SpotSpuImportVo> excelImport(InputStream inputStream) {
        //读取excel
        List<SpotSpuImportData> reqList =  EasyExcel.read(inputStream)
                .head(SpotSpuImportData.class)
                .sheet()
                .doReadSync();
        log.info("=== excel导入现货SPU reqList:{} ===", JSON.toJSONString(reqList));
        SdpDesignException.notEmpty(reqList, "导入数据为空!");
        SdpDesignException.isTrue(reqList.size() <= 100, "最多导入100条记录");

        //品类映射校验与设值
        this.checkExcelImport(reqList);

        SpotSourceTypeEnum sourceTypeEnum = SpotSourceTypeEnum.SPU_IMPORT_EXCEL;
        UserContent userContent = UserContentHolder.get();

        //创建待提交的spu
        List<SpotSpu> spotSpuList = new ArrayList<>(reqList.size());
        List<SpotSpuDetail> spotSpuDetailList = new ArrayList<>(reqList.size());
        List<SpuIdentifyAddReq> identifyAddInfoList = this.buildExcelImportData(reqList, userContent, sourceTypeEnum, spotSpuList, spotSpuDetailList);
        spotSpuRepository.saveBatch(spotSpuList);
        spotSpuDetailRepository.saveBatch(spotSpuDetailList);

        //创建待提交skc
        spotSkcService.create4ExcelImport(spotSpuList);

        //操作日志
        spotSpuList.forEach(spotSpu -> {
            this.addLog(spotSpu.getStyleCode(), spotSpu.getSpotSpuId(), DesignLogContentEnum.SPOT_IMPORT_EXCEL_CREATE.getDesc());
        });

        //发起spu图片识别
        spuIdentifyService.batchAddAsync(identifyAddInfoList);

        List<SpotSpuImportVo> respList = spotSpuList.stream()
                .map(spotSpu -> SpotSpuImportVo.builder()
                        .styleCode(spotSpu.getStyleCode())
                        .categoryName(spotSpu.getCategoryName())
                        .build())
                .collect(Collectors.toList());
        log.info("=== excel导入现货SPU respList:{} ===", JSON.toJSONString(respList));
        return respList;
    }

    private List<SpuIdentifyAddReq> buildExcelImportData(List<SpotSpuImportData> reqList, UserContent userContent, SpotSourceTypeEnum sourceTypeEnum, List<SpotSpu> spotSpuList, List<SpotSpuDetail> spotSpuDetailList) {
        return reqList.stream().map(req -> {
            //spu
            String styleCode = businessCodeGenerator.generate(CodeRuleEnum.SPOT_SPU_CODE, spotSpuRepository::selectLatestStyleCode);
            SpotSpuImportReq importReq = SpotSpuImportReq.builder()
                    .category(req.getCategory()).categoryName(req.getCategoryName()).stylePicture(req.getStylePicture()).build();
            SpotSpu spotSpuEo = SpotConverter.buildImportSpuEo(importReq, styleCode, userContent, sourceTypeEnum);

            //spu详情
            SpotSpuDetail spuDetailEo = SpotSpuDetail.builder()
                    .spotSpuDetailId(IdPool.getId()).spotSpuId(spotSpuEo.getSpotSpuId()).build();
            spuDetailEo.setProductPictureList(Collections.singletonList(req.getStylePicture()));

            spotSpuList.add(spotSpuEo);
            spotSpuDetailList.add(spuDetailEo);

            //款式识别req
            return SpuIdentifyAddReq.builder()
                    .sourceType(SpuIdentifySourceEnum.SPOT_SPU.getCode())
                    .bizId(spotSpuEo.getSpotSpuId()).bizCode(spotSpuEo.getStyleCode())
                    .identifyUrl(req.getStylePicture())
                    .build();
        }).toList();
    }

    private void checkExcelImport(List<SpotSpuImportData> reqList) {
        //查询品类映射Map
        Map<String, String> categoryMap = this.getCategoryDictMap();

        //校验与品类匹配
        reqList.forEach(item -> {
            SdpDesignException.notBlank(item.getStylePicture(), "款式图片不能为空");
            SdpDesignException.notBlank(item.getCategoryName(), "品类名称不能为空");
            String categoryCode = categoryMap.get(item.getCategoryName());
            SdpDesignException.notBlank(categoryCode, "品类错误, 编码映射失败! {}", item.getCategoryName());
            item.setCategory(categoryCode);
        });
    }


    /**
     * 获取品类Map<名称,编码>
     */
    private Map<String, String> getCategoryDictMap() {
        List<DictVo> dictResp = dictValueRemoteHelper.listByDictCodes(Collections.singletonList(DictConstant.CLOTHING_CATEGORY));
        Map<String, DictVo> dictValueMap = dictResp.stream().collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));
        DictVo categoryDictVo = dictValueMap.get(DictConstant.CLOTHING_CATEGORY);
        return DictTreeConverter.convertToMap(categoryDictVo, "-");
    }



    private void selfCreateSupplierCheck(SpotSpuSelfCreateReq req) {
        //「供应商名称+供应商款号」是否已存在SPU，存在时高亮对应行，并提示：该款号已存在SPU，请勿重复添加
        List<String> supplierStyleList = new ArrayList<>();
        List<String> supplierNameList = new ArrayList<>();
        req.getSupplierAddList().forEach(item -> {
            if (StrUtil.isNotBlank(item.getSupplierStyle())) {
                supplierStyleList.add(item.getSupplierStyle());
            }
            if (StrUtil.isNotBlank(item.getSupplierName())) {
                supplierNameList.add(item.getSupplierName());
            }
        });
        if (CollUtil.isEmpty(supplierNameList) || CollUtil.isEmpty(supplierStyleList)) {
            return;
        }
        List<SpotSpuSupplier> oldSupplierList = spotSpuSupplierRepository.listBySupplierNameStyle(supplierNameList, supplierStyleList);
        Map<String, SpotSpuSupplier> oldSupplierMap = StreamUtil.list2Map(oldSupplierList, SpotSpuSupplier::getSupplierNameStyle);
        if (CollUtil.isNotEmpty(oldSupplierList)) {
            req.getSupplierAddList().forEach(item -> {
                SpotSpuSupplier oldSupplier = oldSupplierMap.get(item.getSupplierNameStyle());
                SdpDesignException.isNull(oldSupplier, "该款号已存在SPU:{}，请勿重复添加; 供应商名称:{}; 款号:{}",
                        oldSupplier.getStyleCode(), item.getSupplierName(), item.getSupplierStyle());
            });
        }
    }

    private void addLog(String styleCode, Long bizId, String logContent) {
        DesignLogSdpSaveReq logSdpSaveReq = DesignLogSdpSaveReq.builder()
                .bizId(bizId)
                .bizType(DesignLogBizTypeEnum.SPOT)
                .styleCode(styleCode)
                .content(logContent)
                .build();
        designLogService.sdpSave(logSdpSaveReq);
    }


    @Override
    public SpotWebDetailVo getWebDetail(String styleCode) {
        SpotWebDetailVo webDetailVo = new SpotWebDetailVo();
        //spu
        SpotSpuVo spuVo = this.getSpuInfo(styleCode);
        if (Objects.isNull(spuVo)) {
            return null;
        }
        BeanUtils.copyProperties(spuVo, webDetailVo);
        //spu详情
        SpotSpuDetail spotSpuDetail = spotSpuDetailRepository.getBySpotSpuId(spuVo.getSpotSpuId());
        if (Objects.nonNull(spotSpuDetail)) {
            webDetailVo.setStyleSeasonList(spotSpuDetail.getStyleSeasonList());
            webDetailVo.setProductLink(spotSpuDetail.getProductLink());
            webDetailVo.setProductPictureList(spotSpuDetail.getProductPictureList());
            webDetailVo.setTryOnPictureList(spotSpuDetail.getTryOnPictureList());
            webDetailVo.setAttributes(spotSpuDetail.getAttributes());
        }

        //供应商信息
        List<SpotSpuSupplier> spuSupplierList = spotSpuSupplierRepository.listByStyleCodes(Collections.singleton(styleCode));
        if (CollUtil.isNotEmpty(spuSupplierList)) {
            List<SpotWebDetailVo.SupplierInfo> supplierInfoList = spuSupplierList.stream().map(item -> {
                SpotWebDetailVo.SupplierInfo supplierInfo = new SpotWebDetailVo.SupplierInfo();
                BeanUtils.copyProperties(item, supplierInfo);
                return supplierInfo;
            }).toList();
            webDetailVo.setSupplierInfoList(supplierInfoList);
        }

        //skc信息
        List<SpotSkc> skcList = spotSkcRepository.listByStyleCode(styleCode);
        if (CollUtil.isNotEmpty(skcList)) {
            List<Long> skcIdList = StreamUtil.convertListAndDistinct(skcList, SpotSkc::getSpotSkcId);
            List<SpotSkcDetail> skcDetailList = spotSkcDetailRepository.listBySkcIds(skcIdList);
            Map<Long, SpotSkcDetail> skcDetailMap = StreamUtil.list2Map(skcDetailList, SpotSkcDetail::getSpotSkcId);
            List<SpotWebDetailVo.SkcInfo> skcInfoList = skcList.stream().map(item -> {
                SpotWebDetailVo.SkcInfo skcInfo = new SpotWebDetailVo.SkcInfo();
                BeanUtils.copyProperties(item, skcInfo);
                SpotSkcDetail skcDetail = skcDetailMap.get(item.getSpotSkcId());
                if (Objects.nonNull(skcDetail)) {
                    skcInfo.setSampleSize(skcDetail.getSampleSize());
                    skcInfo.setOriginalSampleSize(skcDetail.getOriginalSampleSize());
                    skcInfo.setColorInfoList(skcDetail.getColorInfoList());
                    skcInfo.setProductPictureList(skcDetail.getProductPictureList());
                }
                return skcInfo;
            }).toList();
            webDetailVo.setSkcInfoList(skcInfoList);
        }

        //款式识别信息
        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizCodeSource(Collections.singleton(styleCode), SpuIdentifySourceEnum.SPOT_SPU.getCode(), null);
        Map<String, List<SpuIdentify>> spuIdentifyGroupMap = StreamUtil.groupingBy(spuIdentifyList, SpuIdentify::getBizCode);
        webDetailVo.setMuseIdentifyInfo(SpuIdentifyConverter.buildIdentifyInfo(styleCode, spuIdentifyGroupMap));

        return webDetailVo;
    }

    @Override
    public SpotSpuVo getSpuInfo(String styleCode) {
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        if (Objects.isNull(spotSpu)) {
            return null;
        }

        SpotSpuVo spuVo = new SpotSpuVo();
        BeanUtils.copyProperties(spotSpu, spuVo);

        return spuVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpuSkc(SpotSpuUpdateReq req) {
        log.info("=== 更新现货spu req {} ===", JSONObject.toJSONString(req));
        String styleCode = req.getStyleCode();
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        SdpDesignException.notNull(spotSpu, "spu不存在!");
        SdpDesignException.isFalse(Objects.equals(spotSpu.getIsCanceled(), Bool.YES.getCode()), "spu已取消!");
        SdpDesignException.isTrue(Objects.equals(spotSpu.getVersionNum(), req.getVersionNum()), "spu已被更新, 请刷数页面重试!");
        SpotSpuDetail spuDetail = spotSpuDetailRepository.getBySpotSpuId(spotSpu.getSpotSpuId());
        SdpDesignException.notNull(spuDetail, "spu详情不存在!");
        //供应商校验
        this.supplierCheck4Update(req, spotSpu);

        UserContent userContent = UserContentHolder.get();

        //更新前的最大采购价
        BigDecimal oldMaxPurchasePrice = spotSpuSupplierRepository.getMaxPurchasePrice(styleCode);

        LocalDateTime now = LocalDateTime.now();
        Integer newSpuVersionNum = spotSpu.getVersionNum() + 1;

        //更新spu信息
        SpotSpuUpdateDto spotSpuUpdateDto = SpotConverter.buildSpuUpdateDto(req, styleCode, newSpuVersionNum, now);
        spotSpuUpdateDto.setSpotSpuId(spotSpu.getSpotSpuId());
        //更新商品图上传时间
        if (Objects.equals(req.getIsUploadProductPicture(), Bool.YES.getCode())
                && CollUtil.isNotEmpty(req.getProductPictureList())) {
            spotSpuUpdateDto.setProductPictureUploadTime(LocalDateTime.now());

            //商品图首图变更发起款式识别
            this.addSpuIdentifyByUpdate(req, spotSpu);
        }


        //资料状态与商品图状态设置
        this.setProductResourceState(req, spotSpu, spotSpuUpdateDto, userContent);
        spotSpuUpdateDto.setDeveloperName(userContent.getCurrentUserName());
        spotSpuUpdateDto.setDeveloperId(userContent.getCurrentUserId());
        spotSpuRepository.updateSpuInfo(spotSpuUpdateDto, userContent);

        //更新spu详情
        SpotSpuDetailUpdateDto spuDetailUpdateDto = SpotConverter.buildSpuDetailUpdateDto(req, spuDetail, now);
        spotSpuDetailRepository.updateDetail(spuDetailUpdateDto, userContent);

        //更新任务分配表信息
        spotSpuTryOnConfigService.updateBySpuMessage(req, spotSpu);

        //供应商增删改
        spotSpuSupplierService.addDelUpdate(req);

        //skc更新
        List<SpotSkcUpdateReq> skcUpdateList = req.getSkcUpdateList();
        spotSkcService.updateBatch(styleCode, skcUpdateList);

        //更新后与其他系统交互
        spuUpdateAfterHandle(req, styleCode, spotSpu, oldMaxPurchasePrice,userContent,spuDetail);

        log.info("=== 更新现货spu 成功 spu:{} ===", spotSpu.getStyleCode());
    }

    private void addSpuIdentifyByUpdate(SpotSpuUpdateReq req, SpotSpu spotSpu) {
        if (CollUtil.isEmpty(req.getProductPictureList()) || Objects.isNull(spotSpu)) {
            return;
        }
        //第一张商品图
        String identifyPicture = req.getProductPictureList().getFirst();
        if (StrUtil.isBlank(identifyPicture)) {
            return;
        }

        //查询款式识别记录是否存在
        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizCodeSource(
                Collections.singletonList(spotSpu.getStyleCode()), SpuIdentifySourceEnum.SPOT_SPU.getCode(), null
        );

        Boolean identifyFlag = Boolean.FALSE;

        //不存在则发起款式识别
        if (CollUtil.isEmpty(spuIdentifyList)) {
            identifyFlag = Boolean.TRUE;
        }
        //已存在, 对比商品图与识别记录的识别图是否一致, 如果不一致, 发起款式识别
        else {
            String oldIdentifyUrl = spuIdentifyList.getFirst().getIdentifyUrl();
            if (!Objects.equals(oldIdentifyUrl, identifyPicture)) {
                identifyFlag = Boolean.TRUE;
            }
        }

        //发起款式识别
        if (identifyFlag) {
            SpuIdentifyAddReq identifyAddReq = SpuIdentifyAddReq.builder()
                    .sourceType(SpuIdentifySourceEnum.SPOT_SPU.getCode())
                    .bizId(spotSpu.getSpotSpuId()).bizCode(spotSpu.getStyleCode())
                    .identifyUrl(identifyPicture)
                    .build();

            log.info("=== 现货商品图更新 发起款式识别 :{} ===", JSON.toJSONString(identifyAddReq));
            spuIdentifyService.batchAddAsync(Collections.singletonList(identifyAddReq));
        }
    }


    @Override
    public List<String> visualCheck(SpotVisualCheckReq req) {
        String styleCode = req.getStyleCode();
        //SPU编辑提交时, spu与skc都是资料已完善状态, 只需校验tryOn款状态
        if (Objects.equals(req.getCheckType(), 1)) {
            SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
            List<String> errorList = new ArrayList<>();
            //款式状态
            if (!Objects.equals(SpotStyleStateEnum.WAIT_SUBMIT.getCode(), spotSpu.getStyleStatus())) {
                errorList.add("款式状态不为未提交，请检查，spu编码：" + spotSpu.getStyleCode());
                return errorList;
            }
            if (Objects.equals(spotSpu.getSupplyModeCode(), SupplyModeEnum.TRY_ON.getCode())) {
                //预估核价状态
                if (!Objects.equals(SpotPriceStateEnum.RE_CHECK_PASS.getCode(), spotSpu.getPredictCheckPriceStatus())) {
                    errorList.add("预估核价状态不为复核通过，请检查，spu编码：" + spotSpu.getStyleCode());
                }
                //try on 价状态
                if (!Objects.equals(SpotTryOnStateEnum.PASS.getCode(), spotSpu.getTryOnStatus())) {
                    errorList.add("try on状态不为已通过，请检查，spu编码：" + spotSpu.getStyleCode());
                }
                //资料状态
                if (!Objects.equals(SpotResourceStateEnum.FINISH.getCode(), spotSpu.getResourceStatus())) {
                    errorList.add("资料状态状态不为已完善，请检查，spu编码：" + spotSpu.getStyleCode());
                }
                return errorList;
            }
            return List.of();
        }
        //SPU复色,校验逻辑与批量提交校验一致
        else if (Objects.equals(req.getCheckType(), 2)) {
            return List.of("复色不需要弹框");
            // return this.batchCommitVisualCheck(Collections.singletonList(styleCode));
        }else {
            throw new SdpDesignException("未知校验类型!");
        }
    }


    @Override
    public List<String> batchCommitVisualCheck(List<String> styleCodeList) {
        SdpDesignException.notEmpty(styleCodeList, "styleCode不能为空!");

        List<SpotSpu> spuList = spotSpuRepository.listByStyleCodes(styleCodeList);

        return this.batchCommitCheck(styleCodeList.size(), spuList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchCommit(BatchCommitReq req) {
        log.info("=== 批量提交现货spu req {} ===", JSONObject.toJSONString(req));
        List<SpotSpu> spuList = spotSpuRepository.listByIds(req.getSpotSpuIds());
        //校验
        List<String> errorList = this.batchCommitCheck(req.getSpotSpuIds().size(), spuList);
        if (CollUtil.isNotEmpty(errorList)) {
            return errorList;
        }

        //推送到pop
        spuList.forEach(spu -> {
            //同步符合条件的SKC到商品平台
            spotSkcService.pushPopProductBySpotSpu(spu);

            SpotVisualDemandReq visualDemandInfo = req.getVisualDemandInfo();
            SpotVisualSubmitReq visualSubmitReq = null;
            //创建视觉需求，前端有传需求内容
            if (visualDemandInfo != null) {
                visualSubmitReq = new SpotVisualSubmitReq();
                BeanUtils.copyProperties(visualDemandInfo, visualSubmitReq);
                visualSubmitReq.setStyleCode(spu.getStyleCode());
                visualSubmitReq.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_BATCH_SUBMIT);
            }
            //前端没有传需求内容，但系统内有一条待推送的需求
            else {
                //spu下最新记录
                SpuVisualDemandRecord oldDemandRecord = spuVisualDemandRecordRepository.getLatestBySpu(spu.getStyleCode());
                //有待推送的视觉需求
                if (oldDemandRecord != null && oldDemandRecord.getVisualDemandId() == null) {
                    visualSubmitReq = new SpotVisualSubmitReq();
                    BeanUtils.copyProperties(oldDemandRecord, visualSubmitReq);
                    visualSubmitReq.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_BATCH_SUBMIT);
                }
            }
            //创建视觉需求
            if(visualSubmitReq!=null) {
                try {
                    this.submitVisualTask(visualSubmitReq);
                } catch (Exception e) {
                    log.error("批量提交现货SPU，创建视觉需求失败", e);
                    //如果是未符合发起条件，则先保存需求
                    VisualErrorCodeEnum visualErrorCode = VisualErrorCodeEnum.findByCode(e.getMessage());
                    if (!VisualErrorCodeEnum.UNKNOWN.equals(visualErrorCode)) {
                        submitSpuVisualDemandRecord(visualSubmitReq, null);
                    }
                }
            }
        });
        return new ArrayList<>();
    }

    private List<String> batchCommitCheck(int reqSpuSize, List<SpotSpu> spuList) {
        if (CollectionUtil.isEmpty(spuList) || spuList.size() != reqSpuSize) {
            throw new SdpDesignException("spu信息不存在，请检查");
        }
        List<SpotSpu> tryOnList = spuList.stream().filter(t -> SupplyModeEnum.TRY_ON.getCode().equals(t.getSupplyModeCode())).collect(Collectors.toList());
        List<SpotSpu> odmList = spuList.stream().filter(t -> SupplyModeEnum.MANUFACTURER.getCode().equals(t.getSupplyModeCode())
                || SupplyModeEnum.IMITATION.getCode().equals(t.getSupplyModeCode())).collect(Collectors.toList());

        List<String> styleCodes = spuList.stream().map(SpotSpu::getStyleCode).collect(Collectors.toList());
        List<SpotSkc> skcList = spotSkcRepository.listByStyleCodes(styleCodes);


        List<String> errorList = new ArrayList<>();
        checkTryOnList(errorList, tryOnList, skcList);
        checkOdmList(errorList, odmList, skcList);
        this.checkSkcColorList(skcList, errorList);

        return errorList;
    }

    private void checkSkcColorList(List<SpotSkc> skcList, List<String> errorList) {
        if (CollUtil.isEmpty(skcList)) {
            return;
        }
        //已完善的skc, 颜色信息不能为空
        List<Long> skcIdList = StreamUtil.convertListAndDistinct(skcList, SpotSkc::getSpotSkcId);
        List<SpotSkcDetail> skcDetailList = spotSkcDetailRepository.listBySkcIds(skcIdList);
        Map<Long, SpotSkcDetail> skcDetailMap = StreamUtil.list2Map(skcDetailList, SpotSkcDetail::getSpotSkcId);
        skcList.forEach(skc -> {
            if (Objects.equals(skc.getResourceStatus(), SpotResourceStateEnum.FINISH.getCode())) {
                SpotSkcDetail skcDetail = skcDetailMap.get(skc.getSpotSkcId());
                if (Objects.isNull(skcDetail)) {
                    errorList.add("异常数据, skc详情为空,skc编码：" + skc.getDesignCode());
                }
                List<tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo> colorInfoList = skcDetail.getColorInfoList();
                if (CollUtil.isEmpty(colorInfoList) || Objects.isNull(colorInfoList.getFirst())) {
                    errorList.add("skc颜色信息异常,请编辑skc重新维护颜色信息,skc编码：" + skc.getDesignCode());
                }
            }
        });
    }

    @Override
    public SpuImageMaterial buildSpuImageMaterial(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            log.warn("根据styleCode查询现货款的素材图片失败，styleCode为空");
            return null;
        }
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        if (Objects.isNull(spotSpu)) {
            log.warn("根据styleCode:{}查询现货款的素材图片失败，现货款不存在",styleCode);
            return null;
        }

        //skc
        List<SpotSkc> spotSkcList = spotSkcRepository.listByStyleCode(styleCode);
        if(CollectionUtil.isEmpty(spotSkcList)){
            log.warn("根据styleCode:{}查询现货款的素材图片失败，现货款skc为空",styleCode);
            return null;
        }

        SpuImageMaterial spuImageMaterial = new SpuImageMaterial();
        spuImageMaterial.setStyleCode(styleCode);
        //spu:商品图, tryOn图, skc图;
        SpotSpuDetail spuDetail = spotSpuDetailRepository.getBySpotSpuId(spotSpu.getSpotSpuId());
        List<String> productPictureList = spuDetail.getProductPictureList();
        List<String> tryOnPictureList = spuDetail.getTryOnPictureList();

        List<Long> spotSkcIdList = StreamUtil.convertListAndDistinct(spotSkcList, SpotSkc::getSpotSkcId);
        List<SpotSkcDetail> skcDetailList = spotSkcDetailRepository.listBySkcIds(spotSkcIdList);
        Map<Long, SpotSkcDetail> skdetailMap = StreamUtil.list2Map(skcDetailList, SpotSkcDetail::getSpotSkcId);
        //数码描稿效果图
        Map<String,List<String>> designCodeToDigitalPaintingMap = sampleClothesRemoteHelper.listSkcDigitalPaintingPictureByStyleCode(styleCode);

        List<SpuImageMaterial.SkcImageMaterial> skcImageMaterialList = spotSkcList.stream().map(skc -> {
            SpuImageMaterial.SkcImageMaterial skcImageMaterial = new SpuImageMaterial.SkcImageMaterial();
            skcImageMaterial.setDesignCode(skc.getDesignCode());
            //商品图
            skcImageMaterial.setProductImages(CollectionUtil.isEmpty(productPictureList) ? null : productPictureList);
            //tryOn图
            skcImageMaterial.setTryOnImages(CollectionUtil.isEmpty(tryOnPictureList) ? null : tryOnPictureList);
            //skc图
            SpotSkcDetail skcDetail = skdetailMap.get(skc.getSpotSkcId());
            if (Objects.nonNull(skcDetail)) {
                StringBuilder sbColor = new StringBuilder();
                StringBuilder sbColorEn = new StringBuilder();
                if(CollectionUtil.isNotEmpty(skcDetail.getColorInfoList())){
                    int i = 0;
                    for (tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo colorInfoVo : skcDetail.getColorInfoList()) {
                        if(i!=0){
                            sbColor.append(" ");
                            sbColorEn.append(" ");
                        }
                        sbColor.append(colorInfoVo.getColor());
                        sbColorEn.append(colorInfoVo.getColorEnglishName());
                        i++;
                    }
                }
                skcImageMaterial.setColor(sbColor.toString());
                skcImageMaterial.setColorEn(sbColorEn.toString());
                if (CollUtil.isNotEmpty(skcDetail.getProductPictureList())) {
                    skcImageMaterial.setDesignImages(skcDetail.getProductPictureList());
                }
            }
            //数码描稿效果图
            skcImageMaterial.setDigitalPaintingPictureList(designCodeToDigitalPaintingMap.get(skc.getDesignCode()));
            return skcImageMaterial;
        }).toList();

        spuImageMaterial.setSkcImageMaterials(skcImageMaterialList);

        return spuImageMaterial;
    }

    @Override
    public Long submitVisualTask(SpotVisualSubmitReq req) {
        String styleCode = req.getStyleCode();
        Assert.isTrue(StrUtil.isNotBlank(styleCode), "现货款styleCode:{}创建视觉任务失败：styleCode is null");
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        SdpDesignException.notNull(spotSpu, "现货款styleCode:{}创建视觉任务失败：spu信息不存在", styleCode);
        SdpDesignException.isFalse(Objects.equals(spotSpu.getIsCanceled(), Bool.YES.getCode()), "现货款styleCode:{}创建视觉任务失败：spu已取消!",styleCode);
        //款式资料未完善，不能发起视觉需求
        SdpDesignException.isTrue(Objects.equals(spotSpu.getResourceStatus(), SpotResourceStateEnum.FINISH.getCode()),VisualErrorCodeEnum.ERROR_99102.getCode());
        //商品图未齐全
        SdpDesignException.isTrue(Objects.equals(spotSpu.getProductPictureStatus(),SpotProductPictureStateEnum.FINISH.getCode()),VisualErrorCodeEnum.ERROR_99100.getCode());
        //如果供给方式是tryOn,还要判断tryOn状态是否为已通过
        if (Objects.equals(spotSpu.getSupplyModeCode(), SupplyModeEnum.TRY_ON.getCode())){
            SdpDesignException.isTrue(Objects.equals(spotSpu.getTryOnStatus(),SpotTryOnStateEnum.PASS.getCode()), VisualErrorCodeEnum.ERROR_99101.getCode());
        }

        SaveVisualDemandBySpuReq demandReq = new SaveVisualDemandBySpuReq();
        BeanUtils.copyProperties(req, demandReq);

        //复色即自动发起视觉需求
        demandReq.setIsAutoInit(req.getIsColorMaking());
        demandReq.setDemandImages(CollUtil.isEmpty(req.getDemandImageList())? null: req.getDemandImageList());
        demandReq.setBackgroundPicList(CollUtil.isEmpty(req.getBackgroundPicList())? null: req.getBackgroundPicList());
        demandReq.setModelPicList(CollUtil.isEmpty(req.getModelPicList())? null: req.getModelPicList());
        demandReq.setPosturePicList(CollUtil.isEmpty(req.getPosturePicList())? null: req.getPosturePicList());
        demandReq.setModelReferenceImageList(req.getModelReferenceImageList());
        demandReq.setBackgroundImageList(req.getBackgroundImageList());
        demandReq.setModelFaceImageList(req.getModelFaceImageList());

        VisualDemand visualDemand = visualDemandService.saveVisualDemandBySpu(demandReq);
        Long visualDemandId = visualDemand.getDemandId();

        submitSpuVisualDemandRecord(req,visualDemandId);
        return visualDemandId;
    }

    @Override
    public SpuVisualDemandRecord submitSpuVisualDemandRecord(SpotVisualSubmitReq req,Long visualDemandId) {
        //未取消的skc
        List<SpotSkc> noCancelSkcList = spotSkcRepository.listNotCancelByStyleCodes(Collections.singletonList(req.getStyleCode()));
        List<String> skcList = StreamUtil.convertListAndDistinct(noCancelSkcList, SpotSkc::getDesignCode);

        //新增需求提交记录
        SpuVisualDemandRecordSaveReq visualDemandRecord = SpuVisualDemandRecordSaveReq.builder()
                .styleCode(req.getStyleCode())
                .spuType(SdpStyleTypeEnum.SPOT.getCode())
                .handleType(req.getSpuVisualHandleType().getCode())
                .demandType(req.getDemandType())
                .visualDemandId(visualDemandId)
                .realObjectColorState(req.getRealObjectColorState())
                .skcList(skcList)
                .demandImageList(req.getDemandImageList())
                .demandDesc(req.getDemandDesc())
                .modelPicList(req.getModelPicList())
                .backgroundPicList(req.getBackgroundPicList())
                .posturePicList(req.getPosturePicList())
                .modelReferenceImageList(req.getModelReferenceImageList())
                .backgroundImageList(req.getBackgroundImageList())
                .modelFaceImageList(req.getModelFaceImageList())
                .build();
        return spuVisualDemandRecordService.create(visualDemandRecord);
    }

    @Override
    public VisualDemandInfoVo queryVisualTaskForEdit(String styleCode) {
        //查询SPU下最新的【上新、优化】视觉任务
        SdpDesignException.notBlank(styleCode, "spu不能为空");
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        SdpDesignException.notNull(spotSpu, "spu信息不存在!");

        //查询SPU下最新的【上新、优化】视觉任务
        // visualDemandService.queryVisualTask(styleCode);
        List<Integer> taskTypeList = List.of(VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode(), VisualTaskTypeEnum.OPTIMIZE_TASK.getCode());
        List<VisualTask> latestVisualTasks = visualTaskRepository.queryLatestHandlingTaskBySpuTaskType(styleCode, taskTypeList);
        if (Objects.nonNull(latestVisualTasks)) {
            //优先返回在途的上新任务
            VisualTask latestVisualTask = latestVisualTasks.stream().filter(v->VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode().equals(v.getTaskType())).findFirst().orElse(null);
            if(latestVisualTask==null){
                latestVisualTask = latestVisualTasks.stream().filter(v->VisualTaskTypeEnum.OPTIMIZE_TASK.getCode().equals(v.getTaskType())).findFirst().orElse(null);
            }
            if(latestVisualTask!=null){
                VisualDemand visualDemand = visualDemandRepository.getById(latestVisualTask.getLatestDemandId());
                VisualDemandInfoVo infoVo = new VisualDemandInfoVo();
                BeanUtils.copyProperties(visualDemand, infoVo);
                this.setDemandPicInfo(visualDemand, infoVo);
                infoVo.setModelReferenceImageList(JSON.parseArray(visualDemand.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
                infoVo.setBackgroundImageList(JSON.parseArray(visualDemand.getBackgroundImage(), BackgroundDTO.class));
                infoVo.setModelFaceImageList(JSON.parseArray(visualDemand.getModelFaceImage(), ModelFaceDTO.class));
                //任务信息
                infoVo.setState(latestVisualTask.getState());
                infoVo.setProcessCode(latestVisualTask.getProcessCode());
                return infoVo;
            }
        }

        //如果无视觉任务, 查询视觉提交记录, 存在则返回spu下最新提交记录
        SpuVisualDemandRecord visualDemandRecord = spuVisualDemandRecordRepository.getLatestBySpu(styleCode);
        if (Objects.nonNull(visualDemandRecord)) {
            VisualDemandInfoVo infoVo = new VisualDemandInfoVo();
            BeanUtils.copyProperties(visualDemandRecord, infoVo);
            infoVo.setModelReferenceImageList(JSON.parseArray(visualDemandRecord.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
            infoVo.setBackgroundImageList(JSON.parseArray(visualDemandRecord.getBackgroundImage(), BackgroundDTO.class));
            infoVo.setModelFaceImageList(JSON.parseArray(visualDemandRecord.getModelFaceImage(), ModelFaceDTO.class));
            return infoVo;
        }

        return null;
    }

    private void checkOdmList(List<String> errorList, List<SpotSpu> odmList, List<SpotSkc> skcList) {
        Map<String, List<SpotSkc>> skcMap = skcList.stream().collect(Collectors.groupingBy(SpotSkc::getStyleCode));
        odmList.forEach(spotSpu -> {
            //资料状态
            if (!Objects.equals(SpotResourceStateEnum.FINISH.getCode(), spotSpu.getResourceStatus())) {
                errorList.add("资料状态状态不为已完善，请检查，spu编码：" + spotSpu.getStyleCode());
            }
            //款式状态
            if (!Objects.equals(SpotStyleStateEnum.WAIT_SUBMIT.getCode(), spotSpu.getStyleStatus())) {
                errorList.add("款式状态不为未提交，请检查，spu编码：" + spotSpu.getStyleCode());
            }
            //检验spu下面的skc是否是有资料是已完善
            ckeckSkcFinish(errorList, spotSpu, skcMap);
        });
    }

    private void ckeckSkcFinish(List<String> errorList, SpotSpu spotSpu, Map<String, List<SpotSkc>> skcMap) {
        if (skcMap.containsKey(spotSpu.getStyleCode())) {
            List<SpotSkc> resourceFinishSpotSkcs = skcMap.get(spotSpu.getStyleCode()).stream()
                    .filter(v -> SpotResourceStateEnum.FINISH.getCode().equals(v.getResourceStatus()))
                    .toList();
            if (CollectionUtils.isEmpty(resourceFinishSpotSkcs)) {
                errorList.add("spu下面的skc信息资料状态全部都是待补充，请检查！spu编码：" + spotSpu.getStyleCode());
            }
        }
    }

    private void checkTryOnList(List<String> errorList, List<SpotSpu> tryOnList, List<SpotSkc> skcList) {
        Map<String, List<SpotSkc>> skcMap = skcList.stream().collect(Collectors.groupingBy(SpotSkc::getStyleCode));
        tryOnList.forEach(spotSpu -> {
            //预估核价状态
            if (!Objects.equals(SpotPriceStateEnum.RE_CHECK_PASS.getCode(), spotSpu.getPredictCheckPriceStatus())) {
                errorList.add("预估核价状态不为复核通过，请检查，spu编码：" + spotSpu.getStyleCode());
            }
            //try on 价状态
            if (!Objects.equals(SpotTryOnStateEnum.PASS.getCode(), spotSpu.getTryOnStatus())) {
                errorList.add("try on状态不为已通过，请检查，spu编码：" + spotSpu.getStyleCode());
            }
            //资料状态
            if (!Objects.equals(SpotResourceStateEnum.FINISH.getCode(), spotSpu.getResourceStatus())) {
                errorList.add("资料状态状态不为已完善，请检查，spu编码：" + spotSpu.getStyleCode());
            }
            //款式状态
            if (!Objects.equals(SpotStyleStateEnum.WAIT_SUBMIT.getCode(), spotSpu.getStyleStatus())) {
                errorList.add("款式状态不为未提交，请检查，spu编码：" + spotSpu.getStyleCode());
            }
            //检验spu下面的skc是否是有资料是已完善
            ckeckSkcFinish(errorList, spotSpu, skcMap);
        });
    }


    private void spuUpdateAfterHandle(SpotSpuUpdateReq req,
                                      String styleCode, SpotSpu spotSpu,
                                      BigDecimal oldMaxPurchasePrice,
                                      UserContent userContent,SpotSpuDetail spuDetail) {
        SpotSpu newSpotSpu = spotSpuRepository.getByStyleCode(styleCode);

        SpotSpuDetail newSpuDetail = spotSpuDetailRepository.getBySpotSpuId(newSpotSpu.getSpotSpuId());

        String platformName = this.getPlatformName(req.getStoreName());

        //同步符合条件的SKC到商品平台
        spotSkcService.pushPopProductBySpotSpu(newSpotSpu);

        //创建视觉需求
        this.addSpotVisualDemandForUpdateSpu(req.getVisualDemandInfo(), styleCode);

        //视觉需求spu更新
        visualSpuService.updateSpu(styleCode, SdpStyleTypeEnum.SPOT, platformName);

        //品类/风格 更新推送运营平台
        this.categoryStyleUpdate2Pop(spotSpu, newSpotSpu);

        this.updateProductAttribute2Pop(styleCode,spuDetail,newSpuDetail);
        this.updateSpuBaseInfo2Pop(newSpotSpu,newSpuDetail);

        //采购价变更推送
        BigDecimal newMaxPurchasePrice = spotSpuSupplierRepository.getMaxPurchasePrice(styleCode);
        if (Objects.nonNull(oldMaxPurchasePrice) && !Objects.equals(oldMaxPurchasePrice, newMaxPurchasePrice)) {
            //因为现货的采购价是在编辑SPU的供应商时录入的，所以这里用当前更新人作为核价师传给POP
            popProductHelper.updateProductPurchasePriceBySpotSpu(newSpotSpu, newMaxPurchasePrice,userContent.getCurrentUserId(),userContent.getCurrentUserName());
        }

        //上传tryOn图后要调一下这个方法同步一下状态给灵感中心
        if (CollUtil.isNotEmpty(req.getTryOnPictureList())) {
            tryOnHelper.uploadPictureForPass(Collections.singletonList(styleCode));
        }

        //通知预估核价，SPU有更新
        sampleClothesRemoteHelper.noticeEstimateCheckPriceUpdateSpotSpu(Collections.singletonList(styleCode));
        this.addLog(spotSpu.getStyleCode(), spotSpu.getSpotSpuId(), DesignLogContentEnum.SPOT_UPDATE_RESOURCE.getDesc());
    }

    private String getPlatformName(String storeName) {
        if (StrUtil.isBlank(storeName)) {
            return null;
        }
        List<ShopResp> shopList = popProductHelper.getShopList(Collections.singletonList(storeName));
        if (CollUtil.isEmpty(shopList)) {
            return null;
        }
        return shopList.getFirst().getPlatformName();
    }

    private void categoryStyleUpdate2Pop(SpotSpu oldSpotSpu, SpotSpu newSpotSpu) {
        //品类/风格 更新推送运营平台
        PopCategoryStyleUpdateTypeEnum updateTypeEnum = null;
        if (!Objects.equals(oldSpotSpu.getCategory(), newSpotSpu.getCategory())
                && !Objects.equals(oldSpotSpu.getClothingStyleCode(), newSpotSpu.getClothingStyleCode())) {
            updateTypeEnum = PopCategoryStyleUpdateTypeEnum.CATEGORY_AND_STYLE;
        } else {
            if (!Objects.equals(oldSpotSpu.getCategory(), newSpotSpu.getCategory())) {
                updateTypeEnum = PopCategoryStyleUpdateTypeEnum.CATEGORY;
            } else if (!Objects.equals(oldSpotSpu.getClothingStyleCode(), newSpotSpu.getClothingStyleCode())) {
                updateTypeEnum = PopCategoryStyleUpdateTypeEnum.STYLE;
            }
        }
        if (Objects.nonNull(updateTypeEnum)) {
            popProductHelper.updateProductCategoryBySpotSpu(newSpotSpu, updateTypeEnum);
        }
    }


    private void updateProductAttribute2Pop(String styleCode, SpotSpuDetail oldSpu, SpotSpuDetail newSpu) {
        //商品属性 更新推送运营平台
        if(Objects.isNull(oldSpu.getAttributes()) && Objects.isNull(newSpu.getAttributes())){
            return;
        }

        if (Objects.isNull(newSpu.getAttributes())) {
            return;
        }

        if ((Objects.isNull(oldSpu.getAttributes()) && Objects.nonNull(newSpu.getAttributes())) ||
                (oldSpu.getAttributes().size() != newSpu.getAttributes().size())
                || !JSON.toJSONString(oldSpu.getAttributes()).equals(JSON.toJSONString(newSpu.getAttributes()))) {
            popProductHelper.updateProductAttribute(styleCode, newSpu.getAttributes());
        }
    }

    @Override
    public void batchUpdateProductAttribute2Pop(List<String> styleCodes){
        List<SpotSpu> spotSpuList = spotSpuRepository.getAllocationListByStyleCodes(styleCodes);
        List<Long> spotIds = spotSpuList.stream().map(SpotSpu::getSpotSpuId).toList();
        List<SpotSpuDetail> spotSpuDetailList = spotSpuDetailRepository.listBySpotSpuIds(spotIds);
        Map<Long, SpotSpuDetail> spotSpuDetailMap = spotSpuDetailList.stream().collect(Collectors.toMap(SpotSpuDetail::getSpotSpuId, v -> v));
        for( SpotSpu spotSpu : spotSpuList){
            popProductHelper.updateProductAttribute(spotSpu.getStyleCode(), spotSpuDetailMap.get(spotSpu.getSpotSpuId()).getAttributes());
        }

    }

    private void updateSpuBaseInfo2Pop(SpotSpu spotSpu, SpotSpuDetail spotSpuDetail) {
        // 基本信息更新推送运营平台
        ProductUpdateMqDto.SpuBaseInfo spuBaseInfo = ProductUpdateMqDto.SpuBaseInfo.builder()
                .clothingStyleName(spotSpu.getClothingStyleName())
                .clothingStyleCode(spotSpu.getClothingStyleCode())
                .planningType(spotSpu.getPlanningType())
                .marketCode(spotSpu.getMarketCode())
                .marketSeriesCode(spotSpu.getMarketSeriesCode())
                .waves(spotSpu.getWaveBandName()) // 使用波段名称作为波次
                .goodsRepType(spotSpu.getPalletTypeName()) // 货盘类型名称
                .weaveModeCode(spotSpu.getWeaveModeCode())
                .weaveMode(spotSpu.getWeaveMode())
                .qualityLevel(spotSpu.getQualityLevel())
                .qualityLevelCode(spotSpu.getQualityLevelCode())
                .productThemeCode(spotSpu.getProductThemeCode())
                .productThemeName(spotSpu.getProductThemeName())
                .shopId(spotSpu.getStoreId())
                .shopName(spotSpu.getStoreName())
                .countrys(Collections.singletonList(spotSpu.getCountrySiteCode())) // 将单个国家站点转为列表
                .spotTypeCode(spotSpu.getSpotTypeCode())
                .buyerId(spotSpu.getBuyerId())
                .buyerName(spotSpu.getBuyerName())
                .productLink(spotSpuDetail.getProductLink()) // 从SpotSpuDetail获取商品链接
                .spuName(spotSpu.getSpuName())
                .spuNameTrans(spotSpu.getSpuNameTrans())
                .build();

        popProductHelper.updateProductBaseInfo(spotSpu.getStyleCode(),spuBaseInfo);
    }

    private void supplierCheck4Update(SpotSpuUpdateReq req, SpotSpu spotSpu) {
        //供应商款号：输入框，供给方式=现货try on时必填；供给方式=ODM时非必填；
        if (Objects.equals(spotSpu.getSupplyModeCode(), SupplyModeEnum.TRY_ON.getCode())) {
            req.getSupplierAddList().forEach(item -> SdpDesignException.notBlank(item.getSupplierStyle(), "现货try on供应商款号必填!"));
            req.getSupplierUpdateInfoList().forEach(item -> SdpDesignException.notBlank(item.getSupplierStyle(), "现货try on供应商款号必填!"));
        }
        List<String> supplierStyleList = new ArrayList<>();
        List<String> supplierNameList = new ArrayList<>();
        req.getSupplierAddList().forEach(item -> {
            if (StrUtil.isNotBlank(item.getSupplierStyle())) {
                supplierStyleList.add(item.getSupplierStyle());
            }
            if (StrUtil.isNotBlank(item.getSupplierName())) {
                supplierNameList.add(item.getSupplierName());
            }
        });
        req.getSupplierUpdateInfoList().forEach(item -> {
            if (StrUtil.isNotBlank(item.getSupplierStyle())) {
                supplierStyleList.add(item.getSupplierStyle());
            }
            if (StrUtil.isNotBlank(item.getSupplierName())) {
                supplierNameList.add(item.getSupplierName());
            }
        });
        if (CollUtil.isEmpty(supplierNameList) || CollUtil.isEmpty(supplierStyleList)) {
            return;
        }
        //「供应商名称+供应商款号」是否已存在SPU，存在时高亮对应行，并提示：该款号已存在SPU，请勿重复添加。
        List<SpotSpuSupplier> oldSupplierList = spotSpuSupplierRepository.listBySupplierNameStyle(supplierNameList, supplierStyleList);
        Map<String, SpotSpuSupplier> oldSupplierMap = StreamUtil.list2Map(oldSupplierList, SpotSpuSupplier::getSupplierNameStyle);
        if (CollUtil.isNotEmpty(oldSupplierList)) {
            req.getSupplierAddList().forEach(item -> {
                SpotSpuSupplier oldSupplier = oldSupplierMap.get(item.getSupplierNameStyle());
                SdpDesignException.isFalse(Objects.nonNull(oldSupplier)
                                && !Objects.equals(oldSupplier.getStyleCode(), spotSpu.getStyleCode()),
                        "该款号已存在SPU，请勿重复添加; 供应商名称:{}; 款号:{}", item.getSupplierName(), item.getSupplierStyle());
            });
            req.getSupplierUpdateInfoList().forEach(item -> {
                SpotSpuSupplier oldSupplier = oldSupplierMap.get(item.getSupplierNameStyle());
                SdpDesignException.isFalse(Objects.nonNull(oldSupplier)
                                && !Objects.equals(oldSupplier.getStyleCode(), spotSpu.getStyleCode()),
                        "该款号已存在SPU，请勿重复添加; 供应商名称:{}; 款号:{}", item.getSupplierName(), item.getSupplierStyle());
            });
        }
    }

    private void setProductResourceState(SpotSpuUpdateReq req, SpotSpu spotSpu, SpotSpuUpdateDto spotSpuUpdateDto, UserContent userContent) {
        //资料状态变为已完善; SPU资料由待补充变更为已完善时，记录对应操作用户为开发人；
        if (!Objects.equals(spotSpu.getResourceStatus(), SpotResourceStateEnum.FINISH.getCode())) {
            spotSpuUpdateDto.setResourceStatus(SpotResourceStateEnum.FINISH.getCode());
            spotSpuUpdateDto.setDeveloperId(userContent.getCurrentUserId());
            spotSpuUpdateDto.setDeveloperName(userContent.getCurrentUserName());
        }
        //商品图状态
        if (CollUtil.isNotEmpty(req.getProductPictureList())) {
            spotSpuUpdateDto.setProductPictureStatus(SpotProductPictureStateEnum.FINISH.getCode());
        } else {
            spotSpuUpdateDto.setProductPictureStatus(SpotProductPictureStateEnum.WAIT_COMPLETE.getCode());
        }
        //tryOn图状态修改
        if (CollUtil.isNotEmpty(req.getTryOnPictureList())) {
            log.info("编辑SPU变更tryOn状态为已通过styleCode:" + spotSpu.getStyleCode());
            //原来不是审核通过的
            if (!Objects.equals(SpotTryOnStateEnum.PASS.getCode(), spotSpu.getTryOnStatus())) {
                spotSpuUpdateDto.setTryOnAuditTime(LocalDateTime.now());
                spotSpuUpdateDto.setUpdateTryOnAuditTimeFlag(true);
            }
            spotSpuUpdateDto.setTryOnStatus(SpotTryOnStateEnum.PASS.getCode());
        } else {
            //已通过后, 删除tryOn图:
            if (Objects.equals(SpotTryOnStateEnum.PASS.getCode(), spotSpu.getTryOnStatus())) {
                spotSpuUpdateDto.setTryOnAuditTime(null);
                spotSpuUpdateDto.setUpdateTryOnAuditTimeFlag(true);
                //未进行tryOn分配
                if (Objects.isNull(spotSpu.getTryOnUserId())) {
                    log.info("编辑SPU变更tryOn状态为待分配styleCode:" + spotSpu.getStyleCode());
                    spotSpuUpdateDto.setTryOnStatus(SpotTryOnStateEnum.WAIT_ALLOCATE.getCode());
                } else {
                    log.info("编辑SPU变更tryOn状态为待创建styleCode:" + spotSpu.getStyleCode());
                    spotSpuUpdateDto.setTryOnStatus(SpotTryOnStateEnum.WAIT_CREATE.getCode());
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpotColorMakingVo colorMaking(SpotColorMakingReq req) {
        log.info("=== 现货复色 req:{} ===", JSONObject.toJSONString(req));
        String styleCode = req.getStyleCode();
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        SdpDesignException.notNull(spotSpu, "spu不存在!");
        SdpDesignException.isFalse(Objects.equals(spotSpu.getIsCanceled(), Bool.YES.getCode()), "spu已取消!");
        SpotSkcCreateReq skcCreateReq = req.getSkcCreateReq();
        Boolean sameColor = spotSkcService.checkSpuSameColor(styleCode, skcCreateReq.getColor());
        SdpDesignException.isFalse(sameColor, "存在颜色重复的SKC，请勿重复创建!");

        //新建复色skc
        SpotSkc spotSkc = spotSkcService.colorMaking(req, spotSpu);

        //现货SPU复色成功就同步到商品运营平台
        spotSkcService.pushPopProductBySpotSpu(spotSpu);

        //复色创建视觉需求
        try {
            SpotVisualSubmitReq visualSubmitReq = new SpotVisualSubmitReq();
            visualSubmitReq.setStyleCode(styleCode);
            //自动逻辑为在需求描述中回车一行增加【复色SKCxxxxx、SKCxxxxxx】
            String demandDesc = "【复色" + spotSkc.getDesignCode() + "】";
            visualSubmitReq.setDemandDesc(demandDesc);
            visualSubmitReq.setIsColorMaking(true);
            visualSubmitReq.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_COLOR);
            SpotVisualDemandReq visualDemandInfo = req.getVisualDemandInfo();
            if (Objects.nonNull(visualDemandInfo)) {
                visualSubmitReq.setModelReferenceImageList(visualDemandInfo.getModelReferenceImageList());
                visualSubmitReq.setBackgroundImageList(visualDemandInfo.getBackgroundImageList());
                visualSubmitReq.setModelFaceImageList(visualDemandInfo.getModelFaceImageList());
            }
            this.submitVisualTask(visualSubmitReq);
        }catch (Exception e) {
            log.error("现货复色创建视觉需求失败",e);
        }

        // 更新视觉SPU多色款状态
        visualSpuService.updateMultiColor(styleCode, Bool.YES.getCode());

        SpotColorMakingVo colorMakingVo = new SpotColorMakingVo();
        BeanUtils.copyProperties(spotSkc, colorMakingVo);

        //操作日志
        this.addLog(styleCode, spotSpu.getSpotSpuId(), DesignLogContentEnum.SPOT_COLOR_MAKING.getDesc() + spotSkc.getColor());

        log.info("=== 现货复色 成功:{} ===", JSONObject.toJSONString(colorMakingVo));
        return colorMakingVo;
    }

    private void addSpotVisualDemandForSelfCreate(SpotVisualDemandReq visualDemandInfo, String styleCode) {
        log.info("新建现货款号后创建视觉需求，参数：visualDemandInfo:{},styleCode:{}", JSONObject.toJSONString(visualDemandInfo), styleCode);
        if (StrUtil.isBlank(styleCode)) {
            log.error("新建现货款号后创建视觉需求失败，styleCode为空");
            return;
        }
        try{
            SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
            SdpDesignException.notNull(spotSpu, "spu信息不存在:{}", styleCode);
            SdpDesignException.isFalse(Objects.equals(spotSpu.getIsCanceled(), Bool.YES.getCode()), "spu已取消!");
            //未取消的skc
            List<SpotSkc> noCancelSkcList = spotSkcRepository.listNotCancelByStyleCodes(Collections.singletonList(styleCode));
            List<String> skcList = StreamUtil.convertListAndDistinct(noCancelSkcList, SpotSkc::getDesignCode);

            //商品图片未齐全，先保存视觉需求
            if(!Objects.equals(spotSpu.getProductPictureStatus(), SpotProductPictureStateEnum.FINISH.getCode())){
                //新增需求提交记录
                SpuVisualDemandRecordSaveReq visualDemandRecord = SpuVisualDemandRecordSaveReq.builder()
                        .styleCode(styleCode)
                        .spuType(SdpStyleTypeEnum.SPOT.getCode())
                        .handleType(SpuVisualHandleTypeEnum.SPOT_SELF_CREATE.getCode())
                        .demandType(visualDemandInfo!=null ? visualDemandInfo.getDemandType() : null)
                        .visualDemandId(null)
                        .realObjectColorState(visualDemandInfo!=null ? visualDemandInfo.getRealObjectColorState() : null)
                        .skcList(skcList)
                        .demandImageList(visualDemandInfo!=null ? visualDemandInfo.getDemandImageList() : null)
                        .demandDesc(visualDemandInfo!=null ? visualDemandInfo.getDemandDesc() : null)
                        .modelPicList(visualDemandInfo!=null ? visualDemandInfo.getModelPicList() : null)
                        .backgroundPicList(visualDemandInfo!=null ? visualDemandInfo.getBackgroundPicList() : null)
                        .posturePicList(visualDemandInfo!=null ? visualDemandInfo.getPosturePicList() : null)
                        .modelReferenceImageList(visualDemandInfo!=null ? visualDemandInfo.getModelReferenceImageList() : null)
                        .backgroundImageList(visualDemandInfo!=null ? visualDemandInfo.getBackgroundImageList() : null)
                        .modelFaceImageList(visualDemandInfo!=null ? visualDemandInfo.getModelFaceImageList() : null)
                        .build();
                spuVisualDemandRecordService.create(visualDemandRecord);
            }else{
                //创建视觉需求
                SpotVisualSubmitReq visualSubmitReq = new SpotVisualSubmitReq();
                visualSubmitReq.setStyleCode(styleCode);
                if(visualDemandInfo!=null){
                    BeanUtils.copyProperties(visualDemandInfo, visualSubmitReq);
                }
                visualSubmitReq.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_SELF_CREATE);
                this.submitVisualTask(visualSubmitReq);
            }
        }catch (Exception e){
            log.error("自建现货款，创建视觉需求失败", e);
        }
    }

    private void addSpotVisualDemandForUpdateSpu(SpotVisualDemandReq visualDemandInfo, String styleCode) {
        log.info("更新现货SPU创建视觉需求,参数：visualDemandInfo:{},styleCode:{}", JSONObject.toJSONString(visualDemandInfo), styleCode);
        if (StrUtil.isBlank(styleCode)) {
            log.info("更新现货SPU创建视觉需求失败，styleCode为空");
            return;
        }
        SpotVisualSubmitReq visualSubmitReq = null;
        //创建视觉需求，前端有传需求内容
        if (visualDemandInfo != null) {
            visualSubmitReq = new SpotVisualSubmitReq();
            BeanUtils.copyProperties(visualDemandInfo, visualSubmitReq);
            visualSubmitReq.setStyleCode(styleCode);
            visualSubmitReq.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_SUBMIT);
        }
        //前端没有传需求内容，但系统内有一条待推送的需求
        else {
            //spu下最新记录
            SpuVisualDemandRecord oldDemandRecord = spuVisualDemandRecordRepository.getLatestBySpu(styleCode);
            //有待推送的视觉需求
            if (oldDemandRecord != null && oldDemandRecord.getVisualDemandId() == null) {
                visualSubmitReq = new SpotVisualSubmitReq();
                BeanUtils.copyProperties(oldDemandRecord, visualSubmitReq);
                visualSubmitReq.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_SUBMIT);
                visualSubmitReq.setModelReferenceImageList(JSON.parseArray(oldDemandRecord.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
                visualSubmitReq.setBackgroundImageList(JSON.parseArray(oldDemandRecord.getBackgroundImage(), BackgroundDTO.class));
                visualSubmitReq.setModelFaceImageList(JSON.parseArray(oldDemandRecord.getModelFaceImage(), ModelFaceDTO.class));
            }
        }
        if(visualSubmitReq!=null){
            try {
                this.submitVisualTask(visualSubmitReq);
            } catch (Exception e) {
                log.error("更新现货SPU创建视觉需求失败", e);
                //如果是未符合发起条件，则先保存需求
                VisualErrorCodeEnum visualErrorCode = VisualErrorCodeEnum.findByCode(e.getMessage());
                if (!VisualErrorCodeEnum.UNKNOWN.equals(visualErrorCode)) {
                    submitSpuVisualDemandRecord(visualSubmitReq, null);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSpu(List<String> spuList) {
        log.info("=== 现货取消spu req:{} ===", JSONObject.toJSONString(spuList));
        SdpDesignException.notEmpty(spuList, "取消spu为空!");
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(spuList);
        Map<String, SpotSpu> spotSpuMap = StreamUtil.list2Map(spotSpuList, SpotSpu::getStyleCode);

        //更新spu取消
        List<SpotSpu> cancelSpuList = new ArrayList<>(spuList.size());
        List<String> cancelStyleCodeList = new ArrayList<>();
        spuList.forEach(styleCode -> {
            SpotSpu spotSpu = spotSpuMap.get(styleCode);
            SdpDesignException.notNull(spotSpu, "spu不存在! {}", styleCode);
            //若spu已取消 不处理
            // SdpDesignException.isFalse(Objects.equals(spotSpu.getIsCanceled(), Bool.YES.getCode()), "spu已取消! {}", styleCode);
            if (Objects.equals(spotSpu.getIsCanceled(), Bool.YES.getCode())) {
                return;
            }
            SpotSpu cancelSpu = new SpotSpu();
            cancelSpu.setSpotSpuId(spotSpu.getSpotSpuId());
            cancelSpu.setIsCanceled(Bool.YES.getCode());
            cancelSpuList.add(cancelSpu);
            cancelStyleCodeList.add(styleCode);
        });
        if (CollUtil.isEmpty(cancelSpuList)) {
            return;
        }
        spotSpuRepository.updateBatchById(cancelSpuList);

        TransactionUtil.afterCompletion(() -> {
            applicationContext.publishEvent(new CancelSpotSpuEvent(this, cancelStyleCodeList));
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SpotSpuUploadPictureResultVo> uploadPictureByMatchingType(UploadPictureReq req) {
        List<String> fileNames = req.getImageInfoList().stream().map(SpotSpuUploadPictureDto::getFileName).collect(Collectors.toList());
        Map<String, SpotSpuUploadPictureDto> fileNameMap = StreamUtil.list2Map(req.getImageInfoList(), SpotSpuUploadPictureDto::getFileName);

        //大于图片限制数量的spu信息
        int pictureLimitNum = req.getPictureLimitNumber();
        Map<String, SpotSpuUploadPictureDto> overMap = fileNameMap.entrySet().stream()
                .filter(entry -> entry.getValue().getImageUrls().size() > pictureLimitNum)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        List<SpotSpuUploadPictureResultVo> allList = new ArrayList<>();
        List<SpotSpuUploadPictureResultVo> uploadResultFailList = new ArrayList<>();
        List<SpotSpuUploadPictureResultVo> uploadResultSuccessList = new ArrayList<>();
        List<String> sucessList = new ArrayList<>();
        //获取已存在的spu数组
        List<String> exitSpu = new ArrayList<>();

        List<String> notExitList = new ArrayList<>();
        //超过数量限制的图片的spu进行错误提示
        uploadResultFailList.addAll(overMapDeal(overMap, pictureLimitNum));

        if (!overMap.isEmpty()) {
            fileNames = fileNames.stream().filter(t -> !overMap.containsKey(t)).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(fileNames)) {
            // 匹配方式,1：SPU编号，2：供应商款号
            switch (req.getMatchingType()) {
                case 1:
                    exitSpu = spotSpuRepository.selectByStyleCodes(fileNames)
                            .stream().map(SpotSpuVo::getStyleCode).collect(Collectors.toList());
                    //处理不存在的spu数据
                    List<String> finalExitSpu = exitSpu;
                    notExitList = CollectionUtil.isEmpty(exitSpu) ? fileNames : fileNames.stream()
                            .filter(fileName -> !finalExitSpu.contains(fileName))
                            .collect(Collectors.toList());
                    sucessList = exitSpu;
                    break;
                case 2:
                    Map<String, SpotSpuUploadPictureDto> allMap = new HashMap<>(fileNameMap);
                    fileNameMap.clear();
                    checkMessage(req);
                    for (String fileName : fileNames) {
                        List<SpotSpuSupplier> suppliers = spotSpuSupplierRepository.selectBySupplier(fileName, req.getSupplierName());
                        if (suppliers.isEmpty()) {
                            notExitList.add(fileName);
                        } else {
                            //供应商名称+供应商款号就可以确定spu
                            List<String> styleCodes = suppliers.stream().map(SpotSpuSupplier::getStyleCode).toList();
                            exitSpu.addAll(styleCodes);
                            sucessList.add(fileName);
                            fileNameMap.put(styleCodes.getFirst(), allMap.get(fileName));
                        }
                    }
                    break;
                default:
                    exitSpu = Collections.emptyList();
            }
        }

        if (!notExitList.isEmpty()) {
            uploadResultFailList.addAll(dealIsExit(notExitList, 0));
        }
        //处理已存在的spu数据
        if (!exitSpu.isEmpty() && !sucessList.isEmpty()) {
            uploadResultSuccessList.addAll(dealIsExit(sucessList, 1));

            //更新主表spot_spu状态
            updateSpuStateByUploadPicture(req.getPictureType(), exitSpu);

            //更新spot_spu_detail图片信息
            updateSpotSpuDetailPicture(req.getPictureType(), exitSpu, fileNameMap);
        }
        allList.addAll(uploadResultFailList);
        allList.addAll(uploadResultSuccessList);

        return allList;
    }
//    public  File fileConvert(MultipartFile multipartFile,String fileName)  {
//        // 创建一个临时文件
//        String tempDirStr = FileUtils.getTempDirectoryPath() + File.separator +
//                "spotSpu_upload"  + File.separator +
//                DateTimeFormatter.ofPattern(DatePatternConstants.PURE_DATETIME_PATTERN).format(LocalDateTime.now());
//        File tempFile = null;
//        try {
//            File tempDir = new File(tempDirStr);
//            FileUtils.forceMkdir(tempDir);
//            tempFile = File.createTempFile(fileName.substring(0, fileName.lastIndexOf(".") ), fileName.substring(fileName.lastIndexOf(".")),tempDir);
//            // 将 MultipartFile 写入临时文件
//            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
//                fos.write(multipartFile.getBytes());
//            }
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        return tempFile;
//    }
    /**
     * 导入图包
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<SpotSpuUploadExcelResultVo> uploadExcelSync(UploadExcelReq req){
        Assert.isTrue(req.getPictureType() == 1, "只支持商品图片导入");
        List<SpotSpuUploadExcelResultVo> excelResultVos = new ArrayList<>();
//        FileUploadDTO fileUploadDTO = uploaderOssHelper.createFileUploadDTO(fileConvert(req.getFile(),req.getFileName()), MediaType.MICROSOFT_EXCEL.type());
        if (req.getMatchingType() == 1) {
            // spu
            List<SpotUploadExcelSpuDto> spuDtoList = getSpotUploadExcelSpuDtos(req, SpotUploadExcelSpuDto.class);
            // 导入每行校验
            List<SpotSpuVo> spotSpuList = spotSpuRepository.selectByStyleCodes(spuDtoList.stream().map(SpotUploadExcelSpuDto::getSpuCode).filter(Objects::nonNull).toList());
            Map<String, SpotSpuVo> spotSpuMap = spotSpuList.stream().collect(Collectors.toMap(SpotSpuVo::getStyleCode, Function.identity(), (k1, k2) -> k2));
            validSpuRow(spuDtoList, excelResultVos,spotSpuMap);
            if (CollectionUtil.isNotEmpty(excelResultVos)) {
                return excelResultVos;
            }
            // 校验上传图片数量
            validSpuPicNumber(spuDtoList);

            // 保存
            SpotSpuImportTask spotSpuImportTask = new SpotSpuImportTask();
            spotSpuImportTask.setPictureType(req.getPictureType());
            spotSpuImportTask.setMatchingType(req.getMatchingType());
            spotSpuImportTask.setFileUrl(req.getFileUrl());
            spotSpuImportTask.setFileName(req.getFileName());
            UserContent userContent = UserContentHolder.get();
            spotSpuImportTask.setCreatorName(userContent.getCurrentUserName());
            spotSpuImportTask.setReviserName(userContent.getCurrentUserName());
            spotSpuImportTask.setReviserId(userContent.getCurrentUserId());
            LocalDateTime now = LocalDateTime.now();
            spotSpuImportTask.setRevisedTime(now);
            spotSpuImportTask.setIsDeleted(cn.yibuyun.framework.enumeration.Bool.NO.getCode());
            spotSpuImportTask.setCreatorId(userContent.getCurrentUserId());
            spotSpuImportTask.setCreatedTime(now);
            spotSpuImportTaskRepository.save(spotSpuImportTask);
            List<SpotSpuImportTaskDetail> taskDetails = new ArrayList<>();
            for (int i = 0; i < spuDtoList.size(); i++) {
                SpotUploadExcelSpuDto dto = spuDtoList.get(i);
                if (StrUtil.isNotBlank(dto.getProductPictureUrlJson())) {
                    List<String> productPictureUrlList = JSONObject.parseArray(dto.getProductPictureUrlJson(), String.class);

                    for (String productPictureUrl : productPictureUrlList) {
                        SpotSpuImportTaskDetail taskDetail = new SpotSpuImportTaskDetail();
                        taskDetail.setTaskId(spotSpuImportTask.getId());
                        taskDetail.setSpuCode(dto.getSpuCode());
                        taskDetail.setPicUrl(productPictureUrl);
                        taskDetail.setPicMd5(Md5Utils.getMD5(productPictureUrl.getBytes(StandardCharsets.UTF_8)));
                        taskDetail.setExcelRow(i + 1);
                        taskDetail.setCreatorName(userContent.getCurrentUserName());
                        taskDetail.setReviserName(userContent.getCurrentUserName());
                        taskDetail.setReviserId(userContent.getCurrentUserId());
                        taskDetail.setRevisedTime(now);
                        taskDetail.setIsDeleted(cn.yibuyun.framework.enumeration.Bool.NO.getCode());
                        taskDetail.setCreatorId(userContent.getCurrentUserId());
                        taskDetail.setCreatedTime(now);
                        taskDetails.add(taskDetail);
                    }
                }
                if (StrUtil.isNotBlank(dto.getSizePictureUrl())) {
                    SpotSpuImportTaskDetail taskDetail = new SpotSpuImportTaskDetail();
                    taskDetail.setTaskId(spotSpuImportTask.getId());
                    taskDetail.setExcelRow(i + 1);
                    taskDetail.setSpuCode(dto.getSpuCode());
                    taskDetail.setPicUrl(dto.getSizePictureUrl());
                    taskDetail.setPicMd5(Md5Utils.getMD5(dto.getSizePictureUrl().getBytes(StandardCharsets.UTF_8)));
                    taskDetail.setCreatorName(userContent.getCurrentUserName());
                    taskDetail.setReviserName(userContent.getCurrentUserName());
                    taskDetail.setReviserId(userContent.getCurrentUserId());
                    taskDetail.setRevisedTime(now);
                    taskDetail.setIsDeleted(cn.yibuyun.framework.enumeration.Bool.NO.getCode());
                    taskDetail.setCreatorId(userContent.getCurrentUserId());
                    taskDetail.setCreatedTime(now);
                    taskDetails.add(taskDetail);
                }
            }
            spotSpuImportTaskDetailRepository.saveBatch(taskDetails);
        }else if(req.getMatchingType() == 2){
            // 供应商款号
            List<SpotUploadExcelSupplyDto> supplyDtoList = getSpotUploadExcelSpuDtos(req,SpotUploadExcelSupplyDto.class);
            // 导入每行校验
            List<SpotSpuSupplier> supplierStyles = spotSpuSupplierRepository.selectBySupplierStyles(supplyDtoList.stream().map(SpotUploadExcelSupplyDto::getSupplierStyle).filter(Objects::nonNull).toList());
            Map<String, SpotSpuSupplier> supplierMap = supplierStyles.stream().collect(Collectors.toMap(e -> e.getSupplierStyle()+"-"+e.getSupplierName(), Function.identity(), (k1, k2) -> k2));
            validSupplyRow(supplyDtoList, excelResultVos,supplierMap);
            if(CollectionUtil.isNotEmpty(excelResultVos)){
                return excelResultVos;
            }
            // 校验上传图片数量
            validSupplyPicNumber(supplyDtoList);

            // 保存
            SpotSpuImportTask spotSpuImportTask = new SpotSpuImportTask();
            spotSpuImportTask.setPictureType(req.getPictureType());
            spotSpuImportTask.setMatchingType(req.getMatchingType());
            spotSpuImportTask.setFileUrl(req.getFileUrl());
            spotSpuImportTask.setFileName(req.getFileName());
            UserContent userContent = UserContentHolder.get();
            spotSpuImportTask.setCreatorName(userContent.getCurrentUserName());
            spotSpuImportTask.setReviserName(userContent.getCurrentUserName());
            spotSpuImportTask.setReviserId(userContent.getCurrentUserId());
            LocalDateTime now = LocalDateTime.now();
            spotSpuImportTask.setRevisedTime(now);
            spotSpuImportTask.setIsDeleted(cn.yibuyun.framework.enumeration.Bool.NO.getCode());
            spotSpuImportTask.setCreatorId(userContent.getCurrentUserId());
            spotSpuImportTask.setCreatedTime(now);
            spotSpuImportTaskRepository.save(spotSpuImportTask);
            List<SpotSpuImportTaskDetail> taskDetails = new ArrayList<>();
            for(int i = 0; i< supplyDtoList.size(); i++){
                SpotUploadExcelSupplyDto dto = supplyDtoList.get( i);
                SpotSpuSupplier spotSpuSupplier = supplierMap.get(dto.getSupplierStyle()+"-"+dto.getSupplierName());
                if(StrUtil.isNotBlank(dto.getProductPictureUrlJson())){
                    List<String> productPictureUrlList = JSONObject.parseArray(dto.getProductPictureUrlJson(), String.class);
                    for(String productPictureUrl : productPictureUrlList){
                        SpotSpuImportTaskDetail taskDetail = new SpotSpuImportTaskDetail();
                        taskDetail.setTaskId(spotSpuImportTask.getId());
                        taskDetail.setSpuCode(spotSpuSupplier.getStyleCode());
                        taskDetail.setSupplierStyle(dto.getSupplierStyle());
                        taskDetail.setSupplierName(dto.getSupplierName());
                        taskDetail.setPicUrl(productPictureUrl);
                        taskDetail.setPicMd5(Md5Utils.getMD5(productPictureUrl.getBytes(StandardCharsets.UTF_8)));
                        taskDetail.setCreatorName(userContent.getCurrentUserName());
                        taskDetail.setReviserName(userContent.getCurrentUserName());
                        taskDetail.setReviserId(userContent.getCurrentUserId());
                        taskDetail.setRevisedTime(now);
                        taskDetail.setIsDeleted(cn.yibuyun.framework.enumeration.Bool.NO.getCode());
                        taskDetail.setCreatorId(userContent.getCurrentUserId());
                        taskDetail.setCreatedTime(now);
                        taskDetail.setExcelRow(i+1);
                        taskDetails.add(taskDetail);
                    }
                }
                if(StrUtil.isNotBlank(dto.getSizePictureUrl())){
                    SpotSpuImportTaskDetail taskDetail = new SpotSpuImportTaskDetail();
                    taskDetail.setTaskId(spotSpuImportTask.getId());
                    taskDetail.setExcelRow(i+1);
                    taskDetail.setSupplierStyle(dto.getSupplierStyle());
                    taskDetail.setSupplierName(dto.getSupplierName());
                    taskDetail.setSpuCode(spotSpuSupplier.getStyleCode());
                    taskDetail.setPicUrl(dto.getSizePictureUrl());
                    taskDetail.setPicMd5(Md5Utils.getMD5(dto.getSizePictureUrl().getBytes(StandardCharsets.UTF_8)));
                    taskDetail.setCreatorName(userContent.getCurrentUserName());
                    taskDetail.setReviserName(userContent.getCurrentUserName());
                    taskDetail.setReviserId(userContent.getCurrentUserId());
                    taskDetail.setRevisedTime(now);
                    taskDetail.setIsDeleted(cn.yibuyun.framework.enumeration.Bool.NO.getCode());
                    taskDetail.setCreatorId(userContent.getCurrentUserId());
                    taskDetail.setCreatedTime(now);
                    taskDetails.add(taskDetail);
                }
            }
            spotSpuImportTaskDetailRepository.saveBatch(taskDetails);
        }
        return null;
    }

    private void validSupplyRow(List<SpotUploadExcelSupplyDto> supplyDtoList, List<SpotSpuUploadExcelResultVo> excelResultVos,Map<String, SpotSpuSupplier> supplierMap) {
        // 每行校验
        for(int i = 0; i< supplyDtoList.size(); i++){
            SpotUploadExcelSupplyDto supplyDto = supplyDtoList.get( i);
            if(StrUtil.isBlank(supplyDto.getSupplierStyle())){
                excelResultVos.add(buildErrInfo(i+1, "供应商款号不能为空"));
            }
            if(StrUtil.isBlank(supplyDto.getSupplierName())){
                excelResultVos.add(buildErrInfo(i+1, "供应商名称不能为空"));
            }
            if(supplierMap.get(supplyDto.getSupplierStyle()+"-"+supplyDto.getSupplierName()) == null){
                excelResultVos.add(buildErrInfo(i+1, String.format("供应商款号不存在: %s-%s", supplyDto.getSupplierStyle(), supplyDto.getSupplierName())));
            }
            if(StrUtil.isNotEmpty(supplyDto.getProductPictureUrlJson())){
                // 校验是否为json数组结构[]
                try{
                    JSON.parseArray(supplyDto.getProductPictureUrlJson(), String.class);
                } catch (Exception e){
                    excelResultVos.add(buildErrInfo(i+1, "商品图格式错误"));
                }
            }
        }
    }

    private void validSpuRow(List<SpotUploadExcelSpuDto> spuDtoList, List<SpotSpuUploadExcelResultVo> excelResultVos, Map<String, SpotSpuVo> spotSpuMap) {
        // 每行校验
        for(int i = 0; i< spuDtoList.size(); i++){
            SpotUploadExcelSpuDto spuDto = spuDtoList.get( i);
            if(StrUtil.isBlank(spuDto.getSpuCode())){
                excelResultVos.add(buildErrInfo(i+1, "SPU编码不能为空"));
            }
            if(spotSpuMap.get(spuDto.getSpuCode()) == null){
                excelResultVos.add(buildErrInfo(i+1, String.format("SPU编码不存在: %s", spuDto.getSpuCode())));
            }
            if(StrUtil.isNotEmpty(spuDto.getProductPictureUrlJson())){
                // 校验是否为json数组结构[]
                try{
                    JSON.parseArray(spuDto.getProductPictureUrlJson(), String.class);
                } catch (Exception e){
                    excelResultVos.add(buildErrInfo(i+1, "商品图格式错误"));
                }
            }
        }
    }


    public SpotSpuUploadExcelResultVo buildErrInfo(Integer rowNum, String errorMsg){
        SpotSpuUploadExcelResultVo resultVo = new SpotSpuUploadExcelResultVo();
        resultVo.setRowNum(rowNum);
        resultVo.setErrMsg(errorMsg);
        return resultVo;
    }

    private static void validSpuPicNumber(List<SpotUploadExcelSpuDto> spuDtoList) {
        // 按 spuCode 分组
        Map<String, List<SpotUploadExcelSpuDto>> groupedBySpuCode = spuDtoList.stream()
                .collect(Collectors.groupingBy(SpotUploadExcelSpuDto::getSpuCode));

        // 构建最终结果
        Map<String, List<String>> result = new HashMap<>();

        for (Map.Entry<String, List<SpotUploadExcelSpuDto>> entry : groupedBySpuCode.entrySet()) {
            List<String> allUrls = new ArrayList<>();
            for (SpotUploadExcelSpuDto dto : entry.getValue()) {
                // 解析 productPictureUrlJson JSON 数组
                if (dto.getProductPictureUrlJson() != null && !dto.getProductPictureUrlJson().isEmpty()) {
                    List<String> urls = JSON.parseArray(dto.getProductPictureUrlJson(), String.class);
                    allUrls.addAll(urls);
                }

                // 添加 sizePictureUrl
                if (dto.getSizePictureUrl() != null && !dto.getSizePictureUrl().isEmpty()) {
                    allUrls.add(dto.getSizePictureUrl());
                }
            }
            result.put(entry.getKey(), allUrls.stream().distinct().toList());
        }
        // 校验所有图片大于20张stylecode
        List<String>  extraStyleCode = result.keySet().stream().filter(key -> result.get(key).size() > 20).toList();
        if(CollectionUtil.isNotEmpty(extraStyleCode)){
            // extraStyleCode用逗号拼接
            String extraStyleCodeStr = String.join(",", extraStyleCode);
            throw new SdpDesignException( String.format("存在spu图片数量大于20张,请重新上传,:%s", extraStyleCodeStr));
        }
    }

    private static void validSupplyPicNumber(List<SpotUploadExcelSupplyDto> spuDtoList) {
        // 按 spuCode 分组
        Map<String, List<SpotUploadExcelSupplyDto>> groupedBySpuCode = spuDtoList.stream()
                .collect(Collectors.groupingBy(e -> e.getSupplierStyle()+"_"+e.getSupplierName()));

        // 构建最终结果
        Map<String, List<String>> result = new HashMap<>();

        for (Map.Entry<String, List<SpotUploadExcelSupplyDto>> entry : groupedBySpuCode.entrySet()) {
            List<String> allUrls = new ArrayList<>();
            for (SpotUploadExcelSupplyDto dto : entry.getValue()) {
                // 解析 productPictureUrlJson JSON 数组
                if (dto.getProductPictureUrlJson() != null && !dto.getProductPictureUrlJson().isEmpty()) {
                    List<String> urls = JSON.parseArray(dto.getProductPictureUrlJson(), String.class);
                    allUrls.addAll(urls);
                }

                // 添加 sizePictureUrl
                if (dto.getSizePictureUrl() != null && !dto.getSizePictureUrl().isEmpty()) {
                    allUrls.add(dto.getSizePictureUrl());
                }
            }
            result.put(entry.getKey(), allUrls.stream().distinct().toList());
        }
        // 校验所有图片大于20张supplierStyle
        List<String>  extraSupplierStyle = result.keySet().stream().filter(key -> result.get(key).size() > 20).toList();
        if(CollectionUtil.isNotEmpty(extraSupplierStyle)){
            throw new SdpDesignException( String.format("存在spu图片数量大于20张,请重新上传,:%s", String.join(",", extraSupplierStyle)));
        }
    }

    @NotNull
    private static <T> List<T> getSpotUploadExcelSpuDtos(UploadExcelReq req, Class<T> targetClass) {
        List<T> spotUploadDtoList;
        try {
            spotUploadDtoList = EasyExcel.read(req.getFile().getInputStream())
                    .head(targetClass)
                    .sheet()
                    .doReadSync();

        } catch (IOException e) {
            log.error("导入失败", e);
            throw new BusinessException("导入失败", e);
        }
        if (CollectionUtil.isEmpty(spotUploadDtoList)) {
            throw new BusinessException("导入数据为空");
        }
        return spotUploadDtoList;
    }

    @Override
    public SpotSpuReEstimateCheckPriceVo getReEstimateCheckPriceInfo(String styleCode) {
        SpotSpuReEstimateCheckPriceVo vo = new SpotSpuReEstimateCheckPriceVo();
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        Assert.isTrue(spotSpu != null, "根据styleCode:" + styleCode + "找不到对应的现货记录");
        SpotSpuDetail spotSpuDetail = spotSpuDetailRepository.getBySpotSpuId(spotSpu.getSpotSpuId());
        //优先拿SPU的商品图
        if (spotSpuDetail != null && !CollectionUtils.isEmpty(spotSpuDetail.getProductPictureList())) {
            vo.setProductPictureList(spotSpuDetail.getProductPictureList());
        }//如果SPU没有商品图，则拿第一个SKC的商品图
        else {
            List<SpotSkc> skcList = spotSkcRepository.listByStyleCodes(Collections.singletonList(styleCode));
            if (!CollectionUtils.isEmpty(skcList)) {
                SpotSkc spotSkc = skcList.getFirst();
                SpotSkcDetail spotSkcDetail = spotSkcDetailRepository.getBySpotSkcId(spotSkc.getSpotSkcId());
                if (spotSkcDetail != null && !CollectionUtils.isEmpty(spotSkcDetail.getProductPictureList())) {
                    vo.setProductPictureList(spotSkcDetail.getProductPictureList());
                }
            }
        }

        if (spotSpu.getPredictCheckPriceId() != null) {
            SpotSpuEstimateCheckPriceDto checkPriceDto = sampleClothesRemoteHelper.getEstimateCheckPriceBySpotSpu(spotSpu);
            if (checkPriceDto != null) {
                vo.setPredictCheckPriceId(checkPriceDto.getPredictCheckPriceId());
                vo.setStyleCode(checkPriceDto.getStyleCode());
                vo.setPricerId(checkPriceDto.getPricerId());
                vo.setPricerName(checkPriceDto.getPricerName());
                vo.setEstimateCheckPrice(checkPriceDto.getTotalCost());
            }
        }
        //拿第一个供应商的信息（包含采购价）
        List<SpotSpuSupplier> supplierList = spotSpuSupplierRepository.listByStyleCodes(Collections.singletonList(spotSpu.getStyleCode()));
        if (!CollectionUtils.isEmpty(supplierList)) {
            SpotSpuSupplier spotSpuSupplier = supplierList.getFirst();
            SpotSpuSupplierVo spotSpuSupplierVo = new SpotSpuSupplierVo();
            BeanUtils.copyProperties(spotSpuSupplier, spotSpuSupplierVo);
            vo.setSpotSpuSupplier(spotSpuSupplierVo);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEstimateCheckPriceStatus(UpdatePredictCheckPriceStatusReq req) {
        log.info("更新现货预估核价状态，req:{}", JSONObject.toJSONString(req));
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(req.getStyleCode());
        Assert.isTrue(spotSpu != null, "根据styleCode:" + req.getStyleCode() + "找不到对应的现货记录");
        spotSpu.setPredictCheckPriceId(req.getPredictCheckPriceId());
        spotSpu.setPredictCheckPriceStatus(req.getSpotPriceState().getCode());
        spotSpu.setPredictCheckPriceTime(req.getPredictCheckPriceTime());
        spotSpuRepository.updateById(spotSpu);
        //复核通过，需要通知商品定价类型
        if (SpotPriceStateEnum.RE_CHECK_PASS.getCode().equals(spotSpu.getPredictCheckPriceStatus())) {
            //操作日志
            this.addLog(spotSpu.getStyleCode(), spotSpu.getSpotSpuId(), DesignLogContentEnum.SPOT_RE_PRICE_PASS.getDesc());
            //核价通过，需要同步SKC到商品平台
            try {
                spotSkcService.pushPopProductBySpotSpu(spotSpu);
            } catch (Exception e) {
                log.error("核价通过后推送商品信息异常，styleCode:" + req.getStyleCode(), e);
            }
            //通知商品更新核价
            popProductHelper.updateProductEstimateCheckPriceBySpotSpu(spotSpu, req.getTotalCost(),req.getPricerId(),req.getPricerName());
            //通知商品更新定价类型
            popProductHelper.updateProductPricingTypeBySpotSpu(spotSpu, req.getPriceType());
        } else if (SpotPriceStateEnum.REJECT.getCode().equals(spotSpu.getPredictCheckPriceStatus())) {
            //操作日志
            this.addLog(spotSpu.getStyleCode(), spotSpu.getSpotSpuId(), DesignLogContentEnum.SPOT_RE_PRICE_REJECT.getDesc());
        }
    }

    @Override
    public List<SpotSupplierVo> listSupplier(SpotSupplierListReq req) {
        if (CollUtil.isEmpty(req.getSupplierNameList()) && CollUtil.isEmpty(req.getSupplierStyleList())) {
            throw new SdpDesignException("入参为空!");
        }
        List<SpotSpuSupplier> supplierList = spotSpuSupplierRepository.listBySupplierNameStyle(req.getSupplierNameList(), req.getSupplierStyleList());

        return supplierList.stream().map(item -> {
            SpotSupplierVo supplierVo = new SpotSupplierVo();
            BeanUtils.copyProperties(item, supplierVo);
            return supplierVo;
        }).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTryOnUser(String styleCode) {
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        if (Objects.isNull(spotSpu)) {
            return;
        }
        //已tryOn分配, 不更新
        if (Objects.nonNull(spotSpu.getTryOnBizId())) {
            return;
        }
        UserContent userContent = UserContentHolder.get();
        SdpDesignException.notNull(userContent, "用户信息为空!");
        SpotSpu spotSpuUpdate = new SpotSpu();
        spotSpuUpdate.setSpotSpuId(spotSpu.getSpotSpuId());
        spotSpuUpdate.setTryOnUserId(userContent.getCurrentUserId());
        spotSpuUpdate.setTryOnUserName(userContent.getCurrentUserName());

        spotSpuRepository.updateById(spotSpuUpdate);
    }

    private void checkMessage(UploadPictureReq req) {
        if (StringUtils.isBlank(req.getSupplierName())) {
            throw new SdpDesignException("供应商名字不能为空");
        }
    }

    @Override
    public void updateSpotSpuDetailPicture(Integer pictureType, List<String> styleCodes, Map<String, SpotSpuUploadPictureDto> fileNameMap) {

        List<SpotSpuDetailVo> detailList = spotSpuDetailRepository.getByStyleCodes(styleCodes);
        //图片类型,1：商品图，2：Try on图
        List<SpotSpuDetail> updateList = detailList.stream().map(t -> {
            SpotSpuDetail detail = new SpotSpuDetail();
            detail.setSpotSpuDetailId(t.getSpotSpuDetailId());
            if (Objects.equals(pictureType, SpotSpuPictureTypeEnum.PRODUCT.getCode())) {
                detail.setProductPictureList(dealPicture(t.getStyleCode(), fileNameMap));
            } else if (Objects.equals(pictureType, SpotSpuPictureTypeEnum.TRY_ON.getCode())) {
                detail.setTryOnPictureList(dealPicture(t.getStyleCode(), fileNameMap));
            }
            return detail;
        }).collect(Collectors.toList());
        if (!updateList.isEmpty()) {
            spotSpuDetailRepository.updateBatchById(updateList);
        }
    }

    @Override
    public void updateSpuStateByUploadPicture(Integer pictureType, List<String> styleCodes) {
        //更新图片状态
        List<SpotSpu> spuList = spotSpuRepository.listByStyleCodes(styleCodes);
        spuList.forEach(t -> {
            //1：商品图，改为已齐全product_picture_status
            if (Objects.equals(pictureType, SpotSpuPictureTypeEnum.PRODUCT.getCode())) {
                t.setProductPictureStatus(SpotProductPictureStateEnum.FINISH.getCode());
                t.setProductPictureUploadTime(LocalDateTime.now());
            }
            //2：Try on图-更改try_on_status为已通过
            else if (Objects.equals(pictureType, SpotSpuPictureTypeEnum.TRY_ON.getCode())) {
                log.info("上传tryOn图变更tryOn状态为已通过styleCode:" + t.getStyleCode());
                t.setTryOnStatus(SpotTryOnStateEnum.PASS.getCode());
                t.setTryOnAuditTime(LocalDateTime.now());
            }
        });
        if (!CollUtil.isEmpty(spuList)) {
            //更新图片状态
            spotSpuRepository.updateBatchById(spuList);
            List<String> updateStyleCodes = spuList.stream().map(SpotSpu::getStyleCode).collect(Collectors.toList());
            //如果是上传tryOn图，则需要通知灵感中心
            if (Objects.equals(pictureType, SpotSpuPictureTypeEnum.TRY_ON.getCode())) {
                //上传tryOn图tryOn状态变为已通过，通知灵感中心
                tryOnHelper.uploadPictureForPass(updateStyleCodes);
            }
            spuList.forEach(spotSpu -> {
                log.info("styleCode:{}上传图片后,推送待创建的视觉需求任务到视觉中心", spotSpu.getStyleCode());
                try {
                    //spu下最新记录
                    SpuVisualDemandRecord oldDemandRecord = spuVisualDemandRecordRepository.getLatestBySpu(spotSpu.getStyleCode());
                    //有待推送的视觉需求
                    if (oldDemandRecord != null && oldDemandRecord.getVisualDemandId() == null) {
                        log.info("styleCode:{}上传图片后，有一个待创建的视觉需求SpuVisualDemandRecordId:{}，如果符合条件则推送到视觉中心去创建任务",spotSpu.getStyleCode(),oldDemandRecord.getDemandRecordId());
                        SpotVisualSubmitReq spotVisualSubmitReq = new SpotVisualSubmitReq();
                        BeanUtils.copyProperties(oldDemandRecord, spotVisualSubmitReq);
                        if (Objects.equals(pictureType, SpotSpuPictureTypeEnum.TRY_ON.getCode())) {
                            spotVisualSubmitReq.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_UPLOAD_TRYON_PICTURE);
                        }else{
                            spotVisualSubmitReq.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_UPLOAD_PRODUCT_PICTURE);
                        }
                        submitVisualTask(spotVisualSubmitReq);
                    }
                } catch (Exception e) {
                    log.error("styleCode:{}上传图片后,推送视觉需求异常",spotSpu.getStyleCode(), e);
                }
            });
            //添加日志
            spuList.forEach(t -> {
                String logContent = Objects.equals(pictureType, SpotSpuPictureTypeEnum.PRODUCT.getCode())
                        ? DesignLogContentEnum.SPOT_UPLOAD_PRODUCT_IMG.getDesc() : DesignLogContentEnum.SPOT_UPLOAD_TRY_ON_IMG.getDesc();
                this.addLog(t.getStyleCode(), t.getSpotSpuId(), logContent);
            });
        }
    }

    @Override
    public List<VisualTaskListExtraVo> visualListExtra(List<String> styleCodes) {
        if (CollUtil.isEmpty(styleCodes)) {
            return Collections.emptyList();
        }
        //根据spu查询已提交的skc
        List<SpotSkc> spotSkcList = spotSkcRepository.listByStyleCodes(styleCodes, PrototypeStatusEnum.DECOMPOSED.getCode());
        if (CollUtil.isEmpty(spotSkcList)) {
            return Collections.emptyList();
        }
        List<Long> spotSkcIdList = StreamUtil.convertListAndDistinct(spotSkcList, SpotSkc::getSpotSkcId);
        List<SpotSkcDetail> skcDetailList = spotSkcDetailRepository.listBySkcIds(spotSkcIdList);
        Map<Long, SpotSkcDetail> skcDetailMap = StreamUtil.list2Map(skcDetailList, SpotSkcDetail::getSpotSkcId);

        Map<String, List<SpotSkc>> skcGroupMap = StreamUtil.groupingBy(spotSkcList, SpotSkc::getStyleCode);

        //构建返回结果
        return skcGroupMap.entrySet().stream()
                .map(entry -> this.buildVisualTaskListExtraVo(entry.getKey(), entry.getValue(), skcDetailMap))
                .collect(Collectors.toList());
    }

    /**
     * 构建单个SPU的 VisualTaskListExtraVo 对象
     */
    private VisualTaskListExtraVo buildVisualTaskListExtraVo(String styleCode,
                                                             List<SpotSkc> skcList,
                                                             Map<Long, SpotSkcDetail> skcDetailMap) {
        List<VisualTaskListExtraVo.SkcInfo> skcInfoList = skcList.stream()
                .map(skc -> {
                    SpotSkcDetail detail = skcDetailMap.get(skc.getSpotSkcId());
                    return buildVisualSkcInfo(skc, detail);
                })
                .collect(Collectors.toList());

        return VisualTaskListExtraVo.builder()
                .styleCode(styleCode)
                .skcInfoList(skcInfoList)
                .build();
    }

    /**
     * 构建单个SKC的信息
     */
    private VisualTaskListExtraVo.SkcInfo buildVisualSkcInfo(SpotSkc skc, SpotSkcDetail detail) {
        return VisualTaskListExtraVo.SkcInfo.builder()
                .designCode(skc.getDesignCode())
                .colorInfoList(detail != null ? detail.getColorInfoList() : Collections.emptyList())
                .build();
    }

    private List<String> dealPicture(String styleCode, Map<String, SpotSpuUploadPictureDto> fileNameMap) {
        if (fileNameMap.containsKey(styleCode)) {
            return fileNameMap.get(styleCode).getImageUrls().stream()
                    .map(SpotSpuUploadFileDto::getOssImageUrl).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private List<SpotSpuUploadPictureResultVo> dealIsExit(List<String> exitSpu, Integer type) {
        String tip = Objects.equals(type, 1) ? "上传成功" : "上传失败，无法匹配到正确的SPU，请检查文件夹命名";
        return exitSpu.stream()
                .map(fileName -> {
                    SpotSpuUploadPictureResultVo vo = new SpotSpuUploadPictureResultVo();
                    vo.setFileName(fileName);
                    vo.setUploadState(Objects.equals(type, 1) ? Bool.YES.getCode() : Bool.NO.getCode());
                    vo.setUploadResultDesc(tip);
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private List<SpotSpuUploadPictureResultVo> overMapDeal(Map<String, SpotSpuUploadPictureDto> overMap, int pictureLimitNum) {

        String overTip = "上传失败，图片数量超过" + pictureLimitNum + "张";
        return overMap.keySet().stream()
                .map(fileName -> {
                    SpotSpuUploadPictureResultVo vo = new SpotSpuUploadPictureResultVo();
                    vo.setFileName(fileName);
                    vo.setUploadState(Bool.NO.getCode());
                    vo.setUploadResultDesc(overTip);
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 刷新历史数据
     * @param file
     */
    @Override
    public void initHisData(MultipartFile file){
        List<StyleHisDataUploadExcelDto> uploadDtoList;
        try {
            uploadDtoList = EasyExcel.read(file.getInputStream())
                    .head(StyleHisDataUploadExcelDto.class)
                    .sheet()
                    .doReadSync();

        } catch (IOException e) {
            log.error("导入失败", e);
            throw new BusinessException("导入失败", e);
        }
        if (CollectionUtil.isEmpty(uploadDtoList)) {
            throw new BusinessException("导入数据为空");
        }
        Map<String, StyleHisDataUploadExcelDto> spuMap = uploadDtoList.stream().collect(Collectors.toMap(StyleHisDataUploadExcelDto::getSpuCode, t -> t));
        List<String> spuCodeList = uploadDtoList.stream().map(StyleHisDataUploadExcelDto::getSpuCode).distinct().toList();
        List<SpotSpu> spuList = spotSpuRepository.listByStyleCodes(spuCodeList);
        Assert.isTrue(CollectionUtil.isNotEmpty(spuList), "未找到对应的SPU");
        // 市场风格
        Map<String, String> marketMap = dictValueRemoteHelper.getMarketNameMap(1);
        Map<String, String> marketSeriesMap = dictValueRemoteHelper.getMarketNameMap(2);
        Map<String, String> marketStyleMap = dictValueRemoteHelper.getMarketNameMap(3);
        List<SpotSpu> updateSpuList = new ArrayList<>();
        for(SpotSpu spotSpu : spuList){
            StyleHisDataUploadExcelDto excelDto = spuMap.get(spotSpu.getStyleCode());
            if(excelDto == null){
                continue;
            }
            // TODO 市场使用业务给的，没有不导入 需确认
            if(excelDto.getMarketName() == null){
                continue;
            }
            String maretkCode = marketMap.get(excelDto.getMarketName());
            if(StrUtil.isBlank(maretkCode)){
                log.warn("未找到对应的市场spu：{},市场:{}", excelDto.getSpuCode(), excelDto.getMarketName());
                continue;
            }
            spotSpu.setMarketCode(maretkCode);
            /**
             * 系列
             * 1、业务给了使用业务给的
             * 2、中东市场：【传统本士服饰-Classic 传统本士服饰-Glamour 新式本士服饰-Modern 国际货盘-Global】取末级，否则空
             * 3、欧美市场：从【风格】末级值取值，用-号拆分
             * 4：东南亚市场：拿场景
             */
            String seriesName = "";
            if(StrUtil.isNotEmpty(excelDto.getMarketSeriesName())){
                seriesName = excelDto.getMarketSeriesName();
            }else{
                List<String> zdPalletTypeName = List.of("传统本士服饰-Classic","传统本士服饰-Glamour","新式本士服饰-Modern","国际货盘-Global");
                if("中东".equals(excelDto.getMarketName()) && zdPalletTypeName.contains(spotSpu.getPalletTypeName())){
                    seriesName =getLastPartAfterSplit(spotSpu.getPalletTypeName(),'-');
                }else if("欧美".equals(excelDto.getMarketName()) && StrUtil.isNotBlank(spotSpu.getClothingStyleName())){
                    seriesName = getLastPartAfterSplit(spotSpu.getClothingStyleName(),'-');
                }else if ("东南亚".equals(excelDto.getMarketName()) && StrUtil.isNotBlank(spotSpu.getSceneName())){
                    seriesName = spotSpu.getSceneName();
                }
            }
            spotSpu.setMarketSeriesCode(marketSeriesMap.get(excelDto.getMarketName() + "-"+ seriesName));
            if(StrUtil.isEmpty(spotSpu.getMarketSeriesCode())){
                log.warn("未找到对应的系列：spu{},市场名称：{}，系列名称:{}", excelDto.getSpuCode(), excelDto.getMarketName(),seriesName);
            }
            /**
             * 风格
             * 1、中东：取原风格，删除AR-AR ，并转换为中文casual-休闲、elegant-优雅、luxury-华丽、romatic-浪漫、Boho-度假
             * 2、欧美：为空
             * 3、东南亚：取风格末级
             */
            if(StrUtil.isNotEmpty(spotSpu.getMarketSeriesCode())){
                String marketStyleName = "";
                if("中东".equals(excelDto.getMarketName()) && StrUtil.isNotBlank(spotSpu.getClothingStyleName())){
                    Map<String, String> styleMapping = new HashMap<>();
                    styleMapping.put("casual", "休闲");
                    styleMapping.put("elegant", "优雅");
                    styleMapping.put("luxury", "华丽");
                    styleMapping.put("romatic", "浪漫");
                    styleMapping.put("Boho", "度假");
                    marketStyleName = styleMapping.get(spotSpu.getClothingStyleName().replace("AR-AR ", ""));
                }else if("欧美".equals(excelDto.getMarketName())){
                    marketStyleName = "";
                }else if("东南亚".equals(excelDto.getMarketName()) && StrUtil.isNotBlank(spotSpu.getClothingStyleName())){
                    marketStyleName=getLastPartAfterSplit(spotSpu.getClothingStyleName(),'-');
                }
                String marketStyleCode = marketStyleMap.get(excelDto.getMarketName().concat("-").concat(seriesName).concat("-").concat(marketStyleName));
                if(StrUtil.isNotBlank(marketStyleCode)){
                    spotSpu.setClothingStyleName(marketStyleName);
                    spotSpu.setClothingStyleCode(marketStyleCode);
                }
            }
            updateSpuList.add(spotSpu);
            spotSpuRepository.updateBatchById(updateSpuList);
        }

    }
    @Override
    public void initHisDataV2(MultipartFile file) {
        List<StyleHisDataUploadExcelDto> uploadDtoList;
        try {
            uploadDtoList = EasyExcel.read(file.getInputStream())
                    .head(StyleHisDataUploadExcelDto.class)
                    .sheet()
                    .doReadSync();

        } catch (IOException e) {
            log.error("导入失败", e);
            throw new BusinessException("导入失败", e);
        }
        if (CollectionUtil.isEmpty(uploadDtoList)) {
            throw new BusinessException("导入数据为空");
        }
        Map<String, StyleHisDataUploadExcelDto> spuMap = uploadDtoList.stream().collect(Collectors.toMap(StyleHisDataUploadExcelDto::getSpuCode, t -> t));
        List<String> spuCodeList = uploadDtoList.stream().map(StyleHisDataUploadExcelDto::getSpuCode).distinct().toList();
        List<SpotSpu> spuList = spotSpuRepository.listByStyleCodes(spuCodeList);
        Assert.isTrue(CollectionUtil.isNotEmpty(spuList), "未找到对应的SPU");
        // 市场风格
        Map<String, String> marketMap = dictValueRemoteHelper.getMarketNameMap(1);
        Map<String, String> marketSeriesMap = dictValueRemoteHelper.getMarketNameMap(2);
        Map<String, String> marketStyleMap = dictValueRemoteHelper.getMarketNameMap(3);
        List<SpotSpu> updateSpuList = new ArrayList<>();
        for (SpotSpu spotSpu : spuList) {
            StyleHisDataUploadExcelDto excelDto = spuMap.get(spotSpu.getStyleCode());
            if (excelDto == null) {
                continue;
            }
            if(StrUtil.isNotBlank(excelDto.getMarketName())){
                spotSpu.setMarketCode(marketMap.get(excelDto.getMarketName()));
            }
            if (StrUtil.isNotBlank(excelDto.getMarketName()) && StrUtil.isNotBlank(excelDto.getMarketSeriesName())){
                spotSpu.setMarketSeriesCode(marketSeriesMap.get(excelDto.getMarketName() + "-" + excelDto.getMarketSeriesName()));
            }
            if(StrUtil.isNotBlank(excelDto.getMarketName() ) && StrUtil.isNotBlank(excelDto.getMarketSeriesName()) && StrUtil.isNotBlank(excelDto.getMarketStyleName())){
                spotSpu.setClothingStyleCode(marketStyleMap.get(excelDto.getMarketName() + "-" + excelDto.getMarketSeriesName() + "-" + excelDto.getMarketStyleName()));
                if(StrUtil.isNotBlank(spotSpu.getClothingStyleCode())){
                    spotSpu.setClothingStyleName(excelDto.getMarketStyleName());
                }
            }
            updateSpuList.add(spotSpu);
        }
        spotSpuRepository.updateBatchById(updateSpuList);
    }

    public  String getLastPartAfterSplit(String input, char separator) {
        List<String> parts = StrUtil.split(input, separator);
        return parts.isEmpty() ? null : parts.getLast();
    }

}
