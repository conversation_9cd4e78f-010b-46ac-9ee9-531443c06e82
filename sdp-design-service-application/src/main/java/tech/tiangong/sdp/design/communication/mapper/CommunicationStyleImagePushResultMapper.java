package tech.tiangong.sdp.design.communication.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import tech.tiangong.sdp.design.communication.CommunicationStyleImagePushResult;
import tech.tiangong.sdp.design.communication.CommunicationSpu;

import java.util.List;

public interface CommunicationStyleImagePushResultMapper extends BaseMapper<CommunicationStyleImagePushResult> {

    /**
     * 查询未推送的SPU
     * 
     * @param limit 限制数量
     * @return 未推送的SPU列表
     */
    List<CommunicationSpu> findUnPushedSpuList(int limit);
}
