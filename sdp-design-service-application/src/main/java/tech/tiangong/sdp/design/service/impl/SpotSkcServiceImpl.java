package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.id.IdPool;
import tech.tiangong.inspiration.common.req.product.StyleLibraryDeleteReq;
import tech.tiangong.pop.common.dto.CreateProductDto;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.EstimateCheckPriceVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.converter.PopCreateInfoConverter;
import tech.tiangong.sdp.design.converter.StyleLibraryConverter;
import tech.tiangong.sdp.design.converter.spot.SpotConverter;
import tech.tiangong.sdp.design.converter.spot.SpotSkcConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.enums.spot.*;
import tech.tiangong.sdp.design.enums.visual.VisualErrorCodeEnum;
import tech.tiangong.sdp.design.helper.BuyerPrototypeHelper;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.helper.StyleLibraryHelper;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.remote.SampleClothesRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.DesignLogService;
import tech.tiangong.sdp.design.service.SpotSkcService;
import tech.tiangong.sdp.design.service.SpotSpuService;
import tech.tiangong.sdp.design.service.VisualSpuService;
import tech.tiangong.sdp.design.vo.dto.product.CreateProductCallbackDto;
import tech.tiangong.sdp.design.vo.dto.spot.SpotPickStyleCreateDto;
import tech.tiangong.sdp.design.vo.dto.spot.SpotSpuEstimateCheckPriceDto;
import tech.tiangong.sdp.design.vo.dto.visual.BackgroundDTO;
import tech.tiangong.sdp.design.vo.dto.visual.ModelFaceDTO;
import tech.tiangong.sdp.design.vo.dto.visual.TryOnModelReferenceImageDTO;
import tech.tiangong.sdp.design.vo.query.spot.SpotSkcQuery;
import tech.tiangong.sdp.design.vo.req.buyer.ListOfpSelectBuyerPrototypeReq;
import tech.tiangong.sdp.design.vo.req.buyer.OfpSelectBuyerPrototypeReq;
import tech.tiangong.sdp.design.vo.req.log.DesignLogSdpSaveReq;
import tech.tiangong.sdp.design.vo.req.spot.*;
import tech.tiangong.sdp.design.vo.resp.buyer.BuyerPrototypeToOfpResp;
import tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcForOfpVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcUpdateVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcVo;
import tech.tiangong.sdp.design.vo.resp.style.PopCreateProductVo;
import tech.tiangong.sdp.qy.converter.DictTreeConverter;
import tech.tiangong.sdp.utils.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * spot_skc表(SpotSkc)服务
 *
 * <AUTHOR>
 * @since 2025-02-25 11:37:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpotSkcServiceImpl implements SpotSkcService {
    private final SpotSkcRepository spotSkcRepository;
    private final SpotSkcDetailRepository spotSkcDetailRepository;
    private final SpotSkcConverter spotSkcConverter;
    private final BuyerPrototypeHelper buyerPrototypeHelper;
    private final SpotSpuRepository spotSpuRepository;
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final PopProductHelper popProductHelper;
    private final DesignLogService designLogService;
    private final BusinessCodeGenerator businessCodeGenerator;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final VisualSpuService visualSpuService;
    private final SpuVisualDemandRecordRepository spuVisualDemandRecordRepository;
    @Lazy
    @Resource
    private SpotSpuService spotSpuService;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final SpotSpuSupplierRepository spotSpuSupplierRepository;
    private final StyleLibraryHelper styleLibraryHelper;
    private static final Executor asyncExecutor = Executors.newFixedThreadPool(4);

    @Override
    public PageRespVo<SpotSkcVo> page(SpotSkcQuery query) {
        IPage<SpotSkcVo> page = spotSkcRepository.findPage(query);
        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.empty();
        }

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpotSkc colorMaking(SpotColorMakingReq req, SpotSpu spotSpu) {
        SdpDesignException.notBlank(req.getStyleCode(), "spu为空!");
        SdpDesignException.notNull(req.getSkcCreateReq(), "复色skc信息为空!");
        SdpDesignException.notNull(spotSpu, "spu信息为空!");
        String styleCode = req.getStyleCode();
        SpotSkcCreateReq skcCreateReq = req.getSkcCreateReq();

        //spu下所有skc
        List<SpotSkc> skcList = spotSkcRepository.listByStyleCode(styleCode);

        //颜色校验
        this.checkSameColor(skcList, skcCreateReq.getColor());

        //skc
        LocalDateTime submitTime = LocalDateTime.now();
        long spotSkcId = IdPool.getId();
        // int serialNo = skcList.size() + 1;
        String designCode = this.getSpotSkcCode();
        SpotSkc spotSkcEo = SpotConverter.buildSkcEo4ColorMaking(styleCode, skcCreateReq, spotSkcId, submitTime, designCode);
        spotSkcRepository.save(spotSkcEo);

        //skc详情
        SpotSkcDetail detailEo = SpotConverter.buildSkcDetailEo4ColorMaking(spotSpu, skcCreateReq, spotSkcId);
        spotSkcDetailRepository.save(detailEo);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(AsyncTask.wrapper(() -> {
                    //新增向量数据请求
                    SpotSpu spu = spotSpuRepository.getByStyleCode(styleCode);
                    styleLibraryHelper.spotStyleLibrarySaveOrUpdate(spu, spotSkcEo, detailEo, 1);
                }));
            }
        });

        return spotSkcEo;
    }

    /**
     * 现货skc编码: PS + yyMMdd + 1位品类码(女装-0; 男装-1) + 5位流水号
     */
    private String getSpotSkcCode() {
        String skcCode = businessCodeGenerator.generate(CodeRuleEnum.SPOT_SKC_CODE, spotSkcRepository::selectLatestDesignCode);
        return skcCode.substring(0, 8) + "0" + skcCode.substring(8);
    }

    private void checkSameColor(List<SpotSkc> skcList, String color) {
        //spu下重复颜色校验, 过滤已取消的
        SpotSkc sameColorSkc = skcList.stream()
                .filter(item -> Objects.equals(item.getIsCanceled(), Bool.NO.getCode()) && Objects.equals(item.getColor(), color))
                .findFirst().orElse(null);
        SdpDesignException.isNull(sameColorSkc, "存在颜色重复的SKC，请勿重复创建!");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SpotSkc> create4SelfCreate(String styleCode, SpotSpuSelfCreateReq req) {
        SdpDesignException.notBlank(styleCode, "styleCode为空!");
        SdpDesignException.notEmpty(req.getSkcAddList(), "skcAddList为空!");

        //保存时，校验SKC颜色是否重复，重复时提示：存在颜色重复的SKC，请勿重复创建；
        List<SpotSkcCreateReq> skcAddList = req.getSkcAddList();
        Map<String, List<SpotSkcCreateReq>> stringListMap = StreamUtil.groupingBy(skcAddList, SpotSkcCreateReq::getColor);
        stringListMap.forEach((color, reqs) -> SdpDesignException.isFalse(reqs.size() > 1, "颜色重复: {}", color));

        LocalDateTime submitTime = LocalDateTime.now();
        // Integer serialNo = 1;

        //创建skc与详情
        List<SpotSkc> skcSaveList = new ArrayList<>(skcAddList.size());
        List<SpotSkcDetail> skcDetailSaveList = new ArrayList<>(skcAddList.size());
        for (SpotSkcCreateReq item : skcAddList) {
            //skc
            long spotSkcId = IdPool.getId();
            String spotSkcCode = this.getSpotSkcCode();
            SpotSkc spotSkcEo = SpotConverter.buildSkcEo4SelfCreate(styleCode, item, spotSkcId, submitTime, spotSkcCode);
            skcSaveList.add(spotSkcEo);
            // serialNo++;

            //skc详情
            SpotSkcDetail detailEo = SpotConverter.buildSkcDetailEo4SelfCreate(req, item, spotSkcId);
            skcDetailSaveList.add(detailEo);
        }

        spotSkcRepository.saveBatch(skcSaveList);
        spotSkcDetailRepository.saveBatch(skcDetailSaveList);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(() -> {
                    //新增向量数据请求
                    SpotSpu spu = spotSpuRepository.getByStyleCode(styleCode);
                    Map<Long, SpotSkcDetail> detailMap = StreamUtil.list2Map(skcDetailSaveList, SpotSkcDetail::getSpotSkcId);
                    skcSaveList.forEach(t -> {
                        SpotSkcDetail detailEo = detailMap.get(t.getSpotSkcId());
                        styleLibraryHelper.spotStyleLibrarySaveOrUpdate(spu, t, detailEo, 1);
                    });
                }, asyncExecutor);
            }
        });

        return skcSaveList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpotSkc create4PickStyle(String styleCode, SpotPickStyleCreateDto skcReq) {
        log.info("==== create4PickStyle: styleCode:{}; ====", styleCode);
        SdpDesignException.notBlank(styleCode, "styleCode为空!");

        LocalDateTime submitTime = LocalDateTime.now();
        // Integer skcSerialNo = Objects.isNull(serialNo) ? 1 : serialNo;

        //查询标准尺码信息
        List<String> dictCodeList = List.of(DictConstant.STANDARD_TIANGONG_CODE);
        List<DictVo> dictResp = dictValueRemoteHelper.listByDictCodes(dictCodeList);
        Map<String, String> sizeVoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(dictResp)) {
            DictVo vo = dictResp.getFirst();
            sizeVoMap = DictTreeConverter.traverseSizeStandardToMap(vo);
        }
        //skc
        long spotSkcId = IdPool.getId();
        //PS + yyMMdd + 1位品类码(女装-0; 男装-1) + 5位流水号
        String designCode = getSpotSkcCode();
        SpotSkc spotSkcEo = SpotConverter.buildSkcEo4PickStyle(styleCode, skcReq, spotSkcId, submitTime, designCode, sizeVoMap);

        //skc详情
        SpotSkcDetail detailEo = SpotConverter.buildSkcDetailEo4PickStyle(skcReq, spotSkcId, sizeVoMap);

        spotSkcRepository.save(spotSkcEo);
        spotSkcDetailRepository.save(detailEo);
        log.info("==== 新增现货skc: designCode:{}; ====", designCode);

        CompletableFuture.runAsync(AsyncTask.wrapper(() -> {
            //新增向量数据请求
            SpotSpu spu = spotSpuRepository.getByStyleCode(styleCode);
            //新增向量数据请求
            styleLibraryHelper.spotStyleLibrarySaveOrUpdate(spu, spotSkcEo, detailEo, 1);
        }));

        return spotSkcEo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpotSkc create4ImportSpu(SpotSpu spotSpuEo) {
        SdpDesignException.notNull(spotSpuEo, "现货spu信息为空!");

        //skc
        String designCode = getSpotSkcCode();
        SpotSkc spotSkcEo = SpotConverter.buildSkcEo4ImportSpu(spotSpuEo.getStyleCode(), designCode);

        //skc详情
        SpotSkcDetail detailEo = SpotSkcDetail.builder().spotSkcDetailId(IdPool.getId()).spotSkcId(spotSkcEo.getSpotSkcId()).build();

        spotSkcRepository.save(spotSkcEo);
        spotSkcDetailRepository.save(detailEo);

        log.info("==== 导入现货SPU 新增skc: designCode:{}; spu:{} ====", designCode, spotSpuEo.getStyleCode());
        return spotSkcEo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SpotSkc> create4ExcelImport(List<SpotSpu> spotSpuList) {
        SdpDesignException.notEmpty(spotSpuList, "现货spu信息为空!");
        List<SpotSkc> spotSkcList = new ArrayList<>(spotSpuList.size());
        List<SpotSkcDetail> spotSkcDetailList = new ArrayList<>(spotSpuList.size());

        spotSpuList.forEach(spotSpuEo -> {
            //skc
            String designCode = getSpotSkcCode();
            SpotSkc spotSkcEo = SpotConverter.buildSkcEo4ImportSpu(spotSpuEo.getStyleCode(), designCode);
            spotSkcList.add(spotSkcEo);
            //skc详情
            SpotSkcDetail detailEo = SpotSkcDetail.builder().spotSkcDetailId(IdPool.getId()).spotSkcId(spotSkcEo.getSpotSkcId()).build();
            spotSkcDetailList.addFirst(detailEo);
        });

        spotSkcRepository.saveBatch(spotSkcList);
        spotSkcDetailRepository.saveBatch(spotSkcDetailList);

        return spotSkcList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpotSkc create4PickStyleWithSpu(String styleCode, SpotPickStyleCreateDto skcReq) {
        //SPU已存在, 判断是否需要创建skc: 选款的颜色是否与该SPU下的颜色不重复, 不重复则根据颜色生成skc: 资料状态: 待补充;
        SdpDesignException.notBlank(styleCode, "spu为空!");
        List<SpotSkc> skcList = spotSkcRepository.listByStyleCode(styleCode);
        SdpDesignException.notEmpty(skcList, "spu无skc! spu:{}", styleCode);

        //判断颜色是否重复: 排除当前skc, 已取消的
        SpotSkc sameColorSkc = skcList.stream()
                .filter(item -> Objects.equals(item.getIsCanceled(), Bool.NO.getCode()) && Objects.equals(item.getColor(), skcReq.getColor()))
                .findFirst().orElse(null);
        if (Objects.nonNull(sameColorSkc)) {
            return sameColorSkc;
        }

        //不重复则根据颜色生成skc: 资料状态: 待补充;
        // return this.create4PickStyle(styleCode, skcReq, skcList.size()+1);
        return this.create4PickStyle(styleCode, skcReq);
    }

    @Override
    public List<String> visualCheck(SpotSkcVisualCheckReq req) {
        //skc编辑,视觉需求弹框校验: spu是待提交状态, 且满足发送pop的条件时维护视觉需求
        String designCode = req.getDesignCode();
        SpotSkc spotSkc = spotSkcRepository.getByDesignCode(req.getDesignCode());
        SdpDesignException.notNull(spotSkc, "skc不存在, skc编码:{}", designCode);
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(spotSkc.getStyleCode());
        SdpDesignException.notNull(spotSpu, "spu不存在, skc编码:{}", req.getDesignCode());

        List<String> errorList = new ArrayList<>();
        if (!Objects.equals(spotSpu.getStyleStatus(), SpotStyleStateEnum.WAIT_SUBMIT.getCode())) {
            errorList.add("spu不是待提交状态");
            return errorList;
        }
        //===================== 推送pop的条件 =================
        //SPU的供给方式=现货try on
        if (SupplyModeEnum.TRY_ON.getCode().equals(spotSpu.getSupplyModeCode())) {
            //SPU核价状态=复核通过 && try on状态=已通过 && SPU资料=已完善
            if (SpotPriceStateEnum.RE_CHECK_PASS.getCode().equals(spotSpu.getPredictCheckPriceStatus())
                    && SpotTryOnStateEnum.PASS.getCode().equals(spotSpu.getTryOnStatus())
                    && SpotResourceStateEnum.FINISH.getCode().equals(spotSpu.getResourceStatus())) {
                log.info("现货tryOn,推送商品平台,styleCode:{}", spotSpu.getStyleCode());
                return List.of();
            } else {
                errorList.add("tryOn款编辑skc后不满足推送pop条件(SPU核价状态=复核通过 && try on状态=已通过 && SPU资料=已完善)");
            }
        }
        //SPU的供给方式=ODM
        else if (SupplyModeEnum.MANUFACTURER.getCode().equals(spotSpu.getSupplyModeCode())
                || SupplyModeEnum.IMITATION.getCode().equals(spotSpu.getSupplyModeCode())) {
            //1.新建款号成功，将SPU&SKC的基本信息，同步到运营平台；
            //2.若ODM款是从选款推送下来，则编辑完善信息后，将SPU&SKC的信息同步到运营平台
            if (SpotSourceTypeEnum.SELF_MAKE.getCode().equals(spotSpu.getSourceType())
                    || SpotSourceTypeEnum.BUYER_QY.getCode().equals(spotSpu.getSourceType())
                    || SpotSourceTypeEnum.SPU_IMPORT.getCode().equals(spotSpu.getSourceType())
                    || SpotSourceTypeEnum.SPU_IMPORT_EXCEL.getCode().equals(spotSpu.getSourceType())
            ) {
                return List.of();
            } else if (SpotSourceTypeEnum.PICK_STYLE.getCode().equals(spotSpu.getSourceType())
                    && SpotResourceStateEnum.FINISH.getCode().equals(spotSpu.getResourceStatus())) {
                return List.of();
            } else {
                errorList.add("ODM/仿款,编辑skc后不满足推送pop条件(SPU资料=已完善)");
            }
        } else {
            String supplyModeName = SupplyModeEnum.getDescByCode(spotSpu.getSupplyModeCode());
            errorList.add("供给方式异常:" + supplyModeName);
        }

        return errorList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpotSkcUpdateVo update(SpotSkcUpdateReq req) {
        log.info("=== 现货编辑skc req:{} ===", JSONObject.toJSONString(req));
        SpotSkc spotSkc = spotSkcRepository.getByDesignCode(req.getDesignCode());
        SdpDesignException.notNull(spotSkc, "skc不存在, skc编码:{}", req.getDesignCode());
        SdpDesignException.isFalse(Objects.equals(spotSkc.getIsCanceled(), Bool.YES.getCode()), "skc已取消!");

        SpotSkcDetail skcDetail = spotSkcDetailRepository.getBySpotSkcId(spotSkc.getSpotSkcId());
        SdpDesignException.notNull(skcDetail, "skc详情不存在, skc编码:{}", req.getDesignCode());

        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(spotSkc.getStyleCode());
        SdpDesignException.notNull(spotSpu, "spu不存在, skc编码:{}", req.getDesignCode());

        //颜色重复校验
        List<SpotSkc> oldSkcList = spotSkcRepository.listByStyleCode(spotSkc.getStyleCode());
        //排除当前skc, 已取消的
        SpotSkc sameColorSkc = oldSkcList.stream()
                .filter(item -> !Objects.equals(req.getDesignCode(), item.getDesignCode())
                        && Objects.equals(item.getIsCanceled(), Bool.NO.getCode()) && Objects.equals(item.getColor(), req.getColor()))
                .findFirst().orElse(null);
        SdpDesignException.isNull(sameColorSkc, "存在颜色重复的SKC，请勿重复创建!");

        //更新skc
        SpotSkc updateSkc = new SpotSkc();
        updateSkc.setSpotSkcId(spotSkc.getSpotSkcId());
        updateSkc.setVersionNum(spotSkc.getVersionNum() + 1);
        updateSkc.setColor(req.getColor());
        updateSkc.setColorEnglishName(req.getColorEnglishName());
        updateSkc.setResourceStatus(SpotResourceStateEnum.FINISH.getCode());
        updateSkc.setPrototypeStatus(PrototypeStatusEnum.DECOMPOSED.getCode());
        updateSkc.setDesignCode(req.getDesignCode());
        spotSkcRepository.updateById(updateSkc);

        //更新skc详情
        SpotSkcDetail updateSkcDetail = new SpotSkcDetail();
        updateSkcDetail.setSpotSkcDetailId(skcDetail.getSpotSkcDetailId());
        updateSkcDetail.setProductPictureList(req.getProductPictureList());
        updateSkcDetail.setSampleSize(req.getSampleSize());
        List<ColorInfoVo> colorInfoVoList = req.getColorInfoList().stream().map(color -> {
            ColorInfoVo colorInfoVo = new ColorInfoVo();
            BeanUtils.copyProperties(color, colorInfoVo);
            return colorInfoVo;
        }).toList();
        updateSkcDetail.setColorInfoList(colorInfoVoList);
        spotSkcDetailRepository.updateById(updateSkcDetail);

        //同步符合条件的SKC到商品平台
        this.pushPopProductBySpotSpu(spotSpu);

        //创建视觉需求
        SpotVisualDemandReq visualDemandInfo = req.getVisualDemandInfo();
        SpotVisualSubmitReq visualSubmitReq = null;
        //创建视觉需求，前端有传需求内容
        if (visualDemandInfo != null) {
            visualSubmitReq = new SpotVisualSubmitReq();
            BeanUtils.copyProperties(visualDemandInfo, visualSubmitReq);
            visualSubmitReq.setStyleCode(spotSpu.getStyleCode());
            visualSubmitReq.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_SKC_SUBMIT);
        }
        //前端没有传需求内容，但系统内有一条待推送的需求
        else {
            //spu下最新记录
            SpuVisualDemandRecord oldDemandRecord = spuVisualDemandRecordRepository.getLatestBySpu(spotSpu.getStyleCode());
            //有待推送的视觉需求
            if (oldDemandRecord != null && oldDemandRecord.getVisualDemandId() == null) {
                visualSubmitReq = new SpotVisualSubmitReq();
                BeanUtils.copyProperties(oldDemandRecord, visualSubmitReq);
                visualSubmitReq.setSpuVisualHandleType(SpuVisualHandleTypeEnum.SPOT_SKC_SUBMIT);
                visualSubmitReq.setModelReferenceImageList(JSON.parseArray(oldDemandRecord.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
                visualSubmitReq.setBackgroundImageList(JSON.parseArray(oldDemandRecord.getBackgroundImage(), BackgroundDTO.class));
                visualSubmitReq.setModelFaceImageList(JSON.parseArray(oldDemandRecord.getModelFaceImage(), ModelFaceDTO.class));
            }
        }

        if (Objects.nonNull(visualSubmitReq)) {
            try {
                spotSpuService.submitVisualTask(visualSubmitReq);
            } catch (Exception e) {
                log.error("现货编辑skc创建视觉需求失败", e);
                //如果是未符合发起条件，则先保存需求
                VisualErrorCodeEnum visualErrorCode = VisualErrorCodeEnum.findByCode(e.getMessage());
                if (!VisualErrorCodeEnum.UNKNOWN.equals(visualErrorCode)) {
                    spotSpuService.submitSpuVisualDemandRecord(visualSubmitReq, null);
                }
            }
        }

        //skc颜色推送商品运营
        SpotSkc pushSkc = spotSkcRepository.getById(spotSkc.getSpotSkcId());
        popProductHelper.updateProductColorBySpotSkc(pushSkc);

        //尺码信息更新推送商品运营
        popProductHelper.updateProductSizeBySpotSkc(pushSkc);

        this.addLog(spotSpu.getStyleCode(), spotSpu.getSpotSpuId(), DesignLogContentEnum.SPOT_UPDATE_RESOURCE.getDesc());

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(AsyncTask.wrapper(() -> {
                    //请求向量数据刷新
                    styleLibraryHelper.spotStyleLibrarySaveOrUpdate(spotSpu, updateSkc, updateSkcDetail, 2);
                }));
            }
        });

        SpotSkcUpdateVo skcUpdateVo = SpotSkcUpdateVo.builder()
                .spotSkcId(spotSkc.getSpotSkcId())
                .designCode(spotSkc.getDesignCode())
                .versionNum(updateSkc.getVersionNum())
                .styleCode(spotSkc.getStyleCode())
                .build();
        log.info("=== 现货编辑skc 成功:{} ===", JSONObject.toJSONString(skcUpdateVo));
        return skcUpdateVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(String styleCode, List<SpotSkcUpdateReq> skcUpdateReqList) {
        if (CollUtil.isEmpty(skcUpdateReqList)) {
            return;
        }
        List<String> designCodeList = StreamUtil.convertListAndDistinct(skcUpdateReqList, SpotSkcUpdateReq::getDesignCode);
        List<SpotSkc> oldSkcList = spotSkcRepository.listByDesignCodes(designCodeList);
        SdpDesignException.notEmpty(oldSkcList, "skc不存在");
        List<Long> skcIdList = StreamUtil.convertListAndDistinct(oldSkcList, SpotSkc::getSpotSkcId);
        List<SpotSkcDetail> oldSkcDetailList = spotSkcDetailRepository.listBySkcIds(skcIdList);
        SdpDesignException.notEmpty(oldSkcDetailList, "skc详情不存在");

        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        //保存时，校验SKC颜色是否重复，重复时提示：存在颜色重复的SKC，请勿重复创建；
        Map<String, List<SpotSkcUpdateReq>> stringListMap = StreamUtil.groupingBy(skcUpdateReqList, SpotSkcUpdateReq::getColor);
        stringListMap.forEach((color, reqs) -> SdpDesignException.isFalse(reqs.size() > 1, "颜色重复: {}", color));
        //spu下所有skc
        List<SpotSkc> skcList = spotSkcRepository.listByStyleCode(styleCode);
        skcUpdateReqList.forEach(skcReq -> {
            //排除当前skc, 已取消的
            SpotSkc sameColorSkc = skcList.stream()
                    .filter(item -> !Objects.equals(skcReq.getDesignCode(), item.getDesignCode())
                            && Objects.equals(item.getIsCanceled(), Bool.NO.getCode()) && Objects.equals(item.getColor(), skcReq.getColor()))
                    .findFirst().orElse(null);
            SdpDesignException.isNull(sameColorSkc, "存在颜色重复的SKC，请勿重复创建!");
        });

        Map<String, SpotSkc> skcMap = StreamUtil.list2Map(oldSkcList, SpotSkc::getDesignCode);
        Map<Long, SpotSkcDetail> skcDetailMap = StreamUtil.list2Map(oldSkcDetailList, SpotSkcDetail::getSpotSkcId);


        List<SpotSkc> skcUpdateList = new ArrayList<>(skcUpdateReqList.size());
        List<SpotSkcDetail> skcDetailUpdateList = new ArrayList<>(skcUpdateReqList.size());
        Map<Long, SpotSkcDetail> skcDetailUpdateMap = new HashMap<>();

        skcUpdateReqList.forEach(item -> {
            SpotSkc spotSkc = skcMap.get(item.getDesignCode());
            SdpDesignException.notNull(spotSkc, "skc不存在, skc编码:{}", item.getDesignCode());
            SdpDesignException.isFalse(Objects.equals(spotSkc.getIsCanceled(), Bool.YES.getCode()), "skc已取消! {}", item.getDesignCode());
            SpotSkcDetail skcDetail = skcDetailMap.get(spotSkc.getSpotSkcId());
            SdpDesignException.notNull(skcDetail, "skc详情不存在, skc编码:{}", item.getDesignCode());

            //skc
            SpotSkc updateSkc = new SpotSkc();
            updateSkc.setSpotSkcId(spotSkc.getSpotSkcId());
            updateSkc.setVersionNum(spotSkc.getVersionNum() + 1);
            updateSkc.setColor(item.getColor());
            updateSkc.setColorEnglishName(item.getColorEnglishName());
            updateSkc.setResourceStatus(SpotResourceStateEnum.FINISH.getCode());
            updateSkc.setPrototypeStatus(PrototypeStatusEnum.DECOMPOSED.getCode());
            updateSkc.setDesignCode(spotSkc.getDesignCode());
            skcUpdateList.add(updateSkc);

            //skc详情
            SpotSkcDetail updateSkcDetail = new SpotSkcDetail();
            updateSkcDetail.setSpotSkcDetailId(skcDetail.getSpotSkcDetailId());
            List<ColorInfoVo> colorInfoVoList = item.getColorInfoList().stream().map(color -> {
                ColorInfoVo colorInfoVo = new ColorInfoVo();
                BeanUtils.copyProperties(color, colorInfoVo);
                return colorInfoVo;
            }).toList();
            updateSkcDetail.setColorInfoList(colorInfoVoList);
            updateSkcDetail.setProductPictureList(item.getProductPictureList());
            updateSkcDetail.setSampleSize(item.getSampleSize());
            updateSkcDetail.setSizeStandard(spotSpu.getSizeStandard());
            updateSkcDetail.setSizeStandardCode(spotSpu.getSizeStandardCode());
            skcDetailUpdateList.add(updateSkcDetail);
            skcDetailUpdateMap.put(skcDetail.getSpotSkcDetailId(), updateSkcDetail);

        });

        spotSkcRepository.updateBatchById(skcUpdateList);
        spotSkcDetailRepository.updateBatchById(skcDetailUpdateList);
        //根据SKCID查询完整的SKC信息再推送到商品平台
        List<Long> spotSkcIds = skcUpdateList.stream().map(SpotSkc::getSpotSkcId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(spotSkcIds)) {
            List<SpotSkc> spotSkcs = spotSkcRepository.listByIds(spotSkcIds);
            spotSkcs.forEach(pushSkc -> {
                //更新尺码
                popProductHelper.updateProductSizeBySpotSkc(pushSkc);
                //更新颜色
                popProductHelper.updateProductColorBySpotSkc(pushSkc);
            });
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                ExecutorService executor = Executors.newSingleThreadExecutor();
                skcUpdateList.stream()
                        .map(item -> CompletableFuture.runAsync(() -> {
                            try {
                                SpotSkc spotSkc = skcMap.get(item.getDesignCode());
                                SpotSkcDetail skcDetail = skcDetailMap.get(spotSkc.getSpotSkcId());
                                SpotSkcDetail skcUpdateDetail = skcDetailUpdateMap.get(skcDetail.getSpotSkcDetailId());
                                //看看是否第一个图片是否相同，不相同的话就请求创建向量
                                if (!skcDetail.getProductPictureList().getFirst().equals(skcUpdateDetail.getProductPictureList().getFirst())) {
                                    //向量信息更新操作
                                    styleLibraryHelper.spotStyleLibrarySaveOrUpdate(spotSpu, item, skcUpdateDetail, 2);
                                }
                            } catch (Exception e) {
                                log.error("异步处理向量更新失败: {}", e.getMessage(), e);
                            }
                        }, executor))
                        .toArray(CompletableFuture[]::new);
                executor.shutdown();
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCancel(SpotSkcBatchCancelReq req) {
        log.info("=== 现货取消skc req:{} ===", JSONObject.toJSONString(req));
        //4、如果整个SPU被取消，则通知核价SPU被取消；若只存在SKC被取消，则不通知；
        //5、如果SKC被取消，则通知商品运营平台
        List<String> designCodeList = req.getDesignCodeList();
        List<SpotSkc> skcList = spotSkcRepository.listByDesignCodes(designCodeList);
        if (CollUtil.isEmpty(skcList)) {
            return;
        }
        //过滤已取消的skc
        List<SpotSkc> waitCancelSkcList = StreamUtil.filter(skcList, item -> Objects.equals(item.getIsCanceled(), Bool.NO.getCode()));
        if (CollUtil.isEmpty(waitCancelSkcList)) {
            return;
        }

        List<String> cancelDesignCodeList = StreamUtil.convertListAndDistinct(waitCancelSkcList, SpotSkc::getDesignCode);
        List<Long> skcIdList = StreamUtil.convertListAndDistinct(waitCancelSkcList, SpotSkc::getSpotSkcId);
        List<SpotSkcDetail> oldSkcDetailList = spotSkcDetailRepository.listBySkcIds(skcIdList);

        List<String> spuList = StreamUtil.convertListAndDistinct(waitCancelSkcList, SpotSkc::getStyleCode);
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(spuList);

        Map<String, SpotSkc> skcMap = StreamUtil.list2Map(waitCancelSkcList, SpotSkc::getDesignCode);
        Map<Long, SpotSkcDetail> skcDetailMap = StreamUtil.list2Map(oldSkcDetailList, SpotSkcDetail::getSpotSkcId);
        Map<String, SpotSpu> spuMap = StreamUtil.list2Map(spotSpuList, SpotSpu::getStyleCode);

        Map<String, List<SpotSkc>> cancelSkcMap = StreamUtil.groupingBy(waitCancelSkcList, SpotSkc::getStyleCode);

        LocalDateTime cancelTime = LocalDateTime.now();
        UserContent userContent = UserContentHolder.get();

        //取消skc
        this.handleSkcCancel(req, cancelDesignCodeList, skcMap, skcDetailMap, cancelTime, userContent);

        //若spu下对应skc是否全部取消了, 则取消spu
        List<String> styleCodeList = StreamUtil.convertListAndDistinct(waitCancelSkcList, SpotSkc::getStyleCode);
        List<SpotSkc> allSkcList = spotSkcRepository.listByStyleCodes(styleCodeList);
        Map<String, List<SpotSkc>> allSkcMap = StreamUtil.groupingBy(allSkcList, SpotSkc::getStyleCode);
        List<String> cancelSpuList = new ArrayList<>();
        allSkcMap.forEach((spu, spotSkcList) -> {
            List<SpotSkc> noCancelSkcList = spotSkcList.stream()
                    .filter(item -> !Objects.equals(item.getIsCanceled(), Bool.YES.getCode())).toList();
            SpotSpu spotSpu = spuMap.get(spu);
            if (CollUtil.isEmpty(noCancelSkcList)) {
                cancelSpuList.add(spu);
                //操作日志-取消spu
                this.addLog(spu, spotSpu.getSpotSpuId(), DesignLogContentEnum.SPOT_CANCEL_SPU.getDesc());
            }
            //操作日志-取消skc
            else {
                List<SpotSkc> cancelList = cancelSkcMap.get(spu);
                List<String> designCodes = StreamUtil.convertListAndDistinct(cancelList, SpotSkc::getDesignCode);
                String skcStr = StrUtil.join("、", designCodes);
                this.addLog(spu, spotSpu.getSpotSpuId(), DesignLogContentEnum.SPOT_CANCEL_SKC.getDesc() + skcStr);
            }
        });
        if (CollUtil.isNotEmpty(cancelSpuList)) {
            spotSpuService.cancelSpu(cancelSpuList);
            //取消视觉需求
            log.info("现货取消spu同步视觉, spuList:{}", JSON.toJSONString(cancelSpuList));
            visualSpuService.cancelSpu(cancelSpuList);
        }

        //skc取消通知商品运营平台
        waitCancelSkcList.forEach(popProductHelper::cancelSpotSkc);

        log.info("=== 现货取消skc 成功:{} ===", JSONObject.toJSONString(waitCancelSkcList));
    }

    private void handleSkcCancel(SpotSkcBatchCancelReq req, List<String> designCodeList, Map<String, SpotSkc> skcMap, Map<Long, SpotSkcDetail> skcDetailMap, LocalDateTime cancelTime, UserContent userContent) {
        List<SpotSkc> cancelSkcList = new ArrayList<>(designCodeList.size());
        List<StyleLibraryDeleteReq> deleteReqs = new ArrayList<>();
        List<SpotSkcDetail> cancelSkcDetailList = new ArrayList<>(designCodeList.size());
        designCodeList.forEach(designCode -> {
            SpotSkc spotSkc = skcMap.get(designCode);
            SdpDesignException.notNull(spotSkc, "skc不存在! {}", designCode);
            SdpDesignException.isFalse(Objects.equals(spotSkc.getIsCanceled(), Bool.YES.getCode()), "skc已取消! {}", designCode);
            SpotSkcDetail skcDetail = skcDetailMap.get(spotSkc.getSpotSkcId());
            SdpDesignException.notNull(skcDetail, "skc详情不存在, skc编码:{}", designCode);

            SpotSkc cancelSkc = new SpotSkc();
            cancelSkc.setSpotSkcId(spotSkc.getSpotSkcId());
            cancelSkc.setIsCanceled(Bool.YES.getCode());
            cancelSkcList.add(cancelSkc);

            //更新skc详情
            SpotSkcDetail updateSkcDetail = new SpotSkcDetail();
            updateSkcDetail.setSpotSkcDetailId(skcDetail.getSpotSkcDetailId());
            updateSkcDetail.setCancelReason(req.getCancelReason());
            updateSkcDetail.setCancelTime(cancelTime);
            updateSkcDetail.setCancelUserName(userContent.getCurrentUserName());
            updateSkcDetail.setCancelUserId(userContent.getCurrentUserId());
            cancelSkcDetailList.add(updateSkcDetail);

            //向量删除信息组装
            StyleLibraryDeleteReq deleteReq = StyleLibraryConverter.deleteReq(StyleLibrarySourceTypeEnum.SPOT_MANAGEMENT.code, spotSkc.getSpotSkcId());
            deleteReqs.add(deleteReq);
        });
        spotSkcRepository.updateBatchById(cancelSkcList);
        spotSkcDetailRepository.updateBatchById(cancelSkcDetailList);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(AsyncTask.wrapper(() -> {
                    //批量删除向量信息
                    deleteReqs.forEach(styleLibraryHelper::styleLibraryDelete);
                }));
            }
        });

    }

    private void addLog(String styleCode, Long bizId, String logContent) {
        DesignLogSdpSaveReq logSdpSaveReq = DesignLogSdpSaveReq.builder()
                .bizId(bizId)
                .bizType(DesignLogBizTypeEnum.SPOT)
                .styleCode(styleCode)
                .content(logContent)
                .build();
        designLogService.sdpSave(logSdpSaveReq);
    }

    @Override
    public void syncSpotSkcFirstPurchaseOrder(SyncSpotSkcPurchaseOrderReq req) {
        log.info("同步OFP现货款的首单信息req:{}", JSONObject.toJSONString(req));
        SpotSkc spotSkc = spotSkcRepository.getByDesignCode(req.getDesignCode());
        Assert.isTrue(spotSkc != null, "根据skc编号没找到对应的现货记录,designCode=" + req.getDesignCode());
        SpotPurchaseOrderStateEnum spotPurchaseOrderState = SpotPurchaseOrderStateEnum.findByCode(req.getPurchaseOrderStatus());
//        Assert.isTrue(spotPurchaseOrderState!=null,"订单状态无效");
        //只关注2待接单 4进行中 5已完结 6已终止，其他状态不处理
        if (spotPurchaseOrderState != null) {
            if (SpotPurchaseOrderStateEnum.TERMINATION.equals(spotPurchaseOrderState)) {
                spotSkcRepository.updatePurchaseOrderStatus(spotSkc.getSpotSkcId(), null, null);
            } else {
                spotSkcRepository.updatePurchaseOrderStatus(spotSkc.getSpotSkcId(), spotPurchaseOrderState, req.getPurchaseOrderCode());
            }
        }
    }

    @Override
    public PageRespVo<SpotSkcForOfpVo> querySkcForOfp(QuerySkcForOfpReq req) {
        //查旧JV的买手系统
        if (req.getQuerySkcSourceType() != null && OfpQuerySkcSourceTypeEnum.OLD_JV_PLM_BUYER.equals(req.getQuerySkcSourceType())) {
            OfpSelectBuyerPrototypeReq ofpSelectBuyerPrototypeReq = new OfpSelectBuyerPrototypeReq();
            BeanUtils.copyProperties(req, ofpSelectBuyerPrototypeReq);
            try {
                PageRespVo<BuyerPrototypeToOfpResp> pageRespVo = buyerPrototypeHelper.getBuyerInfoForOfp(ofpSelectBuyerPrototypeReq);
                if (pageRespVo != null && !CollectionUtils.isEmpty(pageRespVo.getList())) {
                    List<SpotSkcForOfpVo> list = spotSkcConverter.trans2SpotSkcForOfpVo(pageRespVo.getList());
                    return PageRespVoHelper.of(pageRespVo.getPage(), pageRespVo.getTotal(), list);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            return PageRespVoHelper.empty();
        }
        //查新现货管理系统
        // 默认不查货通的款
        if (req.getCommunication() == null) req.setCommunication(ProductCommunicationEnum.NO.getCode());
        IPage<SpotSkcForOfpVo> page = spotSkcRepository.querySkcForOfp(req);
        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.empty();
        }
        spotSkcConverter.fillSpotSkcForOfpVo(page.getRecords());
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    public List<SpotSkcForOfpVo> listSkcForOfp(ListSkcForOfpReq req) {
        List<SpotSkcForOfpVo> list = new ArrayList<>();
        //先查本系统的
        // 默认不查货通的款
        if (req.getCommunication() == null) req.setCommunication(ProductCommunicationEnum.NO.getCode());
        List<SpotSkcForOfpVo> tempList = spotSkcRepository.listSkcForOfp(req);
        spotSkcConverter.fillSpotSkcForOfpVo(tempList);
        if (!CollectionUtils.isEmpty(tempList)) {
            list.addAll(tempList);
        }
        //再查买手系统的
        ListOfpSelectBuyerPrototypeReq listOfpSelectBuyerPrototypeReq = new ListOfpSelectBuyerPrototypeReq();
        BeanUtils.copyProperties(req, listOfpSelectBuyerPrototypeReq);
        try {
            List<BuyerPrototypeToOfpResp> buyerPrototypeToOfpResps = buyerPrototypeHelper.listBuyerInfoForOfp(listOfpSelectBuyerPrototypeReq);
            if (!CollectionUtils.isEmpty(buyerPrototypeToOfpResps)) {
                list.addAll(spotSkcConverter.trans2SpotSkcForOfpVo(buyerPrototypeToOfpResps));
            }
        } catch (Exception e) {
            log.error("查询买手系统的款式异常" + e.getMessage(), e);
        }
        return list;
    }

    /*
    public void pushPopProductBySpotSpu(SpotSpu spotSpu){
        Product2PopMqDto mqDTO = new Product2PopMqDto();
        BeanUtils.copyProperties(spotSpu, mqDTO);
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.PRODUCT_2_POP_PRODUCT,
                DesignMqConstant.SPOT_PRODUCT_2_POP_EXCHANGE,
                DesignMqConstant.SPOT_PRODUCT_2_POP_ROUTING_KEY,
                JSONUtil.toJsonStr(mqDTO));
        log.info("商品创建推送pop. mq消息：{}", JSON.toJSONString(mqMessageReq));
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pushPopProductBySpotSpu(SpotSpu spotSpu) {
        boolean isPush = false;
        log.info("以SPU维度推送POP商品styleCode:{}", spotSpu.getStyleCode());
        List<SpotSkc> spotSkcs = spotSkcRepository.listByStyleCode(spotSpu.getStyleCode());
        if (CollectionUtils.isEmpty(spotSkcs)) {
            log.error("现货tryOn推送商品平台失败：SKC为空，styleCode:{}", spotSpu.getStyleCode());
        }
        spotSkcs = spotSkcs.stream()
                .filter(v -> Bool.NO.getCode() == v.getIsCanceled())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spotSkcs)) {
            log.error("现货tryOn推送商品平台失败：没有有效的SKC，styleCode:{}", spotSpu.getStyleCode());
        }
        //SPU的供给方式=现货try on
        if (SupplyModeEnum.TRY_ON.getCode().equals(spotSpu.getSupplyModeCode())) {
            //SPU核价状态=复核通过 && try on状态=已通过 && SPU资料=已完善
            if (SpotPriceStateEnum.RE_CHECK_PASS.getCode().equals(spotSpu.getPredictCheckPriceStatus())
                    && SpotTryOnStateEnum.PASS.getCode().equals(spotSpu.getTryOnStatus())
                    && SpotResourceStateEnum.FINISH.getCode().equals(spotSpu.getResourceStatus())) {
                log.info("现货tryOn,推送商品平台,styleCode:{}", spotSpu.getStyleCode());
                List<SpotSkc> resourceFinishSpotSkcs = spotSkcs.stream()
                        .filter(v -> SpotResourceStateEnum.FINISH.getCode().equals(v.getResourceStatus()))
                        .collect(Collectors.toList());
                // 货通商品：按颜色去重，保留首个SKC
                if (Objects.equals(ProductCommunicationEnum.YES.getCode(), spotSpu.getCommunication())) {
                    resourceFinishSpotSkcs = dedupeSkcByColorKeepFirst(resourceFinishSpotSkcs);
                }
                if (CollectionUtils.isEmpty(resourceFinishSpotSkcs)) {
                    log.error("现货tryOn推送商品平台失败：SKC信息未完善，styleCode:{}", spotSpu.getStyleCode());
                }
                //已完善的SKC信息
                List<SpotSkcDetail> spotSkcDetails = verifySize(spotSpu, resourceFinishSpotSkcs);
                if (CollectionUtils.isEmpty(spotSkcDetails)) {
                    log.error("现货tryOn推送商品平台失败：SKC信息尺码无交集或不在字典配置的尺码范围，styleCode:{}", spotSpu.getStyleCode());
                }
                CreateProductDto req = spotSkcConverter.convertProductCreateReq(spotSpu, resourceFinishSpotSkcs, spotSkcDetails);
                if (Objects.nonNull(req)) {
                    popProductHelper.noticeCreateSpotProduct(req);
                    isPush = true;
                }
            } else {
                log.error("供给方式=现货try on的现货推送商品平台失败，styleCode:{}，条件不符合条件（SPU核价状态=复核通过 && try on状态=已通过 && SPU资料=已完善），当前状态为（SPU核价状态={} && try on状态={} && SPU资料={}）"
                        , spotSpu.getStyleCode()
                        , SpotPriceStateEnum.getDescByCode(spotSpu.getPredictCheckPriceStatus())
                        , SpotTryOnStateEnum.getDescByCode(spotSpu.getTryOnStatus())
                        , SpotResourceStateEnum.getDescByCode(spotSpu.getResourceStatus()));
            }
        }
        //SPU的供给方式=ODM
        else if (SupplyModeEnum.MANUFACTURER.getCode().equals(spotSpu.getSupplyModeCode())
                || SupplyModeEnum.IMITATION.getCode().equals(spotSpu.getSupplyModeCode())) {
            //1.新建款号成功，将SPU&SKC的基本信息，同步到运营平台；
            //2.若ODM款是从选款推送下来，则编辑完善信息后，将SPU&SKC的信息同步到运营平台
            if (SpotSourceTypeEnum.SELF_MAKE.getCode().equals(spotSpu.getSourceType())
                    || SpotSourceTypeEnum.BUYER_QY.getCode().equals(spotSpu.getSourceType())
                    || SpotSourceTypeEnum.SPU_IMPORT.getCode().equals(spotSpu.getSourceType())
                    || SpotSourceTypeEnum.SPU_IMPORT_EXCEL.getCode().equals(spotSpu.getSourceType())
            ) {
                log.info("现货ODM-自建款,推送商品平台,styleCode:{}", spotSpu.getStyleCode());
                //只考虑未被取消的SKC，不考虑资料是否完善
                // 货通商品：按颜色去重，保留首个SKC
                List<SpotSkc> skcsForPush = spotSkcs;
                if (Objects.equals(ProductCommunicationEnum.YES.getCode(), spotSpu.getCommunication())) {
                    skcsForPush = dedupeSkcByColorKeepFirst(spotSkcs);
                }
                List<SpotSkcDetail> spotSkcDetails = verifySize(spotSpu, skcsForPush);
                if (CollectionUtils.isEmpty(spotSkcDetails)) {
                    log.error("现货ODM-自建款,推送商品平台失败：SKC信息尺码无交集或不在字典配置的尺码范围，styleCode:{}", spotSpu.getStyleCode());
                }
                CreateProductDto req = spotSkcConverter.convertProductCreateReq(spotSpu, skcsForPush, spotSkcDetails);
                if (Objects.nonNull(req)) {
                    popProductHelper.noticeCreateSpotProduct(req);
                    isPush = true;
                }
            } else if (SpotSourceTypeEnum.PICK_STYLE.getCode().equals(spotSpu.getSourceType())
                    && SpotResourceStateEnum.FINISH.getCode().equals(spotSpu.getResourceStatus())) {
                log.info("现货ODM-选款,推送商品平台,styleCode:{}", spotSpu.getStyleCode());
                List<SpotSkc> resourceFinishSpotSkcs = spotSkcs.stream()
                        .filter(v -> SpotResourceStateEnum.FINISH.getCode().equals(v.getResourceStatus()))
                        .collect(Collectors.toList());
                // 货通商品：按颜色去重，保留首个SKC
                if (Objects.equals(ProductCommunicationEnum.YES.getCode(), spotSpu.getCommunication())) {
                    resourceFinishSpotSkcs = dedupeSkcByColorKeepFirst(resourceFinishSpotSkcs);
                }
                if (CollectionUtils.isEmpty(resourceFinishSpotSkcs)) {
                    log.error("现货ODM-选款,推送商品平台失败：SKC信息未完善，styleCode:{}", spotSpu.getStyleCode());
                }
                List<SpotSkcDetail> spotSkcDetails = verifySize(spotSpu, resourceFinishSpotSkcs);
                if (CollectionUtils.isEmpty(spotSkcDetails)) {
                    log.error("现货ODM-选款,推送商品平台失败：SKC信息尺码无交集或不在字典配置的尺码范围，styleCode:{}", spotSpu.getStyleCode());
                }
                CreateProductDto req = spotSkcConverter.convertProductCreateReq(spotSpu, resourceFinishSpotSkcs, spotSkcDetails);
                if (Objects.nonNull(req)) {
                    popProductHelper.noticeCreateSpotProduct(req);
                    isPush = true;
                }
            } else {
                log.error("SPU的供给方式=ODM的现货推送商品平台失败：自建款或者选款的SPU和SKC信息未完善，styleCode:{}"
                        , spotSpu.getStyleCode());
            }
        } else {
            log.error("当前现货的供给方式是{}：{}，不推送商品平台，styleCode:{}"
                    , SupplyModeEnum.getDescByCode(spotSpu.getSupplyModeCode())
                    , spotSpu.getSupplyModeCode()
                    , spotSpu.getStyleCode());
        }
        //更新提交状态
        log.info("资料是否更新状态：" + isPush);
        if (isPush) {
            spotSpu.setSubmitTime(LocalDateTime.now());
            spotSpu.setStyleStatus(SpotStyleStateEnum.SUBMITTED.getCode());
            spotSpuRepository.updateById(spotSpu);
        }
        return isPush;
    }

    @Override
    public Boolean checkSpuSameColor(String styleCode, String color) {
        SdpDesignException.notBlank(styleCode, "spu为空!");
        SdpDesignException.notBlank(color, "color为空!");
        List<SpotSkc> skcList = spotSkcRepository.listByStyleCode(styleCode);
        if (CollUtil.isEmpty(skcList)) {
            return false;
        }
        //spu下重复颜色校验, 过滤已取消的
        SpotSkc sameColorSkc = skcList.stream()
                .filter(item -> Objects.equals(item.getIsCanceled(), Bool.NO.getCode()) && Objects.equals(item.getColor(), color))
                .findFirst().orElse(null);
        return Objects.nonNull(sameColorSkc);
    }

    @Override
    public void createProductCallBackHandle(List<CreateProductCallbackDto> list) {
        List<String> styleCodeList = StreamUtil.convertListAndDistinct(list, CreateProductCallbackDto::getSpuCode);
        if (CollUtil.isEmpty(styleCodeList)) {
            return;
        }
        //创建商品成功后，把相关字段的都更新一次
        list.forEach(v -> {
            log.info("商品平台创建现货商品成功后，把相关字段的都更新一次，styleCode:{},designCode:{}", v.getSpuCode(), v.getSkc());
            SpotSpu spotSpu = spotSpuRepository.getByStyleCode(v.getSpuCode());
            if (Objects.isNull(spotSpu)) {
                log.error("现货spu不存在 spu:{}", v.getSpuCode());
                return;
            }
            SpotSkc spotSkc = spotSkcRepository.getByDesignCode(v.getSkc());

            //更新品类/风格
            popProductHelper.updateProductCategoryBySpotSpu(spotSpu, PopCategoryStyleUpdateTypeEnum.CATEGORY_AND_STYLE);
            //更新尺码
            popProductHelper.updateProductSizeBySpotSkc(spotSkc);
            //更新颜色
            popProductHelper.updateProductColorBySpotSkc(spotSkc);
            //更新采购价
            BigDecimal purchasePrice = spotSpuSupplierRepository.getMaxPurchasePrice(spotSpu.getStyleCode());
            if(purchasePrice!=null){
                //因为现货的采购价是在编辑SPU的供应商时录入的，所以这里用SPU的更新人作为核价师传给POP
                popProductHelper.updateProductPurchasePriceBySpotSpu(spotSpu,purchasePrice,spotSpu.getReviserId(),spotSpu.getReviserName());
            }
            SpotSpuEstimateCheckPriceDto spotSpuEstimateCheckPriceDto = sampleClothesRemoteHelper.getEstimateCheckPriceBySpotSpu(spotSpu);
            if(spotSpuEstimateCheckPriceDto!=null){
                //更新预估核价
                popProductHelper.updateProductEstimateCheckPriceBySpotSpu(spotSpu,spotSpuEstimateCheckPriceDto.getTotalCost(),spotSpuEstimateCheckPriceDto.getPricerId(),spotSpuEstimateCheckPriceDto.getPricerName());
                SpotSpuPricingTypeEnum spotSpuPricingType = SpotSpuPricingTypeEnum.findByCode(spotSpuEstimateCheckPriceDto.getPriceType());
                if (spotSpuPricingType != null) {
                    //更新定价类型
                    popProductHelper.updateProductPricingTypeBySpotSpu(spotSpu, spotSpuPricingType);
                }
            }
        });

        //印花款异常推送状态补偿
        /*
        DpPushStateFixReq fixReq = new DpPushStateFixReq();
        fixReq.setStyleCodeList(styleCodeList);
        digitalPrintingStyleService.fixFailState(fixReq);
         */
    }

    @Override
    public List<PopCreateProductVo> queryStyleInfo4Pop(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        //现货spu与详情
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(spotSpuList)) {
            return Collections.emptyList();
        }
        List<Long> spotSpuIdList = StreamUtil.convertListAndDistinct(spotSpuList, SpotSpu::getSpotSpuId);
        List<SpotSpuDetail> spotSpuDetailList = spotSpuDetailRepository.listBySpotSpuIds(spotSpuIdList);
        Map<Long, SpotSpuDetail> spuDetailMap = StreamUtil.list2Map(spotSpuDetailList, SpotSpuDetail::getSpotSpuId);

        //spu供应商
        List<SpotSpuSupplier> supplierList = spotSpuSupplierRepository.listByStyleCodes(styleCodeList);
        Map<String, List<SpotSpuSupplier>> supplierGroupMap = StreamUtil.groupingBy(supplierList, SpotSpuSupplier::getStyleCode);

        //现货skc与详情
        List<SpotSkc> spotSkcList = spotSkcRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(spotSkcList)) {
            return Collections.emptyList();
        }
        Map<String, List<SpotSkc>> skcGroupMap = StreamUtil.groupingBy(spotSkcList, SpotSkc::getStyleCode);

        List<Long> spotSkcIdList = StreamUtil.convertListAndDistinct(spotSkcList, SpotSkc::getSpotSkcId);
        List<SpotSkcDetail> spotSkcDetailList = spotSkcDetailRepository.listBySkcIds(spotSkcIdList);
        Map<Long, SpotSkcDetail> skcDetailMap = StreamUtil.list2Map(spotSkcDetailList, SpotSkcDetail::getSpotSkcId);

        //核价信息
        List<Long> checkPriceIdList = StreamUtil.convertListAndDistinct(spotSpuList, SpotSpu::getPredictCheckPriceId);
        List<EstimateCheckPriceVo> priceList = sampleClothesRemoteHelper.listEstimateCheckPriceByIds(checkPriceIdList);
        Map<Long, EstimateCheckPriceVo> priceMap = StreamUtil.list2Map(priceList, EstimateCheckPriceVo::getEstimateCheckPriceId);

        //封装出参
        List<PopCreateProductVo> productVoList = new ArrayList<>(spotSpuList.size());
        spotSpuList.forEach(spotSpu -> {
            SpotSpuDetail spotSpuDetail = spuDetailMap.get(spotSpu.getSpotSpuId());
            List<SpotSkc> skcList = skcGroupMap.get(spotSpu.getStyleCode());

            List<SpotSkcDetail> skcDetailList = skcList.stream()
                    .filter(item -> Objects.nonNull(skcDetailMap.get(item.getSpotSkcId())))
                    .map(item -> skcDetailMap.get(item.getSpotSkcId())).toList();

            SpotSpuEstimateCheckPriceDto checkPriceDto = new SpotSpuEstimateCheckPriceDto();
            EstimateCheckPriceVo priceVo = priceMap.get(spotSpu.getPredictCheckPriceId());
            if (Objects.nonNull(priceVo)) {
                BeanUtils.copyProperties(priceVo, checkPriceDto);
            }

            BigDecimal purchasePrice = null;
            List<SpotSpuSupplier> suppliers = supplierGroupMap.get(spotSpu.getStyleCode());
            if (CollUtil.isNotEmpty(suppliers)) {
                SpotSpuSupplier maxPriceSupplier = suppliers.stream().max(Comparator.comparing(SpotSpuSupplier::getPurchasePrice)).orElse(new SpotSpuSupplier());
                purchasePrice = maxPriceSupplier.getPurchasePrice();
            }

            //查询当前品类字典获取尺码明细表
            DictVo dictVoByCategory = dictValueRemoteHelper.getDictVoByCategory(spotSpu.getCategory());

            CreateProductDto createProductDto = spotSkcConverter.buildCreateProductDto(spotSpu, spotSpuDetail,
                    checkPriceDto, skcList, purchasePrice, skcDetailList, dictVoByCategory);
            PopCreateProductVo productVo = PopCreateInfoConverter.buildPopCreateProductVo(createProductDto);
            productVo.setStyleType(SdpStyleTypeEnum.SPOT.getCode());

            productVoList.add(productVo);

        });
        return productVoList;
    }

    /**
     * 货通商品去重辅助：按颜色去重，仅保留首个SKC（保持原有顺序）。
     * 仅当 SKC 的 `color` 非空时参与去重；`color` 为空的条目不做去重（全部保留）。
     */
    private List<SpotSkc> dedupeSkcByColorKeepFirst(List<SpotSkc> list) {
        if (CollectionUtils.isEmpty(list)) return list;
        LinkedHashMap<String, SpotSkc> firstByColor = new LinkedHashMap<>();
        List<SpotSkc> result = new ArrayList<>(list.size());
        for (SpotSkc skc : list) {
            String color = skc.getColor();
            if (StrUtil.isBlank(color)) {
                // 不含颜色的记录，不参与去重，直接保留
                result.add(skc);
                continue;
            }
            String key = color.trim();
            if (!firstByColor.containsKey(key)) {
                firstByColor.put(key, skc);
                result.add(skc);
            }
        }
        return result;
    }

    /**
     * 校验尺码的合法性
     * @param spu   spu信息
     * @param pushSkc   skc信息
     * @return  skc详情集合（含校验完成后的尺码）
     */
    private List<SpotSkcDetail> verifySize(SpotSpu spu, List<SpotSkc> pushSkc) {
        List<SpotSkcDetail> spotSkcDetails = spotSkcDetailRepository.listBySkcIds(pushSkc.stream().map(SpotSkc::getSpotSkcId).collect(Collectors.toList()));
        if (CollUtil.isEmpty(spotSkcDetails) || spotSkcDetails.stream().anyMatch(e -> StrUtil.isBlank(e.getSampleSize()))) {
            log.error("推送商品平台失败,spu下的存在无尺码的skc, spu:{}", spu.getStyleCode());
            return null;
        }
        // 所有SKC的尺码范围需一致
        List<String> sizeList = spotSkcDetails.stream().map(SpotSkcDetail::getSampleSize).toList();
        log.info("SPU [{}] 所有尺码:{}", spu.getStyleCode(), sizeList);
        Set<String> sizeIntersectionSet = SizeVerifyUtil.findSizeIntersection(sizeList);
        log.info("SPU [{}] 所有SKC的交集尺码:{}", spu.getStyleCode(), sizeIntersectionSet);
        if (CollUtil.isEmpty(sizeIntersectionSet)) {
            log.error("推送商品平台失败,spu下的skc尺码无交集, spu:{}", spu.getStyleCode());
            return null;
        }
        String sizeIntersection = String.join(",", sizeIntersectionSet);
        // 符合尺码范围
        DictVo dictVoByCategory = dictValueRemoteHelper.getDictVoByCategory(spu.getCategory());
        if (Objects.nonNull(dictVoByCategory) && CollUtil.isNotEmpty(dictVoByCategory.getAttributes())) {    //不为空则才判断尺码范围
            Optional<team.aikero.admin.common.vo.AttributeVo> codeAttributeOpt = dictVoByCategory.getAttributes().stream()
                    .filter(attr -> attr != null && "popSize".equals(attr.getCode()))
                    .findFirst();


            if (codeAttributeOpt.isPresent() && StringUtils.isNotBlank(codeAttributeOpt.get().getName())) {
                List<String> popSizeList = Arrays.stream(codeAttributeOpt.get().getName().split(",")).collect(Collectors.toList());
                popSizeList.retainAll(sizeIntersectionSet);
                if (CollUtil.isEmpty(popSizeList)) {
                    log.error("推送商品平台失败,spu下的skc尺码不在字典配置的尺码范围内, spu:{}, 尺码:{}", spu.getStyleCode(), sizeIntersectionSet);
                    return null;
                }
                sizeIntersection = String.join(",", popSizeList);
                log.info("SPU [{}] 所有SKC尺码与字典尺码范围的交集:{}", spu.getStyleCode(), sizeIntersection);
            }
        }
        for (SpotSkcDetail spotSkcDetail : spotSkcDetails) {
            // 设置成统一的尺码给到POP
            spotSkcDetail.setSampleSize(sizeIntersection);
        }

        log.info("推送商品平台尺码校验完成, spu:{}, 尺码范围:{}", spu.getStyleCode(), sizeIntersection);

        return spotSkcDetails;
    }

}
