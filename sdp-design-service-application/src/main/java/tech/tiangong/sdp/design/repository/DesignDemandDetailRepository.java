package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.DesignDemandDetail;
import tech.tiangong.sdp.design.mapper.DesignDemandDetailMapper;

import java.util.List;
import java.util.Objects;

/**
 * 灵感设计需求_详情表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class DesignDemandDetailRepository extends BaseRepository<DesignDemandDetailMapper, DesignDemandDetail> {


    public DesignDemandDetail getByDemandId(Long designDemandId) {
        if (Objects.isNull(designDemandId)) {
            return null;
        }
        return lambdaQuery().eq(DesignDemandDetail::getDesignDemandId, designDemandId).one();
    }

    public List<DesignDemandDetail> listByDesignDemandIds(List<Long> demandIdList) {
        if (CollUtil.isEmpty(demandIdList)) {
            return null;
        }
        return lambdaQuery().in(DesignDemandDetail::getDesignDemandId, demandIdList).list();
    }
}
