package tech.tiangong.sdp.design.remote.zj.commodity;

import cn.yibuyun.framework.collection.Collections;
import cn.yibuyun.framework.net.DataResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.clothes.client.ZjCommodityClient;
import tech.tiangong.sdp.clothes.vo.req.zj.Commodity3dPictureReq;
import tech.tiangong.sdp.clothes.vo.resp.zj.Commodity3dPictureVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 *  致景中台_接口调用helper
 * </p>
 *
 **/
@Service
@Slf4j
@AllArgsConstructor
public class ZjCommodityRemoteHelper {

    private final ZjCommodityClient zjCommodityClient;

    /**
     * 获取供应商列表
     * @param skuInfoList 入参
     */
    public List<Commodity3dPictureVo.SkuPictureInfo> query3dPicture(List<Commodity3dPictureReq.SkuInfo> skuInfoList){
        SdpDesignException.notEmpty(skuInfoList, "入参为空!");
        Commodity3dPictureReq req = new Commodity3dPictureReq();
        req.setSkuInfoList(skuInfoList);
        log.info("=== 面料3D图片查询-req：{} ===", JSONObject.toJSONString(req));

        try {
            DataResponse<Commodity3dPictureVo> response = zjCommodityClient.query3dPicture(req);
            log.info("=== 面料3D图片查询-response:{}", JSON.toJSONString(response));
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            return Objects.isNull(response.getData()) ?  null : response.getData().getSku3dPictures();
        } catch (Exception e) {
//            throw new SdpDesignException("面料3D图片查询失败:"+e.getMessage(), e);
            log.error("面料3D图片查询失败:"+e.getMessage(), e);
        }
        return Collections.emptyList();
    }

}
