package tech.tiangong.sdp.design.converter.visual;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.design.entity.ImageFile;
import tech.tiangong.sdp.design.entity.OnShelfImagePackage;
import tech.tiangong.sdp.design.entity.VisualImagePackage;
import tech.tiangong.sdp.design.entity.VisualSpu;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.repository.VisualSpuRepository;
import tech.tiangong.sdp.design.vo.req.visual.SaveOnShelfImagePackageReq;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageResp;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageVo;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Component
public class VisualImagePackageConverter {
    private final VisualSpuRepository visualSpuRepository;
    private final VisualTaskHelper visualTaskHelper;

    public VisualImagePackageResp trans2VisualImagePackageResp(VisualImagePackageVo vo){
        if(vo==null){
            return null;
        }
        return JSONObject.parseObject(JSONObject.toJSONString(vo),VisualImagePackageResp.class);
    }

    public VisualImagePackageVo trans2VisualImagePackageVo(VisualImagePackage visualImagePackage) {
        if(visualImagePackage==null){
            return null;
        }
        VisualImagePackageVo vo = new VisualImagePackageVo();
        BeanUtils.copyProperties(visualImagePackage, vo);
        //需要展示最新的SKC项目，所以展示包时需要先获取spu下的所有SKC，再将已有图片回填到对象内
        VisualSpu visualSpu = visualSpuRepository.getByStyleCode(visualImagePackage.getStyleCode());
        if(visualSpu==null){
            if(StrUtil.isNotBlank(visualImagePackage.getOnShelfImages())){
                vo.setOnShelfImages(JSONObject.parseObject(visualImagePackage.getOnShelfImages(),OnShelfImagePackage.class));
            }
            if(StrUtil.isNotBlank(visualImagePackage.getOnShelfImages11())){
                vo.setOnShelfImages11(JSONObject.parseObject(visualImagePackage.getOnShelfImages11(),OnShelfImagePackage.class));
            }
            if(StrUtil.isNotBlank(visualImagePackage.getOnShelfImages34())){
                vo.setOnShelfImages34(JSONObject.parseObject(visualImagePackage.getOnShelfImages34(),OnShelfImagePackage.class));
            }
            if(StringUtils.isNotBlank(visualImagePackage.getVideoGenerations())){
                List<ImageFile> imageFiles = JSONObject.parseArray(visualImagePackage.getVideoGenerations(), ImageFile.class);
                vo.setVideoGenerations(imageFiles);
            }
            return vo;
        }
        OnShelfImagePackage onShelfImages = visualTaskHelper.initOnShelfImagePackageBySpu(visualSpu);
        log.info("SPU初始化图包对象的结构:{}", JSON.toJSONString(onShelfImages));

        // 处理onShelfImages
        vo.setOnShelfImages(processImagePackage(onShelfImages, visualImagePackage.getOnShelfImages()));
        log.info("处理onShelfImages1:1{}", JSON.toJSONString(onShelfImages));

        // 处理onShelfImages11
        vo.setOnShelfImages11(processImagePackage(onShelfImages, visualImagePackage.getOnShelfImages11()));

        // 处理onShelfImages34
        vo.setOnShelfImages34(processImagePackage(onShelfImages, visualImagePackage.getOnShelfImages34()));

        if(StringUtils.isNotBlank(visualImagePackage.getVideoGenerations())){
            List<ImageFile> imageFiles = JSONObject.parseArray(visualImagePackage.getVideoGenerations(), ImageFile.class);
            vo.setVideoGenerations(imageFiles);
        }
        return vo;
    }





    public OnShelfImagePackage processImagePackage(OnShelfImagePackage basePackage, String imagePackageJson) {
        if (basePackage == null) {
            return null;
        }

        // 深拷贝基础包结构
        OnShelfImagePackage resultPackage = JSONObject.parseObject(JSONObject.toJSONString(basePackage), OnShelfImagePackage.class);

        if (StringUtils.isNotBlank(imagePackageJson)) {
            OnShelfImagePackage recordOnShelfImagePackage = JSONObject.parseObject(imagePackageJson, OnShelfImagePackage.class);

            // 处理SKC图片
            if (CollectionUtil.isNotEmpty(resultPackage.getSkcImages()) &&
                    CollectionUtil.isNotEmpty(recordOnShelfImagePackage.getSkcImages())) {

                resultPackage.getSkcImages().forEach(skcImage -> {
                    recordOnShelfImagePackage.getSkcImages().forEach(recordSkcImage -> {
                        if (recordSkcImage.getDesignCode().equals(skcImage.getDesignCode()) &&
                                recordSkcImage.getColor().equals(skcImage.getColor())) {
                            skcImage.setImages(recordSkcImage.getImages());
                        }
                    });
                });

            }
            // 如果SKC图不存在，则使用记录的SKC图
            if (CollUtil.isEmpty(resultPackage.getSkcImages()) &&
                    CollUtil.isNotEmpty(recordOnShelfImagePackage.getSkcImages())) {
                resultPackage.setSkcImages(recordOnShelfImagePackage.getSkcImages());
            }

            // 处理SPU图片
            if (CollectionUtil.isNotEmpty(resultPackage.getSpuImages()) &&
                    CollectionUtil.isNotEmpty(recordOnShelfImagePackage.getSpuImages())) {

                resultPackage.getSpuImages().forEach(spuImage -> {
                    recordOnShelfImagePackage.getSpuImages().forEach(recordSpuImage -> {
                        if (recordSpuImage.getImageType().equals(spuImage.getImageType())) {
                            spuImage.setImages(recordSpuImage.getImages());
                        }
                    });
                });
            }
        }

        return resultPackage;
    }


    public OnShelfImagePackage trans2OnShelfImagePackage(SaveOnShelfImagePackageReq req){
        //转换成上架图包对象
        OnShelfImagePackage onShelfImagePackage = new OnShelfImagePackage();
        onShelfImagePackage.setStyleCode(req.getStyleCode());
        //转换SPU图
        List<OnShelfImagePackage.SpuImage> spuImages = req.getSpuImages().stream().map(v->{
            OnShelfImagePackage.SpuImage spuImage = new OnShelfImagePackage.SpuImage();
            BeanUtils.copyProperties(v, spuImage);
            if(CollectionUtil.isNotEmpty(v.getImages())){
                spuImage.setImages(v.getImages());
            }
            return spuImage;
        }).collect(Collectors.toList());
        onShelfImagePackage.setSpuImages(spuImages);
        //转换SKC图
        if(CollectionUtil.isNotEmpty(req.getSkcImages())) {
            List<OnShelfImagePackage.SkcImage> skcImages = req.getSkcImages().stream().map(v -> {
                OnShelfImagePackage.SkcImage skcImage = new OnShelfImagePackage.SkcImage();
                BeanUtils.copyProperties(v, skcImage);
                if(CollectionUtil.isNotEmpty(v.getImages())){
                    skcImage.setImages(v.getImages());
                }
                return skcImage;
            }).collect(Collectors.toList());
            onShelfImagePackage.setSkcImages(skcImages);
        }
        return onShelfImagePackage;
    }
}
