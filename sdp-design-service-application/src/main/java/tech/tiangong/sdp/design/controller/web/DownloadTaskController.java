package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.download.DownloadTaskHandleService;
import tech.tiangong.sdp.design.service.download.DownloadTaskService;
import tech.tiangong.sdp.design.vo.query.download.DownloadTaskQuery;
import tech.tiangong.sdp.design.vo.resp.download.DownloadTaskPageVo;

/**
 * 下载管理-web
 * <AUTHOR>
 * @date 2025/6/2 22:36
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/download-task")
public class DownloadTaskController extends BaseController {
    private final DownloadTaskService downloadTaskService;
    private final DownloadTaskHandleService downloadTaskHandleService;


    /**
     * 分页列表
     *
     * @param query 查询参数
     * @return 下载任务分页列表
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<DownloadTaskPageVo>> page(@RequestBody @Validated DownloadTaskQuery query) {
        return DataResponse.ok(downloadTaskService.page(query));
    }

    /**
     * 取消下载
     *
     * @param taskId 任务主键
     * @return 操作结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/cancel/{taskId}")
    public DataResponse<Void> cancelTask(@PathVariable(value = "taskId") Long taskId) {
        downloadTaskService.cancelTask(taskId);
        return DataResponse.ok();
    }

    /**
     * 重试下载
     *
     * @param taskId 任务主键
     * @return 操作结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/retry/{taskId}")
    public DataResponse<Void> retryTask(@PathVariable(value = "taskId") Long taskId) {
        downloadTaskService.retryTask(taskId);
        return DataResponse.ok();
    }



    /**
     * 处理单个任务
     *
     * @param taskId 任务主键
     * @return 操作结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/process/{taskId}")
    public DataResponse<Void> processTask(@PathVariable(value = "taskId") Long taskId) {
        downloadTaskHandleService.processTask(taskId);
        return DataResponse.ok();
    }

    /**
     * 处理单个任务-忽略其状态是否已完成等
     *
     * @param taskId 任务主键
     * @return 操作结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/force-process/{taskId}")
    public DataResponse<Void> forceProcessTask(@PathVariable(value = "taskId") Long taskId) {
        downloadTaskHandleService.forceProcessTask(taskId);
        return DataResponse.ok();
    }

}

