package tech.tiangong.sdp.design.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.util.Json;
import com.alibaba.fastjson.JSON;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import tech.tiangong.id.IdPool;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.pop.common.dto.CreateProductDto;
import tech.tiangong.sdp.design.constant.SdpDesignConstant;
import tech.tiangong.pop.common.dto.ProductAttributesV2Dto;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DesignStyleSourceTypeEnum;
import tech.tiangong.sdp.design.enums.SdpStyleTypeEnum;
import tech.tiangong.sdp.design.enums.StyleStatusEnum;
import tech.tiangong.sdp.design.vo.base.AttributeVo;
import tech.tiangong.sdp.design.vo.dto.style.DesignStyleUpdateDto;
import tech.tiangong.sdp.design.vo.req.digital.TemplateSizeDetailReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleCreateReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleImportReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleUpdateReq;
import tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo;
import tech.tiangong.sdp.material.pojo.dto.DesignerDTO;
import tech.tiangong.sdp.prod.vo.resp.StyleSizeInfoVo;
import tech.tiangong.sdp.utils.StreamUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SPU转换器
 * <AUTHOR>
 */
@UtilityClass
@Slf4j
public class DesignStyleConverter {

    /**
     * SPU创建EO
     */
    public DesignStyle buildSpuCreateEo(DesignStyleCreateReq req,
                                               long designStyleId,
                                               String styleCode,
                                               Integer styleVersionNum,
                                               Long currentUserId,
                                               DesignerDTO designerDTO) {
        DesignStyle designStyleEo = DesignStyle.builder()
                .designStyleId(designStyleId)
                .styleCode(styleCode)
                .styleStatus(StyleStatusEnum.SUBMITTED.getCode())
                .versionNum(styleVersionNum)
                .latestSubmitTime(LocalDateTime.now())
                .build();

        //灵感设计需求创建,状态为待提交
        if (Objects.equals(DesignStyleSourceTypeEnum.DESIGN_DEMAND.getCode(), req.getSourceType())) {
            designStyleEo.setStyleStatus(StyleStatusEnum.WAIT_SUBMIT.getCode());
        }

        BeanUtils.copyProperties(req, designStyleEo);
        //季节;
        if (CollUtil.isNotEmpty(req.getStyleSeasonList())) {
            designStyleEo.setStyleSeason(Json.serialize(req.getStyleSeasonList()));
        }

        //设计师与设计组别
        designStyleEo.setDesignerId(currentUserId);
        if (Objects.nonNull(designerDTO)) {
            designStyleEo.setDesignerCode(designerDTO.getDesignerCode());
            designStyleEo.setDesignerName(designerDTO.getDesignerName());
            designStyleEo.setDesignerGroup(designerDTO.getDesignerGroupName());
            designStyleEo.setDesignerGroupCode(designerDTO.getDesignerGroupCode());
        }

        return designStyleEo;
    }

    public DesignStyle buildSpuImportCreateEo(DesignStyleImportReq req,
                                               String styleCode,
                                               Long currentUserId,
                                               DesignerDTO designerDTO) {
        DesignStyle designStyleEo = DesignStyle.builder()
                .designStyleId(IdPool.getId())
                .styleCode(styleCode)
                .styleStatus(StyleStatusEnum.WAIT_SUBMIT.getCode()) //缺少必填信息, 编辑后才是提交状态
                .versionNum(1)
                .latestSubmitTime(LocalDateTime.now())
                .build();
        BeanUtils.copyProperties(req, designStyleEo);

        //设计师与设计组别
        designStyleEo.setDesignerId(currentUserId);
        if (Objects.nonNull(designerDTO)) {
            designStyleEo.setDesignerCode(designerDTO.getDesignerCode());
            designStyleEo.setDesignerName(designerDTO.getDesignerName());
            designStyleEo.setDesignerGroup(designerDTO.getDesignerGroupName());
            designStyleEo.setDesignerGroupCode(designerDTO.getDesignerGroupCode());
        }

        return designStyleEo;
    }

    /**
     * SPU编辑dto
     */
    public DesignStyleUpdateDto buildDesignStyleUpdateDto(DesignStyleUpdateReq req, String styleCode, Integer newVersionNum, LocalDateTime now) {
        DesignStyleUpdateDto updateDto = new DesignStyleUpdateDto();
        BeanUtils.copyProperties(req, updateDto);
        updateDto.setVersionNum(newVersionNum);
        updateDto.setStyleCode(styleCode);
        updateDto.setStyleSeason(Json.serialize(req.getStyleSeasonList()));
        updateDto.setSpuUpdateTime(now);
        return updateDto;
    }

    public CreateProductDto convertProductCreateReq(DesignStyle designStyle, Prototype prototype,
                                                    DesignDemand designDemand, DesignDemandDetail demandDetail,
                                                    PrototypeDetail prototypeDetail, BigDecimal localPrice,
                                                    DictVo dictVoByCategory,List<StyleSizeInfoVo> styleSizeInfoVos){
        CreateProductDto productReq = new CreateProductDto();
        productReq.setStyleType(SdpStyleTypeEnum.DESIGN.getCode());
        //有来源业务id,  designDemand.getSourceBizId(); 也有灵感来源id: designDemand.getInspirationStyleId()
        if (Objects.nonNull(designDemand)) {
            if (Objects.nonNull(designDemand.getSourceBizId())) {
                productReq.setInspiraSourceId(designDemand.getInspirationStyleId());
            }
            productReq.setPlanSourceId(designDemand.getPlanningId());
            productReq.setSelectStyleId(designDemand.getChosenId());
            productReq.setSelectStyleName(designDemand.getChosenName());
            productReq.setSelectStyleTime(designDemand.getChosenTime());
            productReq.setInspiraCountry(designDemand.getCountrySiteName());
            productReq.setShopId(designDemand.getStoreId());
            productReq.setShopName(designDemand.getStoreName());
            if (StrUtil.isNotBlank(designDemand.getCountrySiteName())) {
                productReq.setCountrys(Collections.singletonList(designDemand.getCountrySiteName()));
            }
            //0904
            productReq.setPlanningSourceName(designDemand.getPlanningSourceName());
            productReq.setPlanningSourceCode(designDemand.getPlanningSourceCode());
        }
        if (Objects.nonNull(demandDetail)) {
            productReq.setInspiraImgUrl(StrUtil.join(StrUtil.COMMA, demandDetail.getInspirationImageList()) );
        }
        productReq.setGoodsRepType(designStyle.getPalletTypeName());
        productReq.setGoodsType(designStyle.getProductType());
        productReq.setSupplyMode(designStyle.getSupplyModeCode());
        //主图只要1张
        if (StrUtil.isNotBlank(prototypeDetail.getDesignPicture())) {
            List<String> designPicture = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);
            productReq.setMainImgUrl(designPicture.getFirst());
        }
        productReq.setWaves(designStyle.getWaveBandName());
        productReq.setSpuCode(designStyle.getStyleCode());
        productReq.setCategoryCode(designStyle.getCategory());
        productReq.setCategoryName(designStyle.getCategoryName());
        productReq.setSizeGroupName(designStyle.getSizeStandard());
        productReq.setSizeGroupCode(designStyle.getSizeStandardCode());
        productReq.setClothingStyleCode(designStyle.getClothingStyleCode());
        productReq.setClothingStyleName(designStyle.getClothingStyleName());
        productReq.setPlanningType(designStyle.getPlanningType());
        productReq.setMarketCode(designStyle.getMarketCode());
        productReq.setMarketSeriesCode(designStyle.getMarketSeriesCode());
        productReq.setStyleElementName(designStyle.getElementName());
        productReq.setStyleElementCode(designStyle.getElementCode());
        productReq.setFitCode(designStyle.getFitCode());
        productReq.setFitName(designStyle.getFitName());
        productReq.setStyleSeason(designStyle.getStyleSeason());
        productReq.setDataList(convertSkc(designStyle, prototype, prototypeDetail, localPrice));


        //0904新增
        productReq.setWeaveMode(designStyle.getWeaveMode());
        productReq.setWeaveModeCode(designStyle.getWeaveModeCode());
        productReq.setMarketSeriesCode(designStyle.getMarketSeriesCode());
        productReq.setElasticCode(designStyle.getElasticCode());
        productReq.setElasticName(designStyle.getElasticName());


        productReq.setQualityLevel(designStyle.getQualityLevel());
        productReq.setQualityLevelCode(designStyle.getQualityLevelCode());

        productReq.setProductThemeCode(designStyle.getProductThemeCode());
        productReq.setProductThemeName(designStyle.getProductThemeName());

        productReq.setBuyerName(designStyle.getBuyerName());
        productReq.setBuyerId(designStyle.getBuyerId());
        productReq.setProductLink(designStyle.getReferLink());

        if(CollectionUtil.isNotEmpty(designStyle.getAttributes())){
            List<AttributeVo> attributeVos  = designStyle.getAttributes();

            // 转换为 ProductAttributesV2 列表，以 values 维度转换
            List<ProductAttributesV2Dto> productAttributesV2List = attributeVos.stream()
                    .filter(attributeVo -> attributeVo.getValues() != null) // 过滤掉没有值的属性
                    .flatMap(attributeVo -> attributeVo.getValues().stream()
                            .map(value -> {
                                ProductAttributesV2Dto productAttributesV2 = new ProductAttributesV2Dto();
                                productAttributesV2.setAttributeId(attributeVo.getAttributeId());
                                productAttributesV2.setCategoryId(attributeVo.getCategoryId());
                                productAttributesV2.setAttributeValueId(value.getAttributeValueId());
                                productAttributesV2.setAttributeValue(value.getAttributeValue());
                                return productAttributesV2;
                            }))
                    .collect(Collectors.toList());

            productReq.setAttributesV2List(productAttributesV2List);
        }

        if(CollectionUtil.isNotEmpty(styleSizeInfoVos) && CollectionUtil.isNotEmpty(styleSizeInfoVos.getFirst().getSizeDetailList())){
            sdpOrderChangeSizeDetail(styleSizeInfoVos, productReq);
        }else {
            changeProductSizeDetail(dictVoByCategory, productReq);
        }
        return productReq;
    }

    /**
     * 大货资料转尺码
     * @param styleSizeInfoVos
     * @param productReq
     */
    private static void sdpOrderChangeSizeDetail(List<StyleSizeInfoVo> styleSizeInfoVos, CreateProductDto productReq) {
        // 转换 StyleSizeReq 列表为 ProductSizeDetail 列表
        List<CreateProductDto.ProductSizeDetail> sizeDetails = styleSizeInfoVos.getFirst().getSizeDetailList().stream()
                .map(styleSize -> {
                    CreateProductDto.ProductSizeDetail  detail = new CreateProductDto.ProductSizeDetail();
                    detail.setPartName(styleSize.getPosition());

                    // 转换 sizeList 到 ProductSizeJson 列表
                    if (styleSize.getSizeList() != null) {
                        List<CreateProductDto.ProductSizeJson> sizeJsonList = styleSize.getSizeList().stream()
                                .map(sizeData -> {
                                    CreateProductDto.ProductSizeJson json = new CreateProductDto.ProductSizeJson();
                                    json.setSize(sizeData.getSize());
                                    json.setData(sizeData.getData().toString());
                                    return json;
                                })
                                .collect(Collectors.toList());
                        detail.setSizeJson(sizeJsonList);
                    }

                    return detail;
                })
                .collect(Collectors.toList());
        productReq.setSizeDetails(sizeDetails);
    }

    private void changeProductSizeDetail(DictVo dictVoByCategory, CreateProductDto productReq) {
        if(dictVoByCategory != null && CollectionUtil.isNotEmpty(dictVoByCategory.getAttributes())){

            Optional<team.aikero.admin.common.vo.AttributeVo> codeAttributeOpt = dictVoByCategory.getAttributes().stream()
                    .filter(attr -> attr != null && "CMMXB".equals(attr.getCode()))
                    .findFirst();


            if (codeAttributeOpt.isPresent() && StringUtils.isNotBlank(codeAttributeOpt.get().getName())) {
                String jsonString = codeAttributeOpt.get().getName();
                List<TemplateSizeDetailReq> sizeDetailReqs;

                try {
                    // 尝试解析JSON，失败时显式抛出异常
                    sizeDetailReqs = JSON.parseArray(jsonString, TemplateSizeDetailReq.class);
                } catch (Exception e) {
                    // 日志记录异常信息（可选）
                    log.error("JSON解析失败，原始字符串: {}", jsonString, e);
                    // 抛出明确的运行时异常，可替换为自定义异常
                    throw new RuntimeException("字典里的产品尺寸详情JSON格式错误,请检查配置", e);
                }
                // 转换为 ProductSizeDetail 列表
                List<CreateProductDto.ProductSizeDetail> productSizeDetails = sizeDetailReqs.stream()
                        .map(templateReq -> {
                            CreateProductDto.ProductSizeDetail productSizeDetail = new CreateProductDto.ProductSizeDetail();
                            productSizeDetail.setPartName(templateReq.getPartName());

                            // 转换 SizeData 列表到 ProductSizeJson 列表
                            if (templateReq.getSizeList() != null) {
                                List<CreateProductDto.ProductSizeJson> productSizeJsons = templateReq.getSizeList().stream()
                                        .map(sizeData -> {
                                            CreateProductDto.ProductSizeJson productSizeJson = new CreateProductDto.ProductSizeJson();
                                            productSizeJson.setSize(sizeData.getSize());
                                            // 将 BigDecimal 转换为 String
                                            productSizeJson.setData(sizeData.getData() != null ? sizeData.getData().toString() : null);
                                            return productSizeJson;
                                        })
                                        .collect(Collectors.toList());
                                productSizeDetail.setSizeJson(productSizeJsons);
                            }

                            return productSizeDetail;
                        })
                        .collect(Collectors.toList());

                productReq.setSizeDetails(productSizeDetails);
            }
        }
    }

    private List<CreateProductDto.Skc> convertSkc(DesignStyle designStyle, Prototype prototype,  PrototypeDetail prototypeDetail, BigDecimal localPrice) {
        if (Objects.isNull(prototype)) {
            return List.of();
        }
        CreateProductDto.Skc skcInfo = new CreateProductDto.Skc();
        skcInfo.setSkc(prototype.getDesignCode());


        if (ObjectUtil.isNotNull(prototypeDetail)) {
            String designPicture = prototypeDetail.getDesignPicture();
            if (StrUtil.isNotBlank(designPicture)) {
                skcInfo.setPictures(StrUtil.splitTrim(designPicture,StrUtil.COMMA));
            }
            //颜色信息
            List<ColorInfoVo> colorInfoList = prototypeDetail.getColorInfoList();
            if (CollUtil.isNotEmpty(colorInfoList)) {
                List<String> colorList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColor);
                List<String> abbrCodeList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColorAbbrCode);
                List<String> englishNameList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColorEnglishName);
                String color = StrUtil.join(StrUtil.SPACE, colorList);
                String colorCode = StrUtil.join(StrUtil.SPACE, englishNameList);
                String abbrCode = StrUtil.join(StrUtil.SPACE, abbrCodeList);
                skcInfo.setColor(color);
                skcInfo.setColorCode(colorCode);
                skcInfo.setColorAbbrCode(abbrCode);
            }
        }

        skcInfo.setLocalPrice(localPrice);
        String sizeStandardValue = designStyle.getSizeStandardCode();
        if (StrUtil.isNotBlank(sizeStandardValue)) {
            List<CreateProductDto.Sku> skuList = new ArrayList<>();
            CreateProductDto.Sku sku = new CreateProductDto.Sku();
            if (ObjectUtil.isNotNull(prototypeDetail)) {
                sku.setSizeName(prototypeDetail.getSampleSize());
            }
            skuList.add(sku);
            skcInfo.setSkuList(skuList);
        }

        List<CreateProductDto.Skc> skcList = new ArrayList<>();
        skcList.add(skcInfo);

        return skcList;
    }

    public static DesignStyleCreateReq buildDesignStyleCreateReq4Demand(DesignDemand designDemand, String platformName) {
        DesignStyleCreateReq createReq = new DesignStyleCreateReq();
        createReq.setSourceType(DesignStyleSourceTypeEnum.DESIGN_DEMAND.getCode());
        createReq.setDesignDemandId(designDemand.getDesignDemandId());
        //供给方式, 波段, 建议售价, 货盘类型, 国家, 店铺, 平台, 元素, 选款人(买手) 添加到spu中
        createReq.setSupplyModeCode(designDemand.getSupplyModeCode());
        createReq.setSupplyModeName(designDemand.getSupplyModeName());
        createReq.setSuggestedSellingPrice(designDemand.getSellingPrice());
        //如果是仿款, 建议售价取期望成本
        if (Objects.equals(designDemand.getSupplyModeName(), SdpDesignConstant.COPY_STYLE_NAME)) {
            createReq.setSuggestedSellingPrice(designDemand.getExpectedCostPrice());
        }
        createReq.setPalletTypeCode(designDemand.getPalletTypeCode());
        createReq.setPalletTypeName(designDemand.getPalletTypeName());
        createReq.setWaveBandCode(designDemand.getWaveBandCode());
        createReq.setWaveBandName(designDemand.getWaveBandName());
        createReq.setCountrySiteCode(designDemand.getCountrySiteCode());
        createReq.setCountrySiteName(designDemand.getCountrySiteName());
        createReq.setStoreId(designDemand.getStoreId());
        createReq.setStoreName(designDemand.getStoreName());
        createReq.setPlatformName(platformName);
        createReq.setInspirationImageSource(designDemand.getInspirationImageSource());
        createReq.setInspirationImageSourceCode(designDemand.getInspirationImageSourceCode());
        createReq.setInspirationBrand(designDemand.getInspirationBrand());
        createReq.setInspirationBrandCode(designDemand.getInspirationBrandCode());
        createReq.setProductThemeCode(designDemand.getProductThemeCode());
        createReq.setProductThemeName(designDemand.getProductThemeName());
        createReq.setElementCode(designDemand.getElementCode());
        createReq.setElementName(designDemand.getElementName());
        createReq.setBuyerId(designDemand.getChosenId());
        createReq.setBuyerName(designDemand.getChosenName());
        createReq.setPlanningType(designDemand.getPlanningType());
        createReq.setMarketCode(designDemand.getMarketCode());
        createReq.setMarketSeriesCode(designDemand.getMarketSeriesCode());
        return createReq;
    }
}
