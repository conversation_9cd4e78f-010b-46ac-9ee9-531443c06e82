package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.entity.VisualDemand;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualDemandByIdReq;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualDemandBySpuReq;
import tech.tiangong.sdp.design.vo.resp.visual.VisualDemandVo;

/**
 * (VisualDemand)服务接口
 */
public interface VisualDemandService {

    /**
     * 获取需求详情
     * @param demandId
     */
    VisualDemandVo getVisualDemandVo(Long demandId);

    /**
     * 前端发起创建视觉需求
     * @param req
     */
    VisualDemand createVisualDemand4Front(SaveVisualDemandBySpuReq req);

    /**
     * 创建视觉需求-尺寸表任务
     * @param styleCode spu
     * @return 视觉需求
     */
    VisualDemand createVisualDemand4ProdOrder(String styleCode);

    /**
     * 根据需求ID编辑保存视觉需求
     * @param req
     */
    VisualDemand saveVisualDemandById(SaveVisualDemandByIdReq req);

    /**
     * 根据spuCode保存视觉需求
     * @param req
     * @return
     */
    VisualDemand saveVisualDemandBySpu(SaveVisualDemandBySpuReq req);
}
