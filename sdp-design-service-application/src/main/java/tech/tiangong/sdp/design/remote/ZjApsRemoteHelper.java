package tech.tiangong.sdp.design.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import team.aikero.blade.core.protocol.DataResponse;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.remote.zj.aps.ZjApsSupplierClient;
import tech.tiangong.sdp.design.vo.req.zj.ZjSupplierReq;
import tech.tiangong.sdp.design.vo.req.zj.aps.SupplierInfoDataReq;
import tech.tiangong.sdp.design.vo.resp.zj.aps.SupplierSimpleResp;

import java.util.List;

/**
 * <p>
 *  致景APS_接口调用helper
 * </p>
 *
 **/
@Service
@Slf4j
@AllArgsConstructor
public class ZjApsRemoteHelper {

    private final ZjApsSupplierClient zjApsSupplierClient;

    /**
     * 获取供应商列表
     * @param req 入参
     */
    public List<SupplierSimpleResp> queryApsSupplier(ZjSupplierReq req){
        SupplierInfoDataReq dataReq = new SupplierInfoDataReq();
        dataReq.setSupplierName(req.getSupplierName());
        dataReq.setSupplierCode(req.getSupplierCode());
        dataReq.setSupplierId(req.getSupplierId());
        dataReq.setSupplierState(1);
        log.info("=== APS供应商查询-req：{} ===", JSONObject.toJSONString(dataReq));
        try {
            DataResponse<List<SupplierSimpleResp>> response = zjApsSupplierClient.getSupplierName(dataReq);
            log.info("=== APS供应商查询-response:{}", JSON.toJSONString(response));
            SdpDesignException.isTrue(response.getSuccessful(), response.getMessage());
            return response.getData();
        } catch (Exception e) {
            throw new SdpDesignException("APS供应商查询失败:"+e.getMessage(), e);
        }
    }

}
