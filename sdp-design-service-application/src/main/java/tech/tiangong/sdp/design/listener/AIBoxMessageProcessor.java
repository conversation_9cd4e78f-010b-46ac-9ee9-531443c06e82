package tech.tiangong.sdp.design.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.design.listener.entity.AIBoxTaskNotification;
import tech.tiangong.sdp.design.listener.entity.Tag;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * AI Box消息处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AIBoxMessageProcessor {
    
    private final List<AIBoxTaskHandler> handlers;
    private Map<Tag, AIBoxTaskHandler> handlerMap;
    
    /**
     * 初始化处理器映射
     */
    private void initHandlerMap() {
        if (handlerMap == null) {
            handlerMap = handlers.stream()
                    .collect(Collectors.toMap(
                            AIBoxTaskHandler::getSupportedTag,
                            Function.identity()
                    ));
        }
    }
    
    /**
     * 处理消息
     */
    public ConsumeResult processMessage(Tag tag, AIBoxTaskNotification taskNotification) {
        initHandlerMap();
        
        AIBoxTaskHandler handler = handlerMap.get(tag);
        if (handler == null) {
            log.warn("未找到标签 {} 对应的处理器，忽略消息", tag);
            return ConsumeResult.SUCCESS;
        }
        
        return switch (taskNotification.status()) {
            case RUNNING -> {
                handler.handleTaskCreation(taskNotification);
                yield ConsumeResult.SUCCESS;
            }
            case COMPLETED -> {
                handler.handleTaskUpdate(taskNotification);
                yield ConsumeResult.SUCCESS;
            }
            case FAILED -> {
                handler.handleTaskFailure(taskNotification);
                yield ConsumeResult.SUCCESS;
            }
            default -> {
                log.info("未识别的任务状态: {}, 忽略处理", taskNotification.status());
                yield ConsumeResult.SUCCESS;
            }
        };
    }
}