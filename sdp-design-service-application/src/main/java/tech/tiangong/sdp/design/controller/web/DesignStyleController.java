package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tech.tiangong.sdp.design.service.DesignStyleService;
import tech.tiangong.sdp.design.vo.dto.product.PopProductImageChangeStateDto;
import tech.tiangong.sdp.design.vo.query.style.DesignStyleQuery;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleCreateReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleImportReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleUpdateReq;
import tech.tiangong.sdp.design.vo.req.style.SpuProductImgReq;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleCreateResp;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleImportResp;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleVo;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleWebDetailVo;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;


/**
 * SPU管理-web
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/design-spu")
public class DesignStyleController extends BaseController {
    private final DesignStyleService designStyleService;

    /**
     * 查询列表（分页）
     *
     * @param queryDTO 分页对象
     * @return PageRespVo<DesignSpuVo>
     */
    @GetMapping("/page")
    public DataResponse<PageRespVo<DesignStyleVo>> page(DesignStyleQuery queryDTO) {
        return DataResponse.ok(designStyleService.page(queryDTO));
    }

    /**
     * 编辑页详情查询
     *
     * @param styleCode spu编码
     * @return 响应结果
     */
    @GetMapping("/web-detail/{styleCode}")
    public DataResponse<DesignStyleWebDetailVo> getWebDetail(@PathVariable(value = "styleCode") String styleCode) {
        return DataResponse.ok(designStyleService.getWebDetail(styleCode));
    }

    /**
     * 新建SPU-创建SPU与SKC
     *
     * @param req 请求参数对象
     * @deprecated 该创建入口废弃, 使用图片导入或excel导入  -素材中心1期
     * @return 响应结果
     */
    @Deprecated
    @PostMapping("/save")
    @NoRepeatSubmitLock(lockTime = 10L)
    public DataResponse<DesignStyleCreateResp> createSpuSkc(@RequestBody @Validated DesignStyleCreateReq req) {
        return DataResponse.ok(designStyleService.createSpuSkc(req));
    }

    /**
     * 导入SPU
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PostMapping("/import")
    @NoRepeatSubmitLock(lockTime = 5L)
    public DataResponse<DesignStyleImportResp> importSpu(@RequestBody @Validated DesignStyleImportReq req) {
        return DataResponse.ok(designStyleService.importSpu(req));
    }

    /**
     * excel导入
     * @param file excel文件
     * @return 响应结果
     */
    @NoRepeatSubmitLock(lockTime = 5L)
    @PostMapping("/excel-import")
    public DataResponse<List<DesignStyleImportResp>> excelImport(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            return DataResponse.ok(designStyleService.excelImport(inputStream));
        }
    }


    /**
     * 编辑SPU
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    @PutMapping("/update")
    @NoRepeatSubmitLock(lockTime = 10L)
    public DataResponse<Void> updateSpu(@RequestBody @Validated DesignStyleUpdateReq req) {
        designStyleService.updateSpu(req);
        return DataResponse.ok();
    }

    /**
     * 从POP查询spu上架图信息
     * @param req 入参
     * @return PopProductImageChangeStateDto
     */
    @PostMapping("/pop/list-product-picture")
    public DataResponse<PopProductImageChangeStateDto> listPopProductPicture(@RequestBody @Validated SpuProductImgReq req) {
        return DataResponse.ok(designStyleService.listPopProductPicture(req));
    }

    /**
     * 初始化风格历史数据
     * @param file
     * @return
     */
    @PostMapping("/initHisDataV2")
    public DataResponse<Void> initHisDataV2(MultipartFile file){
        designStyleService.initHisDataV2(file);
        return DataResponse.ok();
    }


}
