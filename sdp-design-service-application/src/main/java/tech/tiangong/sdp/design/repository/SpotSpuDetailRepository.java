package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.bean.user.UserContent;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.SpotSpuDetail;
import tech.tiangong.sdp.design.mapper.SpotSpuDetailMapper;
import tech.tiangong.sdp.design.mq.rocketmq.entity.MurmurationTaskStatusEnum;
import tech.tiangong.sdp.design.vo.dto.spot.SpotSpuCommunicationDto;
import tech.tiangong.sdp.design.vo.dto.spot.SpotSpuDetailUpdateDto;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuDetailVo;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * spot_spu_detail表(SpotSpuDetail)服务仓库类
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:49
 */
@Slf4j
@Repository
public class SpotSpuDetailRepository extends BaseRepository<SpotSpuDetailMapper, SpotSpuDetail> {
    private final static DateTimeFormatter LOG_TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("MM-dd HH:mm:ss");


    public List<SpotSpuDetail> listBySpotSpuIds(Collection<Long> spotSpuIds){
        if(CollectionUtils.isEmpty(spotSpuIds)){
           return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SpotSpuDetail>()
                .in(SpotSpuDetail::getSpotSpuId,spotSpuIds));
    }

    public SpotSpuDetail getBySpotSpuId(Long spotSpuId){
        if(spotSpuId == null){
            return null;
        }
        return getOne(new LambdaQueryWrapper<SpotSpuDetail>()
                .eq(SpotSpuDetail::getSpotSpuId,spotSpuId),false);
    }
    public List<SpotSpuDetail> listRecentDaysSpuDetail(Integer day,Integer aiAlibabaDistributionSyncStatus, Integer limit) {
        return list(new LambdaQueryWrapper<SpotSpuDetail>()
                .eq(SpotSpuDetail::getAiAlibabaDistributionStatus, MurmurationTaskStatusEnum.COMPLETED.getCode())
                .eq(SpotSpuDetail::getAiAlibabaDistributionSyncStatus, aiAlibabaDistributionSyncStatus)
                .ge(SpotSpuDetail::getCreatedTime, LocalDateTime.now().minusDays(day))
                .orderByAsc(SpotSpuDetail::getCreatedTime)
                .last("limit " + limit));
    }

    public List<SpotSpuDetailVo> getByStyleCodes(List<String> styleCodes) {
        return  baseMapper.getByStyleCodes(styleCodes);
    }

    public void updateDetail(SpotSpuDetailUpdateDto updateDto, UserContent userContent) {
        SdpDesignException.notNull(updateDto, "入参为null");
        SdpDesignException.notNull(updateDto.getSpotSpuDetailId(), "spotSpuDetailId为空! ");

        lambdaUpdate()
                .set(SpotSpuDetail::getStyleSeasonList, JSON.toJSONString(updateDto.getStyleSeasonList()))
                .set(SpotSpuDetail::getProductLink, updateDto.getProductLink())
                .set(SpotSpuDetail::getProductPictureList, JSON.toJSONString(updateDto.getProductPictureList()))
                .set(SpotSpuDetail::getTryOnPictureList, JSON.toJSONString(updateDto.getTryOnPictureList()))
                .set(SpotSpuDetail::getRevisedTime, Objects.isNull(updateDto.getSpuUpdateTime()) ? LocalDateTime.now() : updateDto.getSpuUpdateTime())
                .set(SpotSpuDetail::getReviserId, userContent.getCurrentUserId())
                .set(SpotSpuDetail::getReviserName, userContent.getCurrentUserName())
                .set(SpotSpuDetail::getAttributes, JSON.toJSONString(updateDto.getAttributes()))
                .eq(SpotSpuDetail::getSpotSpuDetailId, updateDto.getSpotSpuDetailId())
                .update();
    }

    public List<SpotSpuDetail> listByProductIds(List<Long> productIds) {
        return list(new LambdaQueryWrapper<SpotSpuDetail>()
                .in(!CollectionUtils.isEmpty(productIds), SpotSpuDetail::getProductId, productIds)
                .isNotNull(SpotSpuDetail::getProductId)
                .isNotNull(SpotSpuDetail::getProductPictureList)
        );
    }

    public void updatePictureListByProductId(SpotSpuDetail updateDto) {
        lambdaUpdate()
                .set(SpotSpuDetail::getProductPictureList, JSON.toJSONString(updateDto.getProductPictureList()))
                .eq(SpotSpuDetail::getProductId, updateDto.getProductId())
                .update();
    }


    // ============ 货通商品推送相关查询方法 ============

    /**
     * 查询待推送的货通商品
     * 基于spot_spu_detail.ai_attribute_result查询
     * 
     * @param batchSize 批量大小
     * @return 待推送商品列表
     */
    public List<SpotSpuCommunicationDto> queryPendingCommunicationProducts(Integer batchSize) {
        if (batchSize == null || batchSize <= 0) {
            return Collections.emptyList();
        }
        return baseMapper.queryPendingCommunicationProducts(batchSize);
    }

    /**
     * 查询指定styleCode的货通商品详情
     * 基于spot_spu_detail.ai_attribute_result查询
     * 
     * @param styleCode 商品编码
     * @return 商品详情DTO，如果不存在返回null
     */
    public SpotSpuCommunicationDto queryPendingCommunicationProductByStyleCode(String styleCode) {
        if (styleCode == null || styleCode.trim().isEmpty()) {
            return null;
        }
        return baseMapper.queryPendingCommunicationProductByStyleCode(styleCode);
    }

    /**
     * 统计待推送商品数量
     * 基于spot_spu_detail.ai_attribute_result统计
     * 
     * @return 待推送商品总数
     */
    public Long countPendingCommunicationProducts() {
        Long count = baseMapper.countPendingCommunicationProducts();
        return count != null ? count : 0L;
    }

    /**
     * 更新SpotSpuDetail表的推送失败原因
     *
     * @param spuId SPU主键ID
     * @param failReason 失败原因（为空时不处理，为空字符串时清空）
     */
    public void updateSpotSpuDetailFailReason(Long spuId, String failReason) {
        if (failReason == null) {
            return;
        }

        try {
            // 查询对应的SpotSpuDetail记录
            SpotSpuDetail spotSpuDetail = lambdaQuery().select(SpotSpuDetail::getSpotSpuDetailId, SpotSpuDetail::getPushPopFailReason)
                    .eq(SpotSpuDetail::getSpotSpuId, spuId).orderByDesc(SpotSpuDetail::getCreatedTime)
                    .last("LIMIT 1").one();

            if (spotSpuDetail == null) {
                log.warn("更新SpotSpuDetail失败原因失败：未找到对应记录, spuId={}", spuId);
                return;
            }

            // 查询现有的详细失败原因，用于智能拼接
            String existingDetailFailReason = spotSpuDetail.getPushPopFailReason();

            SpotSpuDetail updateEntity = new SpotSpuDetail();
            updateEntity.setSpotSpuDetailId(spotSpuDetail.getSpotSpuDetailId());

            if (failReason.isEmpty()) {
                // 成功时清空失败原因
                updateEntity.setPushPopFailReason("");
            } else {
                // 失败时智能拼接失败原因（TEXT字段不需要截断）
                String finalDetailFailReason = buildConcatenatedText(existingDetailFailReason, failReason);
                updateEntity.setPushPopFailReason(finalDetailFailReason);
            }

            updateById(updateEntity);
            log.debug("SpotSpuDetail {} 推送失败原因已更新", spotSpuDetail.getSpotSpuDetailId());
        } catch (Exception e) {
            log.error("更新SpotSpuDetail推送失败原因异常: spuId={}, error={}", spuId, e.getMessage(), e);
        }
    }

    /**
     * 更新SpotSpuDetail表的推送过程日志
     *
     * @param spuId SPU主键ID
     * @param messageLog 日志信息（为空时不处理，为空字符串时清空）
     */
    public void updateSpotSpuDetailMessageLog(Long spuId, String messageLog) {
        if (messageLog == null) {
            return;
        }

        try {
            // 查询对应的SpotSpuDetail记录
            SpotSpuDetail spotSpuDetail = lambdaQuery().select(SpotSpuDetail::getSpotSpuDetailId, SpotSpuDetail::getPushPopMessageLog)
                    .eq(SpotSpuDetail::getSpotSpuId, spuId).orderByDesc(SpotSpuDetail::getCreatedTime)
                    .last("LIMIT 1").one();

            if (spotSpuDetail == null) {
                log.warn("更新SpotSpuDetail消息日志失败：未找到对应记录, spuId={}", spuId);
                return;
            }

            // 查询现有的消息日志，用于智能拼接
            String existingMessageLog = spotSpuDetail.getPushPopMessageLog();

            SpotSpuDetail updateEntity = new SpotSpuDetail();
            updateEntity.setSpotSpuDetailId(spotSpuDetail.getSpotSpuDetailId());

            if (messageLog.isEmpty()) {
                // 清空日志
                updateEntity.setPushPopMessageLog("");
            } else {
                // 智能拼接日志信息
                String finalMessageLog = buildConcatenatedText(existingMessageLog, messageLog);
                updateEntity.setPushPopMessageLog(finalMessageLog);
            }

            updateById(updateEntity);
            log.debug("SpotSpuDetail {} 推送消息日志已更新", spotSpuDetail.getSpotSpuDetailId());
        } catch (Exception e) {
            log.error("更新SpotSpuDetail推送消息日志异常: spuId={}, error={}", spuId, e.getMessage(), e);
        }
    }

    /**
     * 构建拼接的消息日志
     *
     * @param existingMessageLog 现有消息日志
     * @param newMessageLog 新消息日志
     * @return 拼接后的消息日志
     */
    private String buildConcatenatedText(String existingMessageLog, String newMessageLog) {
        if (StringUtils.isBlank(newMessageLog)) {
            return existingMessageLog;
        }

        String timestamp = LocalDateTime.now().format(LOG_TIMESTAMP_FORMATTER);
        String newEntry = "[" + timestamp + "] " + newMessageLog;

        if (StringUtils.isBlank(existingMessageLog)) {
            return newEntry;
        }

        // 拼接新日志到前面，保持时间顺序
        return newEntry + "\n" + existingMessageLog;
    }
}
