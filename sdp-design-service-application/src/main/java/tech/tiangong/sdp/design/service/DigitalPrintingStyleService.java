package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.query.digital.DigitalPrintingQuery;
import tech.tiangong.sdp.design.vo.req.digital.*;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingDetailVo;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingQueryVo;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingRePushVo;
import tech.tiangong.sdp.design.vo.resp.digital.DpStyleAddVo;
import tech.tiangong.sdp.design.vo.resp.style.PopCreateProductVo;

import java.util.List;

/**
 * 数码印花_服务接口
 *
 * <AUTHOR>
 */
public interface DigitalPrintingStyleService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<DigitalPrintingQueryVo> page(DigitalPrintingQuery query);

    /**
     * 查询详情
     *
     * @param printingPrototypeId 主键
     * @return 数据实体
     */
    DigitalPrintingDetailVo getDetailById(Long printingPrototypeId);

    /**
     * 失败重推
     * @return 推送信息
     */
    DigitalPrintingRePushVo rePush();

    /**
     * 批量创建数码印花款
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    List<DpStyleAddVo> batchAdd(DpStyleBatchAddReq req);

    /**
     * 创建数码印花款
     *
     * @param req 请求参数对象
     * @return 响应结果
     */
    DpStyleAddVo save(DpStyleAddReq req);

    /**
     * 更新生产资料
     *
     * @param req 入参
     */
    void updateProductFile(DpProductFileUpdateReq req);

    /**
     * 更新商品推送pop状态
     *
     * @param req 入参
     */
    void updatePushState(DpPushStateUpdateReq req);

    /**
     * 根据spu与状态推送pop
     * @param req 入参
     */
    void pushFailBySpu(DpPushPopReq req);

    /**
     * 根据spu查询pop创建商品的信息
     * @param styleCodeList spu集合
     * @return pop创建商品信息
     */
    List<PopCreateProductVo> queryStyleInfo4Pop(List<String> styleCodeList);

    /**
     * 修正推送状态(刷数接口)
     *  已经成功推送到pop,但推送状态还不是成功的
     * @param req 入参
     */
    void fixFailState(DpPushStateFixReq req);

}
