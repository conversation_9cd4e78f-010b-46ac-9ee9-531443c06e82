package tech.tiangong.sdp.design.service;


import tech.tiangong.sdp.design.vo.req.prototype.FirstVersionTimeUpdateReq;
import tech.tiangong.sdp.design.vo.req.prototype.SkcOnShelfImg2ZjReq;
import tech.tiangong.sdp.design.vo.req.prototype.SyncOnShelfImg2ZjReq;
import tech.tiangong.sdp.design.vo.req.prototype.SyncOnShelfImgReq;
import tech.tiangong.sdp.design.vo.resp.style.ProductImageSyncVo;

import java.util.List;

/**
 * 版单-数据处理服务接口
 *
 * <AUTHOR>
 */
public interface PrototypeDataHandleService {

    /**
     * 根据采购商编码 加密对应联系人手机号
     * @param purchaserCode 采购商编码(若为空, 加密所有未加密的手机号)
     * @return 加密的手机号数量
     */
    Integer encryptPurchaserPhone(String purchaserCode);

    /**
     * 加密数据
     * @param phoneNum 手机号
     * @return 加密后的结果
     */
    String encryptPhone(String phoneNum);

    /**
     * 刷新首次拆板提交时间-prototype表中first_version_done_time字段
     * @return 更新数量
     * @param req
     */
    Integer updateFirstVersionTime(FirstVersionTimeUpdateReq req);

    /**
     * 买手款StyleVersionId修复
     * @param designCode 设计款号,为空时刷新全部
     * @return 更新数量
     */
    Integer updateBuyerStyleVersionId(String designCode);

    /**
     * 同步商品上架图
     * @return 更新数量
     */
    ProductImageSyncVo syncOnShelfImg(SyncOnShelfImgReq req);

    /**
     * 根据skc推送上架图给致景
     */
    List<String> onShelfImg2Zj(SkcOnShelfImg2ZjReq req);

    /**
     * 同步上架图给致景-可全量同步
     */
    Integer syncImg2Zj(SyncOnShelfImg2ZjReq req);

    /**
     * 对接致景-更新旧款bizChannel
     * @param styleCode spu
     */
    void updateBizChannel(String styleCode);
}