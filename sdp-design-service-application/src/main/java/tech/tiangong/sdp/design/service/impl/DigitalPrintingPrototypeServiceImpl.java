package tech.tiangong.sdp.design.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.entity.DigitalPrintingPrototype;
import tech.tiangong.sdp.design.repository.DigitalPrintingPrototypeRepository;
import tech.tiangong.sdp.design.service.DigitalPrintingPrototypeService;
import tech.tiangong.sdp.design.vo.req.digital.DigitalPrintingPrototypeReq;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingPrototypeVo;

/**
 * 数码印花_SKC表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalPrintingPrototypeServiceImpl implements DigitalPrintingPrototypeService {
    private final DigitalPrintingPrototypeRepository digitalPrintingPrototypeRepository;

    @Override
    public DigitalPrintingPrototypeVo getById(Long id) {
        DigitalPrintingPrototype entity = digitalPrintingPrototypeRepository.getById(id);
        DigitalPrintingPrototypeVo vo = new DigitalPrintingPrototypeVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(DigitalPrintingPrototypeReq req) {
        DigitalPrintingPrototype entity = new DigitalPrintingPrototype();
        BeanUtils.copyProperties(req, entity);
        digitalPrintingPrototypeRepository.save(entity);
    }

}
