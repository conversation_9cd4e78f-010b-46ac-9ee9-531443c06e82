package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.query.bom.BomMaterialDemandTransientQuery;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialDemandTransientReq;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialDemandTransientVo;

import java.util.List;

/**
 * bom物料需求_暂存表服务接口
 *
 * <AUTHOR>
 */
public interface BomMaterialDemandTransientService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<BomMaterialDemandTransientVo> page(BomMaterialDemandTransientQuery query);

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    BomMaterialDemandTransientVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(BomMaterialDemandTransientReq req);

    /**
     * 更新数据
     *
     * @param req 数据实体
     */
    void update(BomMaterialDemandTransientReq req);

    /**
     * 删除数据
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 根据暂存bomId查询暂存需求信息集合
     * @param bomTransientId bom暂存id
     * @return 暂存需求信息集合
     */
    List<BomMaterialDemandTransientVo> listByBomTransientId(Long bomTransientId);
}
