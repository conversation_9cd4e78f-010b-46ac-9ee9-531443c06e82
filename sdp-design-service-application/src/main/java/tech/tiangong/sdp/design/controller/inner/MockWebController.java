package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.core.aop.ResetCurrentUser;
import tech.tiangong.sdp.design.remote.zj.design.ZjPlmDesignRemoteHelper;
import tech.tiangong.sdp.design.service.BomDataHandleService;
import tech.tiangong.sdp.design.service.SpecialAccessoriesService;
import tech.tiangong.sdp.design.vo.req.zj.design.PrototypeInfoOpenV2Req;
import tech.tiangong.sdp.design.vo.resp.zj.design.PrototypeInfoOpenV2Resp;

/**
 * bom单号和数据清洗
 *
 * 一个skc只有1个bom单号，skc不同则bom单号不同，后续修改只更新版本号
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/13 14:48
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/bom/mock")
public class MockWebController {

	private final BomDataHandleService bomDataHandleService;
	private final SpecialAccessoriesService specialAccessoriesService;
	private final ZjPlmDesignRemoteHelper plmDesignRemoteHelper;

	/**
	 * 一个skc只有1个bom单号，skc不同则bom单号不同，后续修改只更新版本号。bom单号取skc首次生成bom单的时间及流水。
	 * @return void
	 */
	@ResetCurrentUser
	@PostMapping("/update-bomCode")
	public DataResponse<Void> updateBomCodeByDesignCode(@RequestParam(required = false)  String designCode){
		bomDataHandleService.updateBomCodeByDesignCode(designCode);
		return DataResponse.ok();
	}


	/**
	 * 清洗Bom物料清单数据,缺少履约用量核算单位 值(plm设计2.1.2新加）
	 */
	@GetMapping("/material/dosage-account-unit")
	public DataResponse<Void> purgeBomMaterialDosageAccountUnitData(@RequestParam(required = false) String designCode){
		bomDataHandleService.purgeBomMaterialDosageAccountUnitData(designCode);
		return DataResponse.ok();
	}

	/**
	 * 清洗Bom物料清单数据,缺少履约用量核算单位 值(plm设计2.1.2新加） http://localhost:8080/inner/v1/bom/mock/zj-skc
	 */
	@GetMapping("/zj-skc")
	public DataResponse<PrototypeInfoOpenV2Resp> prototypeInfo(@RequestParam(required = false) Long prototypeId){
		PrototypeInfoOpenV2Req v2Req = new PrototypeInfoOpenV2Req();
		v2Req.setBizChannel(3);
		v2Req.setExtPrototypeId(prototypeId);
		DataResponse<PrototypeInfoOpenV2Resp> response = plmDesignRemoteHelper.prototypeInfo(v2Req);
		return DataResponse.ok(response.getData());
	}

	/**
	 * 手动加密工艺敏感信息
	 */
	@ResetCurrentUser
	@PostMapping("/encrypt/craft")
	public DataResponse<Void> encryptCraftSensitiveInfo(){
		bomDataHandleService.encryptCraftSensitiveInfo();
		return DataResponse.ok();
	}

	/**
	 * 清洗Bom特殊辅料-商品货号
	 */
	@ResetCurrentUser
	@PostMapping("/special-accessories/commodity-number")
	public DataResponse<Void> purgeSpecialAccessoriesCommodityNumber(){
		specialAccessoriesService.purgeCommodityNumber();
		return DataResponse.ok();
	}

	/**
	 * 清洗Bom特殊辅料-合作关系,包装数量单位,最小价格单位
	 */
	@ResetCurrentUser
	@PostMapping("/special-accessories/min-price")
	public DataResponse<Void> purgeSpecialAccessoriesMinPrice(){
		specialAccessoriesService.purgeSpecialAccessoriesMinPrice();
		return DataResponse.ok();
	}



}
