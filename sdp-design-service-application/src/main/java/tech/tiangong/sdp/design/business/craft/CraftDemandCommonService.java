package tech.tiangong.sdp.design.business.craft;

import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjkj.scf.bundle.common.dto.demand.dto.request.CraftDemandAddReq;
import com.zjkj.scf.bundle.common.dto.demand.dto.request.CraftDemandAddReqWrapper;
import com.zjkj.scf.bundle.common.dto.demand.dto.response.DemandCreateResp;
import com.zjkj.scf.bundle.common.dto.demand.enums.DemandCraftsRequireEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.clothes.mq.CraftDemandMqConstant;
import tech.tiangong.sdp.clothes.mq.body.BomOrderCraftDemandSyncMessage;
import tech.tiangong.sdp.clothes.vo.resp.SecondCraftDemandDetailVo;
import tech.tiangong.sdp.design.converter.CraftDemandInfoConverter;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.entity.PrototypeDetail;
import tech.tiangong.sdp.design.entity.PrototypeHistory;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.DemandRemoteHelper;
import tech.tiangong.sdp.design.repository.CraftDemandInfoRepository;
import tech.tiangong.sdp.design.repository.PrototypeDetailRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工艺需求公共服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/20 14:11
 */
@Slf4j
@AllArgsConstructor
@Service
public class CraftDemandCommonService {

	private final CraftDemandInfoRepository craftDemandInfoRepository;
	private final MqProducer mqProducer;
	private final DemandRemoteHelper demandRemoteHelper;
	private final PrototypeDetailRepository prototypeDetailRepository;

	/**
	 * 工艺需求同步给样衣打版
	 *
	 * @param addCraftDemandList
	 * @param delCraftDemandList
	 * @param prototypeId
	 * @param designCode
	 * @param bomId
	 */
	public void handlerCraftDemandToClothes(List<CraftDemandInfo> addCraftDemandList, List<CraftDemandInfo> delCraftDemandList,
	                                         Long prototypeId, String designCode, Long bomId) {
		log.info("工艺需求同步给样衣打版 addCraftDemandList:{} delCraftDemandList:{} prototypeId:{}", JSON.toJSONString(addCraftDemandList), JSON.toJSONString(delCraftDemandList), prototypeId);

		if (CollectionUtil.isEmpty(addCraftDemandList) && CollectionUtil.isEmpty(delCraftDemandList)) {
			log.info("工艺需求同步给样衣打版 无数据");
			return;
		}

		BomOrderCraftDemandSyncMessage demandSyncMessage = new BomOrderCraftDemandSyncMessage();
		demandSyncMessage.setPrototypeId(prototypeId);
		demandSyncMessage.setDesignCode(designCode);
		demandSyncMessage.setBomId(bomId);
		UserContent userContent = UserContentHolder.get();
		demandSyncMessage.setCreator(userContent.getCurrentUserId());
		demandSyncMessage.setCreatedName(userContent.getCurrentUserName());
		demandSyncMessage.setCreatedTime(LocalDateTime.now());

		//删除工艺需求
		if (CollectionUtil.isNotEmpty(delCraftDemandList)) {
			List<Long> thirdPartyCraftDemandIdList = delCraftDemandList.stream().map(CraftDemandInfo::getThirdPartyCraftDemandId).collect(Collectors.toList());
			List<Long> delCraftDemandIdList = craftDemandInfoRepository.getListByThirdPartyCraftDemandIds(thirdPartyCraftDemandIdList)
					.stream().map(CraftDemandInfo::getCraftDemandId).collect(Collectors.toList());
			demandSyncMessage.setDelCraftDemandIdList(delCraftDemandIdList);
		}

		//拆版新增工艺同步样衣只推送裁后工艺
		if (CollectionUtil.isNotEmpty(addCraftDemandList)) {
			List<BomOrderCraftDemandSyncMessage.CraftInfo> craftInfoList = addCraftDemandList.stream().filter(craft ->
					Objects.equals(craft.getCraftsRequire(), DemandCraftsRequireEnum.POST_CUTTING_CRAFTS.getCode()))
					.map(craft -> {
						BomOrderCraftDemandSyncMessage.CraftInfo craftInfo = new BomOrderCraftDemandSyncMessage.CraftInfo();
						BeanUtils.copyProperties(craft, craftInfo);
						return craftInfo;
					}).collect(Collectors.toList());
			demandSyncMessage.setAddCraftInfoList(craftInfoList);
		}

		MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.BOM_CRAFT_DEMAND_SYNC_CLOTHES,
				CraftDemandMqConstant.BOM_ORDER_CRAFT_SYNC_EXCHANGE, CraftDemandMqConstant.BOM_ORDER_CRAFT_SYNC_ROUTING_KEY,
				JSON.toJSONString(demandSyncMessage));
		//发送消息
		mqProducer.sendOnAfterCommit(mqMessageReq);

	}

	/**
	 * 处理将新增二次工艺给履约组
	 * @param newCraftDemandInfoList
	 * @param prototypeHistory
	 */
	public void handlerAddCraftDemandToSupplyChain(List<CraftDemandInfo> newCraftDemandInfoList, PrototypeHistory prototypeHistory) {
		if (CollectionUtil.isEmpty(newCraftDemandInfoList)) {
			return;
		}
		PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototypeHistory.getPrototypeId());

		List<CraftDemandAddReqWrapper> craftDemandAddReqList = newCraftDemandInfoList.stream()
				.map(craft -> {
					CraftDemandAddReq craftDemandAddReq = CraftDemandInfoConverter.assemblyCraftDemandAddReq(craft, prototypeHistory, prototypeDetail);
					CraftDemandAddReqWrapper demandAddReqWrapper = new CraftDemandAddReqWrapper();
					demandAddReqWrapper.setCraftDemandAddReq(craftDemandAddReq);
					return demandAddReqWrapper;
				}).collect(Collectors.toList());

		List<DemandCreateResp> demandCreateRespList = demandRemoteHelper.craftAddToSupplyChain(craftDemandAddReqList);
		Map<Long, DemandCreateResp> demandCreateRespMap = demandCreateRespList.stream().collect(Collectors.toMap(demandResp -> {
			Map<String, Object> extra = demandResp.getExtra();
			return (Long) extra.get(CraftDemandInfoConverter.CRAFT_DEMAND_ID);
		}, Function.identity(), (k1, k2) -> k1));

		newCraftDemandInfoList.forEach(addCraft -> {
			DemandCreateResp demandCreateResp = demandCreateRespMap.get(addCraft.getCraftDemandId());
			if (Objects.nonNull(demandCreateResp)) {
				addCraft.setThirdPartyCraftDemandId(demandCreateResp.getDemandId());
				addCraft.setThirdPartyCraftDemandCode(demandCreateResp.getDemandCode());
			}
		});
	}

	public void handlerCloseCraftDemandToSupplyChain(Set<Long> delCraftDemandIdList) {
		if (CollectionUtil.isEmpty(delCraftDemandIdList)) {
			return;
		}

		List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.list(
				new QueryWrapper<CraftDemandInfo>().lambda().in(CraftDemandInfo::getCraftDemandId, delCraftDemandIdList)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode()));

		List<Long> thirdPartyCraftDemandIds = craftDemandInfoList.stream().filter(craftDemandInfo -> Objects.nonNull(craftDemandInfo.getThirdPartyCraftDemandId()))
				.map(CraftDemandInfo::getThirdPartyCraftDemandId).collect(Collectors.toList());
		if (CollectionUtil.isEmpty(thirdPartyCraftDemandIds)) {
			log.info("【拆版删除工艺需求】还未与履约工艺需求关联");
			return;
		}

		// 兼容版货一本化第一版数据： 工艺取消时,也同时将这个工艺的子需求取消
		List<SecondCraftDemandDetailVo> secondCraftDemandList = demandRemoteHelper.getSecondCraftByThirdPartyDemand(thirdPartyCraftDemandIds);
		if (CollectionUtil.isNotEmpty(secondCraftDemandList)) {
			List<Long> thirdPartyChildCraftDemandIds = secondCraftDemandList.stream().filter(second -> Objects.nonNull(second.getThirdPartyChildCraftDemandId()))
					.map(SecondCraftDemandDetailVo::getThirdPartyChildCraftDemandId).collect(Collectors.toList());
			if (CollectionUtil.isNotEmpty(thirdPartyChildCraftDemandIds)) {
				thirdPartyCraftDemandIds.addAll(thirdPartyChildCraftDemandIds);
			}
		}

		demandRemoteHelper.craftCloseToSupplyChain(thirdPartyCraftDemandIds.stream().distinct().collect(Collectors.toList()));
	}
}
