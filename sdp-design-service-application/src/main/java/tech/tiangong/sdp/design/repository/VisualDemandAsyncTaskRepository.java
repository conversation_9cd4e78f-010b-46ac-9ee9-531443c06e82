package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualDemandAsyncTask;
import tech.tiangong.sdp.design.mapper.VisualDemandAsyncTaskMapper;

import java.util.List;


/**
 *
 */
@Repository
public class VisualDemandAsyncTaskRepository extends BaseRepository<VisualDemandAsyncTaskMapper, VisualDemandAsyncTask> {


    /**
     * 查询所有待执行的
     */
    public List<VisualDemandAsyncTask> getPendingTasks() {
        LambdaQueryWrapper<VisualDemandAsyncTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(VisualDemandAsyncTask::getState, List.of(0, 3));
        return this.list(wrapper);
    }

}
