package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.business.Transaction;
import tech.tiangong.sdp.design.business.bom.craft.CraftFirstSubmitNoTransientTransaction;
import tech.tiangong.sdp.design.business.bom.craft.CraftReSubmitNoTransientTransaction;
import tech.tiangong.sdp.design.business.bom.notification.CraftNotification;
import tech.tiangong.sdp.design.business.bom.notification.Notification;
import tech.tiangong.sdp.design.business.bom.notification.ReplenishPreCuttingCraftNotification;
import tech.tiangong.sdp.design.business.bom.notification.SyncCraftClothesNotification;
import tech.tiangong.sdp.design.converter.CraftDemandInfoConverter;
import tech.tiangong.sdp.design.converter.NotificationConverter;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.BomOrderMaterial;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.entity.CraftDemandInfoTransient;
import tech.tiangong.sdp.design.enums.CraftDemandStateEnum;
import tech.tiangong.sdp.design.repository.BomOrderMaterialRepository;
import tech.tiangong.sdp.design.repository.BomOrderRepository;
import tech.tiangong.sdp.design.repository.CraftDemandInfoRepository;
import tech.tiangong.sdp.design.repository.CraftDemandInfoTransientRepository;
import tech.tiangong.sdp.design.service.BomCraftSubmitHandleService;
import tech.tiangong.sdp.design.vo.dto.bom.CraftNotificationDto;
import tech.tiangong.sdp.design.vo.dto.bom.ReplenishPreCuttingCraftDto;
import tech.tiangong.sdp.design.vo.dto.bom.SyncCraftClothesDto;
import tech.tiangong.sdp.design.vo.req.bom.BomCraftSubmitHandleReq;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * bom提交 工艺处理服务
 *
 * <AUTHOR>
 * @date 2022/11/17 11:30
 */


@Slf4j
@Service
@RequiredArgsConstructor
public class BomCraftSubmitHandleServiceImpl implements BomCraftSubmitHandleService {
    private final CraftDemandInfoTransientRepository craftDemandInfoTransientRepository;
    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final BomOrderRepository bomOrderRepository;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void firstSubmitNoTransient(BomCraftSubmitHandleReq req) {

        // 工艺需求新增(); //新增的工艺需求, 维护到原表

        // 删除工艺处理, 历史数据可能有删除的工艺(2.0时工艺维护中拆板中, 拆板提交物料确认后,, 待提交的bom就有物料与工艺了)

        // 要处理工艺需求创建于同步: 推送履约,同步版房,补推裁前工艺

        Notification<CraftNotificationDto> craftNotification = new CraftNotification();
        Notification<ReplenishPreCuttingCraftDto> replenishPreCuttingCraftNotification = new ReplenishPreCuttingCraftNotification();
        Notification<SyncCraftClothesDto> syncCraftClothesNotification = new SyncCraftClothesNotification();
        Transaction craftFirstSubmitNoTransientTransaction = new CraftFirstSubmitNoTransientTransaction(req, craftNotification, replenishPreCuttingCraftNotification,syncCraftClothesNotification);
        craftFirstSubmitNoTransientTransaction.execute();

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void firstSubmitWithTransient(BomCraftSubmitHandleReq req) {
        //调用这个接口前，需调用暂存接口 @see BomCraftTransientHandleService.reTransient
        submit(req, false);
    }


    private List<CraftNotificationDto> buildNotification(List<CraftDemandInfo> addCraftDemands) {
        List<CraftNotificationDto> craftNotificationDtos = addCraftDemands.stream().map(craftDemandInfo -> {
            CraftNotificationDto craftNotificationDto = new CraftNotificationDto();
            BeanUtils.copyProperties(craftDemandInfo, craftNotificationDto);
            return craftNotificationDto;
        }).collect(Collectors.toList());
        return craftNotificationDtos;
    }


    private List<CraftDemandInfo> handleNotModifyCraft(BomCraftSubmitHandleReq req, List<CraftDemandInfo> deleteCraftDemandInfos, List<CraftDemandInfo> addCraftDemands) {

        Map<Long, CraftDemandInfo> deleteCraftDemandInfosMap = deleteCraftDemandInfos.stream().collect(Collectors.toMap(CraftDemandInfo::getCraftDemandId, craftDemandInfo -> craftDemandInfo));
        Map<Long, CraftDemandInfo> addCraftDemandsMap = addCraftDemands.stream().collect(Collectors.toMap(CraftDemandInfo::getCraftDemandId, craftDemandInfo -> craftDemandInfo));
        List<CraftDemandInfoTransient> craftDemandInfoTransients = craftDemandInfoTransientRepository.listByBomId(req.getNewBomOrderId());
        if (CollectionUtil.isEmpty(craftDemandInfoTransients)) {
            return List.of();
        }

        return craftDemandInfoTransients.stream().filter(e -> Objects.isNull(deleteCraftDemandInfosMap.get(e.getOriginCraftDemandId())))
                .filter(e -> Objects.isNull(addCraftDemandsMap.get(e.getCraftDemandId()))).map(craftDemandInfoTransient -> {
                    CraftDemandInfo craftDemandInfo = new CraftDemandInfo();
                    BeanUtils.copyProperties(craftDemandInfoTransient, craftDemandInfo);
                    craftDemandInfo.setBomId(req.getNewBomOrderId());
                    return craftDemandInfo;
                }).collect(Collectors.toList());
    }


    private List<CraftDemandInfo> handleDeleteCraft(BomCraftSubmitHandleReq req) {
        if (CollectionUtil.isEmpty(req.getDelCraftDemandIds())) {
            return Collections.EMPTY_LIST;
        }

        if (CollectionUtil.isEmpty(req.getOldTransientCraftMap())) {
            throw new SdpDesignException("需要删除的工艺，工艺需求暂存数据为空");
        }

        List<CraftDemandInfo> delCraftDemandInfoList = req.getDelCraftDemandIds().stream().map(craftDemandId -> {
            CraftDemandInfoTransient craftDemandInfoTransient = req.getOldTransientCraftMap().get(craftDemandId);
            SdpDesignException.notNull(craftDemandInfoTransient, "需要删除的工艺，工艺需求暂存数据为空. 工艺craftDemandId: " + craftDemandId);

            CraftDemandInfo craftDemandInfo = new CraftDemandInfo();
            BeanUtils.copyProperties(craftDemandInfoTransient, craftDemandInfo);
            craftDemandInfo.setBomId(req.getNewBomOrderId());
            craftDemandInfo.setState(CraftDemandStateEnum.CLOSED.getCode());
            return craftDemandInfo;
        }).collect(Collectors.toList());

        return delCraftDemandInfoList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reSubmitNoTransient(BomCraftSubmitHandleReq req) {
        Notification<CraftNotificationDto> craftNotification = new CraftNotification();
        Notification<ReplenishPreCuttingCraftDto> replenishPreCuttingCraftNotification = new ReplenishPreCuttingCraftNotification();
        Notification<SyncCraftClothesDto> syncCraftClothesNotification = new SyncCraftClothesNotification();
        Transaction craftReSubmitNoTransientTransaction = new CraftReSubmitNoTransientTransaction(req, craftNotification, replenishPreCuttingCraftNotification,syncCraftClothesNotification);
        craftReSubmitNoTransientTransaction.execute();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reSubmitWithTransient(BomCraftSubmitHandleReq req) {
        //调用这个接口前，需调用暂存接口 @see BomCraftTransientHandleService.reTransient

        submit(req, true);
    }

    private void submit(BomCraftSubmitHandleReq req, boolean isReSubmit) {
        Notification<CraftNotificationDto> craftNotification = new CraftNotification();
        Notification<ReplenishPreCuttingCraftDto> replenishPreCuttingCraftNotification = new ReplenishPreCuttingCraftNotification();

        BomOrder newBomOrder = bomOrderRepository.getById(req.getNewBomOrderId());

        List<CraftDemandInfo> saveCraftDemandInfos = new ArrayList<>(10);

        //1. 若有删除工艺，传过来的是craft_demand_info的craft_demand_id
        List<CraftDemandInfo> deleteCraftDemandInfos = handleDeleteCraft(req);

        //2. 新增工艺
        List<CraftDemandInfo> addCraftDemands = req.getAddCraftDemandList().stream()
                .map(craftDemandReq -> CraftDemandInfoConverter.newCraftDemandV3(craftDemandReq, newBomOrder)).collect(Collectors.toList());
        //3. 要拿没改动的数据，也要复制到原表，然后要过滤已关闭的工艺
        List<CraftDemandInfo> notModifyCraftDemands = handleNotModifyCraft(req, deleteCraftDemandInfos, addCraftDemands);


        //保存到数据库
        saveCraftDemandInfos.addAll(deleteCraftDemandInfos);
        saveCraftDemandInfos.addAll(addCraftDemands);
        saveCraftDemandInfos.addAll(notModifyCraftDemands);
        craftDemandInfoRepository.saveBatch(saveCraftDemandInfos);


        //4. 通知工艺
        craftNotification.addBatch(buildNotification(addCraftDemands));

        if (Boolean.TRUE.equals(isReSubmit)) {
            //4. 重新提交，需要把原来的工艺需求关闭,推送到履约
            craftNotification.addBatch(buildNotification(deleteCraftDemandInfos));

            //补推裁前工艺、打版
            List<Long> addNotModifyBomMaterialIds = Stream.of(addCraftDemands).flatMap(List::stream).map(CraftDemandInfo::getBomMaterialId).collect(Collectors.toList());
            Map<Long, BomOrderMaterial> addNotModifyBomMaterialMap = Map.of();
            if(CollectionUtil.isNotEmpty(addNotModifyBomMaterialIds)){
                addNotModifyBomMaterialMap = bomOrderMaterialRepository.listByIds(addNotModifyBomMaterialIds).stream().collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, bomOrderMaterial -> bomOrderMaterial));
            }
            replenishPreCuttingCraftNotification.add(NotificationConverter.buildReplenishPreCuttingCraftDto(addCraftDemands, req.getBomOrder(), addNotModifyBomMaterialMap));

        } else {
            //待提交的，有暂存，如果没改里面的工艺，要推送到履约。 若是已提交的状态，不需要推送到履约
            craftNotification.addBatch(buildNotification(notModifyCraftDemands));

            //补推裁前工艺、打版
            List<Long> addNotModifyBomMaterialIds = Stream.of(addCraftDemands, notModifyCraftDemands).flatMap(List::stream).map(CraftDemandInfo::getBomMaterialId).collect(Collectors.toList());
            Map<Long, BomOrderMaterial> addNotModifyBomMaterialMap = Map.of();
            if (CollectionUtil.isNotEmpty(addNotModifyBomMaterialIds)) {
                addNotModifyBomMaterialMap = bomOrderMaterialRepository.listByIds(addNotModifyBomMaterialIds).stream().collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, bomOrderMaterial -> bomOrderMaterial));
            }

            replenishPreCuttingCraftNotification.add(NotificationConverter.buildReplenishPreCuttingCraftDto(addCraftDemands, req.getBomOrder(), addNotModifyBomMaterialMap));
            replenishPreCuttingCraftNotification.add(NotificationConverter.buildReplenishPreCuttingCraftDto(notModifyCraftDemands, req.getBomOrder(), addNotModifyBomMaterialMap));
        }

        //推送二次工艺到履约
        craftNotification.send();
        //补推裁前工艺、打版
        replenishPreCuttingCraftNotification.send();

    }
}
