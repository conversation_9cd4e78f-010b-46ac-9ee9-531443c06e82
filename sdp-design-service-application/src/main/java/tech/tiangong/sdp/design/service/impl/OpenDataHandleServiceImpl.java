package tech.tiangong.sdp.design.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.service.OpenDataHandleService;
import tech.tiangong.sdp.design.vo.req.open.OpenNotifyReq;


/**
 * <AUTHOR>
 * @date 2023/8/15 16:03
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class OpenDataHandleServiceImpl implements OpenDataHandleService {

    private final MqProducer mqProducer;


    /**
     * zj通知处理
     *
     * @param req 入参
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void zjNotify(OpenNotifyReq req) {
        log.info("===zj消息通知-req: {} ===", JSON.toJSONString(req));
        mqProducer.sendOnAfterCommit(MqMessageReq.build(
                MqBizTypeEnum.ZJ_NOTIFY,
                req.getEvent(),
                req.getDestination(),
                req.getContent()));
        log.info("===zj消息通知完毕===");
    }

}
