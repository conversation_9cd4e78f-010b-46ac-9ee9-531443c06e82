package tech.tiangong.sdp.design.service;

import org.springframework.web.multipart.MultipartFile;
import tech.tiangong.sdp.design.entity.VisualSpu;
import tech.tiangong.sdp.design.enums.SdpStyleTypeEnum;

import java.util.Collection;
import java.util.List;

/**
 * (VisualSpu)服务接口
 */
public interface VisualSpuService {

    /**
     * 初始化SPU信息
     * @param styleCode
     */
    VisualSpu initVisualSpu(String styleCode);
    /**
     * 根据SPU编码更新3D任务状态
     * @param styleCodes
     */
    void changeDimensionState(Collection<String> styleCodes);

    /**
     * 更新spu信息
     *
     * @param styleCode        spu
     * @param sdpStyleTypeEnum spu类型
     * @param platformName 平台名称
     */
    void updateSpu(String styleCode, SdpStyleTypeEnum sdpStyleTypeEnum, String platformName);

    /**
     * 取消SPU后取消相关任务
     * @param styleCodes
     */
    void cancelSpu(List<String> styleCodes);

    /**
     * 根据SPU编码更新动销状态
     * @param styleCode spu
     * @param isOnSale 动销状态
     */
    void updateOnSale(String styleCode, Integer isOnSale);

    /**
     * 根据SPU编码更新多色款
     * @param styleCode spu
     * @param isMultiColor 多色款状态
     */
    void updateMultiColor(String styleCode, Integer isMultiColor);

    void initHisDataV2(MultipartFile file);

    /**
     * 根据SPU编码更新花型状态
     * @param styleCode spu
     * @param flowerState 花型状态
     */
    void updateFlowerState(String styleCode, Integer flowerState);

}
