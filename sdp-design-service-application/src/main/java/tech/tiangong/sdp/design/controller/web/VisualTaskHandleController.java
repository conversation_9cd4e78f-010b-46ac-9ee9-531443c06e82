package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.enums.visual.VisualErrorCodeEnum;
import tech.tiangong.sdp.design.service.VisualTaskHandleService;
import tech.tiangong.sdp.design.vo.req.visual.OnShelfHandleReq;
import tech.tiangong.sdp.design.vo.req.visual.TryOnHandleReq;


/**
 * 视觉任务处理-web
 *
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/visual-task-handle")
public class VisualTaskHandleController extends BaseController {
    private final VisualTaskHandleService visualTaskHandleService;

    /**
     * tryOn处理
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/try-on-handle")
    public DataResponse<Void> tryOnHandle(@RequestBody @Validated TryOnHandleReq req) {
        try{
            visualTaskHandleService.tryOnHandle(req);
        }catch (Exception e){
            VisualErrorCodeEnum visualErrorCode = VisualErrorCodeEnum.findByCode(e.getMessage());
            if(!VisualErrorCodeEnum.UNKNOWN.equals(visualErrorCode)){
                log.error("tryOn处理失败",e);
                DataResponse<Void> dataResponse = DataResponse.failed();
                dataResponse.setCode(visualErrorCode.getCode());
                dataResponse.setMessage(visualErrorCode.getDesc());
                dataResponse.setSuccessful(false);
                return dataResponse;
            }
            throw e;
        }
        return DataResponse.ok();
    }
    /**
     * 修图处理
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/on-shelf-handle")
    public DataResponse<Void> onShelfHandle(@RequestBody @Validated OnShelfHandleReq req) {
        try{
            visualTaskHandleService.onShelfHandle(req);
        }catch (Exception e){
            VisualErrorCodeEnum visualErrorCode = VisualErrorCodeEnum.findByCode(e.getMessage());
            if(!VisualErrorCodeEnum.UNKNOWN.equals(visualErrorCode)){
                log.error("修图处理失败",e);
                DataResponse<Void> dataResponse = DataResponse.failed();
                dataResponse.setCode(visualErrorCode.getCode());
                dataResponse.setMessage(visualErrorCode.getDesc());
                dataResponse.setSuccessful(false);
                return dataResponse;
            }
            throw e;
        }
        return DataResponse.ok();
    }
}
