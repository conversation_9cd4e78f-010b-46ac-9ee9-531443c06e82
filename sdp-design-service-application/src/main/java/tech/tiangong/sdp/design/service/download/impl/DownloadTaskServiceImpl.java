package tech.tiangong.sdp.design.service.download.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.DesignAsyncTask;
import tech.tiangong.sdp.design.enums.DownloadTaskStatusEnum;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.repository.DesignAsyncTaskRepository;
import tech.tiangong.sdp.design.service.download.DownloadTaskService;
import tech.tiangong.sdp.design.vo.dto.download.FileUploadDTO;
import tech.tiangong.sdp.design.vo.query.download.DownloadTaskQuery;
import tech.tiangong.sdp.design.vo.resp.download.DownloadTaskPageVo;
import tech.tiangong.sdp.design.vo.resp.download.DesignAsyncTaskVo;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 下载任务管理
 *
 * <AUTHOR>
 * @date 2024/8/27
 */
@Slf4j
@Service
@AllArgsConstructor
public class DownloadTaskServiceImpl implements DownloadTaskService {

    private final DesignAsyncTaskRepository designAsyncTaskRepository;

    @Override
    public Long createTask(String taskName, DesignAsyncTaskTypeEnum taskTypeEnum, String parameters) {
        SdpDesignException.notBlank(taskName, "创建下载任务时任务名不能为空");
        SdpDesignException.notNull(taskTypeEnum, "下载任务类型不能为空");

        DesignAsyncTask task = new DesignAsyncTask();
        task.setAsyncTaskId(IdPool.getId());
        task.setTaskName(taskName);
        task.setTaskType(taskTypeEnum.getTaskType());
        task.setBusinessType(taskTypeEnum.getCode());
        task.setParameters(parameters);
        task.setState(DownloadTaskStatusEnum.PENDING.getCode());
        designAsyncTaskRepository.save(task);

        return task.getAsyncTaskId();
    }

    @Override
    public void cancelTask(Long taskId) {
        DesignAsyncTask task = designAsyncTaskRepository.getById(taskId);
        if (ObjectUtils.notEqual(DownloadTaskStatusEnum.PENDING.getCode(), task.getState())) {
            throw new SdpDesignException("任务非等待状态无法取消");
        }

        DesignAsyncTask updateTask = new DesignAsyncTask();
        updateTask.setAsyncTaskId(taskId);
        updateTask.setState(DownloadTaskStatusEnum.CANCELLED.getCode());
        designAsyncTaskRepository.updateById(updateTask);
    }

    @Override
    public void retryTask(Long taskId) {
        DesignAsyncTask task = designAsyncTaskRepository.getById(taskId);
        if (Objects.equals(DownloadTaskStatusEnum.PENDING.getCode(), task.getState())
                || Objects.equals(DownloadTaskStatusEnum.PROCESSING.getCode(), task.getState())) {
            throw new SdpDesignException("无法重试，任务正在处理中，请稍等");
        }

        DesignAsyncTask retryTask = new DesignAsyncTask();
        retryTask.setAsyncTaskId(IdPool.getId());
        retryTask.setState(DownloadTaskStatusEnum.PENDING.getCode());
        retryTask.setTaskName(task.getTaskName());
        retryTask.setParameters(task.getParameters());
        retryTask.setTaskType(task.getTaskType());
        retryTask.setBusinessType(task.getBusinessType());
        designAsyncTaskRepository.save(retryTask);

        DesignAsyncTask updateTask = new DesignAsyncTask();
        updateTask.setAsyncTaskId(taskId);
        updateTask.setRetryCount(Objects.isNull(task.getRetryCount()) ? 1 : task.getRetryCount() + 1);
        designAsyncTaskRepository.updateById(updateTask);

    }

    @Override
    public DesignAsyncTask getOneTask() {
        LambdaQueryWrapper<DesignAsyncTask> lqw = Wrappers.lambdaQuery();
        lqw.eq(DesignAsyncTask::getState, DownloadTaskStatusEnum.PENDING.getCode())
                .orderByAsc(DesignAsyncTask::getCreatedTime).last(" LIMIT 1");
        return designAsyncTaskRepository.getOne(lqw);
    }

    @Override
    public PageRespVo<DownloadTaskPageVo> page(DownloadTaskQuery req) {
        log.info("开始查询选款任务分页数据，请求参数：{}", req);

        IPage<DesignAsyncTaskVo> page = designAsyncTaskRepository.findPage(req);
        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.empty();
        }

        List<DownloadTaskPageVo> pageVoList = page.getRecords().stream().map(item -> {
            DownloadTaskPageVo pageVo = new DownloadTaskPageVo();
            BeanUtils.copyProperties(item, pageVo);
            Optional.ofNullable(pageVo.getState())
                    .map(DownloadTaskStatusEnum::findByCode)
                    .map(DownloadTaskStatusEnum::getDesc).ifPresent(pageVo::setStatusName);
            String result = item.getResult();
            if (StrUtil.isNotBlank(result)) {
                List<FileUploadDTO> resultFiles = JSON.parseArray(result, FileUploadDTO.class);
                pageVo.setResultFileList(resultFiles);
            }
            return pageVo;
        }).toList();

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), pageVoList);
    }

    @Override
    public void updateById(DesignAsyncTask freshTask) {
        designAsyncTaskRepository.updateById(freshTask);
    }

    @Override
    public List<DesignAsyncTask> getDownloadTasks() {
        return designAsyncTaskRepository.findPendingTask(1);
    }

    @Override
    public DesignAsyncTask getById(Long id) {
        if (Objects.isNull(id)) {
            throw new SdpDesignException("获取下载任务入参主键不能为空");
        }
        return designAsyncTaskRepository.getById(id);
    }
}
