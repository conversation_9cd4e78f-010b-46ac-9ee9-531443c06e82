package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualTask;
import tech.tiangong.sdp.design.enums.visual.VisualTaskNodeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskProcessTypeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskStateEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskTypeEnum;
import tech.tiangong.sdp.design.mapper.VisualTaskMapper;
import tech.tiangong.sdp.design.vo.query.visual.BaseVisualPageQuery;
import tech.tiangong.sdp.design.vo.query.visual.VisualTaskQuery;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskInfoVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskStateCountVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskStepNodeStateCountVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskTypeCountVo;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * (VisualTask)服务仓库类
 */
@Repository
public class VisualTaskRepository extends BaseRepository<VisualTaskMapper, VisualTask> {

    public String selectLatestProcessCode() {
        VisualTask visualTask = lambdaQuery()
                .select(VisualTask::getProcessCode)
                .orderByDesc(VisualTask::getCreatedTime)
                .last("limit 1").one();
        return Optional.ofNullable(visualTask).map(VisualTask::getProcessCode).orElse("");
    }

    public List<VisualTask> listLatestByDemandIds(List<Long> demandIds) {
        return list(new LambdaQueryWrapper<VisualTask>()
                .in(VisualTask::getLatestDemandId, demandIds)
                .eq(VisualTask::getIsLatest,Bool.YES.getCode())
                .eq(VisualTask::getIsDeleted,Bool.NO.getCode()));
    }

    public List<VisualTask> listLatestVisualTaskBySpu(List<String> styleCodes){
        if(CollectionUtil.isEmpty(styleCodes)){
            return CollUtil.newArrayList();
        }
        return list(new LambdaQueryWrapper<VisualTask>()
                .in(VisualTask::getStyleCode,styleCodes)
                .eq(VisualTask::getIsLatest, Bool.YES.getCode())
                .eq(VisualTask::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualTask::getCreatedTime));
    }

    public List<VisualTask> queryLatestVisualTaskBySpu(String styleCode){
        return list(new LambdaQueryWrapper<VisualTask>()
                .eq(VisualTask::getStyleCode,styleCode)
                .eq(VisualTask::getIsLatest, Bool.YES.getCode())
                .eq(VisualTask::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualTask::getCreatedTime));
    }

    /**
     * 根据SPU查询最新的上新任务
     * @param styleCode
     * @return
     */
    public VisualTask getLatestNewArrivalTaskBySpu(String styleCode){
        return getOne(new LambdaQueryWrapper<VisualTask>()
                .eq(VisualTask::getStyleCode,styleCode)
                .eq(VisualTask::getTaskType, VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode())
                .eq(VisualTask::getIsLatest, Bool.YES.getCode())
                .eq(VisualTask::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualTask::getCreatedTime),false);
    }

    /**
     * 查询最新的非取消状态的非尺寸任务（可能是上新任务或者是优化任务）
     * @param styleCode
     * @return
     */
    public VisualTask getLatestNotCancelAndNotSizeTaskBySpu(String styleCode){
        return getOne(new LambdaQueryWrapper<VisualTask>()
                .eq(VisualTask::getStyleCode,styleCode)
                .eq(VisualTask::getIsCancel,Bool.NO.getCode())
                .eq(VisualTask::getIsLatest, Bool.YES.getCode())
                .eq(VisualTask::getIsDeleted, Bool.NO.getCode()),false);
    }

    /**
     * 查询最新的尺寸表任务(非取消状态的)
     * @param styleCode
     * @return
     */
    public VisualTask getLatestNotCancelSizeTaskBySpu(String styleCode){
        return getOne(new LambdaQueryWrapper<VisualTask>()
                .eq(VisualTask::getStyleCode,styleCode)
                .eq(VisualTask::getTaskType,VisualTaskTypeEnum.SIZE_TASK.getCode())
                .eq(VisualTask::getIsCancel,Bool.NO.getCode())
                .eq(VisualTask::getIsLatest, Bool.YES.getCode())
                .eq(VisualTask::getIsDeleted, Bool.NO.getCode()),false);
    }

    public VisualTask getLatestTaskBySpuAndProcessType(String styleCode,VisualTaskProcessTypeEnum visualTaskHandleType){
        if (StrUtil.isBlank(styleCode) || visualTaskHandleType==null) {
            return null;
        }
        return lambdaQuery()
                .eq(VisualTask::getStyleCode, styleCode)
                .eq(VisualTask::getProcessType, visualTaskHandleType.getCode())
                .eq(VisualTask::getIsLatest, Bool.YES.getCode())
                .eq(VisualTask::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualTask::getCreatedTime)
                .last("limit 1")
                .one();
    }


    /**
     * 根据spu与任务类型查询最新在途任务
     */
    public List<VisualTask> queryLatestHandlingTaskBySpuTaskType(String styleCode, List<Integer> taskTypeList) {
        if (StrUtil.isBlank(styleCode) || CollUtil.isEmpty(taskTypeList)) {
            return null;
        }
        return lambdaQuery()
                .eq(VisualTask::getStyleCode, styleCode)
                .in(VisualTask::getTaskType, taskTypeList)
                .in(VisualTask::getState,Arrays.asList(VisualTaskStateEnum.WAITING.getCode(),VisualTaskStateEnum.DOING.getCode()))
                .eq(VisualTask::getIsCancel, Bool.NO.getCode())
                .eq(VisualTask::getIsLatest, Bool.YES.getCode())
                .eq(VisualTask::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualTask::getCreatedTime)
                .list();
    }
    public List<VisualTask> listLatestHandlingTaskBySpuTaskType(Collection<String> styleCodeList, List<Integer> taskTypeList) {
        if (CollUtil.isEmpty(styleCodeList) || CollUtil.isEmpty(taskTypeList)) {
            return null;
        }
        return lambdaQuery()
                .in(VisualTask::getStyleCode, styleCodeList)
                .in(VisualTask::getTaskType, taskTypeList)
                .in(VisualTask::getState,Arrays.asList(VisualTaskStateEnum.WAITING.getCode(),VisualTaskStateEnum.DOING.getCode()))
                .eq(VisualTask::getIsCancel, Bool.NO.getCode())
                .eq(VisualTask::getIsLatest, Bool.YES.getCode())
                .eq(VisualTask::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualTask::getCreatedTime)
                .list();
    }

    public List<VisualTask> listLatestBySpuTaskType(List<String> styleCodeList, List<Integer> taskTypeList) {
        if (CollUtil.isEmpty(styleCodeList) || CollUtil.isEmpty(taskTypeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(VisualTask::getStyleCode, styleCodeList)
                .in(VisualTask::getTaskType, taskTypeList)
                .eq(VisualTask::getIsLatest, Bool.YES.getCode())
                .eq(VisualTask::getIsDeleted, Bool.NO.getCode())
                .list();
    }

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<VisualTaskInfoVo> queryByPage(VisualTaskQuery query) {
         return baseMapper.queryByPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    /**
     * 任务类型统计
     * @return
     */
    public List<VisualTaskTypeCountVo> getVisualTaskTypeCountVo(VisualTaskQuery query){
        List<VisualTaskTypeCountVo> list = baseMapper.getVisualTaskTypeCountVo(query);
        if(CollectionUtil.isNotEmpty(list)){
            list.forEach(item->{
                VisualTaskTypeEnum visualTaskType = VisualTaskTypeEnum.findByCode(item.getTaskTypeCode());
                item.setTaskTypeDesc(visualTaskType.getDesc());
            });
        }
        return list;
    }

    /**
     * 任务状态统计
     * @return
     */
    public List<VisualTaskStateCountVo> getVisualTaskStateCountVo(VisualTaskQuery query){
        List<VisualTaskStateCountVo> list = baseMapper.getVisualTaskStateCountVo(query);
        if(CollectionUtil.isNotEmpty(list)){
            list.forEach(item->{
                VisualTaskStateEnum visualTaskState = VisualTaskStateEnum.findByCode(item.getTaskStateCode());
                item.setTaskStateDesc(visualTaskState.getDesc());
            });
        }
        return list;
    }

    /**
     * 任务环节节点状态统计
     * @return
     */
    public List<VisualTaskStepNodeStateCountVo> getVisualTaskStepNodeStateCountVo(BaseVisualPageQuery query){
        List<VisualTaskStepNodeStateCountVo> list = baseMapper.getVisualTaskStepNodeStateCountVo(query);
        Map<String, VisualTaskStepNodeStateCountVo> countVoMap = CollectionUtil.isEmpty(list) ? new HashMap<>()
                : list.stream().collect(Collectors.toMap(v -> v.getProcessNode() + "-" + v.getNodeState(), v -> v, (o1, o2) -> o2));

        List<VisualTaskStepNodeStateCountVo> result = new ArrayList<>();
        //没有统计到结果的数据，补全
        Stream.of(VisualTaskNodeEnum.values()).filter(v -> !v.name().equals("UNKNOWN"))
                .forEach(stepNode -> {
                    result.addAll(stepNode.getStateEnum().getBizStateDtoList().stream()
                            .filter(e -> !e.getName().equals("UNKNOWN"))
                            .map(state -> {
                                return countVoMap.getOrDefault(stepNode.getCode() + "-" + state.getCode(), new VisualTaskStepNodeStateCountVo().setCount(0))
                                        .setProcessStep(stepNode.getVisualStep().getCode())
                                        .setProcessStepDesc(stepNode.getVisualStep().getDesc())
                                        .setProcessNode(stepNode.getCode())
                                        .setProcessNodeDesc(stepNode.getDesc())
                                        .setNodeState(state.getCode())
                                        .setNodeStateDesc(state.getDesc());
                            }).toList());
                });
        return result;
    }

}
