package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.vo.dto.ChangeDesignerDto;
import tech.tiangong.sdp.design.vo.req.ordermaterial.*;
import tech.tiangong.sdp.design.vo.resp.material.MaterialPurchaseFollowVo;
import tech.tiangong.sdp.design.vo.resp.material.OrderMaterialInfoVo;
import tech.tiangong.sdp.design.vo.resp.ordermaterial.MaterialDemandVo;
import tech.tiangong.sdp.design.vo.resp.ordermaterial.OrderMaterialFollowPageVo;

import java.util.List;

/**
 *
 * 物料齐套跟进
 * <br>CreateDate August 10,2021
 * <AUTHOR>
 * @since 1.0
 */
public interface OrderMaterialFollowService  {

    /**
     * 物料齐套跟进列表信息（分页）
     *
     * @param req 分页的查询参数
     * @return void
     */
    PageRespVo<OrderMaterialFollowPageVo> pageList(OrderMaterialFollowPageReq req);

    /**
     * 物料齐套跟进新增  同步履约的齐套信息
     *
     * @param req          物料齐套跟进新增信息参数
     * @param mqMessageReq mq消息体
     */
    void syncOrderMaterialCreateOrUpdateState(OrderMaterialFollowAddReq req, MqMessageReq mqMessageReq);

    /**
     * 物料齐套跟进新增  同步履约的齐套信息
     *
     * @param req          物料齐套跟进新增信息参数
     * @param mqMessageReq mq消息体
     */
    void syncOrderMaterialDetailInfo(OrderMaterialDetailReq req, MqMessageReq mqMessageReq);

    /**
     * 扫码管理--齐套签收  -- 齐套签收列表查询
     *
     * @param designCode 设计款号
     * @return List<MaterialPurchaseFollowVo>
     */
    List<MaterialPurchaseFollowVo> allSetSignMaterialList(String designCode);


    /**
     * 齐套签收
     *
     * @param designCode 设计款号
     */
    void sign(String designCode);

    /**
     * 根据设计款号和加工单号查询齐套单状态
     *
     * @param req
     * @return boolean
     */
    boolean getOrderMaterialStatus(OrderMaterialStatusInnerReq req);

    /**
     * 齐套签收 app端
     *
     * @param req 以齐套单号维度进行签收
     * @return String
     */
    String signByOrderMaterialCode(SignMaterialOrderReq req);


    /**
     * 查询剪版单或者齐套单下的工艺信息
     *
     * @param req 请求参数
     */
    List<MaterialDemandVo> materialOrderToCraft(MaterialOrderToCraftReq req);

    /**
     * 齐套签收（app端使用）
     *
     * @param req 齐套签收 app端（手输设计款号签收）参数
     */
    String signByApp(SignMaterialOrderToDesignCodeReq req);


    /**
     * 变更设计师 -- 以设计款号的维度
     * @param dto 设计师信息
     */
    void orderMaterialChangeDesigner(ChangeDesignerDto dto);


    /**
     * 根据设计款号查询齐套单信息
     *
     * @param req 入参
     * @return List<OrderMaterialInfoVo>
     */
    List<OrderMaterialInfoVo> listByDesignCode(OrderMaterialInnerReq req);

    /**
     * 根据设计款号从致景查询齐套单信息
     * @param designCode 设计款号
     * @return 齐套单json信息
     */
    String queryZjOrder(String designCode);
}
