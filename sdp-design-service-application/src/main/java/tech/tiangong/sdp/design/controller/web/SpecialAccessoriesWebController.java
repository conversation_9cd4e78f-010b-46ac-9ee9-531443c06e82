package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.SpecialAccessoriesService;
import tech.tiangong.sdp.design.vo.req.special.accessories.SpecialAccessoriesProductReq;
import tech.tiangong.sdp.design.vo.resp.special.accessories.SpecialAccessoriesDetailResp;
import tech.tiangong.sdp.design.vo.resp.special.accessories.SpecialAccessoriesProductResp;

import java.util.List;

/**
 * 特殊辅料-web
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/4 19:07
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/special-accessories")
public class SpecialAccessoriesWebController {

	private final SpecialAccessoriesService specialAccessoriesService;

	/**
	 * 特殊辅料商品列表
	 * @return
	 */
	@PostMapping("/product/page")
	public DataResponse<PageRespVo<SpecialAccessoriesProductResp>> productPage(@RequestBody SpecialAccessoriesProductReq req) {
		PageRespVo<SpecialAccessoriesProductResp> respVo = specialAccessoriesService.productPage(req);
		return DataResponse.ok(respVo);
	}


	/**
	 * 获取设计款号最新的BOM单特殊辅料详情
	 * @param designCode
	 * @return
	 */
	@GetMapping("/latest/detail")
	public DataResponse<List<SpecialAccessoriesDetailResp>> latestDetail(@RequestParam String designCode) {
		List<SpecialAccessoriesDetailResp> detailRespList = specialAccessoriesService.latestDetail(designCode);
		return DataResponse.ok(detailRespList);
	}


}
