package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.bean.user.UserContent;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.SpotSpu;
import tech.tiangong.sdp.design.enums.spot.SpotPopPushStatusEnum;
import tech.tiangong.sdp.design.mapper.SpotSpuMapper;
import tech.tiangong.sdp.design.vo.dto.SpuSkcNumDto;
import tech.tiangong.sdp.design.vo.dto.spot.SpotSpuUpdateDto;
import tech.tiangong.sdp.design.vo.query.spot.SpotSpuQuery;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SpuRefreshQuery;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuExportVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuVo;
import tech.tiangong.sdp.utils.Bool;

import java.time.LocalDateTime;
import java.util.*;

/**
 * (SpotSpu)服务仓库类
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:46
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class SpotSpuRepository extends BaseRepository<SpotSpuMapper, SpotSpu> {
    private final SpotSpuDetailRepository spotSpuDetailRepository;

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<SpotSpuVo> findPage(SpotSpuQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public List<SpotSpuVo> selectByStyleCodes(List<String> styleCodes) {
        return baseMapper.selectByStyleCodes(styleCodes);
    }

    public SpotSpu getByStyleCode(String styleCode) {
        if (StringUtils.isBlank(styleCode)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<SpotSpu>()
                .eq(SpotSpu::getStyleCode, styleCode), false);
    }

    public List<SpotSpu> listByStyleCodes(Collection<String> styleCodes) {
        if (CollectionUtils.isEmpty(styleCodes)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SpotSpu>()
                .in(SpotSpu::getStyleCode, styleCodes));
    }

    public void updateSpuInfo(SpotSpuUpdateDto updateDto, UserContent userContent) {
        SdpDesignException.notNull(updateDto, "入参为null");
        SdpDesignException.notNull(updateDto.getSpotSpuId(), "spuId为空! ");
        SdpDesignException.notNull(updateDto.getVersionNum(), "versionNum为空! ");
        lambdaUpdate()
                .set(Objects.nonNull(updateDto.getStyleStatus()), SpotSpu::getStyleStatus, updateDto.getStyleStatus())
                .set(SpotSpu::getVersionNum, updateDto.getVersionNum())
                .set(SpotSpu::getSubmitTime, updateDto.getSpuUpdateTime())
                .set(Objects.nonNull(updateDto.getProductPictureStatus()), SpotSpu::getProductPictureStatus, updateDto.getProductPictureStatus())
                .set(Objects.nonNull(updateDto.getTryOnStatus()), SpotSpu::getTryOnStatus, updateDto.getTryOnStatus())
                .set(updateDto.isUpdateTryOnAuditTimeFlag(), SpotSpu::getTryOnAuditTime, updateDto.getTryOnAuditTime())
                .set(Objects.nonNull(updateDto.getProductPictureUploadTime()), SpotSpu::getProductPictureUploadTime, updateDto.getProductPictureUploadTime())
                .set(Objects.nonNull(updateDto.getResourceStatus()), SpotSpu::getResourceStatus, updateDto.getResourceStatus())
                .set(Objects.nonNull(updateDto.getDeveloperId()), SpotSpu::getDeveloperId, updateDto.getDeveloperId())
                .set(Objects.nonNull(updateDto.getDeveloperName()), SpotSpu::getDeveloperName, updateDto.getDeveloperName())
                .set(Objects.nonNull(updateDto.getStoreId()), SpotSpu::getStoreId, updateDto.getStoreId())
                .set(StringUtils.isNotBlank(updateDto.getStoreName()), SpotSpu::getStoreName, updateDto.getStoreName())
                .set(StringUtils.isNotBlank(updateDto.getCategory()), SpotSpu::getCategory, updateDto.getCategory())
                .set(StringUtils.isNotBlank(updateDto.getCategoryName()), SpotSpu::getCategoryName, updateDto.getCategoryName())
                .set(StringUtils.isNotBlank(updateDto.getSpotTypeCode()), SpotSpu::getSpotTypeCode, updateDto.getSpotTypeCode())
                .set(StringUtils.isNotBlank(updateDto.getSpotTypeName()), SpotSpu::getSpotTypeName, updateDto.getSpotTypeName())
                .set(StringUtils.isNotBlank(updateDto.getPalletTypeCode()), SpotSpu::getPalletTypeCode, updateDto.getPalletTypeCode())
                .set(StringUtils.isNotBlank(updateDto.getPalletTypeName()), SpotSpu::getPalletTypeName, updateDto.getPalletTypeName())
                .set(StringUtils.isNotBlank(updateDto.getWeaveModeCode()), SpotSpu::getWeaveModeCode, updateDto.getWeaveModeCode())
                .set(StringUtils.isNotBlank(updateDto.getWeaveMode()), SpotSpu::getWeaveMode, updateDto.getWeaveMode())
                .set(StringUtils.isNotBlank(updateDto.getSizeStandard()), SpotSpu::getSizeStandard, updateDto.getSizeStandard())
                .set(StringUtils.isNotBlank(updateDto.getSizeStandardCode()), SpotSpu::getSizeStandardCode, updateDto.getSizeStandardCode())
                .set(StringUtils.isNotBlank(updateDto.getProductType()), SpotSpu::getProductType, updateDto.getProductType())
                .set(StringUtils.isNotBlank(updateDto.getProductTypeCode()), SpotSpu::getProductTypeCode, updateDto.getProductTypeCode())
                .set(StringUtils.isNotBlank(updateDto.getQualityLevel()), SpotSpu::getQualityLevel, updateDto.getQualityLevel())
                .set(StringUtils.isNotBlank(updateDto.getQualityLevelCode()), SpotSpu::getQualityLevelCode, updateDto.getQualityLevelCode())
                .set(StringUtils.isNotBlank(updateDto.getElasticCode()),SpotSpu::getElasticCode,updateDto.getElasticCode())
                .set(StringUtils.isNotBlank(updateDto.getElasticName()),SpotSpu::getElasticName,updateDto.getElasticName())
                .set(StringUtils.isNotBlank(updateDto.getElementCode()),SpotSpu::getElementCode,updateDto.getElementCode())
                .set(StringUtils.isNotBlank(updateDto.getElementName()),SpotSpu::getElementName,updateDto.getElementName())
                .set(StringUtils.isNotBlank(updateDto.getSpuName()),SpotSpu::getSpuName,updateDto.getSpuName())
                .set(StringUtils.isNotBlank(updateDto.getSpuNameTrans()),SpotSpu::getSpuNameTrans,updateDto.getSpuNameTrans())
                .set(SpotSpu::getSceneCode, updateDto.getSceneCode())
                .set(SpotSpu::getSceneName, updateDto.getSceneName())
                .set(SpotSpu::getProductThemeCode, updateDto.getProductThemeCode())
                .set(SpotSpu::getProductThemeName, updateDto.getProductThemeName())
                .set(SpotSpu::getInspirationBrandCode, updateDto.getInspirationBrandCode())
                .set(SpotSpu::getInspirationBrandName, updateDto.getInspirationBrandName())
                .set(SpotSpu::getClothingStyleCode, updateDto.getClothingStyleCode())
                .set(SpotSpu::getClothingStyleName, updateDto.getClothingStyleName())
                .set(SpotSpu::getPlanningType, updateDto.getPlanningType())
                .set(SpotSpu::getMarketCode, updateDto.getMarketCode())
                .set(SpotSpu::getMarketSeriesCode, updateDto.getMarketSeriesCode())
                .set(SpotSpu::getWaveBandCode, updateDto.getWaveBandCode())
                .set(SpotSpu::getWaveBandName, updateDto.getWaveBandName())
                .set(SpotSpu::getPlanningSourceCode, updateDto.getPlanningSourceCode())
                .set(SpotSpu::getPlanningSourceName, updateDto.getPlanningSourceName())
                .set(SpotSpu::getInspirationImageSourceCode, updateDto.getInspirationImageSourceCode())
                .set(SpotSpu::getInspirationImageSourceName, updateDto.getInspirationImageSourceName())
                .set(SpotSpu::getRevisedTime, Objects.isNull(updateDto.getSpuUpdateTime()) ? LocalDateTime.now() : updateDto.getSpuUpdateTime())
                .set(SpotSpu::getReviserId, userContent.getCurrentUserId())
                .set(SpotSpu::getReviserName, userContent.getCurrentUserName())
                .set(SpotSpu::getBuyerId, updateDto.getBuyerId())
                .set(SpotSpu::getBuyerName, updateDto.getBuyerName())
                .set(SpotSpu::getCountrySiteCode, updateDto.getCountrySiteCode())
                .set(SpotSpu::getCountrySiteName, updateDto.getCountrySiteName())
                .eq(SpotSpu::getSpotSpuId, updateDto.getSpotSpuId())
                .update();
    }

    public List<SpotSpuExportVo> findList(SpotSpuQuery query) {
        return baseMapper.findList(query);
    }

    public List<SpotSpu> getAllocationListByStyleCodes(List<String> styleCodes) {
        return list(new LambdaQueryWrapper<SpotSpu>()
                .in(SpotSpu::getStyleCode, styleCodes)
                .eq(SpotSpu::getIsDeleted, Bool.NO.getCode()));
    }

    public void updateTryOnStatusByStyleCode(List<String> styleCodes, Integer status) {
        lambdaUpdate()
                .set(SpotSpu::getTryOnStatus, status)
                .in(SpotSpu::getStyleCode, styleCodes)
                .eq(SpotSpu::getIsDeleted, Bool.NO.getCode())
                .update();
    }

    public String selectLatestStyleCode() {
        SpotSpu spotSpu = lambdaQuery()
                .select(SpotSpu::getStyleCode)
                .orderByDesc(SpotSpu::getCreatedTime)
                .last("limit 1").one();
        return Optional.ofNullable(spotSpu).map(SpotSpu::getStyleCode).orElse("");
    }

    public SpotSpuVo selectBySpotTypeNameAndProductId(String spotTypeName, Long productId) {
        return baseMapper.selectBySpotTypeNameAndProductId(spotTypeName, productId);
    }

    public List<SpotSpu> selectAll(SpuRefreshQuery query) {
        return lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getStyleCode()),SpotSpu::getStyleCode, query.getStyleCode())
                .eq(SpotSpu::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(SpotSpu::getCreatedTime)
                .list();
    }

    public List<String> listAllSpu() {
        return baseMapper.listAllSpu();
    }

    public List<SpuSkcNumDto> querySpuSkcNum(List<String> qySpuCodeList) {
        return baseMapper.querySpuSkcNum(qySpuCodeList);
    }

    public List<SpotSpu> selectAlibabaDistributionTask(Long id, Integer size){
        return baseMapper.selectAlibabaDistributionTask(id, size);
    }

    /**
     * 更新POP推送状态
     * 状态值：0-未推送，1-已推送，3-推送失败，4-取消
     * 使用异步线程池执行确保记录的原因不被回滚
     * @param spuId SPU主键ID
     * @param status 推送状态（使用SpotPopPushStatusEnum）
     * @param failReason 失败原因（失败时记录）
     */
    @Async(value = "asyncTaskExecutor")
    public void updatePushStatusBySpuId(Long spuId, Integer status, String failReason) {
        if (spuId == null) {
            log.warn("更新推送状态失败：spuId 为空");
            return;
        }

        SpotSpu updateEntity = new SpotSpu();
        updateEntity.setSpotSpuId(spuId);
        updateEntity.setPushedToPop(status);

        updateById(updateEntity);
        
        // 更新SpotSpuDetail表的错误日志信息
        String finalFailReason = null;
        if (SpotPopPushStatusEnum.PUSHED_FAILED.getCode().equals(status) || 
            SpotPopPushStatusEnum.CANCELLED.getCode().equals(status)) {
            // 推送失败或取消时记录失败原因
            finalFailReason = failReason;
        } else if (SpotPopPushStatusEnum.PUSHED_SUCCESS.getCode().equals(status)) {
            // 成功则清空失败原因
            finalFailReason = "";
        }
        spotSpuDetailRepository.updateSpotSpuDetailFailReason(spuId, finalFailReason);
        
        log.debug("SPU {} 推送状态已更新为：{} ({})", spuId, status, 
                SpotPopPushStatusEnum.getDescByCode(status));
    }

    /**
     * 根据StyleCode更新POP推送状态
     *
     * @param styleCode SPU编码
     * @param status 推送状态
     * @param failReason 失败原因
     */
    @Async(value = "asyncTaskExecutor")
    public void updatePushStatusByStyleCode(String styleCode, Integer status, String failReason) {
        if (StringUtils.isBlank(styleCode)) {
            log.warn("更新推送状态失败：styleCode 为空");
            return;
        }

        SpotSpu spotSpu = getByStyleCode(styleCode);
        if (spotSpu != null) {
            updatePushStatusBySpuId(spotSpu.getSpotSpuId(), status, failReason);
        } else {
            log.warn("更新推送状态失败：未找到styleCode={}", styleCode);
        }
    }

}
