package tech.tiangong.sdp.design.converter;


import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import tech.tiangong.inspiration.common.req.product.StyleLibraryDeleteReq;

/**
 * 向量管理转换器
 *
 * <AUTHOR>
 * @date 2025/2/27 12:59
 */
public class StyleLibraryConverter {


    public static StyleLibraryDeleteReq deleteReq(String sourceType, Long busId) {
        UserContent userContent = UserContentHolder.get();
        StyleLibraryDeleteReq deleteReq =  new StyleLibraryDeleteReq();
        deleteReq.setSourceType(sourceType);
        deleteReq.setBusId(busId);
        deleteReq.setTenantId(userContent.getTenantId());
        deleteReq.setCreatorId(userContent.getCurrentUserId());
        deleteReq.setCreatorName(userContent.getCurrentUserName());
        return  deleteReq;
    }
}
