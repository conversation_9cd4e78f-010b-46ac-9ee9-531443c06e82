package tech.tiangong.sdp.design.controller.open;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.OpenDataHandleService;
import tech.tiangong.sdp.design.vo.req.open.OpenNotifyReq;

import java.util.Objects;

/**
 * mq 接口，承接zj的mq的接口
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping( "/openapi/v1" + "/zj")
public class ZjMqOpenController extends BaseController {

    private final OpenDataHandleService openDataHandleService;

    /**
     * 消息通知
     *
     * @param req 入参
     * @return Void
     */
    @PostMapping("/notify")
    DataResponse<Void> notify(@RequestBody OpenNotifyReq req) {
        log.info("===zj消息通知 req: {} ===", JSON.toJSONString(req));
        UserContent userContent = UserContentHolder.get();
        //zj默认用户: 101010102	ZJ默认用户
        if (Objects.isNull(userContent)) {
            userContent = new UserContent();
            userContent.setCurrentUserId(101010102L);
            userContent.setCurrentUserCode("101010102");
            userContent.setCurrentUserName("ZJ默认用户");
            userContent.setTenantId(2L);
            userContent.setSystemCode("SDP");
            UserContentHolder.set(userContent);
        }
        log.info("notify userContent: {}", JSON.toJSONString(userContent));

        try {
            openDataHandleService.zjNotify(req);
            return DataResponse.ok();
        } finally {
            UserContentHolder.clean();
        }
    }


}
