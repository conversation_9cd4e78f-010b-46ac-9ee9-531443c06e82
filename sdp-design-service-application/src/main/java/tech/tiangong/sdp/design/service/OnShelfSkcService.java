package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.req.demand.OnShelfSkcReq;
import tech.tiangong.sdp.design.vo.resp.prototype.OnShelfSkcVo;

/**
 * 上架skc信息表服务接口
 *
 * <AUTHOR>
 */
public interface OnShelfSkcService {

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    OnShelfSkcVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(OnShelfSkcReq req);

    /**
     * 更新数据
     *
     * @param req 数据实体
     */
    void update(OnShelfSkcReq req);


}
