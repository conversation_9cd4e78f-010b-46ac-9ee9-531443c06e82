package tech.tiangong.sdp.design.remote;

import cn.yibuyun.framework.net.DataResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.clothes.client.InspSampleAuditClient;
import tech.tiangong.sdp.clothes.vo.req.audit.SampleAuditPictureReq;
import tech.tiangong.sdp.clothes.vo.resp.audit.SampleAuditPictureVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class InspSampleAuditRemoteHelper {
    private final InspSampleAuditClient inspSampleAuditClient;

    public List<SampleAuditPictureVo> pictureInfoList(SampleAuditPictureReq req) {
        DataResponse<List<SampleAuditPictureVo>> response = inspSampleAuditClient.pictureInfoList(req);
        SdpDesignException.isTrue(response.isSuccessful(), "调用批量查询联合审版单样衣图信息接口失败.错误信息:{}", response.getMessage());
        return response.getData();
    }
}
