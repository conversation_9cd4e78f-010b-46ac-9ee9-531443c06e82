package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.PrototypeHistory;
import tech.tiangong.sdp.design.enums.PrototypeStatusEnum;
import tech.tiangong.sdp.design.mapper.PrototypeHistoryMapper;
import tech.tiangong.sdp.design.vo.dto.style.DesignStyleInsertDto;
import tech.tiangong.sdp.design.vo.dto.style.DesignStyleOldDto;
import tech.tiangong.sdp.design.vo.dto.style.PrototypeUpdateSpuIdDto;
import tech.tiangong.sdp.design.vo.dto.style.Spu2SkcUpdateDto;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 版单历史表服务仓库类
 *
 * <AUTHOR>
 * @since 2021-08-09 14:50:36
 */
@Repository
public class PrototypeHistoryRepository extends BaseRepository<PrototypeHistoryMapper, PrototypeHistory> {

    public PrototypeHistory getByPrototypeId(Long prototypeId){
        return getOne(Wrappers.<PrototypeHistory>lambdaQuery().eq(PrototypeHistory::getPrototypeId,prototypeId));
    }

    public List<PrototypeHistory> getByPrototypeIds(List<Long> prototypeIds) {
        return list(Wrappers.<PrototypeHistory>lambdaQuery().in(PrototypeHistory::getPrototypeId, prototypeIds).eq(PrototypeHistory::getIsDeleted, cn.yibuyun.framework.enumeration.Bool.NO.getCode()));
    }


    /**
     * 取消版单历史
     * @param designCode
     * @param prototypeHistory
     * @return
     */
    public boolean cancelByDesignCode(String designCode,PrototypeHistory prototypeHistory){
        return update(Wrappers.<PrototypeHistory>lambdaUpdate().eq(PrototypeHistory::getDesignCode,designCode).setEntity(prototypeHistory));
    }


    /**
     * 根据设计款编码更新
     * @param designCode
     * @param prototypeHistory
     */
    public void updateByDesignCode(String designCode, PrototypeHistory prototypeHistory) {
        if (StringUtils.isBlank(designCode) || Objects.isNull(prototypeHistory)) {
            return;
        }
        update(prototypeHistory,Wrappers.<PrototypeHistory>lambdaUpdate().eq(PrototypeHistory::getDesignCode,designCode));
    }


    public List<PrototypeHistory> getListByStyleCode(String styleCode) {
        return baseMapper.selectList(new QueryWrapper<PrototypeHistory>().lambda().eq(PrototypeHistory::getStyleCode, styleCode)
                .eq(PrototypeHistory::getIsDeleted, cn.yibuyun.framework.enumeration.Bool.NO.getCode()));
    }


    /**
     * 分组查询所有客户联系人手机号
     * @return
     */
    public List<String> getPurchaserPhone(String purchaserCode) {
        return java.util.Collections.emptyList();
    }

    /**
     * 根据skc编码查询所有已拆版并已完成的版单信息
     */
    public List<PrototypeHistory> listDoneVersionByDesignCode(String designCode) {
        if (StringUtils.isBlank(designCode)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(PrototypeHistory::getDesignCode, designCode)
                .eq(PrototypeHistory::getIsDoneVersion, Bool.YES.getCode())
                .eq(PrototypeHistory::getPrototypeStatus, PrototypeStatusEnum.DECOMPOSED.getCode())
                .orderByDesc(PrototypeHistory::getVersionNum)
                .list();
    }

    /**
     * 根据skcList编码查询所有的版单信息
     */
    public List<PrototypeHistory> listQueryByDesignCode(List<String> designCodeList) {
        if (CollectionUtil.isEmpty(designCodeList)) {
            return List.of();
        }
        return lambdaQuery()
                .in(PrototypeHistory::getDesignCode, designCodeList)
                .orderByDesc(PrototypeHistory::getVersionNum)
                .list();
    }

    /**
     * 根据版单id查询已拆版详情
     */
    public PrototypeHistory getDoneDetailById(Long prototypeId) {
        if (Objects.isNull(prototypeId)) {
            return null;
        }
        return lambdaQuery()
                .eq(PrototypeHistory::getPrototypeId, prototypeId)
                .eq(PrototypeHistory::getIsDoneVersion, Bool.YES.getCode())
                .one();
    }

    /**
     * 根据skc编码查询最新的版单
     */
    public PrototypeHistory getLatestPrototypeByDesignCode(String designCode) {
        if (StringUtils.isBlank(designCode)) {
            return null;
        }
        return lambdaQuery()
                .eq(PrototypeHistory::getDesignCode, designCode)
                .orderByDesc(PrototypeHistory::getVersionNum)
                .last("limit 1").one();
    }

    /**
     * 根据skc编码查询最新的版单
     */
    public PrototypeHistory selectFirstByDesignCode(String designCode) {
        if (StringUtils.isBlank(designCode)) {
            return null;
        }
        return lambdaQuery()
                .eq(PrototypeHistory::getDesignCode, designCode)
                .orderByAsc(PrototypeHistory::getCreatedTime)
                .last("limit 1").one();
    }

    /**
     * 根据skc编码查询最新已提交的版单
     */
    public PrototypeHistory getLatestSubmitPrototypeByDesignCode(String designCode) {
        if (StringUtils.isBlank(designCode)) {
            return null;
        }
        return lambdaQuery()
                .eq(PrototypeHistory::getDesignCode, designCode)
                .eq(PrototypeHistory::getPrototypeStatus, PrototypeStatusEnum.DECOMPOSED.getCode())
                .orderByDesc(PrototypeHistory::getVersionNum)
                .last("limit 1").one();
    }

    /**
     * 查询各SPU维度下最后提交/创建的skc的prototypeId集合-推款前的历史数据
     */
    public List<DesignStyleOldDto> getLatestPrototypeList() {
        return baseMapper.getLatestPrototypeList();
    }

    /**
     * 查询各SPU维度下最后提交/创建的skc的prototypeId集合
     */
    public List<DesignStyleOldDto> listLatestPrototypeList() {
        return baseMapper.listLatestPrototypeList();
    }

    /**
     * 从拆板单中查询SPU信息
     */
    public List<DesignStyleInsertDto> querySpuInfo(List<Long> prototypeIdList) {
        if (CollUtil.isEmpty(prototypeIdList)) {
            return List.of();
        }
        return baseMapper.querySpuInfo(prototypeIdList);
    }

    public void batchUpdateSpuId(List<PrototypeUpdateSpuIdDto> prototypeUpdateSpuIdList) {
        if (CollUtil.isEmpty(prototypeUpdateSpuIdList)) {
            return;
        }
        baseMapper.batchUpdateSpuId(prototypeUpdateSpuIdList);
    }

    public List<Spu2SkcUpdateDto> prototypeIdsByStyleCode(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return List.of();
        }
        return baseMapper.qualityInfoByStyleCode(styleCodeList);
    }

    public void batchUpdateCategoryInfo(List<Spu2SkcUpdateDto> updateList) {
        if (CollUtil.isEmpty(updateList)) {
            return;
        }
        baseMapper.batchUpdateCategoryInfo(updateList);
    }

    public PrototypeHistory geByDesignCodeAndVersionNum(String designCode, Integer versionNum) {
        if (StringUtils.isBlank(designCode) || Objects.isNull(versionNum)) {
            return null;
        }
        return lambdaQuery()
                .eq(PrototypeHistory::getDesignCode, designCode)
                .eq(PrototypeHistory::getVersionNum, versionNum)
                .last("limit 1").one();
    }


    /**
     * 根据designCode更新对应prototypeHistory表的styleVersionId
     */
    public void updateStyleVersionId(String designCode, Long designStyleVersionId) {
        if (StringUtils.isBlank(designCode) || Objects.isNull(designStyleVersionId)) {
            return;
        }
        lambdaUpdate()
                .eq(PrototypeHistory::getDesignCode, designCode)
                .isNull(PrototypeHistory::getDesignStyleVersionId)
                .set(PrototypeHistory::getDesignStyleVersionId, designStyleVersionId)
                .set(PrototypeHistory::getRevisedTime, LocalDateTime.now())
                .update();

    }

    public List<PrototypeHistory> listByStyleCodes(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(PrototypeHistory::getStyleCode)
                .list();
    }
}