package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.dto.product.PopProductImageChangeStateDto;
import tech.tiangong.sdp.design.vo.req.demand.OnShelfSpuReq;
import tech.tiangong.sdp.design.vo.req.mq.ProductImage2ZjMqDto;
import tech.tiangong.sdp.design.vo.resp.prototype.OnShelfSpuVo;

/**
 * 上架spu信息表服务接口
 *
 * <AUTHOR>
 */
public interface OnShelfSpuService {

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    OnShelfSpuVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(OnShelfSpuReq req);

    /**
     * 更新商品图
     *
     * @param dto 数据实体
     * */
    void productImageUpdate(PopProductImageChangeStateDto dto);

    /**
     * 更新商品图推送致景
     * @param mqDto 消息体
     */
    void productImageUpdate2Zj(ProductImage2ZjMqDto mqDto);
}
