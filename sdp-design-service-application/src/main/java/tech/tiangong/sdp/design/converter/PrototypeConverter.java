package tech.tiangong.sdp.design.converter;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.Beans;
import org.springframework.beans.BeanUtils;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.entity.PrototypeDetail;
import tech.tiangong.sdp.design.entity.PrototypeHistory;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandSaveReq;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandUpdateReq;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeCraftReq;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeOperateReq;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeReq;
import tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo;
import tech.tiangong.sdp.material.pojo.dto.DesignerDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public final class PrototypeConverter {


    /**
     * 编辑或创建设计单，转换到PrototypeDetail
     *
     */
    public static PrototypeDetail convertPrototypeDetail(PrototypeDetail prototypeDetail, PrototypeOperateReq req) {
        BeanUtils.copyProperties(req, prototypeDetail);
        // prototypeDetail.setCustomerPicture(StrUtil.join(StrUtil.COMMA,req.getCustomerPicture()));
        prototypeDetail.setDesignPicture(StrUtil.join(StrUtil.COMMA,req.getDesignPicture()));
        List<PrototypeOperateReq.ColorInfoReq> colorInfoReqList = req.getColorInfoList();
        if (CollUtil.isNotEmpty(colorInfoReqList)) {
            List<ColorInfoVo> colorInfoVoList = colorInfoReqList.stream().map(item -> {
                ColorInfoVo colorInfoVo = new ColorInfoVo();
                BeanUtils.copyProperties(item, colorInfoVo);
                return colorInfoVo;
            }).collect(Collectors.toList());
            prototypeDetail.setColorInfoList(colorInfoVoList);
        }

        // prototypeDetail.setTags(JSONUtil.toJsonStr(req.getTags()));
        // prototypeDetail.setCustomerSize(JSONUtil.toJsonStr(req.getCustomerSize()));
//        prototypeDetail.setStylePartRequires(JSONUtil.toJsonStr(req.getRequires()));
        return prototypeDetail;
    }

    @Deprecated
    public static PrototypeDetail convertPrototypeDetail(PrototypeDetail prototypeDetail, PrototypeReq req) {
        BeanUtils.copyProperties(req, prototypeDetail);
        // prototypeDetail.setCustomerPicture(StrUtil.join(StrUtil.COMMA,req.getCustomerPicture()));
        prototypeDetail.setDesignPicture(StrUtil.join(StrUtil.COMMA,req.getDesignPicture()));

        // prototypeDetail.setTags(JSONUtil.toJsonStr(req.getTags()));
        // prototypeDetail.setCustomerSize(JSONUtil.toJsonStr(req.getCustomerSize()));
//        prototypeDetail.setStylePartRequires(JSONUtil.toJsonStr(req.getRequires()));
        return prototypeDetail;
    }

    /**
     * 组装req到PrototypeHistory
     * @param prototypeReq
     * @param prototypeHistoryNew
     */
    public static void composePrototypeReqToPrototypeHistory(PrototypeOperateReq prototypeReq, PrototypeHistory prototypeHistoryNew, LocalDateTime skcCreatedTime){
        prototypeHistoryNew.setMakeSameDesignCode(prototypeReq.getMakeSameDesignCode());
        prototypeHistoryNew.setColor(prototypeReq.getColor());
        // prototypeHistoryNew.setColorCode(prototypeReq.getColorCode());
    }

    @Deprecated
    public static void composePrototypeReqToPrototypeHistory(PrototypeReq prototypeReq, PrototypeHistory prototypeHistoryNew, LocalDateTime skcCreatedTime){
        prototypeHistoryNew.setCategory(prototypeReq.getCategory());
        prototypeHistoryNew.setCategoryName(prototypeReq.getCategoryName());
        prototypeHistoryNew.setMakeSameDesignCode(prototypeReq.getMakeSameDesignCode());
        prototypeHistoryNew.setColor(prototypeReq.getColor());
        // prototypeHistoryNew.setPurchaserSource(prototypeReq.getPurchaserSource());
        // prototypeHistoryNew.setPlanDeliveryTime(prototypeReq.getPlanDeliveryTime());
        // prototypeHistoryNew.setDeliveryTypeCode(prototypeReq.getDeliveryTypeCode());
        // prototypeHistoryNew.setDeliveryTypeName(prototypeReq.getDeliveryTypeName());
        // prototypeHistoryNew.setDeliveryPeriod(prototypeReq.getDeliveryPeriod());
        // prototypeHistoryNew.setDeliveryTime(DeliveryTimeUtil.calDeliveryTime(skcCreatedTime,prototypeReq.getDeliveryPeriod()));
        // prototypeHistoryNew.setIsDeliveryPurchaserSample(prototypeReq.getIsDeliveryPurchaserSample());

    }


    /**
     * 组装req到Prototype
     * @param prototypeReq
     * @param prototype
     */
    public static void composePrototypeReqToPrototype(PrototypeOperateReq prototypeReq, Prototype prototype, LocalDateTime skcCreatedTime){
        prototype.setMakeSameDesignCode(prototypeReq.getMakeSameDesignCode());
        prototype.setColor(prototypeReq.getColor());
        // prototype.setColorEnglishName(prototypeReq.getColorEnglishName());
        // prototype.setColorCode(prototypeReq.getColorCode());
        // prototype.setColorAbbrCode(prototypeReq.getColorAbbrCode());
        // prototype.setColorNumber(prototypeReq.getColorNumber());
    }

    @Deprecated
    public static void composePrototypeReqToPrototype(PrototypeReq prototypeReq, Prototype prototype, LocalDateTime skcCreatedTime){
        prototype.setCategory(prototypeReq.getCategory());
        prototype.setCategoryName(prototypeReq.getCategoryName());
        prototype.setMakeSameDesignCode(prototypeReq.getMakeSameDesignCode());
        prototype.setColor(prototypeReq.getColor());
        // prototype.setPurchaserSource(prototypeReq.getPurchaserSource());
        // prototype.setPlanDeliveryTime(prototypeReq.getPlanDeliveryTime());
        // prototype.setDeliveryTypeCode(prototypeReq.getDeliveryTypeCode());
        // prototype.setDeliveryTypeName(prototypeReq.getDeliveryTypeName());
        // prototype.setDeliveryPeriod(prototypeReq.getDeliveryPeriod());
        // prototype.setDeliveryTime(DeliveryTimeUtil.calDeliveryTime(skcCreatedTime,prototypeReq.getDeliveryPeriod()));
        // prototype.setIsDeliveryPurchaserSample(prototypeReq.getIsDeliveryPurchaserSample());

    }

    /**
     * 拆版单二次工艺信息更新req
     */
    public static CraftDemandUpdateReq buildCraftUpdateReq(Long latestPrototypeIdFlag,
                                                           Long donePrototypeId,
                                                           List<PrototypeCraftReq.SecondCraftAddReq> addCraftDemandList,
                                                           List<Long> delCraftDemandIdList) {
        CraftDemandUpdateReq updateReq = new CraftDemandUpdateReq();
        updateReq.setNewPrototypeId(latestPrototypeIdFlag);
        updateReq.setOldPrototypeId(donePrototypeId);
        updateReq.setDelCraftDemandIds(Set.copyOf(delCraftDemandIdList));

        List<CraftDemandSaveReq> demandSaveReqList = addCraftDemandList.stream()
                .map(item -> Beans.copyValue(item, new CraftDemandSaveReq()))
                .collect(Collectors.toList());
        updateReq.setCraftSaveReqs(demandSaveReqList);
        return updateReq;
    }


    public static Prototype buildColorsPrototype(Prototype prototype, Long designerId, DesignerDTO designerDTO, long colorPrototypeId, String colorDesignCode) {
        Prototype colorPrototype = new Prototype();
        BeanUtils.copyProperties(prototype, colorPrototype);
        colorPrototype.setPrototypeId(colorPrototypeId);
        colorPrototype.setDesignCode(colorDesignCode);
        //是否动销置为null
        colorPrototype.setIsOnSale(null);
        //参考款号置为null, 正常款才有
        colorPrototype.setReferenceDesignCode(null);
        //销售渠道置为null 每个skc不一样
        colorPrototype.setSalesChannel(null);

        //设计师为复色发起人
        colorPrototype.setDesignerId(designerId);
        colorPrototype.setDesignerCode(designerDTO.getDesignerCode());
        colorPrototype.setDesignerName(designerDTO.getDesignerName());
        colorPrototype.setDesignerGroup(designerDTO.getDesignerGroupName());
        colorPrototype.setDesignerGroupCode(designerDTO.getDesignerGroupCode());
        colorPrototype.setCreatorId(null);
        colorPrototype.setCreatorName(null);
        colorPrototype.setCreatedTime(null);
        colorPrototype.setReviserId(null);
        colorPrototype.setReviserName(null);
        colorPrototype.setRevisedTime(null);
        return colorPrototype;
    }
}
