package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.enums.MaterialDemandTypeEnum;
import tech.tiangong.sdp.design.service.PurchaseApplyFollowService;
import tech.tiangong.sdp.design.vo.req.purchase.CancelPurchaseApplyReq;
import tech.tiangong.sdp.design.vo.req.purchase.PurchaseApplyFollowPageReq;
import tech.tiangong.sdp.design.vo.req.purchase.PurchaseApplyMaterialConfirmBatchReq;
import tech.tiangong.sdp.design.vo.resp.purchase.OrderPurchaseSimpleLogVO;
import tech.tiangong.sdp.design.vo.resp.purchase.PurchaseApplyFollowPageVO;

import javax.validation.Valid;
import java.util.List;

/**
*
* 采购申请管理-web
* <br>CreateDate August 09,2021
* <AUTHOR>
* @since 1.0
*/
@RestController
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/purchase/apply")
public class PurchaseApplyFollowController extends BaseController {

    @Autowired
    private PurchaseApplyFollowService applyFollowService;

    /**
     * 取消采购申请
     *
     * 1.选中数据后，点击【取消采购申请】按钮，系统弹出【取消采购申请】弹框，
     * 在弹框中维护取消原因，然后点击【弹框-确定】按钮，
     * 选中的数据状态由【有效】变为【取消】；
     * 同时会会给供应链履约传数据，包括取消人，
     * 取消时间，取消原因。
     *
     * @param req 取消采购申请请求参数
     * @return void
     */
    @PostMapping("/cancel-purchase")
    @NoRepeatSubmitLock
    public DataResponse<Void> cancelPurchaseApply(@RequestBody @Validated CancelPurchaseApplyReq req){
        applyFollowService.cancelPurchaseApply(req);
        return DataResponse.ok();
    }


    /**
     * 采购申请管理列表
     *
     * @param req 剪版需求跟进表（采购申请跟进）请求参数
     * @return {@link PageRespVo<PurchaseApplyFollowPageVO>}
     */
    @PostMapping("/page-list")
    public DataResponse<PageRespVo<PurchaseApplyFollowPageVO>> pageList(@RequestBody PurchaseApplyFollowPageReq req){
        return DataResponse.ok(applyFollowService.pageList(req));
    }


    /**
     * 采购记录
     *
     * @param demandType 需求类型: 1, 面料; 2, 辅料
     * @param orderCode 剪版单号
     * @return {@link List<OrderPurchaseSimpleLogVO>}
     */
    @GetMapping("/purchase-order/log/{demandType}/{orderCode}")
    public DataResponse<List<OrderPurchaseSimpleLogVO>> purchaseOrderLog(@PathVariable(name="demandType") Integer demandType,
                                                                         @PathVariable(name="orderCode") String orderCode){
        return DataResponse.ok(applyFollowService.purchaseOrderLog(MaterialDemandTypeEnum.findByCode(demandType),orderCode));
    }

    /**
     * 批量物料采购-开发bom
     *
     * @param req 采购的物料参数
     * @return 返回值
     */
    @PostMapping("/material-purchase-batch")
    @NoRepeatSubmitLock
    public DataResponse<Void> batchPurchaseApplyMaterial(@RequestBody @Valid PurchaseApplyMaterialConfirmBatchReq req){
        applyFollowService.purchaseApplyMaterialConfirmBatch(req);
        return DataResponse.ok();
    }


}
