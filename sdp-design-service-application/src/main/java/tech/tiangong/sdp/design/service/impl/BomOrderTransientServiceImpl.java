package tech.tiangong.sdp.design.service.impl;

import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.entity.BomOrderTransient;
import tech.tiangong.sdp.design.repository.BomOrderTransientRepository;
import tech.tiangong.sdp.design.service.BomOrderTransientService;
import tech.tiangong.sdp.design.vo.query.bom.BomOrderTransientQuery;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderTransientReq;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderTransientVo;

import java.util.Objects;

/**
 * bom暂存表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BomOrderTransientServiceImpl implements BomOrderTransientService {
    private final BomOrderTransientRepository bomOrderTransientRepository;

    @Override
    public PageRespVo<BomOrderTransientVo> page(BomOrderTransientQuery query) {
        IPage<BomOrderTransientVo> page = bomOrderTransientRepository.findPage(query);
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    public BomOrderTransientVo getById(Long id) {
        BomOrderTransient entity = bomOrderTransientRepository.getById(id);
        if (Objects.isNull(entity)) {
            return null;
        }
        BomOrderTransientVo vo = new BomOrderTransientVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(BomOrderTransientReq req) {
        BomOrderTransient entity = new BomOrderTransient();
        BeanUtils.copyProperties(req, entity);
        bomOrderTransientRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BomOrderTransientReq req) {
        BomOrderTransient entity = new BomOrderTransient();
        BeanUtils.copyProperties(req, entity);
        bomOrderTransientRepository.updateById(entity);
    }

    @Override
    public void remove(Long id) {
        bomOrderTransientRepository.removeById(id);
    }

}
