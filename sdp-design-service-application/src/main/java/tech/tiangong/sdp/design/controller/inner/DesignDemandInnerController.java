package tech.tiangong.sdp.design.controller.inner;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.DesignDemandService;
import tech.tiangong.sdp.design.vo.req.demand.DesignDemandCreateReq;
import tech.tiangong.sdp.design.vo.resp.demand.DesignDemandCreateVo;


/**
 * 灵感设计需求-inner
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/design-demand")
public class DesignDemandInnerController extends BaseController {

    private final DesignDemandService designDemandService;


    /**
     * 创建灵感任务需求单
     *
     * @param req 入参
     * @return response
     */
    @PostMapping("/add")
    @NoRepeatSubmitLock(lockTime = 6L)
    DataResponse<DesignDemandCreateVo> add(@RequestBody @Validated DesignDemandCreateReq req) {
        return DataResponse.ok(designDemandService.add(req));
    }


}