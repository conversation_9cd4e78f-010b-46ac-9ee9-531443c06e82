package tech.tiangong.sdp.design.controller.inner;


import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.MaterialSnapshotDataHandleService;
import tech.tiangong.sdp.design.service.MaterialSnapshotService;
import tech.tiangong.sdp.design.vo.req.material.SnapshotUnitUpdateReq;
import tech.tiangong.sdp.design.vo.resp.material.MaterialSnapshotVo;

import java.util.List;


/**
 * 物料快照-内部接口
 *
 * <AUTHOR>
 * @since 2021-09-13 14:43:19
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/material-snapshot")
public class MaterialSnapshotInnerController extends BaseController {
    private final MaterialSnapshotService materialSnapshotService;
    private final MaterialSnapshotDataHandleService snapshotDataHandleService;

    /**
     * 根据主键批量查询
     * @param materialSnapshotIds 物料快照id集合
     * @return 物料快照集合
     */
    @PostMapping("/list")
    DataResponse<List<MaterialSnapshotVo>> listByIds(@RequestBody List<Long> materialSnapshotIds) {
        return DataResponse.ok(materialSnapshotService.listByIds(materialSnapshotIds));
    }

    /**
     * 批量更新物料快照辅料最小单位 -打版服务_用量核算环节换更新辅料单位
     * @param req 入参
     * @return Void
     */
    @NoRepeatSubmitLock
    @PostMapping("/update/mini-price-unit")
    DataResponse<Void> updateMiniPriceUnit(@Validated @RequestBody List<SnapshotUnitUpdateReq> req){
        materialSnapshotService.batchUpdateMinPriceUnit(req);
        return DataResponse.ok();
    }


    /**
     * 刷新好料网物料中的供应商信息(非线上环境的新数据)
     * @return Void
     */
    @PostMapping("/new-material/update-supplier")
    @NoRepeatSubmitLock(lockTime = 15L)
    public DataResponse<Void> updateNewMaterialSupplierInfo() {
        snapshotDataHandleService.updateNewMaterialSupplierInfo();
        return DataResponse.ok();
    }


    /**
     * 物料图片刷新-2022年10月20日之后提交的数据，需要重新拉一次图片
     * @return Void
     */
    @PostMapping("/update-picture")
    @NoRepeatSubmitLock(lockTime = 15L)
    public DataResponse<Void> updateMatchPicture(@RequestParam(required = false) Integer handleSize) {
        this.setSystemUser();
        try {
            snapshotDataHandleService.updateMatchPicture(handleSize);
            return DataResponse.ok();
        } finally {
            UserContentHolder.clean();
        }
    }

    private void setSystemUser() {
        UserContentHolder.set(new UserContent()
                .setCurrentUserId(0L)
                .setCurrentUserCode("0")
                .setCurrentUserName("系统")
                .setTenantId(2L)
                .setSystemCode("SDP"));
    }

}