package tech.tiangong.sdp.design.converter.visual;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.design.converter.SpuIdentifyConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.visual.*;
import tech.tiangong.sdp.design.repository.VisualQcAiBoxLogRepository;
import tech.tiangong.sdp.design.repository.VisualQcRepository;
import tech.tiangong.sdp.design.vo.resp.visual.*;

import java.util.*;
import java.util.stream.Collectors;

@AllArgsConstructor
@Component
public class VisualQcConverter {
    private final VisualQcRepository visualQcRepository;
    private final VisualQcAiBoxLogRepository visualQcAiBoxLogRepository;

    /**
     * 质检结果列表转换vo
     * @param list  列表数据
     * @param processNode   节点编码    101-tryOn处理 102-上架图处理 201-视觉质检 202-买手质检
     * @param aiBoxTaskStateList    aibox任务状态 0未发起 2进行中 3,5已完成
     * @return
     */
    public List<VisualTaskQcListVo> trans2VisualTaskQcListVo(List<VisualTaskQcListVo> list, Integer processNode, List<Integer> aiBoxTaskStateList) {
        if(CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Long> taskIds = list.stream().map(VisualTaskQcListVo::getTaskId).toList();
        List<VisualQc> qcList = visualQcRepository.listLatestByTaskIds(taskIds);
        List<VisualQcAiBoxLog> visualQcAiBoxLogList = visualQcAiBoxLogRepository.listByTaskIds(taskIds);
        Map<Long,List<VisualQc>> taskIdToQcMap = qcList.stream().collect(Collectors.groupingBy(VisualQc::getTaskId));
        Map<Long, List<VisualQcAiBoxLog>> logByTaskIdMap = visualQcAiBoxLogList.stream().collect(Collectors.groupingBy(VisualQcAiBoxLog::getTaskId));
        list.forEach(item -> {
            List<VisualQc> visualQcs = taskIdToQcMap.get(item.getTaskId());
            List<VisualQcAiBoxLog> logByTaskIdList = logByTaskIdMap.getOrDefault(item.getTaskId(), Collections.emptyList());
            Map<Long, List<VisualQcAiBoxLog>> logByQcRecordIdMap = logByTaskIdList.stream().collect(Collectors.groupingBy(VisualQcAiBoxLog::getQcRecordId));
            Integer visualAiBoxTaskState = 0;
            Integer buyerAiBoxTaskState = 0;
            if(CollectionUtil.isNotEmpty(visualQcs)) {
               VisualQc visualQc = visualQcs.stream().filter(v-> VisualQcTypeEnum.VISUAL.getCode().equals(v.getQcType())).findFirst().orElse(null);
               VisualQc buyerQc = visualQcs.stream().filter(v-> VisualQcTypeEnum.BUYER.getCode().equals(v.getQcType())).findFirst().orElse(null);
               if(visualQc != null) {
                   item.setVisualQcResult(visualQc.getQcResult());
                   item.setVisualQcState(visualQc.getQcState());
                   item.setVisualQcVersionNum(visualQc.getVersionNum());
                   item.setVisualQcInspectorId(visualQc.getInspectorId());
                   item.setVisualQcInspectorName(visualQc.getInspectorName());

                   //将质检图片转成列表
                   if(StringUtils.isNotBlank(visualQc.getQcRecord())) {
                       List<VisualQcRecordVo> visualQcRecordVos = JSONObject.parseArray(visualQc.getQcRecord(), VisualQcRecordVo.class);
                       this.setOnShelfImage(item, visualQcRecordVos);
                   }

                   visualAiBoxTaskState = determineAiBoxTaskState(logByQcRecordIdMap.get(visualQc.getQcRecordId()));
               }
               if(buyerQc != null) {
                   item.setBuyerQcResult(buyerQc.getQcResult());
                   item.setBuyerQcState(buyerQc.getQcState());
                   item.setBuyerQcVersionNum(buyerQc.getVersionNum());
                   item.setBuyerQcInspectorId(buyerQc.getInspectorId());
                   item.setBuyerQcInspectorName(buyerQc.getInspectorName());

                   //如果已经有买手质检任务，则覆盖视觉质检将质检图片转成列表
                   if(StringUtils.isNotBlank(buyerQc.getQcRecord())) {
                       List<VisualQcRecordVo> visualQcRecordVos = JSONObject.parseArray(buyerQc.getQcRecord(), VisualQcRecordVo.class);
                       this.setOnShelfImage(item, visualQcRecordVos);
                   }

                   buyerAiBoxTaskState = determineAiBoxTaskState(logByQcRecordIdMap.get(buyerQc.getQcRecordId()));
               }
            }

            if (Objects.isNull(processNode)) {
                //不是视觉质检/买手质检的,取这两个中的最大
                item.setAiBoxTaskState(Math.max(visualAiBoxTaskState, buyerAiBoxTaskState));
                // 如果单选未发起的,取最小的(正常不在视觉质检/买手质检,不会有进行中的,所以此处不考虑其他情况)
                if (aiBoxTaskStateList.size() == 1 && aiBoxTaskStateList.getFirst().equals(0)) {
                    item.setAiBoxTaskState(Math.min(visualAiBoxTaskState, buyerAiBoxTaskState));
                }
            } else if (VisualTaskNodeEnum.VISUAL_QC.getCode().equals(processNode)) {
                item.setAiBoxTaskState(visualAiBoxTaskState);
            } else if (VisualTaskNodeEnum.BUYER_QC.getCode().equals(processNode)) {
                item.setAiBoxTaskState(buyerAiBoxTaskState);
            }
        });
        return list;
    }

    private void setOnShelfImage(VisualTaskQcListVo item, List<VisualQcRecordVo> visualQcRecordVos) {
        //主图301
        VisualQcRecordVo mainPictureVo = visualQcRecordVos.stream()
                .filter(v -> v.getImageFile() != null
                        && StringUtils.isNotBlank(v.getImageFile().getOssImageUrl())
                        && this.checkMainPicture(v.getImageFile().getOrgImgName())
                )
                .findFirst().orElse(null);
        if (Objects.nonNull(mainPictureVo)) {
            List<String> onShelfImages = new ArrayList<>(visualQcRecordVos.size());
            onShelfImages.add(mainPictureVo.getImageFile().getOssImageUrl());
            onShelfImages.addAll(
                    visualQcRecordVos.stream()
                            .filter(v -> v.getImageFile() != null
                                    && StringUtils.isNotBlank(v.getImageFile().getOssImageUrl())
                                    && !Objects.equals(v.getImageFile().getOssImageUrl(), mainPictureVo.getImageFile().getOssImageUrl())
                            )
                            .map(v -> v.getImageFile().getOssImageUrl())
                            .toList()
            );
            item.setOnShelfImages(onShelfImages);
        }else {
            item.setOnShelfImages(visualQcRecordVos.stream()
                    .filter(v -> v.getImageFile() != null && StringUtils.isNotBlank(v.getImageFile().getOssImageUrl()))
                    .map(v -> v.getImageFile().getOssImageUrl())
                    .collect(Collectors.toList()));
        }
    }

    private boolean checkMainPicture(String fileName) {
        String[] parts = fileName.split(StrUtil.DASHED);
        String lastPart = parts[parts.length - 1];
        return lastPart.contains("301");
    }


    public VisualQcDetailVo tran2VisualQcDetailVo(VisualQc visualQc,
                                                  VisualQc noPassQc,
                                                  VisualTask visualTask,
                                                  VisualSpu visualSpu,
                                                  VisualDemand visualDemand,
                                                   VisualTaskTryOn latestVisualTaskTryOn,
                                                   VisualTaskOnShelf latestVisualTaskOnShelf,
                                                  List<VisualQcAiBoxImage> visualQcAiBoxImageList,
                                                  Map<String, List<SpuIdentify>> spuIdentifyGroupMap){
        if(visualQc==null){
            return null;
        }
        VisualQcDetailVo vo = new VisualQcDetailVo();
        vo.setQcRecordId(visualQc.getQcRecordId());
        vo.setTaskId(visualQc.getTaskId());
        vo.setQcType(visualQc.getQcType());
        vo.setQcState(visualQc.getQcState());
        vo.setQcStateDesc(VisualTaskQcStateEnum.code2Desc(visualQc.getQcState()));
        vo.setIsLatest(visualQc.getIsLatest());
        vo.setInspectorId(visualQc.getInspectorId());
        vo.setInspectorName(visualQc.getInspectorName());
        vo.setQcResult(visualQc.getQcResult());
        vo.setQcResultDesc(VisualTaskQcResultEnum.code2Desc(visualQc.getQcResult()));
        vo.setCreatedTime(visualQc.getCreatedTime());
        vo.setRejectType(visualQc.getRejectType());
        if(StringUtils.isNotBlank(visualQc.getQcRecord())){
            List<VisualQcRecordVo> list = getQcRecordVos(visualQc);
            vo.setQcRecords(list);
        }
        if(CollectionUtil.isNotEmpty(visualQcAiBoxImageList)){
            List<AiBoxRecordVo> qcAiBoxRecords = visualQcAiBoxImageList.stream()
                    .map(e -> new AiBoxRecordVo(e.getAiBoxTaskId(), e.getGeneratedImgName(), e.getGeneratedImg()))
                    .toList();
            vo.setQcAiBoxRecords(qcAiBoxRecords);
        }
        //上次返修内容
        if (Objects.nonNull(noPassQc) && StringUtils.isNotBlank(noPassQc.getQcRecord())) {
            List<VisualQcRecordVo> noPassQcRecordVos = getQcRecordVos(noPassQc);
            vo.setLatestNoPassRecords(noPassQcRecordVos);
            vo.setLatestRejectType(noPassQc.getRejectType());
        }
        vo.setOnShelfHandleVersion(latestVisualTaskOnShelf!=null ? latestVisualTaskOnShelf.getVersionNum() : null);
        vo.setTryOnHandleVersion(latestVisualTaskTryOn!=null ? latestVisualTaskTryOn.getVersionNum() : null);
        vo.setTaskHandleTime(visualQc.getTaskHandleTime());
        vo.setTaskHandlerId(visualQc.getTaskHandlerId());
        vo.setTaskHandlerName(visualQc.getTaskHandlerName());
        vo.setVisualDemandVersion(visualDemand.getVersionNum());
        vo.setVersionNum(visualQc.getVersionNum());

        VisualTaskBaseInfoVo visualTaskBaseInfoVo = new VisualTaskBaseInfoVo();
        BeanUtils.copyProperties(visualTask, visualTaskBaseInfoVo);
        vo.setTaskBaseInfo(visualTaskBaseInfoVo);

        VisualSpuVo visualSpuVo = new VisualSpuVo();
        BeanUtils.copyProperties(visualSpu, visualSpuVo);
        vo.setVisualSpu(visualSpuVo);

        //款式识别信息
        vo.setMuseIdentifyInfo(SpuIdentifyConverter.buildIdentifyInfo(String.valueOf(visualTask.getTaskId()), spuIdentifyGroupMap));
        return vo;
    }

    private static List<VisualQcRecordVo> getQcRecordVos(VisualQc visualQc) {
        List<VisualQcRecordVo> list = JSONObject.parseArray(visualQc.getQcRecord(),VisualQcRecordVo.class);
        list.forEach(item->{
            if(StringUtils.isNotBlank(item.getRemarkImages())) {
                item.setRemarkImageList(StrUtil.splitTrim(item.getRemarkImages(), StrUtil.COMMA));
            }
        });
        return list;
    }

    private Integer determineAiBoxTaskState (List<VisualQcAiBoxLog> logList) {
        if (CollectionUtil.isEmpty(logList)) {
            return 0;
        } else if (logList.stream().anyMatch(e-> VisualTaskHandleStateEnum.DOING.getCode().equals(e.getAiBoxTaskState()))) {
            return VisualTaskHandleStateEnum.DOING.getCode();
        } else if (logList.stream().anyMatch(e-> VisualTaskHandleStateEnum.FINISH.getCode().equals(e.getAiBoxTaskState()))) {
            return VisualTaskHandleStateEnum.FINISH.getCode();
        } else {
            return VisualTaskHandleStateEnum.FAIL.getCode();
        }
    }
}
