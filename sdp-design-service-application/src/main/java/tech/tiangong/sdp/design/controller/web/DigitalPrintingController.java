package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.DigitalPrintingStyleService;
import tech.tiangong.sdp.design.vo.query.digital.DigitalPrintingQuery;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingDetailVo;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingQueryVo;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingRePushVo;


/**
 * 数码印花款-web
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/digital-printing")
public class DigitalPrintingController extends BaseController {
    private final DigitalPrintingStyleService digitalPrintingStyleService;

    /**
     * 查询列表（分页）
     *
     * @param queryDTO 分页对象
     * @return PageRespVo<DigitalPrintingStyleVo>
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<DigitalPrintingQueryVo>> page(@RequestBody @Validated DigitalPrintingQuery queryDTO) {
        return DataResponse.ok(digitalPrintingStyleService.page(queryDTO));
    }

    /**
     * 详情
     *
     * @param printingPrototypeId 数码印花skcId
     * @return 响应结果
     */
    @GetMapping("/detail/{printingPrototypeId}")
    public DataResponse<DigitalPrintingDetailVo> getDetailById(@PathVariable(value = "printingPrototypeId") Long printingPrototypeId) {
        return DataResponse.ok(digitalPrintingStyleService.getDetailById(printingPrototypeId));
    }

    /**
     * 失败重推
     *
     * @return 响应结果
     */
    @PostMapping("/re-push")
    @NoRepeatSubmitLock(lockTime = 30L)
    public DataResponse<DigitalPrintingRePushVo> rePush() {
        return DataResponse.ok(digitalPrintingStyleService.rePush());
    }

}
