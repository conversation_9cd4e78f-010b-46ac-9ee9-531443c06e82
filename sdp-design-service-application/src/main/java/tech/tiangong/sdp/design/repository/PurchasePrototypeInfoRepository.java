package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.PurchasePrototypeInfo;
import tech.tiangong.sdp.design.mapper.PurchasePrototypeInfoMapper;

@AllArgsConstructor
@Slf4j
@Repository
public class PurchasePrototypeInfoRepository extends BaseRepository<PurchasePrototypeInfoMapper, PurchasePrototypeInfo> {




}
