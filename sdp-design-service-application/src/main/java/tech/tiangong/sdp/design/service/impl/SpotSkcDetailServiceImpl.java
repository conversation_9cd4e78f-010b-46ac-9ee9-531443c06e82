package tech.tiangong.sdp.design.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.SpotSkcDetail;
import tech.tiangong.sdp.design.repository.SpotSkcDetailRepository;
import tech.tiangong.sdp.design.service.SpotSkcDetailService;
import tech.tiangong.sdp.design.vo.req.spot.SpotSkcDetailReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcDetailVo;

/**
 * spot_skc_detail表(SpotSkcDetail)服务
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpotSkcDetailServiceImpl implements SpotSkcDetailService {
    private final SpotSkcDetailRepository spotSkcDetailRepository;


    @Override
    public SpotSkcDetailVo getById(Long spotSkcDetailId) {
        SpotSkcDetail entity = spotSkcDetailRepository.getById(spotSkcDetailId);
        SpotSkcDetailVo vo = new SpotSkcDetailVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SpotSkcDetailReq req) {
        SpotSkcDetail entity = new SpotSkcDetail();
        BeanUtils.copyProperties(req, entity);
        entity.setSpotSkcDetailId(IdPool.getId());
        spotSkcDetailRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SpotSkcDetailReq req) {
        SpotSkcDetail entity = new SpotSkcDetail();
        BeanUtils.copyProperties(req, entity);
        spotSkcDetailRepository.updateById(entity);
    }
}
