package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.ZjApsService;
import tech.tiangong.sdp.design.vo.req.zj.ZjSupplierReq;
import tech.tiangong.sdp.design.vo.resp.zj.SupplierInfoVo;

import java.util.List;


/**
 * ZJ-APS-web
 *
 * <AUTHOR>
 * @since 2025-02-25 11:37:13
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB +  UrlVersionConstant.VERSION_V1  + "/aps")
public class ZjApsController extends BaseController {

    private final ZjApsService zjApsService;

    /**
     * 供应商查询
     *
     * @param req 入参
     * @return List<SupplierInfoVo>
     */
    @PostMapping("/supplier/query")
    public DataResponse<List<SupplierInfoVo>> supplierQuery(@RequestBody @Validated ZjSupplierReq req) {
        return DataResponse.ok(zjApsService.supplierQuery(req));
    }


}
