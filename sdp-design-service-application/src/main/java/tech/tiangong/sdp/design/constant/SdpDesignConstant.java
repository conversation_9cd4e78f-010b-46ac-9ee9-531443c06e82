package tech.tiangong.sdp.design.constant;

import tech.tiangong.sdp.design.mq.rocketmq.entity.MurmurationAbility;
import tech.tiangong.sdp.design.mq.rocketmq.entity.MurmurationSupplier;

/**
 * 设计中心 业务常量
 *
 * <AUTHOR>
 */
public interface SdpDesignConstant {

	/**
	 * 供给方式-仿款
	 */
	String COPY_STYLE_NAME = "仿款";
	/**
	 * SDP 设计中心服务前缀
	 */
	String SDP_DESIGN_PREFIX = "sdp-design";

	/**
	 * Murmuration 任务相关常量
	 */
	interface Murmuration {

		/**
		 * AE 属性转换任务 Tag
		 * 格式: sdp-design:AE_PUTAWAY_ATTRIBUTE_CONVERT:AIP
		 */
		String AE_ATTRIBUTE_CONVERT_TAG = 
				SDP_DESIGN_PREFIX + ":" + MurmurationAbility.AE_PUTAWAY_ATTRIBUTE_CONVERT.name() + ":" + MurmurationSupplier.AIP.name();
		
		/**
		 * SKC 颜色识别任务 Tag  
		 * 格式: sdp-design:SKC_COLOR_IDENTIFICATION:AIP
		 */
		String SKC_COLOR_IDENTIFICATION_TAG = 
				SDP_DESIGN_PREFIX + ":" + MurmurationAbility.SKC_COLOR_IDENTIFICATION.name() + ":" + MurmurationSupplier.AIP.name();

		/**
		 * 1688自动铺货图包 Tag
		 * 格式: sdp-design:SKC_COLOR_IDENTIFICATION:AIP
		 */
		String ALIBABA_DISTRIBUTION_TAG =
				SDP_DESIGN_PREFIX + ":" + MurmurationAbility.ALIBABA_DISTRIBUTION.name() + ":" + MurmurationSupplier.AIP.name();

		/**
		 * AE 属性转换业务类型 (用作 bizType 和 MQ Tag)
		 */
		String AE_ATTRIBUTE_CONVERT_BIZ_TYPE = AE_ATTRIBUTE_CONVERT_TAG;
		
		/**
		 * SKC 颜色识别业务类型 (用作 bizType 和 MQ Tag)
		 */
		String SKC_COLOR_IDENTIFICATION_BIZ_TYPE = SKC_COLOR_IDENTIFICATION_TAG;
		/**
		 * 1688自动铺货图包 (用作 bizType 和 MQ Tag)
		 */
		String ALIBABA_DISTRIBUTION_BIZ_TYPE = ALIBABA_DISTRIBUTION_TAG;


		/**
		 * 图片裁剪
		 */
		String IMAGE_CROPPING_TAG =
				SDP_DESIGN_PREFIX + ":" + MurmurationAbility.IMAGE_CROPPING.name() + ":" + MurmurationSupplier.AIDGE.name();



		/**
		 * 图片裁剪 (用作 bizType 和 MQ Tag)
		 */
		String IMAGE_CROPPING_BIZ_TYPE = IMAGE_CROPPING_TAG;


		/**
		 * 图片生成视频
		 */
		String IMAGE_TO_VIDEO_TAG =
				SDP_DESIGN_PREFIX + ":" + MurmurationAbility.IMAGE_TO_VIDEO.name() + ":" + MurmurationSupplier.MIDJOURNEY.name();




		/**
		 * 图片生成视频 (用作 bizType 和 MQ Tag)
		 */
		String IMAGE_TO_VIDEO_BIZ_TYPE = IMAGE_TO_VIDEO_TAG;

		/**
		 * 所有支持的任务 Tag（用于 RocketMQ 过滤表达式）
		 */
		String ALL_TASK_TAGS =
				AE_ATTRIBUTE_CONVERT_TAG + " || " + SKC_COLOR_IDENTIFICATION_TAG + " || " + ALIBABA_DISTRIBUTION_TAG + " || " + IMAGE_CROPPING_TAG + " || " + IMAGE_TO_VIDEO_TAG;



	}
}
