package tech.tiangong.sdp.design.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tech.tiangong.sdp.design.entity.SpecialAccessoriesTransient;
import tech.tiangong.sdp.design.vo.query.bom.SpecialAccessoriesTransientQuery;
import tech.tiangong.sdp.design.vo.resp.bom.SpecialAccessoriesTransientVo;

/**
 * 特殊辅料_暂存表数据库访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface SpecialAccessoriesTransientMapper extends BaseMapper<SpecialAccessoriesTransient> {

    /**
     * 分页查询
     *
     * @param page  分页
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<SpecialAccessoriesTransientVo> findPage(@Param("page") Page<SpecialAccessoriesTransientVo> page, @Param("query") SpecialAccessoriesTransientQuery query);

}
