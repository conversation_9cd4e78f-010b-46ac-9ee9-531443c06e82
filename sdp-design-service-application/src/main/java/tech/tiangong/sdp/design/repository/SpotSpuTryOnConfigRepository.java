package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.design.entity.SpotSpuTryOnConfig;
import tech.tiangong.sdp.design.mapper.SpotSpuTryOnConfigMapper;
import tech.tiangong.sdp.utils.Bool;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * tryOn任务分配配置表服务仓库类
 *
 * <AUTHOR>
 */

@AllArgsConstructor
@Slf4j
@Repository
public class SpotSpuTryOnConfigRepository extends BaseRepository<SpotSpuTryOnConfigMapper, SpotSpuTryOnConfig> {

    public List<SpotSpuTryOnConfig> listByStyleCodes(Collection<String> styleCodes){
        if(CollectionUtils.isEmpty(styleCodes)){
           return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SpotSpuTryOnConfig>()
                .in(SpotSpuTryOnConfig::getStyleCode,styleCodes)
                .eq(SpotSpuTryOnConfig::getIsDeleted, Bool.NO.getCode())
        );
    }

}
