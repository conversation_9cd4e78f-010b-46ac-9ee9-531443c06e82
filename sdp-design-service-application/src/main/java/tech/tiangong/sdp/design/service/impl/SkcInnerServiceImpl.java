package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjkj.booster.common.user.UserContent;
import com.zjkj.booster.common.user.UserContentHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.pop.common.dto.CreateProductDto;
import tech.tiangong.pop.common.dto.Image;
import tech.tiangong.sdp.clothes.vo.req.CreateSampleClothesReq;
import tech.tiangong.sdp.clothes.vo.req.DesignCodeSpuQuery;
import tech.tiangong.sdp.clothes.vo.resp.SampleClothesVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.DesignPricingInfoVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.converter.DesignStyleConverter;
import tech.tiangong.sdp.design.converter.PopCreateInfoConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.*;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.DesignStyleService;
import tech.tiangong.sdp.design.service.PrototypeSampleRecodeService;
import tech.tiangong.sdp.design.service.SkcService;
import tech.tiangong.sdp.design.service.VisualDemandService;
import tech.tiangong.sdp.design.vo.req.mq.ClothesChangeMqDto;
import tech.tiangong.sdp.design.vo.req.mq.DesignPrototypeSalesChannelDTO;
import tech.tiangong.sdp.design.vo.req.prototype.SampleRecodeCreateReq;
import tech.tiangong.sdp.design.vo.req.prototype.inner.*;
import tech.tiangong.sdp.design.vo.resp.prototype.*;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleInnerVo;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleVo;
import tech.tiangong.sdp.design.vo.resp.style.PopCreateProductVo;
import tech.tiangong.sdp.prod.vo.resp.StyleSizeInfoVo;
import tech.tiangong.sdp.utils.Bool;
import tech.tiangong.sdp.utils.StreamUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
* skc-inner service
* <AUTHOR>
*/

@Slf4j
@Service
@RequiredArgsConstructor
public class SkcInnerServiceImpl implements SkcService {

    private final DesignStyleRepository designStyleRepository;
    private final PrototypeRepository prototypeRepository;
    private final PrototypeHistoryRepository prototypeHistoryRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final OnShelfSpuRepository onShelfSpuRepository;
    private final OnShelfSkcRepository onShelfSkcRepository;
    private final PrototypeSampleRecodeService prototypeSampleRecodeService;
    private final DesignStyleService designStyleService;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final DesignDemandRepository designDemandRepository;
    private final DesignDemandDetailRepository designDemandDetailRepository;
    private final PopProductHelper popProductHelper;
    private final PrototypeManageServiceImpl prototypeManageServiceImpl;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final CheckPriceRemoteHelper checkPriceRemoteHelper;
    private final VisualDemandService visualDemandService;
    private final VisualTaskRepository visualTaskRepository;
    private final VisualSpuRepository visualSpuRepository;
    private final MqProducer mqProducer;
    private final SdpOrderHelper sdpOrderHelper;



    /**
     * 分页数量最大
     */
    private static final int PAGE_SIZE_LIMIT_MAX = 1000;

    @Override
    public PageRespVo<SkcInnerQueryVo> pageInner(SkcInnerQuery query) {
        if (query.getPageSize() > PAGE_SIZE_LIMIT_MAX) {
            query.setPageSize(PAGE_SIZE_LIMIT_MAX);
        }

        IPage<SkcInnerQueryVo> page = prototypeRepository.skcPageInner(query);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), Collections.emptyList());
        }
        //SKC详情
        List<Long> prototypeIdList = StreamUtil.convertListAndDistinct(page.getRecords(), SkcInnerQueryVo::getPrototypeId);
        Map<Long, PrototypeDetail> prototypeDetailMap = StreamUtil.list2Map(prototypeDetailRepository.getListByPrototypeIds(prototypeIdList), PrototypeDetail::getPrototypeId);

        //skc上架信息
        List<String> designCodeList = StreamUtil.convertListAndDistinct(page.getRecords(), SkcInnerQueryVo::getDesignCode);
        Map<String, OnShelfSkc> onShelfSkcMap = StreamUtil.list2Map(onShelfSkcRepository.listByDesignCodes(designCodeList), OnShelfSkc::getDesignCode);

        for (SkcInnerQueryVo vo : page.getRecords()) {
            //skc详情信息
            PrototypeDetail prototypeDetail = prototypeDetailMap.get(vo.getPrototypeId());
            if (Objects.nonNull(prototypeDetail)) {
                if (StrUtil.isNotBlank(prototypeDetail.getDesignPicture())) {
                    vo.setDesignPictureList(StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA));
                }
                vo.setSampleSize(prototypeDetail.getSampleSize());
            }
            //商品上架图片信息
            if (Objects.nonNull(onShelfSkcMap.get(vo.getDesignCode()))) {
                OnShelfSkc onShelfSkc = onShelfSkcMap.get(vo.getDesignCode());
                SkcInnerQueryVo.ProductInfo productInfo = new SkcInnerQueryVo.ProductInfo();
                productInfo.setStyleCode(vo.getStyleCode());
                productInfo.setDesignCode(vo.getDesignCode());
                productInfo.setSkcImageList(onShelfSkc.getSkcImageList());
                vo.setProductInfo(productInfo);
            }
        }

        //号型信息封装
        // this.setSizeTypeName(page);

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    /*
    private void setSizeTypeName(IPage<SkcInnerQueryVo> page) {
        List<String> categoryCodeList = StreamUtil.convertListAndDistinct(page.getRecords(), SkcInnerQueryVo::getCategory);
        List<String> sizeStandardCodeList = StreamUtil.convertListAndDistinct(page.getRecords(), SkcInnerQueryVo::getSizeStandardCode);
        List<String> sampleSizeList = StreamUtil.convertListAndDistinct(page.getRecords(), SkcInnerQueryVo::getSampleSize);

        SizeTypeInfoListReq listReq = new SizeTypeInfoListReq();
        listReq.setCategoryCodeList(categoryCodeList);
        listReq.setSizeStandardCodeList(sizeStandardCodeList);
        listReq.setSampleSizeList(sampleSizeList);
        Map<String, SizeTypeInfoVo> sizeTypeInfoMap = this.getStringSizeTypeInfoDtoMap(listReq);

        for (SkcInnerQueryVo vo : page.getRecords()) {
            //号型信息
            SizeTypeInfoVo sizeTypeInfo = sizeTypeInfoMap.get(vo.getCategory() + vo.getSizeStandardCode() + vo.getSampleSize());
            if (Objects.nonNull(sizeTypeInfo)) {
                vo.setSizeTypeName(sizeTypeInfo.getSizeTypeName());
            }
        }
    }

    private Map<String, SizeTypeInfoVo> getStringSizeTypeInfoDtoMap(SizeTypeInfoListReq listReq) {
        List<SizeTypeInfoVo> sizeTypeInfoVoList = clothingFoundationRemoteHelper.sizeTypeInfoList(listReq);
        return StreamUtil.list2Map(sizeTypeInfoVoList, SizeTypeInfoVo::getCategorySizeStandSize);
    }
     */


    @Transactional(rollbackFor = Exception.class)
    @Override
    public AskClothesVo askClothes(AskClothesInnerReq req) {
        //动销,核价发起打版
        log.info("=== 动销核价发起打版-req:{} ===", JSON.toJSONString(req));

        //只处理动销
        if (Objects.equals(req.getStartClothsType(), 2)) {
            return null;
        }
        if (!Objects.equals(req.getStartClothsType(), 1)) {
            throw new SdpDesignException("未知发起类型!");
        }

        String designCode = req.getDesignCode();
        //判断SPU下所有的SKC是否有存在一个已发起打版需求
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        SdpDesignException.notNull(prototype, "skc不存在!");

        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId());
        SdpDesignException.notNull(prototypeDetail, "skc详情不存在!");

        DesignStyle designStyle = designStyleRepository.getByStyleCode(prototype.getStyleCode());
        SdpDesignException.notNull(designStyle, "spu不存在!");

        Long clothesId = null;
        String processCode = null;
        //查询打版服务
        List<SampleClothesVo> clothesVoList = sampleClothesRemoteHelper.listByStyleCode(prototype.getStyleCode());
        if (CollUtil.isEmpty(clothesVoList)) {
            //若无, 自动对应SKC发起-仅纸样 打版需求;
            CreateSampleClothesReq clothesReq = new CreateSampleClothesReq();
            clothesReq.setDesignCode(req.getDesignCode());
            clothesReq.setMakeClothesType(tech.tiangong.sdp.clothes.enums.MakeClothesTypeEnum.ONLY_PATTERN_CLOTHES);
            clothesReq.setSampleSize(prototypeDetail.getSampleSize());
            SampleClothesVo sampleClothes = sampleClothesRemoteHelper.createSampleClothes(clothesReq);

            clothesId = sampleClothes.getClothesId();
            processCode = sampleClothes.getProcessCode();
        }

        // 取消日志记录, pop调用太频繁, 每天几万次调用
        // this.addOnSaleLog(req, designCode, designStyle, prototypeDetail, clothesId, processCode);

        //若是动销打版发起, 该skc标记为动销
        if (Objects.equals(req.getStartClothsType(), 1) && !prototype.getIsOnSale()) {
            Prototype prototypeUpdate = new Prototype();
            prototypeUpdate.setPrototypeId(prototype.getPrototypeId());
            prototypeUpdate.setIsOnSale(Boolean.TRUE);
            prototypeRepository.updateById(prototypeUpdate);

            UserContent userContent = UserContentHolder.get();
            PrototypeHistory historyUpdate = PrototypeHistory.builder()
                    .isOnSale(Boolean.TRUE)
                    .revisedTime(LocalDateTime.now())
                    .build();
            if (Objects.nonNull(userContent)) {
                historyUpdate.setReviserId(userContent.getCurrentUserId());
                historyUpdate.setReviserName(userContent.getCurrentUserName());
            }
            prototypeHistoryRepository.updateByDesignCode(designCode, historyUpdate);

            // 异步执行，不阻塞主流程
            CompletableFuture.runAsync(() -> {
                try {
                    //创建视觉需求
                    this.createVisualDemand4Sale(prototype);
                } catch (Exception e) {
                    log.error("异步创建视觉需求失败", e);
                }
            });
        }

        String currentChannel = prototype.getSalesChannel();
        String newChannel = req.getSalesChannel();
        String updatedChannel = null; // 记录更新后的值

        if (StringUtils.isEmpty(currentChannel)) {
            // 空值直接使用新渠道
            updatedChannel = newChannel;
        } else if (!currentChannel.contains(newChannel)) {
            // 不包含新渠道时追加
            updatedChannel = currentChannel + "," + newChannel;
        }

        // 仅当有实际更新时才执行数据库操作
        if (updatedChannel != null) {
            Prototype prototypeUpdate = new Prototype();
            prototypeUpdate.setPrototypeId(prototype.getPrototypeId());
            prototypeUpdate.setSalesChannel(updatedChannel);
            prototypeRepository.updateById(prototypeUpdate);

            //通知其他服务
            DesignPrototypeSalesChannelDTO mqDTO = new DesignPrototypeSalesChannelDTO();
            mqDTO.setStyleCode(prototype.getStyleCode());
            mqDTO.setPrototypeId(prototype.getPrototypeId());
            mqDTO.setDesignCode(prototype.getDesignCode());
            mqDTO.setSourceType(designStyle.getSourceType());
            mqDTO.setSkcType(prototype.getSkcType());
            mqDTO.setBizChannel(designStyle.getBizChannel());
            mqDTO.setSalesChannel(prototypeUpdate.getSalesChannel());
            MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.DESIGN_PROTOTYPE_SALES_CHANNEL_SEND,
                    DesignMqConstant.DESIGN_PROTOTYPE_UPDATE_SALES_CHANNEL_EXCHANGE,
                    JSON.toJSONString(mqDTO));
            log.info("=== 同步skc信息类销售渠道字段-mqMessageReq:{} ===", JSON.toJSONString(mqMessageReq));
            //发送消息
            mqProducer.sendOnAfterCommit(mqMessageReq);

        }

        //发起数码描稿任务
        sampleClothesRemoteHelper.digitalPainting(designCode);

        return AskClothesVo.builder()
                .startClothsType(req.getStartClothsType())
                .designCode(designCode)
                .clothesId(clothesId)
                .processCode(processCode)
                .build();
    }

    private void addOnSaleLog(AskClothesInnerReq req, String designCode, DesignStyle designStyle, PrototypeDetail prototypeDetail, Long clothesId, String processCode) {
        //记录发起打版记录
        SampleRecodeCreateReq recodeCreateReq = SampleRecodeCreateReq.builder()
                .designCode(designCode)
                .makeClothesType(MakeClothesTypeEnum.ONLY_PATTERN_CLOTHES.getCode())
                .sizeStandard(designStyle.getSizeStandard())
                .sizeStandardCode(designStyle.getSizeStandardCode())
                .sampleSize(prototypeDetail.getSampleSize())
                .sampleAmount(1)
                .clothesId(clothesId)
                .processCode(processCode)
                .build();
        Integer sourceType = null;
        //发起类型: 1-动销; 2-核价
        if (Objects.equals(req.getStartClothsType(), 1)) {
            sourceType = StartClothesSourceTypeEnum.ORDER_ON_SALE.getCode();
        } else if (Objects.equals(req.getStartClothsType(), 2)) {
            sourceType = StartClothesSourceTypeEnum.ORDER_PRICE_NOTICE.getCode();
        } else {
            throw new SdpDesignException("未知发起类型!");
        }
        recodeCreateReq.setSourceType(sourceType);
        prototypeSampleRecodeService.create(recodeCreateReq);
    }

    private void createVisualDemand4Sale(Prototype prototype) {
        String styleCode = prototype.getStyleCode();
        try {
            //标识了拒绝动销的视觉SPU, 不发起
            VisualSpu visualSpu = visualSpuRepository.getByStyleCode(styleCode);
            if (Objects.nonNull(visualSpu) && Objects.equals(visualSpu.getOnSaleRejectState(), Bool.YES.getCode())) {
                log.info("动销发起视觉需求 拒绝动销不发起视觉需求 styleCode:{}", styleCode);
                return;
            }

            //排除当前SKU，如果SPU下还有其他SKU已发起过动销，则不再推送视觉需求任务（即为SPU的首次动销才推）
            //只有提交了大货资料+发起过动销才会生成尺寸表任务，所以如果存在有效的尺寸表任务，即SPU已经出现了首次动销的记录)
            VisualTask sizeTypeTask = visualTaskRepository.getLatestNotCancelSizeTaskBySpu(styleCode);
            Assert.isTrue(sizeTypeTask==null,"styleCode:"+styleCode+"下其他SKU已发起过动销，不用再发起视觉需求");
            log.info("动销发起视觉需求 styleCode:{}", styleCode);
            visualDemandService.createVisualDemand4ProdOrder(styleCode);
        } catch (Exception e) {
            log.error("动销发起视觉需求异常,{}", e.getMessage(), e);
        }
    }

    @Override
    public SkcDetailInnerVo getSkcInfo(SkcInfoReq req) {
        PrototypeHistory prototype = null;
        if (StrUtil.isNotBlank(req.getDesignCode())) {
            //根据skc查询最新的信息
            prototype = prototypeHistoryRepository.getLatestPrototypeByDesignCode(req.getDesignCode());
        } else if (Objects.nonNull(req.getPrototypeId())) {
            //根据id查询指定版本
            prototype = prototypeHistoryRepository.getByPrototypeId(req.getPrototypeId());
        } else {
            return null;
        }
        Long prototypeId = prototype.getPrototypeId();

        //skc详情
        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototypeId);
        SkcDetailInnerVo sdkDetailVo = new SkcDetailInnerVo();
        BeanUtils.copyProperties(prototypeDetail, sdkDetailVo);
        BeanUtils.copyProperties(prototype, sdkDetailVo);

        List<String> designPicture = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);
        sdkDetailVo.setDesignPicture(designPicture);

        //spu信息
        DesignStyleVo styleVo = designStyleService.getLatestVersionByStyleCode(prototype.getStyleCode());
        if (Objects.nonNull(styleVo)) {
            DesignStyleInnerVo styleInnerVo = new DesignStyleInnerVo();
            BeanUtils.copyProperties(styleVo, styleInnerVo);
            sdkDetailVo.setStyleInfo(styleInnerVo);
        }

        //商品上架图信息
        String designCode = prototype.getDesignCode();
        OnShelfSpu onShelfSpu = onShelfSpuRepository.getBySpu(prototype.getStyleCode());
        if (Objects.nonNull(onShelfSpu)) {
            SkcDetailInnerVo.OnShelfInfo onShelfInfo = new SkcDetailInnerVo.OnShelfInfo();
            onShelfInfo.setStyleCode(prototype.getStyleCode());
            onShelfInfo.setDesignCode(prototype.getDesignCode());
            onShelfInfo.setSpuDetailImageList(onShelfSpu.getSpuDetailImageList());
            List<OnShelfSkc> onShelfSkcList = onShelfSkcRepository.listByDesignCodes(Collections.singletonList(designCode));
            if (CollUtil.isNotEmpty(onShelfSkcList) && Objects.nonNull(onShelfSkcList.getFirst())) {
                onShelfInfo.setSkcImageList(onShelfSkcList.getFirst().getSkcImageList());
            }
            sdkDetailVo.setOnShelfInfo(onShelfInfo);
        }

        return sdkDetailVo;
    }

    @Override
    public PrototypeInnerVo getSavedInfoByDesignCode(String designCode) {
        PrototypeHistory prototype = prototypeHistoryRepository.getLatestPrototypeByDesignCode(designCode);
        if (Objects.isNull(prototype)) {
            return null;
        }
        //skc详情
        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId());

        PrototypeInnerVo prototypeVo = new PrototypeInnerVo();
        BeanUtils.copyProperties(prototypeDetail, prototypeVo);
        BeanUtils.copyProperties(prototype, prototypeVo);

        List<String> designPicture = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);
        prototypeVo.setDesignPicture(designPicture);

        return prototypeVo;
    }

    @Override
    public PrototypeInnerVo latestSubmitBySpu(String styleCode) {
        SdpDesignException.notBlank(styleCode, "spu不能为空");
        Prototype prototype = prototypeRepository.getLatestSubmitByStyleCode(styleCode);
        PrototypeInnerVo prototypeVo = new PrototypeInnerVo();
        if (prototype != null) {
            //skc详情
            PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId());
            BeanUtils.copyProperties(prototypeDetail, prototypeVo);
            BeanUtils.copyProperties(prototype, prototypeVo);
            List<String> designPicture = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);
            prototypeVo.setDesignPicture(designPicture);
        }

        return prototypeVo;
    }

    @Override
    public SkcSpuInnerVo getSaveInfoWithSpu(String designCode) {
        SdpDesignException.notBlank(designCode, "skc不能为空");

        PrototypeInnerVo prototypeInnerVo = this.getSavedInfoByDesignCode(designCode);
        if (Objects.isNull(prototypeInnerVo)) {
            return null;
        }
        SkcSpuInnerVo skcSpuInnerVo = new SkcSpuInnerVo();
        BeanUtils.copyProperties(prototypeInnerVo, skcSpuInnerVo);

        //spu信息
        DesignStyleVo styleVo = designStyleService.getLatestVersionByStyleCode(prototypeInnerVo.getStyleCode());
        if (Objects.nonNull(styleVo)) {
            DesignStyleInnerVo styleInnerVo = new DesignStyleInnerVo();
            BeanUtils.copyProperties(styleVo, styleInnerVo);
            skcSpuInnerVo.setStyleInfo(styleInnerVo);
        }

        return skcSpuInnerVo;
    }

    @Override
    public SkcSpuInnerVo latestSubmitWithSpu(String styleCode) {
        //最新已提交skc信息
        PrototypeInnerVo prototypeInnerVo = this.latestSubmitBySpu(styleCode);
        if (Objects.isNull(prototypeInnerVo)) {
            return null;
        }
        SkcSpuInnerVo skcSpuInnerVo = new SkcSpuInnerVo();
        BeanUtils.copyProperties(prototypeInnerVo, skcSpuInnerVo);

        //spu信息
        DesignStyleVo styleVo = designStyleService.getLatestVersionByStyleCode(styleCode);
        if (Objects.nonNull(styleVo)) {
            DesignStyleInnerVo styleInnerVo = new DesignStyleInnerVo();
            BeanUtils.copyProperties(styleVo, styleInnerVo);
            skcSpuInnerVo.setStyleInfo(styleInnerVo);
        }

        return skcSpuInnerVo;
    }

    @Override
    public List<NormalSkcImageVo> getNormalImage(SkcImageQuery req) {
        List<String> styleCodeList = req.getStyleCodeList();
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        //skc
        List<Prototype> prototypeList = prototypeRepository.getByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(prototypeList)) {
            return Collections.emptyList();
        }
        //正常款
        List<Long> normalPrototypeIdList = prototypeList.stream()
                .filter(item -> Objects.equals(item.getSkcType(), SkcTypeEnum.NORMAL.getCode()))
                .map(Prototype::getPrototypeId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(normalPrototypeIdList)) {
            return Collections.emptyList();
        }
        //skc详情
        List<PrototypeDetail> detailList = prototypeDetailRepository.getListByPrototypeIds(normalPrototypeIdList);
        Map<Long, PrototypeDetail> detailMap = StreamUtil.list2Map(detailList, PrototypeDetail::getPrototypeId);

        //上架图
        List<OnShelfSpu> onShelfSpuList = onShelfSpuRepository.listByStyleCodeList(styleCodeList);
        Map<String, OnShelfSpu> OnShelfSpuMap = StreamUtil.list2Map(onShelfSpuList, OnShelfSpu::getStyleCode);
        List<String> normalDesignCodeList = prototypeList.stream()
                .filter(item -> Objects.equals(item.getSkcType(), SkcTypeEnum.NORMAL.getCode()))
                .map(Prototype::getDesignCode)
                .collect(Collectors.toList());
        List<OnShelfSkc> onShelfSkcList = onShelfSkcRepository.listByDesignCodes(normalDesignCodeList);
        Map<String, OnShelfSkc> OnShelfSkcMap = StreamUtil.list2Map(onShelfSkcList, OnShelfSkc::getDesignCode);

        return prototypeList.stream()
                .filter(item -> Objects.equals(item.getSkcType(), SkcTypeEnum.NORMAL.getCode()))
                .map(item -> {
                    NormalSkcImageVo imageVo = new NormalSkcImageVo();
                    imageVo.setStyleCode(item.getStyleCode());
                    imageVo.setDesignCode(item.getDesignCode());
                    PrototypeDetail prototypeDetail = detailMap.get(item.getPrototypeId());
                    if (Objects.nonNull(prototypeDetail) && StrUtil.isNotBlank(prototypeDetail.getDesignPicture())) {
                        List<String> designPicture = StrUtil.splitTrim(prototypeDetail.getDesignPicture(),
                                StrUtil.COMMA);
                        imageVo.setDesignPictureList(designPicture);
                    }
                    //上架图
                    OnShelfSpu onShelfSpu = OnShelfSpuMap.get(item.getStyleCode());
                    if (Objects.nonNull(onShelfSpu)) {
                        imageVo.setSpuDetailImageList(onShelfSpu.getSpuDetailImageList());
                    }
                    OnShelfSkc onShelfSkc = OnShelfSkcMap.get(item.getDesignCode());
                    if (Objects.nonNull(onShelfSkc)) {
                        imageVo.setSkcImageList(onShelfSkc.getSkcImageList());
                    }
                    return imageVo;
                }).collect(Collectors.toList());
    }


    /**
     * 设计款skc推送 (设计款核价, 推送商品到商品平台)
     *
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean pushSkc(SkcPriceReq req) {
        log.info("=== 设计款推送商品运营,req: {} ===", JSON.toJSONString(req));
        String designCode = req.getDesignCode();
        // skc
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        if (ObjectUtil.isNull(prototype)) {
            throw new SdpDesignException("skc【"+designCode+"】不存在");
        }
        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId());
        String styleCode = prototype.getStyleCode();
        // spu
        DesignStyle designStyle = designStyleRepository.getByStyleCode(styleCode);
        Long designDemandId = designStyle.getDesignDemandId();

        DesignDemand designDemand = null;
        DesignDemandDetail demandDetail = null;
        if (ObjectUtil.isNotNull(designDemandId)){
            // 灵感设计需求
             designDemand = designDemandRepository.getById(designDemandId);
             demandDetail = designDemandDetailRepository.getByDemandId(designDemandId);
        }

        //尺寸表EXCEL
        List<StyleSizeInfoVo> styleSizeInfoVos = sdpOrderHelper.listSizeInfoByStyleCode(List.of(designStyle.getStyleCode()));
        DictVo dictVoByCategory = null;
        if(CollectionUtil.isEmpty(styleSizeInfoVos)  || CollectionUtil.isEmpty(styleSizeInfoVos.getFirst().getSizeDetailList())) {
            //查询当前品类字典获取尺码明细表
            dictVoByCategory = dictValueRemoteHelper.getDictVoByCategory(designStyle.getCategory());
        }

        CreateProductDto productReq = DesignStyleConverter.convertProductCreateReq(designStyle,
                prototype,designDemand,demandDetail, prototypeDetail, req.getLocalPrice(),dictVoByCategory,styleSizeInfoVos);

        //设置版型图(101), 类目图(201), 商品主图(301)_对应设计图, 尺码表(401)
        // this.setPopImageUrl(designStyle, productReq, prototypeDetail);

        //设计师/设计组
        if(prototype.getDesignerId()!=null){
            productReq.setDesignerId(String.valueOf(prototype.getDesignerId()));
        }
        productReq.setDesignName(prototype.getDesignerName());
        productReq.setDesignGroup(prototype.getDesignerGroup());

        //推送商品运营平台
        popProductHelper.noticeCreateSpotProduct(productReq);

        return true;
    }


    /*
    private void setPopImageUrl(DesignStyle designStyle, CreateProductDto productReq, PrototypeDetail prototypeDetail) {
        //版型图
        String modePicture = this.getModePicture(designStyle);
        if (StrUtil.isNotBlank(modePicture)) {
            productReq.setImage101Urls(convertImage(Collections.singletonList(modePicture), "版型图"));
        }

        //类目图与尺寸图
        this.set201And401Image(designStyle, productReq);

        //商品主图
        List<String> designPictureList = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);
        productReq.setImage301Urls(convertImage(designPictureList, "商品主图"));
    }

     */



    private List<Image> convertImage(List<String> imageUrlList, String imageName) {
        if (CollUtil.isEmpty(imageUrlList)) {
            return null;
        }
        return imageUrlList.stream().map(item -> {
            Image image = new Image();
            // image.setOrgImgName("");
            image.setOssImageUrl(item);
            return image;
        }).toList();
    }

    /**
     * 设置类目图与尺寸图
     * 查询基础资料: 尺寸表模板图这里增加一个量法图, 推给运营平台时将品类对应的量法图作为20x的图给到运营平台
     */
    /*
    private void set201And401Image(DesignStyle designStyle, CreateProductDto productReq) {
        List<SizeTemplateImageVO> sizeTemplateImageVOList = clothingFoundationRemoteHelper.sizeTemplateImage(Collections.singletonList(designStyle.getCategory()));

        if (CollUtil.isNotEmpty(sizeTemplateImageVOList)) {
            SizeTemplateImageVO sizeTemplateImageVO = sizeTemplateImageVOList.getFirst();
            //设置类目图
            List<String> quantityMethodImageUrlList = sizeTemplateImageVO.getQuantityMethodImageUrls();
            if (CollUtil.isNotEmpty(quantityMethodImageUrlList)) {
                productReq.setImage201Urls(convertImage(quantityMethodImageUrlList, "类目图"));
            }

            //设置尺寸图
            List<String> sizeTemplateImageUrlList = sizeTemplateImageVO.getSizeTemplateImageUrls();
            if (CollUtil.isNotEmpty(sizeTemplateImageUrlList)) {
                productReq.setImage401Urls(convertImage(sizeTemplateImageUrlList, "尺寸图"));
            }
        }
    }

     */

    /**
     * 查询版型图
     *  查询字典: 通过款版型、弹性两个字段做匹配对应图片; 字典编码: operate_model_picture, 字典编码规则为：版型值编码-弹性编码
     */
    private String getModePicture(DesignStyle designStyle) {
        Map<String, DictVo> dictValueMap = dictValueRemoteHelper.mapByDictCodes(List.of(DictConstant.OPERATE_MODEL_PICTURE));
        DictVo modePictureDict = dictValueMap.get(DictConstant.OPERATE_MODEL_PICTURE);
        String modePicture = null;
        if (Objects.nonNull(modePictureDict) && CollectionUtil.isNotEmpty(modePictureDict.getChildren())) {
            String modeCode = designStyle.getFitCode() + StrUtil.DASHED + designStyle.getElasticCode();
            if (StringUtils.isNotBlank(modeCode)) {
                Map<String, DictVo> partUseDictValueMap = StreamUtil.list2Map(modePictureDict.getChildren(), DictVo::getDictCode);
                modePicture = Optional.ofNullable(partUseDictValueMap.get(modeCode)).map(DictVo::getDictName).orElse(null);
            }
        }
        return modePicture;
    }

    @Override
    public void prePlacementMaterial2Zj(PrePlacementMaterialReq req) {
        log.info("=== 修改打版通知齐套预占位,req: {} ====", JSON.toJSONString(req));
        prototypeManageServiceImpl.zjPreOrderMaterial(req.getDesignCode(), req.getMakeClothesType(), req.getSampleType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clothesChange2Zj(ClothesChangeMqDto mqDto) {
        log.info("=== 打版变更处理,mqDto: {} ====", JSON.toJSONString(mqDto));
        Prototype prototype = prototypeRepository.getByDesignCode(mqDto.getDesignCode());
        SdpDesignException.notNull(prototype, "skc信息不存在!");
        zjDesignRemoteHelper.updatePrototypeClothesType(mqDto.getDesignCode(), mqDto.getMakeClothesType(), prototype.getBizChannel());
    }

    @Override
    public SkcBomSubmitVo listBomSubmitSkc(SkcBomSubmitReq req) {
        SkcBomSubmitVo skcBomSubmitVo = new SkcBomSubmitVo();
        List<Prototype> prototypeList = prototypeRepository.listBomSubmitSkc(req);
        if (CollUtil.isEmpty(prototypeList)) {
            return skcBomSubmitVo;
        }
        List<SkcBomSubmitVo.SkcInfo> skcInfoList = prototypeList.stream().map(item -> {
            SkcBomSubmitVo.SkcInfo skcInfo = new SkcBomSubmitVo.SkcInfo();
            BeanUtils.copyProperties(item, skcInfo);
            return skcInfo;
        }).toList();
        skcBomSubmitVo.setSkcInfoList(skcInfoList);

        return skcBomSubmitVo;
    }

    @Override
    public List<SkcBaseInnerVo> listBaseSkc(SkcBaseInnerReq req) {
        return prototypeRepository.listBaseSkc(req);
    }

    @Override
    public SkcBaseInnerVo normalBySpu(String styleCode) {
        Prototype normalSkc = prototypeRepository.normalSkc(styleCode);
        if (Objects.isNull(normalSkc)) {
            return null;
        }
        SkcBaseInnerVo innerVo = new SkcBaseInnerVo();
        BeanUtils.copyProperties(normalSkc, innerVo);
        return innerVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchPushSkc(List<SkcPriceReq> req) {
        if(CollUtil.isEmpty(req)){
            throw new SdpDesignException("推送商品运营平台失败: 入参为空");
        }
        log.info("=== 批量设计款推送商品运营,req: {} ===", JSON.toJSONString(req));
        List<CreateProductDto> createProductDtos = req.stream().map(a -> {
            String designCode = a.getDesignCode();
            // skc
            Prototype prototype = prototypeRepository.getByDesignCode(designCode);
            if (ObjectUtil.isNull(prototype)) {
                throw new SdpDesignException("skc【" + designCode + "】不存在");
            }
            PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId());
            String styleCode = prototype.getStyleCode();
            // spu
            DesignStyle designStyle = designStyleRepository.getByStyleCode(styleCode);
            Long designDemandId = designStyle.getDesignDemandId();

            DesignDemand designDemand = null;
            DesignDemandDetail demandDetail = null;
            if (ObjectUtil.isNotNull(designDemandId)) {
                // 灵感设计需求
                designDemand = designDemandRepository.getById(designDemandId);
                demandDetail = designDemandDetailRepository.getByDemandId(designDemandId);
            }

            //尺寸表EXCEL
            List<StyleSizeInfoVo> styleSizeInfoVos = sdpOrderHelper.listSizeInfoByStyleCode(List.of(designStyle.getStyleCode()));
            DictVo dictVoByCategory = null;
            if(CollectionUtil.isEmpty(styleSizeInfoVos)  || CollectionUtil.isEmpty(styleSizeInfoVos.getFirst().getSizeDetailList())) {
                //查询当前品类字典获取尺码明细表
                dictVoByCategory = dictValueRemoteHelper.getDictVoByCategory(designStyle.getCategory());
            }

            CreateProductDto productReq = DesignStyleConverter.convertProductCreateReq(designStyle,
                    prototype, designDemand, demandDetail, prototypeDetail, a.getLocalPrice(),dictVoByCategory,styleSizeInfoVos);

            //设置版型图(101), 类目图(201), 商品主图(301)_对应设计图, 尺码表(401)
            // this.setPopImageUrl(designStyle, productReq, prototypeDetail);

            //设计师/设计组
            productReq.setDesignName(prototype.getDesignerName());
            productReq.setDesignGroup(prototype.getDesignerGroup());
            return productReq;
        }).toList();

        //推送商品运营平台
        popProductHelper.noticeCreateSpotProductBatch(createProductDtos);

        return true;
    }

    @Override
    public List<SkcOnShelfImageVo> listOnShelfImg(SkcOnShelfImgInnerReq req) {
        List<String> designCodeList = req.getDesignCodeList();

        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
        if (CollUtil.isEmpty(prototypeList)) {
            return Collections.emptyList();
        }

        //spu上架图
        List<String> styleCodeList = StreamUtil.convertListAndDistinct(prototypeList, Prototype::getStyleCode);
        List<OnShelfSpu> onShelfSpuList = onShelfSpuRepository.listByStyleCodeList(styleCodeList);
        Map<String, OnShelfSpu> OnShelfSpuMap = StreamUtil.list2Map(onShelfSpuList, OnShelfSpu::getStyleCode);

        //skc上架图
        List<OnShelfSkc> onShelfSkcList = onShelfSkcRepository.listByDesignCodes(designCodeList);
        Map<String, OnShelfSkc> onShelfSkcMap = StreamUtil.list2Map(onShelfSkcList, OnShelfSkc::getDesignCode);

        return prototypeList.stream().map(item -> {
            SkcOnShelfImageVo imageVo = new SkcOnShelfImageVo();
            imageVo.setStyleCode(item.getStyleCode());
            imageVo.setDesignCode(item.getDesignCode());

            OnShelfSpu onShelfSpu = OnShelfSpuMap.get(item.getStyleCode());
            if (Objects.nonNull(onShelfSpu)) {
                imageVo.setSpuDetailImageList(onShelfSpu.getSpuDetailImageList());
            }

            OnShelfSkc onShelfSkc = onShelfSkcMap.get(item.getDesignCode());
            if (Objects.nonNull(onShelfSkc)) {
                imageVo.setSkcImageList(onShelfSkc.getSkcImageList());
            }
            return imageVo;
        }).toList();
    }

    @Override
    public List<PopCreateProductVo> queryStyleInfo4Pop(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        //spu
        List<DesignStyle> styleList = designStyleRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(styleList)) {
            return Collections.emptyList();
        }
        Map<String, DesignStyle> styleMap = StreamUtil.list2Map(styleList, DesignStyle::getStyleCode);

        //灵感设计需求
        Map<Long, DesignDemand> designDemandMap = new HashMap<>();
        Map<Long, DesignDemandDetail> designDemandDetailMap = new HashMap<>();
        List<Long> designDemandIdList = StreamUtil.convertListAndDistinct(styleList, DesignStyle::getDesignDemandId);
        if (CollUtil.isNotEmpty(designDemandIdList)) {
            designDemandMap = StreamUtil.list2Map(designDemandRepository.listByIds(designDemandIdList), DesignDemand::getDesignDemandId);
            designDemandDetailMap = StreamUtil.list2Map(designDemandDetailRepository.listByDesignDemandIds(designDemandIdList), DesignDemandDetail::getDesignDemandId);
        }
        //已提交skc
        List<Prototype> prototypeList = prototypeRepository.listByStyleCodes(styleCodeList, PrototypeStatusEnum.DECOMPOSED.getCode());
        if (CollUtil.isEmpty(styleList)) {
            return Collections.emptyList();
        }
        List<Long> prototypeIdList = StreamUtil.convertListAndDistinct(prototypeList, Prototype::getPrototypeId);
        List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIdList);
        Map<Long, PrototypeDetail> prototypeDetailMap = StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);

        //核价信息(预估核价与生产核价)
        Map<String, DesignPricingInfoVo> checkPriceMap = this.getCheckPriceMap(prototypeList);

        // 封装出参(skc维度数据)
        List<PopCreateProductVo> productVoList = new ArrayList<>(prototypeList.size());
        for (Prototype prototype : prototypeList) {
            DesignStyle designStyle = styleMap.get(prototype.getStyleCode());
            DesignDemand designDemand = designDemandMap.get(designStyle.getDesignDemandId());
            DesignDemandDetail demandDetail = designDemandDetailMap.get(designStyle.getDesignDemandId());
            PrototypeDetail prototypeDetail = prototypeDetailMap.get(prototype.getPrototypeId());
            BigDecimal localPrice = this.getLocalPrice(prototype, checkPriceMap);

            //尺寸表EXCEL
            List<StyleSizeInfoVo> styleSizeInfoVos = sdpOrderHelper.listSizeInfoByStyleCode(List.of(designStyle.getStyleCode()));
            DictVo dictVoByCategory = null;
            if(CollectionUtil.isEmpty(styleSizeInfoVos)  || CollectionUtil.isEmpty(styleSizeInfoVos.getFirst().getSizeDetailList())) {
                //查询当前品类字典获取尺码明细表
                dictVoByCategory = dictValueRemoteHelper.getDictVoByCategory(designStyle.getCategory());
            }

            CreateProductDto createProductDto = DesignStyleConverter.convertProductCreateReq(designStyle,
                    prototype, designDemand, demandDetail, prototypeDetail, localPrice,dictVoByCategory,styleSizeInfoVos);

            PopCreateProductVo productVo = PopCreateInfoConverter.buildPopCreateProductVo(createProductDto);
            productVo.setStyleType(SdpStyleTypeEnum.DESIGN.getCode());

            productVoList.add(productVo);
        }

        return productVoList;
    }

    private Map<String, DesignPricingInfoVo> getCheckPriceMap(List<Prototype> prototypeList) {
        List<DesignCodeSpuQuery.DesignCodeSpuReq> designCodeSpuList = prototypeList.stream().map(v->{
            DesignCodeSpuQuery.DesignCodeSpuReq designCodeSpuReq = new DesignCodeSpuQuery.DesignCodeSpuReq();
            designCodeSpuReq.setDesignCode(v.getDesignCode());
            designCodeSpuReq.setStyleCode(v.getStyleCode());
            return designCodeSpuReq;
        }).collect(Collectors.toList());
        List<DesignPricingInfoVo> checkPriceList = checkPriceRemoteHelper.findLastPricingBySkcBatch(designCodeSpuList);
        return StreamUtil.list2Map(checkPriceList, DesignPricingInfoVo::getDesignCode);
    }

    private BigDecimal getLocalPrice(Prototype prototype, Map<String, DesignPricingInfoVo> checkPriceMap) {
        DesignPricingInfoVo pricingInfo = checkPriceMap.get(prototype.getDesignCode());
        BigDecimal localPrice = null;
        if (Objects.nonNull(pricingInfo)) {
            if (Objects.nonNull(pricingInfo.getCheckPriceBaseInfo())) {
                localPrice = pricingInfo.getCheckPriceBaseInfo().getTotalCost();
            }else if (Objects.nonNull(pricingInfo.getEstimateCheckPriceVo())) {
                localPrice = pricingInfo.getEstimateCheckPriceVo().getTotalCost();
            }
        }
        return localPrice;
    }
}