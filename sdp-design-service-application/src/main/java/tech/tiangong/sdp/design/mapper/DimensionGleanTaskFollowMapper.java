package tech.tiangong.sdp.design.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import tech.tiangong.sdp.design.entity.DimensionGleanTaskFollow;
import tech.tiangong.sdp.design.vo.dimensiongleantask.DimensionGleanTaskPageQuery;
import tech.tiangong.sdp.design.vo.dto.dimensiongleantask.DimensionGleanTaskStateCountDto;
import tech.tiangong.sdp.design.vo.resp.dimensiongleantask.DimensionGleanTaskListVo;

import java.util.List;

/**
 * 3D采集任务跟踪（对应中台的3D采集任务）
 */

public interface DimensionGleanTaskFollowMapper extends BaseMapper<DimensionGleanTaskFollow> {

    /**
     * 按采集任务状态统计3D采集任务数量
     * @return
     */
    List<DimensionGleanTaskStateCountDto> countByGleanState();
    /**
     * 分页查询3D采集任务及采购信息
     * @param page
     * @param query
     * @return
     */
    IPage<DimensionGleanTaskListVo> queryByPage(@Param("page") Page<DimensionGleanTaskListVo> page, @Param("query") DimensionGleanTaskPageQuery query);
}