package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.yibuyun.framework.base.entity.BaseEntity;
import cn.yibuyun.framework.net.DataResponse;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yibuyun.scm.common.dto.accessories.request.SkuIdsReq;
import com.yibuyun.scm.common.dto.accessories.response.ProductSkuVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import com.yibuyun.scm.common.dto.fabric.response.CommoditySkuCollectionRespVo;
import com.yibuyun.scm.open.client.accessories.SCMAccessoriesSpuClient;
import com.yibuyun.scm.open.client.fabric.SCMFabricClient;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.clothes.client.CheckCountClient;
import tech.tiangong.sdp.clothes.vo.resp.SyncBomDosageAccountVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignRedisConstants;
import tech.tiangong.sdp.design.converter.BomOrderConverter;
import tech.tiangong.sdp.design.converter.BomOrderMaterialConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.BomMaterialStateEnum;
import tech.tiangong.sdp.design.enums.BomOrderStateEnum;
import tech.tiangong.sdp.design.enums.CraftDemandStateEnum;
import tech.tiangong.sdp.design.enums.MaterialDemandTypeEnum;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.BomDataHandleService;
import tech.tiangong.sdp.design.service.DesignRemarksService;
import tech.tiangong.sdp.design.vo.dto.bom.BomMaterialInfoDto;
import tech.tiangong.sdp.design.vo.dto.bom.SnapshotSkuDto;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;
import tech.tiangong.sdp.utils.Bool;
import tech.zj.quanbu.vega2.common.util.AesUtil;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

;

/**
 * bom数据处理 service
 * <AUTHOR>
 * @date 2022/11/27 21:13
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BomDataHandleServiceImpl implements BomDataHandleService {

    private final BomOrderRepository bomOrderRepository;
    private final BomOrderTransientRepository bomOrderTransientRepository;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final BomOrderMaterialTransientRepository bomOrderMaterialTransientRepository;
    private final SpecialAccessoriesRepository specialAccessoriesRepository;
    private final SpecialAccessoriesTransientRepository specialAccessoriesTransientRepository;
    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final CraftDemandInfoTransientRepository craftDemandInfoTransientRepository;
    private final DesignRemarksRepository designRemarksRepository;
    private final RedisTemplate redisTemplate;
    private final DesignRemarksService designRemarksService;
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final SCMFabricClient commoditySkuFeignClient;
    private final SCMAccessoriesSpuClient accessoriesSpuClient;
    private final CheckCountClient checkCountClient;
    private final PrototypeRepository prototypeRepository;

    @Value("${db.field.aes.key:9633663598ab09aaa1074c95acf10d24}")
    private String PLM_AES_KEY;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBomCodeByDesignCode(String designCode) {
        log.info("=========================> 数据清洗，保持每个skc只有一个设计款号 start");
        // 数据清洗，保持每个skc只有一个设计款号
        List<BomOrder> bomOrderList = bomOrderRepository.list(Wrappers.<BomOrder>lambdaQuery()
                .eq(BomOrder::getIsDeleted, cn.yibuyun.framework.enumeration.Bool.NO.getCode())
                .eq(StringUtils.isNotBlank(designCode), BomOrder::getDesignCode, designCode)
                .orderByAsc(BaseEntity::getCreatedTime));

        Map<String, List<BomOrder>> bomOrderMap = bomOrderList.stream()
                .collect(Collectors.groupingBy(BomOrder::getDesignCode));
        if (ObjectUtil.isEmpty(bomOrderMap)) {
            return;
        }
        Set<String> designCodeList = bomOrderMap.keySet();
        List<BomOrder> bomOrderCopieList = new ArrayList<>();
        for (String code : designCodeList) {
            List<BomOrder> bomOrderCopies = bomOrderMap.get(code);
            List<BomOrder> bomOrderCopies1 = listSort(bomOrderCopies);
            BomOrder bomOrder1 = bomOrderCopies1.get(0);// 创建时间最早的（最久的）
            BomOrder bomOrder2 = bomOrderCopies1.get(bomOrderCopies1.size() - 1);// 最新的一条
            int versionNum = 1;

            for (BomOrder bomOrder : bomOrderCopies1) {
                if (BomOrderStateEnum.WAIT_SUBMIT.getCode().equals(bomOrder.getState())
                        && !bomOrder2.equals(bomOrder)) {
                    bomOrder.setState(BomOrderStateEnum.CLOSED.getCode());
                }
                bomOrder.setBomCode(bomOrder1.getBomCode());
                bomOrder.setVersionNum(versionNum++);
                bomOrderCopieList.add(bomOrder);
            }
        }
        if (CollectionUtil.isNotEmpty(bomOrderCopieList)) {
            bomOrderRepository.updateBatchById(bomOrderCopieList);
        }
        log.info("=========================> 数据清洗，保持每个skc只有一个设计款号 end");

    }

    public List<BomOrder> listSort(List<BomOrder> bomOrderCopies) {
        Collections.sort(bomOrderCopies, new Comparator<BomOrder>() {
            @SneakyThrows
            @Override
            public int compare(BomOrder o1, BomOrder o2) {
                if (o1.getCreatedTime().isAfter(o2.getCreatedTime())) {
                    return 1;
                } else {
                    return -1;
                }
            }
        });
        return bomOrderCopies;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void purgeBomMaterialDosageAccountUnitData(String designCode) {
        List<BomOrderMaterial> bomOrderMaterialList = new ArrayList<>();
        if (StringUtils.isNotBlank(designCode)) {
            List<BomOrder> bomOrderList = bomOrderRepository.list(new QueryWrapper<BomOrder>().lambda().eq(BomOrder::getDesignCode, designCode));
            if (CollectionUtil.isNotEmpty(bomOrderList)) {
                List<Long> bomIds = bomOrderList.stream().map(BomOrder::getBomId).collect(Collectors.toList());
                bomOrderMaterialList = bomOrderMaterialRepository.list(new QueryWrapper<BomOrderMaterial>().lambda()
                        .eq(BomOrderMaterial::getIsTransient, cn.yibuyun.framework.enumeration.Bool.NO.getCode()).in(BomOrderMaterial::getBomId, bomIds)
                        .isNull(BomOrderMaterial::getDosageAccountUnit));
            }
        } else {
            bomOrderMaterialList = bomOrderMaterialRepository.list(new QueryWrapper<BomOrderMaterial>().lambda()
                    .eq(BomOrderMaterial::getIsTransient, cn.yibuyun.framework.enumeration.Bool.NO.getCode()).isNull(BomOrderMaterial::getDosageAccountUnit));
        }

        if (CollectionUtil.isEmpty(bomOrderMaterialList)) {
            log.info("清洗Bom物料清单数据,缺少履约用量核算单位 无满足条件BOM物料清单数据");
            return;
        }

        List<Long> bomIds = bomOrderMaterialList.stream().map(BomOrderMaterial::getBomId).distinct().collect(Collectors.toList());
        log.info("【批量获取bom当前已核算的用量信息】调用打版服务 请求参数 bomIds:{}", bomIds);
        DataResponse<Map<Long, SyncBomDosageAccountVo>> dataResponse = checkCountClient.getBomCurrentCheckedCount(bomIds);
        log.info("【批量获取bom当前已核算的用量信息】调用打版服务 响应结果 {}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            throw new SdpDesignException("批量获取bom当前已核算的用量信息 调用打版服务 异常 " + dataResponse.getMessage());
        }
        if (CollectionUtil.isEmpty(dataResponse.getData())) {
            log.info("批量获取bom当前已核算的用量信息 为空,不处理");
            return;
        }

        Map<Long, String> dosageAccountVoMap = new HashMap<>(1024);
        for (Map.Entry<Long, SyncBomDosageAccountVo> entry : dataResponse.getData().entrySet()) {
            entry.getValue().getMaterialDosageAccounts().forEach(dosage -> {
                dosageAccountVoMap.put(dosage.getBomMaterialId(), dosage.getDosageAccountUnit());
            });
        }

        //处理更新数据
        List<BomOrderMaterial> updateBomMaterialList = bomOrderMaterialList.stream().map(bomOrderMaterial -> {
            BomOrderMaterial updateMaterial = new BomOrderMaterial();
            updateMaterial.setBomMaterialId(bomOrderMaterial.getBomMaterialId());
            updateMaterial.setDosageAccountUnit(Optional.ofNullable(dosageAccountVoMap.get(bomOrderMaterial.getBomMaterialId())).orElse(null));
            return updateMaterial;
        }).collect(Collectors.toList());

        for (List<BomOrderMaterial> materialList : CollectionUtil.split(updateBomMaterialList, 200)) {
            bomOrderMaterialRepository.updateBatchById(materialList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void encryptCraftSensitiveInfo() {

        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.list();
        CollectionUtil.split(craftDemandInfoList, 200).forEach(splitCraft -> {

            List<CraftDemandInfo> updateCrafts = splitCraft.stream().map(craft -> {
                CraftDemandInfo updateCraft = CraftDemandInfo.builder().craftDemandId(craft.getCraftDemandId()).build();
                if (StringUtils.isNotBlank(craft.getContactPhone())) {
                    try {
                        updateCraft.setContactPhone(AesUtil.encrypt(craft.getContactPhone(), PLM_AES_KEY));
                    } catch (Exception e) {
                        log.error("craftDemandId:{} 手动加密工厂联系电话 {} 异常", craft.getCraftDemandId(), craft.getContactPhone(), e);
                    }
                }

                if (StringUtils.isNotBlank(craft.getContactDetailAddress())) {
                    try {
                        updateCraft.setContactDetailAddress(AesUtil.encrypt(craft.getContactDetailAddress(), PLM_AES_KEY));
                    } catch (Exception e) {
                        log.error("craftDemandId:{} 手动加密工厂详情地址 {} 异常", craft.getCraftDemandId(), craft.getContactDetailAddress(), e);
                    }
                }
                return updateCraft;
            }).collect(Collectors.toList());

            craftDemandInfoRepository.updateBatchById(updateCrafts);
        });


    }


    /**
     * v3.5.1BOM暂存功能描述:
     *  bom单更新暂存状态: transientState=1; 记录暂存次数; 新增暂存bom: bom_order_transient;
     *  面辅料: 在原表上新增记录, 标记为暂存
     *  特辅: 在原表上新增记录, 标记为暂存
     *  工艺需求: 在原表上新增记录, 标记为暂存
     *  物料备注: 记录在原表,标记为暂存
     *  删除的面辅料,特辅: id记录到redis中;
     *  删除的工艺id: id记录到redis中;
     *
     * v3.11暂存功能调整:
     *  bom物料,工艺需求,特辅表,需求表(新增) 各创建一个暂存表;
     *  将暂存的物料,工艺需求,特辅, 需求信息维护到暂存表中; 物料快照还是在一个表;
     *  注: 待提交的首次暂存,复制原物料到暂存表中,主键使用原表主键;(因为bom提交后不升版本),再次暂存更新即可;
     *
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveTransientData(String designCode) {
        //查询所有v3.5.1暂存的bom
        BomOrder bomOrder = bomOrderRepository.getLatestBomByDesignCode(designCode);
        SdpDesignException.notNull(bomOrder, "bom单不存在!");
        if (!Objects.equals(bomOrder.getTransientState(), Bool.YES.getCode())) {
            throw new SdpDesignException( designCode + "下最新bom单不是暂存状态, 无需处理! ");
        }
        Long bomId = bomOrder.getBomId();
        //暂存bom
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomId);
        Long bomTransientId = transientBom.getBomTransientId();
        SdpDesignException.notNull(transientBom, "{}对应的暂存bom单不存在! ", designCode);

        //暂存的物料
        List<BomOrderMaterial> transientMaterialList = bomOrderMaterialRepository.listTransientByBomId(bomId);
        SdpDesignException.notEmpty(transientMaterialList, "{} 下bom单无暂存物料, 无需处理! ", designCode);
        List<Long> transientMaterialIdList = transientMaterialList.stream().map(BomOrderMaterial::getBomMaterialId).collect(Collectors.toList());

        //暂存的特辅
        List<SpecialAccessories> transientSpecialAccessoriesList = specialAccessoriesRepository.listByBomIdAndTransient(bomId, cn.yibuyun.framework.enumeration.Bool.YES.getCode());

        //暂存二次工艺
        List<CraftDemandInfo> transientCraftList = craftDemandInfoRepository.getTransientCraftList(transientMaterialIdList);

        //暂存物料备注
        List<DesignRemarks> transientRemarkList = designRemarksRepository.getListByBizIdAndTransientState(bomId, Bool.YES.getCode());
        Map<Long, List<DesignRemarks>> transientRemarkMap = transientRemarkList.stream()
                .filter(remark -> Objects.nonNull(remark.getBizChildId()))
                .collect(Collectors.groupingBy(DesignRemarks::getBizChildId));

        //获取暂存删除的二次工艺id
        Set<Long> finalDelCraftIdSet = this.getBomSetByKey(DesignRedisConstants.getBomCraftDelKey(bomId));
        //查询暂存删除的物料
        Set<Long> finalDelMaterialIdSet = this.getBomSetByKey(DesignRedisConstants.getBomMaterialDelKey(bomId));

        Map<Long, Long> oldNewTransientMaterialIdMap = new HashMap<>();
        boolean waitSubmitBom = Objects.equals(bomOrder.getState(), BomOrderStateEnum.WAIT_SUBMIT.getCode());

        //暂存物料拷贝到暂存表中
        this.moveTransientMaterial(bomId, bomTransientId, transientMaterialList, finalDelMaterialIdSet, oldNewTransientMaterialIdMap, waitSubmitBom, transientRemarkMap);

        //暂存特辅拷贝到暂存表中
        this.moveTransientSpecial(bomId, bomTransientId, transientSpecialAccessoriesList, finalDelMaterialIdSet, waitSubmitBom, transientRemarkMap);

        //暂存工艺需求拷贝到暂存表
        this.moveTransientCraft(bomId, bomTransientId, transientCraftList, finalDelCraftIdSet, oldNewTransientMaterialIdMap, waitSubmitBom);

        //逻辑删除原表中的暂存物料,工艺,特辅; 清空redis删除的物料与工艺id缓存
        this.logicDelTransientInfo(bomId, transientMaterialList, transientSpecialAccessoriesList, transientCraftList);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanTransientData(String designCode) {
        //查询所有v3.5.1暂存的bom
        BomOrder bomOrder = bomOrderRepository.getLatestBomByDesignCode(designCode);
        if (!Objects.equals(bomOrder.getTransientState(), Bool.YES.getCode())) {
            throw new SdpDesignException( designCode + "下最新bom单不是暂存状态, 无需处理! ");
        }
        Long bomId = bomOrder.getBomId();
        //暂存bom
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomId);
        Long bomTransientId = transientBom.getBomTransientId();
        SdpDesignException.notNull(transientBom, "{}对应的暂存bom单不存在! ", designCode);

        //暂存的物料
        List<BomOrderMaterial> transientMaterialList = bomOrderMaterialRepository.listTransientByBomId(bomId);
        SdpDesignException.notEmpty(transientMaterialList, "{} 下bom单无暂存物料, 无需处理! ", designCode);
        List<Long> transientMaterialIdList = transientMaterialList.stream().map(BomOrderMaterial::getBomMaterialId).collect(Collectors.toList());

        //暂存的特辅
        List<SpecialAccessories> transientSpecialAccessoriesList = specialAccessoriesRepository.listByBomIdAndTransient(bomId, cn.yibuyun.framework.enumeration.Bool.YES.getCode());

        //暂存二次工艺
        List<CraftDemandInfo> transientCraftList = craftDemandInfoRepository.getTransientCraftList(transientMaterialIdList);

        //暂存物料备注
        List<DesignRemarks> transientRemarkList = designRemarksRepository.getListByBizIdAndTransientState(bomId, Bool.YES.getCode());

        //逻辑删除原表中的暂存物料,工艺,特辅; 清空redis删除的物料与工艺id缓存
        this.logicDelTransientInfo(bomId, transientMaterialList, transientSpecialAccessoriesList, transientCraftList);

        //清除bom单暂存状态
        BomOrder updateBom = new BomOrder();
        updateBom.setBomId(bomId);
        updateBom.setTransientState(Bool.NO.getCode());
        bomOrderRepository.updateById(updateBom);

        bomOrderTransientRepository.removeById(bomTransientId);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateMaterialType() {
        return bomOrderMaterialRepository.updateMaterialType();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseCycleFabric() {
        log.info("=== 面料采购周期信息同步 start ===");
        StopWatch sw = new StopWatch();
        sw.start("面料采购周期信息同步");
        this.updatePurchaseCycle(MaterialDemandTypeEnum.FABRIC);
        sw.stop();
        log.info("=== 面料采购周期信息同步 end 耗时:{}s; ===", sw.getTotalTimeSeconds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseCycleAccessories() {
        log.info("=== 辅料采购周期信息同步 start ===");
        StopWatch sw = new StopWatch();
        sw.start("辅料采购周期信息同步");
        this.updatePurchaseCycle(MaterialDemandTypeEnum.ACCESSORIES);
        sw.stop();
        log.info("=== 辅料采购周期信息同步 end 耗时:{}s; ===", sw.getTotalTimeSeconds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseCycleSpecial() {
        log.info("=== 特辅采购周期信息同步 start ===");
        StopWatch sw = new StopWatch();
        sw.start("特辅采购周期信息同步");
        this.updateSpecialPurchaseCycle();
        sw.stop();
        log.info("=== 特辅采购周期信息同步 end 耗时:{}s; ===", sw.getTotalTimeSeconds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseCycleTransient() {
        log.info("=== 暂存物料采购周期信息同步 start ===");
        StopWatch sw = new StopWatch();
        //暂存面辅料的采购周期信息同步
        sw.start("暂存面辅料采购周期信息同步");
        this.updateTransientMaterial();
        sw.stop();

        //暂存特辅的采购周期信息同步
        sw.start("暂存特辅采购周期信息同步");
        this.updateTransientSpecialPurchase();
        sw.stop();
        log.info("=== 暂存物料采购周期信息同步 end 耗时:{}s; ===", sw.getTotalTimeSeconds());
    }

    private void updateTransientSpecialPurchase() {
        List<SpecialAccessoriesTransient> specialTransientList = specialAccessoriesTransientRepository.list();
        if (CollUtil.isEmpty(specialTransientList)) {
            return;
        }
        Set<Long> skuIdSet = specialTransientList.stream().map(SpecialAccessoriesTransient::getSkuId).collect(Collectors.toSet());
        BomMaterialInfoDto materialInfoDto = this.queryMaterialInfoBySkuId(skuIdSet, MaterialDemandTypeEnum.ACCESSORIES);
        if (Objects.isNull(materialInfoDto)) {
            return;
        }
        if (CollUtil.isEmpty(materialInfoDto.getAccessoriesSkuMap())) {
            return;
        }
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        List<SpecialAccessoriesTransient> updateSpecialList = new LinkedList<>();
        specialTransientList.forEach(item -> {
            ProductSkuVo productSkuVo = accessoriesSkuMap.get(item.getSkuId());
            if (Objects.isNull(productSkuVo)) {
                return;
            }
            //样衣与大货采购周期都未空, 不更新(plm历史数据都是空的,不用更新)
            if (Objects.isNull(productSkuVo.getSamplePurchasingCycle()) && Objects.isNull(productSkuVo.getBulkPurchasingCycle())) {
                return;
            }
            SpecialAccessoriesTransient updateSpecial = new SpecialAccessoriesTransient();
            updateSpecial.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
            updateSpecial.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
            updateSpecial.setSpecialAccessoriesId(item.getSpecialAccessoriesId());
            updateSpecialList.add(updateSpecial);
        });
        if (CollUtil.isNotEmpty(updateSpecialList)) {
            updateSpecialList.forEach(item -> {
                specialAccessoriesTransientRepository.lambdaUpdate()
                        .set(SpecialAccessoriesTransient::getSamplePurchasingCycle, item.getSamplePurchasingCycle())
                        .set(SpecialAccessoriesTransient::getBulkPurchasingCycle, item.getBulkPurchasingCycle())
                        .eq(SpecialAccessoriesTransient::getSpecialAccessoriesId, item.getSpecialAccessoriesId())
                        .update();
            });
        }
    }

    private void updateTransientMaterial() {
        //物料暂存表,查询有snapshotId的物料
        List<BomOrderMaterialTransient> transientList = bomOrderMaterialTransientRepository.list().stream()
                .filter(item -> Objects.nonNull(item.getMaterialSnapshotId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(transientList)) {
            return;
        }
        List<Long> materialSnapshotIdList = transientList.stream()
                .map(BomOrderMaterialTransient::getMaterialSnapshotId).collect(Collectors.toList());
        List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(materialSnapshotIdList);
        Map<Long, MaterialSnapshot> snapshotMap = snapshotList.stream()
                .collect(Collectors.toMap(MaterialSnapshot::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));

        Set<Long> fabricSkuIdSet = snapshotList.stream().filter(item -> Objects.equals(MaterialDemandTypeEnum.FABRIC.getCode(), item.getMaterialType()))
                .map(MaterialSnapshot::getSkuId).collect(Collectors.toSet());
        List<BomOrderMaterialTransient> updateList = new LinkedList<>();
        if (CollUtil.isNotEmpty(fabricSkuIdSet)) {
            BomMaterialInfoDto materialInfoDto = this.queryMaterialInfoBySkuId(fabricSkuIdSet, MaterialDemandTypeEnum.FABRIC);
            if (Objects.isNull(materialInfoDto)) {
                return;
            }
            if (CollUtil.isEmpty(materialInfoDto.getFabricSkuMap())) {
                return;
            }

            Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = materialInfoDto.getFabricSkuMap();
            transientList.forEach(item -> {
                MaterialSnapshot snapshot = snapshotMap.get(item.getMaterialSnapshotId());
                if (Objects.isNull(snapshot)) {
                    return;
                }
                if (!Objects.equals(MaterialDemandTypeEnum.FABRIC.getCode(), snapshot.getMaterialType())) {
                    return;
                }
                CommoditySkuCollectionRespVo.Sku fabricSkuVo = fabricSkuMap.get(snapshot.getSpuSkuId());
                if (Objects.isNull(fabricSkuVo)) {
                    return;
                }
                //历史数据都是空的,不用更新
                if (Objects.isNull(fabricSkuVo.getSamplePurchasingCycle()) && Objects.isNull(fabricSkuVo.getBulkPurchasingCycle())) {
                    return;
                }
                BomOrderMaterialTransient updateTransient = new BomOrderMaterialTransient();
                updateTransient.setBomMaterialId(item.getBomMaterialId());
                updateTransient.setSamplePurchasingCycle(fabricSkuVo.getSamplePurchasingCycle());
                updateTransient.setBulkPurchasingCycle(fabricSkuVo.getBulkPurchasingCycle());
                updateList.add(updateTransient);
            });
        }
        Set<Long> accessoriesSkuIdSet = snapshotList.stream().filter(item -> Objects.equals(MaterialDemandTypeEnum.ACCESSORIES.getCode(), item.getMaterialType()))
                .map(MaterialSnapshot::getSkuId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(accessoriesSkuIdSet)) {
            BomMaterialInfoDto materialInfoDto = this.queryMaterialInfoBySkuId(accessoriesSkuIdSet, MaterialDemandTypeEnum.ACCESSORIES);
            if (Objects.isNull(materialInfoDto)) {
                return;
            }
            if (CollUtil.isEmpty(materialInfoDto.getAccessoriesSkuMap())) {
                return;
            }
            Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
            transientList.forEach(item -> {
                MaterialSnapshot snapshot = snapshotMap.get(item.getMaterialSnapshotId());
                if (Objects.isNull(snapshot)) {
                    return;
                }
                if (!Objects.equals(MaterialDemandTypeEnum.ACCESSORIES.getCode(), snapshot.getMaterialType())) {
                    return;
                }
                ProductSkuVo productSkuVo = accessoriesSkuMap.get(snapshot.getSkuId());
                if (Objects.isNull(productSkuVo)) {
                    return;
                }
                //历史数据都是空的,不用更新
                if (Objects.isNull(productSkuVo.getSamplePurchasingCycle()) && Objects.isNull(productSkuVo.getBulkPurchasingCycle())) {
                    return;
                }
                BomOrderMaterialTransient updateTransient = new BomOrderMaterialTransient();
                updateTransient.setBomMaterialId(item.getBomMaterialId());
                updateTransient.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
                updateTransient.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
                updateList.add(updateTransient);
            });
        }
        if (CollUtil.isNotEmpty(updateList)) {
            updateList.forEach(item -> {
                bomOrderMaterialTransientRepository.lambdaUpdate()
                        .set(BomOrderMaterialTransient::getSamplePurchasingCycle, item.getSamplePurchasingCycle())
                        .set(BomOrderMaterialTransient::getBulkPurchasingCycle, item.getBulkPurchasingCycle())
                        .eq(BomOrderMaterialTransient::getBomMaterialId, item.getBomMaterialId())
                        .update();
            });
        }
    }

    private void updateSpecialPurchaseCycle() {
        //特殊辅料采购周期信息同步
        List<Long> allSkuIdList = specialAccessoriesRepository.getAllSkuId();
        if (CollUtil.isEmpty(allSkuIdList)) {
            return;
        }
        //分批次查询面料采购周期信息
        int handleSize = 500;
        CollectionUtil.split(allSkuIdList, handleSize).forEach(skuIdList -> {
            Set<Long> skuIdSet = new HashSet<>(skuIdList);
            BomMaterialInfoDto materialInfoDto = this.queryMaterialInfoBySkuId(skuIdSet, MaterialDemandTypeEnum.ACCESSORIES);
            if (Objects.isNull(materialInfoDto)) {
                return;
            }
            if (CollUtil.isEmpty(materialInfoDto.getAccessoriesSkuMap())) {
                return;
            }
            List<SpecialAccessories> updateList = new LinkedList<>();
            Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
            for (Long item : skuIdList) {
                ProductSkuVo productSkuVo = accessoriesSkuMap.get(item);
                if (Objects.isNull(productSkuVo)) {
                    continue;
                }
                //样衣与大货采购周期都未空, 不更新(plm的历史数据都是空的)
                if (Objects.isNull(productSkuVo.getSamplePurchasingCycle()) && Objects.isNull(productSkuVo.getBulkPurchasingCycle())) {
                    continue;
                }
                SpecialAccessories updateSpecial = new SpecialAccessories();
                updateSpecial.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
                updateSpecial.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
                updateSpecial.setSkuId(item);
                updateList.add(updateSpecial);
            }
            if (CollUtil.isNotEmpty(updateList)) {
                specialAccessoriesRepository.batchUpdatePurchaseCycle(updateList);
            }
            updateList = null;
        });
    }

    public BomMaterialInfoDto queryMaterialInfoBySkuId(Set<Long>  skuIdSet, MaterialDemandTypeEnum demandTypeEnum) {
        BomMaterialInfoDto materialInfoDto = new BomMaterialInfoDto();
        if (CollUtil.isEmpty(skuIdSet) || Objects.isNull(demandTypeEnum)) {
            return materialInfoDto;
        }
        if (Objects.equals(demandTypeEnum, MaterialDemandTypeEnum.FABRIC)) {
            //面料商品
            List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = this.getFabricSkuInfo(skuIdSet);
            Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = fabricSkuInfos.stream()
                    .collect(Collectors.toMap(BomOrderConverter::getLyFabricSpuSkuId, Function.identity(), (k1, k2) -> k1));
            materialInfoDto.setFabricSkuMap(fabricSkuMap);
            return materialInfoDto;
        }
        if (Objects.equals(demandTypeEnum, MaterialDemandTypeEnum.ACCESSORIES)) {
            //辅料商品
            List<ProductSpuInfoVo> accessoriesSkuInfos = this.getAccessoriesSkuInfo(skuIdSet);
            Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus).flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
            Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream().collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
            materialInfoDto.setAccessoriesSkuMap(accessoriesSkuMap).setAccessoriesSpuMap(accessoriesSpuMap);
        }
        return materialInfoDto;
    }

    public List<CommoditySkuCollectionRespVo.Sku> getFabricSkuInfo(Set<Long> skuIds) {
        if (CollectionUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        DataResponse<CommoditySkuCollectionRespVo> dataResponse = commoditySkuFeignClient.getSkuBySkuIds(new ArrayList<>(skuIds));
        if (!dataResponse.isSuccessful()) {
            throw new SdpDesignException(" 【获取面料sku信息】调用商品服务 失败 " + dataResponse.getMessage());
        }
        return dataResponse.getData().getSkuList();
    }

    public List<ProductSpuInfoVo> getAccessoriesSkuInfo(Set<Long> skuIds) {
        if (CollectionUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        SkuIdsReq skuIdsReq = new SkuIdsReq();
        skuIdsReq.setSkuIds(skuIds);
        DataResponse<List<ProductSpuInfoVo>> dataResponse = accessoriesSpuClient.detailSku(skuIdsReq);
        if (!dataResponse.isSuccessful()) {
            throw new SdpDesignException(" 【获取辅料sku信息】调用商品服务 失败 " + dataResponse.getMessage());
        }
        return dataResponse.getData();
    }

    private void updatePurchaseCycle(MaterialDemandTypeEnum materialDemandTypeEnum) {
        //先查询未更新过的面料的skuId与snapshotId
        List<SnapshotSkuDto> snapshotSkuDtoList = materialSnapshotRepository.listSnapshotId(materialDemandTypeEnum.getCode());
        if (CollUtil.isEmpty(snapshotSkuDtoList)) {
            return;
        }
        //分批次查询面料采购周期信息
        int handleSize = 500;
        CollectionUtil.split(snapshotSkuDtoList, handleSize).forEach(skuList -> {
            Set<Long> skuIdSet = skuList.stream().map(SnapshotSkuDto::getSkuId).collect(Collectors.toSet());
            BomMaterialInfoDto materialInfoDto = this.queryMaterialInfoBySkuId(skuIdSet, materialDemandTypeEnum);
            if (Objects.isNull(materialInfoDto)) {
                return;
            }
            //面料
            List<BomOrderMaterial> updateList = new LinkedList<>();
            if (Objects.equals(materialDemandTypeEnum, MaterialDemandTypeEnum.FABRIC)) {
                if (CollUtil.isEmpty(materialInfoDto.getFabricSkuMap())) {
                    return;
                }
                Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = materialInfoDto.getFabricSkuMap();
                for (SnapshotSkuDto item : skuList) {
                    CommoditySkuCollectionRespVo.Sku skuVo = fabricSkuMap.get(item.getSpuSkuId());
                    if (Objects.isNull(skuVo)) {
                        continue;
                    }
                    // 样衣与大货采购周期都未空, 不更新(plm的历史数据都是空的)
                    if (Objects.isNull(skuVo.getSamplePurchasingCycle()) && Objects.isNull(skuVo.getBulkPurchasingCycle())) {
                        continue;
                    }

                    BomOrderMaterial updateMaterial = new BomOrderMaterial();
                    updateMaterial.setSamplePurchasingCycle(skuVo.getSamplePurchasingCycle());
                    updateMaterial.setBulkPurchasingCycle(skuVo.getBulkPurchasingCycle());
                    updateMaterial.setMaterialSnapshotId(item.getMaterialSnapshotId());
                    updateList.add(updateMaterial);
                }
            }
            //辅料
            else if (Objects.equals(materialDemandTypeEnum, MaterialDemandTypeEnum.ACCESSORIES)) {
                if (CollUtil.isEmpty(materialInfoDto.getAccessoriesSkuMap())) {
                    return;
                }
                Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
                for (SnapshotSkuDto item : skuList) {
                    ProductSkuVo productSkuVo = accessoriesSkuMap.get(item.getSkuId());
                    if (Objects.isNull(productSkuVo)) {
                        continue;
                    }
                    //样衣与大货采购周期都未空, 不更新(plm的历史数据都是空的)
                    if (Objects.isNull(productSkuVo.getSamplePurchasingCycle()) && Objects.isNull(productSkuVo.getBulkPurchasingCycle())) {
                        continue;
                    }
                    BomOrderMaterial updateMaterial = new BomOrderMaterial();
                    updateMaterial.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
                    updateMaterial.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
                    updateMaterial.setMaterialSnapshotId(item.getMaterialSnapshotId());
                    updateList.add(updateMaterial);
                }
            }

            //根据materialSnapshotId批量更新bom物料的采购周期信息
            if (CollUtil.isNotEmpty(updateList)) {
                bomOrderMaterialRepository.batchUpdatePurchaseCycle(updateList);
            }
            updateList = null;
        });
    }

    private void logicDelTransientInfo(Long bomId, List<BomOrderMaterial> transientMaterialList, List<SpecialAccessories> transientSpecialAccessoriesList, List<CraftDemandInfo> transientCraftList) {
        // 逻辑删除暂存的物料
        List<Long> materialIdDelList = transientMaterialList.stream().map(BomOrderMaterial::getBomMaterialId).collect(Collectors.toList());
        bomOrderMaterialRepository.removeByIds(materialIdDelList);
        //逻辑删除暂存的特辅
        List<Long> specialDelIdList = transientSpecialAccessoriesList.stream().map(SpecialAccessories::getSpecialAccessoriesId).collect(Collectors.toList());
        specialAccessoriesRepository.removeByIds(specialDelIdList);
        //逻辑删除暂存的工艺
        List<Long> craftDelIdList = transientCraftList.stream().map(CraftDemandInfo::getCraftDemandId).collect(Collectors.toList());
        craftDemandInfoRepository.removeByIds(craftDelIdList);

        String bomMaterialKey = DesignRedisConstants.getBomMaterialDelKey(bomId);
        String bomCraftKey = DesignRedisConstants.getBomCraftDelKey(bomId);
        redisTemplate.delete(bomMaterialKey);
        redisTemplate.delete(bomCraftKey);
    }

    private void moveTransientCraft(Long bomId, Long bomTransientId, List<CraftDemandInfo> transientCraftList, Set<Long> finalDelCraftIdSet, Map<Long, Long> oldNewTransientMaterialIdMap, boolean waitSubmitBom) {
        //原有工艺
        Map<Long, CraftDemandInfo> oldCraftMap = craftDemandInfoRepository.getListByBomIdsAndState(List.of(bomId),
                CraftDemandStateEnum.SUBMIT.getCode()).stream()
                .collect(Collectors.toMap(CraftDemandInfo::getThirdPartyCraftDemandId, Function.identity(), (k1, k2) -> k1));

        List<CraftDemandInfoTransient> transientCraftAddList = new LinkedList<>();

        //旧暂存工艺迁移到新暂存物料表中
        transientCraftList.forEach(item -> {
            CraftDemandInfoTransient newTransient = new CraftDemandInfoTransient();
            BeanUtils.copyProperties(item, newTransient);
            newTransient.setBomTransientId(bomTransientId);
            //暂存的是原有工艺,维护originMaterialId
            Long newTransientCraftId = null;
            CraftDemandInfo oldCraft = oldCraftMap.get(item.getThirdPartyCraftDemandId());
            if (Objects.nonNull(oldCraft)) {
                newTransient.setOriginCraftDemandId(oldCraft.getCraftDemandId());
                //若是待提交的暂存, 使用原物料id做主键
                if (waitSubmitBom) {
                    newTransientCraftId = oldCraft.getCraftDemandId();
                }
            }
            newTransientCraftId = Objects.isNull(newTransientCraftId) ? IdPool.getId() : newTransientCraftId;
            newTransient.setCraftDemandId(newTransientCraftId);
            //关联新暂存物料id
            newTransient.setBomMaterialId(oldNewTransientMaterialIdMap.get(item.getBomMaterialId()));
            transientCraftAddList.add(newTransient);

        });

        //暂存删除的原有物料迁移到新的暂存物料表中
        if (CollUtil.isNotEmpty(finalDelCraftIdSet)) {
            craftDemandInfoRepository.listByIds(finalDelCraftIdSet).forEach(item -> {
                CraftDemandInfoTransient newTransient = new CraftDemandInfoTransient();
                BeanUtils.copyProperties(item, newTransient);
                newTransient.setBomTransientId(bomTransientId);
                //删除的原工艺,标记关闭
                newTransient.setState(BomMaterialStateEnum.CLOSED.getCode());
                //暂存的是原有物料,维护originMaterialId
                newTransient.setOriginCraftDemandId(item.getCraftDemandId());
                //若是待提交的暂存, 使用原物料id做主键
                Long newTransientCraftId = null;
                if (waitSubmitBom) {
                    newTransientCraftId = item.getCraftDemandId();
                } else {
                    newTransientCraftId = IdPool.getId();
                }
                newTransient.setCraftDemandId(newTransientCraftId);
                //关联新暂存物料id
                newTransient.setBomMaterialId(oldNewTransientMaterialIdMap.get(item.getBomMaterialId()));
                transientCraftAddList.add(newTransient);
            });

        }

        //保存到新的暂存表中
        craftDemandInfoTransientRepository.saveBatch(transientCraftAddList);
    }

    private void moveTransientSpecial(Long bomId,
                                      Long bomTransientId,
                                      List<SpecialAccessories> transientSpecialAccessoriesList,
                                      Set<Long> finalDelMaterialIdSet,
                                      boolean waitSubmitBom,
                                      Map<Long, List<DesignRemarks>> transientRemarkMap) {
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        List<DesignRemarks> updateRemarkList = new LinkedList<>();

        //引用的时候,会存在暂存的特辅(要么使用暂存特辅(会把原有特辅删除缓存到redis中),要么使用原有特辅)

        List<SpecialAccessoriesTransient> transientSpecialAddList = new LinkedList<>();

        //旧暂存物料迁移到新暂存物料表中
        transientSpecialAccessoriesList.forEach(item -> {
            //暂存的特辅都是暂存时新增的特辅
            SpecialAccessoriesTransient newTransient = new SpecialAccessoriesTransient();
            BeanUtils.copyProperties(item, newTransient);
            newTransient.setBomTransientId(bomTransientId);
            //若是待提交的暂存, 使用原物料id做主键
            Long newTransientSpecialId = IdPool.getId();
            newTransient.setSpecialAccessoriesId(newTransientSpecialId);
            transientSpecialAddList.add(newTransient);

            //物料备注
            List<DesignRemarks> remarks = transientRemarkMap.get(item.getSpecialAccessoriesId());
            if (CollUtil.isNotEmpty(remarks)) {
                if (waitSubmitBom) {
                    //去重备注的暂存状态
                    remarks.forEach(rk -> {
                        DesignRemarks updateRemark = new DesignRemarks();
                        updateRemark.setDesignRemarksId(rk.getDesignRemarksId());
                        updateRemark.setTransientState(Bool.NO.getCode());
                        updateRemarkList.add(updateRemark);
                    });
                }else {
                    remarks.forEach(remark -> {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, newTransientSpecialId, remark.getRemark()));
                    });
                }
            }
        });

        //暂存删除的原有物料迁移到新的暂存物料表中
        if (CollUtil.isNotEmpty(finalDelMaterialIdSet)) {
            specialAccessoriesRepository.listByIds(finalDelMaterialIdSet).forEach(item -> {
                SpecialAccessoriesTransient newTransient = new SpecialAccessoriesTransient();
                BeanUtils.copyProperties(item, newTransient);
                newTransient.setBomTransientId(bomTransientId);
                newTransient.setOriginSpecialId(item.getSpecialAccessoriesId());
                //若是待提交的暂存, 使用原物料id做主键
                Long newTransientSpecialId = null;
                if (waitSubmitBom) {
                    newTransientSpecialId = item.getSpecialAccessoriesId();
                } else {
                    newTransientSpecialId = IdPool.getId();
                }
                newTransient.setSpecialAccessoriesId(newTransientSpecialId);
                transientSpecialAddList.add(newTransient);
            });
        }

        //保存到新的特辅暂存表
        if (CollUtil.isNotEmpty(transientSpecialAddList)) {
            specialAccessoriesTransientRepository.saveBatch(transientSpecialAddList);
        }

        //暂存物料对应的备注
        if (CollUtil.isNotEmpty(updateRemarkList)) {
            designRemarksRepository.updateBatchById(updateRemarkList);
        }
        this.addDesignRemark(designRemarksReqList);
    }

    // private void moveTransientSpecial(Long bomId,
    //                                   Long bomTransientId,
    //                                   List<SpecialAccessories> transientSpecialAccessoriesList,
    //                                   boolean waitSubmitBom,
    //                                   Map<Long, List<DesignRemarks>> transientRemarkMap) {
    //     List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
    //     List<DesignRemarks> updateRemarkList = new LinkedList<>();
    //
    //
    //     //原有特辅全部迁移到新暂存表中
    //     List<SpecialAccessoriesTransient> transientSpecialAddList = new LinkedList<>();
    //     specialAccessoriesRepository.getListByBomId(bomId).forEach(item -> {
    //         SpecialAccessoriesTransient newTransient = new SpecialAccessoriesTransient();
    //         BeanUtils.copyProperties(item, newTransient);
    //         newTransient.setBomTransientId(bomTransientId);
    //         newTransient.setOriginSpecialId(item.getSpecialAccessoriesId());
    //         //若是待提交的暂存, 使用原物料id做主键
    //         Long newTransientSpecialId = null;
    //         if (waitSubmitBom) {
    //             newTransientSpecialId = item.getSpecialAccessoriesId();
    //         } else {
    //             newTransientSpecialId = IdPool.getId();
    //         }
    //         newTransient.setSpecialAccessoriesId(newTransientSpecialId);
    //         transientSpecialAddList.add(newTransient);
    //
    //         //物料备注
    //         List<DesignRemarks> remarks = transientRemarkMap.get(item.getSpecialAccessoriesId());
    //         if (CollUtil.isNotEmpty(remarks)) {
    //             Long remarkMaterialId = newTransientSpecialId;
    //             if (waitSubmitBom) {
    //                 //去重备注的暂存状态
    //                 remarks.forEach(rk -> {
    //                     DesignRemarks updateRemark = new DesignRemarks();
    //                     updateRemark.setDesignRemarksId(rk.getDesignRemarksId());
    //                     updateRemark.setTransientState(Bool.NO.getCode());
    //                     updateRemarkList.add(updateRemark);
    //                 });
    //             }else {
    //                 remarks.forEach(remark -> {
    //                     designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, remarkMaterialId, remark.getRemark()));
    //                 });
    //             }
    //         }
    //     });
    //
    //     //保存到新的特辅暂存表
    //     if (CollUtil.isNotEmpty(transientSpecialAddList)) {
    //         specialAccessoriesTransientRepository.saveBatch(transientSpecialAddList);
    //     }
    //
    //     //暂存物料对应的备注
    //     if (CollUtil.isNotEmpty(updateRemarkList)) {
    //         designRemarksRepository.updateBatchById(updateRemarkList);
    //     }
    //     this.addDesignRemark(designRemarksReqList);
    // }

    private void moveTransientMaterial(Long bomId,
                                       Long bomTransientId,
                                       List<BomOrderMaterial> transientMaterialList,
                                       Set<Long> finalDelMaterialIdSet,
                                       Map<Long, Long> oldNewTransientMaterialIdMap,
                                       boolean waitSubmitBom,
                                       Map<Long, List<DesignRemarks>> transientRemarkMap) {
        if (CollUtil.isEmpty(transientMaterialList)) {
            return;
        }
        //原有物料
        Map<Long, BomOrderMaterial> oldMaterialMap = bomOrderMaterialRepository.getListByBomId(bomId).stream()
                .collect(Collectors.toMap(BomOrderMaterial::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));
        List<BomOrderMaterialTransient> transientMaterialAddList = new LinkedList<>();

        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        List<DesignRemarks> updateRemarkList = new LinkedList<>();

        //旧暂存物料迁移到新暂存物料表中
        transientMaterialList.forEach(item -> {
            BomOrderMaterialTransient newTransient = new BomOrderMaterialTransient();
            BeanUtils.copyProperties(item, newTransient);
            newTransient.setBomTransientId(bomTransientId);
            //暂存的是原有物料,维护originMaterialId
            Long newTransientMaterialId = null;
            BomOrderMaterial oldMaterial = oldMaterialMap.get(item.getMaterialSnapshotId());
            if (Objects.nonNull(oldMaterial)) {
                newTransient.setOriginMaterialId(oldMaterial.getBomMaterialId());
                //若是待提交的暂存, 使用原物料id做主键
                if (waitSubmitBom) {
                    newTransientMaterialId = oldMaterial.getBomMaterialId();
                }
            }
            newTransientMaterialId = Objects.isNull(newTransientMaterialId) ? IdPool.getId() : newTransientMaterialId;
            newTransient.setBomMaterialId(newTransientMaterialId);
            oldNewTransientMaterialIdMap.put(item.getBomMaterialId(), newTransientMaterialId);
            transientMaterialAddList.add(newTransient);

            //物料备注
            List<DesignRemarks> remarks = transientRemarkMap.get(item.getBomMaterialId());
            if (CollUtil.isNotEmpty(remarks)) {
                Long remarkMaterialId = newTransientMaterialId;
                if (waitSubmitBom) {
                    //去重备注的暂存状态
                    remarks.forEach(rk -> {
                        DesignRemarks updateRemark = new DesignRemarks();
                        updateRemark.setDesignRemarksId(rk.getDesignRemarksId());
                        updateRemark.setTransientState(Bool.NO.getCode());
                        updateRemarkList.add(updateRemark);
                    });
                }else {
                    remarks.forEach(remark -> {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, remarkMaterialId, remark.getRemark()));
                    });
                }
            }
        });

        //暂存删除的原有物料迁移到新的暂存物料表中
        if (CollUtil.isNotEmpty(finalDelMaterialIdSet)) {
            bomOrderMaterialRepository.listByIds(finalDelMaterialIdSet).forEach(item -> {
                BomOrderMaterialTransient newTransient = new BomOrderMaterialTransient();
                BeanUtils.copyProperties(item, newTransient);
                newTransient.setBomTransientId(bomTransientId);
                //删除的原物料,标记关闭
                newTransient.setMaterialState(BomMaterialStateEnum.CLOSED.getCode());
                //暂存的是原有物料,维护originMaterialId
                newTransient.setOriginMaterialId(item.getBomMaterialId());
                //若是待提交的暂存, 使用原物料id做主键
                Long newTransientMaterialId = null;
                if (waitSubmitBom) {
                    newTransientMaterialId = item.getBomMaterialId();
                } else {
                    newTransientMaterialId = IdPool.getId();
                }
                newTransient.setBomMaterialId(newTransientMaterialId);
                oldNewTransientMaterialIdMap.put(item.getBomMaterialId(), newTransientMaterialId);
                transientMaterialAddList.add(newTransient);
            });
        }

        //暂存物料对应的备注
        if (CollUtil.isNotEmpty(updateRemarkList)) {
            designRemarksRepository.updateBatchById(updateRemarkList);
        }
        this.addDesignRemark(designRemarksReqList);

        //保存到新的暂存表中
        bomOrderMaterialTransientRepository.saveBatch(transientMaterialAddList);

    }

    private void addDesignRemark(List<DesignRemarksReq> designRemarksReqList) {
        //新增物料备注
        designRemarksReqList.stream()
                .filter(designRemark -> StringUtils.isNotBlank(designRemark.getRemark()))
                .forEach(designRemarksService::create);
    }

    /**
     * 根据key获取bom缓存在redis中的set值
     */
    private Set<Long> getBomSetByKey(String bomCraftDelKey) {
        Set<Long> finalIdSet = new HashSet<>();
        Set<Set<Long>> setIds = redisTemplate.opsForSet().members(bomCraftDelKey);
        if (CollUtil.isNotEmpty(setIds)) {
            for (Set<Long> set : setIds) {
                finalIdSet.addAll(set);
            }
        }
        return finalIdSet;
    }
}
