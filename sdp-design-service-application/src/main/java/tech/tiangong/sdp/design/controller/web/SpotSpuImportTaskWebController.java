package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.SpotSpuImportTaskService;
import tech.tiangong.sdp.design.service.SpotSpuService;
import tech.tiangong.sdp.design.vo.query.spot.SpotSpuImportTaskQuery;
import tech.tiangong.sdp.design.vo.query.spot.SpotSpuQuery;
import tech.tiangong.sdp.design.vo.resp.spot.SpotManagePageVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuImportTaskVo;

/**
 * @Author: caicijie
 * @description: 现货导入图片包管理
 * @Date: 2025/6/26 18:14
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/spot-spu-import-task")

public class SpotSpuImportTaskWebController {

    private final SpotSpuImportTaskService spotSpuImportTaskService;

    /**
     * 列表
     *
     * @param query 分页参数
     * @return PageRespVo<SpotSpuImportTaskVo>
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<SpotSpuImportTaskVo>> page(@RequestBody @Validated SpotSpuImportTaskQuery query) {
        return DataResponse.ok(spotSpuImportTaskService.page(query));
    }

    /**
     * 图包导入任务取消
     * @param taskId
     * @return
     */
    @PostMapping("/cancel/{taskId}")
    public DataResponse<Void> cancel(@PathVariable(value = "taskId")  Long taskId){
        spotSpuImportTaskService.cancel(taskId);
        return DataResponse.ok();
    }
}
