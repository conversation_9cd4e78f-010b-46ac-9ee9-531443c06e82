package tech.tiangong.sdp.design.business.remark;

import cn.hutool.core.lang.Assert;
import cn.hutool.extra.spring.SpringUtil;
import tech.tiangong.sdp.design.entity.DesignRemarks;
import tech.tiangong.sdp.design.entity.DigitalPrintingPrototype;
import tech.tiangong.sdp.design.repository.DigitalPrintingPrototypeRepository;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;

/**
 * <AUTHOR>
 */
public class ComposeDesignRemarkDigitalPrinting implements ComposeDesignRemark {
    @Override
    public DesignRemarks compose(DesignRemarksReq req, DesignRemarks designRemarks) {
        DigitalPrintingPrototypeRepository digitalPrintingPrototypeRepository = SpringUtil.getBean(DigitalPrintingPrototypeRepository.class);
        DigitalPrintingPrototype printingPrototype = digitalPrintingPrototypeRepository.getById(req.getBizId());
        Assert.notNull(printingPrototype, "不存在此数码印花款号");
        designRemarks.setStyleCode(printingPrototype.getStyleCode());
        designRemarks.setDesignCode(printingPrototype.getDesignCode());
        return designRemarks;
    }
}
