package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import team.aikero.blade.core.protocol.DataResponse;
import tech.tiangong.inspiration.client.InspirationProductClient;
import tech.tiangong.inspiration.client.StyleLibraryClient;
import tech.tiangong.inspiration.common.req.SpuImgListReq;
import tech.tiangong.inspiration.common.req.product.StyleLibraryReq;
import tech.tiangong.inspiration.common.resp.product.ProductFileVo;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.StyleLibrarySourceTypeEnum;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.RefreshDataService;
import tech.tiangong.sdp.design.vo.req.prototype.inner.DesignDemandRefreshQuery;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SpuImgListRefreshQuery;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SpuRefreshQuery;
import tech.tiangong.sdp.utils.StreamUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 刷数实现类
 * <br>CreateDate August 10,2021
 *
 * <AUTHOR>
 * @since 1.0
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class RefreshDataServiceImpl implements RefreshDataService {

    private final StyleLibraryClient styleLibraryClient;
    private final InspirationProductClient inspirationProductClient;
    private final DesignDemandRepository designDemandRepository;
    private final DesignDemandDetailRepository designDemandDetailRepository;
    private final SpotSpuRepository spotSpuRepository;
    private final SpotSkcRepository spotSkcRepository;
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final SpotSkcDetailRepository spotSkcDetailRepository;
    private final DesignStyleRepository designStyleRepository;
    private final PrototypeRepository prototypeRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final SpotSpuSupplierRepository spotSpuSupplierRepository;
    private final PrototypeHistoryRepository prototypeHistoryRepository;

    @Override
    public void designDemandVectorRefresh(DesignDemandRefreshQuery query) {
        List<DesignDemand> allList = designDemandRepository.selectAll(query);
        //分批处理
        int batchSize = 800;
        // 分批处理
        for (int i = 0; i < allList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, allList.size());
            List<DesignDemand> batch = allList.subList(i, end);

            List<Long> demandIdList = StreamUtil.convertListAndDistinct(batch, DesignDemand::getDesignDemandId);
            //详情信息
            List<DesignDemandDetail> detailList = designDemandDetailRepository.listByDesignDemandIds(demandIdList);
            Map<Long, DesignDemandDetail> detailMap = StreamUtil.list2Map(detailList, DesignDemandDetail::getDesignDemandId);
            // 异步处理当前批次
            batch.stream().map(item -> CompletableFuture.runAsync(() -> {
                DesignDemandDetail demandDetail = detailMap.get(item.getDesignDemandId());
                StyleLibraryReq req = new StyleLibraryReq();
                req.setSourceType(StyleLibrarySourceTypeEnum.INSPIRATION_TASK_DISTRIBUTION.code);
                req.setBusId(item.getDesignDemandId());
                req.setBusCode(item.getDesignDemandId().toString());
                List<String> imageList = demandDetail.getInspirationImageList();
                if (CollectionUtil.isEmpty(imageList)) {
                    log.info("图片信息为空，不能进行请求，灵感主键id：" + item.getDesignDemandId());
                    return;
                }
                req.setStyleImg(imageList.get(0));
                // req.setCategoryId(item.getCategory());
                req.setCategoryName(item.getCategoryName());
                req.setTenantId(2L);
                req.setCreatorId(item.getCreatorId());
                req.setCreatorName(item.getCreatorName());
                try {
                    log.info("灵感任务开始请求向量创建，请求信息：" + JSON.toJSONString(req));
                    DataResponse<Long> resp = styleLibraryClient.create(req);
                    if (!resp.getSuccessful()) {
                        log.info("灵感任务请求向量创建失败，失败原因:" + resp.getMessage());
                    } else {
                        log.info("灵感任务请求向量创建处理成功，业务id:" + req.getBusId());
                    }
                    log.info("灵感任务开始请求向量创建处理成功，业务id:" + req.getBusId());
                } catch (Exception e) {
                    log.info("灵感任务开始请求向量创建处理失败，业务id:" + req.getBusId() + ",异常信息：" + e.getMessage());
                }

            })).collect(Collectors.toList());
        }
    }

    @Override
    public void spotSpuRefresh(SpuRefreshQuery query) {
        List<SpotSpu> allList = spotSpuRepository.selectAll(query);
        //分批处理
        int batchSize = 800;
        // 分批处理
        for (int i = 0; i < allList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, allList.size());
            List<SpotSpu> batch = allList.subList(i, end);

            Map<String, SpotSpu> spuMap = StreamUtil.list2Map(batch, SpotSpu::getStyleCode);
            List<String> styleCodeList = StreamUtil.convertListAndDistinct(batch, SpotSpu::getStyleCode);

            //skc
            List<SpotSkc> skcAllList = spotSkcRepository.listNotCancelByStyleCodes(styleCodeList);
            //供应商
            List<SpotSpuSupplier> spotSpuSuppliers = spotSpuSupplierRepository.listByStyleCodes(styleCodeList);
            Map<String, List<SpotSpuSupplier>> spotSpuSuppliersMap = StreamUtil.groupingBy(spotSpuSuppliers, SpotSpuSupplier::getStyleCode);

            List<Long> skcIdList = StreamUtil.convertListAndDistinct(skcAllList, SpotSkc::getSpotSkcId);
            List<SpotSkcDetail> skcDetails = spotSkcDetailRepository.listBySkcIds(skcIdList);
            Map<Long, SpotSkcDetail> skcDetailMap = StreamUtil.list2Map(skcDetails, SpotSkcDetail::getSpotSkcId);

            // 处理当前批次
            skcAllList.stream()
                    .map(item -> CompletableFuture.runAsync(() -> {
                        SpotSpu spu = spuMap.get(item.getStyleCode());
                        StyleLibraryReq req = new StyleLibraryReq();
                        req.setSourceType(StyleLibrarySourceTypeEnum.SPOT_MANAGEMENT.code);
                        req.setBusId(item.getSpotSkcId());
                        req.setBusCode(item.getDesignCode());
                        SpotSkcDetail skcDetail = skcDetailMap.get(item.getSpotSkcId());
                        if (Objects.nonNull(skcDetail) && CollUtil.isNotEmpty(skcDetail.getProductPictureList())) {
                            req.setStyleImg(skcDetail.getProductPictureList().getFirst());
                        }
                        if (StringUtil.isBlank(req.getStyleImg())) {
                            log.info("图片信息为空，不能进行请求，主键id：" + item.getSpotSkcId());
                            return;
                        }
                        //req.setCategoryId(item.getCategory());
                        req.setCategoryName(spu.getCategoryName());
                        req.setTenantId(spu.getTenantId());
                        req.setCreatorId(item.getCreatorId());
                        req.setCreatorName(item.getCreatorName());
                        //默认取第一个供应商、供应商款号
                        if (!CollectionUtil.isEmpty(spotSpuSuppliersMap) && spotSpuSuppliersMap.containsKey(item.getStyleCode())) {
                            List<SpotSpuSupplier> spotSpuSupplierList = spotSpuSuppliersMap.get(item.getStyleCode());
                            if (!CollectionUtil.isEmpty(spotSpuSupplierList)) {
                                req.setSupplierName(spotSpuSupplierList.get(0).getSupplierName());
                                req.setSupplierStyleCode(spotSpuSupplierList.get(0).getSupplierStyle());
                            }
                        }
                        try {
                            log.info("现货管理开始请求向量创建，请求信息：" + JSON.toJSONString(req));
                            DataResponse<Long> resp = styleLibraryClient.create(req);
                            if (!resp.getSuccessful()) {
                                log.info("款式管理请求向量创建失败，失败原因:" + resp.getMessage());
                            } else {
                                log.info("现货管理开始请求向量创建处理成功，业务id:" + req.getBusId());
                            }
                        } catch (Exception e) {
                            log.info("现货管理请求向量创建处理失败，业务id:" + req.getBusId() + ",异常信息：" + e.getMessage());
                        }
                    })).collect(Collectors.toList());
        }
    }

    @Override
    public void prototypeManageRefresh(SpuRefreshQuery query) {
        List<DesignStyle> allList = designStyleRepository.selectAll(query);
        //分批处理
        int batchSize = 800;
        // 分批处理
        for (int i = 0; i < allList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, allList.size());
            List<DesignStyle> batch = allList.subList(i, end);

            Map<String, DesignStyle> spuMap = StreamUtil.list2Map(batch, DesignStyle::getStyleCode);
            List<String> styleCodeList = StreamUtil.convertListAndDistinct(batch, DesignStyle::getStyleCode);

            //skc
            List<Prototype> skcAllList = prototypeRepository.getNotCancelByStyleCodes(styleCodeList);
            List<Long> skcIdList = StreamUtil.convertListAndDistinct(skcAllList, Prototype::getPrototypeId);
            List<PrototypeDetail> skcDetails = prototypeDetailRepository.getListByPrototypeIds(skcIdList);
            Map<Long, PrototypeDetail> skcDetailMap = StreamUtil.list2Map(skcDetails, PrototypeDetail::getPrototypeId);

            //异步执行
            skcAllList.stream().map(item -> CompletableFuture.runAsync(() -> {
                DesignStyle spu = spuMap.get(item.getStyleCode());
                PrototypeHistory prototypeHistory = prototypeHistoryRepository.selectFirstByDesignCode(item.getDesignCode());
                StyleLibraryReq req = new StyleLibraryReq();
                req.setSourceType(StyleLibrarySourceTypeEnum.STYLE_MANAGEMENT.code);
                req.setBusId(prototypeHistory.getPrototypeId());
                req.setBusCode(item.getDesignCode());
                PrototypeDetail skcDetail = skcDetailMap.get(item.getPrototypeId());
                if (null != skcDetail) {
                    req.setStyleImg(skcDetail.getDesignPicture());
                }
                if (StringUtil.isBlank(req.getStyleImg())) {
                    log.info("图片信息为空，不能进行请求，主键id：" + item.getPrototypeId());
                    return;
                }
                req.setCategoryName(spu.getCategoryName());
                req.setTenantId(2L);
                req.setCreatorId(item.getCreatorId());
                req.setCreatorName(item.getCreatorName());
                try {
                    log.info("款式管理开始请求向量创建，请求信息：" + JSON.toJSONString(req));
                    DataResponse<Long> resp = styleLibraryClient.create(req);
                    if (!resp.getSuccessful()) {
                        log.info("款式管理请求向量创建失败，失败原因:" + resp.getMessage());
                    } else {
                        log.info("款式管理请求向量创建处理成功，业务id:" + req.getBusId());
                    }
                } catch (Exception e) {
                    log.info("款式管理请求向量创建处理失败，业务id:" + req.getBusId() + ",异常信息：" + e.getMessage());
                }

            })).collect(Collectors.toList());

        }
    }

    @Override
    public void productImgUrlRefresh(SpuImgListRefreshQuery query) {
        List<SpotSpuDetail> spotSpuDetails = spotSpuDetailRepository.listByProductIds(query.getProductIds());

        //分批处理
        int batchSize = 500;
        // 分批处理
        List<SpotSpuDetail> updateList = new ArrayList<>();
        for (int i = 0; i < spotSpuDetails.size(); i += batchSize) {
            int end = Math.min(i + batchSize, spotSpuDetails.size());
            List<SpotSpuDetail> batch = spotSpuDetails.subList(i, end);
            List<Long> productIds = StreamUtil.convertListAndDistinct(batch, SpotSpuDetail::getProductId);
            if (!CollectionUtil.isEmpty(productIds)) {
                SpuImgListReq req = new SpuImgListReq();
                req.setProductIds(productIds);
                DataResponse<List<ProductFileVo>> response = inspirationProductClient.getSpuImgList(req);
                if (response.getSuccessful()) {
                    List<ProductFileVo> voList = response.getData();
                    /*Map<Long, List<ProductFileVo>> productMap = voList.stream().collect(Collectors.groupingBy(ProductFileVo::getSpuId));
                    SpotSpuDetail detail = new SpotSpuDetail();
                    for (Long productId : productMap.keySet()){
                        List<ProductFileVo> list = productMap.get(productId);
                        List<String> pictureList = StreamUtil.convertListAndDistinct(list, ProductFileVo::getUrl);
                        detail.setProductPictureList(pictureList);
                        detail.setProductId(productId);
                        updateList.add(detail);
                    }*/
                } else {
                    log.info("调用灵感服务失败:" + response.getMessage());
                }
            }
        }

        if (!CollectionUtil.isEmpty(updateList)) {
            updateList.forEach(t -> {
                spotSpuDetailRepository.updatePictureListByProductId(t);
            });
        }
    }
}