package tech.tiangong.sdp.design.service;

import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.design.entity.DesignDemandAIBoxTaskResult;
import tech.tiangong.sdp.design.repository.DesignDemandAIBoxTaskResultRepository;

@Service
@RequiredArgsConstructor
public class DesignDemandAIBoxTaskResultService {

    private final DesignDemandAIBoxTaskResultRepository designDemandAIBoxTaskResultRepository;

    public void passDesignDemandAIBoxTaskResult(Long id, Boolean passed){
        designDemandAIBoxTaskResultRepository
                .lambdaUpdate()
                .set(DesignDemandAIBoxTaskResult::getAsPassed,passed)
                .eq(DesignDemandAIBoxTaskResult::getId,id)
                .update();
    }
}
