package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.req.digital.DigitalPrintingStyleDetailReq;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingStyleDetailVo;

/**
 * 数码印花_SPU详情表服务接口
 *
 * <AUTHOR>
 */
public interface DigitalPrintingStyleDetailService {

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    DigitalPrintingStyleDetailVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(DigitalPrintingStyleDetailReq req);


}
