package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tech.tiangong.sdp.core.aop.ResetCurrentUser;
import tech.tiangong.sdp.design.entity.VisualDemand;
import tech.tiangong.sdp.design.enums.visual.VisualErrorCodeEnum;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.query.visual.VisualTaskQuery;
import tech.tiangong.sdp.design.vo.req.visual.*;
import tech.tiangong.sdp.design.vo.resp.visual.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.List;


/**
 * 视觉任务管理-web
 *
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/visual-task")
public class VisualTaskController extends BaseController {
    private final VisualTaskService visualTaskService;
    private final VisualDemandService visualDemandService;
    private final VisualSpuService visualSpuService;
    private final VisualTaskTryOnLogService visualTaskTryOnLogService;
    private final VisualTaskTryOnImageService visualTaskTryOnImageService;
    private final VisualDataHandleService visualDataHandleService;

    /**
     * 列表
     *
     * @param query 分页参数
     * @return PageRespVo<VisualTaskListVo>
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<VisualTaskListVo>> page(@RequestBody @Validated VisualTaskQuery query) {
        return DataResponse.ok(visualTaskService.page(query));
    }

    /**
     * 列表-附加信息(skc)-查询
     *
     * @param req 查询对象
     * @return 视觉附加信息
     */
    @PostMapping("/page-extra")
    public DataResponse<List<VisualTaskListExtraVo>> pageExtra(@RequestBody @Validated VisualTaskListExtraReq req) {
        return DataResponse.ok(visualTaskService.pageExtra(req));
    }

    /**
     * 状态统计查询
     *
     */
    @PostMapping("/count-state")
    public DataResponse<VisualTaskListCountVo> countState(@RequestBody @Validated VisualTaskQuery req) {
        return DataResponse.ok(visualTaskService.countState(req));
    }

    /**
     * 任务类型统计查询
     *
     */
    @PostMapping("/count-task-type")
    public DataResponse<List<VisualTaskTypeCountVo>> countTaskType(@RequestBody @Validated VisualTaskQuery req) {
        return DataResponse.ok(visualTaskService.countTaskType(req));
    }

    /**
     * 根据视觉需求ID查询需求
     * @param demandId
     * @return
     */
    @GetMapping("/get-demand-by-id/{demandId}")
    public DataResponse<VisualDemandVo> getVisualDemandById(@PathVariable(value = "demandId") Long demandId) {
        return DataResponse.ok(visualTaskService.getVisualDemandById(demandId));
    }

    /**
     * 根据视觉任务ID查询详情
     * @param taskId
     * @return
     */
    @GetMapping("/get-detail-by-id/{taskId}")
    public DataResponse<VisualTaskDetailVo> getTaskDetailById(@PathVariable(value = "taskId") Long taskId) {
        return DataResponse.ok(visualTaskService.getVisualTaskDetailById(taskId));
    }

    /**
     * 任务分配
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/allocate")
    public DataResponse<Void> allocate(@RequestBody @Validated AllocateTaskReq req) {
        visualTaskService.allocate(req);
        return DataResponse.ok();
    }

    /**
     * 获取上新任务的分配信息
     * @param styleCodes
     * @return
     */
    @PostMapping("/get-new-arrival-task-allocate-info")
    public DataResponse<List<VisualTaskAllocateInfoVo>> getNewArrivalTaskAllocateInfoBySpu(@RequestBody List<String> styleCodes) {
        return DataResponse.ok(visualTaskService.getNewArrivalTaskAllocateInfoBySpu(styleCodes));
    }

    /**
     * 获取允许分配处理方式
     * @param taskId
     * @return
     */
    @GetMapping("/show-optional-process-type/{taskId}")
    public DataResponse<List<VisualTaskProcessTypeVo>> showOptionalProcessType(@PathVariable("taskId") Long taskId) {
        return DataResponse.ok(visualTaskService.showOptionalProcessType(taskId));
    }
    /**
     * 取消需求
     *
     * @param req
     * @return 响应结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/cancel-visual-task")
    public DataResponse<Void> cancelVisualTask(@RequestBody @Validated CancelVisualTaskReq req) {
        visualTaskService.cancelVisualTask(req);
        return DataResponse.ok();
    }

    /**
     * 创建视觉需求
     *
     * @param req
     * @return 响应结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/create-visual-demand")
    public DataResponse<String> createVisualDemand(@RequestBody @Validated SaveVisualDemandBySpuReq req) {
        VisualDemand visualDemand = null;
        try{
            visualDemand = visualDemandService.createVisualDemand4Front(req);
        }catch (Exception e){
            if(e.getMessage().startsWith(VisualErrorCodeEnum.ERROR_98123.getErrorMsg())){
                log.error("创建视觉需求失败",e);
                String demandId = e.getMessage().replace(VisualErrorCodeEnum.ERROR_98123.getErrorMsg(), "");
                DataResponse<String> dataResponse = DataResponse.failed();
                dataResponse.setCode(VisualErrorCodeEnum.ERROR_98123.getCode());
                dataResponse.setMessage(VisualErrorCodeEnum.ERROR_98123.getDesc());
                dataResponse.setData(demandId);
                dataResponse.setSuccessful(false);
                return dataResponse;
            }
            throw e;
        }
        return DataResponse.ok(String.valueOf(visualDemand.getDemandId()));
    }

    /**
     * 编辑视觉需求
     *
     * @param req
     * @return 响应结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/save-visual-demand")
    public DataResponse<VisualDemand> saveVisualDemand(@RequestBody @Validated SaveVisualDemandByIdReq req) {
        return DataResponse.ok(visualDemandService.saveVisualDemandById(req));
    }

    /**
     * 提交图片下载任务
     * @param req
     * @return
     */
    @PostMapping("/submit-image-download-task")
    public DataResponse<String> submitImageDownloadTask(@RequestBody @Validated DownloadVisualTaskImageReq req) {
        return DataResponse.ok(String.valueOf(visualTaskService.submitImageDownloadTask(req)));
    }

    /**
     * 批量上传图片
     * @param req
     * @return
     */
    @PostMapping("/batch-upload-images")
    public DataResponse<List<BatchUploadImagesResp>> batchUploadImages(@RequestBody @Validated BatchUploadImagesReq req) {
        return DataResponse.ok(visualTaskService.batchUploadImages(req));
    }

    /**
     * 初始化风格历史数据
     * @param file
     * @return
     */
    @PostMapping("/initHisDataV2")
    public DataResponse<Void> initHisDataV2(MultipartFile file){
        visualSpuService.initHisDataV2(file);
        return DataResponse.ok();
    }

    /**
     * 批量发起 try on
     */
    @Validated
    @PostMapping("/batch-try-on")
    public DataResponse<String> batchTryOn(@RequestBody @NotEmpty @Valid List<BatchTryOnReq> reqList){
        return DataResponse.ok(visualTaskService.batchTryOn(reqList));
    }

    /**
     * 获取try on 详情
     */
    @GetMapping("/get-try-on-detail/{taskId}")
    public DataResponse<List<VisualTaskTryOnDetailVo>> getTryOnDetail(@PathVariable("taskId") Long taskId){
        return DataResponse.ok(visualTaskTryOnLogService.listTryOnTaskImageDetail(taskId));
    }

    /**
     * 批量获取try on 详情
     */
    @PostMapping("/batch-get-try-on-detail")
    public DataResponse<List<VisualTaskTryOnBatchDetailVo>> batchGetTryOnDetail(@RequestBody List<Long> taskIdList){
        return DataResponse.ok(visualTaskTryOnLogService.batchGetTryOnTaskImageDetail(taskIdList));
    }

    /**
     * 设置try on主图
     */
    @PostMapping("/try-on-set-main-image")
    public DataResponse<Void> tryOnSetMainImage(@RequestBody TryOnSetMainImageReq req){
        visualTaskTryOnImageService.setTryOnMainImage(req.getVisualTaskId(), req.getTryOnTaskImageId());
        return DataResponse.ok();
    }

    /**
     * 发起AIBox前调用
     * @param taskIdList
     * @return
     */
    @PostMapping("/before-AiBox")
    public DataResponse<List<BeforeAIBoxVo>> beforeInvokeAiBox(@RequestBody @NotEmpty @Valid List<Long> taskIdList){
        return DataResponse.ok(visualTaskService.beforeInvokeAiBox(taskIdList));
    }



    /**
     * 批量创建视觉任务-excel导入
     */
    @NoRepeatSubmitLock
    @PostMapping("/visual/demand-create")
    public DataResponse<List<BatchCreateVisualDemandResp>> batchCreateVisualDemand(@RequestParam("file") MultipartFile file) throws IOException {
        return DataResponse.ok(visualDataHandleService.batchCreateVisualDemand(file.getInputStream()));
    }
}
