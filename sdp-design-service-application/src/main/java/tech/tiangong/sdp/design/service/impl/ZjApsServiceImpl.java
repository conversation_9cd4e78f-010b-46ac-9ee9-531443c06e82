package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.design.remote.ZjApsRemoteHelper;
import tech.tiangong.sdp.design.service.ZjApsService;
import tech.tiangong.sdp.design.vo.req.zj.ZjSupplierReq;
import tech.tiangong.sdp.design.vo.resp.zj.SupplierInfoVo;
import tech.tiangong.sdp.design.vo.resp.zj.aps.SupplierSimpleResp;

import java.util.List;

/**
 * 致景 aps service
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:47
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZjApsServiceImpl implements ZjApsService {

    private final ZjApsRemoteHelper zjApsRemoteHelper;

    @Override
    public List<SupplierInfoVo> supplierQuery(ZjSupplierReq req) {
        List<SupplierSimpleResp> respList = zjApsRemoteHelper.queryApsSupplier(req);
        if (CollUtil.isEmpty(respList)) {
            return List.of();
        }

        return respList.stream().map(item ->{
            SupplierInfoVo info = new SupplierInfoVo();
            BeanUtils.copyProperties(item, info);
            return info;
        }).toList();
    }
}
