package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import com.alibaba.fastjson.JSON;
import com.yibuyun.scm.common.dto.accessories.response.ProductSkuVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.converter.BomOrderConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.BomOrderStateEnum;
import tech.tiangong.sdp.design.enums.PushZjTypeEnum;
import tech.tiangong.sdp.design.enums.SpecialAccessoriesStateEnum;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.ProductRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.BomOperateService;
import tech.tiangong.sdp.design.service.BomOrderService;
import tech.tiangong.sdp.design.service.CheckCountMaterialService;
import tech.tiangong.sdp.design.service.DesignLogService;
import tech.tiangong.sdp.design.vo.dto.bom.BomCopyInfoDto;
import tech.tiangong.sdp.design.vo.req.BomMaterialDosageAccountReq;
import tech.tiangong.sdp.design.vo.req.bom.CheckCountMaterialOperateReq;
import tech.tiangong.sdp.design.vo.req.log.DesignLogReq;
import tech.tiangong.sdp.design.vo.req.special.accessories.SpecialAccessoriesUpdateReq;
import tech.tiangong.sdp.design.vo.resp.bom.CheckCountMaterialOperateVo;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 核算环节-物料操作 service
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Service
public class CheckCountMaterialServiceImpl implements CheckCountMaterialService {


	private final BomOrderService bomOrderService;
	private final BomOrderRepository bomOrderRepository;
	private final SpecialAccessoriesRepository specialAccessoriesRepository;
	private final BomOrderMaterialRepository bomOrderMaterialRepository;
	private final PrototypeHistoryRepository prototypeHistoryRepository;
	private final PrototypeRepository prototypeRepository;
	private final BomOperateService bomOperateService;
	private final DesignLogService designLogService;
	private final ProductRemoteHelper productRemoteHelper;
	private final MqProducer mqProducer;


	@Override
	@Transactional(rollbackFor = Exception.class)
	public CheckCountMaterialOperateVo operateMaterial(CheckCountMaterialOperateReq req) {
		log.info("=== 核算操作物料,req:{} ===", JSON.toJSONString(req));
		//核算环节操作物料
		CheckCountMaterialOperateVo operateVo = CheckCountMaterialOperateVo.builder()
				.designCode(req.getDesignCode()).bomId(req.getBomId()).build();
		//1,先判断有没有增删特辅, 如果有, 先按增删特辅的逻辑对bom单升版本
		if (CollUtil.isNotEmpty(req.getDeleteIdList()) || CollUtil.isNotEmpty(req.getSaveList())) {
			log.debug("=== 核算操作物料-bom单升版本 ===");
			//1.1增删特辅处理, 获取的新bom(已核算);
			//1.2面辅料核算更新处理
			//1.3新bom单推送致景
			SpecialAccessoriesUpdateReq updateReq = new SpecialAccessoriesUpdateReq();
			//新增的特辅
			List<SpecialAccessoriesUpdateReq.SaveReq> saveReqList = req.getSaveList().stream().map(item -> {
				SpecialAccessoriesUpdateReq.SaveReq saveReq = new SpecialAccessoriesUpdateReq.SaveReq();
				BeanUtils.copyProperties(item, saveReq);
				return saveReq;
			}).collect(Collectors.toList());

			updateReq.setDesignCode(req.getDesignCode());
			updateReq.setBatchSaveReq(saveReqList);

			List<CheckCountMaterialOperateVo.SpecialMaterial> newSpecialMaterialList = new ArrayList<>();

			//物料增删改处理
			BomOrder newBomOrder = this.addUpdateDeleteSpecial(updateReq, req.getDeleteIdList(), newSpecialMaterialList, req.getUpdateList(), operateVo);

			operateVo.setNewBomId(newBomOrder.getBomId());
			operateVo.setNewSpecialMaterialList(newSpecialMaterialList);

			return operateVo;
		}
		log.debug("=== 核算操作物料-更新核算 ===");
		//2,如果没有增删特辅, 执行核算更新的操作
		//2.1物料核算更新操作
		//2.2更新bom单为已核算
		//2.3核算信息同步致景
		SdpDesignException.notEmpty(req.getUpdateList(), "物料信息为空!");
		List<BomMaterialDosageAccountReq.MaterialDosageAccount> dosageAccountList = req.getUpdateList().stream().map(item -> {
			BomMaterialDosageAccountReq.MaterialDosageAccount dosageAccount = new BomMaterialDosageAccountReq.MaterialDosageAccount();
			BeanUtils.copyProperties(item, dosageAccount);
			return dosageAccount;
		}).collect(Collectors.toList());

		BomMaterialDosageAccountReq dosageAccountReq = new BomMaterialDosageAccountReq();
		dosageAccountReq.setBomId(req.getBomId());
		dosageAccountReq.setDosageAccountList(dosageAccountList);

		bomOrderService.updateBomMaterialDosageAccount(dosageAccountReq);

		return operateVo;
	}

	public BomOrder addUpdateDeleteSpecial(SpecialAccessoriesUpdateReq updateReq, List<Long> deleteIdList,
										   List<CheckCountMaterialOperateVo.SpecialMaterial> newSpecialMaterialList,
										   List<CheckCountMaterialOperateReq.UpdateReq> updateMaterialReqList, CheckCountMaterialOperateVo operateVo) {
		String designCode = updateReq.getDesignCode();
		BomOrder latestBomOrder = bomOrderRepository.getLatestBomByDesignCode(designCode);
		SdpDesignException.notNull(latestBomOrder, "bom单信息不存在!");

		BomOrder newBomOrder;
		PrototypeHistory prototypeHistory;

		//删除特辅Id
		Set<Long> accessoriesFlagIds = new HashSet<>();
		if (CollUtil.isNotEmpty(deleteIdList)) {
            List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.getListBySpecialAccessoriesIds(deleteIdList);
			if (CollectionUtil.isEmpty(specialAccessoriesList)) {
				log.info("【删除特殊辅料】无此特殊辅料 specialAccessoriesIds:{}", JSON.toJSONString(deleteIdList));
			}
			accessoriesFlagIds = specialAccessoriesList.stream().map(SpecialAccessories::getAccessoriesFlagId).collect(Collectors.toSet());
		}

		//升版本时维护版本关联id
		Map<Long, Long> contextIdMap = new HashMap<>();

		// BOM单升版本
		BomCopyInfoDto bomCopyInfoDto = new BomCopyInfoDto();
		newBomOrder = this.findNewBomOrder(updateReq, designCode, latestBomOrder, contextIdMap, newSpecialMaterialList, bomCopyInfoDto);
		prototypeHistory = prototypeHistoryRepository.getById(newBomOrder.getPrototypeId());

		UserContent userContent = UserContentHolder.get();
		LocalDateTime now = LocalDateTime.now();
		//新增特辅
		if (CollUtil.isNotEmpty(updateReq.getBatchSaveReq())) {
			this.insert(updateReq, newBomOrder, prototypeHistory, userContent, contextIdMap, newSpecialMaterialList);
		}
		//将此次删除的特殊辅料变更状态
		this.closeSpecial(accessoriesFlagIds, newBomOrder);

		//物料核算更新(更新的是新版本的bom单物料
		this.dosageUpdate(updateMaterialReqList, userContent, now, bomCopyInfoDto, operateVo);

		//操作日志
		DesignLogReq designLogReq = BomOrderConverter.buildBomOrderLog(newBomOrder, "核算更新【特殊辅料】");
		designLogService.create(designLogReq);

		//bomId不变,更新bom
		// if (Objects.equals(newBomOrder.getBomId(), latestBomOrder.getBomId())) {
		// 	this.updateBomReversedTime(latestBomOrder);
		// 	//若bom是提交状态, 则推送致景
		// 	if (Objects.equals(latestBomOrder.getState(), BomOrderStateEnum.SUBMITTED.getCode())) {
		// 		log.info(" === bom已提交bomId不变推送致景: bomId:{}; ===", latestBomOrder.getBomId());
		// 		bomOperateService.pushBom2Zj(latestBomOrder, PushZjTypeEnum.BOM_UPDATE);
		// 	}
		// }else {
		// 	//bom推送致景
		// 	bomOperateService.pushBom2Zj(newBomOrder, PushZjTypeEnum.BOM_SUBMIT);
		// }

		//bom推送致景
		bomOperateService.pushBom2Zj(newBomOrder, PushZjTypeEnum.BOM_SUBMIT);

		return newBomOrder;
	}

	private void dosageUpdate(List<CheckCountMaterialOperateReq.UpdateReq> updateMaterialReqList, UserContent userContent, LocalDateTime now, BomCopyInfoDto bomCopyInfoDto, CheckCountMaterialOperateVo operateVo) {
		if (CollUtil.isEmpty(updateMaterialReqList)) {
			return;
		}
		log.info("核算bom升版本-更新物料:{}", JSON.toJSONString(updateMaterialReqList));

		List<BomOrderMaterial> updateBomOrderMaterialList = new ArrayList<>();
		List<SpecialAccessories> updateSpecialAccessoriesList = new ArrayList<>();

		List<Long> updateMaterialIdList = StreamUtil.convertListAndDistinct(updateMaterialReqList, CheckCountMaterialOperateReq.UpdateReq::getBomMaterialId);
		Map<Long, SpecialAccessories> specialAccessoriesMap = StreamUtil.list2Map(specialAccessoriesRepository.getListBySpecialAccessoriesIds(updateMaterialIdList), SpecialAccessories::getSpecialAccessoriesId);

		//要更新到新版本的bom单物料中
		log.info("核算bom升版本-新旧物料关系:{}", JSON.toJSONString(bomCopyInfoDto));
		Map<Long, Long> oldNewMaterialIdMap = bomCopyInfoDto.getOldNewMaterialIdMap();
		Map<Long, Long> oldNewSpecialIdMap = bomCopyInfoDto.getOldNewSpecialIdMap();
		Map<Long, Long> oldNewMaterialIdMapVo = new HashMap<>();

		updateMaterialReqList.forEach(dosageAccountReq -> {
			if (Objects.isNull(specialAccessoriesMap.get(dosageAccountReq.getBomMaterialId()))) {
				Long newMaterialId = oldNewMaterialIdMap.get(dosageAccountReq.getBomMaterialId());
                if (Objects.nonNull(newMaterialId)) {
					oldNewMaterialIdMapVo.put(dosageAccountReq.getBomMaterialId(), newMaterialId);
					BomOrderMaterial updateMaterial = BomOrderMaterial.builder()
							.bomMaterialId(newMaterialId)
							.dosageAccount(dosageAccountReq.getDosageAccount()).dosageAccountUnit(dosageAccountReq.getDosageAccountUnit())
							.widthConfirm(dosageAccountReq.getWidthConfirm()).attritionRate(dosageAccountReq.getAttritionRate())
							.reviserName(userContent.getCurrentUserName()).reviserId(userContent.getCurrentUserId()).revisedTime(now)
							.build();
					updateBomOrderMaterialList.add(updateMaterial);
				}
			}else {
				Long newSpecialId = oldNewSpecialIdMap.get(dosageAccountReq.getBomMaterialId());
                if (Objects.nonNull(newSpecialId)) {
					oldNewMaterialIdMapVo.put(dosageAccountReq.getBomMaterialId(), newSpecialId);
					SpecialAccessories updateSpecialAccessories = SpecialAccessories.builder()
							.specialAccessoriesId(newSpecialId)
							.dosageAccount(dosageAccountReq.getDosageAccount())
							.dosageAccountUnit(dosageAccountReq.getDosageAccountUnit())
							.attritionRate(dosageAccountReq.getAttritionRate())
							.reviserName(userContent.getCurrentUserName()).reviserId(userContent.getCurrentUserId()).revisedTime(now)
							.build();
					updateSpecialAccessoriesList.add(updateSpecialAccessories);
				}
			}
		});

		operateVo.setOldNewMaterialIdMap(oldNewMaterialIdMapVo);
		operateVo.setOldNewCraftIdMap(bomCopyInfoDto.getOldNewCraftIdMap());

		if (CollUtil.isNotEmpty(updateBomOrderMaterialList)) {
			bomOrderMaterialRepository.updateBatchById(updateBomOrderMaterialList);
		}
		if (CollUtil.isNotEmpty(updateSpecialAccessoriesList)) {
			specialAccessoriesRepository.updateBatchById(updateSpecialAccessoriesList);
		}
	}

	private void closeSpecial(Set<Long> accessoriesFlagIds, BomOrder newBomOrder) {
		if (CollUtil.isEmpty(accessoriesFlagIds)) {
			return;
		}
		List<SpecialAccessories> updateSpecialAccessories = specialAccessoriesRepository.getListByBomId(newBomOrder.getBomId()).stream()
				.filter(special -> accessoriesFlagIds.contains(special.getAccessoriesFlagId()))
				.map(special -> SpecialAccessories.builder().specialAccessoriesId(special.getSpecialAccessoriesId()).state(SpecialAccessoriesStateEnum.CLOSED.getCode()).build())
				.collect(Collectors.toList());
		specialAccessoriesRepository.updateBatchById(updateSpecialAccessories);
	}

	private void updateBomReversedTime(BomOrder latestBomOrder) {
		//更新bom单的更新时间
		bomOrderRepository.updateById(BomOrder.builder().bomId(latestBomOrder.getBomId()).build());
	}

	/**
	 * 保存特殊辅料
	 *
	 * @param updateReq
	 * @param newSpecialMaterialList
	 */
	private List<SpecialAccessories> insert(SpecialAccessoriesUpdateReq updateReq,
											BomOrder bomOrder,
											PrototypeHistory prototypeHistory,
											UserContent userContent,
											Map<Long, Long> contextIdMap,
											List<CheckCountMaterialOperateVo.SpecialMaterial> newSpecialMaterialList) {

		if (CollectionUtil.isEmpty(updateReq.getBatchSaveReq())) {
			log.info("【批量新增特殊辅料】designCode:{} 参数为空,不处理", updateReq.getDesignCode());
			return List.of();
		}

		LocalDateTime now = LocalDateTime.now();
		List<DesignRemarks> newDesignRemarksList = new ArrayList<>(100);

		List<SpecialAccessories> newSpecialAccessoriesList = updateReq.getBatchSaveReq().stream().map(req -> {
			//校验最小单位, 最小单位
			SdpDesignException.isTrue(Objects.nonNull(req.getMinPrice()) && StringUtils.isNotBlank(req.getMinPriceUnit()),
					"{} 辅料商品 {} 缺少价格最小价格与单位,请联系物料管理员完善后再添加", req.getName(), req.getSkuCode());
			SpecialAccessories specialAccessories = new SpecialAccessories();
			BeanUtils.copyProperties(req, specialAccessories);

			long specialAccessoriesId = IdPool.getId();
			specialAccessories.setSpecialAccessoriesId(specialAccessoriesId);
			specialAccessories.setPrototypeId(bomOrder.getPrototypeId());
			specialAccessories.setDesignCode(bomOrder.getDesignCode());
			specialAccessories.setBomId(bomOrder.getBomId());
			//特辅不同版本的关联id, bom升版本的时候需要从contextIdMap中获取
			if (CollUtil.isNotEmpty(contextIdMap) && Objects.nonNull(contextIdMap.get(req.getSkuId()))) {
				specialAccessories.setAccessoriesFlagId(contextIdMap.get(req.getSkuId()));
			} else {
				long accessoriesFlagId = IdPool.getId();
				specialAccessories.setAccessoriesFlagId(accessoriesFlagId);
				contextIdMap.put(req.getSkuId(), accessoriesFlagId);
			}
			specialAccessories.setState(SpecialAccessoriesStateEnum.SUBMIT.getCode());
			if (CollectionUtil.isNotEmpty(req.getPictureList())) {
				specialAccessories.setSkuPicture(String.join(",", req.getPictureList()));
			}
			specialAccessories.setCreatorName(userContent.getCurrentUserName());
			specialAccessories.setReviserName(userContent.getCurrentUserName());
			specialAccessories.setReviserId(userContent.getCurrentUserId());
			specialAccessories.setRevisedTime(now);
			specialAccessories.setIsDeleted(Bool.NO.getCode());
			specialAccessories.setCreatorId(userContent.getCurrentUserId());
			specialAccessories.setCreatedTime(now);

			//维护核算提交的新增特辅映射关系
			if (!Objects.equals(bomOrder.getMaterialSearchState(), Bool.YES.getCode())) {
				CheckCountMaterialOperateVo.SpecialMaterial specialMaterialVo = new CheckCountMaterialOperateVo.SpecialMaterial();
				specialMaterialVo.setCheckCountUnitId(req.getCheckCountUnitId());
				specialMaterialVo.setSpecialAccessoriesId(specialAccessoriesId);
				newSpecialMaterialList.add(specialMaterialVo);
			}
			//备注
			// if (CollectionUtil.isNotEmpty(req.getMaterialRemarkList())) {
			// 	req.getMaterialRemarkList().stream().sorted(Comparator.comparing(SpecialAccessoriesUpdateReq.RemarkReq::getCreatedTime))
			// 			.forEach(remarkReq -> {
			// 				DesignRemarks designRemarks = buildDesignRemarks(bomOrder, prototypeHistory, specialAccessories.getSpecialAccessoriesId(), remarkReq, userContent);
			// 				newDesignRemarksList.add(designRemarks);
			// 			});
			// }
			return specialAccessories;
		}).collect(Collectors.toList());

		specialAccessoriesRepository.saveBatch(newSpecialAccessoriesList);
		// designRemarksRepository.saveBatch(newDesignRemarksList);

		return newSpecialAccessoriesList;
	}


	private BomOrder findNewBomOrder(SpecialAccessoriesUpdateReq updateReq,
									 String designCode,
									 BomOrder latestBomOrder,
									 Map<Long, Long> contextIdMap,
									 List<CheckCountMaterialOperateVo.SpecialMaterial> newSpecialMaterialList,
									 BomCopyInfoDto bomCopyInfoDto) {
		BomOrder newBomOrder = null;
		if (Objects.equals(latestBomOrder.getState(), BomOrderStateEnum.WAIT_SUBMIT.getCode()) || Objects.equals(latestBomOrder.getState(), BomOrderStateEnum.CLOSED.getCode())) {
			throw new SdpDesignException("bom单状态不是已提交/已核算, 不能操作特辅, skc:" + designCode);
		}

		//bom已提交/已核算 操作特辅, 要升版本
		BomOrder latestSubmitBom = bomOrderRepository.getLatestSubmitBomByDesignCode(designCode);
		SdpDesignException.notNull(latestSubmitBom, "当前skc不存在已提交的bom单, skc:{}", designCode);

		//若bom为已提交-找料中,对上个已提交的无找料中的bom复制
		// (例如: bom-1:已提交; bom-2:已提交-找料中; 对bom-1进行复制升版本为bom-2:已提交; 原bom-2更新版本为-3, 更新特辅)
		if (Objects.equals(latestBomOrder.getMaterialSearchState(), Bool.YES.getCode())) {
			newBomOrder = this.copyTwoVersion(updateReq, latestBomOrder, latestSubmitBom, contextIdMap, newSpecialMaterialList, bomCopyInfoDto);
		}

		//bom已提交-无找料; 升版本
		else {
			log.info("=== 特辅更新, bom已提交-无找料,升版本: bomId:{} versionNum: {} ===", latestBomOrder.getBomId(), latestBomOrder.getVersionNum() + 1);
			newBomOrder = bomOperateService.copyNewVersionBom(latestBomOrder, true, latestBomOrder.getVersionNum() + 1, BomOrderStateEnum.CALCULATED, bomCopyInfoDto);
			//同步信息到推款-生产资料
			this.syncBom2product(newBomOrder);
		}

		return newBomOrder;
	}


	private BomOrder copyTwoVersion(SpecialAccessoriesUpdateReq updateReq, BomOrder latestBomOrder, BomOrder latestSubmitBom, Map<Long, Long> contextIdMap, List<CheckCountMaterialOperateVo.SpecialMaterial> newSpecialMaterialList, BomCopyInfoDto bomCopyInfoDto) {
		PrototypeHistory prototypeHistory;
		BomOrder newBomOrder;
		log.info("=== 特辅更新, bom找料中,复制新版本,before: bomId:{} versionNum: {} ===", latestSubmitBom.getBomId(), latestSubmitBom.getVersionNum());
		//对最新已提交无找料中的bom进行复制,并更新特辅
		BomOrder newSubmitBom = bomOperateService.copyNewVersionBom(latestSubmitBom, false, latestBomOrder.getVersionNum(), BomOrderStateEnum.CALCULATED, bomCopyInfoDto);
		prototypeHistory = prototypeHistoryRepository.getById(newSubmitBom.getPrototypeId());

		//新增更新的特辅添加的新版本
		this.saveAndUpdateSpecial(updateReq, newSubmitBom, prototypeHistory, contextIdMap, newSpecialMaterialList);
		log.info("=== 特辅更新, bom找料中,复制新版本,after: bomId:{} versionNum: {} ===", newSubmitBom.getBomId(), newSubmitBom.getVersionNum());

		//新版本, 推送致景
		bomOperateService.pushBom2Zj(newSubmitBom, PushZjTypeEnum.CHECK_COUNT);

		//同步信息到推款-生产资料
		this.syncBom2product(newSubmitBom);

		//特辅还要维护到最新找料中的bom中
		newBomOrder = latestBomOrder;

		//当前最新的找料中bom单版本+1
		Integer newVersionNum = newBomOrder.getVersionNum() + 1;
		newBomOrder.setVersionNum(newVersionNum);
		BomOrder updateBom = new BomOrder();
		updateBom.setBomId(newBomOrder.getBomId());
		updateBom.setVersionNum(newVersionNum);
		updateBom.setSubmitTime(LocalDateTime.now());
		bomOrderRepository.updateById(updateBom);
		log.info("=== 特辅更新, bom找料中,更新bom单最新版本: bomId:{} versionNum: {} ===", newBomOrder.getBomId(), newBomOrder.getVersionNum());

		return newBomOrder;
	}

	private void saveAndUpdateSpecial(SpecialAccessoriesUpdateReq updateReq,
									  BomOrder newBomOrder,
									  PrototypeHistory prototypeHistory,
									  Map<Long, Long> contextIdMap, List<CheckCountMaterialOperateVo.SpecialMaterial> newSpecialMaterialList) {

		UserContent userContent = UserContentHolder.get();
		LocalDateTime now = LocalDateTime.now();

		//新增特辅
		List<SpecialAccessories> insertList = this.insert(updateReq, newBomOrder, prototypeHistory, userContent, contextIdMap, newSpecialMaterialList);

		//更新特辅
		// this.updateSpecial(updateReq, newBomOrder, prototypeHistory, userContent, now);
	}


	private void updateSpecial(SpecialAccessoriesUpdateReq updateReq,
							   BomOrder newBomOrder,
							   PrototypeHistory prototypeHistory,
							   UserContent userContent,
							   LocalDateTime now) {
		List<SpecialAccessoriesUpdateReq.UpdateReq> updateReqList = updateReq.getBatchUpdateReq();
		if (CollectionUtil.isEmpty(updateReqList)) {
			log.info("【批量更新特殊辅料】designCode:{} 参数为空,不处理", updateReq.getDesignCode());
			return;
		}

		Map<Long, SpecialAccessories> specialAccessoriesMap = new HashMap<>(16);
		if (Objects.nonNull(newBomOrder.getBomId())) {
			List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.getListByBomId(newBomOrder.getBomId());
			if (CollectionUtil.isNotEmpty(specialAccessoriesList)) {
				specialAccessoriesMap = specialAccessoriesList.stream().collect(Collectors.toMap(SpecialAccessories::getAccessoriesFlagId, Function.identity(), (k1, k2) -> k1));
			}
		}
		Map<Long, SpecialAccessories> finalSpecialAccessoriesMap = specialAccessoriesMap;

		List<Long> specialAccessoriesIds = updateReqList.stream().map(SpecialAccessoriesUpdateReq.UpdateReq::getSpecialAccessoriesId).collect(Collectors.toList());

		List<SpecialAccessories> specialList = specialAccessoriesRepository.getListBySpecialAccessoriesIds(specialAccessoriesIds);
		if (CollUtil.isEmpty(specialList)) {
			return;
		}
		Map<Long, SpecialAccessories> oldSpecialAccessoriesMap = specialList.stream().collect(Collectors.toMap(SpecialAccessories::getSpecialAccessoriesId, Function.identity(), (k1, k2) -> k1));

		List<DesignRemarks> newDesignRemarksList = new ArrayList<>(100);
		Set<Long> skuIds = specialList.stream().map(SpecialAccessories::getSkuId).collect(Collectors.toSet());
		List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(skuIds);
		Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus)
				.flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));

		List<SpecialAccessories> updateSpecialAccessoriesList = updateReqList.stream().map(req -> {
			SpecialAccessories oldSpecialAccessories = oldSpecialAccessoriesMap.get(req.getSpecialAccessoriesId());
			SpecialAccessories specialAccessories = finalSpecialAccessoriesMap.get(oldSpecialAccessories.getAccessoriesFlagId());

			ProductSkuVo productSkuVo = accessoriesSkuMap.get(specialAccessories.getSkuId());

			SpecialAccessories newSpecial = SpecialAccessories.builder()
					.specialAccessoriesId(specialAccessories.getSpecialAccessoriesId())
					.partUse(req.getPartUse())
					.cuttingMethod(req.getCuttingMethod())
					.reviserName(userContent.getCurrentUserName())
					.reviserId(userContent.getCurrentUserId()).revisedTime(now)
					.build();
			if (Objects.nonNull(productSkuVo)) {
				newSpecial.setMinPrice(productSkuVo.getMinPrice());
				newSpecial.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());
				newSpecial.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
				newSpecial.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
			}

			// if (CollectionUtil.isNotEmpty(req.getMaterialRemarkList())) {
			// 	req.getMaterialRemarkList().stream().sorted(Comparator.comparing(SpecialAccessoriesUpdateReq.RemarkReq::getCreatedTime))
			// 			.forEach(remarkReq -> {
			// 				DesignRemarks designRemarks = buildDesignRemarks(newBomOrder, prototypeHistory, specialAccessories.getSpecialAccessoriesId(), remarkReq, userContent);
			// 				newDesignRemarksList.add(designRemarks);
			// 			});
			// }
			return newSpecial;
		}).collect(Collectors.toList());

		this.updateSpecial(updateSpecialAccessoriesList, userContent);

		// designRemarksRepository.saveBatch(newDesignRemarksList);
	}


	private void updateSpecial(List<SpecialAccessories> specialUpdateList, UserContent userContent) {
		if (CollUtil.isEmpty(specialUpdateList)) {
			return;
		}
		specialUpdateList.forEach(item -> {
			specialAccessoriesRepository.lambdaUpdate()
					.set(StringUtils.isNotBlank(item.getPartUse()), SpecialAccessories::getPartUse, item.getPartUse())
					.set(SpecialAccessories::getMinPrice, item.getMinPrice())
					.set(SpecialAccessories::getPriceInvalidTime, item.getPriceInvalidTime())
					.set(SpecialAccessories::getSamplePurchasingCycle, item.getSamplePurchasingCycle())
					.set(SpecialAccessories::getBulkPurchasingCycle, item.getBulkPurchasingCycle())
					.set(SpecialAccessories::getRevisedTime, LocalDateTime.now())
					.set(SpecialAccessories::getReviserId, userContent.getCurrentUserId())
					.set(SpecialAccessories::getReviserName, userContent.getCurrentUserName())
					.eq(SpecialAccessories::getSpecialAccessoriesId, item.getSpecialAccessoriesId())
					.update();
		});
	}



	private void syncBom2product(BomOrder newBomOrder) {
		if (Objects.isNull(newBomOrder)) {
			return;
		}
		String designCode = newBomOrder.getDesignCode();
		// BOM提交同步信息到推款-生产资料
		Map<String, Object> mqContent = new HashMap<>(16);
		Prototype prototype = prototypeRepository.getByDesignCode(designCode);
		mqContent.put("styleCode", prototype.getStyleCode());
		mqContent.put("designCode", designCode);
		mqContent.put("bomId", newBomOrder.getBomId());
		MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.BOM_SUBMIT_SYNC_MEANS_OF_PRODUCTION,
				DesignMqConstant.ORDER_UPDATE_MEANS_OF_PRODUCTION_EXCHANGE, null,
				JSON.toJSONString(mqContent));
		log.info("【特殊辅料-同步生产资料】mqMessageReq:{}", JSON.toJSONString(mqMessageReq));
		mqProducer.sendOnAfterCommit(mqMessageReq);
	}
}
