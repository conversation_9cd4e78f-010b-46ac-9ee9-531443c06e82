package tech.tiangong.sdp.design.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import team.aikero.blade.file.FileInfos;
import team.aikero.blade.oss.OssTemplate;
import tech.tiangong.bfg.sdk.client.FmModelTeamLibraryFileClient;
import tech.tiangong.bfg.sdk.client.FmSceneInfoClient;
import tech.tiangong.sdp.design.excel.design_demand.*;
import tech.tiangong.sdp.design.excel.design_demand.export.DesignDemandExportData;
import tech.tiangong.sdp.design.excel.design_demand.export.DropdownDataValidationHandler;
import tech.tiangong.sdp.design.excel.design_demand.export.ExcelStyleHandler;
import tech.tiangong.sdp.design.excel.design_demand.export.HeaderRowStyleHandler;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.remote.UacsRemoteHelper;
import tech.tiangong.sdp.design.repository.DesignDemandDetailRepository;
import tech.tiangong.sdp.design.repository.DesignDemandRepository;
import tech.tiangong.sdp.design.repository.DesignLogRepository;
import tech.tiangong.sdp.material.sdk.service.remote.DesignerRemoteService;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DesignDemandImportService {
    private final OssTemplate ossTemplate;
    private final DesignDemandRepository designDemandRepository;
    private final DesignDemandDetailRepository designDemandDetailRepository;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final FmModelTeamLibraryFileClient fmModelTeamLibraryFileClient;
    private final FmSceneInfoClient fmSceneInfoClient;
    private final DesignerRemoteService designerRemoteService;
    private final UacsRemoteHelper uacsRemoteHelper;
    private final DesignLogRepository designLogRepository;
    private final PopProductHelper popProductHelper;

    private final static String KEY_PREFIX = "design-demand-excel/";

    /**
     * 从url导入Excel
     *
     */
    public DesignDemandListener.ProcessResult importDesignDemand(String url) {
        URI uri = URI.create(url);
        try (var excelStream = uri.toURL().openStream()) {

            var bufferedStream = new BufferedInputStream(excelStream);

            var imageMapping = WPSExcelUtil.getImageMapping(bufferedStream);

            var imageStringMapping = getImageStringMapping(imageMapping);

            try (var newExcelStream = uri.toURL().openStream()) {
                DesignDemandListener designDemandListener =
                        new DesignDemandListener(
                                designDemandRepository,
                                designDemandDetailRepository,
                                dictValueRemoteHelper,
                                fmModelTeamLibraryFileClient,
                                fmSceneInfoClient,
                                designerRemoteService,
                                uacsRemoteHelper,
                                designLogRepository,
                                popProductHelper
                        );

                EasyExcel.read(newExcelStream, DesignDemandImportData.class, designDemandListener)
                        .registerConverter(new ImageImportConverter(imageStringMapping))
                        .sheet()
                        .doRead();

                return designDemandListener.getProcessResult();
            }
        } catch (IOException ex) {
            throw new RuntimeException("处理Excel文件失败", ex);
        }
    }

    private Map<String, String> getImageStringMapping(Map<String, byte[]> imageMapping) {
        return imageMapping.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(entry.getValue());
                            // 获取文件后缀
                            String extension = FileInfos.INSTANCE.type(byteArrayInputStream).getExtension();
                            return ossTemplate.upload(
                                    null,
                                    KEY_PREFIX + entry.getKey() + "." + extension,
                                    new ByteArrayInputStream(entry.getValue())
                            );
                        }
                ));
    }

    /**
     * 导出Excel模板到响应流
     */
    public void exportExcelToResponse(HttpServletResponse response) {
        String fileName = "设计需求导入模板.xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" +
                URLEncoder.encode(fileName, StandardCharsets.UTF_8)
        );
        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        try (ExcelWriter excelWriter =
                     EasyExcel.write(response.getOutputStream(), DesignDemandExportData.class)
                             .registerWriteHandler(new ExcelStyleHandler())
                             .registerWriteHandler(new HeaderRowStyleHandler())
                             .registerWriteHandler(new DropdownDataValidationHandler(dictValueRemoteHelper,popProductHelper))
                             .build()
        ) {
            WriteSheet writeSheet = EasyExcel.writerSheet("设计需求导入模板").build();
            excelWriter.write(Collections.emptyList(), writeSheet);

        } catch (Exception e) {
            log.error("导出Excel模板失败", e);
            throw new RuntimeException("导出Excel模板失败", e);
        }
    }

}
