package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.DesignDemandSuggestedMaterial;
import tech.tiangong.sdp.design.mapper.DesignDemandSuggestedMaterialMapper;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 设计需求_推荐物料表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class DesignDemandSuggestedMaterialRepository extends BaseRepository<DesignDemandSuggestedMaterialMapper, DesignDemandSuggestedMaterial> {


    public List<DesignDemandSuggestedMaterial> listByDemandId(Long designDemandId) {
        if (Objects.isNull(designDemandId)) {
            return Collections.emptyList();
        }
        return lambdaQuery().eq(DesignDemandSuggestedMaterial::getDesignDemandId, designDemandId).list();
    }

    public DesignDemandSuggestedMaterial getChosen(Long designDemandId) {
        if (Objects.isNull(designDemandId)) {
            return null;
        }

        return lambdaQuery()
                .eq(DesignDemandSuggestedMaterial::getDesignDemandId, designDemandId)
                .eq(DesignDemandSuggestedMaterial::getIsChosen, 1)
                .one();
    }
}
