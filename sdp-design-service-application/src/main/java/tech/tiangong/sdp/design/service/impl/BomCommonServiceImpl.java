package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.bean.Beans;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import com.yibuyun.scm.common.dto.fabric.response.CommoditySkuCollectionRespVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.BomOrderConverter;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.BomOrderMaterial;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.entity.SpecialAccessories;
import tech.tiangong.sdp.design.enums.CraftDemandStateEnum;
import tech.tiangong.sdp.design.enums.MaterialDemandTypeEnum;
import tech.tiangong.sdp.design.enums.SpecialAccessoriesStateEnum;
import tech.tiangong.sdp.design.remote.ProductRemoteHelper;
import tech.tiangong.sdp.design.repository.BomOrderMaterialRepository;
import tech.tiangong.sdp.design.repository.BomOrderRepository;
import tech.tiangong.sdp.design.repository.CraftDemandInfoRepository;
import tech.tiangong.sdp.design.repository.SpecialAccessoriesRepository;
import tech.tiangong.sdp.design.service.BomCommonService;
import tech.tiangong.sdp.design.service.BomMaterialDemandService;
import tech.tiangong.sdp.design.service.BomOrderService;
import tech.tiangong.sdp.design.service.MaterialSnapshotService;
import tech.tiangong.sdp.design.vo.req.DesignCodeReq;
import tech.tiangong.sdp.design.vo.req.bom_common.*;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialDemandVo;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderVo;
import tech.tiangong.sdp.design.vo.resp.bom_common.BomDetailVo;
import tech.tiangong.sdp.design.vo.resp.bom_common.BomMaterialVo;
import tech.tiangong.sdp.design.vo.resp.material.MaterialSnapshotVo;
import tech.tiangong.sdp.utils.Bool;
import tech.tiangong.sdp.utils.StreamUtil;

import java.util.*;
import java.util.stream.Collectors;

;

/**
 * bom-公共service
 *
 * <AUTHOR>
 * @date 2023/5/31 15:53
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BomCommonServiceImpl implements BomCommonService {

    private final BomOrderRepository bomOrderRepository;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final SpecialAccessoriesRepository specialAccessoriesRepository;
    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final MaterialSnapshotService materialSnapshotService;
    private final BomMaterialDemandService bomMaterialDemandService;
    private final ProductRemoteHelper productRemoteHelper;
    private final BomOrderService bomOrderService;


    @Override
    public List<BomOrderVo> getLatestBomOrder(DesignCodeReq req) {
        return bomOrderService.getLatestBomOrder(req);
    }

    @Override
    public BomDetailVo getBomDetailById(BomDetailQuery req) {
        SdpDesignException.notNull(req, "查询对象为空! ");
        BomOrder bomOrder = bomOrderRepository.getEntityByBomId(req.getBomId());
        if (Objects.isNull(bomOrder)) {
            return null;
        }

        List<BomDetailVo> bomDetailVoList = this.getBomDetailVoList(req.getQueryCraft(), req.getQueryLatestPicture(), List.of(bomOrder));
        if (CollUtil.isEmpty(bomDetailVoList)) {
            return null;
        }

        return bomDetailVoList.get(0);
    }

    @Override
    public List<BomDetailVo> listBomDetailById(BomDetailBatchQuery req) {
        List<BomOrder> bomOrderList = bomOrderRepository.listByIds(req.getBomIdList());
        if (CollUtil.isEmpty(bomOrderList)) {
            return Collections.emptyList();
        }

        List<BomDetailVo> bomDetailVoList = this.getBomDetailVoList(req.getQueryCraft(), req.getQueryLatestPicture(), bomOrderList);
        if (CollUtil.isEmpty(bomDetailVoList)) {
            return Collections.emptyList();
        }
        return bomDetailVoList;
    }

    @Override
    public BomDetailVo getLatestBomDetailBySkc(LatestBomDetailQuery req) {
        SdpDesignException.notNull(req, "查询对象为空! ");
        SdpDesignException.notBlank(req.getDesignCode(), "设计款号为空! ");

        LatestBomDetailBatchQuery batchQuery = new LatestBomDetailBatchQuery();
        BeanUtils.copyProperties(req, batchQuery);;
        batchQuery.setDesignCodeList(List.of(req.getDesignCode()));

        List<BomDetailVo> bomDetailVoList = this.listLatestBomDetailBySkc(batchQuery);
        if (CollUtil.isEmpty(bomDetailVoList)) {
            return null;
        }

        return bomDetailVoList.get(0);
    }

    @Override
    public List<BomDetailVo> listLatestBomDetailBySkc(LatestBomDetailBatchQuery req) {
        SdpDesignException.notNull(req, "查询对象为空! ");
        List<String> designCodeList = req.getDesignCodeList();
        bomOrderRepository.getListByDesignCodes(designCodeList);
        //skc下最新已提交/已核算的bom单
        List<BomOrder> latestBomOrderList = bomOrderRepository.listLatestSubmitBomOrder(designCodeList, req.getNoSearch());
        if (CollUtil.isEmpty(latestBomOrderList)) {
            return Collections.emptyList();
        }

        return this.getBomDetailVoList(req.getQueryCraft(), req.getQueryLatestPicture(), latestBomOrderList);
    }

    @Override
    public List<BomOrderVo> listSkcBom(SkcBomQuery req) {
        List<BomOrder> bomOrderList = bomOrderRepository.listByDesignCode(req.getDesignCode(), req.getNoSearch());
        return bomOrderList.stream()
                .map(item -> Beans.copyValue(item, new BomOrderVo()))
                .collect(Collectors.toList());
    }

    private List<BomDetailVo> getBomDetailVoList(Boolean queryCraft, Boolean queryLatestPicture, List<BomOrder> bomOrderList) {
        List<Long> bomIdList = bomOrderList.stream().map(BomOrder::getBomId).collect(Collectors.toList());

        //bom面辅料信息
        List<BomOrderMaterial> bomOrderMaterialList = bomOrderMaterialRepository.getListByBomIdList(bomIdList);
        Map<Long, List<BomOrderMaterial>> bomOrderMaterialMap = bomOrderMaterialList.stream().collect(Collectors.groupingBy(BomOrderMaterial::getBomId));
        //物料快照信息
        List<Long> materialSnapshotIds = bomOrderMaterialList.stream().map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
        List<MaterialSnapshotVo> materialSnapshotList = materialSnapshotService.listByIds(materialSnapshotIds);

        //bom找料需求
        List<BomMaterialDemandVo> bomMaterialDemandVoList = bomMaterialDemandService.listByBomIds(bomIdList);
        Map<Long, List<BomMaterialDemandVo>> materialDemandMap = bomMaterialDemandVoList.stream().collect(Collectors.groupingBy(BomMaterialDemandVo::getBomId));

        //特辅
        Map<Long, List<SpecialAccessories>> specialMap = specialAccessoriesRepository.listByBomIds(bomIdList, Bool.NO.getCode())
                .stream().collect(Collectors.groupingBy(SpecialAccessories::getBomId));

        //是否查询工艺
        Map<Long, List<CraftDemandInfo>> bomCraftMap = new HashMap<>();
        if (queryCraft) {
            List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getListByBomIdsAndState(bomIdList,
                    CraftDemandStateEnum.SUBMIT.getCode());
            bomCraftMap = craftDemandInfoList.stream().collect(Collectors.groupingBy(CraftDemandInfo::getBomId));
        }

        //查询最新物料图片
        Map<String, List<String>> commodityPicturesMap = new HashMap<>();
        if (queryLatestPicture) {
            //从供应链查询最新的面辅料信息
            Map<Integer, Set<Long>> skuIdMap = materialSnapshotList.stream()
                    .collect(Collectors.groupingBy(MaterialSnapshotVo::getMaterialType,
                            Collectors.collectingAndThen(Collectors.toSet(), e -> e.stream().map(MaterialSnapshotVo::getSkuId)
                                    .collect(Collectors.toSet()))));
            //需要查询的sku
            Map<String, Long> querySpuSkuMap = StreamUtil.list2MapWithValue(materialSnapshotList, MaterialSnapshotVo::getSpuSkuId, MaterialSnapshotVo::getMaterialSnapshotId);

            Set<Long> fabricSkuIdSet = skuIdMap.get(MaterialDemandTypeEnum.FABRIC.getCode());
            Set<Long> accessorySkuIdSet = skuIdMap.get(MaterialDemandTypeEnum.ACCESSORIES.getCode());
            List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfoList = productRemoteHelper.getFabricSkuInfo(fabricSkuIdSet, querySpuSkuMap);
            List<ProductSpuInfoVo> accessoriesSkuInfoList = productRemoteHelper.getAccessoriesSkuInfo(accessorySkuIdSet);

            commodityPicturesMap = BomOrderConverter.queryCommodityPicture(fabricSkuInfoList, accessoriesSkuInfoList, null, querySpuSkuMap);
        }

        //封装bom详情
        List<BomDetailVo> bomDetailVoList = new LinkedList<>();
        for (BomOrder bomOrder : bomOrderList) {
            BomDetailVo bomDetailVo = Beans.copyValue(bomOrder, new BomDetailVo());
            Long bomId = bomOrder.getBomId();
            List<BomOrderMaterial> materialList = Optional.ofNullable(bomOrderMaterialMap.get(bomId)).orElseGet(Collections::emptyList);
            List<BomMaterialDemandVo> materialDemandVoList = Optional.ofNullable(materialDemandMap.get(bomId)).orElseGet(Collections::emptyList);
            List<CraftDemandInfo> craftDemandInfoList = CollUtil.isEmpty(bomCraftMap)
                    ? Collections.emptyList() : Optional.ofNullable(bomCraftMap.get(bomId)).orElseGet(Collections::emptyList);

            //封装bom面辅料
            List<BomMaterialVo> bomMaterialVoList = BomOrderConverter.assembleBomDetailVo4Common(bomOrder, materialList,
                    materialDemandVoList, materialSnapshotList, craftDemandInfoList, commodityPicturesMap);

            bomDetailVo.setBomMaterialList(bomMaterialVoList);

            //特殊辅料信息
            List<SpecialAccessories> specialAccessories = specialMap.get(bomId);
            List<BomMaterialVo> specialAccessoriesInfo = this.getSpecialAccessoriesInfo(specialAccessories);
            bomDetailVo.getBomMaterialList().addAll(specialAccessoriesInfo);

            bomDetailVoList.add(bomDetailVo);
        }
        return bomDetailVoList;
    }

    private List<BomMaterialVo> getSpecialAccessoriesInfo(List<SpecialAccessories> specialAccessoriesList) {
        if (CollectionUtil.isEmpty(specialAccessoriesList)) {
            return Collections.emptyList();
        }

        return specialAccessoriesList.stream()
                .filter(special -> Objects.equals(special.getState(), SpecialAccessoriesStateEnum.SUBMIT.getCode()))
                .map(special -> {
                    BomMaterialVo bomOrderMaterialVo = new BomMaterialVo();
                    BeanUtils.copyProperties(special, bomOrderMaterialVo);
                    bomOrderMaterialVo.setBomMaterialId(special.getSpecialAccessoriesId());
                    bomOrderMaterialVo.setPrototypeMaterialName(special.getName());
                    bomOrderMaterialVo.setDemandType(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode());
                    bomOrderMaterialVo.setCommodityType(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.name());
                    bomOrderMaterialVo.setCommodityName(special.getSpuName());
                    bomOrderMaterialVo.setCommodityId(special.getSpuId());
                    if (StringUtils.isNotBlank(special.getSkuPicture())) {
                        bomOrderMaterialVo.setMatchPictureList(Arrays.stream(special.getSkuPicture().split(",")).collect(Collectors.toList()));
                    }
                    bomOrderMaterialVo.setCommodityCode(special.getSpuCode());
                    //大货进价取最小价格单位
                    bomOrderMaterialVo.setBulkPurchasePrice(special.getMinPrice());
                    bomOrderMaterialVo.setBulkPurchasePriceUnit(special.getMinPriceUnit());

                    return bomOrderMaterialVo;
                }).sorted(Comparator.comparing(BomMaterialVo::getPrototypeMaterialName)).collect(Collectors.toList());
    }

}
