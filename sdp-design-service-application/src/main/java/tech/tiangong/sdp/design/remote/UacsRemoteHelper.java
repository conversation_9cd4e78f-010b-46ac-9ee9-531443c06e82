package tech.tiangong.sdp.design.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import team.aikero.blade.core.protocol.DataResponse;
import team.aikero.blade.uacs.sdk.client.UserClient;
import team.aikero.blade.uacs.sdk.vo.UserVo;

import java.util.Collections;
import java.util.List;

/**
 * uacs用户相关服务接口
 */
@Service
@Slf4j
@AllArgsConstructor
public class UacsRemoteHelper {
    private final UserClient userClient;

    public List<UserVo> findByNames(List<String> nameList){
        log.info("=== 根据名称列表查询用户信息 req：{}; ===", JSONObject.toJSONString(nameList));
        try {
            DataResponse<List<UserVo>> response = userClient.findByNames(nameList);
            log.info("=== 根据名称列表查询用户信息 response:{}", JSON.toJSONString(response));
            return response.getData();
        } catch (Exception e) {
            log.error("根据名称列表查询用户信息-失败", e);
        }
        return Collections.emptyList();
    }
    public List<UserVo> findByCodes(List<String> userCodeList){
        log.info("=== 根据用户编码列表查询用户信息 req：{}; ===", JSONObject.toJSONString(userCodeList));
        try {
            DataResponse<List<UserVo>> response = userClient.findByCodes(userCodeList);
            log.info("=== 根据用户编码列表查询用户信息 response:{}", JSON.toJSONString(response));
            return response.getData();
        } catch (Exception e) {
            log.error("根据用户编码列表查询用户信息-失败", e);
        }
        return Collections.emptyList();
    }
}
