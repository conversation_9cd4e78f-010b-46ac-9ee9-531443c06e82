package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.query.PushZjLogQuery;
import tech.tiangong.sdp.design.vo.req.log.PushZjLogReq;
import tech.tiangong.sdp.design.vo.resp.PushZjLogVo;

/**
 * 数据推送致景记录表服务接口
 *
 * <AUTHOR>
 */
public interface PushZjLogService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<PushZjLogVo> page(PushZjLogQuery query);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(PushZjLogReq req);

}
