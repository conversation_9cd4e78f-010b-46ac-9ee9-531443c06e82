package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualTaskDetail;
import tech.tiangong.sdp.design.mapper.VisualTaskDetailMapper;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * (VisualTaskDetail)服务仓库类
 */
@Repository
public class VisualTaskDetailRepository extends BaseRepository<VisualTaskDetailMapper, VisualTaskDetail> {

    public VisualTaskDetail getByTaskId(Long taskId) {
        if(taskId == null){
            return null;
        }
        return getOne(new LambdaQueryWrapper<VisualTaskDetail>()
                .eq(VisualTaskDetail::getTaskId, taskId),false);
    }

    public List<VisualTaskDetail> listByTaskIds(Collection<Long> taskIds) {
        if(CollectionUtil.isEmpty(taskIds)){
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<VisualTaskDetail>()
                .in(VisualTaskDetail::getTaskId, taskIds));
    }
}
