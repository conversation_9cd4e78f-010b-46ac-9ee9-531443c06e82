package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.exception.BusinessException;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import cn.yibuyun.plm.design.open.vo.req.purchase.CancelMaterialOpenReq;
import cn.yibuyun.plm.design.open.vo.resp.design.AccessoriesMatchOrderCancelOpenResp;
import cn.yibuyun.plm.design.open.vo.resp.design.CuttingOrderCancelOpenResp;
import cn.yibuyun.plm.design.open.vo.resp.design.MaterialPurchaseFollowResultOpenResp;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.zjkj.scf.bundle.common.dto.cutting.dto.req.AccessoriesMatchOrderInnerCancelReq;
import com.zjkj.scf.bundle.common.dto.cutting.dto.req.OrderRemoteCancelReq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.clothes.client.SecondCraftDemandClient;
import tech.tiangong.sdp.clothes.vo.req.SecondCraftCancelReq;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.DesignLogService;
import tech.tiangong.sdp.design.service.MaterialPurchaseFollowService;
import tech.tiangong.sdp.design.vo.req.log.DesignLogReq;
import tech.tiangong.sdp.design.vo.req.material.OtherProcessCancelMaterialListReq;
import tech.tiangong.sdp.design.vo.req.ordermaterial.OrderMaterialStatusInnerReq;
import tech.tiangong.sdp.design.vo.req.purchase.CancelMaterialReq;
import tech.tiangong.sdp.design.vo.req.purchase.DesignCodePurchaseOrderReq;
import tech.tiangong.sdp.design.vo.req.purchase.MaterialPurchaseFollowPageReq;
import tech.tiangong.sdp.design.vo.resp.material.MaterialColorCardPictureVo;
import tech.tiangong.sdp.design.vo.resp.material.MaterialPurchaseFollowVo;
import tech.tiangong.sdp.design.vo.resp.material.OppositeColorVo;
import tech.tiangong.sdp.design.vo.resp.material.PurchaseApplyFollowCountVO;
import tech.tiangong.sdp.design.vo.resp.purchase.MaterialPurchaseFollowPageVO;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
* 面辅料采购跟进表
* <br>CreateDate August 10,2021
* <AUTHOR>
* @since 1.0
*/

@AllArgsConstructor
@Slf4j
@Service
public class MaterialPurchaseFollowServiceImpl implements MaterialPurchaseFollowService {

    private final MaterialPurchaseFollowRepository materialPurchaseFollowRepository;
    private final PurchaseApplyFollowRepository purchaseApplyFollowRepository;
    private final DesignLogService designLogService;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final PurchaseCancelRecordRepository purchaseCancelRecordRepository;
    private final PurchasePrototypeInfoRepository purchasePrototypeInfoRepository;
    private final SecondCraftDemandClient secondCraftDemandClient;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final PrototypeRepository prototypeRepository;
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;

    /**
     * 面辅料采购跟进列表
     *
     * @param req 参数
     * @return void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageRespVo<MaterialPurchaseFollowPageVO> pageList(MaterialPurchaseFollowPageReq req) {
        Page<MaterialPurchaseFollowPageVO> page;
        if (req.getPageSize() != -1) {
            page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        }else{
            page = new Page<>();
        }

        //统一为剪版单, 配版单的采购状态的查询
        this.resetPurchaseStateQuery(req);
        page = PageHelper.startPage(req.getPageNum(), req.getPageSize())
                .doSelectPage(() -> materialPurchaseFollowRepository.pageList(req));

        List<MaterialPurchaseFollowPageVO> followPageVOList = page.getResult();
        if (CollUtil.isNotEmpty(followPageVOList)) {
            //采购信息
            List<Long> purchaseFollowIdList = StreamUtil.convertListAndDistinct(followPageVOList, MaterialPurchaseFollowPageVO::getMaterialPurchaseFollowId);
            List<MaterialPurchaseFollow> purchaseFollowList = materialPurchaseFollowRepository.listByIds(purchaseFollowIdList);
            Map<Long, MaterialPurchaseFollow> purchaseFollowMap = StreamUtil.list2Map(purchaseFollowList, MaterialPurchaseFollow::getMaterialPurchaseFollowId);

            //skc取消信息
            List<Long> prototypeIdList = StreamUtil.convertListAndDistinct(followPageVOList, MaterialPurchaseFollowPageVO::getPrototypeId);
            List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIdList);
            Map<Long, PrototypeDetail> prototypeDetailMap = StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);

            //封装数据
            this.buildPageVo(followPageVOList, purchaseFollowMap, prototypeDetailMap);
        }

        PageInfo<MaterialPurchaseFollowPageVO> pageInfo = new PageInfo<>(page);
        Assert.isTrue(pageInfo != null, "分页返回参数为空");

        return PageRespVoHelper.of(pageInfo.getPageNum(), pageInfo.getTotal(), pageInfo.getList());
    }

    private void buildPageVo(List<MaterialPurchaseFollowPageVO> followPageVOList, Map<Long, MaterialPurchaseFollow> purchaseFollowMap, Map<Long, PrototypeDetail> prototypeDetailMap) {
        if (CollUtil.isEmpty(followPageVOList)) {
            return;
        }
        followPageVOList.forEach(item -> {
            MaterialPurchaseFollow purchaseFollow = purchaseFollowMap.get(item.getMaterialPurchaseFollowId());
            if (Objects.nonNull(purchaseFollow)) {
                item.setDesignCode(purchaseFollow.getDesignCode());
                item.setPurchaseOrderNo(purchaseFollow.getPurchaseOrderNo());
                item.setStatus(purchaseFollow.getStatus());
                item.setMaterialCategory(purchaseFollow.getMaterialCategory());
                item.setCuttingCode(purchaseFollow.getCuttingCode());
                item.setMaterialCode(purchaseFollow.getMaterialCode());
                item.setMaterialName(purchaseFollow.getMaterialName());
                item.setColorCardPictureUrl(purchaseFollow.getColorCardPictureUrl());
                item.setMaterialColor(purchaseFollow.getMaterialColor());
                item.setPurchaseApplyCause(purchaseFollow.getPurchaseApplyCause());
                item.setCuttingProcess(purchaseFollow.getCuttingProcess());
                item.setPurchaseQuantity(purchaseFollow.getPurchaseQuantity());
                item.setPurchaseUnit(purchaseFollow.getPurchaseUnit());
                item.setMaterialPurchaseStatus(purchaseFollow.getMaterialPurchaseStatus());
                item.setMaterialPurchaseStatusCode(purchaseFollow.getMaterialPurchaseStatusCode());
                item.setMatchPicture(purchaseFollow.getMatchPicture());
                item.setDemandType(purchaseFollow.getDemandType());
                item.setBomMaterialId(purchaseFollow.getBomMaterialId());
                item.setPlatformName(purchaseFollow.getPlatformName());
                item.setPurchaseRequestCode(purchaseFollow.getPurchaseRequestCode());
                item.setScatterCutRatio(purchaseFollow.getScatterCutRatio());
                item.setScatterCutPrice(purchaseFollow.getScatterCutPrice());
                item.setMaterialKittingCode(purchaseFollow.getMaterialKittingCode());
                item.setMaterialColorNo(purchaseFollow.getMaterialColorNo());
                item.setPurchaseCancelReason(purchaseFollow.getCancelReason());
                item.setPurchaseCancelUserName(purchaseFollow.getCancelUserName());
                item.setPurchaseCancelUserName(purchaseFollow.getCancelUserName());
            }

            PrototypeDetail prototypeDetail = prototypeDetailMap.get(item.getPrototypeId());
            if (Objects.nonNull(prototypeDetail)) {
                item.setCancelReason(prototypeDetail.getCancelReason());
                item.setCancelUserName(prototypeDetail.getCancelUserName());
            }
        });
    }

    private void resetPurchaseStateQuery(MaterialPurchaseFollowPageReq req) {
        //采购状态设置(前端传的是面料剪版单的采购状态, 要统一为剪版单, 配版单的采购状态
        List<Integer> purchaseStatusCodeList = req.getMaterialPurchaseStatusCodeList();
        if (CollUtil.isEmpty(purchaseStatusCodeList)) {
            return;
        }
        List<Integer> finalStatusCodeList = new LinkedList<>();
        purchaseStatusCodeList.forEach(item -> {
            finalStatusCodeList.add(item);
            MaterialPurchaseStatusEnum statusEnum = MaterialPurchaseStatusEnum.getByCode(item);
            if (Objects.isNull(statusEnum)) {
                return;
            }
            switch (statusEnum) {
                //待开单(1010)-90
                case BILLING -> finalStatusCodeList.add(MaterialPurchaseStatusEnum.PB_BILLING.getCode());
                //待提货(1030)-110
                case PENDING_TAKE_DELIVERY ->
                        finalStatusCodeList.add(MaterialPurchaseStatusEnum.PB_PENDING_TAKE_DELIVERY.getCode());
                //待发货(1050)-100
                case PENDING_DELIVERY ->
                        finalStatusCodeList.add(MaterialPurchaseStatusEnum.PB_PENDING_DELIVERY.getCode());
                //待签收(1060)-120
                case PENDING_SIGN_FOR ->
                        finalStatusCodeList.add(MaterialPurchaseStatusEnum.PB_PENDING_SIGN_FOR.getCode());
                //已完成(1100)-130
                case COMPLETED -> finalStatusCodeList.add(MaterialPurchaseStatusEnum.PB_COMPLETED.getCode());
                default -> {
                }
            }
        });
        req.setMaterialPurchaseStatusCodeList(finalStatusCodeList);
    }


    /**
     * 取消物料
     *
     * @param req 取消物料的参数
     * @return void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelMaterial(CancelMaterialReq req) {
        UserContent userContent = UserContentHolder.get();
        List<MaterialPurchaseFollow> purchaseCraftMaterialCancels = new ArrayList<>();

        List<MaterialPurchaseFollow> materialPurchaseFollows = materialPurchaseFollowRepository.list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                .eq(MaterialPurchaseFollow::getMaterialPurchaseFollowId, req.getMaterialPurchaseFollowId()));

        SdpDesignException.isTrue(checkIsCancel(materialPurchaseFollows),"有已取消的采购单，请重新选择要取消的采购单");
        log.info("..................开始取消物料操作..................");
        log.info("===================> 取消物料：{}", JSON.toJSON(materialPurchaseFollows));
        MaterialPurchaseFollow purchaseFollow = materialPurchaseFollows.getFirst();
        Prototype prototype = prototypeRepository.getByDesignCode(purchaseFollow.getDesignCode());
        SdpDesignException.notNull(prototype, "skc不存在!:{}", purchaseFollow.getDesignCode());

        if(CollectionUtil.isNotEmpty(materialPurchaseFollows)){
            //调用致景接口取消物料
            CancelMaterialOpenReq cancelMaterialOpenReq = new CancelMaterialOpenReq();
            cancelMaterialOpenReq.setBizChannel(prototype.getBizChannel());
            cancelMaterialOpenReq.setCancelReason(req.getCancelReason());
            cancelMaterialOpenReq.setMaterialPurchaseFollowId(req.getMaterialPurchaseFollowId());
            MaterialPurchaseFollowResultOpenResp cancelResponse = zjDesignRemoteHelper.cancelMaterial(cancelMaterialOpenReq);

            // 批量更新物料状态
            materialPurchaseFollows.forEach(materialPurchaseFollow ->{
                List<PurchaseApplyFollow> purchaseApplyFollows = purchaseApplyFollowRepository.list(Wrappers.<PurchaseApplyFollow>lambdaQuery()
                        .eq(PurchaseApplyFollow::getPurchaseOrderNo, materialPurchaseFollow.getPurchaseOrderNo()));
                if(CollectionUtil.isNotEmpty(purchaseApplyFollows)){
                    // 不等于大货样采购的
                    if(!SourceEnum.LARGE_CARGO_MAKING.getCode().equals(purchaseApplyFollows.get(0).getSource())
                            && PurchaseApplyStatusEnum.EFFECTIVE.getCode().equals(materialPurchaseFollow.getStatus())){
                        purchaseCraftMaterialCancels.add(materialPurchaseFollow);
                    }
                }
                materialPurchaseFollow.setCancelReason(req.getCancelReason());
                materialPurchaseFollow.setStatus(PurchaseApplyStatusEnum.CANCEL.getCode());
                materialPurchaseFollow.setCancelUserId(userContent != null ? userContent.getCurrentUserId():null);
                materialPurchaseFollow.setCancelUserName(userContent != null ? userContent.getCurrentUserName():"");
                materialPurchaseFollow.setCancelTime(LocalDateTime.now());
            });
            this.updateBatchById(materialPurchaseFollows);

            //记录取消失败日志
            //面料
            List<MaterialPurchaseFollow> fabricList = materialPurchaseFollows.stream().filter(materialPurchaseFollow ->
                    materialPurchaseFollow.getDemandType().equals(tech.tiangong.sdp.design.enums.MaterialDemandTypeEnum.FABRIC.getCode())).collect(Collectors.toList());
            // 辅料
            List<MaterialPurchaseFollow> accessoriesList = materialPurchaseFollows.stream().filter(materialPurchaseFollow ->
                    materialPurchaseFollow.getDemandType().equals(tech.tiangong.sdp.design.enums.MaterialDemandTypeEnum.ACCESSORIES.getCode())).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(fabricList)) {
                List<CuttingOrderCancelOpenResp> cuttingOrderCancelDataList = cancelResponse.getCuttingOrderCancelOpenResp();
                this.savePurchaseCancelRecord(fabricList,cuttingOrderCancelDataList,null);
            }
            if (CollUtil.isNotEmpty(accessoriesList)) {
                List<AccessoriesMatchOrderCancelOpenResp> accessoriesMatchOrderCancedataList = cancelResponse.getAccessoriesMatchOrderCancelOpenResp();
                this.savePurchaseCancelRecord(accessoriesList,null,accessoriesMatchOrderCancedataList);
            }

            // 查询采购单下的物料是否已经取消，如果物料都取消了，则更新采购申请跟进为已取消
            updatePurchaseFollowStatus(materialPurchaseFollows);

        }

        materialPurchaseFollows.forEach(materialPurchaseFollow -> {
            PurchaseApplyFollow purchaseApplyFollow = purchaseApplyFollowRepository.getOne(Wrappers.<PurchaseApplyFollow>lambdaQuery()
                    .eq(PurchaseApplyFollow::getPurchaseOrderNo,materialPurchaseFollow.getPurchaseOrderNo()));
            saveDesignLog(purchaseApplyFollow,"点击了【取消物料】");
        });
        // 取消普通物料采购的裁前工艺任务
        purchaseCraftCancel(purchaseCraftMaterialCancels,req.getCancelReason(),"取消物料");
        log.info("..................结束取消物料操作..................");
    }


    /**
     * 1、 关闭物料不需要调用供应履约SDK取消物料采购，而是通过MQ的方式发起取消采购通知信息
     * 2、 关闭物料需要调用供应履约SDK取消物料采购
     * @param req 参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeMaterialPurchaseWithOther(OtherProcessCancelMaterialListReq req) {
        UserContent userContent = UserContentHolder.get();
        // 关闭物料不需要调用供应履约SDK取消物料采购，而是通过MQ的方式发起取消采购通知信息
        if(req.getCloseMaterialPurchaseSource().getCode()
                .equals(CloseMaterialPurchaseSourceEnum.CLOSE_DESIGN_CODE.getCode())){
            Assert.isTrue(StringUtils.isNotEmpty(req.getDesignCode()),"设计款号为空");
            Assert.isTrue(StringUtils.isNotEmpty(req.getCancelReason()),"物料采购取消原因为空");
            List<MaterialPurchaseFollow> materialPurchaseFollows = materialPurchaseFollowRepository
                    .list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                            .eq(MaterialPurchaseFollow::getDesignCode, req.getDesignCode()));
            List<MaterialPurchaseFollow> materialPurchaseFollowList = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(materialPurchaseFollows)) {
                materialPurchaseFollows.forEach(materialPurchaseFollow -> {
                    if (materialPurchaseFollow.getStatus().equals(PurchaseApplyStatusEnum.EFFECTIVE.getCode())) {
                        materialPurchaseFollow.setCancelReason(req.getCancelReason());
                        materialPurchaseFollow.setCancelTime(LocalDateTime.now());
                        materialPurchaseFollow.setCancelUserId(userContent != null ? userContent.getCurrentUserId() : null);
                        materialPurchaseFollow.setCancelUserName(userContent != null ? userContent.getCurrentUserName() : "");
                        materialPurchaseFollow.setStatus(PurchaseApplyStatusEnum.CANCEL.getCode());
                    }
                    materialPurchaseFollowList.add(materialPurchaseFollow);
                });
                if (CollectionUtil.isNotEmpty(materialPurchaseFollowList)) {
                    this.updateBatchById(materialPurchaseFollowList);
                    // 查询采购单下的物料是否已经取消，如果物料都取消了，则更新采购申请跟进为已取消
                    updatePurchaseFollowStatus(materialPurchaseFollowList);
                }
            }
            // 关闭设计款号之后会关闭需求，需求下的工艺也都会关闭 --- 在这前面就已经有这个操作了，所以这里无需再次操作
        }


        if(req.getCloseMaterialPurchaseSource().getCode()
                .equals(CloseMaterialPurchaseSourceEnum.CLOSE_MATERIAL.getCode())){
            throw new BusinessException("当前功能已作废");

        }

    }

    @Override
    public void updateBatchById(List<MaterialPurchaseFollow> materialPurchaseFollows) {
        materialPurchaseFollowRepository.updateBatchById(materialPurchaseFollows);
    }

    /**
     * 校验是否有已取消的采购单
     *
     * @param materialPurchaseFollows 物料跟进单明细
     * @return boolean
     */
    public boolean checkIsCancel(List<MaterialPurchaseFollow> materialPurchaseFollows){
        for(MaterialPurchaseFollow f : materialPurchaseFollows){
            // 状态（剪版单的状态） 1|有效，0|取消
            if(PurchaseApplyStatusEnum.CANCEL.getCode().equals(f.getStatus())){
                return false;
            }
        }
        return true;
    }

    /**
     * 查询采购单下的物料是否已经取消，如果物料都取消了，则更新采购申请跟进为已取消
     * @param materialPurchaseFollowList 物料跟进单明细
     */
    public void updatePurchaseFollowStatus(List<MaterialPurchaseFollow> materialPurchaseFollowList){
        materialPurchaseFollowList.forEach(materialPurchaseFollow -> {
            if(StringUtils.isNotEmpty(materialPurchaseFollow.getPurchaseOrderNo())){
                List<MaterialPurchaseFollow> materialPurchaseFollows = materialPurchaseFollowRepository
                        .list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                        .eq(MaterialPurchaseFollow::getPurchaseOrderNo, materialPurchaseFollow.getPurchaseOrderNo()));
                List<MaterialPurchaseFollow> list = materialPurchaseFollows.stream().filter(v -> v.getStatus()
                        .equals(PurchaseApplyStatusEnum.EFFECTIVE.getCode())).collect(Collectors.toList());
                List<PurchaseApplyFollow> purchaseApplyFollows = purchaseApplyFollowRepository.list(Wrappers.<PurchaseApplyFollow>lambdaQuery()
                        .eq(PurchaseApplyFollow::getPurchaseOrderNo, materialPurchaseFollow.getPurchaseOrderNo()));
                if(CollectionUtil.isEmpty(list)){
                    PurchaseApplyFollow purchaseApplyFollow = purchaseApplyFollows.get(0);
                    PurchaseApplyFollow pf = new PurchaseApplyFollow();
                    pf.setPurchaseApplyFollowId(purchaseApplyFollow.getPurchaseApplyFollowId());
                    pf.setCancelReason(materialPurchaseFollow.getCancelReason());
                    pf.setCancelTime(LocalDateTime.now());
                    pf.setCancelUserId(materialPurchaseFollow.getCancelUserId());
                    pf.setCancelUserName(materialPurchaseFollow.getCancelUserName());
                    pf.setStatus(PurchaseApplyStatusEnum.CANCEL.getCode());
                    purchaseApplyFollowRepository.updateById(pf);
                }
            }
        });
    }

    /**
     * 添加操作日志
     *
     * @param purchaseApplyFollow 物料跟进单明细
     */
    public void saveDesignLog(PurchaseApplyFollow purchaseApplyFollow,String content){
        if(ObjectUtil.isEmpty(purchaseApplyFollow)){
            return;
        }
        PurchasePrototypeInfo purchasePrototypeInfo = purchasePrototypeInfoRepository.getById(purchaseApplyFollow.getPrototypeId());
        DesignLogReq designLogReq = DesignLogReq.builder().bizId(purchaseApplyFollow.getPurchaseApplyFollowId())
                .bizType(DesignLogBizTypeEnum.MATERIAL_PURCHASE)
                .designCode(purchaseApplyFollow.getDesignCode())
                .bizVersionNum(purchaseApplyFollow.getPurchaseCount())
                .content(content)
                .build();
        designLogService.saveDesignLog(designLogReq,purchasePrototypeInfo);
    }

    /**
     * 取消物料采购单
     * @param materialPurchaseFollows 物料跟进单明细
     * @param req 请求参数
     */
    /*
    public void cancelMaterialPurchaseFollow(List<MaterialPurchaseFollow> materialPurchaseFollows,
                                             OtherProcessCancelMaterialListReq req){
        UserContent userContent = UserContentHolder.get();
        List<MaterialPurchaseFollow> materialPurchaseFollowList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(materialPurchaseFollows)){
            materialPurchaseFollows.forEach(materialPurchaseFollow ->{
                if(materialPurchaseFollow.getStatus().equals(PurchaseApplyStatusEnum.EFFECTIVE.getCode())){
                    materialPurchaseFollow.setCancelReason(req.getCancelReason());
                    materialPurchaseFollow.setCancelTime(LocalDateTime.now());
                    materialPurchaseFollow.setCancelUserId(userContent != null ? userContent.getCurrentUserId():null);
                    materialPurchaseFollow.setCancelUserName(userContent != null ? userContent.getCurrentUserName():"");
                    materialPurchaseFollow.setStatus(PurchaseApplyStatusEnum.CANCEL.getCode());
                }
                materialPurchaseFollowList.add(materialPurchaseFollow);
            });
            if(CollectionUtil.isNotEmpty(materialPurchaseFollowList)){
                // TODO 通知供应履约取消对应的剪版单
                //面料
                List<MaterialPurchaseFollow> fabricList = materialPurchaseFollowList.stream().filter(materialPurchaseFollow ->
                        materialPurchaseFollow.getDemandType().equals(MaterialDemandTypeEnum.FABRIC.getCode())).collect(Collectors.toList());
                // 辅料
                List<MaterialPurchaseFollow> accessoriesList = materialPurchaseFollowList.stream().filter(materialPurchaseFollow ->
                        materialPurchaseFollow.getDemandType().equals(MaterialDemandTypeEnum.ACCESSORIES.getCode())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(fabricList)){
                    try{
                        List<OrderRemoteCancelReq> orderRemoteCancelReqs = orderRemoteCancelReq(fabricList,req.getCancelReason());
                        if(CollectionUtil.isNotEmpty(orderRemoteCancelReqs)){
                            com.zjkj.booster.common.protocol.DataResponse<List<CuttingOrderCancelResp>> listDataResponse = cuttingOrderInfoService
                                    .cancelBatch(RequestHeaderUtils.buildHeader(userContent.getCurrentUserId(),
                                            userContent.getCurrentUserName()), orderRemoteCancelReqs);
                            if(!listDataResponse.isSuccessful()){
                                log.error("调用供应履约的取消版单接口:{}",listDataResponse.getMessage());
                                throw new PlmDesignException("【取消采购单失败】" + listDataResponse.getMessage());
                            }
                            this.updateBatchById(fabricList);
                            // 取消剪版单记录表（供应履约返回的信息）,只记录失败的记录
                            List<CuttingOrderCancelResp> cuttingOrderCancelDataList = listDataResponse.getData();
                            savePurchaseCancelRecord(fabricList,cuttingOrderCancelDataList,null);
                        }
                    }catch (Exception e){
                        log.error("取消采购单请求异常:{}",e.getMessage());
                        throw new PlmDesignException("【取消采购单失败】" + e.getMessage(),e);
                    }
                }

                if(CollectionUtil.isNotEmpty(accessoriesList)){
                    try{
                        List<AccessoriesMatchOrderInnerCancelReq> accessoriesMatchOrderInnerCancelReqs
                                = accessoriesMatchOrderCancelReq(accessoriesList, req.getCancelReason());
                        com.zjkj.booster.common.protocol.DataResponse<List<AccessoriesMatchOrderCancelInnerVo>> listDataResponse
                                = accessoriesMatchOrderFeign.cancelBatch(accessoriesMatchOrderInnerCancelReqs);
                        if(!listDataResponse.isSuccessful()){
                            log.error("调用供应履约的取消辅料配版单接口:{}",listDataResponse.getMessage());
                            throw new PlmDesignException("【取消采购单失败】" + listDataResponse.getMessage());
                        }
                        this.updateBatchById(accessoriesList);
                        // 取消剪版单记录表（供应履约返回的信息）,只记录失败的记录
                        List<AccessoriesMatchOrderCancelInnerVo> accessoriesMatchOrderCancedataList = listDataResponse.getData();
                        savePurchaseCancelRecord(accessoriesList,null,accessoriesMatchOrderCancedataList);
                    }catch (Exception e){
                        log.error("取消采购单请求异常:{}",e.getMessage());
                        throw new PlmDesignException("【取消采购单失败】" +e.getMessage(),e);
                    }

                }
                // 查询采购单下的物料是否已经取消，如果物料都取消了，则更新采购申请跟进为已取消
                updatePurchaseFollowStatus(materialPurchaseFollowList);
            }
        }
    }

     */


    /**
     * 供应履约取消采购单参数（面料）
     * @param list 参数
     * @param cancelReason 取消原因
     * @return void
     */
    public List<OrderRemoteCancelReq> orderRemoteCancelReq(List<MaterialPurchaseFollow> list,
                                                           String cancelReason){
        List<OrderRemoteCancelReq> orderRemoteCancelReqList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(list)){
            list.forEach(v -> {
                OrderRemoteCancelReq cancelReq = new OrderRemoteCancelReq();
                cancelReq.setCancelReason(cancelReason);
                cancelReq.setOrderCode(v.getCuttingCode());
                cancelReq.setRemark(cancelReason);
                orderRemoteCancelReqList.add(cancelReq);
            });
        }
        return orderRemoteCancelReqList;
    }

    /**
     * 保存取消物料采购记录（失败的）
     * @param list 物料跟进单明细
     * @param dataList 剪版订单删除响应
     */
    public void savePurchaseCancelRecord(List<MaterialPurchaseFollow> list,List<CuttingOrderCancelOpenResp> dataList,
                                         List<AccessoriesMatchOrderCancelOpenResp> accessoriesMatchOrderCancedataList){
        if(CollectionUtil.isNotEmpty(list)){
            UserContent userContent = UserContentHolder.get();
            if(CollectionUtil.isNotEmpty(dataList)){
                Map<String, List<CuttingOrderCancelOpenResp>> collect = dataList.stream().collect(Collectors.groupingBy(CuttingOrderCancelOpenResp::getOrderCode));
                List<PurchaseCancelRecord> purchaseCancelRecords = new ArrayList<>();
                list.forEach(v ->{
                    List<CuttingOrderCancelOpenResp> cuttingOrderCancelResps = collect.get(v.getCuttingCode());
                    if(CollectionUtil.isNotEmpty(cuttingOrderCancelResps) && cuttingOrderCancelResps.get(0).getIsDel() == 0){
                        PurchaseCancelRecord record = new PurchaseCancelRecord();
                        record.setPurchaseCancelRecordId(IdPool.getId());
                        record.setPurchaseOrderNo(v.getPurchaseOrderNo());
                        // record.setTrackResultId(v.getTrackResultId());
                        record.setMaterialPurchaseStatus(v.getMaterialPurchaseStatus());
                        record.setCuttingCode(cuttingOrderCancelResps.get(0).getOrderCode());
                        // record.setMatchId(v.getMatchId());
                        record.setFailCause(cuttingOrderCancelResps.get(0).getCancelFailReason()); // 待添加
                        record.setMaterialFailStatus(ObjectUtil.isNotEmpty(cuttingOrderCancelResps.get(0).getOrderState()) ?
                                MaterialPurchaseStatusEnum.getByCode(cuttingOrderCancelResps.get(0).getOrderState()).getName(): "");
                        record.setOperatorId(userContent != null ? userContent.getCurrentUserId():null);
                        record.setOperatorTime(LocalDateTime.now());
                        purchaseCancelRecords.add(record);
                    }
                });
                if(CollectionUtil.isNotEmpty(purchaseCancelRecords)){
                    purchaseCancelRecordRepository.saveBatch(purchaseCancelRecords);
                }
            }
            if(CollectionUtil.isNotEmpty(accessoriesMatchOrderCancedataList)){
                Map<String, List<AccessoriesMatchOrderCancelOpenResp>> collect = accessoriesMatchOrderCancedataList.stream()
                        .collect(Collectors.groupingBy(AccessoriesMatchOrderCancelOpenResp::getOrderCode));
                List<PurchaseCancelRecord> purchaseCancelRecords = new ArrayList<>();
                list.forEach(v ->{
                    List<AccessoriesMatchOrderCancelOpenResp> accessoriesMatchOrderCancelInnerVos = collect.get(v.getCuttingCode());
                    if(CollectionUtil.isNotEmpty(accessoriesMatchOrderCancelInnerVos) && accessoriesMatchOrderCancelInnerVos.get(0).getIsDel() == 0){
                        PurchaseCancelRecord record = new PurchaseCancelRecord();
                        record.setPurchaseCancelRecordId(IdPool.getId());
                        record.setPurchaseOrderNo(v.getPurchaseOrderNo());
                        // record.setTrackResultId(v.getTrackResultId());
                        record.setMaterialPurchaseStatus(v.getMaterialPurchaseStatus());
                        record.setCuttingCode(accessoriesMatchOrderCancelInnerVos.get(0).getOrderCode());
                        // record.setMatchId(v.getMatchId());
                        record.setFailCause(accessoriesMatchOrderCancelInnerVos.get(0).getCancelFailReason());
                        record.setMaterialFailStatus(ObjectUtil.isNotEmpty(accessoriesMatchOrderCancelInnerVos.get(0).getMatchPlateState()) ?
                                MaterialPurchaseStatusEnum.getByCode(accessoriesMatchOrderCancelInnerVos.get(0).getMatchPlateState()).getName() : "");
                        record.setOperatorId(userContent != null ? userContent.getCurrentUserId():null);
                        record.setOperatorTime(LocalDateTime.now());
                        purchaseCancelRecords.add(record);
                    }
                });
                if(CollectionUtil.isNotEmpty(purchaseCancelRecords)){
                    purchaseCancelRecordRepository.saveBatch(purchaseCancelRecords);
                }
            }

        }

    }


    public List<AccessoriesMatchOrderInnerCancelReq> otherProcessAccessoriesMatchOrderCancelReq(List<MaterialPurchaseFollow> list,
                                                                                                OtherProcessCancelMaterialListReq req){
        List<AccessoriesMatchOrderInnerCancelReq> accessoriesMatchOrderCancelReqList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(list)){
            list.forEach(v -> {
                AccessoriesMatchOrderInnerCancelReq accessoriesMatchOrderInnerCancelReq = new AccessoriesMatchOrderInnerCancelReq();
                accessoriesMatchOrderInnerCancelReq.setReason(req.getCancelReason());
                accessoriesMatchOrderInnerCancelReq.setOrderCode(v.getCuttingCode());
                accessoriesMatchOrderInnerCancelReq.setRemark(req.getCancelReason());
                accessoriesMatchOrderCancelReqList.add(accessoriesMatchOrderInnerCancelReq);
            });
        }
        return accessoriesMatchOrderCancelReqList;
    }

    public List<AccessoriesMatchOrderInnerCancelReq> accessoriesMatchOrderCancelReq(List<MaterialPurchaseFollow> list,
                                                                                   String cancelReason){
        List<AccessoriesMatchOrderInnerCancelReq> accessoriesMatchOrderCancelReqList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(list)){
            list.forEach(v -> {
                AccessoriesMatchOrderInnerCancelReq accessoriesMatchOrderInnerCancelReq = new AccessoriesMatchOrderInnerCancelReq();
                accessoriesMatchOrderInnerCancelReq.setReason(cancelReason);
                accessoriesMatchOrderInnerCancelReq.setOrderCode(v.getCuttingCode());
                accessoriesMatchOrderInnerCancelReq.setRemark(cancelReason);
                accessoriesMatchOrderCancelReqList.add(accessoriesMatchOrderInnerCancelReq);
            });
        }
        return accessoriesMatchOrderCancelReqList;
    }


    @Override
    public boolean isPurchase(OrderMaterialStatusInnerReq req) {
        SdpDesignException.isTrue(StringUtil.isNotEmpty(req.getProcessCode()), "加工单号为空");
        List<PurchasePrototypeInfo> purchasePrototypeInfoList = purchasePrototypeInfoRepository.list(
                Wrappers.<PurchasePrototypeInfo>lambdaQuery()
                        .eq(PurchasePrototypeInfo::getProcessCode, req.getProcessCode()));
        log.info(" =========================>  根据加工单号查询采购版单信息:{}",JSON.toJSONString(purchasePrototypeInfoList));
        if(CollectionUtil.isEmpty(purchasePrototypeInfoList)){
            return false;
        }

        Set<Long> ids = purchasePrototypeInfoList.stream().map(PurchasePrototypeInfo::getPurchasePrototypeInfoId).collect(Collectors.toSet());

        List<MaterialPurchaseFollow> list = materialPurchaseFollowRepository.list(
                Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                        .in(MaterialPurchaseFollow::getPrototypeId, ids));
        return CollectionUtil.isNotEmpty(list) ? true :false;

    }

    @Override
    public Map<String, List<MaterialPurchaseFollowVo>> materialPurchaseByDesignCode(DesignCodePurchaseOrderReq req) {
        List<MaterialPurchaseFollowVo> materialPurchaseFollowVOList = new ArrayList<>();
        List<MaterialPurchaseFollow> list = materialPurchaseFollowRepository.list(
                Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                        .in(MaterialPurchaseFollow::getDesignCode, req.getDesignCodeList())
                        .eq(ObjectUtil.isNotEmpty(req.getStatus()),MaterialPurchaseFollow::getStatus,req.getStatus())
                        .eq(MaterialPurchaseFollow::getIsDeleted, Bool.NO.getCode()));
        if(CollectionUtil.isEmpty(list)){
            return null;
        }
        list.forEach(materialPurchaseFollow -> {
            MaterialPurchaseFollowVo vo = new MaterialPurchaseFollowVo();
            BeanUtils.copyProperties(materialPurchaseFollow,vo);
            materialPurchaseFollowVOList.add(vo);
        });
        return materialPurchaseFollowVOList.stream()
                .collect(Collectors.groupingBy(MaterialPurchaseFollowVo::getDesignCode));
    }

    public void purchaseCraftCancel(List<MaterialPurchaseFollow> purchaseCraftMaterialCancels,String cancelReason,String content){
        log.info("===========================> 取消物料的裁前工艺任务，相关物料信息("+content+")：{}", JSONUtil.toJsonStr(purchaseCraftMaterialCancels));
        if(CollectionUtil.isEmpty(purchaseCraftMaterialCancels)){
            return ;
        }
        SecondCraftCancelReq secondCraftCancelReq = new SecondCraftCancelReq();
        List<String> orderCodeList = new ArrayList<>();
        purchaseCraftMaterialCancels.forEach(vo -> {
            orderCodeList.add(vo.getCuttingCode());
        });
        secondCraftCancelReq.setOrderCodeList(orderCodeList);
        secondCraftCancelReq.setReason(cancelReason);

        String message = "";

        try{
            cn.yibuyun.framework.net.DataResponse<Void> voidDataResponse = secondCraftDemandClient.purchaseCraftCancel(secondCraftCancelReq);
            log.info(" ====================>  ("+content+")取消物料的裁前工艺任务是否成功：{}，response信息 ：{}",
                    voidDataResponse.isSuccessful(),voidDataResponse.getMessage());
            if(!voidDataResponse.isSuccessful()){
                message = voidDataResponse.getMessage();
            }
            SdpDesignException.isTrue(voidDataResponse.isSuccessful(),"物料的裁前工艺任务取消失败:" + message);
        }catch (Exception e){
            message = StringUtil.isNotEmpty(message)? message : e.getMessage();
            log.info(" ====================>  ("+content+")取消物料的裁前工艺任务异常:{},{}",message,e.getMessage());
            throw new SdpDesignException("【("+content+")取消物料的裁前工艺任务异常】" + message,e);
        }
    }

    @Override
    public List<PurchaseApplyFollowCountVO> purchaseCountByMaterialSnapshotIds(Set<Long> materialSnapshotIdSet) {
        if (CollectionUtils.isEmpty(materialSnapshotIdSet)) {
            return Collections.emptyList();
        }
        //历史物料(走履约匹配的), 需要根据matchId来统计采购次数, materialPurchaseFollow表中历史数据的material_snapshot_id就是matchId
        List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(materialSnapshotIdSet);
        if (CollectionUtils.isEmpty(snapshotList)) {
            return Collections.emptyList();
        }
        Set<Long> newIdList = new HashSet<>(materialSnapshotIdSet.size());
        for (MaterialSnapshot snapshot : snapshotList) {
            Long materialSnapshotId = snapshot.getMaterialSnapshotId();
            //历史物料(走履约匹配的), 取matchId
            if (Objects.equals(snapshot.getNewMaterial(), Bool.NO.getCode()) && Objects.nonNull(snapshot.getMatchId())) {
                materialSnapshotId = snapshot.getMatchId();
            }
            newIdList.add(materialSnapshotId);
        }

        return materialPurchaseFollowRepository.purchaseCountByMaterialSnapshotIds(newIdList);
    }

    public void queryOppositeColorMfNews(List<MaterialPurchaseFollow> mfNews,List<OppositeColorVo> oppositeColorVos){
        List<BomOrderMaterial> bomOrderMaterialList = null;
        if(CollectionUtil.isNotEmpty(mfNews)){
            List<Long> materialSnapshotIdList = mfNews.stream().map(MaterialPurchaseFollow::getMaterialSnapshotId).collect(Collectors.toList());
            bomOrderMaterialList = bomOrderMaterialRepository.list(Wrappers.<BomOrderMaterial>lambdaQuery().in(BomOrderMaterial::getMaterialSnapshotId, materialSnapshotIdList));
        }
        if(CollectionUtil.isNotEmpty(bomOrderMaterialList)){
            Map<Long, List<BomOrderMaterial>> bomOrderMaterialMap = bomOrderMaterialList.stream().collect(Collectors.groupingBy(BomOrderMaterial::getMaterialSnapshotId));
            mfNews.forEach(mf -> {
                OppositeColorVo vo = new OppositeColorVo();
                List<BomOrderMaterial> bomOrderMaterials = bomOrderMaterialMap.get(mf.getMaterialSnapshotId());
                vo.setIsOppositeColors(CollectionUtil.isNotEmpty(bomOrderMaterials) ? Boolean.TRUE:Boolean.FALSE);
                vo.setOppositeColors(CollectionUtil.isNotEmpty(bomOrderMaterials) ? bomOrderMaterials.get(0).getColorMatch():"");
                vo.setOrderCode(mf.getCuttingCode());
                oppositeColorVos.add(vo);
            });
        }
    }

    /**
     * 获取采购物料最新维护的色卡图片
     *
     * @param materialSnapshotIds 物料快照id
     * @return List<MaterialPurchaseFollow>
     */
    @Override
    public List<MaterialColorCardPictureVo> materialColorCardPictureLatest(List<Long> materialSnapshotIds) {
        if(CollectionUtil.isEmpty(materialSnapshotIds)){
            return List.of();
        }

        //历史物料(走履约匹配的), 需要根据matchId来统计采购次数, materialPurchaseFollow表中历史数据的material_snapshot_id就是matchId
        List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(materialSnapshotIds);
        if (CollectionUtils.isEmpty(snapshotList)) {
            return Collections.emptyList();
        }
        Set<Long> newIdList = new HashSet<>(materialSnapshotIds.size());
        for (MaterialSnapshot snapshot : snapshotList) {
            Long materialSnapshotId = snapshot.getMaterialSnapshotId();
            //历史物料(走履约匹配的), 取matchId
            if (Objects.equals(snapshot.getNewMaterial(), Bool.NO.getCode()) && Objects.nonNull(snapshot.getMatchId())) {
                materialSnapshotId = snapshot.getMatchId();
            }
            newIdList.add(materialSnapshotId);
        }

        log.info("=========================> 色卡图片参数={}",JSONUtil.toJsonStr(newIdList));

        List<MaterialPurchaseFollow> list = materialPurchaseFollowRepository.list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                .in(MaterialPurchaseFollow::getMaterialSnapshotId, newIdList));

        if(CollectionUtil.isEmpty(list)){
            return List.of();
        }
        Map<Long, List<MaterialPurchaseFollow>> mfMap = list.stream().collect(Collectors.groupingBy(MaterialPurchaseFollow::getMaterialSnapshotId));

        List<MaterialColorCardPictureVo> materialColorCardPictureVoList = new ArrayList<>();

        Set<Long> materialSnapshotIdList = mfMap.keySet();
        for(Long materialSnapshotId : materialSnapshotIdList){
            List<MaterialPurchaseFollow> materialPurchaseFollows = mfMap.get(materialSnapshotId);
            // 根据创建时间进行排序
            List<MaterialPurchaseFollow> collect = materialPurchaseFollows.stream().sorted(Comparator.comparing(MaterialPurchaseFollow::getCreatedTime).reversed()).collect(Collectors.toList());
            MaterialColorCardPictureVo vo = new MaterialColorCardPictureVo();
            vo.setMaterialSnapshotId(collect.get(0).getMaterialSnapshotId());
            // vo.setMatchId(collect.get(0).getMatchId());
            // vo.setIsOldData(collect.get(0).getIsOldData());
            log.info("=========================> 色卡图片={}",collect.get(0).getColorCardPictureUrl());
            if(StringUtils.isNotBlank(collect.get(0).getColorCardPictureUrl())){
                List<String> pictureUrls = Arrays.stream(collect.get(0).getColorCardPictureUrl().split(",")).collect(Collectors.toList());
                vo.setColorCardPictureUrls(pictureUrls);
            }
            materialColorCardPictureVoList.add(vo);
        }

        return materialColorCardPictureVoList;
    }

}