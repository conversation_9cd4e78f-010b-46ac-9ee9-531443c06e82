package tech.tiangong.sdp.design.entity;

import cn.yibuyun.framework.base.entity.BaseWithReviserEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import tech.tiangong.sdp.design.vo.dto.DesignDemandAttachmentBo;
import tech.tiangong.sdp.design.vo.req.demand.DesignDemandCreateReq;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 灵感设计需求_详情表实体类
 *
 * <AUTHOR>
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "design_demand_detail", autoResultMap = true)
public class DesignDemandDetail extends BaseWithReviserEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 设计需求详情主键id
     */
    @TableId(value = "design_demand_detail_id", type = IdType.INPUT)
    private Long designDemandDetailId;

    /**
     * 设计详情id
     */
    @TableField(value = "design_demand_id")
    private Long designDemandId;

    /**
     * 原图
     */
    @TableField(value = "original_image")
    private String originalImage;

    /**
     * 灵感图,多张,json集合
     */
    @TableField(value = "inspiration_image_list", typeHandler = JacksonTypeHandler.class)
    private List<String> inspirationImageList;

    /**
     * 灵感图对象，包含4k和原图,多张,json集合
     */
    @TableField(value = "inspiration_image_json", typeHandler = JacksonTypeHandler.class)
    private List<DesignDemandCreateReq.ImageInfo> inspirationImageJson;

    /**
     * 淘汰原因名称-ops
     */
    @TableField(value = "no_pass_reason_name")
    private String noPassReasonName;

    /**
     * 淘汰原因code
     */
    @TableField(value = "no_pass_reason_code")
    private String noPassReasonCode;

    /**
     * 淘汰人id
     */
    @TableField(value = "no_pass_user_id")
    private Long noPassUserId;

    /**
     * 淘汰人名称
     */
    @TableField(value = "no_pass_user_name")
    private String noPassUserName;

    /**
     * 淘汰时间
     */
    @TableField(value = "no_pass_time")
    private LocalDateTime noPassTime;

    /**
     * 需求类型: 0无需修图; 1多视图; 2修图; 3多视图+修图
     */
    @TableField(value = "demand_type")
    private Integer demandType;

    /**
     * aigc选款备注
     */
    @TableField(value = "aigc_remark")
    private String aigcRemark;

    /**
     * aigc选款备注附件信息
     */
    @TableField(value = "aigc_remark_attachment", typeHandler = JacksonTypeHandler.class)
    private List<DesignDemandAttachmentBo> aigcRemarkAttachment;

    /**
     * 模特图附件
     */
    @TableField(value = "model_attachments", typeHandler = JacksonTypeHandler.class)
    private List<DesignDemandAttachmentBo> modelAttachments;

    /**
     * 背景图附件
     */
    @TableField(value = "background_attachments", typeHandler = JacksonTypeHandler.class)
    private List<DesignDemandAttachmentBo> backgroundAttachments;

    /**
     * 姿势图附件
     */
    @TableField(value = "posture_attachments", typeHandler = JacksonTypeHandler.class)
    private List<DesignDemandAttachmentBo> postureAttachments;

    /**
     * 参考图dto
     * 格式如见 TryOnModelReferenceImageDTO
     */
    @TableField(value = "model_reference_image")
    private String modelReferenceImage;

    /**
     * 背景图dto
     * 格式如见 BackgroundDTO
     */
    @TableField(value = "background_Image")
    private String backgroundImage;

    /**
     * 模特脸图dto
     * 格式如见 ModelFaceDTO
     */
    @TableField(value = "model_face_image")
    private String modelFaceImage;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建人名称
     */
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    private String creatorName;

    /**
     * 修改人名称
     */
    @TableField(value = "reviser_name", fill = FieldFill.INSERT_UPDATE)
    private String reviserName;

}
