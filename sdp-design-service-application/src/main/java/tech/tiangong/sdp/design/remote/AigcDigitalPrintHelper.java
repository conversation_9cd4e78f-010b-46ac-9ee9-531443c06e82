package tech.tiangong.sdp.design.remote;


import com.zjkj.aigc.client.FloatPrintTemplateClient;
import com.zjkj.aigc.common.resp.FloatPrintTemplateInnerDetailVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import team.aikero.blade.core.protocol.DataResponse;
import tech.tiangong.sdp.core.exception.SdpDesignException;

@Component
@Slf4j
@RequiredArgsConstructor
public class AigcDigitalPrintHelper {

    private final FloatPrintTemplateClient floatPrintTemplateClient;



    public FloatPrintTemplateInnerDetailVO getByPatternCode(String patternCode){
        try {
            log.info("=== 查询版型配置失败 req：{}; ===", patternCode);
            DataResponse<FloatPrintTemplateInnerDetailVO> byPatternCode = floatPrintTemplateClient.getByPatternCode(patternCode);
            return byPatternCode.getData();
        } catch (Exception e) {
            throw new SdpDesignException("查询版型配置失败,调用服务失败:"+e.getMessage(), e);
        }
    }


}
