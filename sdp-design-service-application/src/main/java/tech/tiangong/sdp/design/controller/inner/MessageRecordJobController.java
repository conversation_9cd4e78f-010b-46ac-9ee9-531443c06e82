package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.mq.enums.MqTypeEnum;
import tech.tiangong.sdp.design.service.impl.MessageRecordService;
import tech.tiangong.sdp.design.vo.req.mq.MqRetryReq;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/jobs/mq")
public class MessageRecordJobController {
    private final MessageRecordService messageRecordService;

    /**
     * 重试消费
     *
     * @param req 参数
     * @return DataResponse
     */
    @PostMapping("/retry/consumer")
    public DataResponse<Void> retryConsumer(@RequestBody MqRetryReq req) {
        req.setType(MqTypeEnum.CONSUMER);
        messageRecordService.retryReq(req);
        return DataResponse.ok();
    }

    /**
     * 重试发送
     *
     * @param req 参数
     * @return DataResponse
     */
    @PostMapping("/retry/producer")
    public DataResponse<Void> retryProducer(@RequestBody MqRetryReq req) {
        req.setType(MqTypeEnum.PRODUCER);
        messageRecordService.retryReq(req);
        return DataResponse.ok();
    }

    /**
     * 重试消费和发送
     *
     * @param req 参数
     * @return DataResponse
     */
    @PostMapping("/retry/all")
    public DataResponse<Void> retryAll(@RequestBody MqRetryReq req) {
        messageRecordService.retryReq(req);
        return DataResponse.ok();
    }
}
