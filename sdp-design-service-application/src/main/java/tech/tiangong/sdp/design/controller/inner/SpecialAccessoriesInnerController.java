package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.SpecialAccessoriesService;
import tech.tiangong.sdp.design.vo.req.SpecialAccessoriesReq;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderMaterialVo;

import java.util.List;

/**
 * 特殊辅料-controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/4 19:07
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/special-accessories")
public class SpecialAccessoriesInnerController {

	private final SpecialAccessoriesService specialAccessoriesService;


	/**
	 * 特殊辅料核价信息
	 *
	 * @param req 特殊辅料请求
	 * @return 响应结果
	 */
	@PostMapping("/nuclear-price")
	DataResponse<List<BomOrderMaterialVo>> getSpecialAccessoriesNuclearPrice(@Validated @RequestBody SpecialAccessoriesReq req) {
		List<BomOrderMaterialVo> list = specialAccessoriesService.getSpecialAccessoriesNuclearPrice(req);
		return DataResponse.ok(list);
	}

	private void setSystemUser() {
		UserContentHolder.set(new UserContent()
				.setCurrentUserId(0L)
				.setCurrentUserCode("0")
				.setCurrentUserName("系统")
				.setTenantId(2L)
				.setSystemCode("SDP"));
	}

	/**
	 * 物料图片刷新-2022年10月20日之后提交的数据，需要重新拉一次图片
	 * @return Void
	 */
	@PostMapping("/update-picture")
	@NoRepeatSubmitLock(lockTime = 15L)
	public DataResponse<Void> updateMatchPicture(@RequestParam(required = false) Integer handleSize) {
		this.setSystemUser();
		try {
			specialAccessoriesService.updateMatchPicture(handleSize);
			return DataResponse.ok();
		} finally {
			UserContentHolder.clean();
		}
	}

}
