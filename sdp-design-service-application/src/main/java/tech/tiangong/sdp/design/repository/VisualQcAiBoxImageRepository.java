package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualQcAiBoxImage;
import tech.tiangong.sdp.design.mapper.VisualQcAiBoxImageMapper;

import java.util.List;
import java.util.Objects;

@Repository
public class VisualQcAiBoxImageRepository extends BaseRepository<VisualQcAiBoxImageMapper, VisualQcAiBoxImage> {

    public List<VisualQcAiBoxImage> listByQcRecordId(Long qcRecordId) {
        if (Objects.isNull(qcRecordId)) {
            return null;
        }
        return lambdaQuery()
                .eq(VisualQcAiBoxImage::getQcRecordId, qcRecordId)
                .list();
    }
}
