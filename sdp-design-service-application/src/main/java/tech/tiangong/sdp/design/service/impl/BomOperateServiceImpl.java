package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.clothes.enums.MakeClothesTypeEnum;
import tech.tiangong.sdp.clothes.vo.resp.SampleClothesVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.enums.visual.VisualTaskTypeEnum;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.remote.SampleClothesRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.dto.bom.BomCopyInfoDto;
import tech.tiangong.sdp.design.vo.dto.bom.BomSubmitMaterialChgNotificationDto;
import tech.tiangong.sdp.design.vo.dto.material.BomMaterialUpdateDto;
import tech.tiangong.sdp.design.vo.dto.visual.BackgroundDTO;
import tech.tiangong.sdp.design.vo.dto.visual.ModelFaceDTO;
import tech.tiangong.sdp.design.vo.dto.visual.TryOnModelReferenceImageDTO;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderUpdateV3Req;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandSaveV3Req;
import tech.tiangong.sdp.design.vo.req.log.PushZjLogReq;
import tech.tiangong.sdp.design.vo.req.mq.BomSubmit2PrePriceDto;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualDemandBySpuReq;
import tech.tiangong.sdp.design.vo.req.visual.SpuVisualDemandRecordSaveReq;
import tech.tiangong.sdp.design.vo.req.zj.bom.*;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderUpdateResp;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderVo;
import tech.tiangong.sdp.design.vo.resp.zj.bom.BomOrderOpenResp;
import tech.tiangong.sdp.design.vo.resp.zj.design.PrototypeOrderMaterialOpenResp;
import tech.tiangong.sdp.utils.AsyncTask;
import tech.tiangong.sdp.utils.StreamUtil;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 * bom单 暂存/提交 操作实现类
 * <AUTHOR>
 * @date 2022/11/17 12:54
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BomOperateServiceImpl implements BomOperateService {

    private final BomOrderRepository bomOrderRepository;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final BomMaterialDemandRepository bomMaterialDemandRepository;
    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final DesignRemarksRepository designRemarksRepository;
    private final BomMaterialDemandTransientRepository bomMaterialDemandTransientRepository;
    private final BomSubmitHandleService submitHandleService;
    private final BomMaterialCommonService bomMaterialCommonService;
    private final SpecialAccessoriesRepository specialAccessoriesRepository;
    private final ApplicationContext applicationContext;
    private final PrototypeRepository prototypeRepository;
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final OrderMaterialFollowRepository orderMaterialFollowRepository;
    private final MqProducer mqProducer;
    private final PushZjLogService pushZjLogService;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final SpuVisualDemandRecordRepository spuVisualDemandRecordRepository;
    private final SpuVisualDemandRecordService spuVisualDemandRecordService;
    private final VisualTaskRepository visualTaskRepository;
    private final VisualDemandRepository visualDemandRepository;
    private final VisualSpuService visualSpuService;
    @Lazy
    @Resource
    private VisualDemandService visualDemandService;

    @Lazy
    @Resource
    private BomOrderService bomOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomOrderUpdateResp submit(BomOrderUpdateV3Req req) {
        Long bomId = req.getBomId();
        BomOrder bomOrder = bomOrderRepository.getEntityByBomId(bomId);
        //1, bom提交校验
        this.bomSubmitCheck(req, bomOrder);

        //2, 根据bom单不同状态处理物料, 需求, 工艺信息
        BomMultiStateEnum bomMultiStateEnum = this.getBomMultiStateEnum(bomOrder);
        log.info("===== bom提交类型: {} =====", bomMultiStateEnum.getDesc());
        BomOrder newBomOrder = null;
        switch (bomMultiStateEnum) {
            //待提交_无暂存
            case WAIT_NO_TRANSIENT -> submitHandleService.firstSubmitNoTransient(req);

            //已提交_无暂存
            case SUBMIT_NO_TRANSIENT,
                 //已提交_找料中_无暂存
                 SUBMIT_SEARCH_NO_TRANSIENT,
                 //已核算_无暂存
                 CALCULATED_NO_TRANSIENT ->
                    newBomOrder = submitHandleService.reSubmitNoTransient(req, bomMultiStateEnum);

            default -> Assert.isTrue(false, "bom单状态异常类型，请联系开发人员！bomId:{}", bomId);
        }

        //3, bom提交后续处理
        return this.handleBomAfterSubmit(bomOrder, newBomOrder, bomMultiStateEnum);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishSearch(Long bomId) {
        SdpDesignException.notNull(bomId, "bomId为空!");
        BomOrder bomOrder = bomOrderRepository.getById(bomId);
        if (Objects.equals(bomOrder.getMaterialSearchState(), Bool.YES.getCode())) {
            BomOrder updateBom = new BomOrder();
            updateBom.setBomId(bomId);
            updateBom.setMaterialSearchState(Bool.NO.getCode());
            bomOrderRepository.updateById(updateBom);

            //同步bom到致景
            this.pushBom2Zj(bomOrder, PushZjTypeEnum.BOM_UPDATE);

            //提交视觉需求(用户使用bom提交人)
            UserContent userContent = UserContentHolder.get();
            try {
                UserContent bomUser = new UserContent()
                        .setCurrentUserId(bomOrder.getCreatorId())
                        .setCurrentUserCode(bomOrder.getCreatorId()+"")
                        .setCurrentUserName(bomOrder.getCreatorName())
                        .setTenantId(2L)
                        .setSystemCode("SDP");
                UserContentHolder.set(bomUser);

                this.addVisualDemand(bomOrder.getDesignCode(), SpuVisualHandleTypeEnum.FINISH_SEARCH);
            } catch (Exception e) {
                log.error("找料回复视觉提交失败:", e);
            }
            //恢复当前用户
            UserContentHolder.set(userContent);

            log.info("=== bom单完成找料: bomOrderId:{} ===", bomId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomOrder copyNewVersionBom(BomOrder bomOrder, boolean copyTransient, Integer newBomVersion, BomOrderStateEnum bomOrderStateEnum,  BomCopyInfoDto copyInfoDto) {
        Long oldBomId = bomOrder.getBomId();
        BomOrder oldBomOrder = bomOrderRepository.getById(oldBomId);
        SdpDesignException.notNull(oldBomOrder, "需要复制的bom单不存在! ");
        //该方法只对已提交/已核算的bom进行复制
        if (!(Objects.equals(bomOrder.getState(), BomOrderStateEnum.SUBMITTED.getCode()) || Objects.equals(bomOrder.getState(), BomOrderStateEnum.CALCULATED.getCode()))){
            throw new SdpDesignException("bom单不是已提交或已核算状态, 不能自动升版本");
        }
        //新bom单
        BomOrder newBomOrder = this.copyNewBom(oldBomOrder, newBomVersion);
        //设置bom单状态
        newBomOrder.setState(bomOrderStateEnum.getCode());
        bomOrderRepository.save(newBomOrder);

        //复制旧bom的需求物料工艺信息
        BomCopyInfoDto bomCopyInfoDto = bomMaterialCommonService.copyDemandFabricAccessories(oldBomOrder, newBomOrder);
        BeanUtils.copyProperties(bomCopyInfoDto, copyInfoDto);

        //复制旧bom的特殊辅料信息
        Map<Long, Long> oldNewSpecialIdMap = bomMaterialCommonService.copySpecialAccessories(oldBomOrder, newBomOrder);
        log.info("bom升版本-新旧特辅关系:{}", JSON.toJSONString(oldNewSpecialIdMap));
        copyInfoDto.setOldNewSpecialIdMap(oldNewSpecialIdMap);

        //若旧bom有暂存, 复制暂存信息(注: 要放在面辅料复制之后)
        /*
        if (copyTransient) {
            bomMaterialCommonService.copyTransientInfo(oldBomOrder, newBomOrder, copyInfoDto);
            //旧bom删除暂存状态(因为暂存表指向了新bom)
            bomOrderRepository.updateById(BomOrder.builder().bomId(oldBomId).transientState(Bool.NO.getCode()).build());
        }
         */

        return newBomOrder;
    }

    /**
     * bom提交时推送bom信息到致景
     * PS: 调用该方法前要先推送spu, skc信息到致景
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushBom2Zj(BomOrder pushBomOrder, PushZjTypeEnum pushZjTypeEnum) {
        SdpDesignException.notNull(pushBomOrder, "bom单为空!");
        BomOrder bomOrder = bomOrderRepository.getById(pushBomOrder.getBomId());
        SdpDesignException.notNull(bomOrder, "bom单为空!");
        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        SdpDesignException.notNull(prototype, "skc不存在!{}", bomOrder.getDesignCode());
        log.info(" === bom单推送致景, bomId:{}", bomOrder.getBomId());

        Long bomId = bomOrder.getBomId();
        //当前bom单的物料, 工艺需求, 找料需求, 快照信息, 物料备注, 特辅 (过滤掉暂存的)
        List<BomOrderMaterial> bomOrderMaterials = bomOrderMaterialRepository.getListByBomId(bomId);
        List<Long> materialSnapshotIds = StreamUtil.convertListAndDistinct(bomOrderMaterials, BomOrderMaterial::getMaterialSnapshotId);
        List<BomMaterialDemand> bomMaterialDemands = bomMaterialDemandRepository.listByBomId(bomId);
        List<CraftDemandInfo> craftDemandInfos = craftDemandInfoRepository.getListByBomIds(Collections.singletonList(bomId));
        List<MaterialSnapshot> materialSnapshots = new ArrayList<>();
        if (CollUtil.isNotEmpty(materialSnapshotIds)) {
            materialSnapshots = materialSnapshotRepository.listByIds(materialSnapshotIds);
        }

        List<SpecialAccessories> specialAccessories = specialAccessoriesRepository.getListByBomId(bomId);

        //物料的备注(面辅料+特辅)
        Map<Long, Integer> bizChildIdMap = new HashMap<>();
        bomOrderMaterials.forEach(item -> {
            bizChildIdMap.put(item.getBomMaterialId(), 1);
        });
        //已提交特辅的备注
        specialAccessories.stream()
                .filter(item -> Objects.equals(item.getState(), SpecialAccessoriesStateEnum.SUBMIT.getCode()))
                .forEach(item -> {
                    bizChildIdMap.put(item.getSpecialAccessoriesId(), 1);
                });
        List<DesignRemarks> designRemarks = designRemarksRepository.getListByBizIdAndTransientState(bomId, Bool.NO.getCode()).stream()
                .filter(item -> Objects.nonNull(bizChildIdMap.get(item.getBizChildId())))
                .collect(Collectors.toList());

        //字典
        List<DictVo> dictResp = dictValueRemoteHelper.listByDictCodes(List.of(DictConstant.PART_USE_CODE, DictConstant.BOM_CUTTING_METHOD_CODE));
        Map<String, DictVo> dictValueMap = dictResp.stream().collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));

        //封装bom
        BomOrderAddOpenReq openReq = new BomOrderAddOpenReq();
        BomOrderOpenReq bomOrderOpenReq = new BomOrderOpenReq();
        BeanUtils.copyProperties(bomOrder, bomOrderOpenReq);
        //暂存状态信息不提交给致景, BOM提交时如果还有暂存状态(更新特辅升版本)去掉暂存状态
        if (Objects.equals(bomOrder.getTransientState(), Bool.YES.getCode())) {
            bomOrderOpenReq.setTransientState(Bool.NO.getCode());
            bomOrderOpenReq.setTransientCount(0);
        }
        openReq.setBomOrderOpenReq(bomOrderOpenReq);
        //物料
        List<BomOrderMaterialOpenReq> bomOrderMaterialOpenReqList = bomOrderMaterials.stream().map(item -> {
            BomOrderMaterialOpenReq bomOrderMaterialOpenReq = new BomOrderMaterialOpenReq();
            BeanUtils.copyProperties(item, bomOrderMaterialOpenReq);
            //使用部位与裁剪方法字典值设置
            this.setMaterialDictValue2Zj(item, bomOrderMaterialOpenReq, dictValueMap);
            return bomOrderMaterialOpenReq;
        }).collect(Collectors.toList());
        openReq.setBomOrderMaterialOpenReqs(bomOrderMaterialOpenReqList);
        //找料需求
        List<BomMaterialDemandOpenReq> bomMaterialDemandOpenReqs = bomMaterialDemands.stream().map(item -> {
            BomMaterialDemandOpenReq bomMaterialDemandOpenReq = new BomMaterialDemandOpenReq();
            BeanUtils.copyProperties(item, bomMaterialDemandOpenReq);
            return bomMaterialDemandOpenReq;
        }).collect(Collectors.toList());
        openReq.setBomMaterialDemandOpenReqs(bomMaterialDemandOpenReqs);
        //工艺需求
        List<CraftDemandInfoOpenReq> craftDemandInfoOpenReqs = craftDemandInfos.stream().map(item -> {
            CraftDemandInfoOpenReq craftDemandInfoOpenReq = new CraftDemandInfoOpenReq();
            BeanUtils.copyProperties(item, craftDemandInfoOpenReq);
            return craftDemandInfoOpenReq;
        }).collect(Collectors.toList());
        openReq.setCraftDemandInfoOpenReqs(craftDemandInfoOpenReqs);
        //物料快照
        List<MaterialSnapshotOpenReq> materialSnapshotOpenReqs = materialSnapshots.stream().map(item -> {
            MaterialSnapshotOpenReq materialSnapshotOpenReq = new MaterialSnapshotOpenReq();
            BeanUtils.copyProperties(item, materialSnapshotOpenReq);
            return materialSnapshotOpenReq;
        }).collect(Collectors.toList());
        openReq.setMaterialSnapshotOpenReqs(materialSnapshotOpenReqs);
        //物料备注
        List<DesignRemarksOpenReq> designRemarksOpenReqs = designRemarks.stream().map(item -> {
            DesignRemarksOpenReq designRemarksOpenReq = new DesignRemarksOpenReq();
            BeanUtils.copyProperties(item, designRemarksOpenReq);
            return designRemarksOpenReq;
        }).collect(Collectors.toList());
        openReq.setDesignRemarksOpenReqs(designRemarksOpenReqs);
        //特辅信息
        List<SpecialAccessoriesOpenReq> specialAccessoriesOpenReqs = specialAccessories.stream()
                .map(item -> {
                    SpecialAccessoriesOpenReq specialAccessoriesOpenReq = new SpecialAccessoriesOpenReq();
                    BeanUtils.copyProperties(item, specialAccessoriesOpenReq);
                    //使用部位
                    this.setSpecialDictValue2Zj(item, specialAccessoriesOpenReq, dictValueMap);
                    return specialAccessoriesOpenReq;
                }).collect(Collectors.toList());
        openReq.setSpecialAccessoriesOpenReqs(specialAccessoriesOpenReqs);
        openReq.setBizChannel(prototype.getBizChannel());

        //对接致景 新增bom-v2 替换接口
        BomOrderOpenResp bomOrderOpenResp = zjDesignRemoteHelper.pushBom2Zj(openReq);

        //记录推送标识
        pushZjLogService.create(PushZjLogReq.builder()
                .pushType(Objects.isNull(pushZjTypeEnum)? PushZjTypeEnum.BOM_SUBMIT.getCode(): pushZjTypeEnum.getCode())
                .bizId(bomId)
                .bizCode(bomOrder.getBomCode())
                .zjBizId(bomOrderOpenResp.getBomId())
                .zjBizCode(bomOrderOpenResp.getBomCode())
                .build()
        );

    }


    private static void setMaterialDictValue2Zj(BomOrderMaterial item, BomOrderMaterialOpenReq openReq, Map<String, DictVo> dictValueMap) {
        //使用部位
        DictVo partUseDict = dictValueMap.get(DictConstant.PART_USE_CODE);
        if (Objects.nonNull(partUseDict) && CollectionUtil.isNotEmpty(partUseDict.getChildren())) {
            String partUse = item.getPartUse();
            if (StringUtils.isNotBlank(partUse)) {
                Map<String, DictVo> partUseDictValueMap = partUseDict.getChildren().stream()
                        .collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));
                String partUsrName = Arrays.stream(partUse.split(",")).map(partStr ->
                                Optional.ofNullable(partUseDictValueMap.get(partStr)).map(DictVo::getDictName).orElse(""))
                        .collect(Collectors.joining(","));
                //使用部位名称
                openReq.setPartUseName(partUsrName);
            }
        }

        //裁剪方法
        DictVo bomCuttingMethodDict = dictValueMap.get(DictConstant.BOM_CUTTING_METHOD_CODE);
        if (Objects.nonNull(bomCuttingMethodDict) && CollectionUtil.isNotEmpty(bomCuttingMethodDict.getChildren())) {
            bomCuttingMethodDict.getChildren().stream()
                    .filter(dictValueVo -> Objects.equals(item.getCuttingMethod(), Integer.valueOf(dictValueVo.getDictCode())))
                    .findFirst()
                    //剪辑方法
                    .ifPresent(dictValueVo -> openReq.setCuttingMethodName(dictValueVo.getDictName()));
        }
    }


    private static void setSpecialDictValue2Zj(SpecialAccessories item, SpecialAccessoriesOpenReq openReq, Map<String, DictVo> dictValueMap) {
        //使用部位
        DictVo partUseDict = dictValueMap.get(DictConstant.PART_USE_CODE);
        if (Objects.nonNull(partUseDict) && CollectionUtil.isNotEmpty(partUseDict.getChildren())) {
            String partUse = item.getPartUse();
            if (StringUtils.isNotBlank(partUse)) {
                Map<String, DictVo> partUseDictValueMap = partUseDict.getChildren().stream()
                        .collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));
                String partUsrName = Arrays.stream(partUse.split(",")).map(partStr ->
                                Optional.ofNullable(partUseDictValueMap.get(partStr)).map(DictVo::getDictName).orElse(""))
                        .collect(Collectors.joining(","));
                //使用部位名称
                openReq.setPartUseName(partUsrName);
            }
        }
    }

    private BomOrder copyNewBom(BomOrder oldBomOrder, Integer newBomOrderVersion) {
        BomOrder newBomOrder = new BomOrder();
        BeanUtils.copyProperties(oldBomOrder, newBomOrder);
        long newBomOrderId = IdPool.getId();
        newBomOrder.setBomId(newBomOrderId);
        newBomOrder.setSubmitTime(LocalDateTime.now());
        //清空暂存次数
        newBomOrder.setTransientCount(null);
        newBomOrder.setSubmitTime(LocalDateTime.now());
        newBomOrder.setVersionNum(newBomOrderVersion);
        return newBomOrder;
    }

    private BomMultiStateEnum getBomMultiStateEnum(BomOrder bomOrder) {
        Integer bomState = bomOrder.getState();
        Integer transientState = bomOrder.getTransientState();
        Integer searchState = bomOrder.getMaterialSearchState();

        BomMultiStateEnum bomMultiStateEnum = null;
        //待提交
        if (Objects.equals(bomState, BomOrderStateEnum.WAIT_SUBMIT.getCode())) {
            //待提交-无暂存; 待提交-有暂存
            bomMultiStateEnum = BomMultiStateEnum.WAIT_NO_TRANSIENT;
            // bomMultiStateEnum = Objects.equals(transientState, Bool.NO.getCode()) ? BomMultiStateEnum.WAIT_NO_TRANSIENT : BomMultiStateEnum.WAIT_HAS_TRANSIENT;
        }
        //已提交
        else if (Objects.equals(bomState, BomOrderStateEnum.SUBMITTED.getCode())) {
            //已提交-找料中
            if (Objects.equals(searchState, Bool.YES.getCode())) {
                bomMultiStateEnum = BomMultiStateEnum.SUBMIT_SEARCH_NO_TRANSIENT;
                // 已提交-找料中-无暂存;  已提交-找料中-有暂存
                // bomMultiStateEnum = Objects.equals(transientState, Bool.NO.getCode()) ?
                //         BomMultiStateEnum.SUBMIT_SEARCH_NO_TRANSIENT : BomMultiStateEnum.SUBMIT_SEARCH_HAS_TRANSIENT;
            }else {
                bomMultiStateEnum = BomMultiStateEnum.SUBMIT_NO_TRANSIENT;
                // 已提交-无暂存;  已提交-有暂存
                // bomMultiStateEnum = Objects.equals(transientState, Bool.NO.getCode()) ?
                //         BomMultiStateEnum.SUBMIT_NO_TRANSIENT : BomMultiStateEnum.SUBMIT_HAS_TRANSIENT;
            }
        }
        //已核算
        else if (Objects.equals(bomState, BomOrderStateEnum.CALCULATED.getCode())) {
            bomMultiStateEnum = BomMultiStateEnum.CALCULATED_NO_TRANSIENT;
            // 已核算-无暂存;  已核算-有暂存
            // bomMultiStateEnum = Objects.equals(transientState, Bool.NO.getCode()) ?
            //         BomMultiStateEnum.CALCULATED_NO_TRANSIENT : BomMultiStateEnum.CALCULATED_HAS_TRANSIENT;

        }
        SdpDesignException.notNull(bomMultiStateEnum, "bom单状态异常 bomId:{}, skcCode:{}", bomOrder.getBomId(), bomOrder.getDesignCode());
        return bomMultiStateEnum;
    }

    private BomOrderUpdateResp handleBomAfterSubmit(BomOrder bomOrder, BomOrder newBomOrder, BomMultiStateEnum bomMultiStateEnum) {
        Long bomId = bomOrder.getBomId();
        if (Objects.nonNull(newBomOrder)) {
            bomId = newBomOrder.getBomId();
        }

        //更新bom找料中状态: 查询bom是否有找料中的需求 更新bom的找料状态;
        Boolean searchFlag = this.updateBomSearchState(bomId, bomMultiStateEnum);

        //BOM提交且非找料中状态 推送致景与生产资料
        if (!searchFlag) {
            //同步信息到推款-生产资料
            this.syncBom2Production(bomOrder, bomId);
        }

        //首次提交,创建预估核价
        if (Objects.isNull(newBomOrder)) {
            this.bomSubmit2CheckCount(bomOrder);
        }

        //再次提交,若旧bom为找料中状态则关闭
        this.closeOldSearchBom(bomOrder, newBomOrder, bomMultiStateEnum);

        //创建视觉需求
        this.addVisualDemand(bomOrder.getDesignCode(), SpuVisualHandleTypeEnum.BOM_SUBMIT);

        //bom单推送致景
        this.bomSubmit2Zj(bomOrder, newBomOrder);


        //BOM提交且非找料中状态 获取预估用量推送ZJ
        if (!searchFlag) {
            if (Objects.nonNull(newBomOrder)) {
                bomSyncEstimateUsage(newBomOrder);
            }else {
                bomSyncEstimateUsage(bomOrder);
            }
        }

        //已打版且未生成齐套单, 通知致景齐套预占位
        this.prePlacementOrderZj(bomOrder.getDesignCode());

        //bom更新异步通知资源库服务
        BomOrderServiceImpl bomOrderService = applicationContext.getBean(BomOrderServiceImpl.class);
        bomOrderService.asyncInformResourceBomUpdate(bomId, UserContentHolder.get());

        //异步更新视觉花型
        asyncFlowerState2Visual(bomOrderService, bomOrder.getDesignCode());

        log.info("=== bom提交成功: bomOrderId:{}; bomMultiState:{}===", bomOrder.getBomId(), bomMultiStateEnum.getDesc());
        return new BomOrderUpdateResp().setBomId(bomId);
    }

    private void asyncFlowerState2Visual(BomOrderServiceImpl bomOrderService, String designCode) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(AsyncTask.wrapper(()->{
                    Prototype prototype = prototypeRepository.getByDesignCode(designCode);
                    //校验是否花型
                    Boolean flowerFlag = bomOrderService.checkFlowerState(prototype.getStyleCode());
                    Integer flowerState = flowerFlag ? 1 : 0;
                    visualSpuService.updateFlowerState(prototype.getStyleCode(), flowerState);
                }));
            }
        });
    }

    @Override
    public List<String> checkAllSkcBomSubmitted(String styleCode) {
        log.info("=== 检查spu下skc是否已提交bom: styleCode:{}===", styleCode);

        List<Prototype> noCancelSkcList = prototypeRepository.getNotCancelByStyleCodes(Collections.singletonList(styleCode));
        if (CollUtil.isEmpty(noCancelSkcList)) {
            return Collections.emptyList();
        }

        //skc下最新已提交/已核算的bom单(不包含找料中的bom)
        List<String> skcList = StreamUtil.convertListAndDistinct(noCancelSkcList, Prototype::getDesignCode);
        List<BomOrder> bomOrderList = bomOrderRepository.listLatestSubmitBomOrder(skcList, true);
        Map<String, BomOrder> bomOrderMap = StreamUtil.list2Map(bomOrderList, BomOrder::getDesignCode);

        Prototype noSubmitBomSkc = noCancelSkcList.stream()
                .filter(skc -> Objects.isNull(bomOrderMap.get(skc.getDesignCode())))
                .findFirst().orElse(null);

        //存在未提交bom单skc, 不创建视觉需求
        if (Objects.nonNull(noSubmitBomSkc)) {
            return Collections.emptyList();
        }
        return skcList;
    }

    /**
     * 根据spu查询所有bom已提交的且为取消的skc
     */
    @Override
    public List<BomOrder> listAllBomSubmittedSkc(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        //spu下未取消的skc
        List<Prototype> noCancelSkcList = prototypeRepository.getNotCancelByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(noCancelSkcList)) {
            return Collections.emptyList();
        }

        //skc下最新已提交/已核算的bom单
        List<String> skcList = StreamUtil.convertListAndDistinct(noCancelSkcList, Prototype::getDesignCode);
        List<BomOrder> bomOrderList = bomOrderRepository.listLatestSubmitBomOrder(skcList, true);
        if (CollUtil.isEmpty(bomOrderList)) {
            return Collections.emptyList();
        }
        return bomOrderList;
    }

    private Long addVisualDemand(String designCode, SpuVisualHandleTypeEnum handleTypeEnum) {
        log.info("bom-addVisualDemand skc:{}; handleType:{} ", designCode, handleTypeEnum.getDesc());
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        if (Objects.isNull(prototype)) {
            return null;
        }

        //若spu下所有未取消skc都已提交bom, 则创建视觉需求
        List<String> skcList = this.checkAllSkcBomSubmitted(prototype.getStyleCode());
        log.info("=== spu下已提交bom的skc: skcList:{}===", JSON.toJSONString(skcList));
        if (CollUtil.isEmpty(skcList)) {
            return null;
        }

        //skc下只有一个已提交的版本的bom才推
        List<BomOrder> submitBomOrderList = bomOrderRepository.listSubmittedBom(designCode, Bool.NO.getCode());
        log.info("=== skc下已提交非找料中的bom数量:{}===", submitBomOrderList.size());
        if (CollUtil.isEmpty(submitBomOrderList)) {
            //没有已完成的bom, 不处理
            return null;
        }
        //只有一个已提交的版本的bom才推
        if (submitBomOrderList.size() != 1) {
            return null;
        }

        // ============= 创建视觉需求 =============
        String demandDesc = "";
        SaveVisualDemandBySpuReq demandReq  = new SaveVisualDemandBySpuReq();
        demandReq.setStyleCode(prototype.getStyleCode());

        //获取当前最新视觉需求提交记录
        SpuVisualDemandRecord demandRecord = spuVisualDemandRecordRepository.getLatestBySpu(prototype.getStyleCode());
        if (Objects.nonNull(demandRecord)) {
            //未提交视觉需求, 根据需求记录创建视觉需求
            BeanUtils.copyProperties(demandRecord, demandReq);
            demandReq.setDemandImages(demandRecord.getDemandImageList());
            if (CharSequenceUtil.isNotBlank(demandRecord.getModelReferenceImage())) {
                demandReq.setModelReferenceImageList(JSON.parseArray(demandRecord.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
            }
            if (CharSequenceUtil.isNotBlank(demandRecord.getBackgroundImage())) {
                demandReq.setBackgroundImageList(JSON.parseArray(demandRecord.getBackgroundImage(), BackgroundDTO.class));
            }
            if (CharSequenceUtil.isNotBlank(demandRecord.getModelFaceImage())) {
                demandReq.setModelFaceImageList(JSON.parseArray(demandRecord.getModelFaceImage(), ModelFaceDTO.class));
            }
            //已创建了视觉需求, 但skc一致, 不发起需求创建; skc不一致, 按复色逻辑需要重新创建视觉需求

            //spu批量查询下最新的【上新、优化】视觉任务
            List<Integer> taskTypeList = List.of(VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode(), VisualTaskTypeEnum.OPTIMIZE_TASK.getCode());
            List<VisualTask> visualTaskList = visualTaskRepository.listLatestBySpuTaskType(Collections.singletonList(prototype.getStyleCode()), taskTypeList);
            if (CollUtil.isNotEmpty(visualTaskList)) {
                List<String> recordSkcList = demandRecord.getSkcList();
                List<String> difSkcList = skcList.stream()
                        .filter(skc -> !recordSkcList.contains(skc))
                        .collect(Collectors.toList());
                //skc无变化, 不提交视觉需求
                if (CollUtil.isEmpty(difSkcList)) {
                    log.info("=== 视觉需求下skc无变更, 不提交需求创建: spu:{}===", prototype.getStyleCode());
                    return null;
                }
                //自动逻辑为在需求描述中回车一行增加【复色SKCxxxxx、SKCxxxxxx】
                String colorDesc = "【复色" + String.join("、", difSkcList) + "】";
                demandDesc = StrUtil.isNotBlank(demandRecord.getDemandDesc()) ? demandRecord.getDemandDesc() + "\n" : "";
                demandDesc += colorDesc;
                demandReq.setDemandDesc(colorDesc);

                //记录描述与视觉需求描述不一致时, 带上记录描述
                VisualDemand visualDemand = visualDemandRepository.getById(visualTaskList.getFirst().getLatestDemandId());
                if (Objects.nonNull(visualDemand)) {
                    if (!Objects.equals(visualDemand.getDemandDesc(), demandRecord.getDemandDesc())) {
                        demandReq.setDemandDesc(demandDesc);
                    }
                }

                demandReq.setIsAutoInit(true);
            }
        }
        else {
            //未提交过视觉需求创建(历史款spu下所有skc已提交 或 自建视觉需求)
            log.info("bom发起视觉需求创建 skc:{}", designCode);
        }

        log.info("bom发起视觉需求创建 req:{}", JSON.toJSONString(demandReq));
        VisualDemand visualDemand = visualDemandService.saveVisualDemandBySpu(demandReq);

        //新增需求提交记录
        SpuVisualDemandRecordSaveReq visualDemandRecord = SpuVisualDemandRecordSaveReq.builder()
                .styleCode(prototype.getStyleCode())
                .spuType(SdpStyleTypeEnum.DESIGN.getCode())
                .handleType(handleTypeEnum.getCode())
                .demandType(demandReq.getDemandType())
                .realObjectColorState(demandReq.getRealObjectColorState())
                .visualDemandId(visualDemand.getDemandId())
                .skcList(skcList)
                .demandImageList(demandReq.getDemandImages())
                .demandDesc(demandDesc)
                .modelPicList(demandReq.getModelPicList())
                .backgroundPicList(demandReq.getBackgroundPicList())
                .posturePicList(demandReq.getPosturePicList())
                .build();
        spuVisualDemandRecordService.create(visualDemandRecord);

        return visualDemand.getDemandId();

    }

    /**
     * 齐套单预占位
     *  已打版, 打版方式包含车版, 且未生成齐套单, 通知致景齐套预占位
     */
    private void prePlacementOrderZj(String designCode) {
        SampleClothesVo clothesVo = sampleClothesRemoteHelper.getLatestSampleClothes(designCode);
        if (Objects.isNull(clothesVo)) {
            return;
        }
        //打版方式包含
        if (!(Objects.equals(clothesVo.getMakeClothesType(), MakeClothesTypeEnum.REAL_SAMPLE_CLOTHES)
                || Objects.equals(clothesVo.getMakeClothesType(), MakeClothesTypeEnum.THREE_DIMENSION_REAL_SAMPLE))) {
            log.debug("=== 加工单打版类型不包含车版,不推送齐套预占位,skc:{} ===", designCode);
            return;
        }

        //是否齐套 先查本地库, 再查履约
        List<OrderMaterialFollow> orderMaterialFollowList = orderMaterialFollowRepository.listByDesignCode(designCode);
        if (CollUtil.isNotEmpty(orderMaterialFollowList)) {
            return;
        }else {
            List<PrototypeOrderMaterialOpenResp> latestMaterialList = zjDesignRemoteHelper.findLatestMaterial(Collections.singletonList(designCode), true);
            if (CollUtil.isNotEmpty(latestMaterialList)) {
                log.debug("=== 履约已创建普通款齐套单,不推送齐套预占位,skc:{} ===", designCode);
                return;
            }
        }
        zjDesignRemoteHelper.prePlacementCompleteMaterials(designCode);
    }


    private void bomSubmit2Zj(BomOrder bomOrder, BomOrder newBomOrder) {
        Long bomId = Objects.nonNull(newBomOrder) ? newBomOrder.getBomId() : bomOrder.getBomId();
        BomOrder pushBom = bomOrderRepository.getEntityByBomId(bomId);
        this.pushBom2Zj(pushBom, PushZjTypeEnum.BOM_SUBMIT);
    }

    private void syncBom2Production(BomOrder bomOrder, Long bomId) {
        Map<String, Object> mqContent = new HashMap<>(16);
        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        mqContent.put("styleCode", prototype.getStyleCode());
        mqContent.put("designCode", bomOrder.getDesignCode());
        mqContent.put("bomId", bomId);
        mqContent.put("bomVersionNum",bomOrder.getVersionNum());
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.BOM_SUBMIT_SYNC_MEANS_OF_PRODUCTION,
                DesignMqConstant.ORDER_UPDATE_MEANS_OF_PRODUCTION_EXCHANGE, null,
                JSON.toJSONString(mqContent));
        log.info("【bom提交-同步生产资料】mqMessageReq:{}", JSON.toJSONString(mqMessageReq));
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

    private void bomSubmit2CheckCount(BomOrder bomOrder) {
        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        BomSubmit2PrePriceDto mqContent = BomSubmit2PrePriceDto.builder()
                .designCode(prototype.getDesignCode())
                .styleCode(prototype.getStyleCode())
                .bomId(bomOrder.getBomId())
                .bomVersionNum(bomOrder.getVersionNum())
                .build();
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.DIGITAL_PRINTING_STYLE_CREATE_CHECK_PRICE,
                DesignMqConstant.BOM_SUBMIT_2_PRE_PRICE_EXCHANGE,
                DesignMqConstant.BOM_SUBMIT_2_PRE_PRICE_ROUTING_KEY,
                JSON.toJSONString(mqContent));
        log.info("【bom提交-预估核价】mqMessageReq:{}", JSON.toJSONString(mqMessageReq));
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

    private void closeOldSearchBom(BomOrder bomOrder, BomOrder newBomOrder, BomMultiStateEnum bomMultiStateEnum) {
        if (Objects.isNull(newBomOrder)) {
            return;
        }
        if (Objects.equals(bomOrder.getMaterialSearchState(), Bool.YES.getCode())) {
            BomOrder updateBom = new BomOrder();
            updateBom.setBomId(bomOrder.getBomId());
            updateBom.setState(BomOrderStateEnum.CLOSED.getCode());
            bomOrderRepository.updateById(updateBom);
            log.info("=== bom提交, 关闭找料中的旧bom单: bomOrderId:{}; bomMultiState:{}===", bomOrder.getBomId(), bomMultiStateEnum.getDesc());

            //推送bom单状态给致景
            this.pushBom2Zj(bomOrder, PushZjTypeEnum.BOM_UPDATE);
        }
    }

    private Boolean updateBomSearchState(Long bomId, BomMultiStateEnum bomMultiStateEnum) {
        //查询bom是否有已提交且找料中的需求,如果有,更新bom为找料中
        BomMaterialDemand searchDemand = bomMaterialDemandRepository.listByBomId(bomId).stream()
                .filter(item -> Objects.equals(item.getDemandState(), BomDemandStateEnum.SUBMIT.getCode()))
                .filter(item -> Objects.equals(item.getMaterialSearchState(), Bool.YES.getCode()))
                .findFirst().orElse(null);
        BomOrder searchBom = bomOrderRepository.getById(bomId);
        Boolean searchFlag = false;
        //bom单 找料中
        if (Objects.nonNull(searchDemand)) {
            searchFlag = true;
            if (Objects.equals(searchBom.getMaterialSearchState(), Bool.NO.getCode())) {
                BomOrder updateBom = new BomOrder();
                updateBom.setBomId(bomId);
                updateBom.setMaterialSearchState(Bool.YES.getCode());
                bomOrderRepository.updateById(updateBom);
                log.info("=== bom提交, 更新bom单为找料中状态: bomOrderId:{}; bomMultiState:{}===", bomId, bomMultiStateEnum.getDesc());
            }
        }
        //bom单 完成找料
        else {
            if (Objects.equals(searchBom.getMaterialSearchState(), Bool.YES.getCode())) {
                BomOrder updateBom = new BomOrder();
                updateBom.setBomId(bomId);
                updateBom.setMaterialSearchState(Bool.NO.getCode());
                bomOrderRepository.updateById(updateBom);
                log.info("=== bom提交, 删除bom单找料中状态: bomOrderId:{}; bomMultiState:{}===", bomId, bomMultiStateEnum.getDesc());
            }
        }
        return searchFlag;
    }

    // private void notification2QiTao(BomOrder bomOrder, BomOrder newBomOrder) {
    //
    //     Long prototypeId = bomOrder.getPrototypeId();
    //     if (Objects.nonNull(newBomOrder)) {
    //         prototypeId = newBomOrder.getPrototypeId();
    //     }
    //     PrototypeHistory prototypeHistory = prototypeHistoryRepository.getByPrototypeId(prototypeId);
    //     SdpDesignException.notNull(prototypeHistory, "拆板单不存在! prototypeId:{}", prototypeId);
    //
    //     //查询bom本次提交增删的物料与需求
    //     List<BomSubmitMaterialChgNotificationDto.Material> addMaterials = new LinkedList<>();
    //     List<BomSubmitMaterialChgNotificationDto.Material> delMaterials = new LinkedList<>();
    //     List<BomSubmitMaterialChgNotificationDto.Demand> addDemands = new LinkedList<>();
    //     List<BomSubmitMaterialChgNotificationDto.Demand> delDemands = new LinkedList<>();
    //
    //     //封装增删的物料与需求
    //     this.handleMaterialAndDemand(bomOrder, newBomOrder, addMaterials, delMaterials, addDemands, delDemands);
    //
    //     BomSubmitMaterialChgNotification bomSubmitNotification = new BomSubmitMaterialChgNotification();
    //     BomSubmitMaterialChgNotificationDto notificationDto = BomSubmitMaterialChgNotificationDto.builder()
    //             .bomOrder(Objects.isNull(newBomOrder) ? bomOrder : newBomOrder)
    //             .prototypeHistory(prototypeHistory)
    //             .addMaterials(addMaterials)
    //             .delMaterials(delMaterials)
    //             .addDemands(addDemands)
    //             .delDemands(delDemands)
    //             .build();
    //
    //     bomSubmitNotification.add(notificationDto);
    //
    //     log.info("===bom提交通知齐套-notificationDto: {} ===", JSON.toJSONString(notificationDto));
    //     bomSubmitNotification.send();
    // }

    private void handleMaterialAndDemand(BomOrder bomOrder, BomOrder newBomOrder, List<BomSubmitMaterialChgNotificationDto.Material> addMaterials, List<BomSubmitMaterialChgNotificationDto.Material> delMaterials, List<BomSubmitMaterialChgNotificationDto.Demand> addDemands, List<BomSubmitMaterialChgNotificationDto.Demand> delDemands) {
        //第一次提交, 只会有新增的物料与需求
        if (Objects.isNull(newBomOrder)) {
            //新增的物料
            bomOrderMaterialRepository.getListByBomId(bomOrder.getBomId()).forEach(item -> {
                //过滤找料中的物料
                if (Objects.isNull(item.getMaterialSnapshotId())) {
                    return;
                }
                BomSubmitMaterialChgNotificationDto.Material material = BomSubmitMaterialChgNotificationDto.Material.builder()
                        .materialSnapshotId(item.getMaterialSnapshotId())
                        .build();
                addMaterials.add(material);

            });
            //新增的需求
            bomMaterialDemandRepository.listByBomId(bomOrder.getBomId()).forEach(item -> {
                if (Objects.isNull(item.getSupplyChainDemandId())) {
                    return;
                }
                BomSubmitMaterialChgNotificationDto.Demand demand = BomSubmitMaterialChgNotificationDto.Demand.builder()
                        .demandId(item.getSupplyChainDemandId())
                        .build();
                addDemands.add(demand);
            });
        }
        //比对新旧版本的物料, 需求, 获取增删的物料与需求
        else {
            //old中有, new没有 -> 被删除的;
            // List<Integer> list = CollUtil.subtractToList(old, new);
            //new中有, old没有 -> 新增的;
            // List<Integer> list = CollUtil.subtractToList(new, old);

            List<Long> oldSnapshotIdList = bomOrderMaterialRepository.getListByBomId(bomOrder.getBomId()).stream()
                    .filter(item -> Objects.nonNull(item.getMaterialSnapshotId()))
                    .map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
            List<Long> newSnapshotIdList = bomOrderMaterialRepository.getListByBomId(newBomOrder.getBomId()).stream()
                    .filter(item -> Objects.nonNull(item.getMaterialSnapshotId()))
                    .map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
            List<Long> addSnapshotIdList = CollUtil.subtractToList(newSnapshotIdList, oldSnapshotIdList);
            List<Long> delSnapshotIdList = CollUtil.subtractToList(oldSnapshotIdList, newSnapshotIdList);
            addSnapshotIdList.forEach(item -> {
                BomSubmitMaterialChgNotificationDto.Material material = BomSubmitMaterialChgNotificationDto.Material.builder()
                        .materialSnapshotId(item)
                        .build();
                addMaterials.add(material);
            });
            delSnapshotIdList.forEach(item -> {
                BomSubmitMaterialChgNotificationDto.Material material = BomSubmitMaterialChgNotificationDto.Material.builder()
                        .materialSnapshotId(item)
                        .build();
                delMaterials.add(material);
            });

            List<Long> oldSupplyDemandId = bomMaterialDemandRepository.listByBomId(bomOrder.getBomId()).stream()
                    .filter(item -> Objects.nonNull(item.getSupplyChainDemandId()))
                    .map(BomMaterialDemand::getSupplyChainDemandId).collect(Collectors.toList());
            List<Long> newSupplyDemandId = bomMaterialDemandRepository.listByBomId(newBomOrder.getBomId()).stream()
                    .filter(item -> Objects.nonNull(item.getSupplyChainDemandId()))
                    .map(BomMaterialDemand::getSupplyChainDemandId).collect(Collectors.toList());

            List<Long> addSupplyDemandId = CollUtil.subtractToList(newSupplyDemandId, oldSupplyDemandId);
            List<Long> delSupplyDemandId = CollUtil.subtractToList(oldSupplyDemandId, newSupplyDemandId);
            addSupplyDemandId.forEach(item -> {
                BomSubmitMaterialChgNotificationDto.Demand demand = BomSubmitMaterialChgNotificationDto.Demand.builder()
                        .demandId(item)
                        .build();
                addDemands.add(demand);
            });
            delSupplyDemandId.forEach(item -> {
                BomSubmitMaterialChgNotificationDto.Demand demand = BomSubmitMaterialChgNotificationDto.Demand.builder()
                        .demandId(item)
                        .build();
                delDemands.add(demand);
            });
        }
    }

    private void bomSubmitCheck(BomOrderUpdateV3Req req, BomOrder bomOrder) {
        //设计款校验
        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
        SdpDesignException.notNull(prototype, "设计款号不存在! ");
        SdpDesignException.isFalse(prototype.getIsCanceled(), "设计款已取消! ");

        //上新数据无法操作
        // prototypeOnShelfService.commonVerification(bomOrder.getDesignCode());

        //校验bom单状态是否变更
        SdpDesignException.isTrue(Objects.equals(req.getBomOrderState(), BomOrderStateEnum.findEntityByCode(bomOrder.getState())), "当前bom单状态已变更, 请刷新页面后重试");

        //校验是否最新版本
        this.bomLatestCheck( bomOrder, req.getBomVersionNum());

        //判断是否已经完成找料
        if (Objects.equals(Bool.YES.getCode(), req.getMaterialSearchState())) {
            SdpDesignException.isTrue(Objects.equals(Bool.YES.getCode(), bomOrder.getMaterialSearchState()), "当前bom单已完成找料, 请刷新页面后重试");
        }

        //特殊辅料校验最小价格单位
        this.checkSpecialMinPriceUnit(req);

        //物料入参与工艺入参校验
        this.checkReqParams(req);
        // if (isSubmit) {
        //     this.checkReqParamsSubmit(req);
        // }else {
        //     this.checkReqParams(req);
        // }

        //校验特辅是否有变更
        this.checkSpecialChange(bomOrder, req);

        //校验需求的匹配回复数量是否有变更
        this.checkDemandMatchNum(req, bomOrder);
    }

    /**
     * 校验特辅变更: 对待提交/未提交过的找料中的bom单,暂存/提交时,校验特辅是否变更
     * @param bomOrder bom
     */
    private void checkSpecialChange(BomOrder bomOrder, BomOrderUpdateV3Req req) {
        boolean waitSubmitBom = Objects.equals(bomOrder.getState(), BomOrderStateEnum.WAIT_SUBMIT.getCode());
        BomOrder latestSubmitBom = bomOrderRepository.getLatestSubmitBomByDesignCode(bomOrder.getDesignCode());

        if (!(waitSubmitBom || Objects.isNull(latestSubmitBom))) {
            return;
        }
        //查询bom单下的特辅
        List<SpecialAccessories> specialList = specialAccessoriesRepository.getListByBomId(bomOrder.getBomId());
        if (CollUtil.isEmpty(specialList) || Objects.isNull(req.getRevisedTime())) {
            return;
        }

        //有特辅, 对比最新更新的特辅与提交的bom单更新时间
        SpecialAccessories latestSpecial = specialList.stream()
                .max(Comparator.comparing(SpecialAccessories::getRevisedTime))
                .orElse(null);
        if (Objects.isNull(latestSpecial)) {
            return;
        }
        //特辅更新了,提示用户刷新页面
        if (latestSpecial.getRevisedTime().isAfter(req.getRevisedTime())) {
            //历史数据更新特辅时不更新bom,要更新下bom
            if (latestSpecial.getRevisedTime().isAfter(bomOrder.getRevisedTime())) {
                CompletableFuture.runAsync(AsyncTask.wrapper(()->{
                    bomOrderRepository.updateById(BomOrder.builder().bomId(bomOrder.getBomId()).build());
                }));
            }
            throw new SdpDesignException("特殊辅料被更新，当前bom内容不是最新，请刷新页面后再尝试。");
        }

    }

    private void checkDemandMatchNum(BomOrderUpdateV3Req req, BomOrder bomOrder) {
        List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateBomDemandList = req.getUpdateBomMaterialDemandList();
        if (CollUtil.isEmpty(updateBomDemandList)) {
            return;
        }
        List<Long> demandIdList = updateBomDemandList.stream().map(BomOrderUpdateV3Req.UpdateBomMaterialDemandReq::getBomMaterialDemandId).collect(Collectors.toList());

        if (Objects.equals(bomOrder.getTransientState(), Bool.YES.getCode())) {
            //bom有暂存
            Map<Long, BomMaterialDemandTransient> transientDemandMap = bomMaterialDemandTransientRepository.listByIds(demandIdList).stream()
                    .collect(Collectors.toMap(BomMaterialDemandTransient::getBomMaterialDemandId, Function.identity(), (k1, k2) -> k1));
            updateBomDemandList.forEach(item -> {
                Integer materialMatchNum = item.getMaterialMatchNum();
                BomMaterialDemandTransient demandTransient = transientDemandMap.get(item.getBomMaterialDemandId());
                SdpDesignException.notNull(demandTransient, "{}的需求不存在! bomId:{} ", item.getPrototypeMaterialName(), bomOrder.getBomId());
                Integer latestMatchNum = demandTransient.getMaterialMatchNum();
                SdpDesignException.isTrue(Objects.equals(materialMatchNum, latestMatchNum), "{}需求的匹配回复已更新, 请刷新后重新操作!", item.getPrototypeMaterialName());
            });
        } else {
            Map<Long, BomMaterialDemand> demandMap = bomMaterialDemandRepository.listByIds(demandIdList).stream()
                    .collect(Collectors.toMap(BomMaterialDemand::getBomMaterialDemandId, Function.identity(), (k1, k2) -> k1));
            updateBomDemandList.forEach(item -> {
                Integer materialMatchNum = item.getMaterialMatchNum();
                BomMaterialDemand demand = demandMap.get(item.getBomMaterialDemandId());
                SdpDesignException.notNull(demand, "{}的需求不存在! bomId:{} ", item.getPrototypeMaterialName(), bomOrder.getBomId());
                Integer latestMatchNum = demand.getMaterialMatchNum();
                SdpDesignException.isTrue(Objects.equals(materialMatchNum, latestMatchNum), "{}需求的匹配回复已更新, 请刷新后重新操作!", item.getPrototypeMaterialName());
            });
        }
    }

    private void checkReqParamsSubmit(BomOrderUpdateV3Req req) {
        List<BomOrderUpdateV3Req.AddBomMaterialReq> addBomMaterials = req.getAddBomMaterials();
        List<BomOrderUpdateV3Req.UpdateBomMaterialReq> updateBomMaterials = req.getUpdateBomMaterials();
        List<BomOrderUpdateV3Req.AddBomMaterialDemandReq> addBomDemandList = req.getAddBomMaterialDemandList();
        List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateBomDemandList = req.getUpdateBomMaterialDemandList();

        //收集物料项目名
        Map<String, Boolean> prototypeNameMap = this.getAllPrototypeNameMapFromReq(addBomMaterials, updateBomMaterials, addBomDemandList, updateBomDemandList);

        //辅料选择对色/包料时, 对应关联物料名称不能为空,且存在
        if (CollUtil.isNotEmpty(addBomMaterials)) {
            addBomMaterials.forEach(item -> {
                this.checkColorMatchNameSubmit(item.getColorMatchMaterialState(), item.getColorMatchMaterialName(), item.getPrototypeMaterialName(), prototypeNameMap);
            });
        }
        if (CollUtil.isNotEmpty(updateBomMaterials)) {
            updateBomMaterials.forEach(item -> {
                this.checkColorMatchNameSubmit(item.getColorMatchMaterialState(), item.getColorMatchMaterialName(), item.getPrototypeMaterialName(), prototypeNameMap);
            });
        }
        if (CollUtil.isNotEmpty(addBomDemandList)) {
            addBomDemandList.forEach(item -> {
                this.checkColorMatchNameSubmit(item.getColorMatchMaterialState(), item.getColorMatchMaterialName(), item.getPrototypeMaterialName(), prototypeNameMap);
            });
        }
        if (CollUtil.isNotEmpty(updateBomDemandList)) {
            updateBomDemandList.forEach(updateDemand -> {
                //更新物料
                List<BomOrderUpdateV3Req.DemandMaterialUpdateReq> materialUpdateReqList = updateDemand.getMaterialUpdateReqList();
                if (CollUtil.isNotEmpty(materialUpdateReqList) && Objects.nonNull(materialUpdateReqList.get(0))) {
                    BomOrderUpdateV3Req.DemandMaterialUpdateReq updateReq = materialUpdateReqList.get(0);
                    this.checkColorMatchNameSubmit(updateReq.getColorMatchMaterialState(), updateReq.getColorMatchMaterialName(), updateReq.getPrototypeMaterialName(), prototypeNameMap);
                }
                //更换物料
                List<BomOrderUpdateV3Req.DemandMaterialAddReq> materialAddReqList = updateDemand.getMaterialAddReqList();
                if (CollUtil.isNotEmpty(materialAddReqList) && Objects.nonNull(materialAddReqList.get(0))) {
                    BomOrderUpdateV3Req.DemandMaterialAddReq addReq = materialAddReqList.get(0);
                    String prototypeMaterialName = addReq.getPrototypeMaterialName();
                    this.checkColorMatchNameSubmit(addReq.getColorMatchMaterialState(), addReq.getColorMatchMaterialName(), addReq.getPrototypeMaterialName(), prototypeNameMap);

                    //需求更换物料时, 新增物料必填入参校验
                    SdpDesignException.notNull(addReq.getSkuId(), "{} skuId不能为空!", prototypeMaterialName);
                    SdpDesignException.notBlank(addReq.getSkuCode(), "{} skuCode不能为空!", prototypeMaterialName);
                }
            });
        }
    }

    private void checkReqParams(BomOrderUpdateV3Req req) {
        List<BomOrderUpdateV3Req.AddBomMaterialReq> addBomMaterials = req.getAddBomMaterials();
        List<BomOrderUpdateV3Req.UpdateBomMaterialReq> updateBomMaterials = req.getUpdateBomMaterials();
        List<BomOrderUpdateV3Req.AddBomMaterialDemandReq> addBomDemandList = req.getAddBomMaterialDemandList();
        List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateBomDemandList = req.getUpdateBomMaterialDemandList();

        //收集物料项目名
        List<String> prototypeNameList = this.getAllPrototypeNameFromReq(addBomMaterials, updateBomMaterials, addBomDemandList, updateBomDemandList);

        //校验: 1,辅料选择对色/包料时, 对应关联物料名称不能为空,且存在;
        //校验: 2, 新增工艺必填信息校验
        if (CollUtil.isNotEmpty(addBomMaterials)) {
            addBomMaterials.forEach(item -> {
                this.checkColorMatchName(item.getColorMatchMaterialState(), item.getColorMatchMaterialName(), item.getPrototypeMaterialName(), prototypeNameList);
                this.checkCraftInfo(item.getAddCraftDemandList());
            });
        }
        if (CollUtil.isNotEmpty(updateBomMaterials)) {
            updateBomMaterials.forEach(item -> {
                this.checkColorMatchName(item.getColorMatchMaterialState(), item.getColorMatchMaterialName(), item.getPrototypeMaterialName(), prototypeNameList);
                this.checkCraftInfo(item.getAddCraftDemandList());
            });
        }
        if (CollUtil.isNotEmpty(addBomDemandList)) {
            addBomDemandList.forEach(item -> {
                this.checkColorMatchName(item.getColorMatchMaterialState(), item.getColorMatchMaterialName(), item.getPrototypeMaterialName(), prototypeNameList);
                Optional.ofNullable(item.getMaterialAddReq()).ifPresent(materialReq -> {
                    this.checkCraftInfo(materialReq.getAddCraftDemandList());
                });
            });
        }
        if (CollUtil.isNotEmpty(updateBomDemandList)) {
            updateBomDemandList.forEach(updateDemand -> {
                //更新物料
                List<BomOrderUpdateV3Req.DemandMaterialUpdateReq> materialUpdateReqList = updateDemand.getMaterialUpdateReqList();
                if (CollUtil.isNotEmpty(materialUpdateReqList) && Objects.nonNull(materialUpdateReqList.get(0))) {
                    BomOrderUpdateV3Req.DemandMaterialUpdateReq updateReq = materialUpdateReqList.get(0);
                    this.checkColorMatchName(updateReq.getColorMatchMaterialState(), updateReq.getColorMatchMaterialName(), updateReq.getPrototypeMaterialName(), prototypeNameList);
                    this.checkCraftInfo(updateReq.getAddCraftDemandList());
                }
                //更换物料
                List<BomOrderUpdateV3Req.DemandMaterialAddReq> materialAddReqList = updateDemand.getMaterialAddReqList();
                if (CollUtil.isNotEmpty(materialAddReqList) && Objects.nonNull(materialAddReqList.getFirst())) {
                    BomOrderUpdateV3Req.DemandMaterialAddReq addReq = materialAddReqList.getFirst();
                    String prototypeMaterialName = addReq.getPrototypeMaterialName();
                    this.checkColorMatchName(addReq.getColorMatchMaterialState(), addReq.getColorMatchMaterialName(), addReq.getPrototypeMaterialName(), prototypeNameList);
                    this.checkCraftInfo(addReq.getAddCraftDemandList());
                    //需求更换物料时, 新增物料必填入参校验
                    SdpDesignException.notNull(addReq.getSkuId(), "{} skuId不能为空!", prototypeMaterialName);
                    SdpDesignException.notBlank(addReq.getSkuCode(), "{} skuCode不能为空!", prototypeMaterialName);
                }
            });
        }
    }

    private void checkCraftInfo(List<CraftDemandSaveV3Req> addCraftDemandList) {
        if (CollUtil.isEmpty(addCraftDemandList)) {
            return;
        }
        //新增工艺必填校验
        addCraftDemandList.forEach(craft -> {
            SdpDesignException.notBlank(craft.getCategory1(), "一级分类不能为空");
            SdpDesignException.notBlank(craft.getCategory2(), "二级分类不能为空");
            SdpDesignException.notNull(craft.getCraftsRequire(), "工艺要求不能为空");
            SdpDesignException.notBlank(craft.getPicture(), "工艺图片不能为空");
            SdpDesignException.notBlank(craft.getPositionRequirement(), "位置要求不能为空");
        });
    }

    private void checkColorMatchNameSubmit(Integer colorMatchMaterialState,
                                     String colorMatchMaterialName,
                                     String prototypeMaterialName,
                                     Map<String, Boolean> prototypeNameMap) {

        //物料项目名不能为空
        SdpDesignException.notBlank(prototypeMaterialName, "物料项目名不能为空! ");

        if (Objects.isNull(colorMatchMaterialState)) {
            return;
        }
        SdpDesignException.isTrue(CollUtil.isNotEmpty(prototypeNameMap), "物料项目名为空! ");

        if (!Objects.equals(colorMatchMaterialState, BomColorMaterialStateEnum.NO_THING.getCode())) {
            SdpDesignException.isTrue(StringUtils.isNotBlank(colorMatchMaterialName), "{}对色/包料的物料项目名不能为空! ", prototypeMaterialName);
            SdpDesignException.notNull(prototypeNameMap.get(colorMatchMaterialName), "{} 对色/包料的物料项目名不存在! ", prototypeMaterialName);
            SdpDesignException.isFalse(prototypeNameMap.get(colorMatchMaterialName), "物料项目{}为空找料需求，物料项目{}无法对色物料项目{}! ", colorMatchMaterialName, prototypeMaterialName, colorMatchMaterialName);
        }
    }

    private void checkColorMatchName(Integer colorMatchMaterialState,
                                     String colorMatchMaterialName,
                                     String prototypeMaterialName,
                                     List<String> prototypeNameList) {

        //物料项目名不能为空
        SdpDesignException.notBlank(prototypeMaterialName, "物料项目名不能为空! ");

        if (Objects.isNull(colorMatchMaterialState)) {
            return;
        }

        SdpDesignException.notEmpty(prototypeNameList, "物料项目名为空! ");

        if (!Objects.equals(colorMatchMaterialState, BomColorMaterialStateEnum.NO_THING.getCode())) {
            SdpDesignException.isTrue(StringUtils.isNotBlank(colorMatchMaterialName), "{}对色/包料的物料项目名不能为空! ", prototypeMaterialName);
            SdpDesignException.isTrue(prototypeNameList.contains(colorMatchMaterialName), "{} 对色/包料的物料项目名不存在! ", prototypeMaterialName);
        }

    }

    /**
     *  获取入参中所有物料项目名
     */
    private List<String> getAllPrototypeNameFromReq(List<BomOrderUpdateV3Req.AddBomMaterialReq> addBomMaterials, List<BomOrderUpdateV3Req.UpdateBomMaterialReq> updateBomMaterials, List<BomOrderUpdateV3Req.AddBomMaterialDemandReq> addBomDemandList, List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateBomDemandList) {
        List<String> prototypeNameList = new LinkedList<>();
        if (CollUtil.isNotEmpty(addBomMaterials)) {
            List<String> nameList = addBomMaterials.stream()
                    .map(BomOrderUpdateV3Req.AddBomMaterialReq::getPrototypeMaterialName)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nameList)) {
                prototypeNameList.addAll(nameList);
            }
        }
        if (CollUtil.isNotEmpty(updateBomMaterials)) {
            List<String> nameList = updateBomMaterials.stream()
                    .map(BomOrderUpdateV3Req.UpdateBomMaterialReq::getPrototypeMaterialName)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nameList)) {
                prototypeNameList.addAll(nameList);
            }
        }
        if (CollUtil.isNotEmpty(addBomDemandList)) {
            List<String> nameList = addBomDemandList.stream()
                    .map(BomOrderUpdateV3Req.AddBomMaterialDemandReq::getPrototypeMaterialName)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nameList)) {
                prototypeNameList.addAll(nameList);
            }
        }
        if (CollUtil.isNotEmpty(updateBomDemandList)) {
            updateBomDemandList.forEach(updateDemand -> {
                //更新物料
                List<BomOrderUpdateV3Req.DemandMaterialUpdateReq> materialUpdateReqList = updateDemand.getMaterialUpdateReqList();
                if (CollUtil.isNotEmpty(materialUpdateReqList) && Objects.nonNull(materialUpdateReqList.getFirst())) {
                    String prototypeMaterialName = materialUpdateReqList.getFirst().getPrototypeMaterialName();
                    if (StringUtils.isNotBlank(prototypeMaterialName)) {
                        prototypeNameList.add(prototypeMaterialName);
                    }
                }
                //更换物料
                List<BomOrderUpdateV3Req.DemandMaterialAddReq> materialAddReqList = updateDemand.getMaterialAddReqList();
                if (CollUtil.isNotEmpty(materialAddReqList) && Objects.nonNull(materialAddReqList.getFirst())) {
                    String prototypeMaterialName = materialAddReqList.getFirst().getPrototypeMaterialName();
                    if (StringUtils.isNotBlank(prototypeMaterialName)) {
                        prototypeNameList.add(prototypeMaterialName);
                    }
                }
            });
        }
        return prototypeNameList;
    }

    /**
     *  获取入参中所有物料项目名
     */
    private Map<String, Boolean> getAllPrototypeNameMapFromReq(List<BomOrderUpdateV3Req.AddBomMaterialReq> addBomMaterials, List<BomOrderUpdateV3Req.UpdateBomMaterialReq> updateBomMaterials, List<BomOrderUpdateV3Req.AddBomMaterialDemandReq> addBomDemandList, List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateBomDemandList) {
        Map<String, Boolean> nameMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(addBomMaterials)) {
            List<String> nameList = addBomMaterials.stream()
                    .map(BomOrderUpdateV3Req.AddBomMaterialReq::getPrototypeMaterialName)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nameList)) {
                nameList.forEach(item -> nameMap.put(item, false));
                // prototypeNameList.addAll(nameList);
            }
        }
        if (CollUtil.isNotEmpty(updateBomMaterials)) {
            List<String> nameList = updateBomMaterials.stream()
                    .map(BomOrderUpdateV3Req.UpdateBomMaterialReq::getPrototypeMaterialName)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nameList)) {
                nameList.forEach(item -> nameMap.put(item, false));
                // prototypeNameList.addAll(nameList);
            }
        }
        if (CollUtil.isNotEmpty(addBomDemandList)) {
            List<String> nameList = addBomDemandList.stream()
                    .map(BomOrderUpdateV3Req.AddBomMaterialDemandReq::getPrototypeMaterialName)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nameList)) {
                nameList.forEach(item -> nameMap.put(item, true));
                // prototypeNameList.addAll(nameList);
            }
        }
        if (CollUtil.isNotEmpty(updateBomDemandList)) {
            updateBomDemandList.forEach(updateDemand -> {
                Long materialDemandId = updateDemand.getBomMaterialDemandId();
                BomMaterialDemand demand = bomMaterialDemandRepository.getById(materialDemandId);
                if (Objects.nonNull(demand)) {
                    boolean searchMaterial = Objects.equals(demand.getMaterialSearchState(), Bool.YES.getCode());
                    nameMap.put(updateDemand.getPrototypeMaterialName(), searchMaterial);
                }
                //可能是暂存的需求
                else {
                    BomMaterialDemandTransient transientDemand = bomMaterialDemandTransientRepository.getById(materialDemandId);
                    if (Objects.nonNull(transientDemand)) {
                        boolean searchMaterial = Objects.equals(transientDemand.getMaterialSearchState(), Bool.YES.getCode());
                        nameMap.put(updateDemand.getPrototypeMaterialName(), searchMaterial);
                    }
                }
            });
        }
        return nameMap;
    }


    private void bomLatestCheck(BomOrder bomOrder, Integer bomVersionNum) {
        SdpDesignException.notNull(bomOrder, "无此bom单 {}", bomOrder.getBomId());
        //是否最新版本bom
        BomOrder latestBomOrder = bomOrderRepository.getLatestBomByDesignCode(bomOrder.getDesignCode());
        SdpDesignException.isTrue(Objects.equals(latestBomOrder.getBomId(), bomOrder.getBomId()), "当前bom已被重新提交，请刷新页面后重试");
        //bom单id不变,升版本的场景
        SdpDesignException.isTrue(Objects.equals(latestBomOrder.getVersionNum(), bomVersionNum), "当前bom已被重新提交，请刷新页面后重试");
    }

    private void checkSpecialMinPriceUnit(BomOrderUpdateV3Req req) {
        //校验特殊辅料最小单位, 最小单位
        List<BomOrderUpdateV3Req.UpdateBomMaterialReq> specialAccessoriesReqList = req.getUpdateBomMaterials().stream()
                .filter(item -> Objects.equals(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode(), item.getDemandType()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(specialAccessoriesReqList)) {
            return;
        }
        //查询特殊辅料
        List<Long> specialAccessoriesList = specialAccessoriesReqList.stream()
                .map(BomOrderUpdateV3Req.UpdateBomMaterialReq::getBomMaterialId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(specialAccessoriesList)) {
            return;
        }
        specialAccessoriesRepository.listByIds(specialAccessoriesList).forEach(item -> {
            SdpDesignException.isTrue(Objects.nonNull(item.getMinPrice()) && StringUtils.isNotBlank(item.getMinPriceUnit()),
                    "{} 辅料商品 {} 缺少价格最小价格与单位,请联系物料管理员完善后再添加", item.getName(), item.getSkuCode());

        });
    }

    // private void sendMq2FinanceCost(Long prototypeId) {
    //     PrototypeHistory prototype = prototypeHistoryRepository.getById(prototypeId);
    //     PlmDesignException.notNull(prototype, "找不到设计款信息! ");
    //
    //     FinanceCostMqDTO mqDto = FinanceCostMqDTO.builder()
    //             .prototypeId(prototypeId)
    //             .styleCode(prototype.getStyleCode())
    //             .designCode(prototype.getDesignCode())
    //             .demandTaskType(prototype.getDemandTaskType())
    //             .sampleType(prototype.getSampleType())
    //             .spuCreatedTime(prototype.getSpuCreatedTime())
    //             .entryTypistId(prototype.getEntryTypistId())
    //             .entryTypistName(prototype.getEntryTypistName())
    //             .build();
    //     log.info("===财务成本-bom提交后发送mq: mqDto: [{}] ===", JSON.toJSONString(mqDto));
    //     mqProducer.sendOnAfterCommit(MqMessageReq.build(
    //             MqBizTypeEnum.MATERIAL_TRACK_FINANCE_ORDER_PRODUCER,
    //             DesignMqConstant.MATERIAL_TRACK_FINANCE_ORDER_EXCHANGE,
    //             DesignMqConstant.MATERIAL_TRACK_FINANCE_ORDER_ROUTING_KEY,
    //             JSON.toJSONString(mqDto)));
    //     log.info("===财务成本-bom提交后_mq消息发送完毕===");
    // }

    // private void sendBomMaterialUpdateMqFanout(BomOrder bomOrder, BomOrder newBomOrder) {
    //     //第一次提交, 不会有物料变更, 不用推送变更物料
    //     if (Objects.isNull(newBomOrder)) {
    //         return;
    //     }
    //     //再次提交, 只推送需求中有变更物料的辅料
    //     List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfoList = this.buildDemandMaterialUpdateInfos(bomOrder, newBomOrder);
    //     if (CollUtil.isEmpty(materialUpdateInfoList)) {
    //         return;
    //     }
    //     BomMaterialUpdateDto bomMaterialUpdateDto = new BomMaterialUpdateDto();
    //     bomMaterialUpdateDto.setUpdateInfoList(materialUpdateInfoList);
    //     MqMessageReq messageReq = MqMessageReq.build(
    //             MqBizTypeEnum.BOM_SUBMIT_SYNC_MATERIAL_UPDATE,
    //             DesignMqConstant.BOM_MATERIAL_UPDATE_EXCHANGE,
    //             JSON.toJSONString(bomMaterialUpdateDto));
    //     log.info("===bom单物料变更-bom提交后发送mq: mqDto: [{}] ===", JSON.toJSONString(bomMaterialUpdateDto));
    //     mqProducer.sendOnAfterCommit(messageReq);
    //
    //     log.info("===bom单物料变更-bom提交后_mq消息发送完毕===");
    // }

    // private void sendBomMaterialUpdateMqFanout(BomOrder bomOrder, BomOrder newBomOrder) {
    //     List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfoList = new LinkedList<>();
    //     if (Objects.isNull(newBomOrder)) {
    //         //第一次提交, 不会有物料变更, 推送所有新增物料
    //         materialUpdateInfoList = this.buildUpdateInfoFirstSubmit(bomOrder);
    //     }else {
    //         materialUpdateInfoList = this.buildMaterialUpdateInfos(bomOrder, newBomOrder);
    //     }
    //     if (CollUtil.isEmpty(materialUpdateInfoList)) {
    //         return;
    //     }
    //     BomMaterialUpdateDto bomMaterialUpdateDto = new BomMaterialUpdateDto();
    //     bomMaterialUpdateDto.setUpdateInfoList(materialUpdateInfoList);
    //     MqMessageReq messageReq = MqMessageReq.build(
    //             MqBizTypeEnum.BOM_SUBMIT_SYNC_MATERIAL_UPDATE,
    //             DesignMqConstant.BOM_MATERIAL_UPDATE_EXCHANGE,
    //             JSON.toJSONString(bomMaterialUpdateDto));
    //     final Map<String, String> messageHeaders = new HashMap<>(16);
    //     messageHeaders.put("acs_code", "SDP_DESIGN_MATERIAL_UPDATE");
    //     log.info("===bom单物料变更-bom提交后发送mq: mqDto: [{}] ===", JSON.toJSONString(bomMaterialUpdateDto));
    //     mqProducer.sendOnAfterCommit(messageReq, messageHeaders);
    //
    //     log.info("===bom单物料变更-bom提交后_mq消息发送完毕===");
    // }

    private List<BomMaterialUpdateDto.MaterialUpdateInfo> buildUpdateInfoFirstSubmit(BomOrder bomOrder) {
        List<BomOrderMaterial> bomOrderMaterialList = bomOrderMaterialRepository.getListByBomId(bomOrder.getBomId());
        List<Long> snapshotIdList = bomOrderMaterialList.stream().map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
        Map<Long, MaterialSnapshot> snapshotMap = materialSnapshotRepository.listByIds(snapshotIdList).stream()
                .collect(Collectors.toMap(MaterialSnapshot::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));

        //需求信息
        Map<Long, BomMaterialDemand> demandMap = bomMaterialDemandRepository.listByBomId(bomOrder.getBomId()).stream()
                .collect(Collectors.toMap(BomMaterialDemand::getBomMaterialDemandId, Function.identity(), (k1, k2) -> k1));

        //封装物料变更入参
        List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfoList = new ArrayList<>();
        bomOrderMaterialList.forEach(item -> {
            MaterialSnapshot newSnapshot = snapshotMap.get(item.getMaterialSnapshotId());

            BomMaterialUpdateDto.MaterialUpdateInfo  updateInfo = new BomMaterialUpdateDto.MaterialUpdateInfo();

            if (Objects.nonNull(item.getBomMaterialDemandId()) && CollUtil.isNotEmpty(demandMap)) {
                BomMaterialDemand materialDemand = demandMap.get(item.getBomMaterialDemandId());
                if (Objects.nonNull(materialDemand)) {
                    //传供应链的需求id
                    updateInfo.setDemandId(materialDemand.getSupplyChainDemandId());
                }
            }
            updateInfo.setNewMaterialSnapShotId(item.getMaterialSnapshotId());
            updateInfo.setOldMaterialSnapShotId(null);
            //找料中的物料还没有物料快照
            if (Objects.nonNull(newSnapshot)) {
                updateInfo.setNewSkuId(newSnapshot.getSkuId());
                updateInfo.setNewCommodityId(newSnapshot.getCommodityId());
            }
            updateInfo.setOldSkuId(null);
            updateInfo.setOldCommodityId(null);
            materialUpdateInfoList.add(updateInfo);
        });
        return materialUpdateInfoList;
    }

    private List<BomMaterialUpdateDto.MaterialUpdateInfo> buildDemandMaterialUpdateInfos(BomOrder bomOrder, BomOrder newBomOrder) {
        //找出新旧版本bom中的物料
        List<BomOrderMaterial> newMaterialList = bomOrderMaterialRepository.getListByBomId(newBomOrder.getBomId());
        Map<Long, BomOrderMaterial> newMaterialMap = newMaterialList.stream()
                .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
        List<BomOrderMaterial> oldMaterialList = bomOrderMaterialRepository.getListByBomId(bomOrder.getBomId());

        //需求信息
        Map<Long, BomMaterialDemand> demandMap = bomMaterialDemandRepository.listByBomId(newBomOrder.getBomId()).stream()
                .collect(Collectors.toMap(BomMaterialDemand::getBomMaterialDemandId, Function.identity(), (k1, k2) -> k1));

        //新增的物料id
        List<Long> newMaterialIdList = newMaterialList.stream().map(BomOrderMaterial::getBomMaterialId).collect(Collectors.toList());
        List<Long> oldMaterialIdList = oldMaterialList.stream().map(BomOrderMaterial::getBomMaterialId).collect(Collectors.toList());
        List<Long> addMaterialIdList = CollUtil.subtractToList(newMaterialIdList, oldMaterialIdList);
        if (CollUtil.isEmpty(addMaterialIdList)) {
            log.info(" ===  bom提交无更换的物料-addMaterialIdList,bomId:{}  ==", bomOrder.getBomId());
            return List.of();
        }

        //找出有变更的物料(只要需求的)
        List<BomOrderMaterial> changeMaterialList = new LinkedList<>();
        addMaterialIdList.forEach(materialId -> {
            BomOrderMaterial bomOrderMaterial = newMaterialMap.get(materialId);
            if (Objects.nonNull(bomOrderMaterial.getReplaceBomMaterialId())
                    && Objects.nonNull(bomOrderMaterial.getBomMaterialDemandId())) {
                changeMaterialList.add(bomOrderMaterial);
            }
        });
        if (CollUtil.isEmpty(changeMaterialList)) {
            log.info(" ===  bom提交无更换的需求物料-changeMaterialList, bomId:{}  ==", bomOrder.getBomId());
            return List.of();
        }
        //被更新的物料信息
        List<Long> replaceIdList = changeMaterialList.stream()
                .map(BomOrderMaterial::getReplaceBomMaterialId).collect(Collectors.toList());
        List<BomOrderMaterial> replaceMaterialList = bomOrderMaterialRepository.listByIds(replaceIdList);
        Map<Long, BomOrderMaterial> replaceMaterialMap = replaceMaterialList.stream()
                .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));

        List<Long> replaceSnapshotIdList = replaceMaterialList.stream()
                .map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
        Map<Long, MaterialSnapshot> replaceSnapshotMap = materialSnapshotRepository.listByIds(replaceSnapshotIdList).stream()
                .collect(Collectors.toMap(MaterialSnapshot::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));

        //新物料快照
        List<Long> changeSnapshotIdList = changeMaterialList.stream()
                .map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
        Map<Long, MaterialSnapshot> changeSnapshotMap = materialSnapshotRepository.listByIds(changeSnapshotIdList).stream()
                .collect(Collectors.toMap(MaterialSnapshot::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));

        //封装物料变更入参
        List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfoList = new ArrayList<>();
        changeMaterialList.forEach(item -> {
            MaterialSnapshot newSnapshot = changeSnapshotMap.get(item.getMaterialSnapshotId());
            BomOrderMaterial oldMaterial = replaceMaterialMap.get(item.getReplaceBomMaterialId());
            //替换的物料不存在; (暂存添加的物料, 不是原物料, 不用处理)
            if (Objects.isNull(oldMaterial)) {
                return;
            }
            MaterialSnapshot oldSnapshot = replaceSnapshotMap.get(oldMaterial.getMaterialSnapshotId());

            BomMaterialUpdateDto.MaterialUpdateInfo  updateInfo = new BomMaterialUpdateDto.MaterialUpdateInfo();

            //只处理需求的物料变更
            if (Objects.isNull(item.getBomMaterialDemandId()) || CollUtil.isNotEmpty(demandMap)) {
                return;
            }

            //传供应链的需求id
            if (Objects.nonNull(item.getBomMaterialDemandId()) && CollUtil.isNotEmpty(demandMap)) {
                BomMaterialDemand materialDemand = demandMap.get(item.getBomMaterialDemandId());
                if (Objects.isNull(materialDemand)) {
                    return;
                }
                updateInfo.setDemandId(materialDemand.getSupplyChainDemandId());
            }

            updateInfo.setNewMaterialSnapShotId(item.getMaterialSnapshotId());
            //找料中的物料无快照
            if (Objects.nonNull(newSnapshot)) {
                updateInfo.setNewSkuId(newSnapshot.getSkuId());
                updateInfo.setNewCommodityId(newSnapshot.getCommodityId());
            }
            if (Objects.nonNull(oldSnapshot)) {
                updateInfo.setOldMaterialSnapShotId(oldSnapshot.getMaterialSnapshotId());
                updateInfo.setOldSkuId(oldSnapshot.getSkuId());
                updateInfo.setOldCommodityId(oldSnapshot.getCommodityId());
            }
            materialUpdateInfoList.add(updateInfo);
        });
        return materialUpdateInfoList;
    }


    // private List<BomMaterialUpdateDto.MaterialUpdateInfo> buildMaterialUpdateInfos(BomOrder bomOrder, BomOrder newBomOrder) {
    //     //找出新旧版本bom中的物料
    //     List<BomOrderMaterial> newMaterialList = bomOrderMaterialRepository.getListByBomId(newBomOrder.getBomId());
    //     Map<Long, BomOrderMaterial> newMaterialMap = newMaterialList.stream()
    //             .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
    //     List<BomOrderMaterial> oldMaterialList = bomOrderMaterialRepository.getListByBomId(bomOrder.getBomId());
    //
    //     //需求信息
    //     Map<Long, BomMaterialDemand> demandMap = bomMaterialDemandRepository.listByBomId(newBomOrder.getBomId()).stream()
    //             .collect(Collectors.toMap(BomMaterialDemand::getBomMaterialDemandId, Function.identity(), (k1, k2) -> k1));
    //
    //     //新增的物料id
    //     List<Long> newMaterialIdList = newMaterialList.stream().map(BomOrderMaterial::getBomMaterialId).collect(Collectors.toList());
    //     List<Long> oldMaterialIdList = oldMaterialList.stream().map(BomOrderMaterial::getBomMaterialId).collect(Collectors.toList());
    //     List<Long> addMaterialIdList = CollUtil.subtractToList(newMaterialIdList, oldMaterialIdList);
    //     if (CollUtil.isEmpty(addMaterialIdList)) {
    //         log.info(" ===  bom提交无更换的物料-addMaterialIdList,bomId:{}  ==", bomOrder.getBomId());
    //         return List.of();
    //     }
    //
    //     //找出有变更的物料
    //     List<BomOrderMaterial> changeMaterialList = new LinkedList<>();
    //     addMaterialIdList.forEach(materialId -> {
    //         BomOrderMaterial bomOrderMaterial = newMaterialMap.get(materialId);
    //         if (Objects.nonNull(bomOrderMaterial.getReplaceBomMaterialId())) {
    //             changeMaterialList.add(bomOrderMaterial);
    //         }
    //     });
    //     if (CollUtil.isEmpty(changeMaterialList)) {
    //         log.info(" ===  bom提交无更换的物料-changeMaterialList, bomId:{}  ==", bomOrder.getBomId());
    //         return List.of();
    //     }
    //     //被更新的物料信息
    //     List<Long> replaceIdList = changeMaterialList.stream()
    //             .map(BomOrderMaterial::getReplaceBomMaterialId).collect(Collectors.toList());
    //     List<BomOrderMaterial> replaceMaterialList = bomOrderMaterialRepository.listByIds(replaceIdList);
    //     Map<Long, BomOrderMaterial> replaceMaterialMap = replaceMaterialList.stream()
    //             .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
    //
    //     List<Long> replaceSnapshotIdList = replaceMaterialList.stream()
    //             .map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
    //     Map<Long, MaterialSnapshot> replaceSnapshotMap = materialSnapshotRepository.listByIds(replaceSnapshotIdList).stream()
    //             .collect(Collectors.toMap(MaterialSnapshot::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));
    //
    //     //新物料快照
    //     List<Long> changeSnapshotIdList = changeMaterialList.stream()
    //             .map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
    //     Map<Long, MaterialSnapshot> changeSnapshotMap = materialSnapshotRepository.listByIds(changeSnapshotIdList).stream()
    //             .collect(Collectors.toMap(MaterialSnapshot::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));
    //
    //     //封装物料变更入参
    //     List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfoList = new ArrayList<>();
    //     changeMaterialList.forEach(item -> {
    //         MaterialSnapshot newSnapshot = changeSnapshotMap.get(item.getMaterialSnapshotId());
    //         BomOrderMaterial oldMaterial = replaceMaterialMap.get(item.getReplaceBomMaterialId());
    //         //替换的物料不存在; (暂存添加的物料, 不是原物料, 不用处理)
    //         if (Objects.isNull(oldMaterial)) {
    //             return;
    //         }
    //         MaterialSnapshot oldSnapshot = replaceSnapshotMap.get(oldMaterial.getMaterialSnapshotId());
    //
    //         BomMaterialUpdateDto.MaterialUpdateInfo  updateInfo = new BomMaterialUpdateDto.MaterialUpdateInfo();
    //
    //         //传供应链的需求id
    //         if (Objects.nonNull(item.getBomMaterialDemandId()) && CollUtil.isNotEmpty(demandMap)) {
    //             BomMaterialDemand materialDemand = demandMap.get(item.getBomMaterialDemandId());
    //             if (Objects.nonNull(materialDemand)) {
    //                 updateInfo.setDemandId(materialDemand.getSupplyChainDemandId());
    //             }
    //         }
    //
    //         updateInfo.setNewMaterialSnapShotId(item.getMaterialSnapshotId());
    //         //找料中的物料无快照
    //         if (Objects.nonNull(newSnapshot)) {
    //             updateInfo.setNewSkuId(newSnapshot.getSkuId());
    //             updateInfo.setNewCommodityId(newSnapshot.getCommodityId());
    //         }
    //         if (Objects.nonNull(oldSnapshot)) {
    //             updateInfo.setOldMaterialSnapShotId(oldSnapshot.getMaterialSnapshotId());
    //             updateInfo.setOldSkuId(oldSnapshot.getSkuId());
    //             updateInfo.setOldCommodityId(oldSnapshot.getCommodityId());
    //         }
    //         materialUpdateInfoList.add(updateInfo);
    //     });
    //     return materialUpdateInfoList;
    // }



    private void bomSyncEstimateUsage(BomOrder bomOrder) {
        BomOrderVo bomOrderVo = BeanUtil.copyProperties(bomOrder, BomOrderVo.class);
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.BOM_OPERATE_SYNC_ESTIMATE_USAGE,
                DesignMqConstant.BOM_SUBMIT_SYNC_ESTIMATE_USAGE_EXCHANGE,
                DesignMqConstant.BOM_SUBMIT_SYNC_ESTIMATE_USAGE_ROUTING_KEY,
                JSON.toJSONString(bomOrderVo));
        log.info("【bom提交-预估核价用量同步】mqMessageReq:{}", JSON.toJSONString(mqMessageReq));
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

}
