package tech.tiangong.sdp.design.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.entity.OnShelfSkc;
import tech.tiangong.sdp.design.repository.OnShelfSkcRepository;
import tech.tiangong.sdp.design.service.OnShelfSkcService;
import tech.tiangong.sdp.design.vo.req.demand.OnShelfSkcReq;
import tech.tiangong.sdp.design.vo.resp.prototype.OnShelfSkcVo;

/**
 * 上架skc信息表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OnShelfSkcServiceImpl implements OnShelfSkcService {
    private final OnShelfSkcRepository onShelfSkcRepository;


    @Override
    public OnShelfSkcVo getById(Long id) {
        OnShelfSkc entity = onShelfSkcRepository.getById(id);
        OnShelfSkcVo vo = new OnShelfSkcVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(OnShelfSkcReq req) {
        OnShelfSkc entity = new OnShelfSkc();
        BeanUtils.copyProperties(req, entity);
        onShelfSkcRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(OnShelfSkcReq req) {
        OnShelfSkc entity = new OnShelfSkc();
        BeanUtils.copyProperties(req, entity);
        onShelfSkcRepository.updateById(entity);
    }
}
