package tech.tiangong.sdp.design.business;

import cn.hutool.core.lang.Assert;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.entity.PrototypeHistory;
import tech.tiangong.sdp.design.enums.PrototypeStatusEnum;
import tech.tiangong.sdp.design.remote.DesignerRemoteHelper;
import tech.tiangong.sdp.design.repository.PrototypeHistoryRepository;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.vo.req.prototype.ChgDesignerReq;
import tech.tiangong.sdp.material.pojo.dto.DesignerDTO;
import tech.tiangong.sdp.material.pojo.web.request.designer.DesignerRemoteReq;

/**
 * @<PERSON>sky
 * @create 2021/8/28
 */
@Slf4j
public class ChgDesignerTransaction implements Transaction{
    private ChgDesignerReq req;

    public ChgDesignerTransaction(ChgDesignerReq req) {
        this.req = req;
    }

    protected void chg(){
        log.info("排单变更={}", req);
        Assert.notEmpty(req.getDesignCodeList(), "设计款号不能为空");
        DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
        designerRemoteReq.setDesignerId(String.valueOf(req.getDesignerId()));
        DesignerDTO designerDTO = SpringUtil.getBean(DesignerRemoteHelper.class).getByDesignerId(designerRemoteReq);
        Assert.notNull(designerDTO, "不存在此设计师");

        PrototypeRepository prototypeRepository = SpringUtil.getBean(PrototypeRepository.class);

        for (String designCode : req.getDesignCodeList()) {
            Prototype prototype = prototypeRepository.getByDesignCode(designCode);
            Assert.notNull(prototype, "不存在此设计款号:{}", designCode);
            Assert.isFalse(prototype.getIsCanceled(), "{}设计款号已被取消，无法变更设计师", designCode);
            Assert.isTrue(PrototypeStatusEnum.WAIT_DECOMPOSE.getCode().equals(prototype.getPrototypeStatus())
                    , "{}设计款号已拆版，无法变更设计师", designCode);
            Prototype updatePrototype = Prototype.builder()
                    .prototypeId(prototype.getPrototypeId())
                    .designerId(req.getDesignerId())
                    .designerCode(designerDTO.getDesignerCode())
                    .designerName(designerDTO.getDesignerName())
                    .designerGroup(designerDTO.getDesignerGroupName())
                    .designerGroupCode(designerDTO.getDesignerGroupCode())
                    .build();


            PrototypeHistory updatePrototypeHistory = new PrototypeHistory();
            BeanUtils.copyProperties(updatePrototype, updatePrototypeHistory);


            Assert.isTrue(SpringUtil.getBean(PrototypeHistoryRepository.class).updateById(updatePrototypeHistory)
                    , "更新排单变更失败！");
            Assert.isTrue(prototypeRepository.updateById(updatePrototype), "更新排单变更失败！");
        }
    }

    @Override
    public void execute() {
        chg();
    }
}
