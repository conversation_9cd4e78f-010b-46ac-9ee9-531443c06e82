package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.SpotSkcService;
import tech.tiangong.sdp.design.vo.req.spot.SpotSkcBatchCancelReq;
import tech.tiangong.sdp.design.vo.req.spot.SpotSkcUpdateReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcUpdateVo;


/**
 * 现货SKC-web
 *
 * <AUTHOR>
 * @since 2025-02-25 11:37:13
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB +  UrlVersionConstant.VERSION_V1  + "/spot-skc")
public class SpotSkcController extends BaseController {
    private final SpotSkcService spotSkcService;

    /**
     * 编辑SKC
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/update")
    public DataResponse<SpotSkcUpdateVo> update(@RequestBody @Validated SpotSkcUpdateReq req) {
        return DataResponse.ok(spotSkcService.update(req));
    }

    /**
     * 批量取消SKC
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @NoRepeatSubmitLock
    @PostMapping("/batch-cancel")
    public DataResponse<Void> batchCancel(@RequestBody @Validated SpotSkcBatchCancelReq req) {
        spotSkcService.batchCancel(req);
        return DataResponse.ok();
    }


    // /**
    //  * 列表
    //  *
    //  * @param query 分页参数
    //  * @return PageRespVo<SpotSkcVo>
    //  */
    // @PostMapping("/page")
    // public DataResponse<PageRespVo<SpotSkcVo>> page(@RequestBody @Validated SpotSkcQuery query) {
    //     return DataResponse.ok(spotSkcService.page(query));
    // }
    //
    // /**
    //  * 详情
    //  *
    //  * @param spotSkcId 主键id
    //  * @return 响应结果
    //  */
    // @GetMapping("/{spotSkcId}")
    // public DataResponse<SpotSkcVo> getById(@PathVariable(value = "spotSkcId") Long spotSkcId) {
    //     return DataResponse.ok(spotSkcService.getById(spotSkcId));
    // }


}
