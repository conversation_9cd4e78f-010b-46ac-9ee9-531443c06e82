package tech.tiangong.sdp.design.remote;

import cn.yibuyun.framework.net.DataResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import tech.tiangong.sdp.qy.vo.req.ZjNotify2OldJvReq;

/**
 * 致景通知转发client
 */
@FeignClient(value = "tg-open-service",
        contextId = "PLM-ZjNotify2OldJvClient",
        path = "/tg-open/inner/v1/mq",
        url = "${domain.tg-api}")
public interface ZjNotify2OldJvClient {

    /**
     * 转发致景通知到旧JV
     *
     * @param req 入参
     * @return void
     */
    @PostMapping("/from-new-jv")
    DataResponse<Void> fromNewJv(@RequestBody ZjNotify2OldJvReq req);

}
