package tech.tiangong.sdp.design.remote.zj.demand;

import cn.yibuyun.framework.net.DataResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import tech.tiangong.sdp.core.config.ZjOpenFeignUserContentConfig;
import tech.tiangong.sdp.design.vo.req.zj.demand.*;
import tech.tiangong.sdp.design.vo.resp.zj.demand.AccessoryDemandCreateOpenV2Resp;
import tech.tiangong.sdp.design.vo.resp.zj.demand.CraftDemandCreateOpenV2Resp;

/**
 * @Created by jeromel<PERSON>
 * @ClassName PlmDemandRemoteHelper
 * @Description 致景PLM-需求模块 对接路由
 * @Date 2024/11/30 11:35
 */
@FeignClient(value = "plm-design",
        contextId = "PLM-DemandCommodityClient",
        configuration = ZjOpenFeignUserContentConfig.class,
        path = "/zj-tg-api/plm-design/open/v2",
        url = "${cx-tg.domain.url}")
public interface ZjPlmDemandRemoteHelper {

    /**
     * 创建辅料找料需求
     *
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84515"
     * @param req
     * @return
     */
    @PostMapping("/accessory-demand/create")
    DataResponse<AccessoryDemandCreateOpenV2Resp> accessoryDemandCreate(@RequestBody AccessoryDemandCreateOpenV2Req req);

    /**
     * 创建工艺需求
     *
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84516"
     * @param req
     * @return
     */
    @PostMapping("/craft-demand/create")
    DataResponse<CraftDemandCreateOpenV2Resp> craftDemandCreate(@RequestBody CraftDemandCreateOpenV2Req req);

    /**
     * 工艺需求 请求关闭
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84517"
     *
     * @param req
     * @return
     */
    @PostMapping("/craft-demand/close")
    DataResponse<Void> craftDemandClose(@RequestBody CraftDemandCloseOpenV2Req req);


    /**
     * 辅料需求 请求关闭
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84518"
     *
     * @param req
     * @return
     */
    @PostMapping("/accessory-demand/close")
    DataResponse<Void> accessoryDemandClose(@RequestBody AccessoryDemandCloseOpenV2Req req);

    /**
     * 辅料需求 请求关闭 (好料发起)
     *
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84519"
     * @param req
     * @return
     */
    @PostMapping("/accessory-demand-by-houliu/close")
    DataResponse<Void> accessoryDemandCloseHouliu(@RequestBody AccessoryDemandCloseHouliuOpenV2Req req);

}
