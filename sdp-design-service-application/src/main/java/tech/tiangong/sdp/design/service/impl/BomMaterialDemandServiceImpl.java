package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.BizChannelEnum;
import tech.tiangong.sdp.design.enums.BomDemandStateEnum;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjNotify2OldJvRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.BomMaterialDemandService;
import tech.tiangong.sdp.design.vo.dto.bom.HouliuDemandCloseDto;
import tech.tiangong.sdp.design.vo.query.bom.BomMaterialDemandQuery;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialDemandReq;
import tech.tiangong.sdp.design.vo.req.zj.demand.AccessoryDemandCloseHouliuOpenV2Req;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialDemandVo;
import tech.tiangong.sdp.utils.Bool;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

;

/**
 * bom物料需求表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BomMaterialDemandServiceImpl implements BomMaterialDemandService {
    private final BomMaterialDemandRepository bomMaterialDemandRepository;
    private final BomMaterialDemandTransientRepository bomMaterialDemandTransientRepository;
    private final BomOrderRepository bomOrderRepository;
    private final BomOrderTransientRepository bomOrderTransientRepository;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;
    private final PrototypeRepository prototypeRepository;
    private final ZjNotify2OldJvRemoteHelper zjNotify2OldJvRemoteHelper;

    @Override
    public PageRespVo<BomMaterialDemandVo> page(BomMaterialDemandQuery query) {
        IPage<BomMaterialDemandVo> page = bomMaterialDemandRepository.findPage(query);
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    public BomMaterialDemandVo getById(Long id) {
        BomMaterialDemand entity = bomMaterialDemandRepository.getById(id);
        if (Objects.isNull(entity)) {
            return null;
        }
        return this.entity2Vo(entity);
    }

    private BomMaterialDemandVo entity2Vo(BomMaterialDemand entity) {
        BomMaterialDemandVo vo = new BomMaterialDemandVo();
        BeanUtils.copyProperties(entity, vo);
        String demandPicture = entity.getDemandPicture();
        if (StringUtils.isNotBlank(demandPicture)) {
            vo.setDemandPictureList(StrUtil.splitTrim(demandPicture, StrUtil.COMMA));
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(BomMaterialDemandReq req) {
        BomMaterialDemand entity = new BomMaterialDemand();
        BeanUtils.copyProperties(req, entity);
        bomMaterialDemandRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BomMaterialDemandReq req) {
        BomMaterialDemand entity = new BomMaterialDemand();
        BeanUtils.copyProperties(req, entity);
        bomMaterialDemandRepository.updateById(entity);
    }

    @Override
    public void remove(Long id) {
        bomMaterialDemandRepository.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void houliuDemandCloseCallBack(MqMessageReq mqMessageReq) {
        HouliuDemandCloseDto closeDto = JSON.parseObject(mqMessageReq.getMqContent(), HouliuDemandCloseDto.class);
        SdpDesignException.notNull(closeDto,"好料需求关闭实体转换有误，接收关闭需求信息失败");
        log.info("===【好料需求关闭推送回调】 入参: {} ===", JSON.toJSONString(closeDto));
        //接收好料需求关闭处理: 更新需求状态
        List<HouliuDemandCloseDto.CloseDemandInfo> closedDtos = closeDto.getClosedDtos();

        //查询当前最新版本bom单
        List<BomMaterialDemand> closeDemandList = new LinkedList<>();
        List<BomMaterialDemandTransient> closeDemandTransientList = new LinkedList<>();
        LocalDateTime closeTime = LocalDateTime.now();

        //推送致景入参
        List<AccessoryDemandCloseHouliuOpenV2Req.CloseDemandInfo> newJvCloseDemandZjList = new ArrayList<>();
        List<AccessoryDemandCloseHouliuOpenV2Req.CloseDemandInfo> oldJvCloseDemandZjList = new ArrayList<>();

        List<BomMaterialDemand> noDemandList = new ArrayList<>();
        for (HouliuDemandCloseDto.CloseDemandInfo supplyDemand : closedDtos) {
            Long supplyDemandId = supplyDemand.getDemandId();
            //根据履约需求id查询最新的bom需求
            BomMaterialDemand materialDemand = bomMaterialDemandRepository.latestBySupplyDemandId(supplyDemandId);
            if (Objects.isNull(materialDemand)) {
                log.info("===【好料需求关闭推送回调】, 需求不存在; supplyDemandId:{} ===", supplyDemandId);
                noDemandList.add(materialDemand);
                return;
            }
            //如果不是已提交状态或PLM关闭状态, 不更新
            if (!(Objects.equals(materialDemand.getDemandState(), BomDemandStateEnum.SUBMIT.getCode())
                    || Objects.equals(materialDemand.getDemandState(), BomDemandStateEnum.CLOSED_BY_PLM.getCode()))) {
                log.info("===【好料需求关闭推送回调】, 当前需求状态为{}, 不需要关闭; supplyDemandId:{} ===",
                        BomDemandStateEnum.findByCode(materialDemand.getDemandState()).getDesc(), supplyDemandId);
                return;
            }
            //根据bomId查询bom单; 再根据skc查询最新bom单;
            Long bomId = materialDemand.getBomId();
            BomOrder bomOrder = bomOrderRepository.getById(materialDemand.getBomId());
            BomOrder latestBomOrder = bomOrderRepository.getLatestBomByDesignCode(bomOrder.getDesignCode());
            //如果不是最新bom单, 不操作;
            if (!Objects.equals(bomId, latestBomOrder.getBomId())) {
                log.info("===【好料需求关闭推送回调】, 需求对应bom单不是最新版本, 不需要关闭; supplyDemandId:{}; bomId:{} ===", supplyDemandId, bomId);
                return;
            }
            Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());
            SdpDesignException.notNull(prototype, "skc不存在!{}", bomOrder.getDesignCode());
            //更新bom需求状态: -> 履约关闭;
            BomMaterialDemand closeDemand = new BomMaterialDemand();
            closeDemand.setBomMaterialDemandId(materialDemand.getBomMaterialDemandId());
            closeDemand.setDemandState(BomDemandStateEnum.CLOSED_BY_SUPPLY.getCode());
            closeDemand.setSupplyCloseReason(supplyDemand.getCloseReason());
            closeDemand.setSupplyCloseTime(closeTime);
            closeDemandList.add(closeDemand);

            //如果bom有暂存, 同步到暂存表
            if (Objects.equals(bomOrder.getTransientState(), Bool.YES.getCode())) {
                BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomId);
                BomMaterialDemandTransient transientDemand = bomMaterialDemandTransientRepository.getByBomTransientIdAndOriginDemandId(transientBom.getBomTransientId(), materialDemand.getBomMaterialDemandId());
                if (Objects.nonNull(transientDemand)) {
                    BomMaterialDemandTransient closeDemandTransient = new BomMaterialDemandTransient();
                    closeDemandTransient.setBomMaterialDemandId(transientDemand.getBomMaterialDemandId());
                    closeDemandTransient.setDemandState(BomDemandStateEnum.CLOSED_BY_SUPPLY.getCode());
                    closeDemandTransient.setSupplyCloseReason(supplyDemand.getCloseReason());
                    closeDemandTransient.setSupplyCloseTime(closeTime);
                    closeDemandTransientList.add(closeDemandTransient);
                }
            }
            //推送致景入参封装
            AccessoryDemandCloseHouliuOpenV2Req.CloseDemandInfo closeDemandReq = new AccessoryDemandCloseHouliuOpenV2Req.CloseDemandInfo();
            closeDemandReq.setClosedTime(closeTime);
            closeDemandReq.setCloseReason(supplyDemand.getCloseReason());
            closeDemandReq.setExtBomMaterialDemandId(materialDemand.getBomMaterialDemandId());
            closeDemandReq.setExtBomId(bomOrder.getBomId());
            closeDemandReq.setExtVersionNum(bomOrder.getVersionNum());
            if (Objects.equals(prototype.getBizChannel(), BizChannelEnum.NEW_JV.getCode())) {
                newJvCloseDemandZjList.add(closeDemandReq);
            }else if (Objects.equals(prototype.getBizChannel(), BizChannelEnum.OLD_JV.getCode())) {
                oldJvCloseDemandZjList.add(closeDemandReq);
            }
        };

        //履约关闭需求更新
        if (CollUtil.isNotEmpty(closeDemandList)) {
            bomMaterialDemandRepository.updateBatchById(closeDemandList);

        }
        if (CollUtil.isNotEmpty(closeDemandTransientList)) {
            bomMaterialDemandTransientRepository.updateBatchById(closeDemandTransientList);
        }
        //推送致景
        if (CollUtil.isNotEmpty(newJvCloseDemandZjList)) {
            AccessoryDemandCloseHouliuOpenV2Req openReq = new AccessoryDemandCloseHouliuOpenV2Req();
            openReq.setCloseDemandInfos(newJvCloseDemandZjList);
            openReq.setBizChannel(BizChannelEnum.NEW_JV.getCode());
            zjDesignRemoteHelper.accessoryDemandCloseHouliu(openReq);
        }
        if (CollUtil.isNotEmpty(oldJvCloseDemandZjList)) {
            AccessoryDemandCloseHouliuOpenV2Req openReq = new AccessoryDemandCloseHouliuOpenV2Req();
            openReq.setCloseDemandInfos(oldJvCloseDemandZjList);
            openReq.setBizChannel(BizChannelEnum.OLD_JV.getCode());
            zjDesignRemoteHelper.accessoryDemandCloseHouliu(openReq);
        }
        if (CollUtil.isNotEmpty(noDemandList)) {
            //转发到旧JV
            log.info("=== 履约关闭需求-转发旧JV ====");
            zjNotify2OldJvRemoteHelper.notify2OldJv(mqMessageReq);
        }
    }

    @Override
    public List<BomMaterialDemandVo> listByBomId(Long bomId) {
        if (Objects.isNull(bomId)) {
            return List.of();
        }

        return bomMaterialDemandRepository.listByBomId(bomId).stream()
                .map(this::entity2Vo)
                .collect(Collectors.toList());
    }

    @Override
    public List<BomMaterialDemandVo> listByBomIds(List<Long> bomIdList) {
        if (CollUtil.isEmpty(bomIdList)) {
            return List.of();
        }
        return bomMaterialDemandRepository.listByBomIdList(bomIdList).stream()
                .map(this::entity2Vo)
                .collect(Collectors.toList());
    }
}
