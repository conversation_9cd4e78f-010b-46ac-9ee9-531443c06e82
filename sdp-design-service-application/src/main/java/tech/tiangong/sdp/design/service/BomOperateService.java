package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.enums.BomOrderStateEnum;
import tech.tiangong.sdp.design.enums.PushZjTypeEnum;
import tech.tiangong.sdp.design.vo.dto.bom.BomCopyInfoDto;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderUpdateV3Req;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderUpdateResp;

/**
 * bom单 暂存/提交 操作接口
 * <AUTHOR>
 * @date 2022/11/17 12:51
 */
public interface BomOperateService {

    /**
     * Bom提交
     * @param req 入参
     * @return BomOrderUpdateResp
     */
    BomOrderUpdateResp submit(BomOrderUpdateV3Req req);

    /**
     * 完成找料
     * @param bomId bom单id
     */
    void finishSearch(Long bomId);

    /**
     * 复制bom, 生成新版本的bom; (若有暂存,将暂存信息指向新bom: 修改transientBom下的bomId)
     * @param bomOrder bom单
     * @param copyTransient 是否复制暂存信息
     * @param newBomVersion 新bom版本
     * @return 新版本的bom
     */
    BomOrder copyNewVersionBom(BomOrder bomOrder, boolean copyTransient, Integer newBomVersion, BomOrderStateEnum bomOrderStateEnum, BomCopyInfoDto copyInfoDto);

    /**
     * 推送bom信息给致景
     * @param bomOrder bom
     * @param pushZjTypeEnum 推送类型
     */
    void pushBom2Zj(BomOrder bomOrder, PushZjTypeEnum pushZjTypeEnum);

}
