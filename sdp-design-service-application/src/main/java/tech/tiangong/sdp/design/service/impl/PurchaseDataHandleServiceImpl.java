package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.entity.MaterialPurchaseFollow;
import tech.tiangong.sdp.design.entity.PurchaseApplyFollow;
import tech.tiangong.sdp.design.enums.CraftCuttingTypeEnum;
import tech.tiangong.sdp.design.repository.CraftDemandInfoRepository;
import tech.tiangong.sdp.design.repository.MaterialPurchaseFollowRepository;
import tech.tiangong.sdp.design.repository.PurchaseApplyFollowRepository;
import tech.tiangong.sdp.design.service.PurchaseDataHandleService;
import tech.tiangong.sdp.design.vo.dto.purchase.CuttingProcessUpdateDto;
import tech.tiangong.sdp.utils.CommonUtil;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 采购相关数据处理 service
 *
 * <AUTHOR>
 * @date 2023/4/2 13:16
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class PurchaseDataHandleServiceImpl implements PurchaseDataHandleService {

    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final PurchaseApplyFollowRepository purchaseApplyFollowRepository;
    private final MaterialPurchaseFollowRepository materialPurchaseFollowRepository;


    /**
     * 刷新自动下采购的辅料裁前工艺
     */
    @Override
    public Integer updateCuttingProcess(String purchaseOrderNo) {
        //需要刷新的表: 采购表: purchase_apply_follow; 物料采购跟进表: material_purchase_follow;
        //查找自动下采购没有记录裁前工艺字段的物料:

        // 有裁前工艺的, 有物料回复的, 找料需求下的辅料, 有采购记录, 但没有记录cuttingProcess字段的物料;
        List<CuttingProcessUpdateDto> errorCuttingProcessList = purchaseApplyFollowRepository.listErrorCuttingProcess(purchaseOrderNo);
        SdpDesignException.notEmpty(errorCuttingProcessList, "无裁前工艺异常数据, 不需处理!");

        //根据配版单号取唯
        Map<String, CuttingProcessUpdateDto> cuttingProcessMap = CommonUtil.getMapByList(errorCuttingProcessList,
                CuttingProcessUpdateDto::getCuttingCode);

        //剪版单号
        List<String> cuttingCodeList = errorCuttingProcessList.stream()
                .map(CuttingProcessUpdateDto::getCuttingCode).distinct().collect(Collectors.toList());
        SdpDesignException.notEmpty(cuttingCodeList, "无裁前工艺异常数据, 不需处理!");

        //查询bom物料对应的工艺需求
        List<Long> craftDemandIdList = errorCuttingProcessList.stream()
                .map(CuttingProcessUpdateDto::getCraftDemandId).distinct().collect(Collectors.toList());
        Map<Long, List<CraftDemandInfo>> craftMap = craftDemandInfoRepository.listByIds(craftDemandIdList).stream()
                .collect(Collectors.groupingBy(CraftDemandInfo::getBomMaterialId));
        if (CollUtil.isEmpty(craftMap)) {
            throw new SdpDesignException("工艺数据查询为空!");
        }

        //查询并更新物料采购记录
        List<MaterialPurchaseFollow> purchaseFollowList = materialPurchaseFollowRepository.listByOrderCodes(cuttingCodeList);
        List<MaterialPurchaseFollow> updateFollowList = new LinkedList<>();
        List<PurchaseApplyFollow> updatePurchaseFollowList = new LinkedList<>();
        purchaseFollowList.forEach(item -> {
            CuttingProcessUpdateDto cuttingProcessUpdateDto = cuttingProcessMap.get(item.getCuttingCode());
            if (Objects.isNull(cuttingProcessUpdateDto)) {
                return;
            }
            //注: 一个物料可能有多个裁前工艺
            List<CraftDemandInfo> craftDemandInfoList = craftMap.get(item.getBomMaterialId());
            if (CollUtil.isEmpty(craftDemandInfoList)) {
                return;
            }

            //拼接裁前二次工艺,取末级(有些只有二级)
            List<String> beforeCraftCategory = this.getBeforeCraftCategory(craftDemandInfoList);
            String cuttingProcess = StrUtil.join(StrUtil.COMMA, beforeCraftCategory);

            //物料采购记录
            MaterialPurchaseFollow updateFollow = new MaterialPurchaseFollow();
            updateFollow.setMaterialPurchaseFollowId(item.getMaterialPurchaseFollowId());
            updateFollow.setCuttingProcess(cuttingProcess);
            updateFollowList.add(updateFollow);

            //采购单
            PurchaseApplyFollow purchaseApplyFollow = new PurchaseApplyFollow();
            purchaseApplyFollow.setPurchaseApplyFollowId(cuttingProcessUpdateDto.getPurchaseApplyFollowId());
            purchaseApplyFollow.setCuttingProcess(cuttingProcess);
            updatePurchaseFollowList.add(purchaseApplyFollow);

        });

        //更新cuttingProcess
        if (CollUtil.isNotEmpty(updateFollowList)) {
            materialPurchaseFollowRepository.updateBatchById(updateFollowList);
        }
        if (CollUtil.isNotEmpty(updatePurchaseFollowList)) {
            purchaseApplyFollowRepository.updateBatchById(updatePurchaseFollowList);
        }

        return updateFollowList.size();
    }

    private List<String> getBeforeCraftCategory(List<CraftDemandInfo> craftDemandInfoList) {
        return craftDemandInfoList.stream()
                .filter(item -> Objects.equals(CraftCuttingTypeEnum.BEFORE.getCode(), item.getCraftsRequire()))
                .map(item -> StringUtils.isBlank(item.getCategory3()) ? item.getCategory2() : item.getCategory3())
                .collect(Collectors.toList());
    }

}
