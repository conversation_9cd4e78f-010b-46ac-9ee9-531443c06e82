package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.SdpStyleTypeEnum;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.base.prototype.OpsObject;
import tech.tiangong.sdp.design.vo.req.style.PopCreateInfoReq;
import tech.tiangong.sdp.design.vo.req.style.SpuBatchReq;
import tech.tiangong.sdp.design.vo.req.style.SpuInnerQuery;
import tech.tiangong.sdp.design.vo.resp.style.*;
import tech.tiangong.sdp.utils.Bool;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* spu-inner service
* <AUTHOR>
*/

@Slf4j
@Service
@RequiredArgsConstructor
public class SpuInnerServiceImpl implements SpuInnerService {

    private final DesignStyleRepository designStyleRepository;
    private final SpotSpuRepository spotSpuRepository;
    private final DigitalPrintingStyleRepository digitalPrintingStyleRepository;
    private final DesignStyleService designStyleService;
    private final PrototypeRepository prototypeRepository;
    private final SkcService skcService;
    private final SpotSkcService spotSkcService;
    private final DigitalPrintingStyleService printingStyleService;
    private final SpotSkcRepository spotSkcRepository;

    /**
     * 分页数量最大
     */
    private static final int PAGE_SIZE_LIMIT_MAX = 1000;

    @Override
    public PageRespVo<SpuInnerQueryVo> pageInner(SpuInnerQuery query) {
        if (query.getPageSize() > PAGE_SIZE_LIMIT_MAX){
            query.setPageSize(PAGE_SIZE_LIMIT_MAX);
        };

        IPage<SpuInnerQueryVo> page =  designStyleRepository.spuPageInner(query);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), Collections.emptyList());
        }

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }


    @Override
    public DesignStyleInnerVo getSpuInfo(String spuCode) {
        if (StrUtil.isBlank(spuCode)) {
            return null;
        }

        DesignStyleVo styleVo = designStyleService.getLatestVersionByStyleCode(spuCode);
        if (Objects.isNull(styleVo)) {
            return null;
        }
        DesignStyleInnerVo styleInnerVo = new DesignStyleInnerVo();
        BeanUtils.copyProperties(styleVo, styleInnerVo);

        return styleInnerVo;
    }

    private DesignStyleInnerVo entity2Vo(DesignStyle entity) {
        DesignStyleInnerVo vo = new DesignStyleInnerVo();
        if (Objects.isNull(entity)) {
            return vo;
        }
        BeanUtils.copyProperties(entity, vo);
        this.resetValues(entity, vo);
        return vo;
    }

    private void resetValues(DesignStyle entity, DesignStyleInnerVo vo) {
        //季节
        String styleSeason = entity.getStyleSeason();
        if (StringUtils.isNotBlank(styleSeason)) {
            vo.setStyleSeasonList(JSON.parseArray(styleSeason, OpsObject.class));
        }
    }


    @Override
    public List<DesignStyleInnerVo> batchSpuInfo(SpuBatchReq req) {
        List<String> styleCodeList = req.getStyleCodeList();
        List<DesignStyle> designStyles = designStyleRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(designStyles)) {
            return Collections.emptyList();
        }
        return designStyles.stream()
                .map(this::entity2Vo)
                .collect(Collectors.toList());
    }

    @Override
    public List<SpuCancelResp> isCancelSpu(List<String> spuCodeList) {
        List<SpuCancelResp> respList = new ArrayList<>();
        for (String spuCode : spuCodeList) {
            SpuCancelResp resp = new SpuCancelResp();
            List<Prototype> prototypeList = prototypeRepository.listByStyleCode(spuCode);
            List<SpotSkc> spotSkcs = spotSkcRepository.listByStyleCode(spuCode);

            if (CollUtil.isEmpty(prototypeList) && CollUtil.isEmpty(spotSkcs)) {
                continue;
            }
            boolean hasActivePrototype  = prototypeList.stream().anyMatch(skc -> Objects.equals(skc.getIsCanceled(),
                    Boolean.FALSE));

            boolean hasActiveSkc  = spotSkcs.stream().anyMatch(skc -> Objects.equals(skc.getIsCanceled(), Bool.NO.getCode()));

            resp.setStyleCode(spuCode);
            resp.setStatue(hasActivePrototype || hasActiveSkc ?Boolean.FALSE :Boolean.TRUE);

            respList.add(resp);
        }
        return respList;
    }

    @Override
    public List<PopCreateProductVo> queryStyleInfo4Pop(PopCreateInfoReq req) {
        List<String> styleCodeList = req.getStyleCodeList();

        List<PopCreateProductVo> productVoList = new ArrayList<>(styleCodeList.size());
        //设计款
        List<PopCreateProductVo> designVoList = skcService.queryStyleInfo4Pop(styleCodeList);
        if (CollUtil.isNotEmpty(designVoList)) {
            productVoList.addAll(designVoList);
        }

        //现货款
        List<PopCreateProductVo> spotVoList = spotSkcService.queryStyleInfo4Pop(styleCodeList);
        if (CollUtil.isNotEmpty(spotVoList)) {
            productVoList.addAll(spotVoList);
        }

        //数码印花款
        List<PopCreateProductVo> printingVoList = printingStyleService.queryStyleInfo4Pop(styleCodeList);
        if (CollUtil.isNotEmpty(printingVoList)) {
            productVoList.addAll(printingVoList);
        }

        return productVoList;
    }

    @Override
    public List<StyleTypeInnerVo> queryStyleType(List<String> spuCodeList) {
        if (CollUtil.isEmpty(spuCodeList)) {
            return Collections.emptyList();
        }
        //spuCodeList数量不能大于1000
        if (CollUtil.isNotEmpty(spuCodeList) && spuCodeList.size() > PAGE_SIZE_LIMIT_MAX) {
            throw new SdpDesignException("数量不能大于1000");
        }

        List<StyleTypeInnerVo> styleTypeList = new ArrayList<>(spuCodeList.size());
        //设计款
        List<DesignStyle> designStyleList = designStyleRepository.listByStyleCodes(spuCodeList);
        if (CollUtil.isNotEmpty(designStyleList)) {
            designStyleList.forEach(spu ->
                    styleTypeList.add(StyleTypeInnerVo.builder()
                            .styleCode(spu.getStyleCode())
                            .styleType(SdpStyleTypeEnum.DESIGN.getCode())
                            .build()));
        }


        //现货款
        List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(spuCodeList);
        if (CollUtil.isNotEmpty(spotSpuList)) {
            spotSpuList.forEach(spu ->
                    styleTypeList.add(StyleTypeInnerVo.builder()
                            .styleCode(spu.getStyleCode())
                            .styleType(SdpStyleTypeEnum.SPOT.getCode())
                            .build()));
        }

        //数码印花款
        List<DigitalPrintingStyle> printingStyleList = digitalPrintingStyleRepository.listByStyleCodes(spuCodeList);
        if (CollUtil.isNotEmpty(printingStyleList)) {
            printingStyleList.forEach(spu ->
                    styleTypeList.add(StyleTypeInnerVo.builder()
                            .styleCode(spu.getStyleCode())
                            .styleType(SdpStyleTypeEnum.DIGITAL_PRINTING.getCode())
                            .build()));
        }

        return styleTypeList;
    }


}