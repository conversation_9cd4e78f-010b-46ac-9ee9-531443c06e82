package tech.tiangong.sdp.design.listener.handler;

import cn.yibuyun.framework.bean.user.UserContentHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.design.entity.DesignDemandAIBoxTask;
import tech.tiangong.sdp.design.entity.DesignDemandAIBoxTaskResult;
import tech.tiangong.sdp.design.entity.DesignDemandDetail;
import tech.tiangong.sdp.design.enums.TaskStatus;
import tech.tiangong.sdp.design.listener.AIBoxTaskHandler;
import tech.tiangong.sdp.design.listener.entity.Tag;
import tech.tiangong.sdp.design.listener.entity.AIBoxTaskNotification;
import tech.tiangong.sdp.design.repository.DesignDemandAIBoxTaskRepository;
import tech.tiangong.sdp.design.repository.DesignDemandAIBoxTaskResultRepository;
import tech.tiangong.sdp.design.repository.DesignDemandDetailRepository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AI时尚选择结果处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AIFashionSelectionHandler implements AIBoxTaskHandler {

    private final DesignDemandAIBoxTaskResultRepository taskResultRepository;
    private final DesignDemandDetailRepository detailRepository;
    private final DesignDemandAIBoxTaskRepository taskRepository;

    @Override
    public Tag getSupportedTag() {
        return Tag.AI_FASHION_SELECTION_RESULT;
    }

    @Override
    public void handleTaskCreation(AIBoxTaskNotification taskNotification) {

            log.info("创建AI时尚选择任务: taskId={}", taskNotification.aiBoxTaskId());
            
            DesignDemandDetail originDetail = detailRepository.getById(taskNotification.taskSourceId());
            if (originDetail == null) {
                log.error("未找到设计需求详情: {}", taskNotification.taskSourceId());
                return;
            }
            
            DesignDemandAIBoxTask task = buildAIBoxTask(taskNotification, originDetail);
            taskRepository.save(task);
            
            log.info("AI时尚选择任务创建成功: taskId={}", taskNotification.aiBoxTaskId());


    }

    @Override
    public void handleTaskUpdate(AIBoxTaskNotification taskNotification) {
        log.info("更新AI时尚选择任务: taskId={}, status={}",
                taskNotification.aiBoxTaskId(), taskNotification.status());

        // 更新任务状态
        DesignDemandAIBoxTask task = taskRepository
                .lambdaQuery()
                .eq(DesignDemandAIBoxTask::getTaskId, taskNotification.aiBoxTaskId())
                .one();

        if (task == null) {
            log.error("未找到AI Box任务: {}", taskNotification.aiBoxTaskId());
            return;
        }

        task.setStatus(taskNotification.status());
        taskRepository.updateById(task);

        // 处理任务结果
        if (taskNotification.status() == TaskStatus.COMPLETED) {
            saveTaskResults(taskNotification);
        }

        log.info("AI时尚选择任务更新成功: taskId={}", taskNotification.aiBoxTaskId());
    }

    private DesignDemandAIBoxTask buildAIBoxTask(
            AIBoxTaskNotification taskNotification,
            DesignDemandDetail originDetail) {
        return DesignDemandAIBoxTask.builder()
                .designDemandDetailId(Long.valueOf(taskNotification.taskSourceId()))
                .designDemandId(originDetail.getDesignDemandId())
                .taskId(taskNotification.aiBoxTaskId())
                .status(taskNotification.status())
                .tenantId(UserContentHolder.get().getTenantId())
                .build();
    }

    private void saveTaskResults(AIBoxTaskNotification taskNotification) {
        if (taskNotification.results() == null || taskNotification.results().isEmpty()) {
            return;
        }
        
        DesignDemandDetail originDetail = detailRepository.getById(taskNotification.taskSourceId());
        List<DesignDemandAIBoxTaskResult> results = taskNotification.results().stream()
                .map(result -> DesignDemandAIBoxTaskResult.builder()
                        .taskId(taskNotification.aiBoxTaskId())
                        .image(result.url())
                        .asPassed(false)
                        .designDemandDetailId(originDetail.getDesignDemandDetailId())
                        .designDemandId(originDetail.getDesignDemandId())
                        .ability(taskNotification.ability())
                        .tenantId(UserContentHolder.get().getTenantId())
                        .build())
                .collect(Collectors.toList());
        
        taskResultRepository.saveBatch(results);
    }
}