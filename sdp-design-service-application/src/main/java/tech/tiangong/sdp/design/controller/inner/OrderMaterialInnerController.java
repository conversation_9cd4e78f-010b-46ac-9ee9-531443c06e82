package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.OrderMaterialFollowService;
import tech.tiangong.sdp.design.vo.req.ordermaterial.OrderMaterialInnerReq;
import tech.tiangong.sdp.design.vo.req.ordermaterial.OrderMaterialStatusInnerReq;
import tech.tiangong.sdp.design.vo.resp.material.OrderMaterialInfoVo;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/order/material")
public class OrderMaterialInnerController extends BaseController {

    private final OrderMaterialFollowService orderMaterialFollowService;

    /**
     * 根据设计款号和加工单号查询齐套单状态
     *
     * @param req
     * @return boolean
     */
    @PostMapping("/order-material-status")
    public DataResponse<Boolean> getOrderMaterialStatus(@RequestBody OrderMaterialStatusInnerReq req){
        return DataResponse.ok(orderMaterialFollowService.getOrderMaterialStatus(req));
    }

    /**
     * 根据设计款号查询齐套单信息
     *
     *  一个skc会对应多个齐套单
     *
     * @param req 入参
     * @return List<OrderMaterialInfoVo>
     */
    @PostMapping("/list")
    public DataResponse<List<OrderMaterialInfoVo>> listByDesignCode(@RequestBody OrderMaterialInnerReq req){
        return DataResponse.ok(orderMaterialFollowService.listByDesignCode(req));
    }

    /**
     * 根据设计款号从致景查询齐套单信息(接口测试用)
     *
     * @param designCode 设计款号
     * @return 齐套单json信息
     */
    @GetMapping("/zj-order/{designCode}")
    DataResponse<String> queryZjOrder(@PathVariable(value = "designCode") String designCode) {
        return DataResponse.ok(orderMaterialFollowService.queryZjOrder(designCode));
    }


}
