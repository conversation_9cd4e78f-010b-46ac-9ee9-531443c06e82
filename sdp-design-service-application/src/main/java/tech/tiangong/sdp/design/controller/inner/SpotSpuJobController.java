package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.SpotSpuCommunicationService;
import tech.tiangong.sdp.design.service.SpotSpuDetailService;
import tech.tiangong.sdp.design.vo.req.spot.SpotAiAlibabaDistributionRetryReq;

/**
 * @Author: caicijie
 * @description:
 * @Date: 2025/6/27 18:09
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/jobs/spot-spu")
public class SpotSpuJobController {
    private final SpotSpuDetailService spotSpuDetailService;
    private final SpotSpuCommunicationService spotSpuCommunicationService;

    /**
     * 图包导入定时任务执行
     */
    @PostMapping("/doAlibabaDistributionJob")
    public DataResponse<Void> doAlibabaDistributionJob(@RequestParam(name = "maxId",
            required = false)Long maxId){
        setSystemUser();
        spotSpuDetailService.doAlibabaDistributionJob(maxId);
        return DataResponse.ok();
    }

    /**
     * 重新解析图包结果
     * @return
     */
    @PostMapping("/retryAiAlibabaDistributionConvertResults")
    public DataResponse<Void> retryAiAlibabaDistributionConvertResults(@RequestBody SpotAiAlibabaDistributionRetryReq req){
        setSystemUser();
        spotSpuDetailService.retryAiAlibabaDistributionConvertResults(req);
        return DataResponse.ok();
    }
    /**
     * 重新发起图包算法任务
     * @return
     */
    @PostMapping("/aiAlibabaDistributionRetryJob")
    public DataResponse<Void> aiAlibabaDistributionRetryJob(@RequestBody SpotAiAlibabaDistributionRetryReq req){
        setSystemUser();
        spotSpuDetailService.aiAlibabaDistributionRetryJob(req);
        return DataResponse.ok();
    }
    /**
     * 图包导入定时任务执行
     * @return
     */
    @PostMapping("/aiAlibabaDistributionJob")
    public DataResponse<Void> aiAlibabaDistributionJob(
            @RequestParam(name = "day",required = false)Integer day,
            @RequestParam(name = "aiAlibabaDistributionSyncStatus",required = false)Integer aiAlibabaDistributionSyncStatus,
            @RequestParam(name = "limit",required = false)Integer limit
    ){
        setSystemUser();
        spotSpuDetailService.aiAlibabaDistributionJob(day,aiAlibabaDistributionSyncStatus,limit);
        return DataResponse.ok();
    }

    /**
     * 推送货通商品到POP
     *
     * @return 处理结果
     */
    @PostMapping("/push-communication-to-pop")
    public DataResponse<String> pushCommunicationProductsToPop() {
        log.info("货通商品推送POP任务开始");
        setSystemUser();
        spotSpuCommunicationService.processCommunicationProductsToPop();
        return DataResponse.ok("货通商品推送POP任务完成");
    }

    private void setSystemUser() {
        UserContentHolder.set(new UserContent()
                .setCurrentUserId(0L)
                .setCurrentUserCode("0")
                .setCurrentUserName("系统")
                .setTenantId(2L)
                .setSystemCode("SDP"));
    }
}
