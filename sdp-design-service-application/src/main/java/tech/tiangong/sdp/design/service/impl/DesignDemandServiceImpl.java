package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tech.tiangong.id.IdPool;
import tech.tiangong.pop.common.resp.ShopResp;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.constant.SdpDesignConstant;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.converter.SpuIdentifyConverter;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.helper.StyleLibraryHelper;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.DesignerRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.dto.DesignDemandAttachmentBo;
import tech.tiangong.sdp.design.vo.dto.visual.BackgroundDTO;
import tech.tiangong.sdp.design.vo.dto.visual.ModelFaceDTO;
import tech.tiangong.sdp.design.vo.dto.visual.TryOnModelReferenceImageDTO;
import tech.tiangong.sdp.design.vo.query.DesignDemandQuery;
import tech.tiangong.sdp.design.vo.req.bom.GoodMaterialInfoReq;
import tech.tiangong.sdp.design.vo.req.demand.*;
import tech.tiangong.sdp.design.vo.req.identify.SpuIdentifyAddReq;
import tech.tiangong.sdp.design.vo.req.log.DesignLogSdpSaveReq;
import tech.tiangong.sdp.design.vo.req.mq.demand.DemandCreateSpuMqDTO;
import tech.tiangong.sdp.design.vo.req.mq.demand.DemandNoPassMqDTO;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleCreateReq;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderMaterialVo;
import tech.tiangong.sdp.design.vo.resp.demand.*;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleCreateResp;
import tech.tiangong.sdp.material.enums.Bool;
import tech.tiangong.sdp.material.pojo.dto.DesignerDTO;
import tech.tiangong.sdp.material.pojo.web.request.designer.DesignerRemoteReq;
import tech.tiangong.sdp.utils.AsyncTask;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 灵感设计需求表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DesignDemandServiceImpl implements DesignDemandService {
    private final DesignDemandRepository designDemandRepository;
    private final DesignDemandDetailRepository designDemandDetailRepository;
    private final SpuIdentifyRepository spuIdentifyRepository;
    private final DesignDemandSuggestedMaterialRepository designDemandSuggestedMaterialRepository;
    private final DesignLogService designLogService;
    private final BomOrderService bomOrderService;
    private final DesignStyleService designStyleService;
    private final SpuIdentifyService spuIdentifyService;
    private final MqProducer mqProducer;
    private final PopProductHelper popProductHelper;
    private final StyleLibraryHelper styleLibraryHelper;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final DesignDemandAIBoxTaskRepository designDemandAIBoxTaskRepository;
    private final DesignDemandAIBoxTaskResultRepository designDemandAIBoxTaskResultRepository;

    @Override
    public PageRespVo<DesignDemandPageVo> page(DesignDemandQuery query) {
        IPage<DesignDemandPageVo> page = designDemandRepository.findPage(query);

        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.empty();
        }

        List<Long> demandIdList = StreamUtil.convertListAndDistinct(page.getRecords(), DesignDemandPageVo::getDesignDemandId);

        //详情信息
        List<DesignDemandDetail> detailList = designDemandDetailRepository.listByDesignDemandIds(demandIdList);
        Map<Long, DesignDemandDetail> detailMap = StreamUtil.list2Map(detailList, DesignDemandDetail::getDesignDemandId);

        // 市场风格
        Map<String, String> marketMap = dictValueRemoteHelper.getMarketNodeMap(1);
        Map<String, String> marketSeriesMap = dictValueRemoteHelper.getMarketNodeMap(2);

        //款式识别信息
        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizIdSource(demandIdList, SpuIdentifySourceEnum.DESIGN_DEMAND.getCode(), null);
        Map<Long, List<SpuIdentify>> spuIdentifyGroupMap = StreamUtil.groupingBy(spuIdentifyList, SpuIdentify::getBizId);

        page.getRecords().forEach(item -> {
            DesignDemandDetail demandDetail = detailMap.get(item.getDesignDemandId());
            if (Objects.nonNull(demandDetail)) {
                List<String> imageAllList = new ArrayList<>();
                if(!CollUtil.isEmpty(demandDetail.getInspirationImageList())){
                    imageAllList.addAll(demandDetail.getInspirationImageList());
                }
                //展示4k图
                List<DesignDemandCreateReq.ImageInfo> imageList = demandDetail.getInspirationImageJson();
                if(null!= imageList){
                    List<String> ultraHdUrlList = imageList.stream()
                            .map(DesignDemandCreateReq.ImageInfo::getUltraHdUrl)
                            .filter(StrUtil::isNotBlank)
                            .collect(Collectors.toList());
                    if(!CollUtil.isEmpty(ultraHdUrlList)){
                        imageAllList.addAll(ultraHdUrlList);
                    }
                }
                item.setInspirationImageList(imageAllList);
                item.setNoPassReasonName(demandDetail.getNoPassReasonName());
                item.setNoPassReasonCode(demandDetail.getNoPassReasonCode());
                item.setNoPassUserName(demandDetail.getNoPassUserName());
                item.setNoPassTime(demandDetail.getNoPassTime());
                //0904 新增
                if(StringUtils.isNotBlank(item.getMarketCode())){
                    item.setMarketName(marketMap.get(item.getMarketCode()));
                }
                if(StringUtils.isNotBlank(item.getMarketSeriesCode())){
                    item.setMarketSeriesName(marketSeriesMap.get(item.getMarketSeriesCode()));
                }
                //款式识别信息
                item.setMuseIdentifyInfo(SpuIdentifyConverter.buildIdentifyInfo(item.getDesignDemandId(), spuIdentifyGroupMap));
            }
        });

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    public DesignDemandDetailInfoVo getDetailById(Long designDemandId) {
        DesignDemand designDemand = designDemandRepository.getById(designDemandId);
        if (Objects.isNull(designDemand)) {
            return null;
        }
        DesignDemandDetailInfoVo vo = new DesignDemandDetailInfoVo();
        BeanUtils.copyProperties(designDemand, vo);


        // 市场风格
        Map<String, String> marketMap = dictValueRemoteHelper.getMarketNodeMap(1);
        Map<String, String> marketSeriesMap = dictValueRemoteHelper.getMarketNodeMap(2);

        //0904 新增
        if(StringUtils.isNotBlank(vo.getMarketCode())){
            vo.setMarketName(marketMap.get(vo.getMarketCode()));
        }
        if(StringUtils.isNotBlank(vo.getMarketSeriesCode())){
            vo.setMarketSeriesName(marketSeriesMap.get(vo.getMarketSeriesCode()));
        }

        DesignDemandDetail demandDetail = designDemandDetailRepository.getByDemandId(designDemandId);
        if (Objects.nonNull(demandDetail)) {
            DesignDemandDetailInfoVo.DemandDetailInfo demandDetailInfo = new DesignDemandDetailInfoVo.DemandDetailInfo();
            BeanUtils.copyProperties(demandDetail, demandDetailInfo);
            demandDetailInfo.setDemandImageList(StreamUtil.convertList(demandDetail.getAigcRemarkAttachment(), DesignDemandAttachmentBo::getFileUrl));
            demandDetailInfo.setModelPicList(StreamUtil.convertList(demandDetail.getModelAttachments(), DesignDemandAttachmentBo::getFileUrl));
            demandDetailInfo.setBackgroundPicList(StreamUtil.convertList(demandDetail.getBackgroundAttachments(), DesignDemandAttachmentBo::getFileUrl));
            demandDetailInfo.setPosturePicList(StreamUtil.convertList(demandDetail.getPostureAttachments(), DesignDemandAttachmentBo::getFileUrl));
            demandDetailInfo.setModelReferenceImageList(JSON.parseArray(demandDetail.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
            demandDetailInfo.setBackgroundImageList(JSON.parseArray(demandDetail.getBackgroundImage(), BackgroundDTO.class));
            demandDetailInfo.setModelFaceImageList(JSON.parseArray(demandDetail.getModelFaceImage(), ModelFaceDTO.class));
            //展示4k图
            List<DesignDemandCreateReq.ImageInfo> imageList = demandDetail.getInspirationImageJson();
            // 获取AIBox任务结果
            List<AIBoxTaskResultVo> aiBoxTaskResultImages =
                    new ArrayList<>(getAIBoxTaskResultImageVo(demandDetail));
            demandDetailInfo.setInspirationImageList(imageList);
            demandDetailInfo.setAiBoxTaskResultList(aiBoxTaskResultImages);

            vo.setDemandDetailInfo(demandDetailInfo);
        }

        //款式识别信息
        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizIdSource(Collections.singleton(designDemandId), SpuIdentifySourceEnum.DESIGN_DEMAND.getCode(), null);
        Map<Long, List<SpuIdentify>> spuIdentifyGroupMap = StreamUtil.groupingBy(spuIdentifyList, SpuIdentify::getBizId);
        vo.setMuseIdentifyInfo(SpuIdentifyConverter.buildIdentifyInfo(designDemandId, spuIdentifyGroupMap));

        return vo;
    }

    private Long getAIBoxTaskId(DesignDemandDetail demandDetail) {
        return designDemandAIBoxTaskRepository
                .lambdaQuery()
                .eq(DesignDemandAIBoxTask::getDesignDemandDetailId, demandDetail.getDesignDemandDetailId())
                .one()
                .getTaskId();
    }

    private List<AIBoxTaskResultVo> getAIBoxTaskResultImageVo(DesignDemandDetail demandDetail) {
        return designDemandAIBoxTaskResultRepository
                .lambdaQuery()
                .eq(DesignDemandAIBoxTaskResult::getDesignDemandDetailId, demandDetail.getDesignDemandDetailId())
                .list()
                .stream()
                .map(it -> AIBoxTaskResultVo
                        .builder()
                        .id(it.getId())
                        .taskId(it.getTaskId())
                        .url(it.getImage())
                        .ability(it.getAbility())
                        .passed(it.getAsPassed())
                        .build()
                )
                .collect(Collectors.toList());
    }

    @Override
    public List<SuggestedMaterialDetailVo> listSuggestMaterial(Long designDemandId) {
        DesignDemand designDemand = designDemandRepository.getById(designDemandId);
        SdpDesignException.notNull(designDemand, "灵感任务需求不存在! demandId:{}", designDemandId);

        //推荐面料
        List<DesignDemandSuggestedMaterial> suggestedMaterialList = designDemandSuggestedMaterialRepository.listByDemandId(designDemandId);
        if (CollUtil.isEmpty(suggestedMaterialList)) {
            return Collections.emptyList();
        }

        //查询中台面料信息
        List<GoodMaterialInfoReq.SpuSkuIdReq> fabricSpuSkuList = suggestedMaterialList.stream().map(item -> {
            GoodMaterialInfoReq.SpuSkuIdReq spuSkuIdReq = new GoodMaterialInfoReq.SpuSkuIdReq();
            spuSkuIdReq.setSpuId(item.getSpuId());
            spuSkuIdReq.setSkuId(item.getSkuId());
            return spuSkuIdReq;
        }).collect(Collectors.toList());
        List<BomOrderMaterialVo> fabricMaterialVoList = bomOrderService.getFabricMaterialVo(fabricSpuSkuList, null);

        //封装出参
        Map<String, BomOrderMaterialVo> materialVoMap = StreamUtil.list2Map(fabricMaterialVoList, BomOrderMaterialVo::getSpuSkuId);
        return suggestedMaterialList.stream().map(item -> {
            SuggestedMaterialDetailVo detailVo = new SuggestedMaterialDetailVo();
            BeanUtils.copyProperties(item, detailVo);
            BomOrderMaterialVo materialVo = materialVoMap.get(item.getSpuId() + StrUtil.AT + item.getSkuId());
            if (Objects.nonNull(materialVo)) {
                SuggestedMaterialDetailVo.MaterialInfo materialInfo = new SuggestedMaterialDetailVo.MaterialInfo();
                BeanUtils.copyProperties(materialVo, materialInfo);
                detailVo.setMaterialInfo(materialInfo);
            }
            return detailVo;
        }).collect(Collectors.toList());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DesignDemandCreateVo add(DesignDemandCreateReq req) {
        log.info("创建灵感任务需求单请求参数:{}", JSONUtil.toJsonStr(req));
        //校验: 灵感选款信息不能重复提交
        if (Objects.nonNull(req.getInspirationStyleId())) {
            DesignDemand designDemand = designDemandRepository.getByInspirationStyleId(req.getInspirationStyleId());
            SdpDesignException.isNull(designDemand, "灵感设计需求已存在! 灵感选款Id:{}", req.getInspirationStyleId());
        }else {
            DesignDemand designDemand = designDemandRepository.getByBizId(req.getSourceBizId());
            SdpDesignException.isNull(designDemand, "灵感设计需求已存在! 来源业务Id:{}", req.getSourceBizId());
        }

        //创建灵感任务需求
        Long designDemandId = IdPool.getId();
        DesignDemand newDemand = new DesignDemand();
        BeanUtils.copyProperties(req, newDemand);

        //填充灵感源过来的款式信息
        if (req.getStyleLabel() != null) {
            buildDemandByStyle(newDemand, req.getStyleLabel());
        }

        newDemand.setSupplyModeCode(req.getSupplyModeCode());
        newDemand.setSupplyModeName(req.getSupplyModeName());
        newDemand.setDesignDemandId(designDemandId);
        newDemand.setDesignDemandStatus(DesignDemandStatusEnum.WAIT_ALLOCATE.getCode());
        newDemand.setRunNo(req.getRunNo());
        newDemand.setRunCreatorName(req.getRunCreatorName());
        designDemandRepository.save(newDemand);

        List<DesignDemandCreateReq.ImageInfo> imageInfoList = req.getInspirationImageList();
        List<String> imageList = imageInfoList.stream().map(DesignDemandCreateReq.ImageInfo::getImageUrl).collect(Collectors.toList());

        //需求详情
        DesignDemandDetail demandDetail = this.saveDesignDemandDetail(req, designDemandId, imageList, imageInfoList);

        //推荐面料
        List<DesignDemandCreateReq.SuggestedMaterialInfo> suggestedMaterialList = req.getSuggestedMaterialList();
        if (CollUtil.isNotEmpty(suggestedMaterialList)) {
            List<DesignDemandSuggestedMaterial> suggestedMaterialAddList = suggestedMaterialList.stream().map(item -> {
                DesignDemandSuggestedMaterial suggestedMaterial = new DesignDemandSuggestedMaterial();
                BeanUtils.copyProperties(item, suggestedMaterial);
                suggestedMaterial.setSuggestedMaterialId(IdPool.getId());
                suggestedMaterial.setDesignDemandId(designDemandId);
                return suggestedMaterial;
            }).collect(Collectors.toList());
            designDemandSuggestedMaterialRepository.saveBatch(suggestedMaterialAddList);
        }

        //添加日志
        this.addLog(designDemandId, DesignLogContentEnum.DESIGN_DEMAND_CREATE.getDesc());

        //发起款式识别
        this.addSpuIdentify(req, designDemandId);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(AsyncTask.wrapper(()->{
                    //请求向量创建
                    styleLibraryHelper.designDemandStyleLibrarySaveOrUpdate(newDemand,demandDetail,1);
                }));
            }
        });

        return DesignDemandCreateVo.builder()
                .designDemandId(designDemandId)
                .inspirationStyleId(req.getInspirationStyleId())
                .build();

    }

    private void addSpuIdentify(DesignDemandCreateReq req, Long designDemandId) {
        //获取识别图
        String identifyUrl = this.getIdentifyUrl(req);
        SdpDesignException.notBlank(identifyUrl, "识别图为空!");

        SpuIdentifyAddReq identifyAddInfo = SpuIdentifyAddReq.builder()
                .sourceType(SpuIdentifySourceEnum.DESIGN_DEMAND.getCode())
                .bizId(designDemandId).bizCode(String.valueOf(req.getSourceBizId()))
                .identifyUrl(identifyUrl)
                .build();

        spuIdentifyService.batchAddAsync(Collections.singletonList(identifyAddInfo));
    }

    private String getIdentifyUrl(DesignDemandCreateReq req) {
        List<DesignDemandCreateReq.ImageInfo> inspirationImageList = req.getInspirationImageList();
        SdpDesignException.notEmpty(inspirationImageList, "灵感图片不能为空!");

        if (Objects.equals(SupplyModeEnum.AIGC.getCode(), req.getSupplyModeCode())) {
            // 选款的(AIGC): 取原图主图
            return inspirationImageList.stream()
                    .filter(item -> Objects.equals(Bool.YES.getCode(), item.getMainImage()))
                    .map(DesignDemandCreateReq.ImageInfo::getImageUrl)
                    .findFirst()
                    .orElse(null);
        } else {
            // 灵感(非AIGC): 取第一张
            return inspirationImageList.getFirst().getImageUrl();
        }
    }

    private DesignDemandDetail saveDesignDemandDetail(DesignDemandCreateReq req, Long designDemandId, List<String> imageList, List<DesignDemandCreateReq.ImageInfo> imageInfoList) {
        DesignDemandDetail demandDetail = DesignDemandDetail.builder()
                .designDemandDetailId(IdPool.getId())
                .designDemandId(designDemandId)
                .originalImage(req.getOriginalImage())
                .inspirationImageList(imageList)
                .inspirationImageJson(imageInfoList)
                .aigcRemark(req.getAigcRemark())
                .aigcRemarkAttachment(req.getAttachments())
                .demandType(req.getDemandType())
                .modelAttachments(req.getModelAttachments())
                .backgroundAttachments(req.getBackgroundAttachments())
                .postureAttachments(req.getPostureAttachments())
                .modelReferenceImage(CollectionUtil.isEmpty(req.getModelReferenceImageList()) ? null : JSON.toJSONString(req.getModelReferenceImageList()))
                .backgroundImage(CollectionUtil.isEmpty(req.getBackgroundImageList()) ? null : JSON.toJSONString(req.getBackgroundImageList()))
                .modelFaceImage(CollectionUtil.isEmpty(req.getModelFaceImageList()) ? null : JSON.toJSONString(req.getModelFaceImageList()))
                .build();
        designDemandDetailRepository.save(demandDetail);
        return demandDetail;
    }

    private void buildDemandByStyle(DesignDemand newDemand, DesignDemandCreateReq.StyleLabel styleLabel) {
        newDemand.setStoreId(styleLabel.getSuggestedShopId());
        newDemand.setStoreName(styleLabel.getSuggestedShopName());
        newDemand.setWaveBandCode(styleLabel.getWaveBatchCode());
        newDemand.setCategory(styleLabel.getCategoryCode());
        newDemand.setCategoryName((styleLabel.getCategoryName()));
        newDemand.setExpectedCostPrice(styleLabel.getExpectedCostPrice().toString());
        newDemand.setSuggestedStyleCode(styleLabel.getSuggestedStyleCode());
        newDemand.setSuggestedStyle(styleLabel.getSuggestedStyleName());
        newDemand.setSuggestedPrintingCode(styleLabel.getSuggestedPrintingCode());
        newDemand.setSuggestedPrintingName(styleLabel.getSuggestedPrintingName());
        newDemand.setElementCode(styleLabel.getElementCode());
        newDemand.setElementName(styleLabel.getElementName());
        newDemand.setPalletTypeCode(styleLabel.getCargoTrayCode());
        newDemand.setPalletTypeName(styleLabel.getCargoTrayName());
        newDemand.setCountrySiteCode(styleLabel.getSuggestedCountrySiteCode());
        newDemand.setSceneCode(styleLabel.getSceneCode());
        newDemand.setSceneName(styleLabel.getSceneName());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allocate(DesignDemandAllocateReq req) {
        log.info("=== 灵感任务分配-req:{}===", JSON.toJSONString(req));
        List<DesignDemand> designDemands = designDemandRepository.listByIds(req.getDesignDemandIdList());
        SdpDesignException.notEmpty(designDemands, "灵感任务需求不存在! demandIds:{}", JSON.toJSONString(req.getDesignDemandIdList()));

        //状态校验
        Boolean copyStyle = this.allocateCheck(req, designDemands);

        UserContent userContent = UserContentHolder.get();

        //分配设计师更新
        this.allocateUpdateDesigner(req, designDemands, userContent);

        //仿款数据更新
        if (copyStyle) {
            DesignDemandAllocateReq.CopyStyleInfo copyStyleInfo = req.getCopyStyleInfo();
            if (!req.getDesignDemandIdList().isEmpty()) {
                // 2025-06-04 仿款上线后补充优化
                // https://alidocs.dingtalk.com/i/nodes/14dA3GK8gj9xjglkc9Xd561RJ9ekBD76?cid=406956615:688789523&corpId=ding8f81f5c3c8bf10f0f2c783f7214b6d69&doc_type=wiki_doc&iframeQuery=utm_medium=portal_main_colum_create&utm_source=portal&utm_medium=im_card&utm_scene=person_space&utm_source=im
                // 灵感任务分配批量提交时，字段非必填
                // 所选任务的供给方式均为【仿款】时，以下字段取消必填，改为使用从【灵感源】提交的取值
                designDemandRepository.lambdaUpdate()
                        .set(DesignDemand::getIsDeleted, 0)
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getCountrySiteCode()), DesignDemand::getCountrySiteCode, copyStyleInfo.getCountrySiteCode())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getCountrySiteName()), DesignDemand::getCountrySiteName, copyStyleInfo.getCountrySiteName())
                        .set(copyStyleInfo.getStoreId() != null, DesignDemand::getStoreId, copyStyleInfo.getStoreId())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getStoreName()), DesignDemand::getStoreName, copyStyleInfo.getStoreName())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getSuggestedStyle()), DesignDemand::getSuggestedStyle, copyStyleInfo.getSuggestedStyle())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getSuggestedStyleCode()), DesignDemand::getSuggestedStyleCode, copyStyleInfo.getSuggestedStyleCode())
                        .set(copyStyleInfo.getPlanningType()!=null, DesignDemand::getPlanningType, copyStyleInfo.getPlanningType())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getMarketCode()), DesignDemand::getMarketCode, copyStyleInfo.getMarketCode())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getMarketSeriesCode()), DesignDemand::getMarketSeriesCode, copyStyleInfo.getMarketSeriesCode())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getSceneName()), DesignDemand::getSceneName, copyStyleInfo.getSceneName())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getSceneCode()), DesignDemand::getSceneCode, copyStyleInfo.getSceneCode())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getWaveBandCode()), DesignDemand::getWaveBandCode, copyStyleInfo.getWaveBandCode())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getWaveBandName()), DesignDemand::getWaveBandName, copyStyleInfo.getWaveBandName())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getPalletTypeName()), DesignDemand::getPalletTypeName, copyStyleInfo.getPalletTypeName())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getPalletTypeCode()), DesignDemand::getPalletTypeCode, copyStyleInfo.getPalletTypeCode())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getCategoryCode()), DesignDemand::getCategory, copyStyleInfo.getCategoryCode())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getCategoryName()), DesignDemand::getCategoryName, copyStyleInfo.getCategoryName())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getSuggestedPrintingCode()), DesignDemand::getSuggestedPrintingCode, copyStyleInfo.getSuggestedPrintingCode())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getSuggestedPrintingName()), DesignDemand::getSuggestedPrintingName, copyStyleInfo.getSuggestedPrintingName())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getElementCode()), DesignDemand::getElementCode, copyStyleInfo.getElementCode())
                        .set(StringUtils.isNotEmpty(copyStyleInfo.getElementName()), DesignDemand::getElementName, copyStyleInfo.getElementName())
                        .in(DesignDemand::getDesignDemandId, req.getDesignDemandIdList())
                        .update();
            }
        }

        //日志
        String logContent = DesignLogContentEnum.DESIGN_DEMAND_FIRST_ALLOCATE.getDesc();
        for (Long designDemandId : req.getDesignDemandIdList()) {
            this.addLog(designDemandId, logContent);
        }

        log.info("=== 灵感任务分配成功-logContent:{}===", logContent);
    }

    private void allocateUpdateDesigner(DesignDemandAllocateReq req, List<DesignDemand> designDemands, UserContent userContent) {
        //仿款设计师设置  设计师不指定时,仿款将默认分配给灵感任务的提交人。
        if (Objects.isNull(req.getDesignerId())) {
            Map<Long, DesignDemand> designDemandMap = StreamUtil.list2Map(designDemands, DesignDemand::getDesignDemandId);
            //仿款时, 若设计师为空, 灵感任务提交人不能为空,
            if (Objects.isNull(req.getDesignerId())) {
                designDemands.forEach(item -> {
                    SdpDesignException.notNull(item.getSubmitUserId(), "灵感任务提交人为空! ");
                });
            }
            List<Long> submitUserIdList = StreamUtil.convertListAndDistinct(designDemands, DesignDemand::getSubmitUserId);
            // i. 存在灵感任务提交人为非设计师，提示：存在非设计师提交的灵感任务，无法自动分配，请指定设计师。
            //  ii. 不存在灵感任务提交人为非设计师，将任务自动分配给提交人；
            List<String> designerIdList = submitUserIdList.stream().map(String::valueOf).toList();
            List<DesignerDTO> designerDTOList = SpringUtil.getBean(DesignerRemoteHelper.class).listByDesignerId(designerIdList);
            Map<String, DesignerDTO> designerDTOMap = StreamUtil.list2Map(designerDTOList, DesignerDTO::getDesignerId);
            //更新设计师与状态, 分配人
            req.getDesignDemandIdList().forEach(item -> {
                DesignDemand updateDemand = new DesignDemand();
                updateDemand.setDesignDemandId(item);
                updateDemand.setDesignDemandStatus(DesignDemandStatusEnum.WAIT_HANDLE.getCode());
                updateDemand.setAllocateUserId(userContent.getCurrentUserId());
                updateDemand.setAllocateUserName(userContent.getCurrentUserName());
                DesignDemand designDemand = designDemandMap.get(item);
                SdpDesignException.notNull(designDemand, "灵感设计信息不存在! designDemandId:{}", item);
                DesignerDTO designerDTO = designerDTOMap.get(String.valueOf(designDemand.getSubmitUserId()));
                SdpDesignException.notNull(designerDTO, "存在非设计师提交的灵感任务，无法自动分配，请指定设计师!  designDemandId:{}", item);
                updateDemand.setDesignerId(Long.valueOf(designerDTO.getDesignerId()));
                updateDemand.setDesignerCode(designerDTO.getDesignerCode());
                updateDemand.setDesignerName(designerDTO.getDesignerName());
                updateDemand.setDesignerGroup(designerDTO.getDesignerGroupName());
                updateDemand.setDesignerGroupCode(designerDTO.getDesignerGroupCode());
                designDemandRepository.updateById(updateDemand);
            });
        }

        //更新指定设计师
        if (Objects.nonNull(req.getDesignerId())) {
            DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
            designerRemoteReq.setDesignerId(String.valueOf(req.getDesignerId()));
            DesignerDTO designerDTO = SpringUtil.getBean(DesignerRemoteHelper.class).getByDesignerId(designerRemoteReq);
            Assert.notNull(designerDTO, "不存在此设计师");

            //更新设计师与状态, 分配人
            req.getDesignDemandIdList().forEach(item -> {
                DesignDemand updateDemand = new DesignDemand();
                updateDemand.setDesignDemandId(item);
                updateDemand.setDesignDemandStatus(DesignDemandStatusEnum.WAIT_HANDLE.getCode());
                updateDemand.setAllocateUserId(userContent.getCurrentUserId());
                updateDemand.setAllocateUserName(userContent.getCurrentUserName());
                updateDemand.setDesignerId(req.getDesignerId());
                updateDemand.setDesignerCode(designerDTO.getDesignerCode());
                updateDemand.setDesignerName(designerDTO.getDesignerName());
                updateDemand.setDesignerGroup(designerDTO.getDesignerGroupName());
                updateDemand.setDesignerGroupCode(designerDTO.getDesignerGroupCode());
                designDemandRepository.updateById(updateDemand);
            });
        }
    }

    private Boolean allocateCheck(DesignDemandAllocateReq req, List<DesignDemand> designDemands) {
        //状态校验
        Map<Long, DesignDemand> designDemandMap = StreamUtil.list2Map(designDemands, DesignDemand::getDesignDemandId);
        req.getDesignDemandIdList().forEach(designDemandId -> {
            DesignDemand designDemand = designDemandMap.get(designDemandId);
            SdpDesignException.notNull(designDemand, "灵感任务需求不存在! demandId:{}", designDemandId);
            //待分配时才可以进行 任务分配操作
            SdpDesignException.isTrue(Objects.equals(designDemand.getDesignDemandStatus(), DesignDemandStatusEnum.WAIT_ALLOCATE.getCode()),
                    "待分配的任务才可以进行任务分配! 灵感选款ID:{}", designDemand.getInspirationStyleId());
        });
        //仿款校验  v1.020
        List<DesignDemand> copyStyleDemandList = designDemands.stream()
                .filter(item -> Objects.equals(item.getSupplyModeName(), SdpDesignConstant.COPY_STYLE_NAME))
                .toList();
        Boolean copyStyle = Boolean.FALSE;
        if (CollUtil.isNotEmpty(copyStyleDemandList)) {
            //c.当选中的数据同时包含仿款+其他供给方式时,提示:请勿同时勾选仿款和其他供给方式的数据。
            SdpDesignException.isTrue(copyStyleDemandList.size() == designDemands.size(), "请勿同时勾选仿款和其他供给方式的数据!");

            copyStyle = Boolean.TRUE;

            //仿款时, 若设计师为空, 灵感任务提交人不能为空,
            if (Objects.isNull(req.getDesignerId())) {
                designDemands.forEach(item -> {
                    SdpDesignException.notNull(item.getSubmitUserId(), "灵感任务提交人为空! ");
                });
            }
        }else {
            //非仿款, 设计师必填
            SdpDesignException.notNull(req.getDesignerId(), "非仿款数据, 设计师必填!");
        }

        return copyStyle;
    }

    @Override
    public void reAllocateUpdate(DesignDemandReAllocateReq req) {
        log.info("=== 灵感任务分配变更-req:{}===", JSON.toJSONString(req));
        List<DesignDemand> designDemands = designDemandRepository.listByIds(req.getDesignDemandIdList());
        SdpDesignException.notEmpty(designDemands, "灵感任务需求不存在! demandIds:{}", JSON.toJSONString(req.getDesignDemandIdList()));

        //分配校验
        Map<Long, DesignDemand> designDemandMap = StreamUtil.list2Map(designDemands, DesignDemand::getDesignDemandId);
        req.getDesignDemandIdList().forEach(designDemandId -> {
            DesignDemand designDemand = designDemandMap.get(designDemandId);
            SdpDesignException.notNull(designDemand, "灵感任务需求不存在! demandId:{}", designDemandId);
            //待处理时才可以进行 分配变更操作
            SdpDesignException.isTrue(Objects.equals(designDemand.getDesignDemandStatus(), DesignDemandStatusEnum.WAIT_HANDLE.getCode()),
                    "待处理的任务才可以进行分配变更! 灵感选款ID:{}", designDemand.getInspirationStyleId());
        });
        //设计师
        DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
        designerRemoteReq.setDesignerId(String.valueOf(req.getDesignerId()));
        DesignerDTO designerDTO = SpringUtil.getBean(DesignerRemoteHelper.class).getByDesignerId(designerRemoteReq);
        Assert.notNull(designerDTO, "不存在此设计师");

        UserContent userContent = UserContentHolder.get();

        //更新设计师与状态, 分配人
        req.getDesignDemandIdList().forEach(item -> {
            DesignDemand updateDemand = new DesignDemand();
            updateDemand.setDesignDemandId(item);
            updateDemand.setDesignDemandStatus(DesignDemandStatusEnum.WAIT_HANDLE.getCode());
            updateDemand.setAllocateUserId(userContent.getCurrentUserId());
            updateDemand.setAllocateUserName(userContent.getCurrentUserName());
            updateDemand.setDesignerId(req.getDesignerId());
            updateDemand.setDesignerCode(designerDTO.getDesignerCode());
            updateDemand.setDesignerName(designerDTO.getDesignerName());
            updateDemand.setDesignerGroup(designerDTO.getDesignerGroupName());
            updateDemand.setDesignerGroupCode(designerDTO.getDesignerGroupCode());
            designDemandRepository.updateById(updateDemand);
        });

        //日志
        String logContent = DesignLogContentEnum.DESIGN_DEMAND_RE_ALLOCATE.getDesc();

        for (Long designDemandId : req.getDesignDemandIdList()) {
            this.addLog(designDemandId, logContent);
        }

        log.info("=== 灵感任务分配变更成功-logContent:{}===", logContent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void noPass(DesignDemandNoPassReq req) {
        log.info("=== 灵感淘汰-req:{}===", JSON.toJSONString(req));
        List<Long> designDemandIdList = req.getDesignDemandIdList();
        List<DesignDemand> designDemandList = designDemandRepository.listByIds(designDemandIdList);
        Map<Long, DesignDemand> designDemandMap = StreamUtil.list2Map(designDemandList, DesignDemand::getDesignDemandId);
        List<DesignDemandDetail> designDemandDetailList = designDemandDetailRepository.listByDesignDemandIds(designDemandIdList);
        Map<Long, DesignDemandDetail> designDemandDetailMap = StreamUtil.list2Map(designDemandDetailList, DesignDemandDetail::getDesignDemandId);

        //校验
        designDemandIdList.forEach(designDemandId -> {
            DesignDemand designDemand = designDemandMap.get(designDemandId);
            SdpDesignException.notNull(designDemand, "灵感任务需求不存在! demandId:{}", designDemandId);
            DesignDemandDetail demandDetail = designDemandDetailMap.get(designDemandId);
            SdpDesignException.notNull(demandDetail, "灵感任务需求详情不存在! demandId:{}", designDemandId);
            //待分配&待处理 可执行
            SdpDesignException.isTrue(Objects.equals(designDemand.getDesignDemandStatus(), DesignDemandStatusEnum.WAIT_ALLOCATE.getCode()) || Objects.equals(designDemand.getDesignDemandStatus(), DesignDemandStatusEnum.WAIT_HANDLE.getCode()),
                    "待分配或待处理的任务才可以淘汰!");
        });

        UserContent userContent = UserContentHolder.get();

        //淘汰更新
        List<DesignDemand> noPassDemandUpdateList = new ArrayList<>();
        List<DesignDemandDetail> noPassDetailUpdateList = new ArrayList<>();
        LocalDateTime noPassTime = LocalDateTime.now();
        designDemandIdList.forEach(item -> {
            DesignDemand noPassDemandUpdate = DesignDemand.builder()
                    .designDemandId(item)
                    .designDemandStatus(DesignDemandStatusEnum.NO_PASS.getCode())
                    .build();
            noPassDemandUpdateList.add(noPassDemandUpdate);

            DesignDemandDetail demandDetail = designDemandDetailMap.get(item);
            DesignDemandDetail noPassDetailUpdate = DesignDemandDetail.builder()
                    .designDemandDetailId(demandDetail.getDesignDemandDetailId())
                    .noPassReasonCode(req.getNoPassReasonCode())
                    .noPassReasonName(req.getNoPassReasonName())
                    .noPassTime(noPassTime)
                    .noPassUserId(userContent.getCurrentUserId())
                    .noPassUserName(userContent.getCurrentUserName())
                    .build();
            noPassDetailUpdateList.add(noPassDetailUpdate);
        });
        designDemandRepository.updateBatchById(noPassDemandUpdateList);
        designDemandDetailRepository.updateBatchById(noPassDetailUpdateList);


        //日志
        designDemandIdList.forEach(designDemandId -> {
            this.addLog(designDemandId, DesignLogContentEnum.DESIGN_DEMAND_NO_PASS.getDesc());
        });

        //发送淘汰MQ
        designDemandList.forEach(designDemand -> {
            DemandNoPassMqDTO mqDTO = DemandNoPassMqDTO.builder()
                    .designDemandId(designDemand.getDesignDemandId())
                    .sourceBizId(designDemand.getSourceBizId())
                    .inspirationStyleId(designDemand.getInspirationStyleId())
                    .noPassReason(req.getNoPassReasonName())
                    .noPassUserId(userContent.getCurrentUserId())
                    .noPassUserName(userContent.getCurrentUserName())
                    .noPassTime(noPassTime)
                    .build();
            MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.DESIGN_DEMAND_NO_PASS,
                    DesignMqConstant.DESIGN_DEMAND_NO_PASS_EXCHANGE,
                    DesignMqConstant.DESIGN_DEMAND_NO_PASS_ROUTING_KEY,
                    JSON.toJSONString(mqDTO));
            log.info("===灵感淘汰-发送mq: mqDto:{} ===", JSON.toJSONString(mqDTO));
            mqProducer.sendOnAfterCommit(mqMessageReq);
        });

        log.info("=== 灵感淘汰完成 designDemandIds:{}===",JSON.toJSONString(req.getDesignDemandIdList()));

    }

    private void passedAIBoxTaskResult(List<Long> ids) {
        designDemandAIBoxTaskResultRepository
                .lambdaUpdate()
                .set(DesignDemandAIBoxTaskResult::getAsPassed, true)
                .in(DesignDemandAIBoxTaskResult::getId, ids)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSpu(DesignDemandCreateSpuReq req) {
        log.info("=== 开款-req:{}===", JSON.toJSONString(req));
        Long designDemandId = req.getDesignDemandId();
        DesignDemand designDemand = designDemandRepository.getById(designDemandId);
        //校验
        this.checkCreateSpu(req, designDemandId, designDemand);

        if(!req.getPassedAIBoxTaskResultIds().isEmpty()){
            passedAIBoxTaskResult(req.getPassedAIBoxTaskResultIds());
        }

        // 查询店铺平台信息
        String platformName = this.getPlatformName(designDemand.getStoreName());

        //创建SPU
        DesignStyleCreateReq spuCreateReq = new DesignStyleCreateReq();
        spuCreateReq.setSourceType(DesignStyleSourceTypeEnum.DESIGN_DEMAND.getCode());
        spuCreateReq.setDesignDemandId(designDemandId);

        DesignStyleCreateResp styleCreateResp = designStyleService.createFromDemand(designDemand, platformName);
        String styleCode = styleCreateResp.getStyleCode();

        //开款更新
        DesignDemand demandUpdate = DesignDemand.builder()
                .designDemandId(designDemandId)
                .designDemandStatus(DesignDemandStatusEnum.CREATE_SPU.getCode())
                .styleCode(styleCode)
                .build();
        designDemandRepository.updateById(demandUpdate);

        //选中物料更新
        Long suggestedMaterialId = req.getSuggestedMaterialId();
        if (Objects.nonNull(suggestedMaterialId)) {
            DesignDemandSuggestedMaterial suggestedMaterialUpdate = DesignDemandSuggestedMaterial.builder()
                    .suggestedMaterialId(suggestedMaterialId)
                    .isChosen(Bool.YES.getCode())
                    .build();
            designDemandSuggestedMaterialRepository.updateById(suggestedMaterialUpdate);
        }

        //日志
        this.addLog(designDemandId, DesignLogContentEnum.DESIGN_DEMAND_CREATE_SPU.getDesc());

        //发送开款MQ
        DemandCreateSpuMqDTO mqDTO = DemandCreateSpuMqDTO.builder()
                .designDemandId(designDemand.getDesignDemandId())
                .sourceBizId(designDemand.getSourceBizId())
                .inspirationStyleId(designDemand.getInspirationStyleId())
                .styleCode(styleCode)
                .designCode(styleCreateResp.getDesignCode())
                .build();
        mqDTO.setDesignDemandId(designDemand.getDesignDemandId());
        mqDTO.setSourceBizId(designDemand.getSourceBizId());
        mqDTO.setInspirationStyleId(designDemand.getInspirationStyleId());
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.DESIGN_DEMAND_CREATE_SPU,
                DesignMqConstant.DESIGN_DEMAND_CREATE_SPU_EXCHANGE,
                DesignMqConstant.DESIGN_DEMAND_CREATE_SPU_ROUTING_KEY,
                JSON.toJSONString(mqDTO));
        mqProducer.sendOnAfterCommit(mqMessageReq);

        log.info("=== 开款完成 designDemandId:{}; spu:{}===",designDemandId, styleCode);
    }

    private String getPlatformName(String storeName) {
        if (StrUtil.isBlank(storeName)) {
            return null;
        }

        List<ShopResp> shopList = popProductHelper.getShopList(Collections.singletonList(storeName));
        if (CollUtil.isEmpty(shopList)) {
            return null;
        }

        return shopList.getFirst().getPlatformName();
    }

    private void checkCreateSpu(DesignDemandCreateSpuReq req, Long designDemandId,
                                              DesignDemand designDemand) {
        SdpDesignException.notNull(designDemand, "灵感任务需求不存在! demandId:{}", designDemandId);
        DesignDemandDetail demandDetail = designDemandDetailRepository.getByDemandId(designDemandId);
        SdpDesignException.notNull(demandDetail, "灵感任务需求详情不存在! demandId:{}", designDemandId);
        SdpDesignException.isTrue(Objects.equals(designDemand.getDesignDemandStatus(), DesignDemandStatusEnum.WAIT_HANDLE.getCode()),
                "待处理的任务才可以开款!");
        Long suggestedMaterialId = req.getSuggestedMaterialId();
        if (Objects.nonNull(suggestedMaterialId)) {
            DesignDemandSuggestedMaterial suggestedMaterial = designDemandSuggestedMaterialRepository.getById(suggestedMaterialId);
            SdpDesignException.notNull(suggestedMaterial, "推荐物料不存在! suggestedMaterialId:{}", suggestedMaterialId);
        }
    }


    private void addLog(Long designDemandId, String logContent) {
        DesignLogSdpSaveReq logSdpSaveReq = DesignLogSdpSaveReq.builder()
                .bizId(designDemandId)
                .bizType(DesignLogBizTypeEnum.DESIGN_DEMAND)
                .content(logContent)
                .build();
        designLogService.sdpSave(logSdpSaveReq);
    }


}
