package tech.tiangong.sdp.design.business.remark;

import cn.hutool.core.lang.Assert;
import cn.hutool.extra.spring.SpringUtil;
import tech.tiangong.sdp.design.entity.DesignDemand;
import tech.tiangong.sdp.design.entity.DesignRemarks;
import tech.tiangong.sdp.design.repository.DesignDemandRepository;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;

/**
 * <AUTHOR>
 */
public class ComposeDesignRemarkDesignDemand implements ComposeDesignRemark {
    @Override
    public DesignRemarks compose(DesignRemarksReq req, DesignRemarks designRemarks) {
        DesignDemandRepository designDemandRepository = SpringUtil.getBean(DesignDemandRepository.class);
        DesignDemand designDemand = designDemandRepository.getById(req.getBizId());
        Assert.notNull(designDemand, "不存在此设计需求");
        designRemarks.setStyleCode(designDemand.getStyleCode());
        designRemarks.setDesignCode(null);
        return designRemarks;
    }
}
