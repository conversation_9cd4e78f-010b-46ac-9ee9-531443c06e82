package tech.tiangong.sdp.design.remote;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.net.DataResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.material.pojo.web.request.sizecategory.SizeTypeInfoListReq;
import tech.tiangong.sdp.material.pojo.web.response.SizeTemplateImageVO;
import tech.tiangong.sdp.material.pojo.web.response.sizecategory.SizeTypeInfoVo;
import tech.tiangong.sdp.material.sdk.service.remote.SizeCategoryRemoteService;
import tech.tiangong.sdp.material.sdk.service.remote.SizeTemplateImageRemoteService;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/7 16:15
 */

@Component
@Slf4j
@RequiredArgsConstructor
public class ClothingFoundationRemoteHelper {
    private final SizeCategoryRemoteService sizeCategoryRemoteService;
    private final SizeTemplateImageRemoteService sizeTemplateImageRemoteService;

    /**
     * 尺码表模板查询
     */
    public List<SizeTemplateImageVO> sizeTemplateImage(List<String> categoryCodes) {
        if (CollUtil.isEmpty(categoryCodes)) {
            return Collections.emptyList();
        }
        DataResponse<List<SizeTemplateImageVO>> response = sizeTemplateImageRemoteService.findByTemplateImageCode(categoryCodes);
        SdpDesignException.isTrue(response.isSuccessful(), "尺码表模板查询异常:{}", response.getMessage());
        return response.getData();
    }

    /**
     * 号型查询
     */
    public List<SizeTypeInfoVo> sizeTypeInfoList(SizeTypeInfoListReq listReq) {
        DataResponse<List<SizeTypeInfoVo>> response = sizeCategoryRemoteService.sizeTypeInfoList(listReq);
        SdpDesignException.isTrue(response.isSuccessful(), "号型查询异常:{}", response.getMessage());
        return response.getData();
    }

}
