package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.enums.visual.VisualErrorCodeEnum;
import tech.tiangong.sdp.design.service.VisualQcService;
import tech.tiangong.sdp.design.service.VisualTaskTryOnLogService;
import tech.tiangong.sdp.design.vo.query.visual.VisualQcQuery;
import tech.tiangong.sdp.design.vo.req.visual.BatchSubmitTryOnReq;
import tech.tiangong.sdp.design.vo.req.visual.BatchSubmitVisualQcReq;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualQcReq;
import tech.tiangong.sdp.design.vo.resp.visual.VisualQcDetailVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskQcListCountVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskQcListVo;

import javax.validation.Valid;
import java.util.List;


/**
 * 视觉任务质检处理-web
 *
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/visual-qc")
public class VisualQcController extends BaseController {
    private final VisualQcService visualQcService;

    /**
     * 列表
     *
     * @param query 分页参数
     * @return PageRespVo<VisualTaskQcListVo>
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<VisualTaskQcListVo>> page(@RequestBody @Validated VisualQcQuery query) {
        return DataResponse.ok(visualQcService.page(query));
    }
    /**
     * 状态统计查询
     *
     */
    @PostMapping("/count-state")
    public DataResponse<VisualTaskQcListCountVo> countState(@RequestBody @Validated VisualQcQuery req) {
        return DataResponse.ok(visualQcService.countState(req));
    }

    /**
     * 根据任务ID查询最新的质检任务
     */
    @GetMapping("/get-latest-qc-type-by-task-id/{taskId}")
    public DataResponse<VisualQcDetailVo> getLatestQcTypeByTaskId(@PathVariable(value = "taskId") Long taskId) {
        return DataResponse.ok(visualQcService.getLatestQcTypeByTaskId(taskId));
    }

    /**
     * 提交质检任务
     */
    @PostMapping("/save-visual-qc")
    public DataResponse<Boolean> saveVisualQc(@RequestBody @Validated SaveVisualQcReq req) {
        Boolean result = null;
        try{
            result = visualQcService.saveVisualQc(req);
        }catch (Exception e){
            VisualErrorCodeEnum visualErrorCode = VisualErrorCodeEnum.findByCode(e.getMessage());
            if(!VisualErrorCodeEnum.UNKNOWN.equals(visualErrorCode)){
                log.error("提交质检任务失败",e);
                DataResponse<Boolean> dataResponse = DataResponse.failed();
                dataResponse.setCode(visualErrorCode.getCode());
                dataResponse.setMessage(visualErrorCode.getDesc());
                dataResponse.setSuccessful(false);
                return dataResponse;
            }
            throw e;
        }
        return DataResponse.ok(result);
    }

    /**
     * 批量提交质检结果
     */
    @PostMapping("/batch-submit-visual-qc")
    public DataResponse<Boolean> batchSubmitVisualQc(@RequestBody @Validated BatchSubmitVisualQcReq req) {
        return DataResponse.ok(visualQcService.batchSubmitVisualQc(req));
    }

    @PostMapping("/batch-submit-try-on-qc")
    public DataResponse<Boolean> batchSubmitTryOnQc(@Valid @RequestBody List<BatchSubmitTryOnReq> reqList) {
        return DataResponse.ok(visualQcService.batchSubmitTryOn(reqList));
    }

}
