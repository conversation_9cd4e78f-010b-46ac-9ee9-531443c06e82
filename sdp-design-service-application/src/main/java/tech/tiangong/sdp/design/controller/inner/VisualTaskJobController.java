package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.VisualTaskTryOnLogService;
import tech.tiangong.sdp.design.vo.req.visual.SyncVisualTaskTryOnStateReq;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/jobs/visual-task")
public class VisualTaskJobController {

    private final VisualTaskTryOnLogService visualTaskTryOnLogService;

    @PostMapping("/sync-visual-try-on-task-state")
    public DataResponse<Void> syncVisualTaskTryOnState(@RequestBody SyncVisualTaskTryOnStateReq req) {
        UserContentHolder.set(new UserContent()
                .setCurrentUserId(0L)
                .setCurrentUserCode("0")
                .setCurrentUserName("系统")
                .setTenantId(2L)
                .setSystemCode("SDP"));
        visualTaskTryOnLogService.syncVisualTaskTryOnState(req);
        return DataResponse.ok();
    }
}
