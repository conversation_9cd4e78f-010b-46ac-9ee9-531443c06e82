package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.service.BomOperateService;
import tech.tiangong.sdp.design.service.BomOrderService;
import tech.tiangong.sdp.design.vo.query.bom.BomMaterialExportQuery;
import tech.tiangong.sdp.design.vo.req.bom.*;
import tech.tiangong.sdp.design.vo.resp.bom.*;
import tech.tiangong.sdp.utils.PlmExcelExportUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 开发Bom-web
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/13 14:48
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/bom")
public class BomOrderWebController extends BaseController {

	private final BomOrderService bomOrderService;
	private final BomOperateService bomOperateService;

	/**
	 * Bom列表
	 *
	 * @param req 分页对象
	 * @return Bom列表响应
	 */
	@PostMapping("/page")
	public DataResponse<PageRespVo<BomOrderListResp>> page(@RequestBody @Validated BomOrderListReq req) {
		PageRespVo<BomOrderListResp> pageRespVo = bomOrderService.page(req);
		return DataResponse.ok(pageRespVo);
	}

	/**
	 * bom详情-web端
	 * @param req 详情查询req
	 * @return bom详情
	 */
	@PostMapping("/web-detail")
	public DataResponse<BomOrderDetailResp> webDetail(@RequestBody @Validated BomWebDetailReq req) throws Exception {
		bomOrderService.updateLatestPriceByBomId(req.getBomId());
		BomOrderDetailResp orderDetailResp = bomOrderService.webDetail(req);
		return DataResponse.ok(orderDetailResp);
	}

	/**
	 * bom打印
	 * @param bomId bomId
	 * @return BomOrderPrintVo
	 */
	@GetMapping("/print")
	public DataResponse<BomOrderPrintVo> print(@RequestParam Long bomId) throws Exception {
		bomOrderService.updateLatestPriceByBomId(bomId);
		BomOrderPrintVo resp = bomOrderService.print(bomId);
		return DataResponse.ok(resp);
	}

	/**
	 * bom批量打印
	 * @param req 入参
	 * @return BomOrderPrintVo
	 */
	@PostMapping("/batch-print")
	public DataResponse<List<BomOrderPrintVo>> batchPrint(@Validated @RequestBody BomBatchPrintReq req) {
		if (!req.getIsPerformance()) {
			SdpDesignException.notEmpty(req.getBomIds(), "BomID不能为空");
			req.getBomIds().forEach(bomOrderService::updateLatestPriceByBomId);
		}else {
			SdpDesignException.notEmpty(req.getDesignCodeList(), "设计款号不能为空");
			req.getDesignCodeList().forEach(bomOrderService::updateLatestPriceByDesignCode);
		}
		List<BomOrderPrintVo> resp = bomOrderService.batchPrint(req);
		return DataResponse.ok(resp);
	}

	/**
	 * bom状态数量统计
	 *
	 * @param designerId 设计师ID
	 * @return BomOrderStateStatisticsResp
	 */
	@GetMapping("/state-statistics")
	public DataResponse<List<BomOrderStateStatisticsResp>> bomStateStatistics(@RequestParam(value = "designerId", required = false) Long designerId,
																			  @RequestParam(value = "clothesDesigner", required = false) Integer clothesDesigner) {
		List<BomOrderStateStatisticsResp> resp = bomOrderService.bomStateStatistics(designerId,clothesDesigner);
		return DataResponse.ok(resp);
	}

	/**
	 * Bom单导出Excel
	 * @param bomId bomId
	 * @return Void
	 */
	@NoRepeatSubmitLock
	@GetMapping("/export/excel")
	public DataResponse<Void> bomOrderExportExcel(@RequestParam Long bomId, HttpServletResponse response) throws Exception {
		// bomOrderService.updateLatestPriceByBomId(bomId);
		BomOrderExcelResp bomOrderExcelResp = bomOrderService.bomOrderExportExcel(bomId);
		PlmExcelExportUtil.bomTemplateExport(bomOrderExcelResp, response);
		return DataResponse.ok();
	}


	/**
	 * 工艺需求匹配信息
	 * @param craftDemandId 工艺需求ID
	 * @return 工艺需求匹配信息
	 */
	@GetMapping("/craft/match")
	public DataResponse<List<CraftDemandMatchResp>> craftDemandMatchInfo(@RequestParam(required = false) Long craftDemandId) {
		List<CraftDemandMatchResp> list = bomOrderService.getCraftDemandMatchInfo(craftDemandId);
		return DataResponse.ok(list);
	}

	/**
	 * 查询好料网面辅料信息
	 *
	 * @param req 入参
	 * @return GoodMaterialInfoResp
	 */
	@PostMapping("/good-material")
	public DataResponse<GoodMaterialInfoResp> goodMaterialInfo(@Validated @RequestBody GoodMaterialInfoReq req) {
		GoodMaterialInfoResp resp = bomOrderService.goodMaterialInfo(req);
		return DataResponse.ok(resp);
	}

	/**
	 * bom提交
	 * @param req 入参
	 * @return bomId
	 */
	@NoRepeatSubmitLock
	@PostMapping("/submit")
	public DataResponse<BomOrderUpdateResp> submit(@Validated @RequestBody BomOrderUpdateV3Req req) {
		BomOrderUpdateResp resp = bomOperateService.submit(req);
		return DataResponse.ok(resp);
	}

	/**
	 * 根据设计款号查询最新已提交(已核算)的Bom采购列表
	 *
	 * @param designCode 设计款号
	 * @return 采购列表
	 */
	@GetMapping("/latest/purchase-list")
	public DataResponse<BomOrderApplyPurchaseResp> getBomApplyPurchaseList(@RequestParam(value = "designCode") String designCode) throws Exception {
		return DataResponse.ok(bomOrderService.getBomApplyPurchaseList(designCode));
	}

	/**
	 * 查询bom单可引用的skc
	 *
	 * @param bomId bomId
	 * @return BomQuoteSkcResp
	 */
	@GetMapping("/quote-skc")
	public DataResponse<BomQuoteSkcResp> quoteSkc(@RequestParam Long bomId) {
		return DataResponse.ok(bomOrderService.quoteSkc(bomId));
	}


	/**
	 * 查询引用款的最新Bom详情
	 *
	 * @param req 入参
	 * @return bom详情
	 */
	@PostMapping("/quote-skc/detail-latest")
	public DataResponse<BomOrderDetailResp> getQuoteBomOrderDetail(@Validated @RequestBody BomQuoteDetailReq req) throws Exception {
		return DataResponse.ok(bomOrderService.getQuoteBomOrderDetail(req));
	}

	/**
	 * 根据设计款号查询最新bom单基础信息
	 *
	 * @param designCode 设计款号
	 * @return BomOrderBaseResp
	 */
	@GetMapping("/latest/base-info/{designCode}")
	public DataResponse<BomOrderBaseResp> getBomBaseBySkc(@PathVariable(value = "designCode") String designCode) {
		return DataResponse.ok(bomOrderService.getBomBaseBySkc(designCode));
	}

	/**
	 * bom物料导出Excel-最多导出1千条bom单的物料记录
	 *
	 */
	@NoRepeatSubmitLock
	@PostMapping("/export/material-excel")
	public void exportMaterialExcel(@RequestBody BomMaterialExportQuery query, HttpServletResponse response) throws Exception {
		List<BomMaterialExcelResp> excelResp = bomOrderService.exportMaterialExcel(query);
		PlmExcelExportUtil.bomMaterialTemplateExport(excelResp, response);
	}

	/**
	 * 根据skc查询最新bom单物料图片信息
	 *
	 * @param req 入参
	 * @return List<BomMaterialPictureResp>
	 */
	@PostMapping("/material-picture")
	public DataResponse<List<BomMaterialPictureResp>> queryMaterialPicture(@Validated @RequestBody BomMaterialPictureReq req) {
		return DataResponse.ok(bomOrderService.queryMaterialPicture(req));
	}


}
