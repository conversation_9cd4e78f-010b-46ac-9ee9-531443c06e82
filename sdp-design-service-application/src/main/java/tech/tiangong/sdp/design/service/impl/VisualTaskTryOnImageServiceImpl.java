package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.zjkj.aigc.common.enums.VirtuaImageNodeEnum;
import com.zjkj.aigc.common.task.resp.VirtualDressingTaskDetailV2VO;
import com.zjkj.aigc.feign.client.saas.VirtualDressingTaskV2Client;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.visual.*;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.VisualTaskHandleService;
import tech.tiangong.sdp.design.service.VisualTaskNodeStateService;
import tech.tiangong.sdp.design.service.VisualTaskService;
import tech.tiangong.sdp.design.service.VisualTaskTryOnImageService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class VisualTaskTryOnImageServiceImpl implements VisualTaskTryOnImageService {

    @Resource
    private VisualTaskTryOnImageRepository visualTaskTryOnImageRepository;
    @Resource
    private VisualTaskTryOnLogRepository visualTaskTryOnLogRepository;
    @Resource
    private VisualTaskTryOnRepository visualTaskTryOnRepository;
    @Resource
    private VisualTaskNodeStateRepository visualTaskNodeStateRepository;
    @Resource
    private VisualTaskNodeStateService nodeStateService;
    @Resource
    private VisualQcRepository qcRepository;
    @Resource
    private VirtualDressingTaskV2Client virtualDressingTaskV2Client;
    @Resource
    private VisualTaskHelper visualTaskHelper;
    @Resource
    private VisualTaskRepository visualTaskRepository;


    @Override
    public void setTryOnMainImage(Long visualTaskId, Long tryOnTaskImageId) {

        VisualTaskTryOnImage image = visualTaskTryOnImageRepository.getById(tryOnTaskImageId);
        Assert.notNull(image, "Try on 图片不存在");
        image.setIsMain(1);
        image.setGeneratedImgName(processImageNameToMainImage(image.getGeneratedImgName()));
        visualTaskTryOnImageRepository.updateById(image);
    }

    private static final String MAIN_IMAGE_SUFFIX = "-301";
    public String processImageNameToMainImage(String imageName) {
        if (CharSequenceUtil.isBlank(imageName)) {
            return "";
        }
        int dotIndex = imageName.lastIndexOf('.');
        if (dotIndex == -1) {
            return imageName;
        }

        String namePart = imageName.substring(0, dotIndex);
        String extension = imageName.substring(dotIndex);

        if (!namePart.endsWith(MAIN_IMAGE_SUFFIX)) {
            return namePart + MAIN_IMAGE_SUFFIX + extension;
        }
        return imageName;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleTryOnResult(VirtualDressingTaskDetailV2VO taskDetail, VisualTaskTryOnLog visualTaskTryOnLog) {

            visualTaskTryOnLog.setTryOnTaskCode(taskDetail.getTaskCode());
            switch (taskDetail.getTaskState()) {
                case 20 -> {
                    visualTaskTryOnLog.setTryOntTaskState(VisualTaskHandleStateEnum.CANCEL.getCode());
                    visualTaskTryOnLogRepository.updateById(visualTaskTryOnLog);
                }
                case 40 -> // 任务失败 暂时借用取消的状态
                {
                    visualTaskTryOnLog.setTryOntTaskState(VisualTaskHandleStateEnum.FAIL.getCode());
                    visualTaskTryOnLogRepository.updateById(visualTaskTryOnLog);
                }
                case 30 -> {
                    // 更新状态
                    visualTaskTryOnLog.setTryOntTaskState(VisualTaskHandleStateEnum.FINISH.getCode());
                    visualTaskTryOnLogRepository.updateById(visualTaskTryOnLog);

                    // 保存图片信息
                    if (CollectionUtil.isNotEmpty(taskDetail.getGeneratedImages())) {
                        List<VisualTaskTryOnImage> visualTaskTryOnImageList = new ArrayList<>();
//                                List<ImageFile> imageFileList = new ArrayList<>();
                        taskDetail.getGeneratedImages().stream().forEach(image -> {
                            if (List.of(VirtuaImageNodeEnum.KONTEXT.getCode(), VirtuaImageNodeEnum.TryOn.getCode()).contains(image.getImageNote())) {
                                VisualTaskTryOnImage visualTaskTryOnImage = new VisualTaskTryOnImage();
                                visualTaskTryOnImage.setTryOnLogId(visualTaskTryOnLog.getVisualTaskTryOnLogId());
                                visualTaskTryOnImage.setTryOnTaskImageId(IdPool.getId());
                                visualTaskTryOnImage.setTaskId(visualTaskTryOnLog.getTaskId());
                                visualTaskTryOnImage.setTryOnTaskId(visualTaskTryOnLog.getTryOnTaskId());
                                visualTaskTryOnImage.setGeneratedImg(image.getImageUrl());
                                visualTaskTryOnImage.setGeneratedImgName(getFileNameFromOssUrl(image.getImageUrl()));
                                visualTaskTryOnImage.setImageNode(image.getImageNote());
                                visualTaskTryOnImage.setImageTryOnType(image.getImageTryOnType());
                                visualTaskTryOnImage.setImageKontextType(image.getImageKontextType());
                                visualTaskTryOnImageList.add(visualTaskTryOnImage);
                            }
                        });
                        visualTaskTryOnImageRepository.saveBatch(visualTaskTryOnImageList);
                    }


                    VisualTaskNodeState nodeState = visualTaskNodeStateRepository.getNodeByProcessNode(visualTaskTryOnLog.getTaskId(), VisualTaskNodeEnum.TRYON_HANDLE);
                    // 只有节点在等待开始或者进行中时，才更新状态为完成
                    if (nodeState != null && Arrays.asList(VisualTaskHandleStateEnum.DOING.getCode(), VisualTaskHandleStateEnum.WAITING_START.getCode()).contains(nodeState.getProcessNodeState()) ) {
                        nodeState.setProcessNodeState(VisualTaskHandleStateEnum.FINISH.getCode());
                        nodeState.setRemark(VisualTaskNodeEnum.TRYON_HANDLE.getDesc()+":"+ VisualTaskHandleStateEnum.FINISH.getDesc());
                        visualTaskNodeStateRepository.updateById(nodeState);

                        // 新增try on质检任务
                        VisualTaskNodeState tryOnQc = visualTaskNodeStateRepository.getNodeByProcessNode(visualTaskTryOnLog.getTaskId(), VisualTaskNodeEnum.TRY_ON_QC);
                        if (tryOnQc == null) {
                            // 新增节点状态
                            tryOnQc = new VisualTaskNodeState();
                            tryOnQc.setNodeStateId(IdPool.getId());
                            tryOnQc.setTaskId(visualTaskTryOnLog.getTaskId());
                            tryOnQc.setProcessNode(VisualTaskNodeEnum.TRY_ON_QC.getCode());
                            tryOnQc.setProcessStep(VisualTaskStepEnum.QC.getCode());
                            tryOnQc.setProcessNodeState(VisualTaskHandleStateEnum.WAITING_ALLOCATE.getCode());
                            tryOnQc.setRemark(VisualTaskNodeEnum.TRY_ON_QC.getDesc()+":"+ VisualTaskQcStateEnum.WAITING_QC.getDesc());
                        }else {
                            // 更新节点状态
                            tryOnQc.setProcessNodeState(VisualTaskHandleStateEnum.WAITING_ALLOCATE.getCode());
                            tryOnQc.setRemark(VisualTaskNodeEnum.TRY_ON_QC.getDesc()+":"+ VisualTaskQcStateEnum.WAITING_QC.getDesc());
                        }
                        visualTaskNodeStateRepository.saveOrUpdate(tryOnQc);

                        List<VisualQc> updateQcList = new ArrayList<>();
                        VisualQc oldVisualQc = qcRepository.getLatestByTaskId(visualTaskTryOnLog.getTaskId(), VisualQcTypeEnum.TRY_ON);
                        //已有视觉质检任务未提交过
                        if(oldVisualQc!=null
                                && StringUtils.isBlank(oldVisualQc.getOnShelfImages())
                                && VisualTaskQcStateEnum.WAITING_QC.getCode().equals(oldVisualQc.getQcState())){
                            oldVisualQc.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
//                        oldVisualQc.setQcRecord(JSONObject.toJSONString(visualQcRecords));
                            oldVisualQc.setTaskHandlerId(visualTaskTryOnLog.getCreatorId());
                            oldVisualQc.setTaskHandlerName(visualTaskTryOnLog.getCreatorName());
                            oldVisualQc.setTaskHandleTime(LocalDateTime.now());
                            updateQcList.add(oldVisualQc);
                        }else{//创建视觉质检任务
                            VisualQc visualQc = new VisualQc();
                            visualQc.setQcRecordId(IdPool.getId());
                            visualQc.setTaskId(visualTaskTryOnLog.getTaskId());
                            visualQc.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
//                        visualQc.setQcRecord(JSONObject.toJSONString(visualQcRecords));
                            visualQc.setVersionNum(oldVisualQc!=null ? oldVisualQc.getVersionNum()+1 : 1);
                            visualQc.setQcState(VisualTaskQcStateEnum.WAITING_QC.getCode());
                            visualQc.setQcType(VisualQcTypeEnum.TRY_ON.getCode());
                            visualQc.setTaskHandlerId(visualTaskTryOnLog.getCreatorId());
                            visualQc.setTaskHandlerName(visualTaskTryOnLog.getCreatorName());
                            visualQc.setTaskHandleTime(LocalDateTime.now());
                            visualQc.setIsLatest(1);
                            updateQcList.add(visualQc);
                            //将旧版本任务过期掉
                            if(oldVisualQc!=null){
                                oldVisualQc.setIsLatest(0);
                                updateQcList.add(oldVisualQc);
                            }
                        }
                        qcRepository.saveOrUpdateBatch(updateQcList);
                    }
                }
                default -> // if 结果等于进行中
                {
                    visualTaskTryOnLog.setTryOntTaskState(VisualTaskHandleStateEnum.DOING.getCode());
                    visualTaskTryOnLogRepository.updateById(visualTaskTryOnLog);
                }
            }
    }

    private String getFileNameFromOssUrl(String ossUrl) {
        if (ossUrl == null || ossUrl.isBlank()) {
            return "";
        }
        // 提取文件名
        int lastSlashIndex = ossUrl.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < ossUrl.length() - 1) {
            return ossUrl.substring(lastSlashIndex + 1);
        }

        // 如果没有找到斜杠，返回空字符串
        return "";
    }
}
