package tech.tiangong.sdp.design.repository;

import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.OrderMaterialFollow;
import tech.tiangong.sdp.design.mapper.OrderMaterialFollowMapper;
import tech.tiangong.sdp.design.vo.req.ordermaterial.OrderMaterialFollowPageReq;
import tech.tiangong.sdp.design.vo.resp.ordermaterial.OrderMaterialFollowPageVo;

import java.util.Collections;
import java.util.List;


/**
* 物料齐套跟进
* <br>CreateDate August 09,2021
* <AUTHOR>
* @since 1.0
*/


@AllArgsConstructor
@Slf4j
@Repository
public class OrderMaterialFollowRepository extends BaseRepository<OrderMaterialFollowMapper, OrderMaterialFollow> {


    public List<OrderMaterialFollowPageVo> pageList(OrderMaterialFollowPageReq req){
        return getBaseMapper().pageList(req);
    }


    public List<OrderMaterialFollow> listByDesignCode(String designCode) {
        if (StrUtil.isBlank(designCode)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(OrderMaterialFollow::getDesignCode, designCode)
                .list();
    }
}