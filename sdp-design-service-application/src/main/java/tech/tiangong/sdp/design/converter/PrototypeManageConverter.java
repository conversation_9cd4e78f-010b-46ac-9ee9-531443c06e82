package tech.tiangong.sdp.design.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import tech.tiangong.sdp.clothes.enums.CheckPriceStateEnum;
import tech.tiangong.sdp.clothes.vo.resp.ClothesProcessInfoVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.CheckPriceBaseInfoInnerVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.DesignPricingInfoVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.EstimateCheckPriceVo;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.BomCloseSourceEnum;
import tech.tiangong.sdp.design.enums.BomOrderStateEnum;
import tech.tiangong.sdp.design.enums.CloseMaterialPurchaseSourceEnum;
import tech.tiangong.sdp.design.enums.PrototypeStatusEnum;
import tech.tiangong.sdp.design.vo.req.bom.BomCloseWithPrototypeReq;
import tech.tiangong.sdp.design.vo.req.demand.DesignDemandCreateReq;
import tech.tiangong.sdp.design.vo.req.manage.PrototypeCancelReq;
import tech.tiangong.sdp.design.vo.req.manage.PrototypeSampleRedoReq;
import tech.tiangong.sdp.design.vo.req.material.OtherProcessCancelMaterialListReq;
import tech.tiangong.sdp.design.vo.resp.bom.CraftDemandInfoVo;
import tech.tiangong.sdp.design.vo.resp.prototype.PrototypeManageInfoQueryResp;
import tech.tiangong.sdp.design.vo.resp.prototype.PrototypeManageQueryResp;
import tech.tiangong.sdp.design.vo.resp.prototype.PrototypeVo;
import tech.tiangong.sdp.utils.Bool;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

;

/**
 *  设计款管理转换器
 * <AUTHOR>
 * @date 2021/8/22 12:59
 */
public class PrototypeManageConverter {


    /**
     * 封装-设计款管理分页结果list
     *
     * @param list                  设计款响应列表
     * @param styleList
     * @param latestBomOrderList    最新的bom单集合
     * @param craftDemandInfoList   二次工艺集合
     * @param checkedPriceMap       核价信息
     * @param designDemandDetailMap
     */
    public static void buildPageList(List<PrototypeManageQueryResp> list,
                                     List<DesignStyle> styleList,
                                     List<BomOrder> latestBomOrderList,
                                     List<CraftDemandInfoVo> craftDemandInfoList,
                                     Map<String, CheckPriceBaseInfoInnerVo> checkedPriceMap,
                                     Map<Long, DesignDemand> designDemandMap, Map<Long, DesignDemandDetail> designDemandDetailMap) {

        Map<String, DesignStyle> styleMap = StreamUtil.list2Map(styleList, DesignStyle::getStyleCode);
        Map<String, BomOrder> bomOrderMap = latestBomOrderList.stream().collect(Collectors.toMap(BomOrder::getDesignCode, Function.identity(), (k1, k2) -> k1));
        //二次工艺map; 注: 部分历史数据二次工艺不跟bom绑定, 没有bomId;
        Map<Long, List<CraftDemandInfoVo>> craftDemandInfoMap = craftDemandInfoList.stream()
                .filter(item -> Objects.nonNull(item.getBomId())).collect(Collectors.groupingBy(CraftDemandInfoVo::getBomId));

        list.forEach(prototypeResp -> {
            boolean canMakeColor = Objects.equals(PrototypeStatusEnum.DECOMPOSED.getCode(), prototypeResp.getPrototypeStatus());
            prototypeResp.setCanMakeColor(Bool.of(canMakeColor).getCode());
            // prototypeResp.setStyleSeasonList(JSONArray.parseArray(prototypeResp.getStyleSeason(), OpsObject.class));

            //spu信息
            DesignStyle designStyle = styleMap.get(prototypeResp.getStyleCode());
            prototypeResp.setWaveBandCode(designStyle.getWaveBandCode());
            prototypeResp.setWaveBandName(designStyle.getWaveBandName());
            prototypeResp.setCountrySiteCode(designStyle.getCountrySiteCode());
            prototypeResp.setCountrySiteName(designStyle.getCountrySiteName());
            prototypeResp.setStoreId(designStyle.getStoreId());
            prototypeResp.setStoreName(designStyle.getStoreName());
            prototypeResp.setBuyerId(designStyle.getBuyerId());
            prototypeResp.setBuyerName(designStyle.getBuyerName());
            prototypeResp.setPlatformName(designStyle.getPlatformName());
            prototypeResp.setSceneName(designStyle.getSceneName());
            prototypeResp.setSceneCode(designStyle.getSceneCode());
            prototypeResp.setPalletTypeName(designStyle.getPalletTypeName());

            //bom单信息
            BomOrder bomOrder = bomOrderMap.get(prototypeResp.getDesignCode());
            if (Objects.nonNull(bomOrder)) {
                PrototypeManageQueryResp.DevelopBomInfo bomInfo = new PrototypeManageQueryResp.DevelopBomInfo();
                bomInfo.setBomId(bomOrder.getBomId());
                bomInfo.setBomCode(bomOrder.getBomCode());
                bomInfo.setBomVersionNum(bomOrder.getVersionNum());
                bomInfo.setBomOrderState(BomOrderStateEnum.findEntityByCode(bomOrder.getState()));
                List<CraftDemandInfoVo> craftDemandInfos = craftDemandInfoMap.get(bomOrder.getBomId());
                if (CollectionUtil.isNotEmpty(craftDemandInfos)) {
                    Map<Integer,List<String>> categoryMap = new HashMap<>();
                    craftDemandInfos.stream().collect(Collectors.groupingBy(CraftDemandInfoVo::getCraftsRequire)).forEach((key, value) -> {
                        categoryMap.put(key,value.stream().map( item ->StringUtils.isBlank(item.getCategory3()) ? item.getCategory2() : item.getCategory3()).collect(Collectors.toList()));
                    });
                    bomInfo.setCategoryMap(categoryMap);
                    List<String> craftPictureList = new LinkedList<>();
                    for (CraftDemandInfoVo craftDemandInfoVo : craftDemandInfos) {
                        if (CollUtil.isNotEmpty(craftDemandInfoVo.getPictureList())) {
                            craftPictureList.addAll(craftDemandInfoVo.getPictureList());
                        }
                    }
                    bomInfo.setCraftPictureList(craftPictureList);
                }
                prototypeResp.setDevelopBomInfo(bomInfo);
            }

            //核价信息
            CheckPriceBaseInfoInnerVo checkedBaseInfo = checkedPriceMap.get(prototypeResp.getDesignCode());
            if (Objects.nonNull(checkedBaseInfo)){
                // 最新bom是已提交/已核算 状态就是展示：最新bom未核价 （存在已核价bom才展示）
                if(Objects.nonNull(bomOrder)
                        && (BomOrderStateEnum.SUBMITTED.getCode().equals(bomOrder.getState()) || BomOrderStateEnum.CALCULATED.getCode().equals(bomOrder.getState()))){
                    prototypeResp.setIsLastBomCheckPrice(Bool.NO.getCode());
                }
            }

            // 判断最新bom是否已经核价
            if ((Objects.nonNull(bomOrder) && Objects.nonNull(checkedBaseInfo))
                    && (bomOrder.getBomId().equals(checkedBaseInfo.getBomId()))) {
                prototypeResp.setIsLastBomCheckPrice(Bool.YES.getCode());
            }

            //灵感需求信息
            DesignDemand designDemand = designDemandMap.get(designStyle.getDesignDemandId());
            if (Objects.nonNull(designDemand)) {
                PrototypeManageQueryResp.DesignDemandInfo designDemandInfo = new PrototypeManageQueryResp.DesignDemandInfo();
                BeanUtils.copyProperties(designDemand, designDemandInfo);
                DesignDemandDetail designDemandDetail = designDemandDetailMap.get(designDemand.getDesignDemandId());
                if (Objects.nonNull(designDemandDetail)) {
                    designDemandInfo.setOriginalImage(designDemandDetail.getOriginalImage());
                    List<String> imageAllList = new ArrayList<>();
                    if(!CollUtil.isEmpty(designDemandDetail.getInspirationImageList())){
                        imageAllList.addAll(designDemandDetail.getInspirationImageList());
                    }

                    //展示4k图
                    List<DesignDemandCreateReq.ImageInfo> imageList = designDemandDetail.getInspirationImageJson();
                    if(null!= imageList){
                        List<String> ultraHdUrlList = imageList.stream()
                                .filter(item -> StrUtil.isNotBlank(item.getUltraHdUrl()))
                                .map(DesignDemandCreateReq.ImageInfo::getUltraHdUrl)
                                .collect(Collectors.toList());
                        if(!CollUtil.isEmpty(ultraHdUrlList)){
                            imageAllList.addAll(ultraHdUrlList);
                        }
                    }
                    designDemandInfo.setInspirationImageList(imageAllList);
                }
                prototypeResp.setDemandDesignInfo(designDemandInfo);
            }
        });
    }

    /**
     * 封装-设计款列表中_样衣_询报价信息
     *
     * @param designCodeList 设计款号集合
     * @param clothesMap     样衣打版信息Map
     * @param checkPriceMap
     * @return 样衣_核价信息Map
     */
    public static List<PrototypeManageInfoQueryResp> buildClothesPriceProductList(List<String> designCodeList,
                                                                                  Map<String, List<ClothesProcessInfoVo>> clothesMap, Map<String, DesignPricingInfoVo> checkPriceMap) {
        List<PrototypeManageInfoQueryResp> respList = new ArrayList<>(designCodeList.size());
        designCodeList.forEach(item -> {
            PrototypeManageInfoQueryResp infoVo = new PrototypeManageInfoQueryResp();
            infoVo.setDesignCode(item);
            //加工单信息
            List<ClothesProcessInfoVo> clothesVos = clothesMap.get(item);
            if (CollectionUtil.isNotEmpty(clothesVos)) {
                List<PrototypeManageInfoQueryResp.SampleInfo> sampleInfos=new ArrayList<>();
                for (ClothesProcessInfoVo clothesVo : clothesVos) {
                    PrototypeManageInfoQueryResp.SampleInfo sampleInfo = new PrototypeManageInfoQueryResp.SampleInfo();
                    BeanUtils.copyProperties(clothesVo,sampleInfo);
                    List<PrototypeManageInfoQueryResp.SampleClothesNodeStateVo> nodeStateVoList = clothesVo.getNodeStates().stream().map(node -> {
                        PrototypeManageInfoQueryResp.SampleClothesNodeStateVo nodeState = new PrototypeManageInfoQueryResp.SampleClothesNodeStateVo();
                        BeanUtils.copyProperties(node, nodeState);
                        return nodeState;
                    }).collect(Collectors.toList());
                    sampleInfo.setNodeStateList(nodeStateVoList);
                    sampleInfos.add(sampleInfo);
                    infoVo.setSampleInfos(sampleInfos);
                }
            }
            //核价单信息
            DesignPricingInfoVo priceInfo = checkPriceMap.get(item);
            if (Objects.nonNull(priceInfo)) {
                CheckPriceBaseInfoInnerVo checkPriceBaseInfo = priceInfo.getCheckPriceBaseInfo();
                EstimateCheckPriceVo estimateCheckPriceVo = priceInfo.getEstimateCheckPriceVo();

                if (Objects.nonNull(checkPriceBaseInfo) && Objects.equals(checkPriceBaseInfo.getState(), CheckPriceStateEnum.HAD_CHECK_PRICE.getCode())) {
                    PrototypeManageInfoQueryResp.PriceOrderInfo priceOrderInfo = new PrototypeManageInfoQueryResp.PriceOrderInfo();
                    priceOrderInfo.setDesignCode(item);
                    priceOrderInfo.setCheckPriceType(2);
                    priceOrderInfo.setCheckPriceName(checkPriceBaseInfo.getPricerName());
                    priceOrderInfo.setCheckPriceTime(checkPriceBaseInfo.getFinishTime());
                    priceOrderInfo.setTotalCost(checkPriceBaseInfo.getTotalCost());
                    infoVo.setPriceOrderInfo(priceOrderInfo);
                }else if (Objects.nonNull(estimateCheckPriceVo) && Objects.equals(estimateCheckPriceVo.getState(), CheckPriceStateEnum.HAD_CHECK_PRICE.getCode())) {
                    PrototypeManageInfoQueryResp.PriceOrderInfo priceOrderInfo = new PrototypeManageInfoQueryResp.PriceOrderInfo();
                    priceOrderInfo.setDesignCode(item);
                    priceOrderInfo.setCheckPriceType(1);
                    priceOrderInfo.setCheckPriceName(estimateCheckPriceVo.getPricerName());
                    priceOrderInfo.setCheckPriceTime(estimateCheckPriceVo.getFinishTime());
                    priceOrderInfo.setTotalCost(estimateCheckPriceVo.getTotalCost());
                    infoVo.setPriceOrderInfo(priceOrderInfo);
                }
            }
            respList.add(infoVo);
        });
        return respList;
    }

    /**
     * 封装-取消bom单req
     * @param prototype  打版单
     * @param cancelReq 取消req
     * @return BomCloseWithPrototypeReq
     */
    public static BomCloseWithPrototypeReq buildCancelBomReq(PrototypeVo prototype, PrototypeCancelReq cancelReq) {
        BomCloseWithPrototypeReq cancelBomReq = new BomCloseWithPrototypeReq();
        cancelBomReq.setDesignCode(prototype.getDesignCode());
        cancelBomReq.setPrototypeId(prototype.getPrototypeId());
        cancelBomReq.setReason(cancelReq.getCancelReason());
        cancelBomReq.setBomCloseSource(BomCloseSourceEnum.PROTOTYPE_CLOSE);
        return cancelBomReq;
    }

    /**
     * 封装-取消采购单req
     * @param prototype 打版单
     * @param cancelReq 取消req
     * @return OtherProcessCancelMaterialListReq
     */
    public static OtherProcessCancelMaterialListReq buildCancelPurchaseReq(PrototypeVo prototype, PrototypeCancelReq cancelReq) {
        OtherProcessCancelMaterialListReq cancelPurchaseReq = new OtherProcessCancelMaterialListReq();
        cancelPurchaseReq.setDesignCode(prototype.getDesignCode());
        cancelPurchaseReq.setCloseMaterialPurchaseSource(CloseMaterialPurchaseSourceEnum.CLOSE_DESIGN_CODE);
        cancelPurchaseReq.setCancelReason(cancelReq.getCancelReason());
        return cancelPurchaseReq;
    }


    public static Prototype buildRedo(PrototypeSampleRedoReq redoReq) {
        return Prototype.builder()
                .prototypeId(redoReq.getPrototypeId())
                .isMakeMore(true)
                .makeMoreLatestTime(LocalDateTime.now())
                .build();
    }

    public static PrototypeHistory buildRedoHistory(UserContent currentUser) {
        return PrototypeHistory.builder()
                .isMakeMore(true)
                .reviserId(currentUser.getCurrentUserId())
                .reviserName(currentUser.getCurrentUserName())
                .revisedTime(LocalDateTime.now())
                .build();
    }
}
