package tech.tiangong.sdp.design.business.bom.notification;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.PrototypeHistory;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.vo.dto.bom.AdjustDesignDemandReq;
import tech.tiangong.sdp.design.vo.dto.bom.BomSubmitMaterialChgNotificationDto;

import java.util.*;
import java.util.stream.Collectors;

/**
 * bom提交物料变更通知 <br>
 * 适用于： <br/>
 * 1. 增删选料，更换规格（也是删除、再增加） <br/>
 * 2. 增删辅料需求。 其中，辅料需求的更换物料，无需通知给齐套。 由另外mq通知到需求服务 <br/>
 * @deprecated 该需求由致景PLM与履约对接, JV不处理
 */
@Deprecated(since = "JV迁移")
@Slf4j
public class BomSubmitMaterialChgNotification implements Notification<BomSubmitMaterialChgNotificationDto> {
    private List<BomSubmitMaterialChgNotificationDto> bomSubmitMaterialChgNotificationDtoList = new LinkedList<>();

    private final MqProducer mqProducer = SpringUtil.getBean(MqProducer.class);

    @Override
    public void add(BomSubmitMaterialChgNotificationDto bomSubmitMaterialChgNotificationDto) {
        if (Objects.nonNull(bomSubmitMaterialChgNotificationDto)) {
            bomSubmitMaterialChgNotificationDtoList.add(bomSubmitMaterialChgNotificationDto);
        }
    }

    @Override
    public void addBatch(List<BomSubmitMaterialChgNotificationDto> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            bomSubmitMaterialChgNotificationDtoList.addAll(list);
        }

    }

    @Override
    public List<BomSubmitMaterialChgNotificationDto> getAll() {
        return bomSubmitMaterialChgNotificationDtoList;
    }

    @Override
    public void send() {

        if (CollectionUtil.isEmpty(bomSubmitMaterialChgNotificationDtoList)) {
            return;
        }

        AdjustDesignDemandReq adjustDesignDemandReq = new AdjustDesignDemandReq();

        Set<AdjustDesignDemandReq.Demand> addDemands = new HashSet<>();
        Set<AdjustDesignDemandReq.Demand> delDemands = new HashSet<>();

        bomSubmitMaterialChgNotificationDtoList.forEach(dto -> {
            //选料
            Set<AdjustDesignDemandReq.Demand> addMaterialsSet = dto.getAddMaterials().stream().map(material -> buildMaterial(material, dto.getBomOrder(), dto.getPrototypeHistory())).collect(Collectors.toSet());
            Set<AdjustDesignDemandReq.Demand> delMaterialsSet = dto.getDelMaterials().stream().map(material -> buildMaterial(material, dto.getBomOrder(), dto.getPrototypeHistory())).collect(Collectors.toSet());

            //需求匹配
            Set<AdjustDesignDemandReq.Demand> addDemandsSet = dto.getAddDemands().stream().map(demand -> buildDemand(demand, dto.getBomOrder())).collect(Collectors.toSet());
            Set<AdjustDesignDemandReq.Demand> delDemandsSet = dto.getDelDemands().stream().map(demand -> buildDemand(demand, dto.getBomOrder())).collect(Collectors.toSet());

            addDemands.addAll(addMaterialsSet);
            addDemands.addAll(addDemandsSet);
            delDemands.addAll(delMaterialsSet);
            delDemands.addAll(delDemandsSet);
        });


        adjustDesignDemandReq.setAddDemands(addDemands);
        adjustDesignDemandReq.setCloseDemands(delDemands);

        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.DESIGN_BOM_SUBMIT_MATERIAL_CHG,
                DesignMqConstant.SDP_DESIGN_BOM_SUBMIT_MATERIAL_CHG_EXCHANGE, DesignMqConstant.SDP_DESIGN_BOM_SUBMIT_MATERIAL_CHG_ROUTING_KEY
                , JSON.toJSONString(adjustDesignDemandReq));
        log.info("bom提交物料变更通知. mq消息：{}", JSON.toJSONString(mqMessageReq));
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

    /**
     * 组装需求
     * @param demandDto
     * @param bomOrder
     * @return
     */
    private AdjustDesignDemandReq.Demand buildDemand(BomSubmitMaterialChgNotificationDto.Demand demandDto, BomOrder bomOrder) {
        AdjustDesignDemandReq.Demand demand = new AdjustDesignDemandReq.Demand();
        demand.setDemandId(demandDto.getDemandId());
        setDefaultParam(demand, bomOrder);
        return demand;
    }

    /**
     * 组装选料
     * @param materialDto
     * @param bomOrder
     * @return
     */
    private AdjustDesignDemandReq.Demand buildMaterial(BomSubmitMaterialChgNotificationDto.Material materialDto, BomOrder bomOrder, PrototypeHistory prototypeHistory) {
        AdjustDesignDemandReq.Demand demand = new AdjustDesignDemandReq.Demand();
        demand.setMaterialSnapshotId(materialDto.getMaterialSnapshotId());
        setDefaultParam(demand, bomOrder);
        // demand.setSampleType(prototypeHistory.getSampleType());
        demand.setIsMakeMore(prototypeHistory.getIsMakeMore());
        return demand;
    }

    private void setDefaultParam(AdjustDesignDemandReq.Demand demand, BomOrder bomOrder) {
        String designCodeNum = ReUtil.getGroup0("(\\d+)", bomOrder.getDesignCode());
        demand.setPrototypeId(Long.valueOf(designCodeNum));
        demand.setPrototypeCode(bomOrder.getDesignCode());
        UserContent userContent = UserContentHolder.get();
        demand.setOperatorId(userContent.getCurrentUserId());
        demand.setOperatorName(userContent.getCurrentUserName());
        //暂定不传，齐套服务自己处理
//        demand.setUrgencyType();
    }


}
