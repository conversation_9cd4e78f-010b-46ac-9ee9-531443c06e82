package tech.tiangong.sdp.design.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tech.tiangong.sdp.design.entity.BomMaterialDemandTransient;
import tech.tiangong.sdp.design.vo.query.bom.BomMaterialDemandTransientQuery;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialDemandTransientVo;

/**
 * bom物料需求_暂存表数据库访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface BomMaterialDemandTransientMapper extends BaseMapper<BomMaterialDemandTransient> {

    /**
     * 分页查询
     *
     * @param page  分页
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<BomMaterialDemandTransientVo> findPage(@Param("page") Page<BomMaterialDemandTransientVo> page, @Param("query") BomMaterialDemandTransientQuery query);

}
