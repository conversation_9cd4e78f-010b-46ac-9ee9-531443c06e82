package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.util.Json;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.converter.BomOrderConverter;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.BomOrderTransient;
import tech.tiangong.sdp.design.entity.CraftDemandInfoTransient;
import tech.tiangong.sdp.design.enums.BomMultiStateEnum;
import tech.tiangong.sdp.design.enums.BomOrderStateEnum;
import tech.tiangong.sdp.design.repository.BomOrderRepository;
import tech.tiangong.sdp.design.repository.BomOrderTransientRepository;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.req.bom.BomCraftSubmitHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomDemandSubmitHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialSubmitHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderUpdateV3Req;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * bom提交处理服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BomSubmitHandleServiceImpl implements BomSubmitHandleService {

    private final BomMaterialSubmitHandleService materialSubmitHandleService;
    private final BomDemandSubmitHandleService demandSubmitHandleService;
    private final BomOrderRepository bomOrderRepository;
    private final DesignLogService designLogService;
    private final BomOrderTransientRepository bomOrderTransientRepository;
    private final BomTransientHandleService bomTransientHandleService;
    private final BomCraftSubmitHandleService craftSubmitHandleService;


    /**
     * 待提交_无暂存 首次提交
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void firstSubmitNoTransient(BomOrderUpdateV3Req req) {
        log.info("===== bom提交-firstSubmitNoTransient-start: bomId:{} =====",req.getBomId());
        Long bomId = req.getBomId();
        BomOrder bomOrder = bomOrderRepository.getById(bomId);

        //1, 物料信息处理
        BomMaterialSubmitHandleReq materialSubmitHandleReq = BomMaterialSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .addBomMaterials(req.getAddBomMaterials())
                .updateBomMaterials(req.getUpdateBomMaterials())
                .delBomMaterialIds(req.getDelBomMaterialIds())
                .build();
        BomCraftSubmitHandleReq materialCraftReq = materialSubmitHandleService.firstSubmitNoTransient(materialSubmitHandleReq);

        //2, 辅料需求信息处理
        BomDemandSubmitHandleReq demandSubmitHandleReq = BomDemandSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .addBomMaterialDemandList(req.getAddBomMaterialDemandList())
                .updateBomMaterialDemandList(req.getUpdateBomMaterialDemandList())
                .delBomMaterialDemandIds(req.getDelBomMaterialDemandIds())
                .build();
        BomCraftSubmitHandleReq demandCraftReq = demandSubmitHandleService.firstSubmitNoTransient(demandSubmitHandleReq);

        //3, 工艺处理
        BomCraftSubmitHandleReq craftSubmitHandleReq = this.buildCraftSubmitHandleReq(bomOrder, null, materialCraftReq, demandCraftReq);
        log.info(" ===== bom提交-firstSubmitNoTransient, 需求-工艺交接入参: {}; bomId:{} =====", Json.serialize(craftSubmitHandleReq), bomOrder.getBomId());
        craftSubmitHandleService.firstSubmitNoTransient(craftSubmitHandleReq);

        //4, 处理Bom提交日志
        designLogService.create(BomOrderConverter.buildBomOrderLog(bomOrder, "提交了 【开发bom表】"));

        //5, 修改bom订单状态为已提交, 非暂存; 逻辑删除bom单暂存记录
        BomOrder updateBomOrder = BomOrder.builder().bomId(bomOrder.getBomId())
                .state(BomOrderStateEnum.SUBMITTED.getCode())
                .transientState(Bool.NO.getCode())
                .submitTime(LocalDateTime.now())
                .quoteDesignCode(StringUtils.isBlank(req.getQuoteDesignCode()) ? null : req.getQuoteDesignCode())
                .build();
        bomOrderRepository.updateById(updateBomOrder);
        // bomOrderTransientRepository.removeByBomId(bomOrder.getBomId());

        log.info("===== bom提交-firstSubmitNoTransient-end: bomId:{} =====",req.getBomId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void firstSubmitWithTransient(BomOrderUpdateV3Req req) {
        log.info("===== bom提交-firstSubmitWithTransient-start: bomId:{} =====",req.getBomId());
        Long bomId = req.getBomId();
        BomOrder bomOrder = bomOrderRepository.getById(bomId);

        //1, 先执行再次暂存: 物料,需求,工艺都再次暂存了
        bomTransientHandleService.reTransient(req);

        //2, 再次暂存后,物料信息处理
        BomMaterialSubmitHandleReq materialSubmitHandleReq = BomMaterialSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .addBomMaterials(req.getAddBomMaterials())
                .updateBomMaterials(req.getUpdateBomMaterials())
                .delBomMaterialIds(req.getDelBomMaterialIds())
                .build();
        BomCraftSubmitHandleReq materialCraftReq = materialSubmitHandleService.firstSubmitWithTransient(materialSubmitHandleReq);

        //3, 再次暂存后,辅料需求信息处理
        BomDemandSubmitHandleReq demandSubmitHandleReq = BomDemandSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .addBomMaterialDemandList(req.getAddBomMaterialDemandList())
                .updateBomMaterialDemandList(req.getUpdateBomMaterialDemandList())
                .delBomMaterialDemandIds(req.getDelBomMaterialDemandIds())
                .build();
        BomCraftSubmitHandleReq demandCraftReq = demandSubmitHandleService.firstSubmitWithTransient(demandSubmitHandleReq);

        //4, 工艺处理(模拟为无暂存首次提交的入参)
        BomCraftSubmitHandleReq craftSubmitHandleReq = this.buildCraftSubmitHandleReq(bomOrder, null, materialCraftReq, demandCraftReq);
        log.info(" ===== bom提交-firstSubmitWithTransient, 需求-工艺交接入参: {}; bomId:{} =====", Json.serialize(craftSubmitHandleReq), bomOrder.getBomId());
        craftSubmitHandleService.firstSubmitNoTransient(craftSubmitHandleReq);

        //5, 处理Bom提交日志
        designLogService.create(BomOrderConverter.buildBomOrderLog(bomOrder, "提交了 【开发bom表】"));

        //6, 修改bom订单状态为已提交, 非暂存; 逻辑删除bom单暂存记录
        BomOrder updateBomOrder = BomOrder.builder().bomId(bomOrder.getBomId())
                .state(BomOrderStateEnum.SUBMITTED.getCode())
                .transientState(Bool.NO.getCode())
                .submitTime(LocalDateTime.now())
                .quoteDesignCode(StringUtils.isBlank(req.getQuoteDesignCode()) ? null : req.getQuoteDesignCode())
                .build();
        bomOrderRepository.updateById(updateBomOrder);
        // bomOrderTransientRepository.removeByBomId(bomOrder.getBomId());
        log.info("===== bom提交-firstSubmitWithTransient-end: bomId:{} =====",req.getBomId());
    }

    /**
     * 已提交/已核算/找料中_无暂存 再次提交(升版本)
     * @param req 入参
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomOrder reSubmitNoTransient(BomOrderUpdateV3Req req, BomMultiStateEnum bomMultiStateEnum) {
        log.info("===== bom提交-reSubmitNoTransient-start: bomId:{} =====",req.getBomId());
        BomOrder oldBomOrder = bomOrderRepository.getById(req.getBomId());
        //1.生成新的版本Bom
        BomOrder newBomOrder = BomOrderConverter.newBomOrderSubmit(oldBomOrder, req);
        bomOrderRepository.save(newBomOrder);

        //2, 物料信息处理
        BomMaterialSubmitHandleReq materialSubmitHandleReq = BomMaterialSubmitHandleReq.builder()
                .bomOrder(oldBomOrder)
                .newBomOrder(newBomOrder)
                .addBomMaterials(req.getAddBomMaterials())
                .updateBomMaterials(req.getUpdateBomMaterials())
                .delBomMaterialIds(req.getDelBomMaterialIds())
                .build();
        BomCraftSubmitHandleReq materialCraftReq = materialSubmitHandleService.reSubmitNoTransient(materialSubmitHandleReq);

        //3, 辅料需求信息处理
        BomDemandSubmitHandleReq demandSubmitHandleReq = BomDemandSubmitHandleReq.builder()
                .bomOrder(oldBomOrder)
                .newBomOrder(newBomOrder)
                .addBomMaterialDemandList(req.getAddBomMaterialDemandList())
                .updateBomMaterialDemandList(req.getUpdateBomMaterialDemandList())
                .delBomMaterialDemandIds(req.getDelBomMaterialDemandIds())
                .build();
        BomCraftSubmitHandleReq demandCraftReq = demandSubmitHandleService.reSubmitNoTransient(demandSubmitHandleReq);

        //3, 工艺处理
        BomCraftSubmitHandleReq craftSubmitHandleReq = this.buildCraftSubmitHandleReq(oldBomOrder, newBomOrder.getBomId(), materialCraftReq, demandCraftReq);
        log.info(" ===== bom提交-reSubmitNoTransient, 需求-工艺交接入参: {}; bomId:{} =====", Json.serialize(craftSubmitHandleReq), oldBomOrder.getBomId());
        craftSubmitHandleService.reSubmitNoTransient(craftSubmitHandleReq);

        //4, 处理Bom提交日志
        designLogService.create(BomOrderConverter.buildBomOrderLog(newBomOrder, "更新了 【开发bom表】"));
        log.info("===== bom提交-reSubmitNoTransient-end: bomId:{} =====",req.getBomId());

        return newBomOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomOrder reSubmitWithTransient(BomOrderUpdateV3Req req, BomMultiStateEnum bomMultiStateEnum) {
        log.info("===== bom提交-reSubmitWithTransient-start: bomId:{} =====",req.getBomId());

        BomOrder oldBomOrder = bomOrderRepository.getById(req.getBomId());

        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(req.getBomId());
        //直接使用暂存bomId作为新bom的id
        Long newBomId = transientBom.getBomTransientId();

        //1.生成新的版本Bom
        BomOrder newBomOrder = BomOrderConverter.newBomOrderSubmit(oldBomOrder, req);
        newBomOrder.setBomId(newBomId);
        bomOrderRepository.save(newBomOrder);

        //2, 先执行再次暂存: 物料,需求,工艺都再次暂存了
        bomTransientHandleService.reTransient4Submitted(req, bomMultiStateEnum);

        //3, 再次暂存后,物料信息处理
        BomMaterialSubmitHandleReq materialSubmitHandleReq = BomMaterialSubmitHandleReq.builder()
                .bomOrder(oldBomOrder)
                .newBomOrder(newBomOrder)
                .addBomMaterials(req.getAddBomMaterials())
                .updateBomMaterials(req.getUpdateBomMaterials())
                .delBomMaterialIds(req.getDelBomMaterialIds())
                .build();
        BomCraftSubmitHandleReq materialCraftReq = materialSubmitHandleService.reSubmitWithTransient(materialSubmitHandleReq);

        //4, 再次暂存后,辅料需求信息处理
        BomDemandSubmitHandleReq demandSubmitHandleReq = BomDemandSubmitHandleReq.builder()
                .bomOrder(oldBomOrder)
                .newBomOrder(newBomOrder)
                .addBomMaterialDemandList(req.getAddBomMaterialDemandList())
                .updateBomMaterialDemandList(req.getUpdateBomMaterialDemandList())
                .delBomMaterialDemandIds(req.getDelBomMaterialDemandIds())
                .build();
        BomCraftSubmitHandleReq demandCraftReq = demandSubmitHandleService.reSubmitWithTransient(demandSubmitHandleReq);

        //5, 工艺处理(模拟为无暂存再次提交的入参)
        BomCraftSubmitHandleReq craftSubmitHandleReq = this.buildCraftSubmitHandleReq(oldBomOrder, newBomId, materialCraftReq, demandCraftReq);
        log.info(" ===== bom提交-reSubmitWithTransient, 需求-工艺交接入参: {}; bomId:{} =====", Json.serialize(craftSubmitHandleReq), oldBomOrder.getBomId());
        craftSubmitHandleService.reSubmitNoTransient(craftSubmitHandleReq);


        //6, 处理Bom提交日志
        designLogService.create(BomOrderConverter.buildBomOrderLog(newBomOrder, "更新了 【开发bom表】"));
        log.info("===== bom提交-reSubmitNoTransient-end: bomId:{} =====",req.getBomId());

        //7, 旧bom暂存状态更新与删除暂存表关联
        bomOrderRepository.updateNoTransient(oldBomOrder.getBomId());
        bomOrderTransientRepository.removeByBomId(oldBomOrder.getBomId());
        log.info("===== bom提交-reSubmitWithTransient-end: bomId:{} =====",req.getBomId());

        return newBomOrder;
    }


    private BomCraftSubmitHandleReq buildCraftSubmitHandleReq(BomOrder bomOrder,
                                                              Long newBomOrderId,
                                                              BomCraftSubmitHandleReq materialCraftReq,
                                                              BomCraftSubmitHandleReq demandCraftReq) {
        return BomCraftSubmitHandleReq.builder()
                .bomOrder(bomOrder)
                .newBomOrderId(newBomOrderId)
                .addCraftDemandList(ListUtils.union(materialCraftReq.getAddCraftDemandList(), demandCraftReq.getAddCraftDemandList()))
                .delCraftDemandIds(Sets.union(materialCraftReq.getDelCraftDemandIds(), demandCraftReq.getDelCraftDemandIds()))
                .oldNewMaterialIdMap(this.mergeIdMap(materialCraftReq.getOldNewMaterialIdMap(), demandCraftReq.getOldNewMaterialIdMap()))
                .oldTransientCraftMap(this.mergeCraftMap(materialCraftReq.getOldTransientCraftMap(), demandCraftReq.getOldTransientCraftMap()))
                .build();
    }

    private Map<Long, CraftDemandInfoTransient> mergeCraftMap(Map<Long, CraftDemandInfoTransient> map1,
                                                              Map<Long, CraftDemandInfoTransient> map2) {
        Map<Long, CraftDemandInfoTransient> mergeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(map1)) {
            mergeMap.putAll(map1);
        }
        if (CollUtil.isNotEmpty(map2)) {
            mergeMap.putAll(map2);
        }
        return mergeMap;
    }
    private Map<Long, Long> mergeIdMap(Map<Long, Long> map1,
                                       Map<Long, Long> map2) {
        Map<Long, Long> mergeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(map1)) {
            mergeMap.putAll(map1);
        }
        if (CollUtil.isNotEmpty(map2)) {
            mergeMap.putAll(map2);
        }
        return mergeMap;
    }



}
