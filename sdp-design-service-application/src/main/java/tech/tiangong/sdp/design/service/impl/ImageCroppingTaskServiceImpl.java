package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import tech.tiangong.sdp.design.entity.ImageCroppingTask;
import tech.tiangong.sdp.design.entity.VisualImagePackage;
import tech.tiangong.sdp.design.enums.visual.CroppedTaskStatusEnum;
import tech.tiangong.sdp.design.helper.ImageCroppingTaskHelper;
import tech.tiangong.sdp.design.mq.rocketmq.entity.MurmurationSimpleTaskResult;
import tech.tiangong.sdp.design.mq.rocketmq.entity.MurmurationTaskStatusEnum;
import tech.tiangong.sdp.design.mq.rocketmq.entity.TaskStatusNotification;
import tech.tiangong.sdp.design.repository.ImageCroppingTaskRepository;
import tech.tiangong.sdp.design.repository.VisualImagePackageRepository;
import tech.tiangong.sdp.design.service.ImageCroppingTaskService;
import tech.tiangong.sdp.design.service.VisualImagePackageService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 智能裁剪任务处理
 *
 * <AUTHOR>
 * @date 2025/8/30
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class ImageCroppingTaskServiceImpl implements ImageCroppingTaskService {

    private final ImageCroppingTaskRepository imageCroppingTaskRepository;
    private final ImageCroppingTaskHelper imageCroppingTaskHelper;
    private final TransactionTemplate transactionTemplate; // 注入编程式事务模板

    private VisualImagePackageService visualImagePackageService;
    private final VisualImagePackageRepository visualImagePackageRepository;


    @Autowired
    public void setVisualImagePackageService(@Lazy VisualImagePackageService visualImagePackageService) {
        this.visualImagePackageService = visualImagePackageService;
    }

    @Override
    public void imageCroppingTaskHandler(Long packageId) {
        List<ImageCroppingTask> imageCroppingTasks = imageCroppingTaskRepository.listByPackageIdStatus(packageId,
                CroppedTaskStatusEnum.PENDING_SUBMISSION.getCode());
        if (CollectionUtil.isEmpty(imageCroppingTasks)) {
            return;
        }

        List<String> errorMessage = new ArrayList<>();
        List<ImageCroppingTask> successfulTasks = new ArrayList<>();
        List<ImageCroppingTask> failedTasks = new ArrayList<>();

        // 第一阶段：尝试提交所有任务（不在事务中）
        for (ImageCroppingTask task : imageCroppingTasks) {
            try {
                Long taskId = imageCroppingTaskHelper.submitImageCroppingTask(task);
                task.setTaskId(taskId);
                successfulTasks.add(task);
            } catch (Exception e) {
                log.error("提交裁剪任务失败: TaskID={}", task.getImageCroppingTaskId(), e);
                task.setErrorMessage(e.getMessage());
                failedTasks.add(task);
                errorMessage.add("提交裁剪任务失败: TaskID" + task.getImageCroppingTaskId() + "--异常" + e.getMessage());
            }
        }

        // 第二阶段：批量更新任务状态（使用事务确保一致性）
        transactionTemplate.executeWithoutResult(status -> {
            // 更新成功任务的状态
            for (ImageCroppingTask task : successfulTasks) {
                task.setTaskStatus(CroppedTaskStatusEnum.SUBMITTED.getCode());
                imageCroppingTaskRepository.updateById(task);
            }

            // 更新失败任务的状态
            for (ImageCroppingTask task : failedTasks) {
                task.setTaskStatus(CroppedTaskStatusEnum.FAILED.getCode());
                imageCroppingTaskRepository.updateById(task);
            }

            // 更新包状态
            VisualImagePackage visualImagePackage = visualImagePackageRepository.getById(packageId);
            if (Objects.isNull(visualImagePackage)) {
                log.warn("视觉图像包不存在: packageId={}", packageId);
                return;
            }

            if (!errorMessage.isEmpty()) {
                log.warn("部分裁剪任务处理失败: {}", JSON.toJSONString(errorMessage));
                visualImagePackageRepository.updateCroppingTaskStatus(visualImagePackage.getPackageId(),
                        CroppedTaskStatusEnum.FAILED.getCode());
            } else {
                visualImagePackageRepository.updateCroppingTaskStatus(visualImagePackage.getPackageId(),
                        CroppedTaskStatusEnum.SUBMITTED.getCode());
            }
        });

    }


    @Transactional(rollbackFor =  Exception.class)
    @Override
    public void handleCroppingTaskCallback(TaskStatusNotification notification) {
        String bizId = notification.getBizId();
        ImageCroppingTask imageCroppingTask = imageCroppingTaskRepository.getById(bizId);
        if(Objects.isNull(imageCroppingTask)){
            return;
        }
        // 处理成功的任务结果
        if (notification.getStatus() == MurmurationTaskStatusEnum.COMPLETED &&
                notification.getResults() != null && !notification.getResults().isEmpty()) {

            MurmurationSimpleTaskResult first = notification.getResults().getFirst();
            imageCroppingTask.setResultImageUrl(first.getUrl());
            imageCroppingTask.setCompletedTime(LocalDateTime.now());
            imageCroppingTask.setTaskStatus(CroppedTaskStatusEnum.SUCCESS.getCode());
            imageCroppingTaskRepository.updateById(imageCroppingTask);
        }

        if(notification.getStatus() == MurmurationTaskStatusEnum.FAILED){
            imageCroppingTask.setErrorMessage(JSON.toJSONString(notification));
            imageCroppingTask.setTaskStatus(CroppedTaskStatusEnum.FAILED.getCode());
            imageCroppingTaskRepository.updateById(imageCroppingTask);
            log.info("智能裁图失败的错误信息={}",JSON.toJSONString(notification));

            if(imageCroppingTask.getRetryCount() < imageCroppingTask.getMaxRetries()){
                //失败重试
                Long task = imageCroppingTaskHelper.submitImageCroppingTask(imageCroppingTask);
                imageCroppingTask.setTaskId(task);
                imageCroppingTask.setRetryCount(imageCroppingTask.getRetryCount() + 1);
                imageCroppingTask.setTaskStatus(CroppedTaskStatusEnum.SUBMITTED.getCode());
                imageCroppingTaskRepository.updateById(imageCroppingTask);
            }else {
                visualImagePackageRepository.updateCroppingTaskStatus(imageCroppingTask.getPackageId(),CroppedTaskStatusEnum.FAILED.getCode());
            }

        }


        List<ImageCroppingTask> imageCroppingTasks = imageCroppingTaskRepository.listByPackageIdStatus(imageCroppingTask.getPackageId(), null);
        boolean allMatch = imageCroppingTasks.stream().allMatch(a -> a.getTaskStatus().equals(CroppedTaskStatusEnum.SUCCESS.getCode()));
        log.info("{}下的裁剪任务都已经成功完成,图包的ID是{}",imageCroppingTask.getStyleCode(),imageCroppingTask.getPackageId());
        if(allMatch){
            visualImagePackageService.updateVisualImagePackageWithCroppedImages(imageCroppingTasks);
        }
    }
}
