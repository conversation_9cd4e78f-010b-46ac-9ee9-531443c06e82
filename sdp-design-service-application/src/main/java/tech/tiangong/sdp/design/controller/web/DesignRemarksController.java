package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.DesignRemarksService;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksBatchBizListReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksListReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;
import tech.tiangong.sdp.design.vo.resp.remark.DesignRemarksBatchListReq;
import tech.tiangong.sdp.design.vo.resp.remark.DesignRemarksVO;

import java.util.List;
import java.util.Map;

/**
*
* 备注信息-web
* <br>CreateDate August 10,2021
* <AUTHOR>
* @since 1.0
*/
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/design/remarks")
public class DesignRemarksController extends BaseController {

    private final DesignRemarksService designRemarksService;

    /**
    * 新建
    *
    * @param req 设计打版备注信息对象
    * @return 设计打版备注信息实体
    */
    @PostMapping("/save")
    @NoRepeatSubmitLock
    public DataResponse<DesignRemarksVO> create(@RequestBody @Validated DesignRemarksReq req) {

        return DataResponse.ok(designRemarksService.create(req));
    }

    /**
     * 查询-根据设计款号查询
     *
     * @param req 设计打版备注信息对象
     * @return 设计打版备注信息实体
     */
    @PostMapping("/list")
    public DataResponse<List<DesignRemarksVO>> dataList(@RequestBody @Validated DesignRemarksListReq req) {
        return DataResponse.ok(designRemarksService.dataList(req));
    }

    /**
     * 批量查询-根据设计款号批量查询
     *
     * @param req 设计打版备注信息对象
     * @return 设计打版备注信息实体
     */
    @PostMapping("/batch/list")
    public DataResponse<Map<String, List<DesignRemarksVO>>> batchDataList(@RequestBody @Validated DesignRemarksBatchListReq req) {
        Map<String, List<DesignRemarksVO>> longListMap = designRemarksService.batchDataList(req);
        return DataResponse.ok(longListMap);
    }

    /**
     * 批量查询-根据业务主键进行查询
     *  调用页面: 灵感任务, 数码印花款, 采购齐套管理, 采购申请管理
     *
     * @param req 入参
     * @return 设计打版备注信息实体
     */
    @PostMapping("/batch/biz/list")
    public DataResponse<Map<Long, List<DesignRemarksVO>>> batchBizDataList(@RequestBody @Validated DesignRemarksBatchBizListReq req) {
        Map<Long, List<DesignRemarksVO>> longListMap = designRemarksService.batchBizDataList(req);
        return DataResponse.ok(longListMap);
    }


}
