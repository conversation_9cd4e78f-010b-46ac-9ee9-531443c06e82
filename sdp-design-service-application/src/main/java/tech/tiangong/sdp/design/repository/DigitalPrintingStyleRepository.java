package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.DigitalPrintingStyle;
import tech.tiangong.sdp.design.enums.DigitalPrintingPushStatusEnum;
import tech.tiangong.sdp.design.mapper.DigitalPrintingStyleMapper;
import tech.tiangong.sdp.design.vo.query.digital.DigitalPrintingQuery;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingQueryVo;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 数码印花_SPU表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class DigitalPrintingStyleRepository extends BaseRepository<DigitalPrintingStyleMapper, DigitalPrintingStyle> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<DigitalPrintingQueryVo> findPage(DigitalPrintingQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public List<DigitalPrintingStyle> listByStyleCodes(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(DigitalPrintingStyle::getStyleCode, styleCodeList)
                .eq(DigitalPrintingStyle::getIsDeleted, Bool.NO.getCode())
                .list();
    }

    public DigitalPrintingStyle getByStyleCode(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            return null;
        }
        return lambdaQuery()
                .eq(DigitalPrintingStyle::getStyleCode, styleCode)
                .last("limit 1").one();
    }

    public String selectLatestStyleCode(String spuPrefix) {
        return baseMapper.selectLatestStyleCode(spuPrefix);
    }

    public DigitalPrintingStyle getBySourceSpu(String sourceSpu) {
        if (StrUtil.isBlank(sourceSpu)) {
            return null;
        }
        return lambdaQuery()
                .eq(DigitalPrintingStyle::getSourceSpu, sourceSpu)
                .last("limit 1").one();
    }

    public DigitalPrintingStyle getBySourceBizId(Long sourceBizId) {
        if (Objects.isNull(sourceBizId)) {
            return null;
        }
        return lambdaQuery()
                .eq(DigitalPrintingStyle::getSourceBizId, sourceBizId)
                .last("limit 1").one();

    }

    public void fixFailState(Set<String> spuSet) {
        if (CollUtil.isEmpty(spuSet)) {
            return;
        }
        lambdaUpdate()
                .set(DigitalPrintingStyle::getSpuPushStatus, DigitalPrintingPushStatusEnum.SUCCESS.getCode())
                .in(DigitalPrintingStyle::getStyleCode, spuSet)
                .update();
    }

    public void rePushUpdate(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            return;
        }
        lambdaUpdate()
                .eq(DigitalPrintingStyle::getStyleCode, styleCode)
                .eq(DigitalPrintingStyle::getSpuPushStatus, DigitalPrintingPushStatusEnum.FAIL.getCode())
                .set(DigitalPrintingStyle::getSpuPushStatus, DigitalPrintingPushStatusEnum.PUSHING.getCode())
                .update();
    }
}
