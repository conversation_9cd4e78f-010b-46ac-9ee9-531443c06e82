package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.DigitalPrintingStyleDetail;
import tech.tiangong.sdp.design.mapper.DigitalPrintingStyleDetailMapper;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 数码印花_SPU详情表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class DigitalPrintingStyleDetailRepository extends BaseRepository<DigitalPrintingStyleDetailMapper, DigitalPrintingStyleDetail> {


    public DigitalPrintingStyleDetail getByStyleId(Long printingStyleId) {
        if (Objects.isNull(printingStyleId)) {
            return null;
        }
        return lambdaQuery().eq(DigitalPrintingStyleDetail::getPrintingStyleId, printingStyleId).one();
    }

    public List<DigitalPrintingStyleDetail> listByStyleIds(List<Long> styleIdList) {
        if (CollUtil.isEmpty(styleIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(DigitalPrintingStyleDetail::getPrintingStyleId, styleIdList)
                .eq(DigitalPrintingStyleDetail::getIsDeleted, Bool.NO.getCode())
                .list();

    }
}
