package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.PrototypeOperateService;
import tech.tiangong.sdp.design.service.PrototypeService;
import tech.tiangong.sdp.design.vo.req.prototype.BatchPrintReq;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeOperateReq;
import tech.tiangong.sdp.design.vo.resp.prototype.*;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * SKC管理-web
 *
 * <AUTHOR>
 * @since 2021-08-09 14:43:19
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/prototype")
public class PrototypeController extends BaseController {
    private final PrototypeService prototypeService;
    private final PrototypeOperateService prototypeOperateService;

    /**
     * 获取复色款号的详情
     *
     * @param designCode 设计款号
     * @return 响应结果
     */
    @GetMapping("/make-same-info/{designCode}")
    public DataResponse<PrototypeMakeSameVo> getMakeSameInfoByDesignCode(@PathVariable(value = "designCode") String designCode) {
        return DataResponse.ok(prototypeService.getMakeSameInfoByDesignCode(designCode));
    }

    /**
     * 根据当前设计款号查找可用的复色款号
     * @param designCode 设计款号
     * @return 响应结果
     */
    @GetMapping("make-same/query-design-codes/{designCode}")
    public DataResponse<List<MakeSameDesignCodeVo>> queryDesignCodesByDesignCode(@PathVariable(value = "designCode")
                                        @NotBlank(message = "设计款号不能为空") String designCode) {
        return DataResponse.ok(prototypeService.queryMakeSameByDesignCode(designCode));
    }


    /**
     * 获取已提交版本的详情
     *
     * @param designCode 设计款号
     * @return 响应结果
     */
    @GetMapping("saved-info/{designCode}")
    public DataResponse<PrototypeVo> getSavedInfoByDesignCode(@PathVariable(value = "designCode") String designCode) {
        return DataResponse.ok(prototypeService.getSavedInfoByDesignCode(designCode));
    }

    /**
     * 根据版单id查询已拆版详情
     *
     * @param prototypeId 版单id
     * @return 响应结果
     */
    @GetMapping("detail-version/{prototypeId}")
    public DataResponse<PrototypeVo> getDetailByVersion(@PathVariable(value = "prototypeId") Long prototypeId) {
        return DataResponse.ok(prototypeService.getDoneDetailById(prototypeId));
    }

    /**
     * 根据designCode查询所有已提交的版单基础信息
     *
     * @param designCode 设计款号
     * @return 响应结果
     */
    @GetMapping("versions/{designCode}")
    public DataResponse<List<PrototypeVo>> getVersionList(@PathVariable(value = "designCode") String designCode) {
        return DataResponse.ok(prototypeService.getVersionList(designCode));
    }

    /**
     * 提交
     *
     * @param req 请求参数对象
     * @return skc下最新版本的bomId
     */
    @PutMapping("/save")
    public DataResponse<PrototypeSubmitVo> save(@RequestBody @Validated PrototypeOperateReq req) {
        return DataResponse.ok(prototypeOperateService.save(req));
    }


    /**
     * 批量查询设计版单打印信息
     *
     * @param req 入参
     * @return 版单打印信息集合
     */
    @PostMapping("/print-batch")
    public DataResponse<List<PrototypePrintInfoVo>> getPrintInfoByDesignCode(@RequestBody @Validated BatchPrintReq req) {
        return DataResponse.ok(prototypeService.batchPrintInfo(req));
    }

    // /**
    //  * 改款关联skc信息查询-查skc下最新已提交的版单信息
    //  *
    //  * @param designCode 设计款号
    //  * @return 改款关联skc信息
    //  */
    // @GetMapping("/skc-change-info/{designCode}")
    // public DataResponse<PrototypeChangeInfoVo> getSkcChangeInfo(@PathVariable(value = "designCode") String designCode) {
    //     return DataResponse.ok(prototypeService.getSkcChangeInfo(designCode));
    // }


}