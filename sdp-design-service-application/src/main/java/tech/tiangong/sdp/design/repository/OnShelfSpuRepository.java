package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.OnShelfSpu;
import tech.tiangong.sdp.design.mapper.OnShelfSpuMapper;

import java.util.Collections;
import java.util.List;

/**
 * 上架spu信息表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class OnShelfSpuRepository extends BaseRepository<OnShelfSpuMapper, OnShelfSpu> {

    public OnShelfSpu getBySpu(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            return null;
        }
        return lambdaQuery()
                .eq(OnShelfSpu::getStyleCode, styleCode)
                .last("limit 1")
                .one();
    }

    public List<OnShelfSpu> listByStyleCodeList(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(OnShelfSpu::getStyleCode, styleCodeList)
                .list();
    }
}
