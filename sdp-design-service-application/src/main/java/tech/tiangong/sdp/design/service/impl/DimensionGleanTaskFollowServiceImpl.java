package tech.tiangong.sdp.design.service.impl;

import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.converter.DimensionGleanTaskFollowConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DimensionGleanPurchaseStateEnum;
import tech.tiangong.sdp.design.enums.DimensionGleanTaskStateEnum;
import tech.tiangong.sdp.design.enums.PurchaseStateEnum;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.DimensionGleanTaskFollowService;
import tech.tiangong.sdp.design.vo.dimensiongleantask.DimensionGleanTaskExportQuery;
import tech.tiangong.sdp.design.vo.dimensiongleantask.DimensionGleanTaskPageQuery;
import tech.tiangong.sdp.design.vo.dto.dimensiongleantask.DimensionGleanTaskStateCountDto;
import tech.tiangong.sdp.design.vo.req.dimensionglean.SyncDimensionGleanPurchaseReq;
import tech.tiangong.sdp.design.vo.req.dimensionglean.SyncDimensionGleanReq;
import tech.tiangong.sdp.design.vo.resp.dimensiongleantask.DimensionGleanTaskExcelResp;
import tech.tiangong.sdp.design.vo.resp.dimensiongleantask.DimensionGleanTaskListVo;
import tech.tiangong.sdp.design.vo.resp.dimensiongleantask.DimensionGleanTaskStateCountVo;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class DimensionGleanTaskFollowServiceImpl implements DimensionGleanTaskFollowService {
    private final DimensionGleanTaskFollowRepository dimensionGleanTaskFollowRepository;
    private final DimensionGleanTaskFollowConverter dimensionGleanTaskFollowConverter;
    private final PrototypeRepository prototypeRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;

    public List<DimensionGleanTaskStateCountVo> countByState(){
        List<DimensionGleanTaskStateCountDto> result = dimensionGleanTaskFollowRepository.countByGleanState();
        Map<Integer,DimensionGleanTaskStateCountDto> resultMap = new HashMap<>();
        if(result!=null){
            resultMap.putAll(result.stream().collect(Collectors.toMap(v->v.getGleanState(), v->v,(k1, k2)->k2)));
        }
        return Arrays.stream(DimensionGleanTaskStateEnum.values())
                .filter(v->!v.equals(DimensionGleanTaskStateEnum.UNKNOWN))
                .map(v->{
                    DimensionGleanTaskStateCountVo vo = new DimensionGleanTaskStateCountVo();
                    vo.setGleanState(v.getCode());
                    vo.setGleanStateDesc(v.getDesc());
                    DimensionGleanTaskStateCountDto dto = resultMap.get(v.getCode());
                    vo.setCount(dto==null ? 0 : dto.getCount());
                    return vo;
        }).collect(Collectors.toList());
    }

    public PageRespVo<DimensionGleanTaskListVo> queryByPage(DimensionGleanTaskPageQuery query){
        IPage<DimensionGleanTaskListVo> page = dimensionGleanTaskFollowRepository.queryByPage(query);
        dimensionGleanTaskFollowConverter.fillDimensionGleanTaskListVo(page.getRecords());
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    public List<DimensionGleanTaskExcelResp> exportExcel(DimensionGleanTaskExportQuery query){
        query.setPageNum(1);
        query.setPageSize(100000);
        List<DimensionGleanTaskListVo> resp = dimensionGleanTaskFollowRepository.queryByPage(query).getRecords();
        return dimensionGleanTaskFollowConverter.trans2DimensionGleanTaskExcelResp(resp);
    }

    public void syncDimensionGleanTask(SyncDimensionGleanReq req){
        List<DimensionGleanTaskFollow> gleanTaskSkuList = new ArrayList<>();
        List<SyncDimensionGleanReq.SkuInfo> skuInfos = req.getSkuInfos();
        if(!CollectionUtils.isEmpty(skuInfos)){
            String designPicture = null;
            Prototype prototype = prototypeRepository.getByDesignCode(req.getCustomerStyleCode());
            if(prototype!=null) {
                PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId());
                designPicture = prototypeDetail!=null ? prototypeDetail.getDesignPicture() : null;
            }
            for(SyncDimensionGleanReq.SkuInfo skuInfo : skuInfos) {
                DimensionGleanTaskFollow dimensionGleanTaskFollow = dimensionGleanTaskFollowRepository.getByGleanTaskAndSku(req.getGleanId(),skuInfo.getSkuCode());
                if (dimensionGleanTaskFollow == null) {
                    dimensionGleanTaskFollow = new DimensionGleanTaskFollow();
                    dimensionGleanTaskFollow.setId(IdPool.getId());
                }
                if(req.getGleanId()!=null){
                    dimensionGleanTaskFollow.setGleanId(req.getGleanId());
                }
                if(StringUtils.isNotBlank(req.getGleanCode())){
                    dimensionGleanTaskFollow.setGleanCode(req.getGleanCode());
                }
                if(StringUtils.isNotBlank(req.getGetName())){
                    dimensionGleanTaskFollow.setTaskTaker(req.getGetName());
                }
                if(StringUtils.isNotBlank(req.getCommodityCode())){
                    dimensionGleanTaskFollow.setCommodityCode(req.getCommodityCode());
                }
                if(StringUtils.isNotBlank(req.getDesignerName())){
                    dimensionGleanTaskFollow.setDesignerName(req.getDesignerName());
                }
                if(StringUtils.isNotBlank(req.getCustomerStyleCode())){
                    dimensionGleanTaskFollow.setCustomerStyleCode(req.getCustomerStyleCode());
                }

                if(StringUtils.isNotBlank(skuInfo.getSkuCode())){
                    dimensionGleanTaskFollow.setSkuCode(skuInfo.getSkuCode());
                }
                if(StringUtils.isNotBlank(skuInfo.getColorCode())){
                    dimensionGleanTaskFollow.setColorCode(skuInfo.getColorCode());
                }
                if(skuInfo.getTaskGleanState()!=null){
                    dimensionGleanTaskFollow.setGleanState(skuInfo.getTaskGleanState());
                }
                if(skuInfo.getTaskFinishTime()!=null && dimensionGleanTaskFollow.getTaskFinishTime()==null){
                    dimensionGleanTaskFollow.setTaskFinishTime(skuInfo.getTaskFinishTime());
                }
                if(skuInfo.getTaskCreatedTime()!=null){
                    dimensionGleanTaskFollow.setTaskCreatedTime(skuInfo.getTaskCreatedTime());
                }
                dimensionGleanTaskFollow.setDesignPicture(designPicture);
                gleanTaskSkuList.add(dimensionGleanTaskFollow);
            }
        }
        if(!CollectionUtils.isEmpty(gleanTaskSkuList)){
            dimensionGleanTaskFollowRepository.saveOrUpdateBatch(gleanTaskSkuList);
        }

    }

    public void syncDimensionGleanPurchase(SyncDimensionGleanPurchaseReq req){
        DimensionGleanTaskFollow dimensionGleanTaskFollow = dimensionGleanTaskFollowRepository.getByGleanTaskCodeAndSku(req.getGleanCode(),req.getSkuCode());
        if(dimensionGleanTaskFollow == null){
            dimensionGleanTaskFollow = new DimensionGleanTaskFollow();
            dimensionGleanTaskFollow.setId(IdPool.getId());
            dimensionGleanTaskFollow.setGleanCode(req.getGleanCode());
            dimensionGleanTaskFollow.setSkuCode(req.getSkuCode());
        }else if(StringUtils.isNotBlank(dimensionGleanTaskFollow.getPurchaseCode())
            && !dimensionGleanTaskFollow.getPurchaseCode().equals(req.getPurchaseCode())){
            DimensionGleanTaskFollow newDimensionGleanTaskFollow = new DimensionGleanTaskFollow();
            BeanUtils.copyProperties(dimensionGleanTaskFollow, newDimensionGleanTaskFollow,"id");
            newDimensionGleanTaskFollow.setId(IdPool.getId());
            dimensionGleanTaskFollow = newDimensionGleanTaskFollow;
        }
        if(req.getPurchaseId()!=null){
            dimensionGleanTaskFollow.setPurchaseId(req.getPurchaseId());
        }
        if(StringUtils.isNotBlank(req.getPurchaseCode())) {
            dimensionGleanTaskFollow.setPurchaseCode(req.getPurchaseCode());
        }
        if(req.getPurchaseState()!=null){
            DimensionGleanPurchaseStateEnum dimensionGleanPurchaseState = DimensionGleanPurchaseStateEnum.findByCode(req.getPurchaseState());
            PurchaseStateEnum purchaseStateEnum = null;
            switch (dimensionGleanPurchaseState){
                case PENDING_BILL: purchaseStateEnum = PurchaseStateEnum.PENDING_BILL;break;
                case PENDING_PROCESS: purchaseStateEnum = PurchaseStateEnum.PENDING_BILL;break;
                case SIGNED: purchaseStateEnum = PurchaseStateEnum.FINISHED;break;
                case CANCELING:purchaseStateEnum = PurchaseStateEnum.PENDING_BILL;break;
                case CLOSED:purchaseStateEnum = PurchaseStateEnum.CLOSED;break;
            }
            if(purchaseStateEnum!=null){
                dimensionGleanTaskFollow.setPurchaseState(purchaseStateEnum.getCode());
            }
        }
        if(req.getCuttingPurchasePrice()!=null){
            dimensionGleanTaskFollow.setCuttingPurchasePrice(req.getCuttingPurchasePrice());
        }
        if(req.getPurchaseRevisedTime()!=null){
            dimensionGleanTaskFollow.setPurchaseRevisedTime(req.getPurchaseRevisedTime());
        }
        if(req.getPurchaseCreateTime()!=null){
            dimensionGleanTaskFollow.setPurchaseCreateTime(req.getPurchaseCreateTime());
        }
        if(req.getCuttingFee()!=null){
            dimensionGleanTaskFollow.setCuttingFee(req.getCuttingFee());
        }
        if(req.getScatteredCuttingPrice()!=null){
            dimensionGleanTaskFollow.setScatteredCuttingPrice(req.getScatteredCuttingPrice());
        }
        if(StringUtils.isNotBlank(req.getScatteredCuttingUnit())){
            dimensionGleanTaskFollow.setScatteredCuttingUnit(req.getScatteredCuttingUnit());
        }
        if(req.getQuantity()!=null){
            dimensionGleanTaskFollow.setQuantity(req.getQuantity());
        }
        dimensionGleanTaskFollowRepository.saveOrUpdate(dimensionGleanTaskFollow);
    }
}
