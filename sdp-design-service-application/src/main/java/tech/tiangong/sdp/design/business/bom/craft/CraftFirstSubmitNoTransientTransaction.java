package tech.tiangong.sdp.design.business.bom.craft;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.tiangong.sdp.design.business.Transaction;
import tech.tiangong.sdp.design.business.bom.notification.Notification;
import tech.tiangong.sdp.design.converter.CraftDemandInfoConverter;
import tech.tiangong.sdp.design.converter.NotificationConverter;
import tech.tiangong.sdp.design.entity.BomOrderMaterial;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.enums.CraftDemandStateEnum;
import tech.tiangong.sdp.design.repository.BomOrderMaterialRepository;
import tech.tiangong.sdp.design.repository.CraftDemandInfoRepository;
import tech.tiangong.sdp.design.vo.dto.bom.CraftNotificationDto;
import tech.tiangong.sdp.design.vo.dto.bom.ReplenishPreCuttingCraftDto;
import tech.tiangong.sdp.design.vo.dto.bom.SyncCraftClothesDto;
import tech.tiangong.sdp.design.vo.req.bom.BomCraftSubmitHandleReq;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RequiredArgsConstructor
public class CraftFirstSubmitNoTransientTransaction implements Transaction {
    private final BomCraftSubmitHandleReq req;
    /**
     * 推送二次工艺
     */
    private final Notification<CraftNotificationDto> craftNotification;
    private final Notification<ReplenishPreCuttingCraftDto> replenishPreCuttingCraftNotification;
    private final Notification<SyncCraftClothesDto> syncCraftClothesNotification;

    private final CraftDemandInfoRepository craftDemandInfoRepository = SpringUtil.getBean(CraftDemandInfoRepository.class);
    private final BomOrderMaterialRepository bomOrderMaterialRepository = SpringUtil.getBean(BomOrderMaterialRepository.class);


    @Override
    public void execute() {
        List<CraftDemandInfo> addCraftDemands = req.getAddCraftDemandList().stream()
                .map(craftDemandReq -> CraftDemandInfoConverter.newCraftDemandV3(craftDemandReq, req.getBomOrder())).collect(Collectors.toList());

        Set<Long> delCraftDemandIds = req.getDelCraftDemandIds();

        List<CraftDemandInfo> delCraftDemandInfos = delCraftDemandIds.stream().map(id -> CraftDemandInfo.builder().craftDemandId(id).state(CraftDemandStateEnum.CLOSED.getCode()).build()).collect(Collectors.toList());

        //查出历史数据，提交的时候，没有改动的工艺
        List<CraftDemandInfo> listByBomIds = craftDemandInfoRepository.getListByBomIds(List.of(req.getBomOrder().getBomId()));
        List<CraftDemandInfo> notModifyCraftDemands = new LinkedList<>();
        if (CollectionUtil.isNotEmpty(delCraftDemandIds)) {
            notModifyCraftDemands = listByBomIds.stream()
                    .map(craftDemandInfo -> delCraftDemandIds.contains(craftDemandInfo.getCraftDemandId()) ? null : craftDemandInfo)
                    .filter(Objects::nonNull).collect(Collectors.toList());
        }

        log.info("【待提交-没有暂存】的提交,bomId:{}, addCraftDemands:{}",req.getBomOrder().getBomId(),req.getNewBomOrderId(), JSONUtil.toJsonStr(addCraftDemands));
        log.info("【待提交-没有暂存】的提交,bomId:{}, delCraftDemandInfos:{}",req.getBomOrder().getBomId(),req.getNewBomOrderId(),JSONUtil.toJsonStr(delCraftDemandInfos));
        log.info("【待提交-没有暂存】的提交,bomId:{}, notModifyCraftDemands:{}",req.getBomOrder().getBomId(),req.getNewBomOrderId(),JSONUtil.toJsonStr(notModifyCraftDemands));


        craftDemandInfoRepository.saveBatch(addCraftDemands);
        craftDemandInfoRepository.updateBatchById(delCraftDemandInfos);


        //推送二次工艺到履约
        craftNotification.addBatch(NotificationConverter.buildCraftNotificationDto(addCraftDemands));
        craftNotification.addBatch(NotificationConverter.buildCraftNotificationDto(notModifyCraftDemands));
        craftNotification.send();

        //创建工艺后，需回查已创建好的工艺,并设置履约的工艺id
        Map<Long, CraftDemandInfo> allCrafts = craftDemandInfoRepository.getListByBomIds(List.of(req.getBomOrder().getBomId())).stream().collect(Collectors.toMap(CraftDemandInfo::getCraftDemandId, Function.identity(), (k1, k2) -> k1));
        Stream.of(addCraftDemands, notModifyCraftDemands).flatMap(List::stream).filter(Objects::nonNull).forEach(craftDemandInfo -> {
            CraftDemandInfo craftDemandInfoDB = allCrafts.get(craftDemandInfo.getCraftDemandId());
            craftDemandInfo.setThirdPartyCraftDemandId(craftDemandInfoDB.getThirdPartyCraftDemandId());
            craftDemandInfo.setThirdPartyCraftDemandCode(craftDemandInfoDB.getThirdPartyCraftDemandCode());
        });


        //补推裁前工艺、打版
        List<Long> addNotModifyBomMaterialIds = Stream.of(addCraftDemands, notModifyCraftDemands).flatMap(List::stream).map(CraftDemandInfo::getBomMaterialId).collect(Collectors.toList());
        Map<Long, BomOrderMaterial> addNotModifyBomMaterialMap = Collections.emptyMap();
        if (CollectionUtil.isNotEmpty(addNotModifyBomMaterialIds)) {
            addNotModifyBomMaterialMap = bomOrderMaterialRepository.listByIds(addNotModifyBomMaterialIds).stream().collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, bomOrderMaterial -> bomOrderMaterial));
        }

        replenishPreCuttingCraftNotification.add(NotificationConverter.buildReplenishPreCuttingCraftDto(addCraftDemands, req.getBomOrder(), addNotModifyBomMaterialMap));
        replenishPreCuttingCraftNotification.add(NotificationConverter.buildReplenishPreCuttingCraftDto(notModifyCraftDemands, req.getBomOrder(), addNotModifyBomMaterialMap));
        replenishPreCuttingCraftNotification.send();

        //推送工艺到打版
        syncCraftClothesNotification.add(NotificationConverter.buildSyncCraftClothesDto(addCraftDemands, req.getBomOrder()));
        syncCraftClothesNotification.add(NotificationConverter.buildSyncCraftClothesDto(notModifyCraftDemands, req.getBomOrder()));
        syncCraftClothesNotification.send();
    }


}
