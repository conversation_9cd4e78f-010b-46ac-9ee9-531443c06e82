package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import com.yibuyun.scm.common.dto.accessories.response.CategoryTreeMapVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSkuVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.BomOrderMaterialConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.BomDemandTransientHandleService;
import tech.tiangong.sdp.design.service.BomMaterialCommonService;
import tech.tiangong.sdp.design.service.DesignRemarksService;
import tech.tiangong.sdp.design.service.MaterialSnapshotService;
import tech.tiangong.sdp.design.vo.dto.bom.BomMaterialInfoDto;
import tech.tiangong.sdp.design.vo.req.bom.BomCraftTransientHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomDemandTransientHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderUpdateV3Req;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandSaveV3Req;
import tech.tiangong.sdp.design.vo.req.material.MaterialSnapshotCreateReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;
import tech.tiangong.sdp.utils.Bool;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

;

/**
 *
 * bom暂存 需求处理服务
 * <AUTHOR>
 * @date 2022/11/17 11:01
 */


@Slf4j
@Service
@RequiredArgsConstructor
public class BomDemandTransientHandleServiceImpl implements BomDemandTransientHandleService {

    private final DesignRemarksService designRemarksService;
    private final BomMaterialDemandRepository materialDemandRepository;
    private final BomMaterialDemandTransientRepository materialDemandTransientRepository;
    private final BomOrderMaterialTransientRepository materialTransientRepository;
    private final BomOrderMaterialRepository materialRepository;
    private final CraftDemandInfoTransientRepository craftDemandInfoTransientRepository;
    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final BomMaterialCommonService bomMaterialCommonService;
    private final MaterialSnapshotService materialSnapshotService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftTransientHandleReq firstTransient(BomDemandTransientHandleReq req) {
        //待提交,首次暂存
        BomOrder bomOrder = req.getBomOrder();
        BomOrderTransient transientBom = req.getTransientBom();
        Long bomTransientId = transientBom.getBomTransientId();

        //收集新增的工艺
        List<CraftDemandSaveV3Req> addCraftReqList = new LinkedList<>();

        //1, 需求新增();首次暂存,只会有新增的需求,维护到暂存表中
        this.addDemandTransientHandle(bomOrder, req.getAddBomMaterialDemandList(), addCraftReqList, bomTransientId);

        //2, 处理工艺
        return BomCraftTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .craftSourceTypeEnum(CraftSourceTypeEnum.DEMAND)
                .addCraftDemandList(addCraftReqList)
                .delCraftDemandIds(Collections.emptySet())
                .oldTransientCraftMap(Collections.emptyMap())
                .oldNewMaterialIdMap(Collections.emptyMap())
                .oldNewDemandIdMap(Collections.emptyMap())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftTransientHandleReq reTransient(BomDemandTransientHandleReq req) {
        //待提交,再次暂存, 维护暂存表
        BomOrder bomOrder = req.getBomOrder();
        BomOrderTransient transientBom = req.getTransientBom();
        Long bomTransientId = transientBom.getBomTransientId();

        //收集新增/删除的工艺
        List<CraftDemandSaveV3Req> addCraftReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);
        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();

        //1, 需求新增();新增的需求都是暂存的, 维护到暂存表中
        this.addDemandTransientHandle(bomOrder, req.getAddBomMaterialDemandList(), addCraftReqList, bomTransientId);

        //2, 需求更新(); //更新暂存表数据; 收集新增,删除的工艺
        this.updateDemandTransientHandle4ReTransient(bomOrder, bomTransientId, req.getUpdateBomMaterialDemandList(), addCraftReqList, delCraftIdSet, oldNewMaterialIdMap);

        //3, 需求删除(); //都是暂存的需求,可逻辑删除需求, 物料; 收集删除的工艺
        this.delDemandTransientHandle4ReTransient(req.getDelBomMaterialDemandIds(), delCraftIdSet);

        //4, 处理工艺
        return BomCraftTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .craftSourceTypeEnum(CraftSourceTypeEnum.DEMAND)
                .addCraftDemandList(addCraftReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldTransientCraftMap(Collections.emptyMap())
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldNewDemandIdMap(Collections.emptyMap())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftTransientHandleReq firstTransient4Submitted(BomDemandTransientHandleReq req) {
        BomOrder bomOrder = req.getBomOrder();
        BomOrderTransient transientBom = req.getTransientBom();
        Long bomTransientId = transientBom.getBomTransientId();

        //收集新增/删除的工艺; 新旧物料映射Map
        List<CraftDemandSaveV3Req> addCraftReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);
        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();
        Map<Long, Long> oldNewDemandIdMap = new HashMap<>();

        //1, 需求新增();新增的需求都是暂存的, 维护到暂存表中
        this.addDemandTransientHandle(bomOrder, req.getAddBomMaterialDemandList(), addCraftReqList, bomTransientId);

        //2, 需求更新(); //在原数据基础上,更新需求与物料, 新增到暂存表; 收集增删的工艺
        this.update4FirstTransientSubmitted(bomOrder, transientBom, req.getUpdateBomMaterialDemandList(),
                addCraftReqList, delCraftIdSet, oldNewMaterialIdMap, oldNewDemandIdMap);

        //3, 需求删除(); //在原数据基础上将需求与物料状态设置为删除,新增到暂存表中; 收集删除的工艺
        this.delDemand4FirstTransientSubmitted(bomOrder, transientBom, req.getDelBomMaterialDemandIds(),
                delCraftIdSet, oldNewMaterialIdMap, oldNewDemandIdMap);

        //4, 处理工艺
        return BomCraftTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .craftSourceTypeEnum(CraftSourceTypeEnum.DEMAND)
                .addCraftDemandList(addCraftReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldNewDemandIdMap(oldNewDemandIdMap)
                .oldTransientCraftMap(Collections.emptyMap())
                .build();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCraftTransientHandleReq reTransient4Submitted(BomDemandTransientHandleReq req) {
        //已提交,再次暂存, 维护暂存表
        BomOrder bomOrder = req.getBomOrder();
        BomOrderTransient transientBom = req.getTransientBom();
        Long bomTransientId = transientBom.getBomTransientId();

        //收集新增/删除的工艺
        List<CraftDemandSaveV3Req> addCraftReqList = new LinkedList<>();
        Set<Long> delCraftIdSet = new HashSet<>(64);
        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();

        //1, 需求新增();新增的需求都是暂存的, 维护到暂存表中
        this.addDemandTransientHandle(bomOrder, req.getAddBomMaterialDemandList(), addCraftReqList, bomTransientId);

        //2, 需求更新(); //更新暂存表数据; 收集新增,删除的工艺; 工艺处理
        this.updateDemandTransientHandle4ReTransient(bomOrder, bomTransientId, req.getUpdateBomMaterialDemandList(), addCraftReqList, delCraftIdSet, oldNewMaterialIdMap);

        //3, 需求删除(); //更新暂存表数据, 暂存数据删除; 原数据close, 物料; 收集删除的工艺
        this.delDemand4ReTransientSubmit(req.getDelBomMaterialDemandIds(), delCraftIdSet);

        //4, 处理工艺
        return BomCraftTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .craftSourceTypeEnum(CraftSourceTypeEnum.DEMAND)
                .addCraftDemandList(addCraftReqList)
                .delCraftDemandIds(delCraftIdSet)
                .oldTransientCraftMap(Collections.emptyMap())
                .oldNewMaterialIdMap(oldNewMaterialIdMap)
                .oldNewDemandIdMap(Collections.emptyMap())
                .build();
    }









    private void delDemand4FirstTransientSubmitted(BomOrder bomOrder,
                                                   BomOrderTransient transientBom,
                                                   List<Long> delBomMaterialDemandIds,
                                                   Set<Long> delCraftIdSet,
                                                   Map<Long, Long> oldNewMaterialIdMap,
                                                   Map<Long, Long> oldNewDemandIdMap) {
        if (CollUtil.isEmpty(delBomMaterialDemandIds)) {
            return;
        }
        Long bomId = bomOrder.getBomId();
        Long bomTransientId = transientBom.getBomTransientId();
        //原有需求
        List<BomMaterialDemand> oldDelDemandList = materialDemandRepository.listByIds(delBomMaterialDemandIds);
        SdpDesignException.notEmpty(oldDelDemandList, "bom单需求信息查询失败! bomId:{}", bomId);

        //原有需求的物料
        List<Long> oldDemandIdList = oldDelDemandList.stream().map(BomMaterialDemand::getBomMaterialDemandId).collect(Collectors.toList());
        List<BomOrderMaterial> oldMaterialList = materialRepository.listByDemandIds(oldDemandIdList);
        Map<Long, List<BomOrderMaterial>> delMaterialGroupMap = oldMaterialList.stream().collect(Collectors.groupingBy(BomOrderMaterial::getBomMaterialDemandId));
        List<Long> oldMaterialIdList = oldMaterialList.stream().map(BomOrderMaterial::getBomMaterialId).collect(Collectors.toList());

        //根据物料id集合查询未关闭的工艺信息
        List<Long> delCraftDemandIdList = craftDemandInfoRepository.getListByMaterialIds(oldMaterialIdList).stream()
                .filter(item -> !Objects.equals(CraftDemandStateEnum.CLOSED.getCode(), item.getState()))
                .map(CraftDemandInfo::getCraftDemandId)
                .collect(Collectors.toList());

        //删除工艺
        if (CollUtil.isNotEmpty(delCraftDemandIdList)) {
            delCraftIdSet.addAll(delCraftDemandIdList);
        }

        List<BomMaterialDemandTransient> addDemandTransientList = new LinkedList<>();
        List<BomOrderMaterialTransient> addMaterialTransientList = new LinkedList<>();

        oldDelDemandList.forEach(delDemand -> {
            //复制删除的需求到暂存表
            Long originDemandId = delDemand.getBomMaterialDemandId();
            Long newDemandId = IdPool.getId();
            BomMaterialDemandTransient addDemandTransient = new BomMaterialDemandTransient();
            BeanUtils.copyProperties(delDemand, addDemandTransient);
            addDemandTransient.setDemandState(BomDemandStateEnum.CLOSED.getCode());
            addDemandTransient.setBomMaterialDemandId(newDemandId);
            addDemandTransient.setOriginDemandId(originDemandId);
            addDemandTransient.setBomTransientId(bomTransientId);
            addDemandTransientList.add(addDemandTransient);
            oldNewDemandIdMap.put(originDemandId, newDemandId);

            //复制删除需求下, 所有的物料到暂存表
            List<BomOrderMaterial> delMaterialList = delMaterialGroupMap.get(delDemand.getBomMaterialDemandId());
            delMaterialList.forEach(delMaterial -> {
                Long originMaterialId = delMaterial.getBomMaterialId();
                Long newMaterialId = IdPool.getId();
                BomOrderMaterialTransient addMaterialTransient = new BomOrderMaterialTransient();
                BeanUtils.copyProperties(delMaterial, addMaterialTransient);
                addMaterialTransient.setBomMaterialId(newMaterialId);
                addMaterialTransient.setMaterialState(BomMaterialStateEnum.CLOSED.getCode());
                addMaterialTransient.setOriginMaterialId(originMaterialId);
                addMaterialTransient.setBomTransientId(bomTransientId);
                addMaterialTransientList.add(addMaterialTransient);
                oldNewMaterialIdMap.put(originMaterialId, newMaterialId);
            });
        });

        //复制删除需求与物料到暂存表
        materialDemandTransientRepository.saveBatch(addDemandTransientList);
        materialTransientRepository.saveBatch(addMaterialTransientList);
    }

    private void delDemandTransientHandle4ReTransient(List<Long> delBomMaterialDemandIds, Set<Long> delCraftIdSet) {
        if (CollUtil.isEmpty(delBomMaterialDemandIds)) {
            return;
        }
        //查询物料信息
        List<Long> delMaterialIdList = materialTransientRepository.listByTransientDemandIdList(delBomMaterialDemandIds).stream()
                .map(BomOrderMaterialTransient::getBomMaterialId)
                .collect(Collectors.toList());

        //根据物料id集合查询暂存工艺信息
        List<Long> craftDemandIdList = craftDemandInfoTransientRepository.listByTransientMaterialIds(delMaterialIdList).stream()
                .filter(item -> !Objects.equals(CraftDemandStateEnum.CLOSED.getCode(), item.getState()))
                .map(CraftDemandInfoTransient::getCraftDemandId)
                .collect(Collectors.toList());

        //删除工艺
        if (CollUtil.isNotEmpty(craftDemandIdList)) {
            delCraftIdSet.addAll(craftDemandIdList);
        }
        //逻辑删除暂存需求与物料
        materialDemandTransientRepository.removeByIds(delBomMaterialDemandIds);
        materialTransientRepository.removeByIds(delMaterialIdList);
    }

    private void delDemand4ReTransientSubmit(List<Long> delBomMaterialDemandIds, Set<Long> delCraftIdSet) {
        if (CollUtil.isEmpty(delBomMaterialDemandIds)) {
            return;
        }
        //暂存需求信息
        List<BomMaterialDemandTransient> delDemandTransientList = materialDemandTransientRepository.listByIds(delBomMaterialDemandIds);

        //暂存物料信息
        List<BomOrderMaterialTransient> delMaterialTransientList = materialTransientRepository.listByTransientDemandIdList(delBomMaterialDemandIds);
        Map<Long, List<BomOrderMaterialTransient>> delMaterialGroupMap = delMaterialTransientList.stream().collect(Collectors.groupingBy(BomOrderMaterialTransient::getBomMaterialDemandId));

        //根据物料id集合查询暂存工艺信息
        List<Long> delMaterialIdList = delMaterialTransientList.stream()
                .map(BomOrderMaterialTransient::getBomMaterialId).collect(Collectors.toList());
        List<Long> craftDemandIdList = craftDemandInfoTransientRepository.listByTransientMaterialIds(delMaterialIdList).stream()
                .filter(item -> !Objects.equals(CraftDemandStateEnum.CLOSED.getCode(), item.getState()))
                .map(CraftDemandInfoTransient::getCraftDemandId)
                .collect(Collectors.toList());

        //删除工艺
        if (CollUtil.isNotEmpty(craftDemandIdList)) {
            delCraftIdSet.addAll(craftDemandIdList);
        }

        List<BomMaterialDemandTransient> updateDemandTransientList = new LinkedList<>();
        List<BomOrderMaterialTransient> updateMaterialTransientList = new LinkedList<>();
        List<Long> logicDelDemandList = new LinkedList<>();
        List<Long> logicDelMaterialList = new LinkedList<>();

        delDemandTransientList.forEach(delDemand -> {
            //暂存添加的需求, 逻辑删除
            Long delDemandId = delDemand.getBomMaterialDemandId();
            if (Objects.isNull(delDemand.getOriginDemandId())) {
                logicDelDemandList.add(delDemandId);
                //对应需求下的物料也逻辑删除
                if (CollUtil.isNotEmpty(delMaterialGroupMap) && CollUtil.isNotEmpty(delMaterialGroupMap.get(delDemandId))) {
                    Set<Long> delMaterialIds = delMaterialGroupMap.get(delDemandId).stream().map(BomOrderMaterialTransient::getBomMaterialId).collect(Collectors.toSet());
                    logicDelMaterialList.addAll(delMaterialIds);
                }
                return;
            }
            //原有需求, 关闭;
            BomMaterialDemandTransient updateDemandTransient = new BomMaterialDemandTransient();
            updateDemandTransient.setBomMaterialDemandId(delDemandId);
            updateDemandTransient.setDemandState(BomDemandStateEnum.CLOSED.getCode());
            updateDemandTransientList.add(updateDemandTransient);
            // 对应需求下的物料也关闭
            if (CollUtil.isNotEmpty(delMaterialGroupMap) && CollUtil.isNotEmpty(delMaterialGroupMap.get(delDemandId))) {
                List<BomOrderMaterialTransient> delMaterialList = delMaterialGroupMap.get(delDemandId);
                delMaterialList.forEach(delMaterial -> {
                    BomOrderMaterialTransient updateMaterialTransient = new BomOrderMaterialTransient();
                    updateMaterialTransient.setBomMaterialId(delMaterial.getBomMaterialId());
                    updateMaterialTransient.setMaterialState(BomMaterialStateEnum.CLOSED.getCode());
                    updateMaterialTransientList.add(updateMaterialTransient);
                });
            }
        });

        //关闭原有需求与物料
        if (CollUtil.isNotEmpty(updateDemandTransientList)) {
            materialDemandTransientRepository.updateBatchById(updateDemandTransientList);
        }
        if (CollUtil.isNotEmpty(updateMaterialTransientList)) {
            materialTransientRepository.updateBatchById(updateMaterialTransientList);
        }

        //逻辑删除暂存需求与物料
        if (CollUtil.isNotEmpty(logicDelDemandList)) {
            materialDemandTransientRepository.removeByIds(logicDelDemandList);
        }
        if (CollUtil.isNotEmpty(logicDelMaterialList)) {
            materialTransientRepository.removeByIds(logicDelMaterialList);
        }
    }

    private void update4FirstTransientSubmitted(BomOrder bomOrder,
                                                BomOrderTransient transientBom,
                                                List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateDemandReqList,
                                                List<CraftDemandSaveV3Req> addCraftReqList,
                                                Set<Long> delCraftIdSet,
                                                Map<Long, Long> oldNewMaterialIdMap,
                                                Map<Long, Long> oldNewDemandIdMap) {

        if (CollUtil.isEmpty(updateDemandReqList)) {
            return;
        }
        List<BomMaterialDemandTransient> addDemandTransientList = new LinkedList<>();
        List<BomOrderMaterialTransient> addMaterialTransientList = new LinkedList<>();
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        //更换新增的物料
        List<BomOrderUpdateV3Req.DemandMaterialAddReq> replaceMaterialList = new LinkedList<>();

        Long bomId = bomOrder.getBomId();
        Long bomTransientId = transientBom.getBomTransientId();
        //原有需求
        List<BomMaterialDemand> oldDemandList = materialDemandRepository.listByBomId(bomId);
        SdpDesignException.notEmpty(oldDemandList, "bom单需求信息查询失败! bomId:{}", bomId);
        Map<Long, BomMaterialDemand> oldDemandMap = oldDemandList.stream()
                .collect(Collectors.toMap(BomMaterialDemand::getBomMaterialDemandId, Function.identity(), (k1, k2) -> k1));

        //原有需求的物料
        List<Long> oldDemandIdList = oldDemandList.stream().map(BomMaterialDemand::getBomMaterialDemandId).collect(Collectors.toList());
        Map<Long, BomOrderMaterial> oldMaterialMap = materialRepository.listByDemandIds(oldDemandIdList).stream()
                .filter(item -> !Objects.equals(item.getMaterialState(), BomMaterialStateEnum.CLOSED.getCode()))
                .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));

        //有匹配回复的需求更新,查询对应的辅料信息
        BomMaterialInfoDto updateMaterialInfoDto = this.getUpdateMaterialInfoDto(updateDemandReqList);

        //1, 更新暂存需求处理: 在原数据基础上,更新需求与物料, 新增到暂存表; 收集增删的工艺
        updateDemandReqList.forEach(updateDemandReq -> {
            //直接更新物料, 不是更换物料的场景
            List<BomOrderUpdateV3Req.DemandMaterialUpdateReq> materialUpdateReqList = updateDemandReq.getMaterialUpdateReqList();
            if (CollUtil.isNotEmpty(materialUpdateReqList) && Objects.nonNull(materialUpdateReqList.get(0))) {
                BomOrderUpdateV3Req.DemandMaterialUpdateReq materialUpdateReq = materialUpdateReqList.get(0);
                BomMaterialDemand oldDemand = oldDemandMap.get(updateDemandReq.getBomMaterialDemandId());
                Long newDemandTransientId = IdPool.getId();
                Long oldDemandId = oldDemand.getBomMaterialDemandId();
                //复制原需求, 更新后, 新增到暂存表
                BomMaterialDemandTransient addDemandTransient = new BomMaterialDemandTransient();
                BeanUtils.copyProperties(oldDemand, addDemandTransient);
                addDemandTransient.setBomMaterialDemandId(newDemandTransientId);
                addDemandTransient.setOriginDemandId(oldDemandId);
                addDemandTransient.setBomTransientId(bomTransientId);
                addDemandTransient.setPrototypeMaterialName(updateDemandReq.getPrototypeMaterialName());
                addDemandTransient.setColorMatchMaterialState(updateDemandReq.getColorMatchMaterialState());
                addDemandTransient.setColorMatchMaterialName(this.getDemandColorMatchName(updateDemandReq));
                oldNewDemandIdMap.put(oldDemandId, newDemandTransientId);

                //复制原物料, 更新后, 新增到暂存表
                BomOrderMaterial oldMaterial = oldMaterialMap.get(materialUpdateReq.getBomMaterialId());
                Long oldMaterialId = oldMaterial.getBomMaterialId();
                Long newMaterialId = IdPool.getId();
                BomOrderMaterialTransient addMaterialTransient = new BomOrderMaterialTransient();
                BeanUtils.copyProperties(oldMaterial, addMaterialTransient);
                addMaterialTransient.setBomMaterialId(newMaterialId);
                //关联暂存表需求
                addMaterialTransient.setBomMaterialDemandId(newDemandTransientId);
                addMaterialTransient.setOriginMaterialId(oldMaterialId);
                addMaterialTransient.setBomTransientId(bomTransientId);
                addMaterialTransient.setPartUse(materialUpdateReq.getPartUse());
                addMaterialTransient.setPrototypeMaterialName(materialUpdateReq.getPrototypeMaterialName());
                addMaterialTransient.setColorMatchMaterialState(materialUpdateReq.getColorMatchMaterialState());
                addMaterialTransient.setColorMatchMaterialName(this.getColorMatchName(materialUpdateReq.getColorMatchMaterialState(), materialUpdateReq.getColorMatchMaterialName()));
                //最新的价格与采购周期信息
                this.resetMaterialInfo(updateMaterialInfoDto, materialUpdateReq, addMaterialTransient);
                addMaterialTransientList.add(addMaterialTransient);
                oldNewMaterialIdMap.put(oldMaterialId, newMaterialId);

                addDemandTransient.setLatestBomMaterialId(addMaterialTransient.getBomMaterialId());
                addDemandTransientList.add(addDemandTransient);

                //物料备注
                if (StringUtils.isNotBlank(materialUpdateReq.getRemark())) {
                    designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, newMaterialId, materialUpdateReq.getRemark()));
                }

                //新增工艺
                if (CollUtil.isNotEmpty(materialUpdateReq.getAddCraftDemandList())) {
                    List<CraftDemandSaveV3Req> addCraftDemandList = materialUpdateReq.getAddCraftDemandList();
                    //设置物料id
                    addCraftDemandList.forEach(craft -> {
                        craft.setBomMaterialDemandId(newDemandTransientId);
                        craft.setBomMaterialId(newMaterialId);
                    });
                    addCraftReqList.addAll(materialUpdateReq.getAddCraftDemandList());
                }
                //删除工艺
                if (CollUtil.isNotEmpty(materialUpdateReq.getDelCraftDemandIds())) {
                    delCraftIdSet.addAll(materialUpdateReq.getDelCraftDemandIds());
                }
            }
            //更换物料的场景
            else {
                List<BomOrderUpdateV3Req.DemandMaterialAddReq> materialAddReqList = updateDemandReq.getMaterialAddReqList();
                if (CollUtil.isNotEmpty(materialAddReqList) && Objects.nonNull(materialAddReqList.get(0))) {
                    BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq = materialAddReqList.get(0);
                    //收集后统一处理
                    replaceMaterialList.add(materialAddReq);
                }
            }
        });

        //更换物料处理
        if (CollUtil.isNotEmpty(replaceMaterialList)) {
            //根据skuId查询物料信息
            BomMaterialInfoDto materialInfoDto = this.getMaterialInfoDto(replaceMaterialList);

            //查询被替换的原有物料
            List<Long> replaceMaterialIdList = replaceMaterialList.stream()
                    .map(BomOrderUpdateV3Req.DemandMaterialAddReq::getBomMaterialIdChange).collect(Collectors.toList());
            Map<Long, BomOrderMaterial> replaceMaterialMap = materialRepository.listByIds(replaceMaterialIdList).stream()
                    .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));

            updateDemandReqList.forEach(updateDemandReq -> {
                //可能是物料更换, 新增一个物料, 需求关联最新的物料
                if (CollUtil.isEmpty(updateDemandReq.getMaterialAddReqList())) {
                    return;
                }
                BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq = updateDemandReq.getMaterialAddReqList().get(0);
                if (Objects.nonNull(materialAddReq) && Objects.nonNull(materialAddReq.getBomMaterialIdChange())) {
                    BomMaterialDemand oldDemand = oldDemandMap.get(updateDemandReq.getBomMaterialDemandId());
                    Long newDemandTransientId = IdPool.getId();
                    Long oldDemandId = oldDemand.getBomMaterialDemandId();
                    //复制原需求, 更新后, 新增到暂存表
                    BomMaterialDemandTransient addDemandTransient = new BomMaterialDemandTransient();
                    BeanUtils.copyProperties(oldDemand, addDemandTransient);
                    addDemandTransient.setBomMaterialDemandId(newDemandTransientId);
                    addDemandTransient.setOriginDemandId(oldDemandId);
                    addDemandTransient.setBomTransientId(bomTransientId);
                    addDemandTransient.setPrototypeMaterialName(updateDemandReq.getPrototypeMaterialName());
                    addDemandTransient.setColorMatchMaterialState(updateDemandReq.getColorMatchMaterialState());
                    addDemandTransient.setColorMatchMaterialName(this.getDemandColorMatchName(updateDemandReq));
                    oldNewDemandIdMap.put(oldDemandId, newDemandTransientId);

                    //复制被替换的原物料, 设置为关闭, 新增到暂存表
                    BomOrderMaterial oldMaterialChange = replaceMaterialMap.get(materialAddReq.getBomMaterialIdChange());
                    Long oldMaterialChangeId = oldMaterialChange.getBomMaterialId();
                    Long newMaterialChangeId = IdPool.getId();
                    BomOrderMaterialTransient addMaterialChangeTransient = new BomOrderMaterialTransient();
                    BeanUtils.copyProperties(oldMaterialChange, addMaterialChangeTransient);
                    addMaterialChangeTransient.setBomMaterialId(newMaterialChangeId);
                    //关联暂存表需求
                    addMaterialChangeTransient.setBomMaterialDemandId(newDemandTransientId);
                    addMaterialChangeTransient.setOriginMaterialId(oldMaterialChangeId);
                    addMaterialChangeTransient.setBomTransientId(bomTransientId);
                    addMaterialChangeTransient.setMaterialState(BomMaterialStateEnum.CLOSED.getCode());
                    addMaterialTransientList.add(addMaterialChangeTransient);

                    //更换的物料新增为暂存物料(注: originId要保留)
                    BomOrderMaterialTransient addMaterialTransient = this.replaceNewMaterialTransientWithSubmit(materialAddReq, addMaterialChangeTransient, materialInfoDto);
                    addMaterialTransientList.add(addMaterialTransient);
                    //需求关联替换后的物料
                    addDemandTransient.setLatestBomMaterialId(addMaterialTransient.getBomMaterialId());
                    addDemandTransientList.add(addDemandTransient);

                    //工艺关联替换后的物料
                    oldNewMaterialIdMap.put(oldMaterialChangeId, addMaterialTransient.getBomMaterialId());

                    if (StringUtils.isNotBlank(materialAddReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, addMaterialTransient.getBomMaterialId(), materialAddReq.getRemark()));
                    }
                    //新增工艺
                    if (CollUtil.isNotEmpty(materialAddReq.getAddCraftDemandList())) {
                        List<CraftDemandSaveV3Req> addCraftDemandList = materialAddReq.getAddCraftDemandList();
                        //设置物料id
                        addCraftDemandList.forEach(craft -> {
                            craft.setBomMaterialDemandId(updateDemandReq.getBomMaterialDemandId());
                            craft.setBomMaterialId(addMaterialTransient.getBomMaterialId());
                        });
                        addCraftReqList.addAll(materialAddReq.getAddCraftDemandList());
                    }
                    //更换物料删除的工艺
                    Set<Long> delCraftDemandIds = materialAddReq.getDelCraftDemandIds();
                    //删除工艺
                    if (CollUtil.isNotEmpty(delCraftDemandIds)) {
                        delCraftIdSet.addAll(delCraftDemandIds);
                    }
                }
            });
            //找料中, 更换物料,不会把工艺全部删除; 找料中的工艺与需求绑定

            // //删除替换物料下的工艺: 根据物料id集合查询暂存工艺信息
            // List<Long> craftDemandIdList = craftDemandInfoRepository.getListByMaterialIds(replaceMaterialIdList).stream()
            //         .filter(item -> !Objects.equals(CraftDemandStateEnum.CLOSED.getCode(), item.getState()))
            //         .map(CraftDemandInfo::getCraftDemandId)
            //         .collect(Collectors.toList());
            // //删除工艺
            // if (CollUtil.isNotEmpty(craftDemandIdList)) {
            //     delCraftIdSet.addAll(craftDemandIdList);
            // }
        }

        //新增暂存需求与物料
        if (CollUtil.isNotEmpty(addDemandTransientList)) {
            materialDemandTransientRepository.saveBatch(addDemandTransientList);
        }
        if (CollUtil.isNotEmpty(addMaterialTransientList)) {
            materialTransientRepository.saveBatch(addMaterialTransientList);
        }

        //添加物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private BomOrderMaterialTransient replaceNewMaterialTransientWithSubmit(BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq,
                                                                            BomOrderMaterialTransient addMaterialChangeTransient,
                                                                            BomMaterialInfoDto materialInfoDto) {
        //复制原物料, 更新后, 新增到暂存表
        Long newMaterialId = IdPool.getId();
        BomOrderMaterialTransient addMaterialTransient = new BomOrderMaterialTransient();
        BeanUtils.copyProperties(addMaterialChangeTransient, addMaterialTransient);
        addMaterialTransient.setBomMaterialId(newMaterialId);
        addMaterialTransient.setMaterialState(BomMaterialStateEnum.NORMAL.getCode());
        addMaterialTransient.setPartUse(materialAddReq.getPartUse());
        addMaterialTransient.setPrototypeMaterialName(materialAddReq.getPrototypeMaterialName());
        addMaterialTransient.setColorMatchMaterialState(materialAddReq.getColorMatchMaterialState());
        addMaterialTransient.setColorMatchMaterialName(this.getColorMatchName(materialAddReq.getColorMatchMaterialState(), materialAddReq.getColorMatchMaterialName()));

        //辅料信息
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Long skuId = materialAddReq.getSkuId();
        SdpDesignException.isTrue(CollUtil.isNotEmpty(accessoriesSkuMap), "辅料spu信息不存在! skuId:{}", skuId);
        ProductSkuVo productSkuVo = accessoriesSkuMap.get(skuId);
        SdpDesignException.notNull(productSkuVo, "辅料spu信息不存在! skuId:{}", skuId);
        addMaterialTransient.setMinPrice(productSkuVo.getMinPrice());
        //价格有效期结束时间
        addMaterialTransient.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());

        //样衣与大货周期
        addMaterialTransient.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
        addMaterialTransient.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());

        //创建物料快照
        Long materialSnapshotId = this.createSnapshot(materialAddReq, materialInfoDto, newMaterialId);

        //新增物料关联快照id
        addMaterialTransient.setMaterialSnapshotId(materialSnapshotId);

        return addMaterialTransient;
    }

    private void updateDemandTransientHandle4ReTransient(BomOrder bomOrder,
                                                         Long bomTransientId,
                                                         List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateDemandReqList,
                                                         List<CraftDemandSaveV3Req> addCraftReqList,
                                                         Set<Long> delCraftIdSet,
                                                         Map<Long, Long> oldNewMaterialIdMap) {
        if (CollUtil.isEmpty(updateDemandReqList)) {
            return;
        }
        List<BomMaterialDemandTransient> updateDemandTransientList = new LinkedList<>();
        List<BomOrderMaterialTransient> updateBomMaterialList = new LinkedList<>();
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();
        //更换新增的物料
        List<BomOrderUpdateV3Req.DemandMaterialAddReq> replaceMaterialList = new LinkedList<>();

        //有匹配回复的需求更新,查询对应的辅料信息
        BomMaterialInfoDto updateMaterialInfoDto = this.getUpdateMaterialInfoDto(updateDemandReqList);

        //1, 更新暂存需求处理
        updateDemandReqList.forEach(updateDemandReq -> {
            //直接更新物料, 不是更换物料
            List<BomOrderUpdateV3Req.DemandMaterialUpdateReq> materialUpdateReqList = updateDemandReq.getMaterialUpdateReqList();
            if (CollUtil.isNotEmpty(materialUpdateReqList) && Objects.nonNull(materialUpdateReqList.get(0))) {
                BomOrderUpdateV3Req.DemandMaterialUpdateReq materialUpdateReq = materialUpdateReqList.get(0);
                //更新需求
                BomMaterialDemandTransient updateDemandTransient = this.buildTransientUpdateDemand(updateDemandReq);
                updateDemandTransientList.add(updateDemandTransient);

                //更新物料
                BomOrderMaterialTransient updateMaterialTransient = new BomOrderMaterialTransient();
                updateMaterialTransient.setBomMaterialId(materialUpdateReq.getBomMaterialId());
                updateMaterialTransient.setPartUse(materialUpdateReq.getPartUse());
                updateMaterialTransient.setPrototypeMaterialName(materialUpdateReq.getPrototypeMaterialName());
                updateMaterialTransient.setColorMatchMaterialState(materialUpdateReq.getColorMatchMaterialState());
                updateMaterialTransient.setColorMatchMaterialName(this.getColorMatchName(materialUpdateReq.getColorMatchMaterialState(), materialUpdateReq.getColorMatchMaterialName()));
                //最新的价格与采购周期信息
                this.resetMaterialInfo(updateMaterialInfoDto, materialUpdateReq, updateMaterialTransient);
                updateBomMaterialList.add(updateMaterialTransient);

                //物料备注
                if (StringUtils.isNotBlank(materialUpdateReq.getRemark())) {
                    designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, materialUpdateReq.getBomMaterialId(), materialUpdateReq.getRemark()));
                }

                //新增工艺
                if (CollUtil.isNotEmpty(materialUpdateReq.getAddCraftDemandList())) {
                    List<CraftDemandSaveV3Req> addCraftDemandList = materialUpdateReq.getAddCraftDemandList();
                    //设置物料id
                    addCraftDemandList.forEach(craft -> {
                        craft.setBomMaterialDemandId(updateDemandReq.getBomMaterialDemandId());
                        craft.setBomMaterialId(materialUpdateReq.getBomMaterialId());
                    });
                    addCraftReqList.addAll(materialUpdateReq.getAddCraftDemandList());
                }
                //删除工艺
                if (CollUtil.isNotEmpty(materialUpdateReq.getDelCraftDemandIds())) {
                    delCraftIdSet.addAll(materialUpdateReq.getDelCraftDemandIds());
                }
            }
            //可能是物料更换, 新增一个物料, 需求关联最新的物料
            List<BomOrderUpdateV3Req.DemandMaterialAddReq> materialAddReqList = updateDemandReq.getMaterialAddReqList();

            if (CollUtil.isNotEmpty(materialAddReqList) && Objects.nonNull(materialAddReqList.get(0))) {
                BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq = materialAddReqList.get(0);
                //收集后统一处理
                replaceMaterialList.add(materialAddReq);
            }
        });

        //更换物料处理
        List<BomOrderMaterialTransient> addBomMaterialList = new LinkedList<>();
        List<Long> delMaterialTransientIdList = new LinkedList<>();
        if (CollUtil.isNotEmpty(replaceMaterialList)) {
            //根据skuId查询物料信息
            BomMaterialInfoDto materialInfoDto = this.getMaterialInfoDto(replaceMaterialList);

            //查询被替换的暂存物料
            List<Long> replaceMaterialIdList = replaceMaterialList.stream()
                    .map(BomOrderUpdateV3Req.DemandMaterialAddReq::getBomMaterialIdChange).collect(Collectors.toList());
            Map<Long, BomOrderMaterialTransient> replaceMaterialMap = materialTransientRepository.listByIds(replaceMaterialIdList).stream()
                    .collect(Collectors.toMap(BomOrderMaterialTransient::getBomMaterialId, Function.identity(), (k1, k2) -> k1));

            updateDemandReqList.forEach(updateDemandReq -> {
                //可能是物料更换, 新增一个物料, 需求关联最新的物料
                if (CollUtil.isEmpty(updateDemandReq.getMaterialAddReqList())) {
                    return;
                }
                BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq = updateDemandReq.getMaterialAddReqList().get(0);
                if (Objects.nonNull(materialAddReq) && Objects.nonNull(materialAddReq.getBomMaterialIdChange())) {
                    BomOrderMaterialTransient replaceMaterial = replaceMaterialMap.get(materialAddReq.getBomMaterialIdChange());
                    //新增一个暂存物料(被替换的暂存物料会被逻辑删除)
                    BomOrderMaterialTransient addMaterialTransient = this.replaceNewMaterialTransient(materialAddReq, replaceMaterial, materialInfoDto);
                    addBomMaterialList.add(addMaterialTransient);

                    //更新需求关联最新物料id
                    BomMaterialDemandTransient updateDemandTransient = this.buildTransientUpdateDemand(updateDemandReq);
                    updateDemandTransient.setLatestBomMaterialId(addMaterialTransient.getBomMaterialId());
                    updateDemandTransientList.add(updateDemandTransient);

                    //暂存工艺中更新替换的物料id
                    oldNewMaterialIdMap.put(materialAddReq.getBomMaterialIdChange(), addMaterialTransient.getBomMaterialId());

                    //物料备注
                    if (StringUtils.isNotBlank(materialAddReq.getRemark())) {
                        designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, addMaterialTransient.getBomMaterialId(), materialAddReq.getRemark()));
                    }
                    //新增工艺
                    if (CollUtil.isNotEmpty(materialAddReq.getAddCraftDemandList())) {
                        List<CraftDemandSaveV3Req> addCraftDemandList = materialAddReq.getAddCraftDemandList();
                        //设置物料id
                        addCraftDemandList.forEach(craft -> {
                            craft.setBomMaterialDemandId(updateDemandReq.getBomMaterialDemandId());
                            craft.setBomMaterialId(addMaterialTransient.getBomMaterialId());
                        });
                        addCraftReqList.addAll(materialAddReq.getAddCraftDemandList());
                    }
                    //更换物料删除的原工艺
                    Set<Long> delCraftDemandIds = materialAddReq.getDelCraftDemandIds();
                    //删除工艺
                    if (CollUtil.isNotEmpty(delCraftDemandIds)) {
                        delCraftIdSet.addAll(delCraftDemandIds);
                    }

                    //删除被替换的暂存物料
                    delMaterialTransientIdList.add(replaceMaterial.getBomMaterialId());
                }
            });
            //找料中, 更换物料,不会把工艺全部删除; 找料中的工艺与需求绑定

            // //删除替换物料下的工艺: 根据物料id集合查询暂存工艺信息
            // List<Long> craftDemandIdList = craftDemandInfoTransientRepository.listByTransientMaterialIds(replaceMaterialIdList).stream()
            //         .filter(item -> !Objects.equals(CraftDemandStateEnum.CLOSED.getCode(), item.getState()))
            //         .map(CraftDemandInfoTransient::getCraftDemandId)
            //         .collect(Collectors.toList());
            // //删除工艺
            // if (CollUtil.isNotEmpty(craftDemandIdList)) {
            //     delCraftIdSet.addAll(craftDemandIdList);
            // }
        }

        //新增物料
        if (CollUtil.isNotEmpty(addBomMaterialList)) {
            materialTransientRepository.saveBatch(addBomMaterialList);
        }

        //更新暂存需求与物料
        if (CollUtil.isNotEmpty(updateDemandTransientList)) {
            this.updateTransientDemand(updateDemandTransientList, UserContentHolder.get());
        }
        if (CollUtil.isNotEmpty(updateBomMaterialList)) {
            this.updateMaterialTransient(updateBomMaterialList, UserContentHolder.get());
        }

        //删除被替换的物料
        if (CollUtil.isNotEmpty(delMaterialTransientIdList)) {
            materialTransientRepository.removeByIds(delMaterialTransientIdList);
        }

        //添加物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void updateTransientDemand(List<BomMaterialDemandTransient> updateDemandTransientList, UserContent userContent) {
        if (CollUtil.isEmpty(updateDemandTransientList)) {
            return;
        }
        //不能用updateById来更新, 有些属性是可以更新为空的
        updateDemandTransientList.forEach(item -> {
            materialDemandTransientRepository.lambdaUpdate()
                    .set(BomMaterialDemandTransient::getPrototypeMaterialName, item.getPrototypeMaterialName())
                    .set(BomMaterialDemandTransient::getColorMatchMaterialState, item.getColorMatchMaterialState())
                    .set(BomMaterialDemandTransient::getColorMatchMaterialName, StringUtils.isBlank(item.getColorMatchMaterialName()) ? null : item.getColorMatchMaterialName())
                    .set(BomMaterialDemandTransient::getDemandNum, item.getDemandNum())
                    .set(BomMaterialDemandTransient::getDemandNumUnit, item.getDemandNumUnit())
                    .set(BomMaterialDemandTransient::getDemandNumUnitName, item.getDemandNumUnitName())
                    .set(BomMaterialDemandTransient::getDemandRemark, item.getDemandRemark())
                    .set(BomMaterialDemandTransient::getDemandPicture, item.getDemandPicture())
                    //需求关联的最新物料id, 有才更新
                    .set(Objects.nonNull(item.getLatestBomMaterialId()), BomMaterialDemandTransient::getLatestBomMaterialId, item.getLatestBomMaterialId())
                    .set(BomMaterialDemandTransient::getRevisedTime, LocalDateTime.now())
                    .set(Objects.nonNull(userContent), BomMaterialDemandTransient::getReviserId, userContent.getCurrentUserId())
                    .set(Objects.nonNull(userContent), BomMaterialDemandTransient::getReviserName, userContent.getCurrentUserName())
                    .eq(BomMaterialDemandTransient::getBomMaterialDemandId, item.getBomMaterialDemandId())
                    .update();
        });
    }

    public void updateMaterialTransient(List<BomOrderMaterialTransient> updateBomMaterialTransients, UserContent userContent) {
        if (CollUtil.isEmpty(updateBomMaterialTransients)) {
            return;
        }
        //不能用updateById来更新, 有些属性是可以更新为空的
        updateBomMaterialTransients.forEach(item -> {
            String colorMatchMaterialName = Objects.equals(item.getColorMatchMaterialState(), BomColorMaterialStateEnum.NO_THING.getCode())
                    ? null : item.getColorMatchMaterialName();
            materialTransientRepository.lambdaUpdate()
                    .set(BomOrderMaterialTransient::getPartUse, item.getPartUse())
                    .set(BomOrderMaterialTransient::getCuttingMethod, item.getCuttingMethod())
                    .set(BomOrderMaterialTransient::getPrototypeMaterialName, item.getPrototypeMaterialName())
                    .set(BomOrderMaterialTransient::getIsNoCraft, item.getIsNoCraft())
                    .set(BomOrderMaterialTransient::getColorMatchMaterialState, item.getColorMatchMaterialState())
                    .set(BomOrderMaterialTransient::getColorMatchMaterialName, StringUtils.isBlank(colorMatchMaterialName) ? null : colorMatchMaterialName)
                    .set(BomOrderMaterialTransient::getMinPrice, item.getMinPrice())
                    .set(BomOrderMaterialTransient::getPriceInvalidTime, item.getPriceInvalidTime())
                    .set(BomOrderMaterialTransient::getSamplePurchasingCycle, item.getSamplePurchasingCycle())
                    .set(BomOrderMaterialTransient::getBulkPurchasingCycle, item.getBulkPurchasingCycle())
                    .set(BomOrderMaterialTransient::getRevisedTime, LocalDateTime.now())
                    .set(Objects.nonNull(userContent), BomOrderMaterialTransient::getReviserId, userContent.getCurrentUserId())
                    .set(Objects.nonNull(userContent), BomOrderMaterialTransient::getReviserName, userContent.getCurrentUserName())
                    .eq(BomOrderMaterialTransient::getBomMaterialId, item.getBomMaterialId())
                    .update();
        });
    }

    private String getDemandColorMatchName(BomOrderUpdateV3Req.UpdateBomMaterialDemandReq updateBomDemandReq) {
        String colorMatchMaterialName = Objects.equals(updateBomDemandReq.getColorMatchMaterialState(), BomColorMaterialStateEnum.NO_THING.getCode())
                ? null : updateBomDemandReq.getColorMatchMaterialName();
        colorMatchMaterialName = StringUtils.isBlank(colorMatchMaterialName) ? null : colorMatchMaterialName;
        return colorMatchMaterialName;
    }

    private String getColorMatchName(Integer colorMatchMaterialState, String colorMatchMaterialName) {
        String colorMatchName = Objects.equals(colorMatchMaterialState, BomColorMaterialStateEnum.NO_THING.getCode()) ? null : colorMatchMaterialName;
        colorMatchName = StringUtils.isBlank(colorMatchName) ? null : colorMatchName;
        return colorMatchName;
    }

    private void resetMaterialInfo(BomMaterialInfoDto updateMaterialInfoDto,
                                   BomOrderUpdateV3Req.DemandMaterialUpdateReq materialUpdateReq,
                                   BomOrderMaterialTransient updateMaterialTransient) {
        //最新的价格与采购周期信息
        if (Objects.isNull(materialUpdateReq.getSkuId()) || Objects.isNull(updateMaterialInfoDto)) {
            return;
        }
        Map<Long, ProductSkuVo> accessoriesSkuMap = updateMaterialInfoDto.getAccessoriesSkuMap();
        if (CollUtil.isEmpty(accessoriesSkuMap) || Objects.isNull(accessoriesSkuMap.get(materialUpdateReq.getSkuId()))) {
            return;
        }
        ProductSkuVo productSkuVo = accessoriesSkuMap.get(materialUpdateReq.getSkuId());

        updateMaterialTransient.setMinPrice(productSkuVo.getMinPrice());
        //价格有效期结束时间
        updateMaterialTransient.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());

        //样衣与大货周期
        updateMaterialTransient.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
        updateMaterialTransient.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
    }

    private BomMaterialInfoDto getUpdateMaterialInfoDto(List<BomOrderUpdateV3Req.UpdateBomMaterialDemandReq> updateDemandReqList) {
        //有匹配回复的需求更新,查询对应的辅料信息
        Set<Long> updateSkuIdSet = new HashSet<>();
        updateDemandReqList.forEach(updateDemandReq -> {
            //直接更新物料, 不是更换物料
            List<BomOrderUpdateV3Req.DemandMaterialUpdateReq> materialUpdateReqList = updateDemandReq.getMaterialUpdateReqList();
            if (CollUtil.isNotEmpty(materialUpdateReqList) && Objects.nonNull(materialUpdateReqList.get(0))) {
                BomOrderUpdateV3Req.DemandMaterialUpdateReq materialUpdateReq = materialUpdateReqList.get(0);
                if (Objects.nonNull(materialUpdateReq.getSkuId())) {
                    updateSkuIdSet.add(materialUpdateReq.getSkuId());
                }
            }
        });
        //根据skuId查询物料信息
        if (CollUtil.isEmpty(updateSkuIdSet)) {
            return null;
        }
        return bomMaterialCommonService.queryMaterialInfoBySkuId(updateSkuIdSet, MaterialDemandTypeEnum.ACCESSORIES);
    }

    private BomMaterialDemandTransient buildTransientUpdateDemand(BomOrderUpdateV3Req.UpdateBomMaterialDemandReq updateDemandReq) {
        BomMaterialDemandTransient updateDemandTransient = new BomMaterialDemandTransient();
        updateDemandTransient.setBomMaterialDemandId(updateDemandReq.getBomMaterialDemandId());
        updateDemandTransient.setPrototypeMaterialName(updateDemandReq.getPrototypeMaterialName());
        updateDemandTransient.setColorMatchMaterialState(updateDemandReq.getColorMatchMaterialState());
        updateDemandTransient.setColorMatchMaterialName(this.getDemandColorMatchName(updateDemandReq));
        updateDemandTransient.setDemandNum(updateDemandReq.getDemandNum());
        updateDemandTransient.setDemandNumUnit(updateDemandReq.getDemandNumUnit());
        updateDemandTransient.setDemandNumUnitName(updateDemandReq.getDemandNumUnitName());
        updateDemandTransient.setDemandRemark(StringUtils.isBlank(updateDemandReq.getDemandRemark()) ? null : updateDemandReq.getDemandRemark());
        if (CollUtil.isNotEmpty(updateDemandReq.getDemandPictureList())) {
            updateDemandTransient.setDemandPicture(StrUtil.join(StrUtil.COMMA, updateDemandReq.getDemandPictureList()));
        }
        return updateDemandTransient;
    }

    private BomMaterialInfoDto getMaterialInfoDto(List<BomOrderUpdateV3Req.DemandMaterialAddReq> replaceMaterialList) {
        //根据skuId查询物料信息
        Set<Long> skuIdSet = replaceMaterialList.stream()
                .map(BomOrderUpdateV3Req.DemandMaterialAddReq::getSkuId).collect(Collectors.toSet());
        SdpDesignException.notEmpty(skuIdSet, "辅料需求更换物料入参异常, 缺少skuId! ");
        return bomMaterialCommonService.queryMaterialInfoBySkuId(skuIdSet, MaterialDemandTypeEnum.ACCESSORIES);
    }

    private BomOrderMaterialTransient replaceNewMaterialTransient(BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq,
                                                                  BomOrderMaterialTransient replaceMaterial,
                                                                  BomMaterialInfoDto materialInfoDto) {

        //生成新的物料
        long bomMaterialTransientId = IdPool.getId();
        BomOrderMaterialTransient materialTransient = new BomOrderMaterialTransient();
        BeanUtils.copyProperties(replaceMaterial, materialTransient);
        materialTransient.setBomMaterialId(bomMaterialTransientId);
        materialTransient.setReplaceBomMaterialId(materialAddReq.getBomMaterialIdChange());
        materialTransient.setPrototypeMaterialName(materialAddReq.getPrototypeMaterialName());
        materialTransient.setPartUse(materialAddReq.getPartUse());
        materialTransient.setCuttingMethod(materialAddReq.getCuttingMethod());
        materialTransient.setColorMatchMaterialState(materialAddReq.getColorMatchMaterialState());
        materialTransient.setColorMatchMaterialName(this.getColorMatchName(materialAddReq.getColorMatchMaterialState(), materialAddReq.getColorMatchMaterialName()));

        //辅料信息
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Long skuId = materialAddReq.getSkuId();
        SdpDesignException.isTrue(CollUtil.isNotEmpty(accessoriesSkuMap), "辅料spu信息不存在! skuId:{}", skuId);
        ProductSkuVo productSkuVo = accessoriesSkuMap.get(skuId);
        SdpDesignException.notNull(productSkuVo, "辅料spu信息不存在! skuId:{}", skuId);
        materialTransient.setMinPrice(productSkuVo.getMinPrice());
        //价格有效期结束时间
        materialTransient.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());

        //样衣与大货周期
        materialTransient.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
        materialTransient.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());

        //创建物料快照
        Long materialSnapshotId = this.createSnapshot(materialAddReq, materialInfoDto, bomMaterialTransientId);

        //新增物料关联快照id
        materialTransient.setMaterialSnapshotId(materialSnapshotId);

        return materialTransient;
    }

    private Long createSnapshot(BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq, BomMaterialInfoDto materialInfoDto, long bomMaterialTransientId) {
        Long skuId = materialAddReq.getSkuId();
        String skuCode = materialAddReq.getSkuCode();
        String prototypeMaterialName = materialAddReq.getPrototypeMaterialName();

        //根据skuId查询履约信息
        Map<Long, ProductSkuVo> accessoriesSkuMap = materialInfoDto.getAccessoriesSkuMap();
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = materialInfoDto.getAccessoriesSpuMap();
        Map<Long, CategoryTreeMapVo> categoryTreeMap = materialInfoDto.getCategoryTreeMap();

        //生成物料快照, 暂存不需要同步履约
        ProductSkuVo productSkuVo = accessoriesSkuMap.get(skuId);
        SdpDesignException.notNull(productSkuVo, "更换物料-辅料sku信息不存在! skuCode:{}; 物料项目:{}", skuCode, prototypeMaterialName);
        ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(productSkuVo.getSpuId());
        SdpDesignException.notNull(productSpuInfoVo, "更换物料-辅料SPU信息不存在! skuCode:{};物料项目:{}",  skuCode, prototypeMaterialName);

        MaterialSnapshotCreateReq snapshotCreateReq = BomOrderMaterialConverter.buildAccessoriesMaterialSnapshotV3(bomMaterialTransientId, productSkuVo, productSpuInfoVo, categoryTreeMap);

        return materialSnapshotService.create(snapshotCreateReq);
    }


    // private BomOrderMaterialTransient replaceNewMaterialTransient(BomOrderUpdateV3Req.UpdateBomMaterialDemandReq updateDemandReq,
    //                                                               BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq,
    //                                                               BomOrderMaterialTransient replaceMaterial) {
    //     long bomMaterialTransientId = IdPool.getId();
    //     BomOrderMaterialTransient materialTransient = new BomOrderMaterialTransient();
    //     BeanUtils.copyProperties(replaceMaterial, materialTransient);
    //     materialTransient.setBomMaterialId(bomMaterialTransientId);
    //     materialTransient.setReplaceBomMaterialId(materialAddReq.getBomMaterialIdChange());
    //     materialTransient.setPrototypeMaterialName(materialAddReq.getPrototypeMaterialName());
    //     materialTransient.setPartUse(materialAddReq.getPartUse());
    //     materialTransient.setCuttingMethod(materialAddReq.getCuttingMethod());
    //     materialTransient.setColorMatchMaterialState(materialAddReq.getColorMatchMaterialState());
    //     materialTransient.setColorMatchMaterialName(materialAddReq.getColorMatchMaterialName());
    //     return materialTransient;
    // }


    private void addDemandTransientHandle(BomOrder bomOrder,
                                          List<BomOrderUpdateV3Req.AddBomMaterialDemandReq> demandAddReqList,
                                          List<CraftDemandSaveV3Req> addCraftReqList,
                                          Long bomTransientId
                                          ) {
        if (CollUtil.isEmpty(demandAddReqList)) {
            return;
        }
        //新增需求, 无快照的物料, 工艺; 维护到暂存表中, 无需同步履约
        List<BomMaterialDemandTransient> addDemandTransientList = new LinkedList<>();
        List<BomOrderMaterialTransient> addMaterialTransientList = new LinkedList<>();
        List<DesignRemarksReq> designRemarksReqList = new LinkedList<>();

        Long bomId = bomOrder.getBomId();
        //1, 新增暂存需求处理
        demandAddReqList.forEach(item -> {
            //创建需求
            BomMaterialDemandTransient demandTransient = this.buildDemandTransientCreateEo(bomId, item, bomTransientId);
            //预先创建一个无物料快照的bom物料
            BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq = item.getMaterialAddReq();
            BomOrderMaterialTransient bomOrderMaterialTransient = this.buildSearchingMaterialTransient(bomId, demandTransient, materialAddReq, bomTransientId);
            addMaterialTransientList.add(bomOrderMaterialTransient);
            //关联最新物料id
            demandTransient.setLatestBomMaterialId(bomOrderMaterialTransient.getBomMaterialId());
            addDemandTransientList.add(demandTransient);
            //工艺
            if (Objects.nonNull(materialAddReq) && CollUtil.isNotEmpty(materialAddReq.getAddCraftDemandList())) {
                List<CraftDemandSaveV3Req> addCraftDemandList = materialAddReq.getAddCraftDemandList();
                //设置物料id
                addCraftDemandList.forEach(craft -> {
                    craft.setBomMaterialDemandId(demandTransient.getBomMaterialDemandId());
                    craft.setBomMaterialId(bomOrderMaterialTransient.getBomMaterialId());
                });
                addCraftReqList.addAll(materialAddReq.getAddCraftDemandList());
            }

            //物料备注
            if (StringUtils.isNotBlank(materialAddReq.getRemark())) {
                designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomTransientId, bomOrderMaterialTransient.getBomMaterialId(), materialAddReq.getRemark()));
            }
        });

        //新增暂存需求与物料
        materialDemandTransientRepository.saveBatch(addDemandTransientList);
        materialTransientRepository.saveBatch(addMaterialTransientList);

        //添加物料备注
        this.addDesignRemark(designRemarksReqList);
    }

    private void addDesignRemark(List<DesignRemarksReq> designRemarksReqList) {
        //新增物料备注
        designRemarksReqList.stream()
                .filter(designRemark -> StringUtils.isNotBlank(designRemark.getRemark()))
                .forEach(designRemarksService::create);
    }

    private BomOrderMaterialTransient buildSearchingMaterialTransient(Long bomId,
                                                                      BomMaterialDemandTransient demandTransient,
                                                                      BomOrderUpdateV3Req.DemandMaterialAddReq materialAddReq,
                                                                      Long bomTransientId) {
        long bomMaterialTransientId = IdPool.getId();
        return BomOrderMaterialTransient.builder()
                .bomMaterialId(bomMaterialTransientId)
                .bomMaterialDemandId(demandTransient.getBomMaterialDemandId())
                .bomId(bomId)
                .bomTransientId(bomTransientId)
                .bomMaterialType(MaterialDemandTypeEnum.ACCESSORIES.getCode())
                .partUse(materialAddReq.getPartUse())
                .prototypeMaterialName(materialAddReq.getPrototypeMaterialName())
                .colorMatchMaterialState(materialAddReq.getColorMatchMaterialState())
                .colorMatchMaterialName(materialAddReq.getColorMatchMaterialName())
                .materialState(BomMaterialStateEnum.SEARCHING.getCode())
                .materialContextId(IdPool.getId())
                .build();
    }

    private BomMaterialDemandTransient buildDemandTransientCreateEo(Long bomId,
                                                                    BomOrderUpdateV3Req.AddBomMaterialDemandReq req,
                                                                    Long bomTransientId) {
        long materialDemandTransientId = IdPool.getId();
        BomMaterialDemandTransient demand = new BomMaterialDemandTransient();
        BeanUtils.copyProperties(req, demand);
        demand.setBomMaterialDemandId(materialDemandTransientId)
                .setOriginDemandId(null)
                .setBomId(bomId)
                .setBomTransientId(bomTransientId)
                .setMaterialDemandType(MaterialDemandTypeEnum.ACCESSORIES.getCode())
                .setDemandState(BomDemandStateEnum.INITIAL.getCode())
                //新建的需求默认找料中
                .setMaterialSearchState(Bool.YES.getCode());
        if (CollUtil.isNotEmpty(req.getDemandPictureList())) {
            demand.setDemandPicture(StrUtil.join(StrUtil.COMMA, req.getDemandPictureList()));
        }

        return demand;
    }
}
