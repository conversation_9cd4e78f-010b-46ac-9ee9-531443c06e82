package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.enumeration.Bool;
import com.alibaba.excel.EasyExcel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import team.aikero.blade.core.exception.BusinessException;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.clothes.enums.MakeClothesTypeEnum;
import tech.tiangong.sdp.clothes.vo.resp.SampleClothesVo;
import tech.tiangong.sdp.clothes.vo.resp.audit.SampleAuditDimensionPictureVo;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.SdpStyleTypeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualSpuDimensionStateEnum;
import tech.tiangong.sdp.design.enums.visual.VisualSpuTypeEnum;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.remote.SampleClothesRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.BomOrderService;
import tech.tiangong.sdp.design.service.VisualSpuService;
import tech.tiangong.sdp.design.service.VisualTaskService;
import tech.tiangong.sdp.design.vo.dto.spot.StyleHisDataUploadExcelDto;
import tech.tiangong.sdp.utils.StreamUtil;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (VisualSpu)服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VisualSpuServiceImpl implements VisualSpuService {
    private final VisualSpuRepository visualSpuRepository;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final DesignStyleRepository designStyleRepository;
    private final PrototypeRepository prototypeRepository;
    private final SpotSpuRepository spotSpuRepository;
    private final SpotSkcRepository spotSkcRepository;
    private final DigitalPrintingStyleRepository digitalPrintingStyleRepository;
    private final DigitalPrintingPrototypeRepository digitalPrintingPrototypeRepository;
    private final VisualTaskRepository visualTaskRepository;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final BomOrderService bomOrderService;

    @Lazy
    @Resource
    private VisualTaskService visualTaskService;

    @Override
    public VisualSpu initVisualSpu(String styleCode){
        VisualSpu visualSpu = visualSpuRepository.getByStyleCode(styleCode);
        if(visualSpu==null) {
            visualSpu = new VisualSpu();
            visualSpu.setVisualSpuId(IdPool.getId());
            visualSpu.setStyleCode(styleCode);
            visualSpu.setIsMultiColor(Bool.NO.getCode());
            visualSpu.setDimensionState(VisualSpuDimensionStateEnum.NO_DIMENSION.getCode());
        }
        //设计款
        DesignStyle designStyle = designStyleRepository.getByStyleCode(styleCode);
        if(designStyle!=null) {
            BeanUtils.copyProperties(designStyle,visualSpu,"creatorId","creatorName","createdTime","reviserId","reviserName","revisedTime","tenantId");
            visualSpu.setSpuCreatedTime(designStyle.getCreatedTime());
            visualSpu.setStyleType(VisualSpuTypeEnum.DESIGN_STYLE.getCode());
            //3D打版状态
            VisualSpuDimensionStateEnum dimensionState = getDimensionStateByStyleCode(styleCode);
            visualSpu.setDimensionState(dimensionState.getCode());
            //是否多色, 动销
            visualSpu.setIsOnSale(Bool.NO.getCode());
            List<Prototype> prototypes = prototypeRepository.listByStyleCode(styleCode);
            Assert.isTrue(CollUtil.isNotEmpty(prototypes),"设计款"+styleCode+"下的没有SKC");
            if (CollUtil.isNotEmpty(prototypes)) {
                boolean allSkcIsCancel = prototypes.stream().allMatch(Prototype::getIsCanceled);
                Assert.isTrue(!allSkcIsCancel,"设计款"+styleCode+"下的SKC都已取消");
                if (prototypes.size() > 1) {
                    visualSpu.setIsMultiColor(Bool.YES.getCode());
                }
                prototypes = StreamUtil.filter(prototypes, Prototype::getIsOnSale);
                Prototype onSaleSkc = CollUtil.isNotEmpty(prototypes) ? prototypes.getFirst() : null;
                if (Objects.nonNull(onSaleSkc)) {
                    visualSpu.setIsOnSale(Bool.YES.getCode());
                }
            }
            //是否花型  判断SPU下SKC的bom中是否存在花型面料
            visualSpu.setFlowerState(Bool.NO.getCode());
            Boolean flowState = bomOrderService.checkFlowerState(styleCode);
            if (flowState) {
                visualSpu.setFlowerState(Bool.YES.getCode());
            }
        }else{
            //现货款
            SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
            if(spotSpu!=null) {
                //是否多色
                List<SpotSkc> spotSkcList = spotSkcRepository.listByStyleCode(styleCode);
                Assert.isTrue(CollUtil.isNotEmpty(spotSkcList),"现货款"+styleCode+"下的没有SKC");
                boolean allSkcIsCancel = spotSkcList.stream().allMatch(v->Bool.YES.getCode()==v.getIsCanceled());
                Assert.isTrue(!allSkcIsCancel,"现货款"+styleCode+"下的SKC都已取消");
                if (spotSkcList.size() > 1) {
                    visualSpu.setIsMultiColor(Bool.YES.getCode());
                }
                BeanUtils.copyProperties(spotSpu,visualSpu,"creatorId","creatorName","createdTime","reviserId","reviserName","revisedTime","tenantId");
                visualSpu.setSpuCreatedTime(spotSpu.getCreatedTime());
                visualSpu.setStyleType(VisualSpuTypeEnum.SPOT_SPU.getCode());
            }else{
                //数码印花款
                DigitalPrintingStyle digitalPrintingStyle = digitalPrintingStyleRepository.getByStyleCode(styleCode);
                if(digitalPrintingStyle!=null){
                    List<DigitalPrintingPrototype> printingPrototypes = digitalPrintingPrototypeRepository.listBySpu(styleCode);
                    Assert.isTrue(CollUtil.isNotEmpty(printingPrototypes),"数码印花款"+styleCode+"下的没有SKC");
                    if (printingPrototypes.size() > 1) {
                        visualSpu.setIsMultiColor(Bool.YES.getCode());
                    }
                    BeanUtils.copyProperties(digitalPrintingStyle,visualSpu,"creatorId","creatorName","createdTime","reviserId","reviserName","revisedTime","tenantId");
                    //印花款买手取选中人
                    visualSpu.setBuyerId(digitalPrintingStyle.getChosenId());
                    visualSpu.setBuyerName(digitalPrintingStyle.getChosenName());
                    visualSpu.setSpuCreatedTime(digitalPrintingStyle.getCreatedTime());
                    visualSpu.setStyleType(VisualSpuTypeEnum.DIGITAL_PRINTING_STYLE.getCode());
                }
            }
        }
        Assert.isTrue(visualSpu.getStyleType()!=null,"款式类型错误");

        visualSpu.setIsDeleted(Bool.NO.getCode());
        visualSpuRepository.saveOrUpdate(visualSpu);
        return visualSpu;
    }

    @Override
    public void changeDimensionState(Collection<String> styleCodes){
        if(CollUtil.isEmpty(styleCodes)){
            return;
        }
        for(String styleCode : styleCodes){
            VisualSpu visualSpu = visualSpuRepository.getByStyleCode(styleCode);
            if(visualSpu == null){
                log.warn("更新视觉中心SPU的3D任务状态失败：根据styleCode:{}没有找到对应信息",styleCode);
                continue;
            }
            VisualSpuDimensionStateEnum dimensionState = getDimensionStateByStyleCode(styleCode);
            VisualSpu updateVisualSpu = new VisualSpu();
            updateVisualSpu.setVisualSpuId(visualSpu.getVisualSpuId());
            updateVisualSpu.setDimensionState(dimensionState.getCode());
            visualSpuRepository.updateById(updateVisualSpu);
        }
    }

    @Override
    public void updateSpu(String styleCode, SdpStyleTypeEnum styleTypeEnum, String platformName) {
        if (StrUtil.isBlank(styleCode) || Objects.isNull(styleTypeEnum)) {
            return;
        }
        VisualSpu visualSpu = visualSpuRepository.getByStyleCode(styleCode);
        if (Objects.isNull(visualSpu)) {
            return;
        }
        switch (styleTypeEnum) {
            case DESIGN:
                this.updateDesignSpuInfo(styleCode, visualSpu); break;
            case SPOT:
                this.updateSpotSpuInfo(styleCode, visualSpu, platformName); break;
            default:
                break;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSpu(List<String> styleCodes) {
        //不取消SPU，只取消对应的在途任务
        List<VisualTask> visualTasks = visualTaskRepository.listLatestVisualTaskBySpu(styleCodes);
        if(CollectionUtil.isNotEmpty(visualTasks)){
            visualTaskService.doCancelVisualTask(visualTasks,true);
        }
    }

    private void updateDesignSpuInfo(String styleCode, VisualSpu visualSpu) {
        DesignStyle designStyle = designStyleRepository.getByStyleCode(styleCode);
        visualSpuRepository.update4Design(visualSpu.getVisualSpuId(), designStyle);
    }

    private void updateSpotSpuInfo(String styleCode, VisualSpu visualSpu, String platformName) {
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(styleCode);
        visualSpuRepository.update4Spot(visualSpu.getVisualSpuId(), spotSpu, platformName);
    }

    private VisualSpuDimensionStateEnum getDimensionStateByStyleCode(String styleCode){
        VisualSpuDimensionStateEnum dimensionState = VisualSpuDimensionStateEnum.NO_DIMENSION;
        //查询打版服务
        List<SampleClothesVo> clothesVoList = sampleClothesRemoteHelper.listByStyleCode(styleCode);
        if (!CollUtil.isEmpty(clothesVoList)) {
            //找出未被取消的，有3D打版任务的版单
            clothesVoList = clothesVoList.stream()
                    .filter(v->v.getIsLatest().equals(Bool.YES.getCode())
                                    && v.getIsCancel().equals(Bool.NO.getCode())
                                    && v.getMakeClothesType()!=null
                                    && (MakeClothesTypeEnum.THREE_DIMENSION_REAL_SAMPLE.getCode().equals(v.getMakeClothesType().getCode())
                                    || MakeClothesTypeEnum.THREE_DIMENSION_SAMPLE.getCode().equals(v.getMakeClothesType().getCode())))
                    .collect(Collectors.toList());
            //有3D任务
            if(!CollUtil.isEmpty(clothesVoList)){
                List<SampleAuditDimensionPictureVo> list = sampleClothesRemoteHelper.listAuditPassDimensionPictureByStyleCode(styleCode);
                //只要有一条审版通过3D任务，则为已完成
                if(!CollUtil.isEmpty(list)){
                    dimensionState = VisualSpuDimensionStateEnum.FINISHED;
                }else{
                    dimensionState = VisualSpuDimensionStateEnum.DOING;
                }
            }
        }
        return dimensionState;
    }


    @Override
    public void updateOnSale(String styleCode, Integer isOnSale) {
        VisualSpu visualSpu = visualSpuRepository.getByStyleCode(styleCode);
        if (Objects.isNull(visualSpu) || Objects.isNull(isOnSale)) {
            return;
        }

        VisualSpu updateVisualSpu = new VisualSpu();
        updateVisualSpu.setVisualSpuId(visualSpu.getVisualSpuId());
        updateVisualSpu.setIsOnSale(isOnSale);
        visualSpuRepository.updateById(updateVisualSpu);
    }

    @Override
    public void updateMultiColor(String styleCode, Integer isMultiColor) {
        VisualSpu visualSpu = visualSpuRepository.getByStyleCode(styleCode);
        if (Objects.isNull(visualSpu) || Objects.isNull(isMultiColor)) {
            return;
        }

        VisualSpu updateVisualSpu = new VisualSpu();
        updateVisualSpu.setVisualSpuId(visualSpu.getVisualSpuId());
        updateVisualSpu.setIsMultiColor(isMultiColor);
        visualSpuRepository.updateById(updateVisualSpu);
    }

    @Override
    public void initHisDataV2(MultipartFile file){
        List<StyleHisDataUploadExcelDto> uploadDtoList = new ArrayList<>();
        try {
            uploadDtoList = EasyExcel.read(file.getInputStream())
                    .head(StyleHisDataUploadExcelDto.class)
                    .sheet()
                    .doReadSync();

        } catch (IOException e) {
            log.error("导入失败", e);
            throw new BusinessException("导入失败", e);
        }
        if (CollectionUtil.isEmpty(uploadDtoList)) {
            throw new BusinessException("导入数据为空");
        }
        Map<String, StyleHisDataUploadExcelDto> spuMap = uploadDtoList.stream().collect(Collectors.toMap(StyleHisDataUploadExcelDto::getSpuCode, t -> t));
        List<String> spuCodeList = uploadDtoList.stream().map(StyleHisDataUploadExcelDto::getSpuCode).distinct().toList();
        List<VisualSpu> visualSpuList = visualSpuRepository.listByStyleCodes(spuCodeList);
        Assert.isTrue(CollectionUtil.isNotEmpty(visualSpuList), "未找到对应的款式");
        // 市场风格
        Map<String, String> marketStyleMap = dictValueRemoteHelper.getMarketNameMap(3);
        List<VisualSpu> updateSpuList = new ArrayList<>();
        for(VisualSpu visualSpu : visualSpuList){
            StyleHisDataUploadExcelDto excelDto = spuMap.get(visualSpu.getStyleCode());
            if(excelDto == null){
                continue;
            }
            if(StrUtil.isNotBlank(excelDto.getMarketName() ) && StrUtil.isNotBlank(excelDto.getMarketSeriesName()) && StrUtil.isNotBlank(excelDto.getMarketStyleName())){
                visualSpu.setClothingStyleCode(marketStyleMap.get(excelDto.getMarketName() + "-" + excelDto.getMarketSeriesName() + "-" + excelDto.getMarketStyleName()));
                if(StrUtil.isNotBlank(visualSpu.getClothingStyleCode())){
                    visualSpu.setClothingStyleName(excelDto.getMarketStyleName());
                }
            }
            updateSpuList.add(visualSpu);
        }

        visualSpuRepository.updateBatchById(updateSpuList);
    }


    @Override
    public void updateFlowerState(String styleCode, Integer flowerState) {
        VisualSpu visualSpu = visualSpuRepository.getByStyleCode(styleCode);
        if (Objects.isNull(visualSpu) || Objects.isNull(flowerState)) {
            return;
        }
        if (Objects.equals(flowerState, visualSpu.getFlowerState())) {
            return;
        }

        VisualSpu updateVisualSpu = new VisualSpu();
        updateVisualSpu.setVisualSpuId(visualSpu.getVisualSpuId());
        updateVisualSpu.setFlowerState(flowerState);
        visualSpuRepository.updateById(updateVisualSpu);
    }
}
