package tech.tiangong.sdp.design.listener.handler;

import cn.hutool.core.collection.CollectionUtil;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.Ability;
import tech.tiangong.sdp.design.enums.TaskStatus;
import tech.tiangong.sdp.design.enums.visual.VisualTaskHandleStateEnum;
import tech.tiangong.sdp.design.listener.AIBoxTaskHandler;
import tech.tiangong.sdp.design.listener.entity.AIBoxMQTaskResult;
import tech.tiangong.sdp.design.listener.entity.AIBoxTaskNotification;
import tech.tiangong.sdp.design.listener.entity.Tag;
import tech.tiangong.sdp.design.repository.*;

import java.util.List;
import java.util.Objects;

/**
 * 视觉修图中处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VisualOnShelfHandler implements AIBoxTaskHandler {

    private final VisualTaskRepository visualTaskRepository;
    private final VisualTaskOnShelfRepository visualTaskOnShelfRepository;
    private final VisualOnShelfAiBoxImageRepository visualOnShelfAiBoxImageRepository;
    private final VisualOnShelfAiBoxLogRepository visualOnShelfAiBoxLogRepository;

    @Override
    public Tag getSupportedTag() {
        return Tag.VISUAL_ON_SHELF_HANDLE;
    }

    @Override
    public void handleTaskCreation(AIBoxTaskNotification taskNotification) {
        log.info("创建视觉修图中任务: taskId={}", taskNotification.aiBoxTaskId());

        VisualTaskOnShelf visualTaskOnShelf = visualTaskOnShelfRepository.getById(taskNotification.taskSourceId());
        VisualTask visualTask = visualTaskRepository.getById(visualTaskOnShelf.getTaskId());

        VisualOnShelfAiBoxLog logEntity = buildAiBoxLog(taskNotification, visualTaskOnShelf, visualTask);
        visualOnShelfAiBoxLogRepository.save(logEntity);

        log.info("视觉修图中任务创建成功: taskId={}", taskNotification.aiBoxTaskId());

    }

    @Override
    public void handleTaskUpdate(AIBoxTaskNotification taskNotification) {
        log.info("更新视觉修图中任务: taskId={}, status={}",
                taskNotification.aiBoxTaskId(), taskNotification.status());

        // 更新任务状态
        updateTaskStatus(taskNotification);

        // 保存结果图片
        if (taskNotification.status() == TaskStatus.COMPLETED) {
            saveTaskResults(taskNotification);
        }

        log.info("视觉修图中任务更新成功: taskId={}", taskNotification.aiBoxTaskId());

    }

    private String[] parseTaskSourceId(String taskSourceId) {
        String[] parts = taskSourceId.split(":", 2);
        if (parts.length != 2) {
            log.error("视觉修图中任务源ID格式错误: {}", taskSourceId);
            return null;
        }
        return parts;
    }

    private VisualOnShelfAiBoxLog buildAiBoxLog(
            AIBoxTaskNotification taskNotification,
            VisualTaskOnShelf visualTaskOnShelf,
            VisualTask visualTask) {
        return VisualOnShelfAiBoxLog.builder()
                .visualOnShelfAiBoxLogId(IdPool.getId())
                .onShelfDetailId(visualTaskOnShelf.getOnShelfDetailId())
                .taskId(visualTaskOnShelf.getTaskId())
                .taskCode(visualTask.getProcessCode())
                .aiBoxTaskState(VisualTaskHandleStateEnum.DOING.getCode())
                .aiBoxTaskId(taskNotification.aiBoxTaskId())
                .ability(taskNotification.ability())
                .build();
    }

    private void updateTaskStatus(AIBoxTaskNotification taskNotification) {
        Integer status = taskNotification.status() == TaskStatus.COMPLETED
                ? VisualTaskHandleStateEnum.FINISH.getCode()
                : VisualTaskHandleStateEnum.FAIL.getCode();

        VisualOnShelfAiBoxLog logEntity = visualOnShelfAiBoxLogRepository
                .lambdaQuery()
                .eq(VisualOnShelfAiBoxLog::getAiBoxTaskId, taskNotification.aiBoxTaskId())
                .one();

        if (logEntity != null) {
            logEntity.setAiBoxTaskState(status);
            visualOnShelfAiBoxLogRepository.updateById(logEntity);
        }
    }

    private void saveTaskResults(AIBoxTaskNotification taskNotification) {
        if (CollectionUtil.isEmpty(taskNotification.results())) {
            return;
        }

        VisualOnShelfAiBoxLog logEntity = visualOnShelfAiBoxLogRepository
                .lambdaQuery()
                .eq(VisualOnShelfAiBoxLog::getAiBoxTaskId, taskNotification.aiBoxTaskId())
                .one();

        List<VisualOnShelfAiBoxImage> imageList = taskNotification.results().stream()
                .map(result -> buildAiBoxImage(taskNotification, logEntity, result))
                .toList();

        visualOnShelfAiBoxImageRepository.saveBatch(imageList);
    }

    private VisualOnShelfAiBoxImage buildAiBoxImage(
            AIBoxTaskNotification taskNotification,
            VisualOnShelfAiBoxLog logEntity,
            AIBoxMQTaskResult result) {

        VisualOnShelfAiBoxImage image = VisualOnShelfAiBoxImage.builder()
                .visualOnShelfAiBoxImageId(IdPool.getId())
                .onShelfDetailId(logEntity.getOnShelfDetailId())
                .taskId(logEntity.getTaskId())
                .visualOnShelfAiBoxLogId(logEntity.getVisualOnShelfAiBoxLogId())
                .aiBoxTaskId(taskNotification.aiBoxTaskId())
                .ability(taskNotification.ability())
                .generatedImg(result.url())
                .generatedImgName(getFileNameFromOssUrl(result.url()))
                .build();

        if (!result.attributes().isNull()) {
            image.setAttributes(result.attributes());
            // 处理TRY_ON类型的特殊属性
            if (Objects.equals(taskNotification.ability(), Ability.TRY_ON.name())
                    && result.attributes().has("modelName")) {   //参考 DifyTryOnAttribute
                image.setModel(result.attributes().get("modelName").asText());
            }
        }

        return image;
    }

    private String getFileNameFromOssUrl(String ossUrl) {
        if (StringUtil.isBlank(ossUrl)) {
            return "";
        }
        // 提取文件名
        int lastSlashIndex = ossUrl.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < ossUrl.length() - 1) {
            return ossUrl.substring(lastSlashIndex + 1);
        }

        // 如果没有找到斜杠，返回空字符串
        return "";
    }
}