package tech.tiangong.sdp.design.vo.resp.prototype;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.List;

/**
 * 设计款管理 skc信息
 *
 * <AUTHOR>
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
public class PrototypeInfoVo extends PrototypeVo {
    @Serial
    private static final long serialVersionUID = -969431200718977481L;

    /**
     * 颜色信息集合
     */
    private List<ColorInfoVo> colorInfoList;
}
