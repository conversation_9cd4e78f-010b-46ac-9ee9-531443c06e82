package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.entity.BaseEntity;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.collection.Collections;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.BomOrderMaterial;
import tech.tiangong.sdp.design.enums.BomColorMaterialStateEnum;
import tech.tiangong.sdp.design.enums.BomMaterialStateEnum;
import tech.tiangong.sdp.design.mapper.BomOrderMaterialMapper;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialExcelResp;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 干什么
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/13 14:59
 */
@Repository
public class BomOrderMaterialRepository extends BaseRepository<BomOrderMaterialMapper, BomOrderMaterial> {


    /**
     * 根据bomId获取物料集合(不包含暂存的)
     */
    public List<BomOrderMaterial> getListByBomId(Long bomId) {
        return baseMapper.selectList(new QueryWrapper<BomOrderMaterial>().lambda()
                .eq(BomOrderMaterial::getBomId, bomId)
                .eq(BomOrderMaterial::getIsDeleted, Bool.NO.getCode())
                .eq(BomOrderMaterial::getIsTransient, Bool.NO.getCode()));
    }


    /**
     * 根据bomId获取物料集合(不包含暂存的 物料状态为正常的)
     */
    public List<BomOrderMaterial> getNormalListByBomId(Long bomId) {
        return baseMapper.selectList(new QueryWrapper<BomOrderMaterial>().lambda()
                .eq(BomOrderMaterial::getBomId, bomId)
                .eq(BomOrderMaterial::getIsDeleted, Bool.NO.getCode())
                .eq(BomOrderMaterial::getIsTransient, Bool.NO.getCode())
                .eq(BomOrderMaterial::getMaterialState, BomMaterialStateEnum.NORMAL.getCode()));
    }


    /**
     * 根据bomId获取物料集合(包含暂存的)
     */
    public List<BomOrderMaterial> listByBomId(Long bomId) {
        return baseMapper.selectList(new QueryWrapper<BomOrderMaterial>().lambda()
                .eq(BomOrderMaterial::getBomId, bomId)
                .eq(BomOrderMaterial::getIsDeleted, Bool.NO.getCode()));
    }

    /**
     * 根据bomId获取暂存物料集合
     */
    public List<BomOrderMaterial> listTransientByBomId(Long bomId) {
        return baseMapper.selectList(new QueryWrapper<BomOrderMaterial>().lambda()
                .eq(BomOrderMaterial::getBomId, bomId)
                .eq(BomOrderMaterial::getIsDeleted, Bool.NO.getCode())
                .eq(BomOrderMaterial::getIsTransient, Bool.YES.getCode()));
    }

    public List<BomOrderMaterial> getListByBomMaterialIds(List<Long> bomMaterialIds) {
        return baseMapper.selectList(new QueryWrapper<BomOrderMaterial>().lambda().in(BomOrderMaterial::getBomMaterialId, bomMaterialIds)
                .eq(BomOrderMaterial::getIsDeleted, Bool.NO.getCode()));

    }

    public List<BomOrderMaterial> getListByBomIdList(List<Long> bomIdList) {
        if (CollUtil.isEmpty(bomIdList)) {
            return List.of();
        }
        return lambdaQuery().in(BomOrderMaterial::getBomId, bomIdList)
                .eq(BomOrderMaterial::getIsDeleted, Bool.NO.getCode())
                .list();
    }

    /**
     * 查询选料的物料 (不包含找料的辅料)
     */
    public List<BomOrderMaterial> listChooseMaterial(List<Long> bomIdList, Integer bomMaterialType) {
        if (CollUtil.isEmpty(bomIdList)) {
            return List.of();
        }
        return lambdaQuery()
                .in(BomOrderMaterial::getBomId, bomIdList)
                .eq(Objects.nonNull(bomMaterialType), BomOrderMaterial::getBomMaterialType, bomMaterialType)
                .isNull(BomOrderMaterial::getBomMaterialDemandId)
                .eq(BomOrderMaterial::getIsDeleted, Bool.NO.getCode())
                .list();
    }

    /**
     * 根据需求id集合查询物料
     */
    public List<BomOrderMaterial> listByDemandIds(List<Long> delDemandIdList) {
        if (CollUtil.isEmpty(delDemandIdList)) {
            return List.of();
        }
        return lambdaQuery()
                .in(BomOrderMaterial::getBomMaterialDemandId, delDemandIdList)
                .eq(BomOrderMaterial::getIsDeleted, Bool.NO.getCode())
                .list();
    }

    /**
     * 根据物料快照id获取最新bom单物料信息
     *
     * @param materialSnapshotIds 物料快照id
     * @return
     */
    public List<BomOrderMaterial> latestByMaterialSnapshotId(List<Long> materialSnapshotIds) {
        if (Collections.isEmpty(materialSnapshotIds)) {
            return new ArrayList<>();
        }
        return getByIds(materialSnapshotIds, BomOrderMaterial::getMaterialSnapshotId, BomOrderMaterial::getMaterialSnapshotId);
    }

    /**
     * 根据物料需求id获取最新bom单物料信息
     *
     * @param bomMaterialDemandIds 物料需求id
     * @return
     */
    public List<BomOrderMaterial> latestByBomMaterialDemandIds(List<Long> bomMaterialDemandIds) {
        if (Collections.isEmpty(bomMaterialDemandIds)) {
            return new ArrayList<>();
        }
        return getByIds(bomMaterialDemandIds, BomOrderMaterial::getBomMaterialDemandId, BomOrderMaterial::getBomMaterialDemandId);
    }

	public List<BomOrderMaterial> getByIds(List<Long> ids, SFunction<BomOrderMaterial, ?> conditionFunction, Function<BomOrderMaterial, Long> groupingByFunction) {
		Map<Long, List<BomOrderMaterial>> bomMaterialMap = list(new QueryWrapper<BomOrderMaterial>().lambda().in(conditionFunction, ids))
				.stream()
				//找料中的需求可能对色的是选料, 虽是找料中,但对应的对色物料信息也要查出来
				// .filter(e ->Objects.nonNull(e.getMaterialSnapshotId()))
				.collect(Collectors.groupingBy(groupingByFunction));
		List<BomOrderMaterial> bomOrderMaterialList = new ArrayList<>();
		bomMaterialMap.forEach((key, value) -> bomOrderMaterialList.add(value.stream().max(Comparator.comparing(BaseEntity::getCreatedTime)).orElseGet(BomOrderMaterial::new)));
		return bomOrderMaterialList;
	}

    public List<BomOrderMaterial> getOppositeColorMaterialByRelateMaterialName(List<BomOrderMaterial> bomOrderMaterialList) {
        if (Collections.isEmpty(bomOrderMaterialList)) {
            return new ArrayList<>();
        }
        return bomOrderMaterialList.stream().flatMap(e -> list(new QueryWrapper<BomOrderMaterial>().lambda()
                .eq(BomOrderMaterial::getBomId, e.getBomId())
                .eq(BomOrderMaterial::getColorMatchMaterialName, e.getPrototypeMaterialName())).stream()).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

	public List<BomOrderMaterial> getBeOppositeColorMaterialByColorMatchMaterialName(List<BomOrderMaterial> bomOrderMaterialList) {
		if (Collections.isEmpty(bomOrderMaterialList)) {
			return new ArrayList<>();
		}
		return bomOrderMaterialList.stream()
				.filter(e -> !Objects.equals(e.getColorMatchMaterialState(), BomColorMaterialStateEnum.NO_THING.getCode()) && Objects.nonNull(e.getColorMatchMaterialName()))
				.flatMap(e -> list(new QueryWrapper<BomOrderMaterial>().lambda()
				.eq(BomOrderMaterial::getBomId, e.getBomId())
				.eq(BomOrderMaterial::getPrototypeMaterialName, e.getColorMatchMaterialName())).stream()).filter(Objects::nonNull)
				.collect(Collectors.toList());
	}

    /**
     * 查询需求下找料中的物料
     */
    public BomOrderMaterial getSearchingOne(Long bomMaterialDemandId) {
        if (Objects.isNull(bomMaterialDemandId)) {
            return null;
        }
        return lambdaQuery()
                .eq(BomOrderMaterial::getBomMaterialDemandId, bomMaterialDemandId)
                .eq(BomOrderMaterial::getMaterialState, BomMaterialStateEnum.SEARCHING.getCode())
                .one();
    }

    public List<BomOrderMaterial> listByIds(List<Long> bomMaterialIds) {
        if (Collections.isEmpty(bomMaterialIds)) {
            return new ArrayList<>();
        }
        return super.listByIds(bomMaterialIds);
    }

    public Long updateMaterialType() {
        return baseMapper.updateMaterialType();
    }

    public void batchUpdatePurchaseCycle(List<BomOrderMaterial> updateList) {
    	baseMapper.batchUpdatePurchaseCycle(updateList);
    }

    /**
     * 根据bomId查询找料需求下的辅料
     * @param bomIdList bomId
     * @param isMatch true查询已匹配回复的物料;
     * @return 找料的物料
     */
    public List<BomOrderMaterial> listDemandMaterialByBomIds(List<Long> bomIdList, Boolean isMatch) {
        if (Collections.isEmpty(bomIdList)) {
            return Collections.emptyList();
        }
        return baseMapper.listDemandMaterialByBomIds(bomIdList, isMatch);
    }

    public List<BomMaterialExcelResp> listMaterialExcel(List<Long> bomIdList) {
        if (CollUtil.isEmpty(bomIdList)) {
            return Collections.emptyList();
        }
        return baseMapper.listMaterialExcel(bomIdList);
    }

    public List<BomMaterialExcelResp> listMaterialDemandExcel(List<Long> bomIdList) {
        if (CollUtil.isEmpty(bomIdList)) {
            return Collections.emptyList();
        }
        return baseMapper.listMaterialDemandExcel(bomIdList);
    }
}

