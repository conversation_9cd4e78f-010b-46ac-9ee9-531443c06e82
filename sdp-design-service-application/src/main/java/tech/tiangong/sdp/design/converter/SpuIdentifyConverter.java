package tech.tiangong.sdp.design.converter;

import cn.hutool.core.collection.CollUtil;
import tech.tiangong.sdp.design.entity.SpuIdentify;
import tech.tiangong.sdp.design.enums.SpuIdentifyTypeEnum;
import tech.tiangong.sdp.design.vo.resp.identity.MuseSpuIdentifyVo;

import java.util.List;
import java.util.Map;

/**
 * 款式识别 Converter
 *
 * <AUTHOR>
 * @date 2025/8/8 15:30
 */
public class SpuIdentifyConverter {


    public static <T> MuseSpuIdentifyVo buildIdentifyInfo(T bizKey, Map<T, List<SpuIdentify>> spuIdentifyGroupMap) {
        List<SpuIdentify> identifyList = spuIdentifyGroupMap.get(bizKey);
        if (CollUtil.isEmpty(identifyList)) {
            return null;
        }
        MuseSpuIdentifyVo identifyInfo = new MuseSpuIdentifyVo();

        //设置识别URL（取第一个识别记录的URL）
        identifyInfo.setMuseIdentifyUrl(identifyList.getFirst().getIdentifyUrl());

        //分别处理同款识别和侵权识别
        identifyList.forEach(identify -> {
            if (SpuIdentifyTypeEnum.SAME_STYLE.getCode().equals(identify.getIdentifyType())) {
                identifyInfo.setMuseSameStyleLevel(identify.getIdentifyLevel());
                identifyInfo.setMuseSameStyleIdentifyState(identify.getIdentifyState());
            } else if (SpuIdentifyTypeEnum.INFRINGEMENT.getCode().equals(identify.getIdentifyType())) {
                identifyInfo.setMuseInfringementLevel(identify.getIdentifyLevel());
                identifyInfo.setMuseInfringementIdentifyState(identify.getIdentifyState());
            }
        });
        return identifyInfo;
    }
}
