package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.DesignAsyncTask;
import tech.tiangong.sdp.design.enums.DownloadTaskStatusEnum;
import tech.tiangong.sdp.design.mapper.DesignAsyncTaskMapper;
import tech.tiangong.sdp.design.vo.query.download.DownloadTaskQuery;
import tech.tiangong.sdp.design.vo.resp.download.DesignAsyncTaskVo;

import java.util.List;
import java.util.Objects;


/**
 *
 * sync_task表(SyncTask)服务仓库类
 */
@Repository
public class DesignAsyncTaskRepository extends BaseRepository<DesignAsyncTaskMapper, DesignAsyncTask> {

    public IPage<DesignAsyncTaskVo> findPage(DownloadTaskQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public List<DesignAsyncTask> findPendingTask(Integer taskType) {
        return lambdaQuery()
                .eq(Objects.nonNull(taskType), DesignAsyncTask::getTaskType, taskType)
                .eq(DesignAsyncTask::getState, DownloadTaskStatusEnum.PENDING.getCode())
                .orderByAsc(DesignAsyncTask::getCreatedTime)
                .last("limit 10")
                .list();

    }
}
