package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.sdp.clothes.enums.ClothesSampleTypeEnum;
import tech.tiangong.sdp.clothes.enums.MakeClothesTypeEnum;
import tech.tiangong.sdp.clothes.vo.req.CreateSampleClothesReq;
import tech.tiangong.sdp.clothes.vo.req.DesignCodeListReq;
import tech.tiangong.sdp.clothes.vo.req.DesignCodeSpuQuery;
import tech.tiangong.sdp.clothes.vo.resp.ClothesProcessInfoVo;
import tech.tiangong.sdp.clothes.vo.resp.SampleClothesVo;
import tech.tiangong.sdp.clothes.vo.resp.audit.SampleAuditDimensionPictureVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.CheckPriceBaseInfoInnerVo;
import tech.tiangong.sdp.clothes.vo.resp.checkprice.DesignPricingInfoVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.converter.PrototypeManageConverter;
import tech.tiangong.sdp.design.converter.SpuIdentifyConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.enums.visual.VisualTaskTypeEnum;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.*;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.dto.ChangeDesignerDto;
import tech.tiangong.sdp.design.vo.dto.DesignDemandAttachmentBo;
import tech.tiangong.sdp.design.vo.dto.visual.BackgroundDTO;
import tech.tiangong.sdp.design.vo.dto.visual.ModelFaceDTO;
import tech.tiangong.sdp.design.vo.dto.visual.TryOnModelReferenceImageDTO;
import tech.tiangong.sdp.design.vo.query.prototype.PrototypeManageExcelQuery;
import tech.tiangong.sdp.design.vo.query.prototype.PrototypeManageQuery;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialPictureReq;
import tech.tiangong.sdp.design.vo.req.demand.DesignDemandCreateReq;
import tech.tiangong.sdp.design.vo.req.log.DesignLogReq;
import tech.tiangong.sdp.design.vo.req.manage.*;
import tech.tiangong.sdp.design.vo.req.mq.CancelPrototypeMqDTO;
import tech.tiangong.sdp.design.vo.req.mq.UrgentRemarkMqDTO;
import tech.tiangong.sdp.design.vo.req.prototype.ChgDesignerReq;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeManageInfoBatchListReq;
import tech.tiangong.sdp.design.vo.req.prototype.SampleRecodeCreateReq;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SkcBomSubmitReq;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualDemandBySpuReq;
import tech.tiangong.sdp.design.vo.req.visual.SpuVisualDemandRecordSaveReq;
import tech.tiangong.sdp.design.vo.req.zj.design.DesignerUpdateOpenV2Req;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialPictureResp;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderDetailVo;
import tech.tiangong.sdp.design.vo.resp.bom.CraftDemandInfoVo;
import tech.tiangong.sdp.design.vo.resp.prototype.*;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleVo;
import tech.tiangong.sdp.design.vo.resp.visual.SpuAigcImageVo;
import tech.tiangong.sdp.design.vo.resp.visual.SpuImageMaterial;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskListExtraVo;
import tech.tiangong.sdp.design.vo.resp.zj.design.PrototypeOrderMaterialOpenResp;
import tech.tiangong.sdp.material.pojo.dto.DesignerDTO;
import tech.tiangong.sdp.material.pojo.web.request.designer.DesignerRemoteReq;
import tech.tiangong.sdp.material.pojo.web.response.SizeTemplateImageVO;
import tech.tiangong.sdp.material.sdk.service.remote.DesignerRemoteService;
import tech.tiangong.sdp.utils.FileDownloader;
import tech.tiangong.sdp.utils.FolderCompressor;
import tech.tiangong.sdp.utils.StreamUtil;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设计款管理-服务
 *
 * <AUTHOR>
 * @since 2021-08-09 14:43:18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrototypeManageServiceImpl implements PrototypeManageService {

    private final PrototypeRepository prototypeRepository;
    private final PrototypeService prototypeService;
    private final PrototypeHistoryRepository prototypeHistoryRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final PrototypeManageRepository prototypeManageRepository;
    private final BomOrderService bomOrderService;
    private final BomOrderRepository bomOrderRepository;
    private final OrderMaterialFollowRepository orderMaterialFollowRepository;
    private final CraftDemandInfoService craftDemandInfoService ;
    private final MaterialPurchaseFollowService purchaseFollowService;
    private final MqProducer mqProducer;
    private final DesignLogService logService;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final DesignerRemoteService designerRemoteService;
    private final DesignStyleService designStyleService;
    private final DesignStyleRepository designStyleRepository;
    private final DesignDemandRepository designDemandRepository;
    private final SpuIdentifyRepository spuIdentifyRepository;
    private final DesignDemandDetailRepository designDemandDetailRepository;
    private final OnShelfSpuRepository onShelfSpuRepository;
    private final OnShelfSkcRepository onShelfSkcRepository;
    private final CheckPriceRemoteHelper checkPriceRemoteHelper;
    private final OrderMaterialFollowService orderMaterialFollowService;
    private final PrototypeSampleRecodeService prototypeSampleRecodeService;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;
    private final BomOperateService bomOperateService;
    private final PopProductHelper popProductHelper;
    private final VisualDemandRepository visualDemandRepository;
    private final VisualTaskRepository visualTaskRepository;
    private final SpuVisualDemandRecordRepository spuVisualDemandRecordRepository;
    private final SpuVisualDemandRecordService spuVisualDemandRecordService;
    private final VisualSpuService visualSpuService;
    private final ClothingFoundationRemoteHelper clothingFoundationRemoteHelper;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    @Lazy
    @Resource
    private VisualDemandService visualDemandService;
    @Autowired
    private DesignDemandAIBoxTaskResultRepository designDemandAIBoxTaskResultRepository;

    @Override
    public PageRespVo<PrototypeManageQueryResp> page(PrototypeManageQuery queryDTO) {
        UserContent userContent = UserContentHolder.get();
        //根据设计组查询
        if(Objects.equals(queryDTO.getClothesDesigner(), Bool.YES.getCode())){
            List<String> designerGroupCodeList = this.queryDesignerGroup(queryDTO, userContent);
            if (CollUtil.isEmpty(designerGroupCodeList)) {
                // 若用户不属于任意设计组则展示空数据
                return PageRespVoHelper.empty();
            }
            queryDTO.setDesignerGroupCodeList(designerGroupCodeList);
        }

        //条件分页查询
        Page<PrototypeManageQueryResp> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize())
                .doSelectPage(() -> prototypeManageRepository.listQuery(queryDTO));
        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getResult())) {
            return PageRespVoHelper.empty();
        }
        List<PrototypeManageQueryResp> list = page.getResult();
        List<String> designCodeList = list.stream().map(PrototypeManageQueryResp::getDesignCode).distinct().collect(Collectors.toList());

        //SPU信息
        List<String> styleCodeList = list.stream().map(PrototypeManageQueryResp::getStyleCode).distinct().collect(Collectors.toList());
        List<DesignStyle> styleList = designStyleRepository.listByStyleCodes(styleCodeList);
        SdpDesignException.notEmpty(styleList, "款式信息不存在! ");
        //设计需求信息
        List<Long> designDemandIdList = StreamUtil.convertListAndDistinct(styleList, DesignStyle::getDesignDemandId);
        Map<Long, DesignDemand> designDemandMap = this.getDesignDemandMap(designDemandIdList);
        Map<Long, DesignDemandDetail> designDemandDetailMap = this.getDesignDemandDetailMap(designDemandIdList);

        //开发bom单信息
        List<BomOrder> bomOrderList = bomOrderRepository.getListByDesignCodes(designCodeList);
        List<BomOrder> latestBomOrderList = new ArrayList<>();
        List<CraftDemandInfoVo> craftDemandInfoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(bomOrderList)) {
            Map<String, List<BomOrder>> bomOrderMap = bomOrderList.stream().collect(Collectors.groupingBy(BomOrder::getDesignCode));
            List<Long> bomIds = new ArrayList<>();
            latestBomOrderList = bomOrderMap.values().stream().filter(CollectionUtil::isNotEmpty).map(bomOrders -> {
                BomOrder bomOrder = bomOrders.stream().max(Comparator.comparing(BomOrder::getVersionNum)).orElse(new BomOrder());
                bomIds.add(bomOrder.getBomId());
                return bomOrder;
            }).collect(Collectors.toList());
            craftDemandInfoList = craftDemandInfoService.getListByBomIdsAndState(bomIds, CraftDemandStateEnum.SUBMIT.getCode());
        }

        //5,查询核价信息
        Map<String, CheckPriceBaseInfoInnerVo> checkedPriceMap = this.getCheckPriceMap(list);

        //封装返回对象
        PrototypeManageConverter.buildPageList(list, styleList, latestBomOrderList, craftDemandInfoList, checkedPriceMap, designDemandMap, designDemandDetailMap);

        return PageRespVoHelper.of(page.getPageNum(), page.getTotal(), list);
    }

    private Map<Long, DesignDemand> getDesignDemandMap(List<Long> designDemandIdList) {
        if (CollUtil.isEmpty(designDemandIdList)) {
            return Collections.emptyMap();
        }
        List<DesignDemand> designDemands = designDemandRepository.listByIds(designDemandIdList);
        return StreamUtil.list2Map(designDemands, DesignDemand::getDesignDemandId);
    }

    private Map<Long, DesignDemandDetail> getDesignDemandDetailMap(List<Long> designDemandIdList) {
        if (CollUtil.isEmpty(designDemandIdList)) {
            return Collections.emptyMap();
        }
        List<DesignDemandDetail> designDemandDetails = designDemandDetailRepository.listByDesignDemandIds(designDemandIdList);
        return StreamUtil.list2Map(designDemandDetails, DesignDemandDetail::getDesignDemandId);
    }

    private Map<String, CheckPriceBaseInfoInnerVo> getCheckPriceMap(List<PrototypeManageQueryResp> list) {
        List<String> priceCheckedDesignCodeList = list.stream()
                .filter(a->(DesignCheckPriceStateEnum.FINISH_CHECK.getCode().equals(a.getCheckPriceState())))
                .map(PrototypeManageQueryResp::getDesignCode).distinct().collect(Collectors.toList());
        Map<String, CheckPriceBaseInfoInnerVo> checkedPriceMap =new HashMap<>(20);
        if (CollectionUtil.isNotEmpty(priceCheckedDesignCodeList)){
            DesignCodeListReq designCodeListReq=new DesignCodeListReq();
            designCodeListReq.setDesignCodeList(priceCheckedDesignCodeList);
            checkedPriceMap = checkPriceRemoteHelper.findLatestCheckedBaseInfoBySkcBatch(designCodeListReq).stream()
                    .collect(Collectors.toMap(CheckPriceBaseInfoInnerVo::getDesignCode, Function.identity(), (x, y) -> x));
        }
        return checkedPriceMap;
    }

    private List<String> queryDesignerGroup(PrototypeManageQuery queryDTO, UserContent userContent) {
        if(Objects.equals(queryDTO.getClothesDesigner(), Bool.YES.getCode())){
            if(ObjectUtil.isEmpty(userContent)){
                // 若用户不属于任意设计组则展示空数据
                return List.of();
            }
            DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
            designerRemoteReq.setDesignerId(String.valueOf(userContent.getCurrentUserId()));
            DataResponse<List<DesignerDTO>> listDataResponse = designerRemoteService.designerInfoList(designerRemoteReq);
            if(listDataResponse.isSuccessful() && CollectionUtil.isNotEmpty(listDataResponse.getData())){
                List<DesignerDTO> data = listDataResponse.getData();
                List<String> designerGroupCodeList = data.stream().map(DesignerDTO::getDesignerGroupCode).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(designerGroupCodeList)) {
                    return designerGroupCodeList;
                }
            }
        }
        return List.of();
    }

    @Override
    public List<PrototypeManageInfoQueryResp> clothesPriceList(PrototypeManageInfoBatchListReq req) {
        List<String> designCodeList = req.getDesignCodeList();
        List<Prototype> prototypes = prototypeRepository.listByDesignCodes(designCodeList);
        if (CollUtil.isEmpty(prototypes)) {
            return Collections.emptyList();
        }
        Map<String, Prototype> prototypeMap = StreamUtil.list2Map(prototypes, Prototype::getDesignCode);

        List<DesignCodeSpuQuery.DesignCodeSpuReq> designCodeSpuList = prototypes.stream().map(v->{
            DesignCodeSpuQuery.DesignCodeSpuReq designCodeSpuReq = new DesignCodeSpuQuery.DesignCodeSpuReq();
            designCodeSpuReq.setDesignCode(v.getDesignCode());
            designCodeSpuReq.setStyleCode(v.getStyleCode());
            return designCodeSpuReq;
        }).collect(Collectors.toList());
        // 样衣开发信息
        Map<String, List<ClothesProcessInfoVo>> clothesMap = sampleClothesRemoteHelper.getClothesProcessInfo(designCodeList);

        //核价信息(预估核价与生产核价)
        List<DesignPricingInfoVo> checkPriceList = checkPriceRemoteHelper.findLastPricingBySkcBatch(designCodeSpuList);
        Map<String, DesignPricingInfoVo> checkPriceMap = StreamUtil.list2Map(checkPriceList, DesignPricingInfoVo::getDesignCode);

        List<String> styleCodeList = StreamUtil.convertListAndDistinct(prototypes, Prototype::getStyleCode);

        //封装样衣与核价信息
        List<PrototypeManageInfoQueryResp> respList = PrototypeManageConverter.buildClothesPriceProductList(designCodeList, prototypeMap, clothesMap, checkPriceMap);

        //视觉需求信息
        this.buildPageVisualDemandInfo(styleCodeList, respList);

        //款式识别信息
        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizCodeSource(styleCodeList, SpuIdentifySourceEnum.DESIGN_STYLE.getCode(), null);
        Map<String, List<SpuIdentify>> spuIdentifyGroupMap = StreamUtil.groupingBy(spuIdentifyList, SpuIdentify::getBizCode);
        respList.forEach(item -> {
            item.setMuseIdentifyInfo(SpuIdentifyConverter.buildIdentifyInfo(item.getStyleCode(), spuIdentifyGroupMap));
        });

        return respList;
    }

    private void buildPageVisualDemandInfo(List<String> styleCodeList, List<PrototypeManageInfoQueryResp> respList) {
        //spu批量查询下最新的【上新、优化】视觉任务
        List<Integer> taskTypeList = List.of(VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode(), VisualTaskTypeEnum.OPTIMIZE_TASK.getCode());
        List<VisualTask> visualTaskList = visualTaskRepository.listLatestBySpuTaskType(styleCodeList, taskTypeList);
        Map<String, VisualTask> visualTaskMap = new HashMap<>(styleCodeList.size());
        List<Long> visualDemandIdList = new ArrayList<>(styleCodeList.size());
        StreamUtil.groupingBy(visualTaskList, VisualTask::getStyleCode).forEach((styleCode, taskList) -> {
            //获取最新的视觉任务
            VisualTask latestVisualTask = taskList.stream().max(Comparator.comparing(VisualTask::getCreatedTime)).orElse(null);
            if (Objects.nonNull(latestVisualTask)) {
                visualTaskMap.put(styleCode, latestVisualTask);
                visualDemandIdList.add(latestVisualTask.getLatestDemandId());
            }
        });
        if (CollUtil.isEmpty(visualDemandIdList)) {
            return;
        }
        List<VisualDemand> visualDemands = visualDemandRepository.listByIds(visualDemandIdList);
        Map<Long, VisualDemand> visualDemandMap = StreamUtil.list2Map(visualDemands, VisualDemand::getDemandId);

        respList.forEach(item -> {
            VisualTask visualTask = visualTaskMap.get(item.getStyleCode());
            if (Objects.isNull(visualTask)) {
                return;
            }
            VisualDemand visualDemand = visualDemandMap.get(visualTask.getLatestDemandId());
            if (Objects.isNull(visualDemand)) {
                return;
            }
            VisualDemandInfoVo demandInfoVo = new VisualDemandInfoVo();
            BeanUtils.copyProperties(visualDemand, demandInfoVo);
            this.setDemandPicInfo(visualDemand, demandInfoVo);
            //任务信息
            demandInfoVo.setState(visualTask.getState());
            demandInfoVo.setProcessCode(visualTask.getProcessCode());
            item.setVisualDemandInfo(demandInfoVo);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrototypeVo cancelDesign(PrototypeCancelReq cancelReq) {
        log.info("设计款管理-取消设计款-请求参数 : {}", JSON.toJSONString(cancelReq));
        Long prototypeId = cancelReq.getPrototypeId();
        PrototypeVo prototype = prototypeService.getById(prototypeId);
        Assert.notNull(prototype, "找不到设计款信息! ");
        Assert.isFalse(prototype.getIsCanceled(), "该设计款已取消! ");
        UserContent currentUser = UserContentHolder.get();
        Assert.notNull(currentUser, "用户信息为空,请登录! ");

        //当前用户的
        UserContent userContent = UserContentHolder.get();
        String currentUserBbCode = userContent.getCurrentUserCode();

        //取消设计款
        prototypeService.cancelPrototype(prototypeId, cancelReq, currentUserBbCode);

        //取消bom单
        if (Objects.equals(prototype.getPrototypeStatus(), PrototypeStatusEnum.DECOMPOSED.getCode())) {
            bomOrderService.closeBomWithPrototype(PrototypeManageConverter.buildCancelBomReq(prototype, cancelReq));
        }
        //取消采购单
        purchaseFollowService.closeMaterialPurchaseWithOther(PrototypeManageConverter.buildCancelPurchaseReq(prototype, cancelReq));

        //spu下所有skc都取消, 取消视觉需求
        List<Prototype> noCancelSkcList = prototypeRepository.getNotCancelByStyleCodes(Collections.singletonList(prototype.getStyleCode()));
        if (CollUtil.isEmpty(noCancelSkcList)) {
            log.info("设计款取消spu同步视觉, spu:{}", prototype.getStyleCode());
            visualSpuService.cancelSpu(Collections.singletonList(prototype.getStyleCode()));
        }

        sendBatchCancelPrototypeMessage2Mq(prototype, cancelReq, currentUser, currentUserBbCode);

        //通知商品平台,取消skc
        popProductHelper.cancelSkc(prototype);

        //添加日志
        addPrototypeLog(prototypeId, prototype.getDesignCode(), prototype.getVersionNum(),  "取消SKC: " + cancelReq.getCancelReason());
        return prototype;
    }

    private void addPrototypeLog(Long bizId, String designCode, Integer bizVersionNum, String content) {
        DesignLogReq designLogReq = DesignLogReq.builder()
                .bizId(bizId)
                .bizType(DesignLogBizTypeEnum.DESIGN_PROTOTYPE)
                .bizVersionNum(bizVersionNum)
                .content(content)
                .designCode(designCode)
                .build();
        logService.create(designLogReq);
    }

    protected void sendBatchCancelPrototypeMessage2Mq(PrototypeVo prototype, PrototypeCancelReq cancelReq
            , UserContent currentUser, String currentUserBbCode) {
        CancelPrototypeMqDTO cancelPrototypeMqDTO = CancelPrototypeMqDTO.builder().prototypeId(prototype.getPrototypeId())
                .designCode(prototype.getDesignCode())
                .cancelReason(cancelReq.getCancelReason())
                .cancelRemark(cancelReq.getCancelRemark())
                .cancelTime(LocalDateTime.now())
                .cancelUserId(currentUser.getCurrentUserId())
                .cancelUserName(currentUser.getCurrentUserName())
                .cancelUserCode(currentUserBbCode)
                .bizChannel(prototype.getBizChannel())
                .styleCode(prototype.getStyleCode())
                //.styleCode(prototype.getStyleCode())
                // .demandTaskId(prototype.getDemandTaskId())
                // .fakeId(prototype.getFakeId())
                .build();

        String cancelPrototypeJson = JSONUtil.toJsonStr(Lists.newArrayList(cancelPrototypeMqDTO));
        log.info("批量取消设计款号 发送mq.designCode={} prototypeId={} data={}", cancelPrototypeMqDTO.getDesignCode()
                , cancelPrototypeMqDTO.getPrototypeId(), cancelPrototypeJson);
        MqMessageReq build = MqMessageReq.build(MqBizTypeEnum.BATCH_CANCELING_PROTOTYPE
                , DesignMqConstant.SDP_DESIGN_BATCH_CANCELING_PROTOTYPE_EXCHANGE
                , cancelPrototypeJson);
        mqProducer.sendOnAfterCommit(build);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markUrgent(PrototypeUrgentReq urgentReq) {
        log.info("设计款管理-标记紧急-请求参数 : {}", JSON.toJSONString(urgentReq));
        UserContent currentUser = UserContentHolder.get();
        Assert.notNull(currentUser, "用户信息为空,请登录! ");
        urgentReq.getPrototypeIds().forEach(item -> {
            PrototypeVo prototype = prototypeService.getById(item);
            Assert.notNull(prototype, "找不到设计款信息! 设计款id: " + item + " ");
            if (!Objects.equals(prototype.getIsUrgent(), true)) {
                //设计款标记为紧急
                prototypeRepository.updateById(Prototype.builder().prototypeId(prototype.getPrototypeId()).isUrgent(true).build());
                //设计款历史表标记为紧急
                PrototypeHistory urgentHistory = PrototypeHistory.builder()
                        .isUrgent(true)
                        .reviserId(currentUser.getCurrentUserId())
                        .reviserName(currentUser.getCurrentUserName())
                        .revisedTime(LocalDateTime.now())
                        .build();
                prototypeHistoryRepository.updateByDesignCode(prototype.getDesignCode(), urgentHistory);

                //推送MQ消息
                sendUrgentMq(prototype.getDesignCode());
            } else {
                throw new SdpDesignException("当前设计款已是紧急状态! ");
            }
        });
    }

    private void sendUrgentMq(String designCode) {
        UrgentRemarkMqDTO mqDto = UrgentRemarkMqDTO.builder().designCode(designCode).isUrgent(Bool.YES).build();
        log.info("===发送mq消息_设计款标记紧急: mqDto: {} ===", JSON.toJSONString(mqDto));
        mqProducer.sendOnAfterCommit(MqMessageReq.build(
                MqBizTypeEnum.PROTOTYPE_URGENT_REMARK_PRODUCER,
                DesignMqConstant.SDP_DESIGN_PROTOTYPE_URGENT_EXCHANGE,
                JSON.toJSONString(mqDto)));
        log.info("===设计款标记紧急_mq消息发送完毕===");
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrototypeMakeClothesVo makeClothes(PrototypeMakeClothesReq req) {
        log.info("设计款管理-发起打版-req : {}", JSON.toJSONString(req));
        String designCode = req.getDesignCode();
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        SdpDesignException.notNull(prototype, "设计款信息不存在! skc:{}", designCode);
        SdpDesignException.isFalse(prototype.getIsCanceled(),"当前设计款号已取消! ");

        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getPrototypeId());
        SdpDesignException.notNull(prototypeDetail, "skc详情不存在!");

        DesignStyle designStyle = designStyleRepository.getByStyleCode(prototype.getStyleCode());
        SdpDesignException.notNull(designStyle, "spu不存在!");
        //判断当前SKC最新版本的bom状态是否为待提交，若是则提示【请提交bom单后再操作】
        BomOrder bomOrder = bomOrderRepository.getLatestBomByDesignCode(designCode);
        SdpDesignException.notNull(bomOrder, "bom单不存在!");
        SdpDesignException.isFalse(Objects.equals(bomOrder.getState(), BomOrderStateEnum.WAIT_SUBMIT.getCode()), "请提交bom单后再操作");

        //发起打版
        CreateSampleClothesReq clothesReq = new CreateSampleClothesReq();
        clothesReq.setDesignCode(req.getDesignCode());
        clothesReq.setMakeClothesType(MakeClothesTypeEnum.getByCode(req.getMakeClothesType()));
        clothesReq.setSampleAmount(req.getSampleAmount());
        clothesReq.setSampleSize(req.getSampleSize());
        SampleClothesVo sampleClothes = sampleClothesRemoteHelper.createSampleClothes(clothesReq);

        Long clothesId = sampleClothes.getClothesId();
        String processCode = sampleClothes.getProcessCode();
        //记录发起打版记录
        SampleRecodeCreateReq recodeCreateReq = SampleRecodeCreateReq.builder()
                .designCode(prototype.getDesignCode())
                .makeClothesType(req.getMakeClothesType())
                .sizeStandard(designStyle.getSizeStandard())
                .sizeStandardCode(designStyle.getSizeStandardCode())
                .sampleSize(prototypeDetail.getSampleSize())
                .sampleAmount(1)
                .clothesId(clothesId)
                .processCode(processCode)
                .build();
        recodeCreateReq.setSourceType(StartClothesSourceTypeEnum.DESIGNER.getCode());
        prototypeSampleRecodeService.create(recodeCreateReq);

        //是否补做
        if (Objects.equals(ClothesSampleTypeEnum.MORE_PATTERN_MAKING.getCode(), sampleClothes.getSampleType())) {
            Prototype makeMoreSkc = Prototype.builder().prototypeId(prototype.getPrototypeId()).isMakeMore(Boolean.TRUE).build();
            prototypeRepository.updateById(makeMoreSkc);
            PrototypeHistory makeMoreSkcHistory = PrototypeHistory.builder().prototypeId(prototype.getPrototypeId()).isMakeMore(Boolean.TRUE).build();
            prototypeHistoryRepository.updateById(makeMoreSkcHistory);
        }

        //通知致景发起齐套占位
        this.zjPreOrderMaterial(designCode, req.getMakeClothesType(), sampleClothes.getSampleType());

        //打版方式同步致景
        zjDesignRemoteHelper.updatePrototypeClothesType(designCode, req.getMakeClothesType(), prototype.getBizChannel());

        //添加日志
        addPrototypeLog(prototype.getPrototypeId(), prototype.getDesignCode(), prototype.getVersionNum(),  "发起打版");


        log.info("设计款管理-发起打版-完成 : designCode:{}; processCode:{}", prototype.getDesignCode(), clothesId);
        return PrototypeMakeClothesVo.builder()
                .clothesId(clothesId)
                .designCode(prototype.getDesignCode())
                .build();
    }


    @Override
    public void prePlacementCompleteMaterials(String designCode) {
        zjDesignRemoteHelper.prePlacementCompleteMaterials(designCode);
    }

    @Override
    public List<PrototypeExcelResp> prototypeManageExportExcel(PrototypeManageExcelQuery queryDTO) {
        List<String> list = List.of("印花","色织","数码印花","扎染");
        if (CollectionUtil.isEmpty(queryDTO.getExportDesignCodeList())) {
            Page<PrototypeManageQueryResp> page = PageHelper.startPage(1, 5_000)
                    .doSelectPage(() -> prototypeManageRepository.listQuery(queryDTO));
            if (CollectionUtil.isNotEmpty(page.getResult())) {
                List<PrototypeManageQueryResp> resp = page.getResult();
                List<String> designCodeList = resp.stream().map(PrototypeManageQueryResp::getDesignCode).toList();
                queryDTO.setExportDesignCodeList(designCodeList);
            }else {
                return Collections.emptyList();
            }
            page = null;
        }
        PrototypeExcelReq req = new PrototypeExcelReq();
        req.setDesignCodeList(queryDTO.getExportDesignCodeList());
        List<PrototypeExcelResp>  prototypeExcelResps = prototypeManageRepository.listExcel(req);
        int handleSize = 1000;
        List<List<PrototypeExcelResp>> splitResp = CollectionUtil.split(prototypeExcelResps, handleSize);
        // 市场风格
        Map<String, String> marketMap = dictValueRemoteHelper.getMarketNodeMap(1);
        Map<String, String> marketSeriesMap = dictValueRemoteHelper.getMarketNodeMap(2);
        Map<String, String> marketStyleMap = dictValueRemoteHelper.getMarketNodeMap(3);

        splitResp.forEach(excelList -> {
            List<Long> bomIdList = StreamUtil.convertListAndDistinct(excelList, PrototypeExcelResp::getBomId);
            List<BomOrderMaterialExcelResp> bomExcelRespList = prototypeManageRepository.listBomOrderMaterialExcel(bomIdList);
            Map<Long, List<BomOrderMaterialExcelResp>> bomExcelRespGroup = StreamUtil.groupingBy(bomExcelRespList, BomOrderMaterialExcelResp::getBomId);
            //是否花型
            for (PrototypeExcelResp resp : excelList) {
                resp.setIsFlower("否");
                List<BomOrderMaterialExcelResp> bomMaterialRespList = bomExcelRespGroup.get(resp.getBomId());
                if (CollUtil.isNotEmpty(bomMaterialRespList)) {
                    List<String> flowerCategorys = StreamUtil.convertListAndDistinct(bomMaterialRespList, BomOrderMaterialExcelResp::getFlowerCategory);
                    List<String> intersection = flowerCategorys.stream().filter(list::contains).toList();
                    if (!intersection.isEmpty()) {
                        resp.setIsFlower("是");
                    }
                }
                if(StringUtils.isNotBlank(resp.getMarketCode())){
                    resp.setMarketName(marketMap.get(resp.getMarketCode()));
                }
                if(StringUtils.isNotBlank(resp.getMarketSeriesCode())){
                    resp.setMarketSeriesName(marketSeriesMap.get(resp.getMarketSeriesCode()));
                }
                if(StringUtils.isBlank(resp.getClothingStyleName()) && StringUtils.isNotBlank(resp.getClothingStyleCode())){
                    resp.setClothingStyleName(marketStyleMap.get(resp.getClothingStyleCode()));
                }
                if(resp.getPlanningType() !=null){
                    resp.setPlanningTypeName(PlanningTypeEnum.getDescByCode(resp.getPlanningType()));
                }
            }
            bomExcelRespList = null;
            bomExcelRespGroup = null;
        });
        return prototypeExcelResps;
    }

    @Override
    public List<PrototypeZipResp> prototypeManageExportZip(PrototypeExcelReq req) {
        List<PrototypeZipResp> resp = prototypeManageRepository.listZip(req);
        resp.forEach(vo ->{
            List<String> designPicture = StrUtil.splitTrim(vo.getDesignPicture(), StrUtil.COMMA);
            vo.setDesignPictureList(designPicture);
        });

        // spu --> skc
        Map<String,List<PrototypeZipResp>> spuMapValueSkc = resp.stream().collect(Collectors.groupingBy(PrototypeZipResp::getStyleCode, Collectors.toList()));
        StringBuffer dirName = new StringBuffer();
        dirName.append("/Users/<USER>/Downloads/");
        try {
            // 按照spu->skc维度创建文件夹,并下载对应图片
            for (String spu : spuMapValueSkc.keySet()) {
                for (PrototypeZipResp skc : spuMapValueSkc.get(spu)) {
                    if (!StringUtils.isEmpty(skc.getDesignPicture())) {
                        String spuDir = dirName + "/" + spu + "/" + skc.getDesignCode();
                        File dir = new File(spuDir);
                        if (!dir.exists()) {
                            dir.mkdir();
                        }
                        List<String> designPicture = StrUtil.splitTrim(skc.getDesignPicture(), StrUtil.COMMA);
                        for (String pic : designPicture) {
                            if (pic.startsWith("https")) {
                                FileDownloader.downloadFile(pic, spuDir);
                            }else{
                                pic = "https://chuangxin-oss-cdn.tiangong.tech" + pic;
                                FileDownloader.downloadFile(pic, spuDir);
                            }
                        }

                    }
                }
                // 压缩spu
                FolderCompressor.zipFolder("/Users/<USER>/Downloads/"+spu,"/Users/<USER>/Downloads/"+spu+".zip");

                // 删除原文件夹
                File dir = new File("/Users/<USER>/Downloads/"+spu);
                deleteFolder(dir);
            }
        }catch (Exception e){
            e.printStackTrace();
        }

        return resp;
    }

    @Override
    public VisualDemandInfoVo queryVisualTaskForEdit(String styleCode) {
        //查询SPU下最新的【上新、优化】视觉任务
        SdpDesignException.notBlank(styleCode, "spu不能为空");
        DesignStyle designStyle = designStyleRepository.getByStyleCode(styleCode);
        SdpDesignException.notNull(designStyle, "spu信息不存在!");

        //查询SPU下最新的【上新、优化】视觉任务
        List<Integer> taskTypeList = List.of(VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode(), VisualTaskTypeEnum.OPTIMIZE_TASK.getCode());
        List<VisualTask> latestVisualTasks = visualTaskRepository.queryLatestHandlingTaskBySpuTaskType(styleCode, taskTypeList);
        if (Objects.nonNull(latestVisualTasks)) {
            //优先返回在途的上新任务
            VisualTask latestVisualTask = latestVisualTasks.stream().filter(v->VisualTaskTypeEnum.NEW_ARRIVAL_TASK.getCode().equals(v.getTaskType())).findFirst().orElse(null);
            if(latestVisualTask==null){
                latestVisualTask = latestVisualTasks.stream().filter(v->VisualTaskTypeEnum.OPTIMIZE_TASK.getCode().equals(v.getTaskType())).findFirst().orElse(null);
            }
            if(latestVisualTask!=null){
                VisualDemand visualDemand = visualDemandRepository.getById(latestVisualTask.getLatestDemandId());
                VisualDemandInfoVo infoVo = new VisualDemandInfoVo();
                BeanUtils.copyProperties(visualDemand, infoVo);
                this.setDemandPicInfo(visualDemand, infoVo);
                //任务信息
                infoVo.setState(latestVisualTask.getState());
                infoVo.setProcessCode(latestVisualTask.getProcessCode());
                infoVo.setModelReferenceImageList(JSON.parseArray(visualDemand.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
                infoVo.setBackgroundImageList(JSON.parseArray(visualDemand.getBackgroundImage(), BackgroundDTO.class));
                infoVo.setModelFaceImageList(JSON.parseArray(visualDemand.getModelFaceImage(), ModelFaceDTO.class));
                return infoVo;
            }
        }

        //如果无视觉任务, 查询视觉提交记录, 存在则返回spu下最新提交记录
        SpuVisualDemandRecord visualDemandRecord = spuVisualDemandRecordRepository.getLatestBySpu(styleCode);
        if (Objects.nonNull(visualDemandRecord)) {
            VisualDemandInfoVo infoVo = new VisualDemandInfoVo();
            BeanUtils.copyProperties(visualDemandRecord, infoVo);
            infoVo.setModelReferenceImageList(JSON.parseArray(visualDemandRecord.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
            infoVo.setBackgroundImageList(JSON.parseArray(visualDemandRecord.getBackgroundImage(), BackgroundDTO.class));
            infoVo.setModelFaceImageList(JSON.parseArray(visualDemandRecord.getModelFaceImage(), ModelFaceDTO.class));
            infoVo.setDemandId(visualDemandRecord.getVisualDemandId());
            return infoVo;
        }

        return null;
    }

    private void setDemandPicInfo(VisualDemand visualDemand, VisualDemandInfoVo infoVo) {
        if (Objects.isNull(visualDemand)) {
            return;
        }
        if(StringUtils.isNotBlank(visualDemand.getDemandImages())){
            infoVo.setDemandImageList(StrUtil.splitTrim(visualDemand.getDemandImages(), StrUtil.COMMA));
        }
        if(StringUtils.isNotBlank(visualDemand.getModelPic())){
            infoVo.setModelPicList(StrUtil.splitTrim(visualDemand.getModelPic(), StrUtil.COMMA));
        }
        if(StringUtils.isNotBlank(visualDemand.getBackgroundPic())){
            infoVo.setBackgroundPicList(StrUtil.splitTrim(visualDemand.getBackgroundPic(), StrUtil.COMMA));
        }
        if(StringUtils.isNotBlank(visualDemand.getPosturePic())){
            infoVo.setPosturePicList(StrUtil.splitTrim(visualDemand.getPosturePic(), StrUtil.COMMA));
        }
        if (StringUtils.isNotBlank(visualDemand.getModelReferenceImage())) {
            infoVo.setModelReferenceImageList(JSON.parseArray(visualDemand.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
        }
        if (StringUtils.isNotBlank(visualDemand.getBackgroundImage())) {
            infoVo.setBackgroundImageList(JSON.parseArray(visualDemand.getBackgroundImage(), BackgroundDTO.class));
        }
        if (StringUtils.isNotBlank(visualDemand.getModelFaceImage())) {
            infoVo.setModelFaceImageList(JSON.parseArray(visualDemand.getModelFaceImage(), ModelFaceDTO.class));
        }
    }

    @Override
    public Long submitVisualTask(DesignVisualSubmitReq req) {
        String designCode = req.getDesignCode();
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        SdpDesignException.notNull(prototype, "设计款信息不存在:{}", designCode);
        SdpDesignException.isTrue(Objects.equals(prototype.getPrototypeStatus(), PrototypeStatusEnum.DECOMPOSED.getCode()), "当前skc未提交:{}", designCode);

        Long visualDemandId = null;

        //若spu下所有未取消skc都已提交bom, 则创建视觉需求
        List<String> bomSubmittedSkcList = bomOperateService.checkAllSkcBomSubmitted(prototype.getStyleCode());
        if (CollUtil.isNotEmpty(bomSubmittedSkcList)) {
            SaveVisualDemandBySpuReq demandReq = new SaveVisualDemandBySpuReq();
            BeanUtils.copyProperties(req, demandReq);
            demandReq.setStyleCode(prototype.getStyleCode());

            demandReq.setDemandImages(CollUtil.isEmpty(req.getDemandImageList())? null: req.getDemandImageList());
            demandReq.setBackgroundPicList(CollUtil.isEmpty(req.getBackgroundPicList())? null: req.getBackgroundPicList());
            demandReq.setModelPicList(CollUtil.isEmpty(req.getModelPicList())? null: req.getModelPicList());
            demandReq.setPosturePicList(CollUtil.isEmpty(req.getPosturePicList())? null: req.getPosturePicList());
            demandReq.setModelReferenceImageList(req.getModelReferenceImageList());
            demandReq.setBackgroundImageList(req.getBackgroundImageList());
            demandReq.setModelFaceImageList(req.getModelFaceImageList());

            VisualDemand visualDemand = visualDemandService.saveVisualDemandBySpu(demandReq);
            visualDemandId = visualDemand.getDemandId();
        }

        //新增需求提交记录
        SpuVisualDemandRecordSaveReq visualDemandRecord = SpuVisualDemandRecordSaveReq.builder()
                .styleCode(prototype.getStyleCode())
                .spuType(SdpStyleTypeEnum.DESIGN.getCode())
                .handleType(SpuVisualHandleTypeEnum.DESIGN_START.getCode())
                .demandType(req.getDemandType())
                .realObjectColorState(req.getRealObjectColorState())
                .visualDemandId(visualDemandId)
                .skcList(bomSubmittedSkcList)
                .demandImageList(req.getDemandImageList())
                .demandDesc(req.getDemandDesc())
                .modelPicList(req.getModelPicList())
                .backgroundPicList(req.getBackgroundPicList())
                .posturePicList(req.getPosturePicList())
                .modelReferenceImageList(req.getModelReferenceImageList())
                .backgroundImageList(req.getBackgroundImageList())
                .modelFaceImageList(req.getModelFaceImageList())
                .build();
        spuVisualDemandRecordService.create(visualDemandRecord);

        return visualDemandId;
    }

    @Override
    public SpuImageMaterial buildSpuImageMaterial(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            return null;
        }
        DesignStyle designStyle = designStyleRepository.getByStyleCode(styleCode);
        if (Objects.isNull(designStyle)) {
            return null;
        }
        //已提交bom的skc
        List<BomOrder> bomOrderList = bomOperateService.listAllBomSubmittedSkc(Collections.singletonList(styleCode));
        if (CollUtil.isEmpty(bomOrderList)) {
            return null;
        }
        SpuImageMaterial spuImageMaterial = new SpuImageMaterial();
        spuImageMaterial.setStyleCode(styleCode);
        //spu-aigc图
        SpuAigcImageVo spuAigcImageVo = this.buildSpuAigcImageVo(designStyle);

        //skc设计图
        List<String> designCodeList = StreamUtil.convertListAndDistinct(bomOrderList, BomOrder::getDesignCode);
        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
        List<Long> prototypeIdList = StreamUtil.convertListAndDistinct(prototypeList, Prototype::getPrototypeId);
        List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIdList);
        Map<Long, PrototypeDetail> prototypeDetailMap = StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);

        // bom物料图(展示对应SKC-bom辅料图&面料3D图（面料褶皱图+衣服示意图）
        BomMaterialPictureReq bomMaterialPictureReq = new BomMaterialPictureReq();
        bomMaterialPictureReq.setDesignCodeList(designCodeList);
        bomMaterialPictureReq.setQueryFabric3DPicture(true);
        List<BomMaterialPictureResp> bomMaterialPictureRespList = bomOrderService.queryMaterialPicture(bomMaterialPictureReq);
        Map<String, BomMaterialPictureResp> bomMaterialPictureRespMap = StreamUtil.list2Map(bomMaterialPictureRespList, BomMaterialPictureResp::getDesignCode);

        //3D图(展示对应SPU最新审版通过的3D或3D+实物打版版单的3D打版正、侧、背面图)
        List<SampleAuditDimensionPictureVo> dimensionPictureVos = sampleClothesRemoteHelper.listAuditPassDimensionPictureByStyleCode(styleCode);
        Map<String,SampleAuditDimensionPictureVo> designCodeToDimensionPictureMap = dimensionPictureVos.stream().collect(Collectors.toMap(SampleAuditDimensionPictureVo::getDesignCode,v->v,(k1,k2)->k2));

        //数码描稿效果图
        Map<String,List<String>> designCodeToDigitalPaintingMap = sampleClothesRemoteHelper.listSkcDigitalPaintingPictureByStyleCode(styleCode);

        //类目图
        List<String> quantityMethodPictureList = getQuantityMethodPicture(designStyle);

        //版型图
        String modePicture = getModePicture(designStyle);

        List<SpuImageMaterial.SkcImageMaterial> skcImageMaterialList = prototypeList.stream().map(skc -> {
            SpuImageMaterial.SkcImageMaterial skcImageMaterial = new SpuImageMaterial.SkcImageMaterial();
            skcImageMaterial.setDesignCode(skc.getDesignCode());

            //aigc图
            skcImageMaterial.setAigcImageInfo(spuAigcImageVo);
            //设计图
            PrototypeDetail prototypeDetail = prototypeDetailMap.get(skc.getPrototypeId());
            if(prototypeDetail!=null) {
                StringBuilder sbColor = new StringBuilder();
                StringBuilder sbColorEn = new StringBuilder();
                if(CollectionUtil.isNotEmpty(prototypeDetail.getColorInfoList())){
                    int i=0;
                    for (ColorInfoVo colorInfoVo : prototypeDetail.getColorInfoList()) {
                        if(i!=0){
                            sbColor.append(" ");
                            sbColorEn.append(" ");
                        }
                        sbColor.append(colorInfoVo.getColor());
                        sbColorEn.append(colorInfoVo.getColorEnglishName());
                        i++;
                    }
                }
                skcImageMaterial.setColor(sbColor.toString());
                skcImageMaterial.setColorEn(sbColorEn.toString());

                if (StrUtil.isNotBlank(prototypeDetail.getDesignPicture())) {
                    List<String> designPicture = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);
                    skcImageMaterial.setDesignImages(designPicture);
                }
            }
            //bom物料图
            BomMaterialPictureResp bomMaterialPictureResp = bomMaterialPictureRespMap.get(skc.getDesignCode());
            if (Objects.nonNull(bomMaterialPictureResp)) {
                List<BomMaterialPictureResp.MaterialPicture> bomMaterialImages = new ArrayList<>();

                List<BomMaterialPictureResp.MaterialPicture> accessoriesPictureList = bomMaterialPictureResp.getAccessoriesPictureInfoList();
                if (CollUtil.isNotEmpty(accessoriesPictureList)) {
                    bomMaterialImages.addAll(accessoriesPictureList);
                }
                List<BomMaterialPictureResp.MaterialPicture> fabric3dPictureList = bomMaterialPictureResp.getFabric3dPictureInfoList();
                if (CollUtil.isNotEmpty(fabric3dPictureList)) {
                    bomMaterialImages.addAll(fabric3dPictureList);
                }
                List<BomMaterialPictureResp.MaterialPicture> fabric3dFoldPictureList = bomMaterialPictureResp.getFabric3dFoldPictureInfoList();
                if (CollUtil.isNotEmpty(fabric3dFoldPictureList)) {
                    bomMaterialImages.addAll(fabric3dFoldPictureList);
                }
                List<BomMaterialPictureResp.MaterialPicture> fabricMainPictureList = bomMaterialPictureResp.getFabricMainPictureList();
                if (CollUtil.isNotEmpty(fabricMainPictureList)) {
                    bomMaterialImages.addAll(fabricMainPictureList);
                }
                //面辅料图
                skcImageMaterial.setBomMaterialImages(bomMaterialImages);
                //二次工艺图
                List<String> craftPictureList = bomMaterialPictureResp.getCraftPictureList();
                if(CollUtil.isNotEmpty(craftPictureList)){
                    skcImageMaterial.setCraftPictureList(craftPictureList);
                }
            }
            //3D图
            SampleAuditDimensionPictureVo dimensionPictureVo = designCodeToDimensionPictureMap.get(skc.getDesignCode());
            if(dimensionPictureVo!=null && dimensionPictureVo.getDimensionPicture()!=null){
                skcImageMaterial.setDimensionImages(dimensionPictureVo.getDimensionPicture());
            }
            //数码描稿效果图
            skcImageMaterial.setDigitalPaintingPictureList(designCodeToDigitalPaintingMap.get(skc.getDesignCode()));

            //类目图
            skcImageMaterial.setQuantityMethodPictureList(quantityMethodPictureList);
            //版型图
            if(StringUtils.isNotBlank(modePicture)){
                skcImageMaterial.setModePictureList(Collections.singletonList(modePicture));
            }
            return skcImageMaterial;
        }).toList();

        spuImageMaterial.setSkcImageMaterials(skcImageMaterialList);

        return spuImageMaterial;
    }

    @Override
    public List<VisualTaskListExtraVo> visualListExtra(List<String> styleCodes) {
        if (CollUtil.isEmpty(styleCodes)) {
            return Collections.emptyList();
        }

        //根据spu查询所有已提交bom单的skc
        SkcBomSubmitReq skcBomSubmitReq = new SkcBomSubmitReq();
        skcBomSubmitReq.setStyleCodeList(styleCodes);
        List<Prototype> prototypeList = prototypeRepository.listBomSubmitSkc(skcBomSubmitReq);

        if (CollUtil.isEmpty(prototypeList)) {
            return Collections.emptyList();
        }

        List<Long> prototypeIdList = StreamUtil.convertListAndDistinct(prototypeList, Prototype::getPrototypeId);
        List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIdList);
        Map<Long, PrototypeDetail> prototypeDetailMap = StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);

        Map<String, List<Prototype>> skcGroupMap = StreamUtil.groupingBy(prototypeList, Prototype::getStyleCode);

        //构建返回结果
        return skcGroupMap.entrySet().stream()
                .map(entry -> this.buildVisualTaskListExtraVo(entry.getKey(), entry.getValue(), prototypeDetailMap))
                .collect(Collectors.toList());
    }

    /**
     * 构建单个SPU的 VisualTaskListExtraVo 对象
     */
    private VisualTaskListExtraVo buildVisualTaskListExtraVo(String styleCode,
                                                             List<Prototype> skcList,
                                                             Map<Long, PrototypeDetail> prototypeDetailMap) {
        List<VisualTaskListExtraVo.SkcInfo> skcInfoList = skcList.stream()
                .map(prototype -> {
                    PrototypeDetail detail = prototypeDetailMap.get(prototype.getPrototypeId());
                    return buildVisualSkcInfo(prototype, detail);
                })
                .collect(Collectors.toList());

        return VisualTaskListExtraVo.builder()
                .styleCode(styleCode)
                .skcInfoList(skcInfoList)
                .build();
    }

    /**
     * 构建单个SKC的信息
     */
    private VisualTaskListExtraVo.SkcInfo buildVisualSkcInfo(Prototype prototype, PrototypeDetail detail) {
        return VisualTaskListExtraVo.SkcInfo.builder()
                .designCode(prototype.getDesignCode())
                .colorInfoList(detail != null ? detail.getColorInfoList() : Collections.emptyList())
                .build();
    }

    private List<String> getQuantityMethodPicture(DesignStyle designStyle){
        try {
            List<SizeTemplateImageVO> sizeTemplateImageVOList = clothingFoundationRemoteHelper.sizeTemplateImage(Collections.singletonList(designStyle.getCategory()));
            if (CollUtil.isNotEmpty(sizeTemplateImageVOList)) {
                SizeTemplateImageVO sizeTemplateImageVO = sizeTemplateImageVOList.getFirst();
                if (CollectionUtil.isNotEmpty(sizeTemplateImageVO.getQuantityMethodImageUrls())) {
                    //设置类目图
                    return sizeTemplateImageVO.getQuantityMethodImageUrls();
                }
            }
        }catch (Exception e){
            log.error("获取类目图失败styleCode:{}",designStyle.getStyleCode(),e);
        }
        return Collections.emptyList();
    }

    private String getModePicture(DesignStyle designStyle) {
        String modePicture = null;
        try {
            Map<String, DictVo> dictValueMap = dictValueRemoteHelper.mapByDictCodes(List.of(DictConstant.OPERATE_MODEL_PICTURE));
            DictVo modePictureDict = dictValueMap.get(DictConstant.OPERATE_MODEL_PICTURE);
            if (Objects.nonNull(modePictureDict) && CollectionUtil.isNotEmpty(modePictureDict.getChildren())) {
                String modeCode = designStyle.getFitCode() + StrUtil.DASHED + designStyle.getElasticCode();
                if (StringUtils.isNotBlank(modeCode)) {
                    Map<String, DictVo> partUseDictValueMap = StreamUtil.list2Map(modePictureDict.getChildren(), DictVo::getDictCode);
                    modePicture = Optional.ofNullable(partUseDictValueMap.get(modeCode)).map(DictVo::getDictName).orElse(null);
                }
            }
        }catch (Exception e){
            log.error("获取版型图失败styleCode:{}",designStyle.getStyleCode(),e);
        }
        return modePicture;
    }

    private SpuAigcImageVo buildSpuAigcImageVo(DesignStyle designStyle) {
        Long designDemandId = designStyle.getDesignDemandId();
        if (Objects.isNull(designDemandId)) {
            log.warn("根据灵感设计需求id查询AIGC图失败:灵感设计需求id为空");
            return null;
        }
        //灵感任务详情
        Map<Long, DesignDemandDetail> designDemandDetailMap = this.getDesignDemandDetailMap(Collections.singletonList(designDemandId));
        DesignDemandDetail designDemandDetail = designDemandDetailMap.get(designDemandId);
        if (Objects.isNull(designDemandDetail) || CollUtil.isEmpty(designDemandDetail.getInspirationImageJson())) {
            log.warn("根据灵感设计需求id{}查询AIGC图失败:灵感任务详情或者灵感图对象（包含4k和原图）为空",designDemandId);
            return null;
        }

        SpuAigcImageVo spuAigcImageVo = new SpuAigcImageVo();
        List<DesignDemandCreateReq.ImageInfo> imageList = designDemandDetail.getInspirationImageJson();

        List<String> originImageList = new ArrayList<>();
        List<String> originMainImageList = new ArrayList<>();
        List<String> hdImageList = new ArrayList<>();
        List<String> hdMainImageList = new ArrayList<>();

        imageList.forEach(item -> {
            //主图
            if (Objects.equals(item.getMainImage(), Bool.YES.getCode())) {
                if (StrUtil.isNotBlank(item.getImageUrl())) {
                    originMainImageList.add(item.getImageUrl());
                }
                if (StrUtil.isNotBlank(item.getUltraHdUrl())) {
                    hdMainImageList.add(item.getUltraHdUrl());
                }
            }
            //非主图
            else {
                if (StrUtil.isNotBlank(item.getImageUrl())) {
                    originImageList.add(item.getImageUrl());
                }
                if (StrUtil.isNotBlank(item.getUltraHdUrl())) {
                    hdImageList.add(item.getUltraHdUrl());
                }
            }
        });
        spuAigcImageVo.setOriginImageList(originImageList.isEmpty() ?  null : originImageList);
        spuAigcImageVo.setOriginMainImageList(originMainImageList.isEmpty() ? null : originMainImageList);
        spuAigcImageVo.setHdImageList(hdImageList.isEmpty() ? null : hdImageList);
        spuAigcImageVo.setHdMainImageList(hdMainImageList.isEmpty() ? null : hdMainImageList);

        return spuAigcImageVo;
    }

    public static void deleteFolder(File folder) {
        if (folder.isDirectory()) {
            File[] files = folder.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteFolder(file);
                }
            }
        }
        folder.delete();
    }


    public void zjPreOrderMaterial(String designCode, Integer makeClothesType, Integer sampleType) {
        //通知致景发起齐套占位 条件: 1, bom单已提交; 2, 正常款打版/复色打版; 3, 打版单包含车版类型; 4, skc还未生成齐套单;
        //如果对应skc的bom单已提交, 打版类型包含车版; 且未生成齐套单; 通知致景发起齐套占位
        if (Objects.equals(tech.tiangong.sdp.design.enums.MakeClothesTypeEnum.REAL_SAMPLE_CLOTHES.getCode(), makeClothesType)
                || Objects.equals(tech.tiangong.sdp.design.enums.MakeClothesTypeEnum.THREE_DIMENSION_AND_REAL_SAMPLE.getCode(), makeClothesType)) {

            //打版类型
            if (Objects.equals(ClothesSampleTypeEnum.MORE_PATTERN_MAKING.getCode(), sampleType)) {
                log.info("=== 补做打版不发起齐套预占位,skc:{} ===", designCode);
                return;
            }

            //是否齐套 先查本地库, 再查履约
            List<OrderMaterialFollow> orderMaterialFollowList = orderMaterialFollowRepository.listByDesignCode(designCode);
            if (CollUtil.isNotEmpty(orderMaterialFollowList)) {
                return;
            }else {
                List<PrototypeOrderMaterialOpenResp> latestMaterialList = zjDesignRemoteHelper.findLatestMaterial(Collections.singletonList(designCode), true);
                if (CollUtil.isNotEmpty(latestMaterialList)) {
                    log.debug("=== 履约已创建普通款齐套单,不推送齐套预占位,skc:{} ===", designCode);
                    return;
                }
            }

            //bom是否提交
            BomOrder bomOrder = bomOrderRepository.getLatestBomByDesignCode(designCode);
            List<BomOrderStateEnum> submitStateList = BomOrderStateEnum.submitStateList();
            if (Objects.isNull(bomOrder) || !submitStateList.contains(BomOrderStateEnum.findEntityByCode(bomOrder.getState()))) {
                log.info("=== bom单未提交不发起齐套预占位,skc:{} ===", designCode);
                return;
            }

            zjDesignRemoteHelper.prePlacementCompleteMaterials(designCode);
        }
    }

    private void checkPrototypeRedo(Prototype prototype) {
        SdpDesignException.notNull(prototype, "找不到设计款信息! ");
        SdpDesignException.notBlank(prototype.getStyleCode(), "spu编号为空! ");
        SdpDesignException.isFalse(prototype.getIsCanceled(),"补做失败，当前设计款号已取消! ");
        // boolean rightType = Objects.equals(SampleTypeEnum.PATTERN_MAKING.getCode(), prototype.getSampleType())
        //         || Objects.equals(SampleTypeEnum.COMPOUND_COLORS_MAKING.getCode(), prototype.getSampleType());
        // SdpDesignException.isTrue(rightType,"补做失败，只有正常打版或复色打版类型才能补做! ");

        //判断最新设计拆版的bom表是否存在(已提交,已核算)? 如果不存在，系统提示：设计拆版之后的bom表没有更新
        BomOrder submitBom = bomOrderRepository.getLatestSubmitBom(prototype.getDesignCode());
        SdpDesignException.notNull(submitBom, "设计拆版之后的bom表没有更新! ");

        //校验授信
        // this.checkCredit(prototype);
    }

    // private void checkCredit(Prototype prototype) {
    //     //当SPU为自建款时且客户id存在, 校验客户授信信息
    //     DesignStyle designStyle = designStyleRepository.getByStyleCode(prototype.getStyleCode());
    //     PlmDesignException.notNull(designStyle, "spu不存在, spu编码: {}", prototype.getStyleCode());
    //     if (Objects.equals(DesignStyleSourceTypeEnum.SELF_SPU_SOURCE.getCode(), designStyle.getSourceType())
    //             && Objects.nonNull(designStyle.getPurchaserId())) {
    //         CustomerInfoResp creditInfo = crmCreditRemoteHelper.queryCreditByCustomerId(designStyle.getPurchaserId());
    //         PlmDesignException.notNull(creditInfo, "客户授信信息不存在，请联系BD在CRM系统完成客户授信！");
    //         // PlmDesignException.isTrue(creditInfo.isCcsCustomer(), "该客户未完成授信，请联系BD于CRM系统完成授信！");
    //         //只校验额度是否启用
    //         PlmDesignException.isTrue(Objects.equals(creditInfo.getAmountStatus(), Bool.YES.getCode()), "该客户未完成授信，请联系BD于CRM系统完成授信！");
    //     }
    // }


    @Override
    public BomOrderDetailVo getBomInfo(String designCode) throws Exception {
        //获取最新的开发bom信息
        return bomOrderService.getLatestBomOrderDetailWithDemand(designCode);
    }


    private PrototypeVo getPrototypeVo(Prototype prototype) {
        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototype.getLatestPrototypeId());
        PrototypeVo prototypeVo = new PrototypeVo();
        BeanUtils.copyProperties(prototype, prototypeVo);
        BeanUtils.copyProperties(prototypeDetail, prototypeVo);

        if (StringUtils.isNotBlank(prototypeDetail.getDesignPicture())) {
            List<String> designPicture = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);
            prototypeVo.setDesignPicture(designPicture);
        }

        return prototypeVo;
    }

    /**
     * 设计款详情基础新 spu + skc
     */
    @Override
    public PrototypeTagVo spuSkcInfo(String designCode, Integer isEdit) {
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        SdpDesignException.notNull(prototype, "设计款不存在! ");
        Long prototypeId = prototype.getPrototypeId();
        String styleCode = prototype.getStyleCode();
        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototypeId);
        SdpDesignException.notNull(prototypeDetail, "设计款详情不存在! ");

        PrototypeTagVo respVo = new PrototypeTagVo();

        //spu信息
        DesignStyleVo styleVo = designStyleService.getLatestVersionByStyleCode(styleCode);
        SdpDesignException.notNull(styleVo, "spu信息不存在! ");
        DesignDemand designDemand = designDemandRepository.getById(styleVo);

        respVo.setStyleInfo(styleVo);

        //skc信息
        PrototypeVo prototypeVo = this.getPrototypeVo(prototype);
        PrototypeInfoVo prototypeInfoVo = new PrototypeInfoVo();
        BeanUtils.copyProperties(prototypeVo, prototypeInfoVo);

        prototypeInfoVo.setReferenceDesignCode(prototype.getReferenceDesignCode());
        prototypeInfoVo.setColorInfoList(prototypeDetail.getColorInfoList());
        respVo.setPrototypeInfo(prototypeInfoVo);

        //灵感设计需求信息
        this.setDesignDemandInfo(styleVo, designDemand, respVo);

        //商品上架图信息
        OnShelfSpu onShelfSpu = onShelfSpuRepository.getBySpu(prototype.getStyleCode());
        if (Objects.nonNull(onShelfSpu)) {
            PrototypeTagVo.OnShelfInfo onShelfInfo = new PrototypeTagVo.OnShelfInfo();
            onShelfInfo.setStyleCode(prototype.getStyleCode());
            onShelfInfo.setDesignCode(prototype.getDesignCode());
            onShelfInfo.setSpuDetailImageList(onShelfSpu.getSpuDetailImageList());
            List<OnShelfSkc> onShelfSkcList = onShelfSkcRepository.listByDesignCodes(Collections.singletonList(designCode));
            if (CollUtil.isNotEmpty(onShelfSkcList) && Objects.nonNull(onShelfSkcList.getFirst())) {
                onShelfInfo.setSkcImageList(onShelfSkcList.getFirst().getSkcImageList());
            }
            respVo.setOnShelfInfo(onShelfInfo);
        }

        //款式识别信息
        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizCodeSource(Collections.singleton(styleCode), SpuIdentifySourceEnum.DESIGN_STYLE.getCode(), null);
        Map<String, List<SpuIdentify>> spuIdentifyGroupMap = StreamUtil.groupingBy(spuIdentifyList, SpuIdentify::getBizCode);
        respVo.setMuseIdentifyInfo(SpuIdentifyConverter.buildIdentifyInfo(styleCode, spuIdentifyGroupMap));

        return respVo;
    }

    private void setDesignDemandInfo(DesignStyleVo styleVo, DesignDemand designDemand, PrototypeTagVo respVo) {
        Long designDemandId = styleVo.getDesignDemandId();
        if (Objects.nonNull(designDemand)) {
            DesignDemandDetail demandDetail = designDemandDetailRepository.getByDemandId(designDemandId);
            PrototypeTagVo.DesignDemandInfo demandInfo = new PrototypeTagVo.DesignDemandInfo();
            BeanUtils.copyProperties(designDemand, demandInfo);
            if (Objects.nonNull(demandDetail)) {
                styleVo.setAigcRemark(demandDetail.getAigcRemark());
                demandInfo.setOriginalImage(demandDetail.getOriginalImage());
                List<String> imageAllList = new ArrayList<>();
                if(!CollUtil.isEmpty(demandDetail.getInspirationImageList())){
                    imageAllList.addAll(demandDetail.getInspirationImageList());
                }
                //视觉需求信息
                demandInfo.setDemandType(demandDetail.getDemandType());
                demandInfo.setAigcRemark(demandDetail.getAigcRemark());
                demandInfo.setDemandImageList(StreamUtil.convertList(demandDetail.getAigcRemarkAttachment(), DesignDemandAttachmentBo::getFileUrl));
                demandInfo.setModelPicList(StreamUtil.convertList(demandDetail.getModelAttachments(), DesignDemandAttachmentBo::getFileUrl));
                demandInfo.setBackgroundPicList(StreamUtil.convertList(demandDetail.getBackgroundAttachments(), DesignDemandAttachmentBo::getFileUrl));
                demandInfo.setPosturePicList(StreamUtil.convertList(demandDetail.getPostureAttachments(), DesignDemandAttachmentBo::getFileUrl));
                demandInfo.setModelReferenceImageList(JSON.parseArray(demandDetail.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
                demandInfo.setBackgroundImageList(JSON.parseArray(demandDetail.getBackgroundImage(), BackgroundDTO.class));
                demandInfo.setModelFaceImageList(JSON.parseArray(demandDetail.getModelFaceImage(), ModelFaceDTO.class));
                //展示4k图
                List<DesignDemandCreateReq.ImageInfo> imageList = demandDetail.getInspirationImageJson();
                if(null!= imageList){
                    List<String> ultraHdUrlList = imageList.stream()
                            .filter(item -> StrUtil.isNotBlank(item.getUltraHdUrl()))
                            .map(DesignDemandCreateReq.ImageInfo::getUltraHdUrl)
                            .collect(Collectors.toList());
                    if(!CollUtil.isEmpty(ultraHdUrlList)){
                        imageAllList.addAll(ultraHdUrlList);
                    }
                }
                // 获取通过的AI修图的结果与灵感图放到同一个List里面返回
                imageAllList.addAll(getDesignDemandAIBoxTaskResultImages(designDemandId));
                demandInfo.setInspirationImageList(imageAllList);
            }

            respVo.setDesignDemandInfo(demandInfo);
        }
    }

    /**
     * 通过灵感id获取AI结果图
     */
    private List<String> getDesignDemandAIBoxTaskResultImages(Long designDemandId) {
        return designDemandAIBoxTaskResultRepository
                .lambdaQuery()
                .eq(DesignDemandAIBoxTaskResult::getDesignDemandId, designDemandId)
                .eq(DesignDemandAIBoxTaskResult::getAsPassed,true)
                .list()
                .stream()
                .map(DesignDemandAIBoxTaskResult::getImage)
                .toList();
    }

    /**
     * 复色需求
     *
     * 勾选一条数据并点击复色后，校验当前SPU的正常打版SKC是否完成设计款信息提交，若已提交则允许创建复色SKC并刷新列表数据。
     * 若正常打版SKC未提交设计款信息时，则提示【请先操作SKC XXXXXXXX提交设计款信息后再进行复色】
     *
     */
    @Override
    public Long colorsMaking(ColorsMakingReq req) {
        log.info("设计款管理-复色-入参: req:{}", JSON.toJSONString(req));
        Long prototypeId = req.getPrototypeId();

        //校验当前SPU的正常打版SKC是否完成设计款信息提交
        Prototype prototype = prototypeRepository.getById(prototypeId);
        SdpDesignException.notNull(prototype, "版单信息不存在, 或已升版本刷新页面重试 ");
        SdpDesignException.isFalse(prototype.getIsCanceled(),"当前设计款号已取消! ");
        SdpDesignException.notBlank(prototype.getStyleCode(), "styleCode为空! ");
        List<Prototype> prototypeList = prototypeRepository.getListByStyleCode(prototype.getStyleCode());

        //正常款的SKC
        Prototype normalSkc = prototypeList.stream()
                .filter(item -> Objects.equals(item.getSkcType(), SkcTypeEnum.NORMAL.getCode()))
                .findFirst().orElse(null);
        SdpDesignException.notNull(normalSkc, "正常款skc不存在, designCode: {}", prototype.getDesignCode());

        //若正常打版SKC未提交设计款信息时，则提示【请先操作SKC XXXXXXXX提交设计款信息后再进行复色】
        boolean isSubmit = Objects.equals(PrototypeStatusEnum.DECOMPOSED.getCode(), normalSkc.getPrototypeStatus());
        SdpDesignException.isTrue(isSubmit, "请先操作{}提交设计款信息后再进行复色", prototype.getDesignCode());

        //若已提交则允许创建复色SKC
        Long colorMakePrototypeId = prototypeService.colorsMakingCreate(prototypeId);
        log.info("设计款管理-复色-成功: prototypeId:{}; colorMakePrototypeId{}", prototypeId, colorMakePrototypeId);

        //更新视觉SPU多色款状态
        visualSpuService.updateMultiColor(prototype.getStyleCode(), Bool.YES.getCode());

        return colorMakePrototypeId;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void designerChange(ChgDesignerReq req) {
        log.info("===设计师变更req:{} ====", JSON.toJSONString(req));
        DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
        designerRemoteReq.setDesignerId(String.valueOf(req.getDesignerId()));
        DesignerDTO designerDTO = SpringUtil.getBean(DesignerRemoteHelper.class).getByDesignerId(designerRemoteReq);
        Assert.notNull(designerDTO, "不存在此设计师");
        List<String> designCodeList = req.getDesignCodeList();
        SdpDesignException.notEmpty(designCodeList,"未选中skc");
        List<Prototype> prototypeList=new ArrayList<>();
        List<String> newJvDesignCodeList = new ArrayList<>();
        List<String> oldJvDesignCodeList = new ArrayList<>();
        List<Prototype> prototypes = prototypeRepository.listByDesignCodes(designCodeList);
        Map<String, Prototype> prototypeMap = StreamUtil.list2Map(prototypes, Prototype::getDesignCode);
        for (String designCode : designCodeList) {
            Prototype prototype = prototypeMap.get(designCode);
            Assert.notNull(prototype, "不存在此设计款号:{}", designCode);
            SdpDesignException.isFalse(prototype.getIsCanceled(),"当前设计款号已取消! ");
            String quoteDesignerName = prototype.getDesignerName();
            Prototype updatePrototype = Prototype.builder()
                    .prototypeId(prototype.getPrototypeId())
                    .designerId(req.getDesignerId())
                    .designerCode(designerDTO.getDesignerCode())
                    .designerName(designerDTO.getDesignerName())
                    .designerGroup(designerDTO.getDesignerGroupName())
                    .designerGroupCode(designerDTO.getDesignerGroupCode())
                    .build();
            prototypeList.add(updatePrototype);
            DesignLogReq logSimpleReq = DesignLogReq.builder()
                    .designCode(prototype.getDesignCode())
                    .bizId(prototype.getPrototypeId())
                    .bizType(DesignLogBizTypeEnum.DESIGN_PROTOTYPE)
                    .content("将设计师" + quoteDesignerName + "变更为" + designerDTO.getDesignerName()).build();
            logService.create(logSimpleReq);
            //skc已提交,则同步致景
            if (Objects.equals(prototype.getPrototypeStatus(), PrototypeStatusEnum.DECOMPOSED.getCode())) {
                if (Objects.equals(prototype.getBizChannel(), BizChannelEnum.NEW_JV.getCode())) {
                    newJvDesignCodeList.add(designCode);
                } else if (Objects.equals(prototype.getBizChannel(), BizChannelEnum.OLD_JV.getCode())) {
                    oldJvDesignCodeList.add(designCode);
                }
            }
        }

        List<PrototypeHistory> prototypeHistoryList = new ArrayList<>();
        for (PrototypeHistory prototypeHistory : prototypeHistoryRepository.listQueryByDesignCode(designCodeList)) {
            PrototypeHistory updatePrototypeHistory = PrototypeHistory.builder()
                    .prototypeId(prototypeHistory.getPrototypeId())
                    .designerId(req.getDesignerId())
                    .designerCode(designerDTO.getDesignerCode())
                    .designerName(designerDTO.getDesignerName())
                    .designerGroup(designerDTO.getDesignerGroupName())
                    .designerGroupCode(designerDTO.getDesignerGroupCode())
                    .build();
            prototypeHistoryList.add(updatePrototypeHistory);
        }
        Assert.isTrue(prototypeRepository.updateBatchById(prototypeList), "设计师变更失败！");
        Assert.isTrue(prototypeHistoryRepository.updateBatchById(prototypeHistoryList), "设计师变更失败！");
        ChangeDesignerDto changeDesignerDto = ChangeDesignerDto.builder()
                .designerId(req.getDesignerId())
                .designerCode(designerDTO.getDesignerCode())
                .designerName(designerDTO.getDesignerName())
                .designerGroupName(designerDTO.getDesignerGroupName())
                .designerGroupCode(designerDTO.getDesignerGroupCode())
                .mobilePhone(designerDTO.getMobilePhone())
                .designCodeList(req.getDesignCodeList())
                .build();

        //采购信息关联版单  设计师变更
        orderMaterialFollowService.orderMaterialChangeDesigner(changeDesignerDto);

        //设计师变更同步致景
        if (CollUtil.isNotEmpty(newJvDesignCodeList)) {
            DesignerUpdateOpenV2Req openReq = new DesignerUpdateOpenV2Req();
            openReq.setBizChannel(BizChannelEnum.NEW_JV.getCode());
            openReq.setExtDesignCodeList(newJvDesignCodeList);
            openReq.setDesignerName(designerDTO.getDesignerName());
            zjDesignRemoteHelper.batchUpdateDesigner(openReq);
        }
        if (CollUtil.isNotEmpty(oldJvDesignCodeList)) {
            DesignerUpdateOpenV2Req openReq = new DesignerUpdateOpenV2Req();
            openReq.setBizChannel(BizChannelEnum.OLD_JV.getCode());
            openReq.setExtDesignCodeList(oldJvDesignCodeList);
            openReq.setDesignerName(designerDTO.getDesignerName());
            zjDesignRemoteHelper.batchUpdateDesigner(openReq);
        }

        //广播发送mq，通知其它服务进行设计师变更
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.DESIGN_CHANGE_DESIGNER_UPDATE,
                DesignMqConstant.DESIGN_CHANGE_DESIGNER_UPDATE_EXCHANGE, null, JSON.toJSONString(changeDesignerDto));
        mqProducer.sendOnAfterCommit(mqMessageReq);

    }

}