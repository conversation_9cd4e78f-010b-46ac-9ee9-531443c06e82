package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.req.bom.BomCraftSubmitHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialSubmitHandleReq;

/**
 * bom单_选料物料处理 接口
 * <AUTHOR>
 * @date 2022/11/16 20:31
 */
public interface BomMaterialSubmitHandleService {

    /**
     * 首次提交
     *
     * 适用场景: bom 待提交_无暂存    (信息直接维护在原表中)
     *
     * 物料新增(); //新增的物料保存到原表中,不升版本
     * 物料更新(); //原有特辅更新,更新原特辅
     * 物料删除(); //引用场景会删除原有特辅,使用引用的特辅
     * 新增物料快照同步履约
     * 将物料快照id设置到工艺入参中, 传给工艺处理
     *
     * @param req 入参
     */
    BomCraftSubmitHandleReq firstSubmitNoTransient(BomMaterialSubmitHandleReq req);

    /**
     * 再次提交
     *
     * 适用场景: bom 已提交/已核算/找料中_无暂存    (信息直接维护在原表中)
     *
     * 物料新增(); //新增的物料保存到新bom中
     * 物料更新(); //原有特辅与面辅料更新, 复制一份到新bom中
     * 物料删除(); //删除物料不需要维护到新版本bom中, 但删除的工艺也要复制到新版本中
     * 新增物料快照同步履约
     * 将物料快照id设置到工艺入参中, 传给工艺处理
     * 旧新旧物料id映射Map提交给工艺处理
     *
     * @param req 入参
     */
    BomCraftSubmitHandleReq reSubmitNoTransient(BomMaterialSubmitHandleReq req);

    /**
     * 有暂存-首次提交
     *
     * 适用场景: bom 待提交_有暂存
     *
     * 暂存完后再复制一份暂存信息到原表中
     * 将物料快照id设置到工艺入参中, 传给工艺处理
     * 旧新旧物料id映射Map提交给工艺处理
     *
     * @param req 入参
     */
    BomCraftSubmitHandleReq firstSubmitWithTransient(BomMaterialSubmitHandleReq req);

    /**
     * 有暂存-再次提交
     *
     * 适用场景: bom 已提交/已核算/找料中_有暂存
     *
     * 暂存完后再复制一份暂存信息到原表中
     * 将物料快照id设置到工艺入参中, 传给工艺处理
     * 旧新旧物料id映射Map提交给工艺处理
     *
     * @param req 入参
     */
    BomCraftSubmitHandleReq reSubmitWithTransient(BomMaterialSubmitHandleReq req);

}
