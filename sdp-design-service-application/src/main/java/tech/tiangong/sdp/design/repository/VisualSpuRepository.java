package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.DesignStyle;
import tech.tiangong.sdp.design.entity.SpotSpu;
import tech.tiangong.sdp.design.entity.VisualSpu;
import tech.tiangong.sdp.design.mapper.VisualSpuMapper;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * (VisualSpu)服务仓库类
 */
@Repository
public class VisualSpuRepository extends BaseRepository<VisualSpuMapper, VisualSpu> {

    public VisualSpu getByStyleCode(String styleCode) {
        if(StringUtils.isBlank(styleCode)){
            return null;
        }
        return getOne(new LambdaQueryWrapper<VisualSpu>()
                .eq(VisualSpu::getStyleCode, styleCode)
                .eq(VisualSpu::getIsDeleted, Bool.NO.getCode()),false);
    }

    public List<VisualSpu> listByStyleCodes(Collection<String> styleCodes) {
        if(CollectionUtil.isEmpty(styleCodes)){
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<VisualSpu>()
                .in(VisualSpu::getStyleCode, styleCodes)
                .eq(VisualSpu::getIsDeleted, Bool.NO.getCode()));
    }

    public void update4Design(Long visualSpuId, DesignStyle spu) {
        if (Objects.isNull(visualSpuId) || Objects.isNull(spu)) {
            return;
        }
        lambdaUpdate()
                .eq(VisualSpu::getVisualSpuId, visualSpuId)
                .set(VisualSpu::getCategory, spu.getCategory())
                .set(VisualSpu::getCategoryName, spu.getCategoryName())
                .set(VisualSpu::getSupplyModeCode, spu.getSupplyModeCode())
                .set(VisualSpu::getSupplyModeName, spu.getSupplyModeName())
                .set(VisualSpu::getClothingStyleCode, spu.getClothingStyleCode())
                .set(VisualSpu::getClothingStyleName, spu.getClothingStyleName())
                .set(VisualSpu::getCountrySiteName, spu.getCountrySiteName())
                .set(VisualSpu::getCountrySiteCode, spu.getCountrySiteCode())
                .set(VisualSpu::getStoreId, spu.getStoreId())
                .set(VisualSpu::getStoreName, spu.getStoreName())
                .set(VisualSpu::getPlatformName, spu.getPlatformName())
                .set(VisualSpu::getBuyerId, spu.getBuyerId())
                .set(VisualSpu::getBuyerName, spu.getBuyerName())
                .set(VisualSpu::getWaveBandCode, spu.getWaveBandCode())
                .set(VisualSpu::getWaveBandName, spu.getWaveBandName())
                .set(VisualSpu::getProductType, spu.getProductType())
                .set(VisualSpu::getProductTypeCode, spu.getProductTypeCode())
                .update();
    }
    public void update4Spot(Long visualSpuId, SpotSpu spu, String platformName) {
        if (Objects.isNull(visualSpuId) || Objects.isNull(spu)) {
            return;
        }
        lambdaUpdate()
                .eq(VisualSpu::getVisualSpuId, visualSpuId)
                .set(VisualSpu::getCategory, spu.getCategory())
                .set(VisualSpu::getCategoryName, spu.getCategoryName())
                .set(VisualSpu::getSupplyModeCode, spu.getSupplyModeCode())
                .set(VisualSpu::getSupplyModeName, spu.getSupplyModeName())
                .set(VisualSpu::getClothingStyleCode, spu.getClothingStyleCode())
                .set(VisualSpu::getClothingStyleName, spu.getClothingStyleName())
                .set(VisualSpu::getCountrySiteName, spu.getCountrySiteName())
                .set(VisualSpu::getCountrySiteCode, spu.getCountrySiteCode())
                .set(VisualSpu::getStoreId, spu.getStoreId())
                .set(VisualSpu::getStoreName, spu.getStoreName())
                .set(VisualSpu::getPlatformName, platformName)
                .set(VisualSpu::getBuyerId, spu.getBuyerId())
                .set(VisualSpu::getBuyerName, spu.getBuyerName())
                .set(VisualSpu::getWaveBandCode, spu.getWaveBandCode())
                .set(VisualSpu::getWaveBandName, spu.getWaveBandName())
                .set(VisualSpu::getProductType, spu.getProductType())
                .set(VisualSpu::getProductTypeCode, spu.getProductTypeCode())
                .update();
    }
}
