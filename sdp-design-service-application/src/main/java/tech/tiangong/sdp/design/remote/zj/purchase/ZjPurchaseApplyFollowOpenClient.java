package tech.tiangong.sdp.design.remote.zj.purchase;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.plm.design.open.vo.resp.design.PurchaseApplyFollowResultOpenResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import tech.tiangong.sdp.core.config.ZjOpenFeignUserContentConfig;
import tech.tiangong.sdp.design.vo.req.zj.purchase.CancelPurchaseApplyOpenV2Req;
import tech.tiangong.sdp.design.vo.req.zj.purchase.PurchaseApplyMaterialConfirmBatchOpenV2Req;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/12/26 11:05
 */

@FeignClient(value = "plm-purchase-apply-follow",
        contextId = "PLM-PurchaseApplyFollowClient",
        configuration = ZjOpenFeignUserContentConfig.class,
        path = "/zj-tg-api/plm-design/open/v1",
        url = "${cx-tg.domain.url}")
public interface ZjPurchaseApplyFollowOpenClient {

    /**
     * 批量物料采购
     *
     * @param req 采购的物料参数
     * @return 返回值
     */
    @PostMapping("/purchase/apply/material-purchase-batch")
    DataResponse<PurchaseApplyFollowResultOpenResp> batchPurchaseApplyMaterial(@RequestBody @Valid PurchaseApplyMaterialConfirmBatchOpenV2Req req);

    /**
     * 取消采购
     *
     * @param req 参数
     * @return 响应
     */
    @PostMapping("/purchase/apply/cancel-purchase")
    DataResponse<Void> cancelPurchaseApply(@RequestBody @Validated CancelPurchaseApplyOpenV2Req req);
}
