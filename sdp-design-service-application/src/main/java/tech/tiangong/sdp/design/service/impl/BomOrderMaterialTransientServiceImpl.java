package tech.tiangong.sdp.design.service.impl;

import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.entity.BomOrderMaterialTransient;
import tech.tiangong.sdp.design.repository.BomOrderMaterialTransientRepository;
import tech.tiangong.sdp.design.service.BomOrderMaterialTransientService;
import tech.tiangong.sdp.design.vo.query.bom.BomOrderMaterialTransientQuery;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderMaterialTransientReq;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderMaterialTransientVo;

/**
 * bom物料_暂存表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BomOrderMaterialTransientServiceImpl implements BomOrderMaterialTransientService {
    private final BomOrderMaterialTransientRepository bomOrderMaterialTransientRepository;

    @Override
    public PageRespVo<BomOrderMaterialTransientVo> page(BomOrderMaterialTransientQuery query) {
        IPage<BomOrderMaterialTransientVo> page = bomOrderMaterialTransientRepository.findPage(query);
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    public BomOrderMaterialTransientVo getById(Long id) {
        BomOrderMaterialTransient entity = bomOrderMaterialTransientRepository.getById(id);
        BomOrderMaterialTransientVo vo = new BomOrderMaterialTransientVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(BomOrderMaterialTransientReq req) {
        BomOrderMaterialTransient entity = new BomOrderMaterialTransient();
        BeanUtils.copyProperties(req, entity);
        bomOrderMaterialTransientRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BomOrderMaterialTransientReq req) {
        BomOrderMaterialTransient entity = new BomOrderMaterialTransient();
        BeanUtils.copyProperties(req, entity);
        bomOrderMaterialTransientRepository.updateById(entity);
    }

    @Override
    public void remove(Long id) {
        bomOrderMaterialTransientRepository.removeById(id);
    }

}
