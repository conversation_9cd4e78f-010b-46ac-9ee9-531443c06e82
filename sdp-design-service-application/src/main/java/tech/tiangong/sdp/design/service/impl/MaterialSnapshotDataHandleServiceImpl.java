package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.util.Json;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yibuyun.scm.common.dto.accessories.response.ProductPictureVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductPictureWebVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSkuVo;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import com.yibuyun.scm.common.dto.fabric.response.CommoditySkuCollectionRespVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierExtVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.BomOrderConverter;
import tech.tiangong.sdp.design.converter.BomOrderMaterialConverter;
import tech.tiangong.sdp.design.entity.MaterialSnapshot;
import tech.tiangong.sdp.design.enums.MaterialDemandTypeEnum;
import tech.tiangong.sdp.design.remote.ProductRemoteHelper;
import tech.tiangong.sdp.design.remote.SupplierInfoRemoteHelper;
import tech.tiangong.sdp.design.repository.MaterialSnapshotRepository;
import tech.tiangong.sdp.design.service.MaterialSnapshotDataHandleService;
import tech.tiangong.sdp.utils.Bool;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

;

/**
 * 物料快照表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialSnapshotDataHandleServiceImpl implements MaterialSnapshotDataHandleService {
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final ProductRemoteHelper productRemoteHelper;
    private final SupplierInfoRemoteHelper supplierInfoRemoteHelper;


    @Override
    public void updateNewMaterialSupplierInfo() {
        //刷新好料网物料中的供应商信息
        List<MaterialSnapshot> list = materialSnapshotRepository.listMaterialWithoutSupplier(Bool.YES.getCode());
        SdpDesignException.notEmpty(list, "新物料中供应商id为空的数据不存在, 无需刷新!");

        //根据面辅料中skuId从履约查询对应的供应商id
        List<MaterialSnapshot> supplierShotList  = this.getSnapshotSupplierMap(list);
        SdpDesignException.notEmpty(supplierShotList, "新物料中供应商id查询履约为空, 无需刷新!");

        //根据供应商id查询供应商信息
        List<Long> supplierIdList = supplierShotList.stream().map(MaterialSnapshot::getSupplierId).collect(Collectors.toList());
        Map<Long, CommoditySupplierInfoVo> supplierMap = this.getSupplierInfo(supplierIdList);
        if (CollUtil.isEmpty(supplierMap)) {
            return;
        }
        //设置供应商合作关系(履约的开票状态)
        supplierShotList.forEach(item -> {
            if (Objects.nonNull(item.getSupplierId()) && Objects.nonNull(supplierMap.get(item.getSupplierId()))) {
                CommoditySupplierInfoVo supplierInfoVo = supplierMap.get(item.getSupplierId());
                CommoditySupplierExtVo supplierExtVo = supplierInfoVo.getExt();
                if (Objects.nonNull(supplierExtVo)) {
                    item.setInvoiceState(supplierExtVo.getInvoiceState());
                }
            }
        });

        //根据skuId更新新物料的供应商信息
        supplierShotList.forEach(item -> {
            boolean update = materialSnapshotRepository.update(Wrappers.lambdaUpdate(MaterialSnapshot.class)
                    .set(MaterialSnapshot::getSupplierId, item.getSupplierId())
                    .set(MaterialSnapshot::getSupplierCode, item.getSupplierCode())
                    .set(MaterialSnapshot::getSupplierName, item.getSupplierName())
                    .set(MaterialSnapshot::getInvoiceState, item.getInvoiceState())
                    .eq(MaterialSnapshot::getSkuId, item.getSkuId())
                    .eq(MaterialSnapshot::getNewMaterial, Bool.YES.getCode()));
            if (!update) {
                log.warn("=== 物料快照更新供应商信息失败,skuId:{}; supplierId:{} ===", item.getSkuId(), item.getSupplierId());
            }
        });

    }


    @Override
    public void cleanOld() {
        materialSnapshotRepository.cleanOld();
    }

    @Override
    public void updateMatchPicture(Integer handleSize) {
        //2022年10月20日之后提交的数据，需要重新拉一次图片
        String dateStr = "2022-10-20 00:00:00";
        LocalDateTime updateBeginDate = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listAfterDate(updateBeginDate);
        if (CollUtil.isEmpty(snapshotList)) {
            return;
        }
        //面辅料分组
        Map<Integer, List<MaterialSnapshot>> materialMap = snapshotList.stream().collect(Collectors.groupingBy(MaterialSnapshot::getMaterialType));
        if (Objects.isNull(handleSize) || handleSize < 0) {
            handleSize = 200;
        }

        List<MaterialSnapshot> fabricList = materialMap.get(MaterialDemandTypeEnum.FABRIC.getCode());
        List<MaterialSnapshot> accessoriesList = materialMap.get(MaterialDemandTypeEnum.ACCESSORIES.getCode());

        Set<Long> noUpdateIdFabric = new HashSet<>();
        Set<Long> noUpdateIdAccessories = new HashSet<>();
        for (List<MaterialSnapshot> fabricChildList : CollectionUtil.split(fabricList, handleSize)) {
            Set<Long> fabricSkuIds = fabricChildList.stream().map(MaterialSnapshot::getSkuId).collect(Collectors.toSet());
            //查询面料信息,履约可能删除了sku,
            List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(fabricSkuIds);
            if (CollUtil.isEmpty(fabricSkuInfos)) {
                noUpdateIdFabric.addAll(fabricSkuIds);
                continue;
            }
            Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = StreamUtil.list2Map(fabricSkuInfos, BomOrderConverter::getLyFabricSpuSkuId);

            if (CollUtil.isEmpty(fabricSkuMap)) {
                noUpdateIdFabric.addAll(fabricSkuIds);
                continue;
            }
            fabricChildList.forEach(item -> {
                CommoditySkuCollectionRespVo.Sku productSkuVo = fabricSkuMap.get(item.getSpuSkuId());
                if (Objects.isNull(productSkuVo)) {
                    return;
                }
                if (CollectionUtil.isNotEmpty(productSkuVo.getPictures())) {
                    List<String> matchPictureList = BomOrderMaterialConverter.buildFabricPicture(productSkuVo);
                    materialSnapshotRepository.update(Wrappers.lambdaUpdate(MaterialSnapshot.class)
                            .set(MaterialSnapshot::getMaterialPicture, Json.serialize(matchPictureList))
                            .eq(MaterialSnapshot::getSkuId, item.getSkuId()));
                }
            });
        }

        for (List<MaterialSnapshot> accessoriesChildList : CollectionUtil.split(accessoriesList, handleSize)) {
            Set<Long> accessoriesSkuIds = accessoriesChildList.stream().map(MaterialSnapshot::getSkuId).collect(Collectors.toSet());
            //查询辅料信息
            List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
            Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream()
                    .collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
            if (CollUtil.isEmpty(accessoriesSpuMap)) {
                noUpdateIdAccessories.addAll(accessoriesSkuIds);
                continue;
            }
            Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus)
                    .flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
            if (CollUtil.isEmpty(accessoriesSkuMap)) {
                noUpdateIdAccessories.addAll(accessoriesSkuIds);
                continue;
            }
            accessoriesChildList.forEach(item -> {
                ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(item.getCommodityId());
                if (Objects.isNull(productSpuInfoVo)) {
                    return;
                }
                ProductSkuVo productSkuVo = accessoriesSkuMap.get(item.getSkuId());
                if (Objects.isNull(productSkuVo)) {
                    return;
                }

                if (CollectionUtil.isNotEmpty(productSkuVo.getPictures())){
                    List<String> pictureList = productSkuVo.getPictures().stream().map(ProductPictureWebVo::getPicturePath).collect(Collectors.toList());
                    materialSnapshotRepository.update(Wrappers.lambdaUpdate(MaterialSnapshot.class)
                            .set(MaterialSnapshot::getMaterialPicture, Json.serialize(pictureList))
                            .eq(MaterialSnapshot::getSkuId, item.getSkuId()));
                }
                //若sku图为空,取商品图 --v3.5.2_汶俊;
                else if (CollectionUtil.isNotEmpty(productSpuInfoVo.getPictures())){
                    List<String> pictureList = productSpuInfoVo.getPictures().stream().map(ProductPictureVo::getPicturePath).collect(Collectors.toList());
                    materialSnapshotRepository.update(Wrappers.lambdaUpdate(MaterialSnapshot.class)
                            .set(MaterialSnapshot::getMaterialPicture, Json.serialize(pictureList))
                            .eq(MaterialSnapshot::getSkuId, item.getSkuId()));
                }
            });
        }
        log.info(" ==== 需要更新的面辅料skuId数量: {} ====", snapshotList.size());
        log.info(" ==== 未更更新的面料skuId: {} ====", Json.serialize(noUpdateIdFabric));
        log.info(" ==== 未更新的辅料skuId: {} ====", Json.serialize(noUpdateIdAccessories));
    }

    private Map<Long, CommoditySupplierInfoVo> getSupplierInfo(List<Long> supplierIds) {
        if (CollectionUtil.isEmpty(supplierIds)) {
            return Collections.emptyMap();
        }

        Map<Long, CommoditySupplierInfoVo> supplierInfoVoMap = new HashMap<>();
        try {
            List<CommoditySupplierInfoVo> supplierInfoList = supplierInfoRemoteHelper.getSupplierBaseInfo(supplierIds);
            supplierInfoVoMap = supplierInfoList.stream().collect(Collectors.toMap(CommoditySupplierInfoVo::getSupplierId, Function.identity(), (k1, k2) -> k1));
        } catch (Exception e) {
            log.warn("获取供应商异常", e);
        }
        return supplierInfoVoMap;
    }

    private List<MaterialSnapshot> getSnapshotSupplierMap(List<MaterialSnapshot> list) {
        List<MaterialSnapshot> supplierShotList = new LinkedList<>();
        //面料
        Set<Long> fabricSkuIds = list.stream()
                .filter(item -> Objects.equals(MaterialDemandTypeEnum.FABRIC.getCode(), item.getMaterialType()))
                .map(MaterialSnapshot::getSkuId).collect(Collectors.toSet());
        List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(fabricSkuIds);

        if (CollUtil.isNotEmpty(fabricSkuInfos)) {
            Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = StreamUtil.list2Map(fabricSkuInfos, BomOrderConverter::getLyFabricSpuSkuId);
            list.stream()
                    .filter(item -> Objects.equals(MaterialDemandTypeEnum.FABRIC.getCode(), item.getMaterialType()))
                    .forEach(item -> {
                        CommoditySkuCollectionRespVo.Sku skuVo = fabricSkuMap.get(item.getSpuSkuId());
                        if (Objects.isNull(skuVo)) {
                            return;
                        }
                        MaterialSnapshot snapshot = new MaterialSnapshot();
                        snapshot.setSkuId(item.getSkuId());
                        snapshot.setSupplierId(skuVo.getSupplierId());
                        snapshot.setSupplierCode(skuVo.getSupplierCode());
                        snapshot.setSupplierName(skuVo.getSupplierName());
                        supplierShotList.add(snapshot);
                    });
        }

        //辅料
        Set<Long> accessoriesSkuIds = list.stream()
                .filter(item -> Objects.equals(MaterialDemandTypeEnum.ACCESSORIES.getCode(), item.getMaterialType()))
                .map(MaterialSnapshot::getSkuId).collect(Collectors.toSet());
        List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
        if (CollUtil.isNotEmpty(accessoriesSkuInfos)) {
            Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream()
                    .collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
            Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus)
                    .flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));

            accessoriesSkuIds.forEach(item -> {
                ProductSkuVo productSkuVo = accessoriesSkuMap.get(item);
                if (Objects.isNull(productSkuVo)) {
                    return;
                }
                ProductSpuInfoVo spuInfoVo = accessoriesSpuMap.get(productSkuVo.getSpuId());
                if (Objects.isNull(spuInfoVo)) {
                    return;
                }
                MaterialSnapshot snapshot = new MaterialSnapshot();
                snapshot.setSkuId(item);
                snapshot.setSupplierId(spuInfoVo.getSupplierId());
                snapshot.setSupplierCode(spuInfoVo.getSupplierCode());
                snapshot.setSupplierName(spuInfoVo.getSupplierName());
                supplierShotList.add(snapshot);
            });
        }
        return supplierShotList;
    }

}
