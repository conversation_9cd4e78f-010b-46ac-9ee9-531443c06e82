package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.resp.bom.CraftDemandInfoVo;

import java.util.List;

/**
 * 二次工艺-service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/16 15:52
 */
public interface CraftDemandInfoService {

	/**
	 * 根据bomId查询工艺信息, 过滤暂存的工艺
	 * @param bomIds bomId集合
	 * @param state 工艺需求状态
	 * @return 工艺信息集合
	 */
	List<CraftDemandInfoVo> getListByBomIdsAndState(List<Long> bomIds, Integer state);
}
