package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.cache.commands.CacheCommands;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.constant.DesignRedisConstants;
import tech.tiangong.sdp.design.entity.DesignStyle;
import tech.tiangong.sdp.design.entity.OnShelfSkc;
import tech.tiangong.sdp.design.entity.OnShelfSpu;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.repository.DesignStyleRepository;
import tech.tiangong.sdp.design.repository.OnShelfSkcRepository;
import tech.tiangong.sdp.design.repository.OnShelfSpuRepository;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.service.OnShelfSpuService;
import tech.tiangong.sdp.design.vo.dto.product.PopProductImageChangeStateDto;
import tech.tiangong.sdp.design.vo.req.demand.OnShelfSpuReq;
import tech.tiangong.sdp.design.vo.req.mq.ProductImage2ZjMqDto;
import tech.tiangong.sdp.design.vo.resp.prototype.OnShelfSpuVo;
import tech.tiangong.sdp.utils.StreamUtil;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * 上架spu信息表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OnShelfSpuServiceImpl implements OnShelfSpuService {
    private final OnShelfSpuRepository onShelfSpuRepository;
    private final OnShelfSkcRepository onShelfSkcRepository;
    private final PrototypeRepository prototypeRepository;
    private final DesignStyleRepository designStyleRepository;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;
    private final CacheCommands cacheCommands;
    private final MqProducer mqProducer;


    @Override
    public OnShelfSpuVo getById(Long id) {
        OnShelfSpu entity = onShelfSpuRepository.getById(id);
        OnShelfSpuVo vo = new OnShelfSpuVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(OnShelfSpuReq req) {
        OnShelfSpu entity = new OnShelfSpu();
        BeanUtils.copyProperties(req, entity);
        onShelfSpuRepository.save(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void productImageUpdate(PopProductImageChangeStateDto dto) {
        log.info("=== 上架图片更新-req:{}===", JSON.toJSONString(dto));
        if (Objects.isNull(dto) || CollUtil.isEmpty(dto.getDataList())) {
            return;
        }
        List<String> designCodeList = new ArrayList<>();
        dto.getDataList().forEach(data -> {
            if (CollUtil.isNotEmpty(data.getSkcList())) {
                data.getSkcList().forEach(skc -> {
                    designCodeList.add(skc.getSkc());
                });
            }
        });
        Map<String, Prototype> prototypeMap = StreamUtil.list2Map(prototypeRepository.getLastByDesignCodes(designCodeList), Prototype::getDesignCode);
        List<String> spuCodeList = StreamUtil.convertListAndDistinct(dto.getDataList(), PopProductImageChangeStateDto.Data::getSpuCode);
        Map<String, DesignStyle> styleMap = StreamUtil.list2Map(designStyleRepository.listByStyleCodes(spuCodeList), DesignStyle::getStyleCode);

        dto.getDataList().forEach(data -> {
            if (StrUtil.isBlank(data.getSpuCode())) {
                return;
            }
            Lock lock = cacheCommands.getDistributedMutexLock(DesignRedisConstants.getOnShelfImageSpuKey(data.getSpuCode()));
            try {
                boolean isLock = lock.tryLock(5, TimeUnit.SECONDS);
                SdpDesignException.isTrue(isLock, "请求频繁，请稍后再试");
                //spu存在才更新
                DesignStyle designStyle = styleMap.get(data.getSpuCode());
                if (Objects.isNull(designStyle)) {
                    log.info(" === spu不存在,不更新上架图:spu{} ===", data.getSpuCode());
                    return;
                }
                OnShelfSpu shelfSpu = onShelfSpuRepository.getBySpu(data.getSpuCode());
                if (shelfSpu == null) {
                    shelfSpu = OnShelfSpu.builder()
                            .onShelfSpuId(IdPool.getId())
                            .styleCode(data.getSpuCode())
                            .spuDetailImageList(data.getProductDetailsImageList())
                            .build();
                }
                shelfSpu.setSpuDetailImageList(data.getProductDetailsImageList());
                onShelfSpuRepository.saveOrUpdate(shelfSpu);
                //skc上架图增改
                this.handleSkcImg(data, prototypeMap, designCodeList);

            } catch (InterruptedException e) {
                throw new RuntimeException("");
            } finally{
                lock.unlock();
            }
        });
        //上架图推送致景
        this.productImg2Zj(dto, prototypeMap);
    }


    private void handleSkcImg(PopProductImageChangeStateDto.Data data, Map<String, Prototype> prototypeMap, List<String> designCodeList) {
        if (CollUtil.isEmpty(data.getSkcList())) {
            return;
        }
        data.getSkcList().forEach(skc -> {
            //没有提交的skc不用更新上架信息
            Prototype prototype = prototypeMap.get(skc.getSkc());
            if (Objects.isNull(prototype)) {
                log.info(" === skc不存在,不更新上架图:skc{} ===", skc.getSkc());
                return;
            }
            designCodeList.add(skc.getSkc());
            List<OnShelfSkc> shelfSkcs = onShelfSkcRepository.listByDesignCodes(Collections.singletonList(skc.getSkc()));
            if (shelfSkcs.isEmpty()){
                OnShelfSkc onShelfSkc = OnShelfSkc.builder()
                        .onShelfSkcId(IdPool.getId())
                        .styleCode(data.getSpuCode())
                        .designCode(skc.getSkc())
                        .skcImageList(skc.getSkcImageList())
                        .build();
                onShelfSkcRepository.save(onShelfSkc);
            }else{
                shelfSkcs.forEach(updateSkc ->{
                    updateSkc.setSkcImageList(skc.getSkcImageList());
                    onShelfSkcRepository.updateById(updateSkc);
                });
            }
        });
    }

    private void productImg2Zj(PopProductImageChangeStateDto dto, Map<String, Prototype> prototypeMap) {
        if (CollUtil.isEmpty(dto.getDataList())) {
            return;
        }
        dto.getDataList().forEach(data -> {
            List<PopProductImageChangeStateDto.Data.Skc> skcList = data.getSkcList();
            if (CollUtil.isEmpty(skcList)) {
                return;
            }
            for (PopProductImageChangeStateDto.Data.Skc skcData : skcList) {
                //没有提交的skc不用推送上架信息
                Prototype prototype = prototypeMap.get(skcData.getSkc());
                if (Objects.isNull(prototype)) {
                    log.info(" === skc不存在,不同步上架图到致景:skc{} ===", skcData.getSkc());
                    continue;
                }
                //内部mq推送
                ProductImage2ZjMqDto mqContent = new ProductImage2ZjMqDto();
                mqContent.setStyleCode(data.getSpuCode());
                mqContent.setDesignCode(skcData.getSkc());
                mqContent.setSpuDetailImageList(data.getProductDetailsImageList());
                mqContent.setSkcImageList(skcData.getSkcImageList());
                mqContent.setBizChannel(prototype.getBizChannel());

                MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.PRODUCT_IMAGE_UPDATE_PRODUCT,
                        DesignMqConstant.PRODUCT_IMAGE_UPDATE_EXCHANGE,
                        DesignMqConstant.PRODUCT_IMAGE_UPDATE_ROUTING_KEY,
                        JSON.toJSONString(mqContent));
                log.info("=== 上架图更新mq通知 mqMessageReq:{} ===", JSON.toJSONString(mqMessageReq));
                mqProducer.sendOnAfterCommit(mqMessageReq);
            }
        });
    }

    @Override
    public void productImageUpdate2Zj(ProductImage2ZjMqDto dto) {
        log.info("=== 上架图片推送致景-req:{}===", JSON.toJSONString(dto));
        if (StrUtil.isBlank(dto.getDesignCode()) || StrUtil.isBlank(dto.getStyleCode())) {
            return;
        }
        zjDesignRemoteHelper.saveMarketingPicture(dto);
    }
}
