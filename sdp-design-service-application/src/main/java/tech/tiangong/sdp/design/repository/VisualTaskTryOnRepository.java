package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualTaskTryOn;
import tech.tiangong.sdp.design.mapper.VisualTaskTryOnMapper;

import java.util.List;

/**
 * (VisualTaskTryOn)服务仓库类
 */
@Repository
public class VisualTaskTryOnRepository extends BaseRepository<VisualTaskTryOnMapper, VisualTaskTryOn> {
    public VisualTaskTryOn getLatestByTaskId(Long taskId) {
       return this.getOne(new LambdaQueryWrapper<VisualTaskTryOn>()
                .eq(VisualTaskTryOn::getTaskId, taskId)
                .eq(VisualTaskTryOn::getIsLatest, 1)
                .last("limit 1"));
    }

    public List<VisualTaskTryOn> getLatestByTaskIdList(List<Long> taskIdList) {
        return this.list(new LambdaQueryWrapper<VisualTaskTryOn>()
                .in(VisualTaskTryOn::getTaskId, taskIdList)
                .eq(VisualTaskTryOn::getIsLatest, 1));
    }
}
