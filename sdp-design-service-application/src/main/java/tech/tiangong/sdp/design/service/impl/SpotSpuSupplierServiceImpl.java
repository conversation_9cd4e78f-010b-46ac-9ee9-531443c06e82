package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.SpotSpuSupplier;
import tech.tiangong.sdp.design.enums.spot.SpotSourceTypeEnum;
import tech.tiangong.sdp.design.repository.SpotSpuSupplierRepository;
import tech.tiangong.sdp.design.service.SpotSpuSupplierService;
import tech.tiangong.sdp.design.vo.dto.spot.SpotPickStyleCreateDto;
import tech.tiangong.sdp.design.vo.dto.spot.SpotSupplierUpdateDto;
import tech.tiangong.sdp.design.vo.req.spot.SpotSpuSupplierReq;
import tech.tiangong.sdp.design.vo.req.spot.SpotSpuUpdateReq;
import tech.tiangong.sdp.design.vo.req.spot.SpotSupplierCreateReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuSupplierVo;
import tech.tiangong.sdp.utils.StreamUtil;

import java.util.*;

/**
 * spot_spu_supplier表(SpotSpuSupplier)服务
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:50
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpotSpuSupplierServiceImpl implements SpotSpuSupplierService {
    private final SpotSpuSupplierRepository spotSpuSupplierRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> create4SelfCreate(String styleCode, List<SpotSupplierCreateReq> supplierAddList) {
        SdpDesignException.notBlank(styleCode, "styleCode为空!");
        SdpDesignException.notEmpty(supplierAddList, "supplierAddList为空!");

        List<SpotSpuSupplier> spuSupplierAddList = supplierAddList.stream().map(item -> {
            SpotSpuSupplier entity = new SpotSpuSupplier();
            BeanUtils.copyProperties(item, entity);
            entity.setStyleCode(styleCode);
            entity.setSpotSpuSupplierId(IdPool.getId());
            entity.setSourceType(SpotSourceTypeEnum.SELF_MAKE.getCode());
            return entity;
        }).toList();

        spotSpuSupplierRepository.saveBatch(spuSupplierAddList);

        return StreamUtil.convertListAndDistinct(spuSupplierAddList, SpotSpuSupplier::getSpotSpuSupplierId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpotSpuSupplier create4PickStyle(String styleCode, SpotPickStyleCreateDto skcReq) {
        SdpDesignException.notBlank(styleCode, "styleCode为空!");

        SpotSpuSupplier entity = new SpotSpuSupplier();
        entity.setStyleCode(styleCode);
        entity.setSpotSpuSupplierId(IdPool.getId());
        entity.setSourceType(SpotSourceTypeEnum.PICK_STYLE.getCode());
        entity.setSupplierName(skcReq.getSupplierName());
        entity.setSupplierStyle(skcReq.getSupplierStyle());
        entity.setPurchasePrice(skcReq.getPurchasePrice());
        entity.setPayeeCode(skcReq.getPayeeCode());
        entity.setPayeeName(skcReq.getPayeeName());
        spotSpuSupplierRepository.save(entity);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDelUpdate(SpotSpuUpdateReq req) {
        String styleCode = req.getStyleCode();
        UserContent userContent = UserContentHolder.get();
        //新增
        List<SpotSpuUpdateReq.SupplierInfo> supplierAddList = req.getSupplierAddList();
        if (CollUtil.isNotEmpty(supplierAddList)) {
            List<SpotSpuSupplier> spuSupplierAddList = supplierAddList.stream().map(item -> {
                SpotSpuSupplier entity = new SpotSpuSupplier();
                BeanUtils.copyProperties(item, entity);
                entity.setStyleCode(styleCode);
                entity.setSpotSpuSupplierId(IdPool.getId());
                entity.setSourceType(SpotSourceTypeEnum.SELF_MAKE.getCode());
                return entity;
            }).toList();
            spotSpuSupplierRepository.saveBatch(spuSupplierAddList);
        }

        //更新
        List<SpotSpuUpdateReq.SupplierUpdateInfo> supplierUpdateInfoList = req.getSupplierUpdateInfoList();
        if (CollUtil.isNotEmpty(supplierUpdateInfoList)) {
            List<SpotSupplierUpdateDto> updateDtoList = new ArrayList<>(supplierUpdateInfoList.size());
            supplierUpdateInfoList.forEach(item -> {
                SpotSupplierUpdateDto updateSupplier = new SpotSupplierUpdateDto();
                BeanUtils.copyProperties(item, updateSupplier);
                updateDtoList.add(updateSupplier);
            });
            spotSpuSupplierRepository.modify(updateDtoList, userContent);
        }

        //删除
        List<Long> supplierDelIdList = req.getSupplierDelIdList();
        if (CollUtil.isNotEmpty(supplierDelIdList)) {
            //不能全部删除, 选款来源的供应商不能删除
            List<SpotSpuSupplier> originSupplierList = spotSpuSupplierRepository.listByStyleCodes(Collections.singletonList(styleCode));
            Map<Long, SpotSpuSupplier> supplierMap = StreamUtil.list2Map(originSupplierList, SpotSpuSupplier::getSpotSpuSupplierId);
            supplierDelIdList.forEach(item -> {
                SpotSpuSupplier spotSpuSupplier = supplierMap.get(item);
                SdpDesignException.notNull(spotSpuSupplier, "供应商信息不存在, supplierId:{}", item);
                if (Objects.equals(SpotSourceTypeEnum.PICK_STYLE.getCode(), spotSpuSupplier.getSourceType())) {
                    throw new SdpDesignException("选款供应商不能删除, supplierId:" + item);
                }
            });
            spotSpuSupplierRepository.removeBatchByIds(supplierDelIdList);
        }

    }

    @Override
    public List<SpotSpuSupplierVo> getAllSupplierList(SpotSpuSupplierReq req) {
        return spotSpuSupplierRepository.getAllSupplierList(req);
    }


}
