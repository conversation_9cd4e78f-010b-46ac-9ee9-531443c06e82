package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.vo.resp.time.TimeVo;

import java.time.LocalDateTime;


/**
 * 时间 -(TimeController)
 *
 * <AUTHOR>
 * @since 2021-08-09 14:43:19
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/time")
public class TimeController {

    /**
     * 获取服务器当前时间
     *
     * @return 响应结果
     */
    @GetMapping("/now")
    public DataResponse<TimeVo> getNow() {
        return DataResponse.ok(new TimeVo(LocalDateTime.now()));
    }
}