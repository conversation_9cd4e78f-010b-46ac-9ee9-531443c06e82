package tech.tiangong.sdp.design.controller.inner;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.service.PrototypeManageService;
import tech.tiangong.sdp.design.service.SkcService;
import tech.tiangong.sdp.design.vo.req.prototype.inner.*;
import tech.tiangong.sdp.design.vo.resp.prototype.*;

import java.util.List;


/**
 * 设计SKC-inner
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/skc")
public class SkcInnerController extends BaseController {
    private final SkcService skcService;
    private final PrototypeManageService manageService;

    /**
     * 分页查询
     *
     * @param query 分页对象
     * @return PageRespVo<SkcInnerQueryVo>
     */
    @PostMapping("/page")
    public DataResponse<PageRespVo<SkcInnerQueryVo>> page(@RequestBody @Validated SkcInnerQuery query) {
        return DataResponse.ok(skcService.pageInner(query));
    }

    /**
     * 发起打版
     *
     * @param req 入参
     * @return StartClothesVo
     */
    @PostMapping("/ask-clothes")
    @NoRepeatSubmitLock(lockTime = 6L)
    DataResponse<AskClothesVo> askClothes(@RequestBody @Validated AskClothesInnerReq req) {
        return DataResponse.ok(skcService.askClothes(req));
    }

    /**
     * 获取已提交版本的skc信息(看不到临时保存的版本)
     *
     * @param designCode 设计款号
     * @return 响应结果
     */
    @GetMapping("/saved-info/{designCode}")
    public DataResponse<PrototypeInnerVo> getSavedInfoByDesignCode(@PathVariable(value = "designCode") String designCode) {
        return DataResponse.ok(skcService.getSavedInfoByDesignCode(designCode));
    }

    /**
     * 获取已提交版本的skc信息_包含spu信息(看不到临时保存的版本)
     *
     * @param designCode 设计款号
     * @return 响应结果
     */
    @PostMapping("/saved-info/with-spu/{designCode}")
    public DataResponse<SkcSpuInnerVo> getSaveInfoWithSpu(@PathVariable(value = "designCode") String designCode) {
        return DataResponse.ok(skcService.getSaveInfoWithSpu(designCode));
    }

    /**
     * 根据spu获取最新已提交版本的skc信息
     *
     * @param styleCode SPU款号
     * @return 响应结果
     */
    @GetMapping("/latest-submit/{styleCode}")
    public DataResponse<PrototypeInnerVo> latestSubmitBySpu(@PathVariable(value = "styleCode") String styleCode){
        return DataResponse.ok(skcService.latestSubmitBySpu(styleCode));
    }

    /**
     * 根据spu获取最新已提交版本的skc详情(包含spu信息)
     *
     * @param styleCode SPU款号
     * @return 响应结果
     */
    @PostMapping( "/latest-submit/with-spu/{styleCode}")
    public DataResponse<SkcSpuInnerVo> latestSubmitWithSpu(@PathVariable(value = "styleCode") String styleCode){
        return DataResponse.ok(skcService.latestSubmitWithSpu(styleCode));
    }

    /**
     * skc详情查询_包含SPU与上架图(根据设计款号或id)
     *
     * @param req 入参
     * @return SkcDetailInnerVo
     */
    @PostMapping("/skc-info")
    public DataResponse<SkcDetailInnerVo> getSkcInfo(@RequestBody @Validated SkcInfoReq req) {
        return  DataResponse.ok(skcService.getSkcInfo(req));
    }

    /**
     * 根据spu查询所有已提交bom单的skc
     *
     * @param req 入参
     * @return SkcBomSubmitVo
     */
    @PostMapping("/bom-submit-skc")
    public DataResponse<SkcBomSubmitVo> listBomSubmitSkc(@RequestBody @Validated SkcBomSubmitReq req) {
        return  DataResponse.ok(skcService.listBomSubmitSkc(req));
    }

    /**
     * skc信息基础信息查询
     *
     * @param req 入参
     * @return List<SkcBaseInnerVo>
     */
    @PostMapping("/list-base-skc")
    DataResponse<List<SkcBaseInnerVo>> listBaseSkc(@RequestBody @Validated SkcBaseInnerReq req) {
        return DataResponse.ok(skcService.listBaseSkc(req));
    }

    /**
     * 根据spu查询正常款的skc基础信息
     * @param styleCode spu
     * @return 正常款skc信息
     */
    @PostMapping("/normal-by-spu/{styleCode}")
    DataResponse<SkcBaseInnerVo> normalBySpu(@PathVariable(value = "styleCode") String styleCode) {
        return DataResponse.ok(skcService.normalBySpu(styleCode));
    }

    /**
     * 根据spu查询正常款的设计图信息(包含上架图)
     *
     * @param req 入参
     * @return List<NormalSkcImageVo>
     */
    @PostMapping("/normal-image")
    public DataResponse<List<NormalSkcImageVo>> getNormalImage(@RequestBody @Validated SkcImageQuery req) {
        return DataResponse.ok(skcService.getNormalImage(req));
    }

    /**
     * 设计款skc推送商品运营平台
     *
     * @param req 入参
     * @return SkcDetailInnerVo
     */
    @PostMapping("/local-price")
    DataResponse<Boolean> pushSkc(@RequestBody @Validated SkcPriceReq req){
        return  DataResponse.ok(skcService.pushSkc(req));
    }

    /**
     * 修改打版方式-通知致景齐套预占位
     *
     * @param req 入参
     * @return 返回
     */
    @PostMapping("/pre-placement-material")
    public DataResponse<Void> prePlacementMaterial(@RequestBody @Validated PrePlacementMaterialReq req) {
        skcService.prePlacementMaterial2Zj(req);
        return DataResponse.ok();
    }

    /**
     * 齐套预占位-测试/刷数预留接口
     *
     * @param designCode 设计款号
     * @return 返回
     */
    @GetMapping("/pre-material/{designCode}")
    public DataResponse<Void> prePlacementCompleteMaterials(@PathVariable(value = "designCode") String designCode) {
        manageService.prePlacementCompleteMaterials(designCode);
        return DataResponse.ok();
    }


    /**
     * 批量 设计款skc推送商品运营平台
     *
     * @param req 入参
     * @return SkcDetailInnerVo
     */
    @PostMapping("/batch-local-price")
    DataResponse<Boolean> batchPushSkc(@RequestBody @Validated List<SkcPriceReq> req){
        return  DataResponse.ok(skcService.batchPushSkc(req));
    }

    /**
     * 根据skc批量查询上架图信息
     *
     * @param req 入参
     * @return List<SkcOnShelfImageVo>
     */
    @PostMapping("/on-shelf-image")
    public DataResponse<List<SkcOnShelfImageVo>> listOnShelfImg(@RequestBody @Validated SkcOnShelfImgInnerReq req) {
        return DataResponse.ok(skcService.listOnShelfImg(req));
    }

}