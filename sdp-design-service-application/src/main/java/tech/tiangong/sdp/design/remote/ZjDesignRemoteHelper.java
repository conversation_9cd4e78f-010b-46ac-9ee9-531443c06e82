package tech.tiangong.sdp.design.remote;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.plm.design.open.vo.req.bom.BomMaterialUpdateOpenReq;
import cn.yibuyun.plm.design.open.vo.req.purchase.CancelMaterialOpenReq;
import cn.yibuyun.plm.design.open.vo.resp.design.MaterialPurchaseFollowResultOpenResp;
import cn.yibuyun.plm.design.open.vo.resp.design.PurchaseApplyFollowResultOpenResp;
import cn.yibuyun.tg.open.client.plm.design.BomOrderOpenClient;
import cn.yibuyun.tg.open.client.plm.design.MaterialPurchaseFollowOpenClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.clothes.enums.MakeClothesTypeEnum;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.enums.BizChannelEnum;
import tech.tiangong.sdp.design.remote.zj.bom.ZjPlmBomRemoteHelper;
import tech.tiangong.sdp.design.remote.zj.demand.ZjPlmDemandRemoteHelper;
import tech.tiangong.sdp.design.remote.zj.design.ZjPlmDesignRemoteHelper;
import tech.tiangong.sdp.design.remote.zj.purchase.ZjPurchaseApplyFollowOpenClient;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.vo.dto.bom.BomHouliuDemandDeleteDto;
import tech.tiangong.sdp.design.vo.dto.material.BomMaterialUpdateDto;
import tech.tiangong.sdp.design.vo.req.BomMaterialDosageAccountReq;
import tech.tiangong.sdp.design.vo.req.mq.CancelPrototypeMqDTO;
import tech.tiangong.sdp.design.vo.req.mq.ProductImage2ZjMqDto;
import tech.tiangong.sdp.design.vo.req.purchase.CancelPurchaseApplyReq;
import tech.tiangong.sdp.design.vo.req.purchase.PurchaseApplyMaterialConfirmBatchReq;
import tech.tiangong.sdp.design.vo.req.zj.bom.*;
import tech.tiangong.sdp.design.vo.req.zj.demand.*;
import tech.tiangong.sdp.design.vo.req.zj.design.*;
import tech.tiangong.sdp.design.vo.req.zj.purchase.CancelPurchaseApplyOpenV2Req;
import tech.tiangong.sdp.design.vo.req.zj.purchase.CraftDemandInfoDemandOpenV2Req;
import tech.tiangong.sdp.design.vo.req.zj.purchase.PurchaseApplyMaterialConfirmBatchOpenV2Req;
import tech.tiangong.sdp.design.vo.resp.zj.bom.BomOrderOpenResp;
import tech.tiangong.sdp.design.vo.resp.zj.demand.AccessoryDemandCreateOpenV2Resp;
import tech.tiangong.sdp.design.vo.resp.zj.demand.CraftDemandCreateOpenV2Resp;
import tech.tiangong.sdp.design.vo.resp.zj.design.DesignStyleCreateOpenV2Resp;
import tech.tiangong.sdp.design.vo.resp.zj.design.PrototypeOrderMaterialOpenResp;
import tech.tiangong.sdp.utils.StreamUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 致景服务调用helper
 *
 * <AUTHOR> while
 */

@Service
@Slf4j
@AllArgsConstructor
public class ZjDesignRemoteHelper {

    private final BomOrderOpenClient bomOrderOpenClient;
    private final ZjPlmBomRemoteHelper zjPlmBomRemoteHelper;
    private final ZjPlmDesignRemoteHelper zjPlmDesignRemoteHelper;
    private final ZjPlmDemandRemoteHelper zjPlmDemandRemoteHelper;
    private final ZjPurchaseApplyFollowOpenClient zjPurchaseApplyFollowOpenClient;
    private final MaterialPurchaseFollowOpenClient materialPurchaseFollowOpenClient;

    private final PrototypeRepository prototypeRepository;


    private static final Integer JV_CHANNEL = 3;

    /**
     * 更新旧款bizChannel
     * @param styleCodeList spu集合
     */
    public void updateBizChannelBySpu(List<String> styleCodeList) {
        SdpDesignException.notEmpty(styleCodeList, "spu为空");
        log.info("=== 更新旧款bizChannel-req:{}", JSON.toJSONString(styleCodeList));
        DataResponse<Void> dataResponse = zjPlmDesignRemoteHelper.updateBizChannelBySpu(styleCodeList);
        log.info("更新旧款bizChannel-response:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            throw new SdpDesignException("更新旧款bizChannel调用zj服务失败 " + dataResponse.getMessage());
        }
    }

    /**
     * 同步skc制作方式
     * @param designCode 设计款号
     * @param clothesType 打版方式
     */
    public void updatePrototypeClothesType(String designCode, Integer clothesType, Integer bizChannel) {
        SdpDesignException.notBlank(designCode, "skc为空");
        SdpDesignException.notNull(clothesType, "打版方式为空");
        ZjUpdateClothesTypeOpenV2Req openReq = new ZjUpdateClothesTypeOpenV2Req();
        openReq.setBizChannel(bizChannel);
        openReq.setExtDesignCode(designCode);
        openReq.setMakeClothesType(this.getMakeClothesType(clothesType));
        log.info("【同步skc制作方式】调用致景服务请求参数:{}", JSON.toJSONString(openReq));
        DataResponse<Void> dataResponse = zjPlmDesignRemoteHelper.updatePrototypeClothesType(openReq);
        log.info("【同步skc制作方式】调用致景服务响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            throw new SdpDesignException("【同步skc制作方式】调用zj服务失败 " + dataResponse.getMessage());
        }
    }

    public Integer getMakeClothesType(Integer clothesType) {
        MakeClothesTypeEnum clothesTypeEnum = MakeClothesTypeEnum.getByCode(clothesType);
        return switch (clothesTypeEnum) {
            case THREE_DIMENSION_SAMPLE -> 2;
            case UNKNOWN -> throw new IllegalArgumentException("未知打版类型");
            case ONLY_PATTERN_CLOTHES,
                 REAL_SAMPLE_CLOTHES,
                 THREE_DIMENSION_REAL_SAMPLE -> 1;
        };
    }

    /**
     * 批量更新设计师
     * @param openReq 入参
     */
    public void batchUpdateDesigner(DesignerUpdateOpenV2Req openReq) {
        SdpDesignException.notNull(openReq, "入参为空!");
        log.info("【批量更新设计师】调用致景服务请求参数 openReq:{}", JSON.toJSONString(openReq));
        DataResponse<Void> dataResponse = zjPlmDesignRemoteHelper.batchUpdateDesigner(openReq);

        log.info("【批量更新设计师】调用致景服务响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            log.error("【批量更新设计师】调用致景服务失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException("【批量更新设计师】调用zj服务失败 " + dataResponse.getMessage());
        }
    }

    /**
     * 齐套单查询
     * @param designCodeList 设计款号集合
     */
    public List<PrototypeOrderMaterialOpenResp> findLatestMaterial(List<String> designCodeList, Boolean queryNormal) {
        SdpDesignException.notNull(designCodeList, "设计款号为空!");
        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
        Map<String, Prototype> prototypeMap = StreamUtil.list2Map(prototypeList, Prototype::getDesignCode);

        List<PrototypeOrderMaterialOpenResp> finalDataList = new ArrayList<>();
        List<String> newJvDesignCodeList = new ArrayList<>();
        List<String> oldJvDesignCodeList = new ArrayList<>();
        designCodeList.forEach(designCode -> {
            Prototype prototype = prototypeMap.get(designCode);
            if (Objects.equals(prototype.getBizChannel(), BizChannelEnum.NEW_JV.getCode())) {
                newJvDesignCodeList.add(designCode);
            } else if (Objects.equals(prototype.getBizChannel(), BizChannelEnum.OLD_JV.getCode())) {
                oldJvDesignCodeList.add(designCode);
            }
        });
        if (CollUtil.isNotEmpty(newJvDesignCodeList)) {
            PrototypeOrderMaterialOpenReq openReq = new PrototypeOrderMaterialOpenReq();
            openReq.setBizChannel(BizChannelEnum.NEW_JV.getCode());
            openReq.setExDesignCodeList(newJvDesignCodeList);
            List<PrototypeOrderMaterialOpenResp> materialOpenRespList = this.findLatestMaterialFromZj(openReq, queryNormal);
            finalDataList.addAll(materialOpenRespList);
        }
        if (CollUtil.isNotEmpty(oldJvDesignCodeList)) {
            PrototypeOrderMaterialOpenReq openReq = new PrototypeOrderMaterialOpenReq();
            openReq.setBizChannel(BizChannelEnum.OLD_JV.getCode());
            openReq.setExDesignCodeList(oldJvDesignCodeList);
            List<PrototypeOrderMaterialOpenResp> materialOpenRespList = this.findLatestMaterialFromZj(openReq, queryNormal);
            finalDataList.addAll(materialOpenRespList);
        }
        return finalDataList;
    }
    public List<PrototypeOrderMaterialOpenResp> findLatestMaterialFromZj(PrototypeOrderMaterialOpenReq openReq, Boolean queryNormal) {
        log.info("【齐套单查询】调用致景服务请求参数 openReq:{}", JSON.toJSONString(openReq));
        DataResponse<List<PrototypeOrderMaterialOpenResp>> dataResponse = zjPlmDesignRemoteHelper.findLatestMaterial(openReq);
        log.info("【齐套单查询】调用致景服务响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            log.error("【齐套单查询】调用致景服务失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException("【齐套单查询】调用zj服务失败 " + dataResponse.getMessage());
        }
        List<PrototypeOrderMaterialOpenResp> dataList = dataResponse.getData();
        if (queryNormal && CollUtil.isNotEmpty(dataList)) {
            List<PrototypeOrderMaterialOpenResp> normalList = dataList.stream()
                    .filter(item -> Objects.equals(item.getPrototypeType(), 0))
                    .toList();
            log.info("=== 普通款齐套单:{} ===", JSON.toJSONString(normalList));
            return normalList;
        }
        return dataList;
    }

    /**
     * 上传营销图
     *
     * @param dto         入参
     */
    public void saveMarketingPicture(ProductImage2ZjMqDto dto) {
        PrototypeMarketingPictureSaveOpenV2Req openReq = new PrototypeMarketingPictureSaveOpenV2Req();
        openReq.setExtStyleCode(dto.getStyleCode());
        openReq.setExtDesignCode(dto.getDesignCode());
        openReq.setMarketingPicture(dto.getSkcImageList());
        openReq.setStyleMarketingPicture(dto.getSpuDetailImageList());
        openReq.setBizChannel(dto.getBizChannel());
        log.info("【上传营销图】调用致景服务 请求参数 openReq:{}", JSON.toJSONString(openReq));
        DataResponse<Void> dataResponse = zjPlmDesignRemoteHelper.saveMarketingPicture(openReq);
        log.info("【上传营销图】调用致景服务 响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            throw new SdpDesignException("【上传营销图】调用zj服务失败 " + dataResponse.getMessage());
        }
    }

    /**
     * 关闭工艺需求
     * @param openReq 入参
     */
    public void craftDemandClose(CraftDemandCloseOpenV2Req openReq) {
        log.info("【关闭工艺需求】调用致景服务请求参数 openReq:{}", JSON.toJSONString(openReq));
        //调用致景新接口 关闭工艺需求-v2
        DataResponse<Void> dataResponse = zjPlmDemandRemoteHelper.craftDemandClose(openReq);
        log.info("【关闭工艺需求】调用致景服务响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            throw new SdpDesignException("【关闭工艺需求】调用zj服务失败 " + dataResponse.getMessage());
        }
    }


    /**
     * 创建工艺需求
     *
     * @param craftDemandAddReqList 入参
     * @param bizChannel
     */
    public List<CraftDemandCreateOpenV2Resp.DemandInfo> craftDemandCreate(List<CraftDemandCreateOpenV2Req.CraftDemandAddReq> craftDemandAddReqList, Integer bizChannel) {
        CraftDemandCreateOpenV2Req openReq = new CraftDemandCreateOpenV2Req();
        openReq.setCraftDemandAddReqs(craftDemandAddReqList);
        openReq.setBizChannel(bizChannel);
        log.info("【创建工艺需求】调用致景服务请求参数 openReq:{}", JSON.toJSONString(openReq));

        //调用致景新接口 创建工艺需求-v2
        DataResponse<CraftDemandCreateOpenV2Resp> dataResponse = zjPlmDemandRemoteHelper.craftDemandCreate(openReq);

        log.info("【创建工艺需求】调用致景服务响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            throw new SdpDesignException("【创建工艺需求】调用zj服务失败 " + dataResponse.getMessage());
        }
        return dataResponse.getData().getDemandInfos();
    }

    /**
     * 创建辅料找料需求
     * @param createBatchReq 入参
     */
    public List<AccessoryDemandCreateOpenV2Resp.DemandInfo> accessoryDemandCreate(AccessoryDemandCreateOpenV2Req createBatchReq) {
        if (Objects.isNull(createBatchReq) || CollectionUtil.isEmpty(createBatchReq.getAccessoryDemandInfos())) {
            return null;
        }
        log.info("【创建辅料找料需求】 请求参数 :{}", JSON.toJSONString(createBatchReq));
        //调用致景新接口 创建辅料找料需求-v2
        DataResponse<AccessoryDemandCreateOpenV2Resp> dataResponse = zjPlmDemandRemoteHelper.accessoryDemandCreate(createBatchReq);
        log.info("【创建辅料找料需求】 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            throw new SdpDesignException("【创建辅料需求】 失败 " + dataResponse.getMessage());
        }
        return Optional.ofNullable(dataResponse.getData()).orElseGet(AccessoryDemandCreateOpenV2Resp::new).getDemandInfos();
    }

    /**
     * 通知致景齐套预占位
     */
    public void prePlacementCompleteMaterials(String designCode) {
        SdpDesignException.notBlank(designCode, "skc为空!");
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        SdpDesignException.notNull(prototype, "skc不存在! {}", designCode);
        try {
            BomMaterialPrePlacementOpenReq req = new BomMaterialPrePlacementOpenReq();
            req.setDesignCodes(Collections.singletonList(designCode));
            req.setBizChannel(prototype.getBizChannel());
            log.info("=== 通知致景齐套预占位 req：{} ===", JSONObject.toJSONString(req));
            DataResponse<Void> response = zjPlmBomRemoteHelper.prePlacementCompleteMaterials(req);
            log.info("=== 通知致景齐套预占位 response：{} ===", JSONObject.toJSONString(response));
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
        } catch (Exception e) {
            throw new SdpDesignException("通知zj齐套预占位-失败:"+e.getMessage(), e);
        }
    }

    /**
     * 取消skc
     */
    public void cancelSkc(CancelPrototypeMqDTO mqDTO) {
        SdpDesignException.notNull(mqDTO, "入参为空!");
        try {
            PrototypeCancelOpenV2Req req = new PrototypeCancelOpenV2Req();
            req.setCancelReason(mqDTO.getCancelReason());
            req.setCancelRemark(mqDTO.getCancelRemark());
            req.setExtDesignCode(mqDTO.getDesignCode());
            req.setBizChannel(mqDTO.getBizChannel());

            log.info("=== 取消skc推送致景 req：{} ===", JSONObject.toJSONString(req));
            //调用致景新接口 取消设计款-v2
            DataResponse<Void> response = zjPlmDesignRemoteHelper.cancelDesign(req);
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
        } catch (Exception e) {
            throw new SdpDesignException("取消skc-推送zj-失败:"+e.getMessage(), e);
        }
    }


    /**
     * 关闭辅料需求
     */
    public void houLiuDemandDelete(BomHouliuDemandDeleteDto demandDeleteDto) {
        SdpDesignException.notNull(demandDeleteDto, "入参为空!");
        AccessoryDemandCloseOpenV2Req req = new AccessoryDemandCloseOpenV2Req();
        List<AccessoryDemandCloseOpenV2Req.DeleteDemandInfo> list = demandDeleteDto.getHandledDTOs().stream().map(item ->{
            AccessoryDemandCloseOpenV2Req.DeleteDemandInfo demandInfo = new AccessoryDemandCloseOpenV2Req.DeleteDemandInfo();
            demandInfo.setDemandId(item.getDemandId());
            return demandInfo;
        }).collect(Collectors.toList());
        req.setHandledDTOs(list);
        log.info("=== 关闭辅料需求 推送致景 req：{} ===", JSONObject.toJSONString(req));
        try {
            //调用致景新接口 辅料需求 请求关闭-v2
            DataResponse<Void> response = zjPlmDemandRemoteHelper.accessoryDemandClose(req);

            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            log.info("=== 关闭辅料需求 推送致景 response：{} ===", JSONObject.toJSONString(response));
        } catch (Exception e) {
            throw new SdpDesignException("关闭辅料需求-推送zj-失败:"+e.getMessage(), e);
        }
    }

    /**
     * 好料网关闭找料需求通知致景
     */
    public void accessoryDemandCloseHouliu(AccessoryDemandCloseHouliuOpenV2Req openReq) {
        SdpDesignException.notNull(openReq, "入参为空!");
        SdpDesignException.notEmpty(openReq.getCloseDemandInfos(), "关闭的需求信息为空!");

        log.info("=== 好料网关闭找料需求通知致景 req：{} ===", JSONObject.toJSONString(openReq));
        try {
            // 调用致景新接口 辅料需求 请求关闭 (好料发起)-v2
            DataResponse<Void> response = zjPlmDemandRemoteHelper.accessoryDemandCloseHouliu(openReq);

            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            log.info("=== 好料网关闭找料需求通知致景 response：{} ===", JSONObject.toJSONString(response));
        } catch (Exception e) {
            throw new SdpDesignException("好料网关闭找料需求-通知zj-失败:"+e.getMessage(), e);
        }
    }

    /**
     * 取消采购申请
     */
    public void cancelPurchaseApply(CancelPurchaseApplyReq req) {
        SdpDesignException.notNull(req, "入参为空!");
        CancelPurchaseApplyOpenV2Req cancelReq = new CancelPurchaseApplyOpenV2Req();
        cancelReq.setBizChannel(JV_CHANNEL);
        cancelReq.setCancelReason(req.getCancelReason());
        cancelReq.setPurchaseOrderNoList(req.getPurchaseOrderNoList());

        log.info("=== 取消采购申请 推送致景 req：{} ===", JSONObject.toJSONString(cancelReq));
        try {
            // DataResponse<Void> response = purchaseApplyFollowOpenClient.cancelPurchaseApply(cancelReq);
            DataResponse<Void> response = zjPurchaseApplyFollowOpenClient.cancelPurchaseApply(cancelReq);

            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            log.info("=== 取消采购申请 推送致景 response：{} ===", JSONObject.toJSONString(response));
        } catch (Exception e) {
            throw new SdpDesignException("取消采购申请-推送zj-失败:"+e.getMessage(), e);
        }
    }

    /**
     * 批量物料采购
     */
    public PurchaseApplyFollowResultOpenResp batchPurchaseApplyMaterial(PurchaseApplyMaterialConfirmBatchReq req, Integer bizChannel, List<String> marketingPictureList) {
        SdpDesignException.notNull(req, "入参为空!");
        PurchaseApplyMaterialConfirmBatchOpenV2Req batchOpenReq = new PurchaseApplyMaterialConfirmBatchOpenV2Req();
        BeanUtils.copyProperties(req, batchOpenReq);
        batchOpenReq.setBizChannel(bizChannel);
        batchOpenReq.setMarketingPicture(marketingPictureList);
        List<PurchaseApplyMaterialConfirmBatchOpenV2Req.PurchaseListReq> purchaseApplyList = req.getPurchaseApplyList().stream()
                .map(item -> {
                    PurchaseApplyMaterialConfirmBatchOpenV2Req.PurchaseListReq materialReq = new PurchaseApplyMaterialConfirmBatchOpenV2Req.PurchaseListReq();
                    BeanUtils.copyProperties(item, materialReq);
                    //使用部位与裁剪方法, 传名称不传编码
                    materialReq.setPartUse(null);
                    materialReq.setCuttingMethod(null);

                    if (CollUtil.isNotEmpty(item.getCraftDemandInfoList())) {
                        List<CraftDemandInfoDemandOpenV2Req> craftReqList = item.getCraftDemandInfoList().stream().map(craft -> {
                            CraftDemandInfoDemandOpenV2Req craftReq = new CraftDemandInfoDemandOpenV2Req();
                            craftReq.setThirdPartyCraftDemandId(craft.getThirdPartyCraftDemandId());
                            return craftReq;
                        }).collect(Collectors.toList());
                        materialReq.setCraftDemandInfoList(craftReqList);
                    }

                    materialReq.setPurchaseApplyChannel(req.getPlatformName());
                    materialReq.setPurchaseDemandCode(item.getPurchaseRequestCode());
                    materialReq.setCustomerItemNo(item.getCustomerItemNo());
                    //散剪价格
                    materialReq.setScatterPrice(item.getScatterCutPrice());
                    materialReq.setScatterMagnification(item.getScatterCutRatio());

                    return materialReq;
        }).collect(Collectors.toList());
        batchOpenReq.setPurchaseApplyList(purchaseApplyList);

        log.info("=== 批量物料采购 推送致景 req：{} ===", JSONObject.toJSONString(batchOpenReq));
        try {
            DataResponse<PurchaseApplyFollowResultOpenResp> response = zjPurchaseApplyFollowOpenClient.batchPurchaseApplyMaterial(batchOpenReq);
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            log.info("=== 批量物料采购 推送致景 response：{} ===", JSONObject.toJSONString(response));
            return response.getData();
        } catch (Exception e) {
            throw new SdpDesignException("批量物料采购-推送zj-失败:"+e.getMessage(), e);
        }
    }

    /**
     * 取消物料
     */
    public MaterialPurchaseFollowResultOpenResp cancelMaterial(CancelMaterialOpenReq req) {
        SdpDesignException.notNull(req, "入参为空!");

        log.info("=== 取消物料 推送致景 req：{} ===", JSONObject.toJSONString(req));
        try {
            DataResponse<MaterialPurchaseFollowResultOpenResp> response = materialPurchaseFollowOpenClient.cancelMaterial(req);
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            log.info("=== 取消物料 推送致景 response：{} ===", JSONObject.toJSONString(response));
            return response.getData();
        } catch (Exception e) {
            throw new SdpDesignException("取消物料-推送zj-失败:"+e.getMessage(), e);
        }
    }

    /**
     * 推送SPU与SKC信息到致景
     */
    public DesignStyleCreateOpenV2Resp pushSpuSkc2Zj(List<DesignStyleCreateOpenV2Req.DesignStyleCreateInfo> createInfoList) {

        SdpDesignException.notEmpty(createInfoList, "入参为空!");

        DesignStyleCreateOpenV2Req createReq = DesignStyleCreateOpenV2Req.builder()
                .createInfoList(createInfoList)
                .build();

        log.info("=== 款信息-推送zj req：{} ===", JSONObject.toJSONString(createReq));
        try {
            DataResponse<DesignStyleCreateOpenV2Resp> response = zjPlmDesignRemoteHelper.createDesignPrototype(createReq);
            log.info("=== 款信息-推送zj response：{} ===", JSONObject.toJSONString(response));
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            return response.getData();
        } catch (Exception e) {
            throw new SdpDesignException("款信息-推送zj-失败:"+e.getMessage(), e);
        }
    }

    /**
     * 匹配回复物料变更推送致景
     */
    public void demandMatchUpdateMaterial(List<BomMaterialUpdateDto.MaterialUpdateInfo> materialUpdateInfos, Integer bizChannel) {

        SdpDesignException.notEmpty(materialUpdateInfos, "bom物料变更集合为空!");

        BomMaterialUpdateOpenReq openReq = new BomMaterialUpdateOpenReq();
        openReq.setBizChannel(bizChannel);
        List<BomMaterialUpdateOpenReq.MaterialUpdateInfoOpen> updateInfoOpenList = materialUpdateInfos.stream()
                .map(item -> {
                    BomMaterialUpdateOpenReq.MaterialUpdateInfoOpen updateInfo =
                            new BomMaterialUpdateOpenReq.MaterialUpdateInfoOpen();
                    BeanUtils.copyProperties(item, updateInfo);
                    return updateInfo;
                }).collect(Collectors.toList());
        openReq.setUpdateInfoList(updateInfoOpenList);

        log.info("=== 匹配回复物料变更 推送致景 入参：{} ===", JSONObject.toJSONString(openReq));
        try {
            DataResponse<Void> response = bomOrderOpenClient.demandMatchUpdateMaterial(openReq);
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            log.info("=== 匹配回复物料变更 推送致景 response：{} ===", JSONObject.toJSONString(response));
        } catch (Exception e) {
            throw new SdpDesignException("匹配回复物料变更-推送zj-失败:"+e.getMessage(), e);
        }
    }

    /**
     * 推送bom信息到致景
     */
    public BomOrderOpenResp pushBom2Zj(BomOrderAddOpenReq createReq) {
        SdpDesignException.notNull(createReq, "入参为空!");

        BomOrderOpenReq bomOrderOpenReq = createReq.getBomOrderOpenReq();
        log.info("=== 推送bom信息到致景 入参-bom单,bomOrderOpenReq：{} ===", JSONObject.toJSONString(bomOrderOpenReq));

        List<BomOrderMaterialOpenReq> bomOrderMaterialOpenReqs = createReq.getBomOrderMaterialOpenReqs();
        log.info("=== 推送bom信息到致景 入参-物料,bomOrderMaterialOpenReqs：{} ===", JSONObject.toJSONString(bomOrderMaterialOpenReqs));
        if (CollUtil.isNotEmpty(bomOrderMaterialOpenReqs)) {
            List<Map<String, Long>> materialOpenReqs = bomOrderMaterialOpenReqs.stream().map(item -> {
                Map<String, Long> materialMap = new HashMap<>();
                materialMap.put(item.getPrototypeMaterialName(), item.getBomMaterialId());
                return materialMap;
            }).collect(Collectors.toList());
            log.info("=== 推送bom信息到致景,入参-物料信息,materialOpenReqs：{} ===", JSONObject.toJSONString(materialOpenReqs));
        }

        List<BomMaterialDemandOpenReq> bomMaterialDemandOpenReqs = createReq.getBomMaterialDemandOpenReqs();
        log.info("=== 推送bom信息到致景,入参-找料需求,bomMaterialDemandOpenReqs：{} ===", JSONObject.toJSONString(bomMaterialDemandOpenReqs));
        List<CraftDemandInfoOpenReq> craftDemandInfoOpenReqs = createReq.getCraftDemandInfoOpenReqs();
        log.info("=== 推送bom信息到致景,入参-工艺需求,craftDemandInfoOpenReqs：{} ===", JSONObject.toJSONString(craftDemandInfoOpenReqs));

        List<MaterialSnapshotOpenReq> materialSnapshotOpenReqs = createReq.getMaterialSnapshotOpenReqs();
        if (CollUtil.isNotEmpty(materialSnapshotOpenReqs)) {
            List<Long> materialSnapshotIdList = StreamUtil.convertList(materialSnapshotOpenReqs, MaterialSnapshotOpenReq::getMaterialSnapshotId);
            log.info("=== 推送bom信息到致景,入参-物料快照,materialSnapshotIdList：{} ===", JSONObject.toJSONString(materialSnapshotIdList));
        }

        List<SpecialAccessoriesOpenReq> specialAccessoriesOpenReqs = createReq.getSpecialAccessoriesOpenReqs();
        if (CollUtil.isNotEmpty(specialAccessoriesOpenReqs)) {
            log.debug("=== 推送bom信息到致景,入参-特辅,specialAccessoriesOpenReqs：{} ===", JSONObject.toJSONString(specialAccessoriesOpenReqs));
            List<Long> specialAccessoriesIdList = StreamUtil.convertList(specialAccessoriesOpenReqs, SpecialAccessoriesOpenReq::getSpecialAccessoriesId);
            log.info("=== 推送bom信息到致景,入参-特辅id,specialAccessoriesIdList：{} ===", JSONObject.toJSONString(specialAccessoriesIdList));
        }
        List<DesignRemarksOpenReq> designRemarksOpenReqs = createReq.getDesignRemarksOpenReqs();
        log.debug("=== 推送bom信息到致景,入参-备注,designRemarksOpenReqs：{} ===", JSONObject.toJSONString(designRemarksOpenReqs));

        try {
            //对接致景使用新接口  新增bom-v2
            DataResponse<BomOrderOpenResp> response = zjPlmBomRemoteHelper.add(createReq);
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            log.info("=== 推送bom信息到致景, jv-bomId:{}; response：{} ===", createReq.getBomOrderOpenReq().getBomId(), JSONObject.toJSONString(response));
            return response.getData();
        } catch (Exception e) {
            throw new SdpDesignException("bom信息推送zj-失败:"+e.getMessage(), e);
        }
    }

    /**
     * bom核算更新推送给致景
     */
    public void updateBomDosageAccount(BomMaterialDosageAccountReq dosageAccountReq, String currentUserName, Integer bizChannel) {
        SdpDesignException.notNull(dosageAccountReq, "入参为空!");

        BomMaterialDosageAccountByJVV2Req req = new BomMaterialDosageAccountByJVV2Req();
        req.setCurrentUser(currentUserName);
        req.setExtBomId(dosageAccountReq.getBomId());
        req.setExtVersionNum(dosageAccountReq.getBomVersionNum());


        List<BomMaterialDosageAccountByJVV2Req.MaterialDosageAccountByJV> dosageAccountList = dosageAccountReq.getDosageAccountList().stream()
                .map(item -> {
                    BomMaterialDosageAccountByJVV2Req.MaterialDosageAccountByJV account =
                            new BomMaterialDosageAccountByJVV2Req.MaterialDosageAccountByJV();
                    BeanUtils.copyProperties(item, account);
                    return account;
                }).collect(Collectors.toList());
        req.setDosageAccountList(dosageAccountList);
        req.setBizChannel(bizChannel);

        log.info("=== bom核算更新推送给致景 req：{} ===", JSONObject.toJSONString(req));
        try {
            //调用致景新方法  jv-Bom物料清单核算用量-v2
            DataResponse<Void> response = zjPlmBomRemoteHelper.bomMaterialDosageAccountByJV(req);
            SdpDesignException.isTrue(response.isSuccessful(), response.getMessage());
            log.info("=== bom核算更新推送给致景 response：{} ===", JSONObject.toJSONString(response));
        } catch (Exception e) {
            throw new SdpDesignException("bom核算更新推送zj-失败:"+e.getMessage(), e);
        }
    }


}
