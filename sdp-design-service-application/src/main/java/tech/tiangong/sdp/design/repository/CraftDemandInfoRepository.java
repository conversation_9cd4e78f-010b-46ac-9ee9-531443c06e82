package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.mapper.CraftDemandInfoMapper;
import tech.tiangong.sdp.design.vo.query.bom.CraftDemandQuery;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 干什么
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/18 11:40
 */
@Repository
public class CraftDemandInfoRepository extends BaseRepository<CraftDemandInfoMapper, CraftDemandInfo> {

	/**
	 * 根据bomId列表查询工艺信息(不含暂存的)
	 *
	 * @param bomIds
	 * @param state 工艺需求状态
	 * @return
	 */
	public List<CraftDemandInfo> getListByBomIdsAndState(List<Long> bomIds, Integer state) {
		return baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda().in(CraftDemandInfo::getBomId, bomIds)
				.eq(CraftDemandInfo::getState, state)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(CraftDemandInfo::getIsTransient,Bool.NO.getCode()));
	}

	/**
	 * 根据bomId列表查询工艺信息(包含暂存的)
	 */
	public List<CraftDemandInfo> listByBomIdsAndState(List<Long> bomIds, Integer state) {
		return baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda().in(CraftDemandInfo::getBomId, bomIds)
				.eq(CraftDemandInfo::getState, state)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode()));
	}

	/**
	 * 根据bomId列表查询暂存的工艺信息
	 */
	public List<CraftDemandInfo> listByBomIdsAndStateTransient(List<Long> bomIds, Integer state) {
		return baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda().in(CraftDemandInfo::getBomId, bomIds)
				.eq(CraftDemandInfo::getState, state)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(CraftDemandInfo::getIsTransient, Bool.YES.getCode()));
	}

	/**
	 * 根据bomId列表查询工艺信息
	 * @param bomIds
	 * @param state
	 * @return
	 */
	public List<CraftDemandInfo> getTransientListByBomId(List<Long> bomIds, Integer state, Integer isTransient) {
		return baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda().in(CraftDemandInfo::getBomId, bomIds)
				.eq(CraftDemandInfo::getState, state)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(CraftDemandInfo::getIsTransient,isTransient));
	}

	public List<CraftDemandInfo> getListByThirdPartyCraftDemandIds(List<Long> thirdPartyCraftDemandIds) {
		if (CollectionUtil.isEmpty(thirdPartyCraftDemandIds)) {
			return Collections.emptyList();
		}
		return  baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda()
				.in(CraftDemandInfo::getThirdPartyCraftDemandId, thirdPartyCraftDemandIds)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(CraftDemandInfo::getIsTransient,Bool.NO.getCode()));
	}

	public List<CraftDemandInfo> getListByIds(Set<Long> craftDemandIds) {
		if (CollectionUtil.isEmpty(craftDemandIds)) {
			return Collections.emptyList();
		}
		return baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda().in(CraftDemandInfo::getCraftDemandId, craftDemandIds)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(CraftDemandInfo::getIsTransient,Bool.NO.getCode()));
	}

	public List<CraftDemandInfo> getListByIds(Set<Long> craftDemandIds,Integer state, Integer isTransient) {
		if (CollectionUtil.isEmpty(craftDemandIds)) {
			return Collections.emptyList();
		}
		return baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda()
				.in(CraftDemandInfo::getCraftDemandId, craftDemandIds)
				.eq(CraftDemandInfo::getState, state)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(Objects.nonNull(isTransient), CraftDemandInfo::getIsTransient, isTransient));
	}


	/**
	 * 获取临时工艺信息列表
	 * @param bomMaterialIds
	 * @return
	 */
	public List<CraftDemandInfo> getTransientCraftList(List<Long> bomMaterialIds) {
		return baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda()
				.in(CraftDemandInfo::getBomMaterialId, bomMaterialIds)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(CraftDemandInfo::getIsTransient,Bool.YES.getCode()));
	}

	public List<CraftDemandInfo> getListByMaterialId(Long bomMaterialId) {
		return baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda().in(CraftDemandInfo::getBomMaterialId, bomMaterialId)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(CraftDemandInfo::getIsTransient,Bool.NO.getCode()));
	}

	public List<CraftDemandInfo> getListByMaterialIds(List<Long> bomMaterialIds) {
		if (CollUtil.isEmpty(bomMaterialIds)) {
			return List.of();
		}
		return baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda()
				.in(CraftDemandInfo::getBomMaterialId, bomMaterialIds)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(CraftDemandInfo::getIsTransient,Bool.NO.getCode()));
	}

	public CraftDemandInfo getEntity(Long craftDemandId) {
		return baseMapper.selectOne(new QueryWrapper<CraftDemandInfo>().lambda()
				.eq(CraftDemandInfo::getCraftDemandId, craftDemandId).eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode()));
	}

	/**
	 * 根据bomId列表查询工艺信息
	 *
	 * @param bomIds
	 * @return
	 */
	public List<CraftDemandInfo> getListByBomIds(List<Long> bomIds) {
		return baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda().in(CraftDemandInfo::getBomId, bomIds)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(CraftDemandInfo::getIsTransient,Bool.NO.getCode()));
	}

	public List<CraftDemandInfo> getListByThirdPartyCraftDemandIdsAndState(List<Long> thirdPartyCraftDemandIds,Integer state) {
		return  baseMapper.selectList(new QueryWrapper<CraftDemandInfo>().lambda()
				.in(CraftDemandInfo::getThirdPartyCraftDemandId, thirdPartyCraftDemandIds)
				.eq(CraftDemandInfo::getState, state)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.eq(CraftDemandInfo::getIsTransient,Bool.NO.getCode()));
	}

	public List<CraftDemandInfo> getCraftDemandInfo(CraftDemandQuery craftQuery) {
		LambdaQueryWrapper<CraftDemandInfo> queryWrapper = new QueryWrapper<CraftDemandInfo>().lambda();
		if (Objects.nonNull(craftQuery.getPrototypeId())) {
			queryWrapper.eq(CraftDemandInfo::getPrototypeId, craftQuery.getPrototypeId());
		}

		if (CollectionUtil.isNotEmpty(craftQuery.getPrototypeIds())) {
			queryWrapper.in(CraftDemandInfo::getPrototypeId, craftQuery.getPrototypeIds());
		}

		if (Objects.nonNull(craftQuery.getState())) {
			queryWrapper.eq(CraftDemandInfo::getState, craftQuery.getState());
		}

		if (Objects.nonNull(craftQuery.getIsTransient())) {
			queryWrapper.eq(CraftDemandInfo::getIsTransient, craftQuery.getIsTransient());
		}

		return baseMapper.selectList(queryWrapper);
	}

	/**
	 * 根据bom物料id集合与工艺状态,查询工艺信息,
	 * @param bomMaterialIdList bom物料id
	 * @param state 工艺状态为null时查询所有状态
	 */
	public List<CraftDemandInfo> getListByBomMaterialIds(List<Long> bomMaterialIdList, Integer state) {
		if (CollUtil.isEmpty(bomMaterialIdList)) {
			return List.of();
		}
		return lambdaQuery()
				.in(CraftDemandInfo::getBomMaterialId, bomMaterialIdList)
				.eq(Objects.nonNull(state), CraftDemandInfo::getState, state)
				.eq(CraftDemandInfo::getIsDeleted, Bool.NO.getCode())
				.list();
	}
}
