package tech.tiangong.sdp.design.converter;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.entity.PrototypeDetail;
import tech.tiangong.sdp.design.enums.DimensionGleanTaskStateEnum;
import tech.tiangong.sdp.design.enums.PurchaseStateEnum;
import tech.tiangong.sdp.design.repository.PrototypeDetailRepository;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.vo.resp.dimensiongleantask.DimensionGleanTaskExcelResp;
import tech.tiangong.sdp.design.vo.resp.dimensiongleantask.DimensionGleanTaskListVo;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class DimensionGleanTaskFollowConverter {
    private final PrototypeRepository prototypeRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    public void fillDimensionGleanTaskListVo(List<DimensionGleanTaskListVo> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<String> designCodes = list.stream().map(DimensionGleanTaskListVo::getCustomerStyleCode).collect(Collectors.toList());
        Map<String,String> designCodeToDesignPictureMap = getLatestDesignPictureMap(designCodes);
        list.forEach(v->{
            v.setGleanStateDesc(DimensionGleanTaskStateEnum.getDesc(v.getGleanState()));
            v.setPurchaseStateDesc(PurchaseStateEnum.getDesc(v.getPurchaseState()));
            v.setDesignPictureList(StrUtil.splitTrim(v.getDesignPicture(), StrUtil.COMMA));
            //始终都要拿最新的skc图
            if(designCodeToDesignPictureMap.get(v.getCustomerStyleCode())!=null){
                v.setDesignPictureList(StrUtil.splitTrim(MapUtil.getStr(designCodeToDesignPictureMap,v.getCustomerStyleCode(),""), StrUtil.COMMA));
            }
        });
    }

    private Map<String,String> getLatestDesignPictureMap(List<String> designCodes){
        Map<String,String> designCodeToDesignPictureMap = new HashMap<>();
        if(CollectionUtils.isEmpty(designCodes)){
            return designCodeToDesignPictureMap;
        }
        List<Prototype> prototypes = prototypeRepository.getLastByDesignCodes(designCodes);
        if(!CollectionUtils.isEmpty(prototypes)){
            Map<String,Long> prototypeMap = prototypes.stream().collect(Collectors.toMap(Prototype::getDesignCode,Prototype::getPrototypeId,(k1, k2)->k2));
            List<PrototypeDetail> details = prototypeDetailRepository.getListByPrototypeIds(new ArrayList<>(prototypeMap.values()));
            Map<Long,String> prototypeIdToDesignPictureMap = details.stream().collect(Collectors.toMap(PrototypeDetail::getPrototypeId,PrototypeDetail::getDesignPicture,(k1, k2)->k2));
            prototypeMap.forEach((designCode,prototypeId)->{
                designCodeToDesignPictureMap.put(designCode,prototypeIdToDesignPictureMap.get(prototypeId));
            });
        }
        return designCodeToDesignPictureMap;
    }

    public List<DimensionGleanTaskExcelResp> trans2DimensionGleanTaskExcelResp(List<DimensionGleanTaskListVo> list){
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        List<String> designCodes = list.stream().map(DimensionGleanTaskListVo::getCustomerStyleCode).collect(Collectors.toList());
        Map<String,String> designCodeToDesignPictureMap = getLatestDesignPictureMap(designCodes);
        return list.stream().map(v->{
            DimensionGleanTaskExcelResp resp = new DimensionGleanTaskExcelResp();
            BeanUtils.copyProperties(v,resp);
            if(v.getPurchaseCreateTime()!=null){
                resp.setPurchaseCreateTime(LocalDateTimeUtil.format(v.getPurchaseCreateTime(),"yyyy-MM-dd HH:mm:ss"));
            }
            if(v.getTaskCreatedTime()!=null){
                resp.setTaskCreatedTime(LocalDateTimeUtil.format(v.getTaskCreatedTime(),"yyyy-MM-dd HH:mm:ss"));
            }
            if(v.getTaskFinishTime()!=null){
                resp.setTaskFinishTime(LocalDateTimeUtil.format(v.getTaskFinishTime(),"yyyy-MM-dd HH:mm:ss"));
            }
            if(v.getPurchaseRevisedTime()!=null){
                resp.setPurchaseRevisedTime(LocalDateTimeUtil.format(v.getPurchaseRevisedTime(),"yyyy-MM-dd HH:mm:ss"));
            }
            if(v.getGleanState()!=null){
                resp.setGleanStateDesc(DimensionGleanTaskStateEnum.getDesc(v.getGleanState()));
            }
            if(v.getPurchaseState()!=null){
                resp.setPurchaseStateDesc(PurchaseStateEnum.getDesc(v.getPurchaseState()));
            }
            //始终都要拿最新的skc图
            if(designCodeToDesignPictureMap.get(v.getCustomerStyleCode())!=null){
                resp.setDesignPictureList(StrUtil.splitTrim(MapUtil.getStr(designCodeToDesignPictureMap,v.getCustomerStyleCode(),""), StrUtil.COMMA));
            }
            return resp;
        }).collect(Collectors.toList());
    }
}
