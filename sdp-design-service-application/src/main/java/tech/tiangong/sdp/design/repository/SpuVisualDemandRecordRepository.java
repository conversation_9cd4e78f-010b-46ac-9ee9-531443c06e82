package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.SpuVisualDemandRecord;
import tech.tiangong.sdp.design.mapper.SpuVisualDemandRecordMapper;
import tech.tiangong.sdp.utils.Bool;

/**
 * 视觉需求创建记录(SpuVisualDemandRecord)服务仓库类
 *
 * <AUTHOR>
 * @since 2025-06-05 18:27:24
 */
@Repository
public class SpuVisualDemandRecordRepository extends BaseRepository<SpuVisualDemandRecordMapper, SpuVisualDemandRecord> {

    /**
     * 根据spu获取最新提交的视觉需求记录
     */
    public SpuVisualDemandRecord getLatestBySpu(String styleCode) {
        return lambdaQuery()
                .eq(SpuVisualDemandRecord::getStyleCode, styleCode)
                .eq(SpuVisualDemandRecord::getLatestState, Bool.YES.getCode())
                .last("limit 1")
                .one();
    }



}
