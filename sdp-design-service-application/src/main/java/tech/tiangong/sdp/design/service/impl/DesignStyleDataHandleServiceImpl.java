package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.id.IdPool;
import tech.tiangong.pop.common.dto.CreateProductDto;
import tech.tiangong.pop.common.resp.ShopResp;
import tech.tiangong.sdp.clothes.vo.req.SpuUpdateSampleClothesReq;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.converter.DesignStyleConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.remote.SampleClothesRemoteHelper;
import tech.tiangong.sdp.design.remote.SdpOrderHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.DesignStyleDataHandleService;
import tech.tiangong.sdp.design.vo.dto.style.*;
import tech.tiangong.sdp.design.vo.req.mq.demand.ProductUpdateMqDto;
import tech.tiangong.sdp.design.vo.req.prototype.SpuProductTypeUpdateReq;
import tech.tiangong.sdp.design.vo.req.spot.SpuPushPopReq;
import tech.tiangong.sdp.design.vo.req.spot.SpuPushPopTypeReq;
import tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo;
import tech.tiangong.sdp.design.vo.resp.spot.StylePushPopTypeVo;
import tech.tiangong.sdp.design.vo.resp.spot.StylePushPopVo;
import tech.tiangong.sdp.prod.vo.resp.StyleSizeInfoVo;
import tech.tiangong.sdp.utils.StreamUtil;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SPU表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DesignStyleDataHandleServiceImpl implements DesignStyleDataHandleService {
    private final DesignStyleRepository designStyleRepository;
    private final DesignStyleVersionRepository designStyleVersionRepository;
    private final DesignDemandRepository designDemandRepository;
    private final DesignDemandDetailRepository designDemandDetailRepository;
    private final PrototypeRepository prototypeRepository;
    private final PrototypeHistoryRepository prototypeHistoryRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final PopProductHelper popProductHelper;
    private final MqProducer mqProducer;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final SdpOrderHelper sdpOrderHelper;

    /**
     * 按SPU下SKC最新提交的款式品类为准（最新一次提交的内容，不是最新的SKC）
     *
     */
    @Override
    @SuppressWarnings("all")
    @Transactional(rollbackFor = Exception.class)
    public Integer handleOldSpu() {
        //prototype表线上15012条记录; prototype_history表线上26182条记录
        log.info("=== 处理历史SPU数据 ===");

        StopWatch sw = new StopWatch();
        sw.start("查询SPU信息");
        //1,查prototype_history表按spu分组, 查最后创建的skc(历史数据, design_style_version_id为null),线上12875条SPU
        List<DesignStyleOldDto> oldDtoList = prototypeHistoryRepository.getLatestPrototypeList();
        SdpDesignException.notEmpty(oldDtoList, "无需要刷数据的skc! ");

        //2,获取SPU对应的prototypeId集合(支持重复刷)
        List<Long> prototypeIdList = this.getPrototypeIdList(oldDtoList);

        //3,查询OPS上的廓形字典
        // Map<String, DictValueVo> shapeMap = this.getDictShapeMap();
        //根据廓形名称, 将基础资料中的廓形code映射到OPS的廓形code;
        // Map<String, String> tag2OpsMap = this.getTag2OpsMap();

        sw.stop();
        //4,拆分集合查询spu信息,封装spu
        int handleSize = 300;
        sw.start("刷数据-新增SPU");
        CollectionUtil.split(prototypeIdList, handleSize).forEach(idList -> {
            List<DesignStyleInsertDto> designStyleList = prototypeHistoryRepository.querySpuInfo(idList);
            if (CollUtil.isEmpty(designStyleList)) {
                return;
            }
            //特殊值处理: 销售群体-> 销售渠道; 尺码标准信息; 廓形; 织造方式
            // this.handleSaleGroupSizeStandard(designStyleList, shapeMap, tag2OpsMap);

            List<DesignStyleVersionInsertDto> styleVersionList = new LinkedList<>();
            List<PrototypeUpdateSpuIdDto> prototypeUpdateSpuIdList = new LinkedList<>();
            for (DesignStyleInsertDto style : designStyleList) {
                style.setDesignStyleId(IdPool.getId());
                style.setVersionNum(1);
                style.setSourceType(DesignStyleSourceTypeEnum.SELF_SPU_SOURCE.getCode());
                style.setRemark("历史SPU");
                style.setLatestSubmitTime(style.getCreatedTime());
                style.setDataBatch(DesignStyleDataBatchEnum.HISTORY.getCode());

                //spu版本表
                DesignStyleVersionInsertDto versionInsertDto = new DesignStyleVersionInsertDto();
                BeanUtils.copyProperties(style, versionInsertDto);
                long designStyleVersionId = IdPool.getId();
                versionInsertDto.setDesignStyleVersionId(designStyleVersionId);
                styleVersionList.add(versionInsertDto);

                //版本表更新spuId
                PrototypeUpdateSpuIdDto updateSpuIdDto = new PrototypeUpdateSpuIdDto();
                updateSpuIdDto.setDesignStyleVersionId(designStyleVersionId);
                updateSpuIdDto.setStyleCode(style.getStyleCode());
                prototypeUpdateSpuIdList.add(updateSpuIdDto);
            }

            //批量新增
            designStyleRepository.batchAddOldSpu(designStyleList);
            designStyleVersionRepository.batchAddOldSpu(styleVersionList);
            designStyleList = null;
            styleVersionList = null;

            //根据spu更新prototype表与prototype_history表中的design_style_version_id
            prototypeRepository.batchUpdateSpuId(prototypeUpdateSpuIdList);
            prototypeHistoryRepository.batchUpdateSpuId(prototypeUpdateSpuIdList);
            prototypeUpdateSpuIdList = null;

        });
        sw.stop();
        log.info("=== 历史拆板单SPU刷新 耗时:{}ms; 本次新增SPU总量:{} ===", sw.getTotalTimeMillis(), prototypeIdList.size());

        return prototypeIdList.size();

    }

    // private Map<String, DictValueVo> getDictShapeMap() {
    //     //查询OPS上的廓形字典
    //     return dictValueRemoteHelper.listByDictCode(DictConstant.PLM_TAG_CODE).stream()
    //             .filter(item -> Objects.equals(item.getValueParentCode(), DictConstant.PLM_CONTOUR_STYLE_CODE))
    //             .collect(Collectors.toMap(DictValueVo::getValueCode, Function.identity(), (k1, k2) -> k1));
    // }
    //
    // private Map<String, DictValueVo> getDictShapeNameMap() {
    //     //查询OPS上的廓形字典
    //     return dictValueRemoteHelper.listByDictCode(DictConstant.PLM_TAG_CODE).stream()
    //             .filter(item -> Objects.equals(item.getValueParentCode(), DictConstant.PLM_CONTOUR_STYLE_CODE))
    //             .collect(Collectors.toMap(DictValueVo::getValue, Function.identity(), (k1, k2) -> k1));
    // }
    //
    // private Map<String, String> getTag2OpsMap() {
    //     Map<String, DictValueVo> shapeNameMap = this.getDictShapeNameMap();
    //     Map<String, String> tag2ShapMap = new HashMap<>();
    //
    //     //基础资料中维护的廓形字典(历史数据中的廓形是从基础资料服务中取的, 要根据名称应用到OPS中廓形字典对应的code)
    //     List<ClothingTagVO> tagList = new ArrayList<>();
    //     // List<ClothingTagVO> tagList = tagRemoteHelper.getAllTag(Boolean.FALSE);
    //     ClothingTagVO clothingTagVO = tagList.stream()
    //             .filter(item -> Objects.equals(item.getCode(), TagTypeEnum.CONTOUR_STYLE.getCode()))
    //             .findFirst().orElse(null);
    //     if (Objects.nonNull(clothingTagVO)) {
    //         List<ClothingTagVO.ClothingTagVoItem> tagVoItemList = clothingTagVO.getNext();
    //         tagVoItemList.forEach(item -> {
    //             if (Objects.nonNull(shapeNameMap.get(item.getName()))) {
    //                 //根据廓形名称, 将基础资料中的廓形code映射到OPS的廓形code;
    //                 DictValueVo dictValueVo = shapeNameMap.get(item.getName());
    //                 tag2ShapMap.put(item.getCode(), dictValueVo.getValueCode());
    //             }
    //         });
    //     }
    //     return tag2ShapMap;
    // }

    private List<Long> getPrototypeIdList4Shape(List<DesignStyleOldDto> oldDtoList) {
        List<String> subSpuCodeList = oldDtoList.stream().map(DesignStyleOldDto::getStyleCode).distinct().collect(Collectors.toList());

        SdpDesignException.notEmpty(subSpuCodeList, "无需要同步的SPU信息");

        Map<String, String> subStyleCodeMap = subSpuCodeList.stream()
                .collect(Collectors.toMap(Function.identity(), Function.identity(), (k1, k2) -> k1));

        List<Long> subPrototypeIdList = new LinkedList<>();

        List<String> colorStyleList = new LinkedList<>();
        for (DesignStyleOldDto oldDto : oldDtoList) {
            //未同步的prototypeId
            if (Objects.nonNull(subStyleCodeMap.get(oldDto.getStyleCode()))) {
                //如果是待拆板的复色skc,取对应spu下最新提交的skc
                if (Objects.equals(oldDto.getPrototypeStatus(), PrototypeStatusEnum.WAIT_DECOMPOSE.getCode())
                        && Objects.equals(oldDto.getSampleType(), SampleTypeEnum.COMPOUND_COLORS_MAKING.getCode())) {
                    colorStyleList.add(oldDto.getStyleCode());
                    continue;
                }
                subPrototypeIdList.add(oldDto.getPrototypeId());
            }
        }
        if (CollUtil.isNotEmpty(colorStyleList)) {
            List<DesignStyleOldDto> latestColorSubmitList = prototypeRepository.latestSubmitByStyleCodes(colorStyleList);
            if (CollUtil.isNotEmpty(latestColorSubmitList)) {
                for (DesignStyleOldDto oldDto : latestColorSubmitList) {
                    subPrototypeIdList.add(oldDto.getPrototypeId());
                }
            }
        }

        return subPrototypeIdList;
    }

    private List<Long> getPrototypeIdList(List<DesignStyleOldDto> oldDtoList) {
        List<String> oldSpuCodeList = oldDtoList.stream().map(DesignStyleOldDto::getStyleCode).distinct().collect(Collectors.toList());

        //查询所有spu表中的所有style_code; 已经刷过的就不要刷了
        Set<String> styleCodeList = designStyleRepository.allStyleCode();

        //找出未同步的spu
        List<String> subSpuCodeList = CollUtil.subtractToList(oldSpuCodeList, styleCodeList);
        SdpDesignException.notEmpty(subSpuCodeList, "无需要同步的SPU信息");

        Map<String, String> subStyleCodeMap = subSpuCodeList.stream()
                .collect(Collectors.toMap(Function.identity(), Function.identity(), (k1, k2) -> k1));

        List<Long> subPrototypeIdList = new LinkedList<>();

        List<String> colorStyleList = new LinkedList<>();
        for (DesignStyleOldDto oldDto : oldDtoList) {
            //未同步的prototypeId
            if (Objects.nonNull(subStyleCodeMap.get(oldDto.getStyleCode()))) {
                //如果是待拆板的复色skc,取对应spu下最新提交的skc
                if (Objects.equals(oldDto.getPrototypeStatus(), PrototypeStatusEnum.WAIT_DECOMPOSE.getCode())
                        && Objects.equals(oldDto.getSampleType(), SampleTypeEnum.COMPOUND_COLORS_MAKING.getCode())) {
                    colorStyleList.add(oldDto.getStyleCode());
                    continue;
                }
                subPrototypeIdList.add(oldDto.getPrototypeId());
            }
        }
        if (CollUtil.isNotEmpty(colorStyleList)) {
            List<DesignStyleOldDto> latestColorSubmitList = prototypeRepository.latestSubmitByStyleCodes(colorStyleList);
            if (CollUtil.isNotEmpty(latestColorSubmitList)) {
                for (DesignStyleOldDto oldDto : latestColorSubmitList) {
                    subPrototypeIdList.add(oldDto.getPrototypeId());
                }
            }
        }

        return subPrototypeIdList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer oldSpu2Skc(String styleCode) {
        //将历史SPU的信息回刷到skc
        StopWatch sw = new StopWatch();
        sw.start("SPU信息回刷SKC");
        //1,查询历史spu中需要回刷的信息
        List<Spu2SkcUpdateDto> updateDtoList = designStyleRepository.spu2SkcUpdateInfo(styleCode);
        SdpDesignException.notEmpty(updateDtoList, "无SPU信息! ");
        sw.stop();

        //2,分批次更新
        int handleSize = 300;
        sw.start("SPU信息回刷SKC");
        CollectionUtil.split(updateDtoList, handleSize).forEach(updateList -> {
            //2.1 批量更新prototype与prototype_history表中的款式品类
            prototypeRepository.batchUpdateCategoryInfo(updateList);
            prototypeHistoryRepository.batchUpdateCategoryInfo(updateList);

            //2.2 查询spu下skc中的品质等级信息, 如果如spu中的一致, 不需要更新
            List<String> styleCodeList = updateList.stream().map(Spu2SkcUpdateDto::getStyleCode).collect(Collectors.toList());
            List<Spu2SkcUpdateDto> detailUpdateList = prototypeHistoryRepository.prototypeIdsByStyleCode(styleCodeList);
            Map<String, Spu2SkcUpdateDto> updateMap = updateList.stream().collect(Collectors.toMap(Spu2SkcUpdateDto::getStyleCode, Function.identity(), (k1, k2) -> k1));

            List<Spu2SkcUpdateDto> finalDetailUpdateList = detailUpdateList.stream().filter(item -> {
                //过滤掉不需要更新品质等级信息的数据
                Spu2SkcUpdateDto spuInfo = updateMap.get(item.getStyleCode());
                if (Objects.isNull(spuInfo)) {
                    return false;
                }
                if (Objects.equals(item.getQualityLevel(), spuInfo.getQualityLevel())) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());

            //2.3 批量更新prototype_detail中的品质等级
            prototypeDetailRepository.batchUpdateQualityLeverInfo(finalDetailUpdateList);

            //2.4 同步打版,更新款式品类与品质等级
            SpuUpdateSampleClothesReq updateClothesReq = new SpuUpdateSampleClothesReq();
            List<SpuUpdateSampleClothesReq.SpuCategoryUpdateReq> spuUpdateList = updateList.stream().map(item -> {
                SpuUpdateSampleClothesReq.SpuCategoryUpdateReq spuCategoryUpdateReq = new SpuUpdateSampleClothesReq.SpuCategoryUpdateReq();
                spuCategoryUpdateReq.setStyleCode(item.getStyleCode());
                spuCategoryUpdateReq.setCategory(item.getCategory());
                spuCategoryUpdateReq.setCategoryName(item.getCategoryName());
                return spuCategoryUpdateReq;
            }).collect(Collectors.toList());
            updateClothesReq.setSpuUpdateList(spuUpdateList);
            sampleClothesRemoteHelper.spuUpdateSampleClothes(updateClothesReq);

        });
        sw.stop();
        log.info("=== SPU信息回刷SKC 耗时:{}ms; 本次新增SPU总量:{} ===", sw.getTotalTimeMillis(), updateDtoList.size());

        return updateDtoList.size();
    }

    @Override
    public Map<String, Object> performStandardInfoHandle(List<PerformStandardExcelDto> list, Boolean refreshData) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }

        //安全类别/执行标准的字典
        // Map<String, DictValueVo> securityMap = this.getDictValueMap(DictConstant.PLM_CLOTHING_SECURITY_TYPE);
        // Map<String, DictValueVo> performStandardMap = this.getDictValueMap(DictConstant.PLM_CLOTHING_EXECUTIVE_STANDARDS);

        //设置对应字典值的编号
        // for (PerformStandardExcelDto dto : list) {
        //     if (Objects.isNull(dto) || StringUtils.isBlank(dto.getStyleCode())) {
        //         continue;
        //     }
        //
        //     if (StringUtils.isNotBlank(dto.getSecurityCategoryName())
        //             && Objects.nonNull(securityMap.get(StringUtils.trim(dto.getSecurityCategoryName())))) {
        //         dto.setSecurityCategoryCode(securityMap.get(dto.getSecurityCategoryName()).getValueCode());
        //     }
        //
        //     if (StringUtils.isNotBlank(dto.getPerformStandardName())
        //             && Objects.nonNull(performStandardMap.get(StringUtils.trim(dto.getPerformStandardName())))) {
        //         dto.setPerformStandardCode(performStandardMap.get(dto.getPerformStandardName()).getValueCode());
        //     }
        // }

        Map<String, Object> retMap = new HashMap<>();
        retMap.put("PerformStandardExcelDtoList", list);

        //封装更新sql
        this.buildUpdatePerformSql(list, retMap);

        return retMap;
    }

    @Override
    public void updateProductType(SpuProductTypeUpdateReq req) {
        log.info("=== 商品类型: {}; 商品类型编码:{}; spu数量:{} ",
                req.getProductType(), req.getProductTypeCode(), req.getStyleCodeSet().size());
        //更新spu表
        designStyleRepository.updateProductTypeBySpu(req);

        //更新spu版本表
        designStyleVersionRepository.updateProductTypeBySpu(req);

        log.info("=== spu商品类型更新完成 ===");
    }

    @Override
    public void updatePlatform(List<String> styleCodeList) {
        SdpDesignException.notEmpty(styleCodeList, "spu不能为空!");
        //查询spu
        List<DesignStyle> styleList = designStyleRepository.listByStyleCodes(styleCodeList);
        SdpDesignException.notEmpty(styleList, "spu不存在!");

        //查询spu版本
        List<DesignStyleVersion> styleVersionList = designStyleVersionRepository.listByStyleCode(styleCodeList);
        Map<String, List<DesignStyleVersion>> styleVersionGroupMap = StreamUtil.groupingBy(styleVersionList, DesignStyleVersion::getStyleCode);

        //查询店铺信息(用版本表的店铺)
        List<String> storeNameList = StreamUtil.convertListAndDistinct(styleVersionList, DesignStyleVersion::getStoreName);
        List<ShopResp> shopList = popProductHelper.getShopList(storeNameList);
        SdpDesignException.notEmpty(shopList, "店铺查询为空! {}", JSON.toJSONString(storeNameList));
        Map<String, ShopResp> shopMap = StreamUtil.list2Map(shopList, ShopResp::getShopName);

        //更新spu平台异常的spu
        List<DesignStyle> styleUpdateList = new ArrayList<>();
        List<DesignStyleVersion> styleVersionUpdateList = new ArrayList<>();
        styleList.forEach(item  -> {
            ShopResp shop = shopMap.get(item.getStoreName());
            if (Objects.isNull(shop)) {
                log.error("店铺不存在:{}", item.getStoreName());
                return;
            }
            //spu平台与店铺平台不一样
            if (!Objects.equals(item.getPlatformName(), shop.getPlatformName())) {
                DesignStyle updateStyle = new DesignStyle();
                updateStyle.setDesignStyleId(item.getDesignStyleId());
                updateStyle.setPlatformName(shop.getPlatformName());
                styleUpdateList.add(updateStyle);
            }
            //spu版本平台与店铺平台不一样
            this.buildVersionPlatformUpdate(item, styleVersionGroupMap, shopMap, styleVersionUpdateList);
        });
        if (CollUtil.isNotEmpty(styleUpdateList)) {
            designStyleRepository.updateBatchById(styleUpdateList);
        }
        if (CollUtil.isNotEmpty(styleVersionUpdateList)) {
            designStyleVersionRepository.updateBatchById(styleVersionUpdateList);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StylePushPopTypeVo> pushSpuWithType(SpuPushPopTypeReq req) {
        List<String> styleCodeList = req.getStyleCodeList();
        List<DesignStyle> styleList = designStyleRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(styleList)) {
            log.info("=== spu不存在 ===");
            return List.of();
        }

        StylePushPopTypeEnum pushPopType = req.getPushPopType();
        List<StylePushPopTypeVo> pushPopTypeVoList = new ArrayList<>(styleList.size());
        for (DesignStyle designStyle : styleList) {
            ProductUpdateMqDto.Data spotUpdateDto = ProductUpdateMqDto.Data.builder()
                    .spuCode(designStyle.getStyleCode())
                    .build();
            switch (pushPopType) {
                case UPDATE_CATEGORY:
                    //更新品类
                    spotUpdateDto.setOpType(ProductUpdateTypeEnum.UPDATE_CATEGORY.getCode());
                    spotUpdateDto.setCategoryName(designStyle.getCategoryName());
                    spotUpdateDto.setCategoryCode(designStyle.getCategory());
                    break;
                case UPDATE_CLOTHING_STYLE:
                    //更新款式风格
                    spotUpdateDto.setOpType(ProductUpdateTypeEnum.UPDATE_CLOTHING_STYLE.getCode());
                    spotUpdateDto.setClothingStyleCode(designStyle.getClothingStyleCode());
                    spotUpdateDto.setClothingStyleName(designStyle.getClothingStyleName());
                    break;
                default:
                    throw new SdpDesignException("未知操作类型");
            }
            log.info("=== 设计款更新推送pop spotUpdateDto:{}; pushPopType:{} ===", JSON.toJSONString(spotUpdateDto), pushPopType.getDesc());
            this.sendUpdateProductMessageSingle(spotUpdateDto);

            StylePushPopTypeVo pushPopTypeVo = StylePushPopTypeVo.builder()
                    .styleCode(designStyle.getStyleCode())
                    .pushPopType(pushPopType)
                    .pushContent(JSON.toJSONString(spotUpdateDto))
                    .build();
            pushPopTypeVoList.add(pushPopTypeVo);
        }

        return pushPopTypeVoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StylePushPopTypeVo> pushSkcColor(List<String> designCodeList) {
        SdpDesignException.notEmpty(designCodeList, "designCode不能为空!");

        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
        if (CollUtil.isEmpty(prototypeList)) {
            log.info("=== skc不存在 ===");
            return List.of();
        }
        List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(StreamUtil.convertList(prototypeList, Prototype::getPrototypeId));
        Map<Long, PrototypeDetail> skcdetailMap = StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);

        List<StylePushPopTypeVo> pushPopTypeVoList = new ArrayList<>(prototypeList.size());
        for (Prototype prototype : prototypeList) {
            ProductUpdateMqDto.Data spotUpdateDto = ProductUpdateMqDto.Data.builder()
                    .spuCode(prototype.getStyleCode())
                    .skc(prototype.getDesignCode())
                    .build();
            PrototypeDetail skcDetail = skcdetailMap.get(prototype.getPrototypeId());
            if (Objects.isNull(skcDetail)) {
                continue;
            }

            //更新颜色
            List<ColorInfoVo> colorInfoList = skcDetail.getColorInfoList();
            List<String> colorList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColor);
            List<String> abbrCodeList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColorAbbrCode);
            List<String> englishNameList = StreamUtil.convertListAndDistinct(colorInfoList, ColorInfoVo::getColorEnglishName);
            String color = StrUtil.join(StrUtil.SPACE, colorList);
            String colorCode = StrUtil.join(StrUtil.SPACE, englishNameList);
            String abbrCode = StrUtil.join(StrUtil.SPACE, abbrCodeList);

            spotUpdateDto.setOpType(ProductUpdateTypeEnum.UPDATE_COLOR.getCode());
            spotUpdateDto.setColor(color);
            spotUpdateDto.setColorCode(colorCode);
            spotUpdateDto.setColorAbbrCode(abbrCode);

            log.info("=== skc颜色推送pop spotUpdateDto:{}; ===", JSON.toJSONString(spotUpdateDto));
            this.sendUpdateProductMessageSingle(spotUpdateDto);

            StylePushPopTypeVo pushPopTypeVo = StylePushPopTypeVo.builder()
                    .styleCode(prototype.getStyleCode())
                    .designCode(prototype.getDesignCode())
                    .pushPopType(StylePushPopTypeEnum.UPDATE_COLOR)
                    .pushContent(JSON.toJSONString(spotUpdateDto))
                    .build();
            pushPopTypeVoList.add(pushPopTypeVo);
        }

        return pushPopTypeVoList;
    }

    @Override
    public List<StylePushPopVo> batchPushNoPriceStyle2Pop(SpuPushPopReq req) {
        List<String> styleCodeList = req.getStyleCodeList();
        //spu
        List<DesignStyle> styleList = designStyleRepository.listByStyleCodes(styleCodeList);
        Map<String, DesignStyle> styleMap = StreamUtil.list2Map(styleList, DesignStyle::getStyleCode);

        //根据spu查询设计款已提交, 未取消的skc信息, 收集不存在数据的spu作为异常数据
        List<Prototype> prototypeList = prototypeRepository.listByStyleCodes(styleCodeList, PrototypeStatusEnum.DECOMPOSED.getCode(), Boolean.FALSE);
        Map<String, List<Prototype>> prototypeSpuMap = StreamUtil.groupingBy(prototypeList, Prototype::getStyleCode);

        //skc详情
        List<Long> prototypeIds = StreamUtil.convertList(prototypeList, Prototype::getPrototypeId);
        List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIds);
        Map<Long, PrototypeDetail> detailMap = StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);

        //校验数据是否存在异常
        List<StylePushPopVo> errorList = this.getStylePushPopErrorList(styleCodeList, styleMap, prototypeSpuMap, detailMap);
        SdpDesignException.isEmpty(errorList, "存在异常数据: {}", JSON.toJSONString(errorList));

        //灵感需求
        List<Long> demandIdList = StreamUtil.convertListAndDistinct(styleList, DesignStyle::getDesignDemandId);
        Map<Long, DesignDemand> designDemandMap = CollUtil.isEmpty(demandIdList) ? Collections.emptyMap()
                : StreamUtil.list2Map(designDemandRepository.listByIds(demandIdList), DesignDemand::getDesignDemandId);
        Map<Long, DesignDemandDetail> designDemandDetailMap = StreamUtil.list2Map(designDemandDetailRepository.listByDesignDemandIds(demandIdList), DesignDemandDetail::getDesignDemandId);

        //是否推送到pop
        List<String> designCodeList = StreamUtil.convertListAndDistinct(prototypeList, Prototype::getDesignCode);
        Map<String,Boolean> skcIsExistMap = popProductHelper.checkSkcIsExist(designCodeList);

        StopWatch sw = new StopWatch();
        sw.start("未核价设计款推送pop");

        List<StylePushPopVo> stylePushPopVoList = new ArrayList<>(prototypeList.size());
        List<String> popExistSkcList = new ArrayList<>();
        prototypeSpuMap.forEach((spu, skcList) -> {
            //spu维度封装推送pop参数(pop已存在的skc不推送)
            List<CreateProductDto> createProductReqList = this.buildCreateProductReqList(skcList, detailMap, styleMap, designDemandMap, designDemandDetailMap, skcIsExistMap);

            //推送商品运营平台
            List<String> designCodes = StreamUtil.convertList(skcList, Prototype::getDesignCode);
            if (req.getPushData() && CollUtil.isNotEmpty(createProductReqList)) {
                log.info(" ==== 未核价设计款推送pop, spu:{}, skc:{} ===", spu, JSON.toJSONString(designCodes));
                popProductHelper.noticeCreateSpotProductBatch(createProductReqList);
            }

            //响应vo
            skcList.forEach(skc -> {
                StylePushPopVo pushPopVo = StylePushPopVo.builder().styleCode(spu).designCode(skc.getDesignCode()).build();
                if (skcIsExistMap.get(skc.getDesignCode())) {
                    pushPopVo.setErrorMsg("skc在pop已存在");
                    popExistSkcList.add(skc.getDesignCode());
                }
                stylePushPopVoList.add(pushPopVo);
            });

            createProductReqList = null;
            designCodes = null;
        });

        List<String> pushSkcList = StreamUtil.filterAndCovertList(stylePushPopVoList, item -> StrUtil.isBlank(item.getErrorMsg()), StylePushPopVo::getDesignCode);
        sw.stop();
        log.info(sw.prettyPrint());
        log.info("=== 未核价设计款推送pop 耗时:{}s; 推送skc:{}; pop已存在的skc:{} ===", sw.getTotalTimeSeconds(),
                JSON.toJSONString(pushSkcList), JSON.toJSONString(popExistSkcList));

        return stylePushPopVoList;
    }

    private List<CreateProductDto> buildCreateProductReqList(List<Prototype> skcList, Map<Long, PrototypeDetail> detailMap, Map<String, DesignStyle> styleMap, Map<Long, DesignDemand> designDemandMap, Map<Long, DesignDemandDetail> designDemandDetailMap, Map<String, Boolean> skcIsExistMap) {
        List<CreateProductDto> productDtoList = new ArrayList<>(skcList.size());
        skcList.forEach(prototype -> {
            //skc在pop中已存在则不推送
            if (skcIsExistMap.get(prototype.getDesignCode())) {
                log.info(" ==== 设计款推送pop  skc在pop已存在:{} ===", prototype.getDesignCode());
                return;
            }
            // skc详情
            PrototypeDetail prototypeDetail = detailMap.get(prototype.getPrototypeId());
            // spu
            DesignStyle designStyle = styleMap.get(prototype.getStyleCode());
            BigDecimal suggestedSellingPrice = new BigDecimal(designStyle.getSuggestedSellingPrice());
            // 灵感设计需求
            Long designDemandId = designStyle.getDesignDemandId();
            DesignDemand designDemand = null;
            DesignDemandDetail demandDetail = null;
            if (ObjectUtil.isNotNull(designDemandId)) {
                designDemand = designDemandMap.get(designDemandId);
                demandDetail = designDemandDetailMap.get(designDemandId);
            }



            //尺寸表EXCEL
            List<StyleSizeInfoVo> styleSizeInfoVos = sdpOrderHelper.listSizeInfoByStyleCode(List.of(designStyle.getStyleCode()));
            DictVo dictVoByCategory = null;
            if(CollectionUtil.isEmpty(styleSizeInfoVos)  || CollectionUtil.isEmpty(styleSizeInfoVos.getFirst().getSizeDetailList())) {
                //查询当前品类字典获取尺码明细表
                dictVoByCategory = dictValueRemoteHelper.getDictVoByCategory(designStyle.getCategory());
            }



            //pop推送入参
            CreateProductDto productReq = DesignStyleConverter.convertProductCreateReq(designStyle,
                    prototype, designDemand, demandDetail, prototypeDetail, suggestedSellingPrice,dictVoByCategory,styleSizeInfoVos);

            //设计师/设计组
            productReq.setDesignName(prototype.getDesignerName());
            productReq.setDesignGroup(prototype.getDesignerGroup());

            productDtoList.add(productReq);
        });
        return productDtoList;
    }

    private List<StylePushPopVo> getStylePushPopErrorList(List<String> styleCodeList,
                                                          Map<String, DesignStyle> styleMap,
                                                          Map<String, List<Prototype>> prototypeSpuMap,
                                                          Map<Long, PrototypeDetail> detailMap) {
        List<StylePushPopVo> errorList = new ArrayList<>(styleCodeList.size());
        styleCodeList.forEach(styleCode -> {
            DesignStyle designStyle = styleMap.get(styleCode);
            //spu与成本价校验
            if (Objects.isNull(designStyle)) {
                errorList.add(StylePushPopVo.builder().styleCode(styleCode).errorMsg("spu不存在").build());
                return;
            }else {
                if (StrUtil.isBlank(designStyle.getSuggestedSellingPrice())) {
                    errorList.add(StylePushPopVo.builder().styleCode(styleCode).errorMsg("spu的期望成本为空").build());
                    return;
                }else {
                    try {
                        new BigDecimal(designStyle.getSuggestedSellingPrice());
                    } catch (Exception e) {
                        errorList.add(StylePushPopVo.builder().styleCode(styleCode).errorMsg("spu的期望成本异常:" + designStyle.getSuggestedSellingPrice()).build());
                        return;
                    }
                }
            }
            //skc校验
            List<Prototype> prototypeList = prototypeSpuMap.get(styleCode);
            if (CollUtil.isEmpty(prototypeList)) {
                errorList.add(StylePushPopVo.builder().styleCode(styleCode).errorMsg("spu下无已提交的skc").build());
                return;
            }
            prototypeList.forEach(skc -> {
                PrototypeDetail prototypeDetail = detailMap.get(skc.getPrototypeId());
                if (Objects.isNull(prototypeDetail)) {
                    errorList.add(StylePushPopVo.builder().styleCode(styleCode).designCode(skc.getDesignCode()).errorMsg("skc详情信息为空").build());
                }else if (StrUtil.isBlank(prototypeDetail.getDesignPicture())) {
                    errorList.add(StylePushPopVo.builder().styleCode(styleCode).designCode(skc.getDesignCode()).errorMsg("skc设计图为空").build());
                }
            });
        });
        return errorList;
    }

    private void sendUpdateProductMessageSingle(ProductUpdateMqDto.Data mqData){
        if (Objects.isNull(mqData)) {
            return;
        }
        ProductUpdateMqDto mqDTO = ProductUpdateMqDto.builder()
                .dataList(Collections.singletonList(mqData))
                .build();
        //exchange跟取消SKC一样
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.PROTOTYPE_MANAGE_CANCEL_SKC,
                DesignMqConstant.PROTOTYPE_MANAGE_CANCEL_SKC_EXCHANGE,
                DesignMqConstant.PROTOTYPE_MANAGE_CANCEL_SKC_ROUTING_KEY,
                JSONUtil.toJsonStr(mqDTO));
        log.info("=== 现货更新推送pop mqDTO: {} ===", JSON.toJSONString(mqMessageReq));
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

    private void buildVersionPlatformUpdate(DesignStyle item, Map<String, List<DesignStyleVersion>> styleVersionGroupMap, Map<String, ShopResp> shopMap, List<DesignStyleVersion> styleVersionUpdateList) {
        List<DesignStyleVersion> versionList = styleVersionGroupMap.get(item.getStyleCode());
        versionList.forEach(version -> {
            ShopResp versionShop = shopMap.get(version.getStoreName());
            if (Objects.isNull(versionShop)) {
                log.error("店铺不存在:{}", version.getStoreName());
                return;
            }
            if (!Objects.equals(version.getPlatformName(), versionShop.getPlatformName())) {
                DesignStyleVersion updateVersion = new DesignStyleVersion();
                updateVersion.setDesignStyleVersionId(version.getDesignStyleVersionId());
                updateVersion.setPlatformName(versionShop.getPlatformName());
                styleVersionUpdateList.add(updateVersion);
            }
        });
    }

    private void buildUpdatePerformSql(List<PerformStandardExcelDto> list, Map<String, Object> retMap) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        StringBuilder updateStyleSb = new StringBuilder();
        StringBuilder updateStyleVersionSb = new StringBuilder();

        String styleTemplate = "UPDATE plm_design.design_style " +
                "set perform_standard_name = '{}', perform_standard_code = '{}', " +
                "security_category_name = '{}', security_category_code = '{}'," +
                "revised_time = NOW() " +
                "where style_code = '{}'; \n";
        String styleVersionTemplate = "UPDATE plm_design.design_style_version " +
                "set perform_standard_name = '{}', perform_standard_code = '{}', " +
                "security_category_name = '{}', security_category_code = '{}'," +
                "revised_time = NOW() " +
                "where style_code = '{}'; \n";

        for (PerformStandardExcelDto item : list) {
            if (StringUtils.isBlank(item.getStyleCode())) {
                continue;
            }
            String styleSql = StrUtil.format(styleTemplate,
                    item.getPerformStandardName(), item.getPerformStandardCode(),
                    item.getSecurityCategoryName(), item.getSecurityCategoryCode(), item.getStyleCode());
            updateStyleSb.append(styleSql);

            String styleVersionSql = StrUtil.format(styleVersionTemplate,
                    item.getPerformStandardName(), item.getPerformStandardCode(),
                    item.getSecurityCategoryName(), item.getSecurityCategoryCode(), item.getStyleCode());
            updateStyleVersionSb.append(styleVersionSql);
        }
        log.info("===== designStyle表更新: \n{} =====", updateStyleSb.toString());
        log.info("===== designStyleVersion表更新: \n{} =====", updateStyleVersionSb.toString());

        retMap.put("updateStyleSql", updateStyleSb.toString());
        retMap.put("updateStyleVersionSql", updateStyleVersionSb.toString());
    }

    // /**
    //  * 根据字典code查询对应的字典
    //  * @param dictCode 字典编码
    //  * @return Map<字段值, 字典对象>
    //  */
    // private Map<String, DictValueVo> getDictValueMap(String dictCode) {
    //     return Collections.emptyMap();
    //     // if (StringUtils.isBlank(dictCode)) {
    //     // }
    //     // return dictValueRemoteHelper.listByDictCode(dictCode).stream()
    //     //         .collect(Collectors.toMap(DictValueVo::getValue, Function.identity(), (k1, k2) -> k1));
    // }

}
