package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.query.bom.SpecialAccessoriesTransientQuery;
import tech.tiangong.sdp.design.vo.req.bom.SpecialAccessoriesTransientReq;
import tech.tiangong.sdp.design.vo.resp.bom.SpecialAccessoriesTransientVo;

/**
 * 特殊辅料_暂存表服务接口
 *
 * <AUTHOR>
 */
public interface SpecialAccessoriesTransientService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<SpecialAccessoriesTransientVo> page(SpecialAccessoriesTransientQuery query);

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    SpecialAccessoriesTransientVo getById(Long id);

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(SpecialAccessoriesTransientReq req);

    /**
     * 更新数据
     *
     * @param req 数据实体
     */
    void update(SpecialAccessoriesTransientReq req);

    /**
     * 删除数据
     *
     * @param id 主键ID
     */
    void remove(Long id);

}
