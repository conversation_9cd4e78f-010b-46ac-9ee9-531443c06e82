package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.ClothingCodeGenerateService;
import tech.tiangong.sdp.design.vo.req.ClothingCodeGenerateReq;

/**
 * <p>
 * 样衣编号生成类
 * </p>
 *
 * <AUTHOR> TG
 * @date : 2022/8/24
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/clothing-code-generate")
public class ClothingCodeGenerateInnerController {

    private final ClothingCodeGenerateService clothingCodeGenerateService;

    /**
     * 样衣编号生成（SPU和SKC）
     *
     * @return 响应结果
     */
    @PostMapping("/generate")
    public DataResponse<String> generate(@RequestBody @Validated ClothingCodeGenerateReq req) {
        return DataResponse.ok(clothingCodeGenerateService.generate(req));
    }

    /**
     * 推款初始化数据
     *
     * @return 结果
     */
    @PostMapping("/init-history")
    public DataResponse<Void> initHistory() {
        UserContentHolder.set(new UserContent()
                .setCurrentUserId(0L)
                .setCurrentUserCode("0")
                .setCurrentUserName("系统")
                .setTenantId(2L)
                .setSystemCode("SDP"));
        clothingCodeGenerateService.initHistory();
        return DataResponse.ok();
    }

    /**
     * 初始化新款号redis数据
     *
     * @return 结果
     */
    @PostMapping("/init-spu-code-redis")
    public DataResponse<Void> initSpuCodeRedis() {
        UserContentHolder.set(new UserContent()
                .setCurrentUserId(0L)
                .setCurrentUserCode("0")
                .setCurrentUserName("系统")
                .setTenantId(2L)
                .setSystemCode("SDP"));
        clothingCodeGenerateService.initNewCode();
        return DataResponse.ok();
    }
}
