package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.util.Json;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.BomOrderTransient;
import tech.tiangong.sdp.design.entity.CraftDemandInfoTransient;
import tech.tiangong.sdp.design.enums.BomMultiStateEnum;
import tech.tiangong.sdp.design.repository.BomOrderRepository;
import tech.tiangong.sdp.design.repository.BomOrderTransientRepository;
import tech.tiangong.sdp.design.service.BomCraftTransientHandleService;
import tech.tiangong.sdp.design.service.BomDemandTransientHandleService;
import tech.tiangong.sdp.design.service.BomMaterialTransientHandleService;
import tech.tiangong.sdp.design.service.BomTransientHandleService;
import tech.tiangong.sdp.design.vo.req.bom.BomCraftTransientHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomDemandTransientHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialTransientHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderUpdateV3Req;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/17 11:12
 */


@Slf4j
@Service
@RequiredArgsConstructor
public class BomTransientHandleServiceImpl implements BomTransientHandleService {

    private final BomOrderTransientRepository bomOrderTransientRepository;
    private final BomOrderRepository bomOrderRepository;
    private final BomMaterialTransientHandleService materialTransientHandleService;
    private final BomDemandTransientHandleService demandTransientHandleService;
    private final BomCraftTransientHandleService craftTransientHandleService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomOrderTransient firstTransient(BomOrderUpdateV3Req req) {
        log.info("===== bom暂存-firstTransient-start: bomId:{} =====",req.getBomId());
        Long bomId = req.getBomId();
        BomOrder bomOrder = bomOrderRepository.getById(bomId);
        //1, 新增暂存bom
        BomOrderTransient bomOrderTransient = this.createTransientBom(req, bomOrder);

        //2, 首次暂存-物料信息处理
        BomMaterialTransientHandleReq materialTransientHandleReq = this.buildMaterialTransientReq(req, bomOrder, bomOrderTransient);
        BomCraftTransientHandleReq materialCraftReq = materialTransientHandleService.firstTransient(materialTransientHandleReq);

        //3, 首次暂存-辅料需求信息处理
        BomDemandTransientHandleReq demandTransientHandleReq = this.buildDemandTransientReq(req, bomOrder, bomOrderTransient);
        BomCraftTransientHandleReq demandCraftReq = demandTransientHandleService.firstTransient(demandTransientHandleReq);

        //4, 工艺处理
        BomCraftTransientHandleReq craftTransientHandleReq = this.buildCraftTransientHandleReq(bomOrder, bomOrderTransient, materialCraftReq, demandCraftReq);
        log.info(" ===== bom暂存-firstTransient,物料-工艺交接入参: {}; bomId:{} =====", Json.serialize(craftTransientHandleReq), bomOrder.getBomId());
        craftTransientHandleService.firstTransient(craftTransientHandleReq);

        log.info("===== bom暂存-firstTransient-end: bomId:{} =====",req.getBomId());

        return bomOrderTransient;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomOrderTransient reTransient(BomOrderUpdateV3Req req) {
        log.info("===== bom暂存-reTransient-start: bomId:{} =====",req.getBomId());
        Long bomId = req.getBomId();
        BomOrder bomOrder = bomOrderRepository.getById(bomId);

        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomId);

        //1, 再次暂存-物料信息处理
        BomMaterialTransientHandleReq materialTransientHandleReq = this.buildMaterialTransientReq(req, bomOrder, transientBom);
        BomCraftTransientHandleReq materialCraftReq = materialTransientHandleService.reTransient(materialTransientHandleReq);

        //2, 再次暂存-辅料需求信息处理
        BomDemandTransientHandleReq demandTransientHandleReq = this.buildDemandTransientReq(req, bomOrder, transientBom);
        BomCraftTransientHandleReq demandCraftReq = demandTransientHandleService.reTransient(demandTransientHandleReq);

        //3, 工艺处理
        BomCraftTransientHandleReq craftTransientHandleReq = this.buildCraftTransientHandleReq(bomOrder, transientBom, materialCraftReq, demandCraftReq);
        log.info(" ===== bom暂存-reTransient,物料-工艺交接入参: {}; bomId:{} =====", Json.serialize(craftTransientHandleReq), bomOrder.getBomId());
        craftTransientHandleService.reTransient(craftTransientHandleReq);

        //4, 更新暂存表
        this.updateReTransientBom(req, bomOrder, transientBom);
        log.info("===== bom暂存-reTransient-end: bomId:{} =====",req.getBomId());

        return transientBom;
    }

    private void updateReTransientBom(BomOrderUpdateV3Req req, BomOrder bomOrder, BomOrderTransient transientBom) {
        int transientCount = bomOrder.getTransientCount() + 1;
        bomOrderTransientRepository.update(Wrappers.lambdaUpdate(BomOrderTransient.class)
                .set(BomOrderTransient::getTransientCount, transientCount)
                .set(BomOrderTransient::getTransientState, Bool.YES.getCode())
                .set(BomOrderTransient::getQuoteDesignCode, req.getQuoteDesignCode())
                .eq(BomOrderTransient::getBomId, transientBom.getBomTransientId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomOrderTransient firstTransient4Submitted(BomOrderUpdateV3Req req, BomMultiStateEnum bomMultiStateEnum) {
        //已提交的首次暂存; 删除/更新的物料,需求,工艺都有原有信息,全部拷贝一份到暂存表; 新增的直接在暂存表新增
        log.info("===== bom暂存-firstTransient4Submitted-start: bomId:{} =====",req.getBomId());
        Long bomId = req.getBomId();
        BomOrder bomOrder = bomOrderRepository.getById(bomId);
        //1, 新增暂存bom
        BomOrderTransient bomOrderTransient = this.createTransientBom(req, bomOrder);

        //2, 已提交的首次暂存-物料信息处理
        BomMaterialTransientHandleReq materialTransientHandleReq = this.buildMaterialTransientReq(req, bomOrder, bomOrderTransient);
        BomCraftTransientHandleReq materialCraftReq = materialTransientHandleService.firstTransient4Submitted(materialTransientHandleReq);

        //3, 已提交的首次暂存-辅料需求信息处理
        BomDemandTransientHandleReq demandTransientHandleReq = this.buildDemandTransientReq(req, bomOrder, bomOrderTransient);
        BomCraftTransientHandleReq demandCraftReq = demandTransientHandleService.firstTransient4Submitted(demandTransientHandleReq);

        //4, 工艺处理
        BomCraftTransientHandleReq craftTransientHandleReq = this.buildCraftTransientHandleReq(bomOrder, bomOrderTransient, materialCraftReq, demandCraftReq);
        log.info(" ===== bom暂存-firstTransient4Submitted,物料-工艺交接入参: {}; bomId:{} =====", Json.serialize(craftTransientHandleReq), bomOrder.getBomId());
        craftTransientHandleService.firstTransient4Submitted(craftTransientHandleReq);

        log.info("===== bom暂存-firstTransient4Submitted-end: bomId:{} =====",req.getBomId());

        return bomOrderTransient;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomOrderTransient reTransient4Submitted(BomOrderUpdateV3Req req, BomMultiStateEnum bomMultiStateEnum) {
        log.info("===== bom暂存-reTransient4Submitted-start: bomId:{} =====",req.getBomId());
        Long bomId = req.getBomId();
        BomOrder bomOrder = bomOrderRepository.getById(bomId);
        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(bomId);
        //已提交-暂存暂存: 前端提交过来的全是暂存信息, 直接操作暂存表; 如果是暂存添加的信息直接逻辑删除, 原数据更新为关闭或删除状态

        //1, 已提交-再次暂存-物料信息处理
        BomMaterialTransientHandleReq materialTransientHandleReq = this.buildMaterialTransientReq(req, bomOrder, transientBom);
        BomCraftTransientHandleReq materialCraftReq = materialTransientHandleService.reTransient4Submitted(materialTransientHandleReq);

        //2, 已提交-再次暂存-辅料需求信息处理
        BomDemandTransientHandleReq demandTransientHandleReq = this.buildDemandTransientReq(req, bomOrder, transientBom);
        BomCraftTransientHandleReq demandCraftReq = demandTransientHandleService.reTransient4Submitted(demandTransientHandleReq);

        //3, 工艺处理
        BomCraftTransientHandleReq craftTransientHandleReq = this.buildCraftTransientHandleReq(bomOrder, transientBom, materialCraftReq, demandCraftReq);
        log.info(" ===== bom暂存-reTransient4Submitted,物料-工艺交接入参: {}; bomId:{} =====", Json.serialize(craftTransientHandleReq), bomOrder.getBomId());
        craftTransientHandleService.reTransient4Submitted(craftTransientHandleReq);

        //4, 更新暂存表
        this.updateReTransientBom(req, bomOrder, transientBom);
        log.info("===== bom暂存-reTransient4Submitted-end: bomId:{} =====",req.getBomId());

        return transientBom;
    }


    private BomCraftTransientHandleReq buildCraftTransientHandleReq(BomOrder bomOrder, BomOrderTransient bomOrderTransient, BomCraftTransientHandleReq materialCraftReq, BomCraftTransientHandleReq demandCraftReq) {
        return BomCraftTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .transientBomId(bomOrderTransient.getBomTransientId())
                .addCraftDemandList(ListUtils.union(materialCraftReq.getAddCraftDemandList(), demandCraftReq.getAddCraftDemandList()))
                .delCraftDemandIds(Sets.union(materialCraftReq.getDelCraftDemandIds(), demandCraftReq.getDelCraftDemandIds()))
                .oldTransientCraftMap(this.mergeCraftMap(materialCraftReq.getOldTransientCraftMap(), demandCraftReq.getOldTransientCraftMap()))
                .oldNewMaterialIdMap(this.mergeIdMap(materialCraftReq.getOldNewMaterialIdMap(), demandCraftReq.getOldNewMaterialIdMap()))
                .oldNewDemandIdMap(this.mergeIdMap(materialCraftReq.getOldNewDemandIdMap(), demandCraftReq.getOldNewDemandIdMap()))
                .build();
    }

    private Map<Long, CraftDemandInfoTransient> mergeCraftMap(Map<Long, CraftDemandInfoTransient> map1,
                                                              Map<Long, CraftDemandInfoTransient> map2) {
        Map<Long, CraftDemandInfoTransient> mergeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(map1)) {
            mergeMap.putAll(map1);
        }
        if (CollUtil.isNotEmpty(map2)) {
            mergeMap.putAll(map2);
        }
        return mergeMap;
    }

    private Map<Long, Long> mergeIdMap(Map<Long, Long> map1,
                                       Map<Long, Long> map2) {
        Map<Long, Long> mergeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(map1)) {
            mergeMap.putAll(map1);
        }
        if (CollUtil.isNotEmpty(map2)) {
            mergeMap.putAll(map2);
        }
        return mergeMap;
    }


    private BomOrderTransient createTransientBom(BomOrderUpdateV3Req req, BomOrder bomOrder) {
        Long transientBomId = IdPool.getId();
        BomOrderTransient bomOrderTransient = new BomOrderTransient();
        BeanUtils.copyProperties(bomOrder, bomOrderTransient);
        bomOrderTransient.setBomTransientId(transientBomId);
        bomOrderTransient.setQuoteDesignCode(req.getQuoteDesignCode());
        bomOrderTransient.setTransientState(Bool.YES.getCode());
        bomOrderTransient.setTransientCount(1);
        bomOrderTransientRepository.save(bomOrderTransient);
        return bomOrderTransient;
    }

    private BomDemandTransientHandleReq buildDemandTransientReq(BomOrderUpdateV3Req req, BomOrder bomOrder, BomOrderTransient transientBom) {
        return BomDemandTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .transientBom(transientBom)
                .addBomMaterialDemandList(req.getAddBomMaterialDemandList())
                .updateBomMaterialDemandList(req.getUpdateBomMaterialDemandList())
                .delBomMaterialDemandIds(req.getDelBomMaterialDemandIds())
                .build();
    }

    private BomMaterialTransientHandleReq buildMaterialTransientReq(BomOrderUpdateV3Req req, BomOrder bomOrder, BomOrderTransient transientBom) {
        return BomMaterialTransientHandleReq.builder()
                .bomOrder(bomOrder)
                .transientBom(transientBom)
                .addBomMaterials(req.getAddBomMaterials())
                .updateBomMaterials(req.getUpdateBomMaterials())
                .delBomMaterialIds(req.getDelBomMaterialIds())
                .build();
    }

}
