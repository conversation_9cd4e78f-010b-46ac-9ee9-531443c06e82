package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualTaskOnShelf;
import tech.tiangong.sdp.design.mapper.VisualTaskOnShelfMapper;
import tech.tiangong.sdp.utils.Bool;

import java.util.Collection;
import java.util.List;

/**
 * (VisualTaskOnShelf)服务仓库类
 */
@Repository
public class VisualTaskOnShelfRepository extends BaseRepository<VisualTaskOnShelfMapper, VisualTaskOnShelf> {

    public List<VisualTaskOnShelf> listLatestByTaskId(Collection<Long> sizeTaskIdList) {
        return lambdaQuery()
                .in(VisualTaskOnShelf::getTaskId, sizeTaskIdList)
                .eq(VisualTaskOnShelf::getIsLatest, Bool.YES.getCode())
                .list();
    }

    public List<VisualTaskOnShelf> getLatestByTaskIdList(List<Long> taskIdList) {
        return this.list(new LambdaQueryWrapper<VisualTaskOnShelf>()
                .in(VisualTaskOnShelf::getTaskId, taskIdList)
                .eq(VisualTaskOnShelf::getIsLatest, 1));
    }
}
