package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.SpotSkcService;
import tech.tiangong.sdp.design.vo.req.spot.ListSkcForOfpReq;
import tech.tiangong.sdp.design.vo.req.spot.QuerySkcForOfpReq;
import tech.tiangong.sdp.design.vo.req.spot.SyncSpotSkcPurchaseOrderReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSkcForOfpVo;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/spot-skc")
public class SpotSkcInnerController extends BaseController {

    private final SpotSkcService spotSkcService;
    /**
     * 同步现货SKC的首单信息
     */
    @PostMapping("/sync-spot-skc-first-purchase-order")
    public DataResponse<Void> syncSpotSkcFirstPurchaseOrder(@RequestBody SyncSpotSkcPurchaseOrderReq req) {
        spotSkcService.syncSpotSkcFirstPurchaseOrder(req);
        return DataResponse.ok();
    }

    /**
     * 根据来源分页查询SKC（可查现货管理的款或者买手系统的款），以skc维度返回
     */
    @PostMapping("/query-skc-for-ofp")
    public DataResponse<PageRespVo<SpotSkcForOfpVo>> querySkcForOfp(@RequestBody QuerySkcForOfpReq req) {
        return DataResponse.ok(spotSkcService.querySkcForOfp(req));
    }

    /**
     * 通过买手spu/skc分页查询，以skc维度返回
     */
    @PostMapping("/list-skc-for-ofp")
    public DataResponse<List<SpotSkcForOfpVo>> listSkcForOfp(@RequestBody ListSkcForOfpReq req) {
        return DataResponse.ok(spotSkcService.listSkcForOfp(req));
    }
}
