package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.MaterialSkuSnapshotService;
import tech.tiangong.sdp.design.vo.query.MaterialSkuSnapshotQuery;
import tech.tiangong.sdp.design.vo.resp.material.MaterialSkuSnapshotVo;


/**
 * 好料网sku快照表管理-web
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/materialSkuSnapshot")
public class MaterialSkuSnapshotController extends BaseController {
    private final MaterialSkuSnapshotService materialSkuSnapshotService;

    /**
     * 查询列表（分页）
     *
     * @param queryDTO 分页对象
     * @return PageRespVo<MaterialSkuSnapshotVo>
     */
    @GetMapping("/page")
    public DataResponse<PageRespVo<MaterialSkuSnapshotVo>> page(MaterialSkuSnapshotQuery queryDTO) {
        return DataResponse.ok(materialSkuSnapshotService.page(queryDTO));
    }

    /**
     * 详情
     *
     * @param id 主键
     * @return 响应结果
     */
    @GetMapping("/{id}")
    public DataResponse<MaterialSkuSnapshotVo> getById(@PathVariable(value = "id") Long id) {
        return DataResponse.ok(materialSkuSnapshotService.getById(id));
    }


}
