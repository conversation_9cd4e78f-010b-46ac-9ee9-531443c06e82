package tech.tiangong.sdp.design.remote.zj.scm;

import cn.yibuyun.framework.net.DataResponse;
import com.yibuyun.scm.common.dto.accessories.response.CategoryPageVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import tech.tiangong.sdp.core.config.ZjOpenFeignUserContentConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @version :1.0
 */
@FeignClient(value = "scm-service", path = "/zj-tg-api/scm",
        contextId = "SCMAccessoriesCategoryClient", configuration = ZjOpenFeignUserContentConfig.class,
        url = "${cx-tg.domain.url}")
public interface ZjSCMAccessoriesCategoryClient {

    /**
     * 类目 - 商品类目
     * <p>
     * 商品类型
     * </p>
     * <p>
     *     <ul>
     *        <li>
     *            辅料 : 10
     *        </li>
     *        <li>
     *            工艺 : 20
     *        </li>
     *     </ul>
     * </p>
     *
     * @param productType 商品类型
     * @return 类目
     */
    @GetMapping(value = "/product/inner/v1/category/list/{productType}")
    DataResponse<List<CategoryPageVo>> listByProductType(@PathVariable("productType") String productType);

}
