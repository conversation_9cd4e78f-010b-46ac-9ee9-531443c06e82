package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.DesignStyleVersion;
import tech.tiangong.sdp.design.mapper.DesignStyleVersionMapper;
import tech.tiangong.sdp.design.vo.dto.style.DesignStyleVersionInsertDto;
import tech.tiangong.sdp.design.vo.query.style.DesignStyleVersionQuery;
import tech.tiangong.sdp.design.vo.req.prototype.SpuProductTypeUpdateReq;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * SPU_版本表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class DesignStyleVersionRepository extends BaseRepository<DesignStyleVersionMapper, DesignStyleVersion> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<DesignStyleVersion> findPage(DesignStyleVersionQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public void batchAddOldSpu(List<DesignStyleVersionInsertDto> styleVersionList) {
        if (CollUtil.isEmpty(styleVersionList)) {
            return;
        }
        baseMapper.batchAddOldSpu(styleVersionList);
    }

    public List<DesignStyleVersion> listByStyleCode(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(DesignStyleVersion::getStyleCode, styleCodeList)
                .list();
    }

    public DesignStyleVersion getLatestByStyleCode(String styleCode) {
        if (StringUtils.isBlank(styleCode)) {
            return null;
        }
        return lambdaQuery()
                .eq(DesignStyleVersion::getStyleCode, styleCode)
                .orderByDesc(DesignStyleVersion::getVersionNum)
                .last("limit 1").one();
    }

    /**
     * 根据spu批量更新商品类型
     */
    public Boolean updateProductTypeBySpu(SpuProductTypeUpdateReq req) {
        if (Objects.isNull(req)) {
            return null;
        }
        return lambdaUpdate()
                .set(DesignStyleVersion::getProductType, req.getProductType())
                .set(DesignStyleVersion::getProductTypeCode, req.getProductTypeCode())
                .in(DesignStyleVersion::getStyleCode, req.getStyleCodeSet())
                .update();
    }
}
