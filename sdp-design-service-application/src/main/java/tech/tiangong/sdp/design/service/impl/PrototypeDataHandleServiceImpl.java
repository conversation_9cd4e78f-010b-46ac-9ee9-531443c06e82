package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.net.DataResponse;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import tech.tiangong.pop.common.dto.ProductImageChangeStateDto;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.remote.zj.design.ZjPlmDesignRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.OnShelfSpuService;
import tech.tiangong.sdp.design.service.PrototypeDataHandleService;
import tech.tiangong.sdp.design.vo.dto.product.PopProductImageChangeStateDto;
import tech.tiangong.sdp.design.vo.req.mq.ProductImage2ZjMqDto;
import tech.tiangong.sdp.design.vo.req.prototype.FirstVersionTimeUpdateReq;
import tech.tiangong.sdp.design.vo.req.prototype.SkcOnShelfImg2ZjReq;
import tech.tiangong.sdp.design.vo.req.prototype.SyncOnShelfImg2ZjReq;
import tech.tiangong.sdp.design.vo.req.prototype.SyncOnShelfImgReq;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SkcBaseInnerReq;
import tech.tiangong.sdp.design.vo.req.zj.design.PrototypeMarketingPictureSaveOpenV2Req;
import tech.tiangong.sdp.design.vo.resp.prototype.SkcBaseInnerVo;
import tech.tiangong.sdp.design.vo.resp.style.ProductImageSyncVo;
import tech.tiangong.sdp.utils.StreamUtil;
import tech.zj.quanbu.vega2.common.util.AesUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 版单-数据处理服务接口
 *
 * <AUTHOR>
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class PrototypeDataHandleServiceImpl implements PrototypeDataHandleService {
    private final PrototypeRepository prototypeRepository;
    private final PrototypeHistoryRepository historyRepository;
    private final DesignLogRepository designLogRepository;
    private final DesignStyleVersionRepository designStyleVersionRepository;
    private final OnShelfSkcRepository onShelfSkcRepository;
    private final OnShelfSpuService onShelfSpuService;
    private final PopProductHelper popProductHelper;
    private final OnShelfSkcRepository onshelfSkcRepository;
    private final OnShelfSpuRepository onShelfSpuRepository;
    private final ZjPlmDesignRemoteHelper zjPlmDesignRemoteHelper;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;


    @Value("${db.field.aes.key:9633663598ab09aaa1074c95acf10d24}")
    private String PLM_AES_KEY;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer encryptPurchaserPhone(String purchaserCode) {
        //purchaserCode为空, 查询所有版单的客户手机号(group by; 线上总量156条)
        List<String> phoneList = prototypeRepository.getPurchaserPhone(purchaserCode);
        List<String> historyPhoneList = historyRepository.getPurchaserPhone(purchaserCode);

        //待加密的手机号
        List<String> unEncryptList = phoneList.stream()
                // .filter(StringUtils::isNumeric)
                .filter(item -> StringUtils.isNotBlank(item) && item.length() < 32)
                .distinct()
                .collect(Collectors.toList());
        List<String> unEncryptHistoryList = historyPhoneList.stream()
                // .filter(StringUtils::isNumeric)
                .filter(item -> StringUtils.isNotBlank(item) && item.length() < 32)
                .distinct()
                .collect(Collectors.toList());

        //加密手机号
        // LocalDateTime updateTime = LocalDateTime.now();
        // if (CollUtil.isNotEmpty(unEncryptList)) {
        //     unEncryptList.forEach(item -> {
        //         prototypeRepository.update(Wrappers.lambdaUpdate(Prototype.class)
        //                 .set(Prototype::getPurchaserContactMobile, item)
        //                 .set(Prototype::getRevisedTime, updateTime)
        //                 .set(Prototype::getReviserId, 0L)
        //                 .set(Prototype::getReviserName, "系统")
        //                 .eq(Prototype::getPurchaserContactMobile, item)
        //         );
        //     });
        // }
        //
        // if (CollUtil.isNotEmpty(unEncryptHistoryList)) {
        //     unEncryptHistoryList.forEach(item -> {
        //         historyRepository.update(Wrappers.lambdaUpdate(PrototypeHistory.class)
        //                 .set(PrototypeHistory::getPurchaserContactMobile, item)
        //                 .set(PrototypeHistory::getRevisedTime, updateTime)
        //                 .set(PrototypeHistory::getReviserId, 0L)
        //                 .set(PrototypeHistory::getReviserName, "系统")
        //                 .eq(PrototypeHistory::getPurchaserContactMobile, item)
        //         );
        //     });
        // }
        return CollUtil.isEmpty(unEncryptList) ? 0 : unEncryptList.size();
    }

    @Override
    public String encryptPhone(String phoneNum) {
        try {
            return AesUtil.encrypt(phoneNum, this.PLM_AES_KEY);
        } catch (Exception e) {
            log.error("=== 加密失败 ====");
        }
        return "加密失败";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateFirstVersionTime(FirstVersionTimeUpdateReq req) {
        //查询 完成了【设计拆版】的拆板日志记录
        LocalDateTime startTime = LocalDateTime.parse(req.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        SdpDesignException.notNull(startTime, "开始时间不能为空! ");

        List<DesignLog> designLogList = designLogRepository.getDonePrototypeByTime(startTime);
        if (CollUtil.isEmpty(designLogList)) {
            return 0;
        }

        //线上21262
        Map<String, DesignLog> logMap = designLogList.stream().collect(Collectors.toMap(DesignLog::getDesignCode, Function.identity(), (k1, k2) -> k1));
        int handleSize = 500;
        CollectionUtil.split(designLogList, handleSize).forEach(logList -> {
            List<String> designCodeList = logList.stream().map(DesignLog::getDesignCode).distinct().collect(Collectors.toList());
            List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
            if (CollUtil.isNotEmpty(prototypeList)) {
                List<Prototype> updatePrototypeList = new LinkedList<>();
                prototypeList.forEach(item -> {
                    DesignLog designLog = logMap.get(item.getDesignCode());
                    if (Objects.isNull(designLog)) {
                        return;
                    }
                    updatePrototypeList.add(Prototype.builder()
                            .prototypeId(item.getPrototypeId())
                            .firstVersionDoneTime(designLog.getCreatedTime())
                            .build());
                });
                if (CollUtil.isNotEmpty(updatePrototypeList)) {
                    prototypeRepository.updateBatchById(updatePrototypeList);
                }
            }

        });
        return designLogList.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateBuyerStyleVersionId(String designCode) {
        //查询缺少styleVersionId的买手款
        List<Prototype> prototypeList = prototypeRepository.listStyleVersionIdIsNull(designCode);
        SdpDesignException.notEmpty(prototypeList, "无StyleVersionId为null的设计款");

        List<String> styleCodeList = StreamUtil.convertListAndDistinct(prototypeList, Prototype::getStyleCode);
        List<DesignStyleVersion> styleVersionList = designStyleVersionRepository.listByStyleCode(styleCodeList);

        Map<String, List<DesignStyleVersion>> styleVersionMap = StreamUtil.groupingBy(styleVersionList, DesignStyleVersion::getStyleCode);

        List<Prototype> prototypeUpdateList = new LinkedList<>();
        prototypeList.forEach(prototype -> {

            //skc创建时对应的spu版本id
            DesignStyleVersion designStyleVersion = styleVersionMap.get(prototype.getStyleCode()).stream()
                    .sorted(Comparator.comparing(DesignStyleVersion::getCreatedTime).reversed())
                    .filter(styleVersion -> prototype.getCreatedTime().equals(styleVersion.getCreatedTime())
                            || prototype.getCreatedTime().isAfter(styleVersion.getCreatedTime()))
                    .findFirst().orElse(null);
            if (Objects.isNull(designStyleVersion)) {
                log.info("=== 找不到对应时间的styleVersion: skc:{} ===", prototype.getDesignCode());
                return;
            }
            //更新prototype表的styleVersionId
            Prototype prototypeUpdate = Prototype.builder()
                    .prototypeId(prototype.getPrototypeId())
                    .designStyleVersionId(designStyleVersion.getDesignStyleVersionId())
                    .build();
            prototypeUpdateList.add(prototypeUpdate);

            //根据designCode更新对应prototypeHistory表的styleVersionId
            historyRepository.updateStyleVersionId(prototype.getDesignCode(), designStyleVersion.getDesignStyleVersionId());
        });
        prototypeRepository.updateBatchById(prototypeUpdateList);

        return prototypeList.size();
    }

    @Override
    public ProductImageSyncVo syncOnShelfImg(SyncOnShelfImgReq req) {
        log.info("==== 同步商品上架图-req:{} ======", JSON.toJSONString(req));
        List<String> spuReqList = req.getStyleCodeList();
        //需要同步上架图的旧JV的spu
        List<String> syncSpuList = onShelfSkcRepository.listSyncSpu(spuReqList, req.getForceSync());
        SdpDesignException.notEmpty(syncSpuList, "无符合同步条件的spu");

        //已有上架图的skc
        List<String> oldSkcList = onShelfSkcRepository.listAllSkc();
        Map<String, String> oldSkcMap = StreamUtil.list2Map(oldSkcList, String::valueOf);

        String logKey = RandomUtil.randomString(6);
        if (req.getUseThread()) {
            //多线程,分批次查询POP获取spu上架图信息
            int splitSize = syncSpuList.size() / req.getHandlerThreadNum() + 1;
            List<List<String>> puSplitList = CollectionUtil.split(syncSpuList, splitSize);
            for (List<String> spuList : puSplitList) {
                String threadKy = RandomUtil.randomString(6);
                ExecutorService executorService = Executors.newSingleThreadExecutor();
                executorService.execute(() -> {
                    this.handleSyncProductImage(req, spuList, logKey, threadKy, oldSkcMap);
                });
            }
            return null;
        }else {
            return this.handleSyncProductImage(req, syncSpuList, logKey, null, oldSkcMap);
        }
    }

    @Override
    public List<String> onShelfImg2Zj(SkcOnShelfImg2ZjReq req) {
        List<String> designCodeList = req.getDesignCodeList();
        List<OnShelfSkc> onShelfSkcList = onshelfSkcRepository.listByDesignCodes(designCodeList);
        SdpDesignException.notEmpty(onShelfSkcList, "无上架skc!");
        List<String> pushDesignCodeList = StreamUtil.convertListAndDistinct(onShelfSkcList, OnShelfSkc::getDesignCode);

        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
        SdpDesignException.notEmpty(prototypeList, "skc不存在!");
        Map<String, Prototype> prototypeMap = StreamUtil.list2Map(prototypeList, Prototype::getDesignCode);

        List<String> styleCodeList = StreamUtil.convertListAndDistinct(onShelfSkcList, OnShelfSkc::getStyleCode);
        List<OnShelfSpu> onShelfSpuList =onShelfSpuRepository.listByStyleCodeList(styleCodeList);
        Map<String, OnShelfSpu> onShelfSpuMap = StreamUtil.list2Map(onShelfSpuList, OnShelfSpu::getStyleCode);

        onShelfSkcList.forEach(onShelfSkc -> {
            ProductImage2ZjMqDto dto = new ProductImage2ZjMqDto();
            dto.setDesignCode(onShelfSkc.getDesignCode());
            dto.setSkcImageList(onShelfSkc.getSkcImageList());
            dto.setStyleCode(onShelfSkc.getStyleCode());

            Prototype prototype = prototypeMap.get(onShelfSkc.getDesignCode());
            SdpDesignException.notNull(prototype, "skc不存在!{}", prototype.getDesignCode());
            dto.setBizChannel(prototype.getBizChannel());

            OnShelfSpu onShelfSpu = onShelfSpuMap.get(onShelfSkc.getStyleCode());
            dto.setSpuDetailImageList(onShelfSpu.getSpuDetailImageList());

            onShelfSpuService.productImageUpdate2Zj(dto);
        });

        return pushDesignCodeList;
    }

    @Override
    public Integer syncImg2Zj(SyncOnShelfImg2ZjReq req) {
        log.info("==== 同步上架图给致景-req:{} ======", JSON.toJSONString(req));
        //需要同步的skc
        List<String> waitPushSkcList = null;
        if (CollUtil.isNotEmpty(req.getDesignCodeList())) {
            List<OnShelfSkc> onShelfSkcList = onshelfSkcRepository.listByDesignCodes(req.getDesignCodeList());
            SdpDesignException.notEmpty(onShelfSkcList, "无上架skc!");
            waitPushSkcList = StreamUtil.convertListAndDistinct(onShelfSkcList, OnShelfSkc::getDesignCode);
        }else {
            //线上数量: 22929; (250325之后的数据: 467)
            waitPushSkcList = onshelfSkcRepository.listAllSkc();
        }
        SdpDesignException.notEmpty(waitPushSkcList, "无符合同步条件的skc");

        //多线程同步
        int splitSize = waitPushSkcList.size() / req.getHandlerThreadNum() + 1;
        String logKey = RandomUtil.randomString(8);
        List<List<String>> skcSplitList = CollectionUtil.split(waitPushSkcList, splitSize);
        log.info("==== 同步上架图给致景 线程数量:{} ====", skcSplitList.size());
        int threadNum = 1;
        for (List<String> skcList : skcSplitList) {
            int threadKey = threadNum;
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            executorService.execute(() -> {
                this.handleSyncImg2Zj(skcList, logKey, threadKey, req);
            });
            threadNum ++;
        }
        return waitPushSkcList.size();
    }

    @Override
    public void updateBizChannel(String styleCode) {
        if (StrUtil.isBlank(styleCode)) {
            return;
        }
        zjDesignRemoteHelper.updateBizChannelBySpu(Collections.singletonList(styleCode));
    }

    private void handleSyncImg2Zj(List<String> handleSkcList, String logKey, int threadKey, SyncOnShelfImg2ZjReq req) {
        log.info("=== 同步上架图给致景 logKey:{}; threadKey:{}; skc数量:{} ===", logKey, threadKey, handleSkcList.size());
        StopWatch sw = new StopWatch();
        sw.start("同步上架图给致景, logKey:" + logKey + "; threadKey:" + threadKey);

        String timeStr = "2025-03-26 00:00:00";
        LocalDateTime handleEndTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));

        List<List<String>> splitShelfSkcList = CollectionUtil.split(handleSkcList, req.getHandleSize());
        int countNum = 0;
        List<String> pushSkcList = new LinkedList<>();
        List<String> failSkcList = new LinkedList<>();
        for (List<String> designCodeList : splitShelfSkcList) {
            countNum++;
            log.info("=== 同步上架图给致景 logKey:{}; threadKey:{}; countNum:{} ===", logKey, threadKey, countNum);
            //查询skc,spu上架图信息
            List<OnShelfSkc> onShelfSkcList = onshelfSkcRepository.listByDesignCodes(designCodeList);
            if (CollUtil.isEmpty(onShelfSkcList)) {
                continue;
            }
            List<String> styleCodeList = StreamUtil.convertListAndDistinct(onShelfSkcList, OnShelfSkc::getStyleCode);
            List<OnShelfSpu> onShelfSpuList = onShelfSpuRepository.listByStyleCodeList(styleCodeList);
            if (CollUtil.isEmpty(onShelfSpuList)) {
                continue;
            }
            Map<String, OnShelfSpu> onShelfSpuMap = StreamUtil.list2Map(onShelfSpuList, OnShelfSpu::getStyleCode);
            //查询skc的渠道信息
            SkcBaseInnerReq innerReq = new SkcBaseInnerReq();
            innerReq.setDesignCodeList(designCodeList);
            innerReq.setQueryAllPrototype(Boolean.TRUE);
            List<SkcBaseInnerVo> prototypeList = prototypeRepository.listBaseSkc(innerReq);
            if (CollUtil.isEmpty(prototypeList)) {
                continue;
            }
            Map<String, SkcBaseInnerVo> prototypeMap = StreamUtil.list2Map(prototypeList, SkcBaseInnerVo::getDesignCode);

            //封装入参, 推送致景
            for (OnShelfSkc onShelfSkc : onShelfSkcList) {
                //推送skc上架图为空的
                if (req.getPushEmptyImgSkc() && CollUtil.isNotEmpty(onShelfSkc.getSkcImageList())) {
                    continue;
                }
                PrototypeMarketingPictureSaveOpenV2Req openReq = this.buildPictureSaveOpenV2Req(onShelfSkc, onShelfSpuMap, prototypeMap, req.getOnlyHistory(), handleEndTime);
                if (openReq == null) {
                    continue;
                }
                try {
                    if (req.getShowZjLog()) {
                        log.info("上架图推送致景 请求参数 {}", JSON.toJSONString(openReq));
                    }
                    this.zjSaveMarketingPicture(openReq);
                    pushSkcList.add(openReq.getExtDesignCode());
                } catch (Exception e) {
                    failSkcList.add(openReq.getExtDesignCode());
                }
            }
            onShelfSkcList = null;
            onShelfSpuList = null;
            prototypeList = null;
            onShelfSpuMap = null;
            prototypeMap = null;
        }
        sw.stop();
        log.info(sw.prettyPrint());
        log.info("==== 同步上架图给致景 logKey:{}; threadKey:{}; 推送失败skc:{}; =========", logKey, threadKey, JSON.toJSONString(failSkcList));
        log.info("==== 同步上架图给致景 完成,logKey:{}; threadKey:{}; 同步skc数量:{}; 耗时:{}s; =========",
                logKey, threadKey, pushSkcList.size(),  sw.getTotalTimeSeconds());
    }

    private PrototypeMarketingPictureSaveOpenV2Req buildPictureSaveOpenV2Req(OnShelfSkc item,
                                                                             Map<String, OnShelfSpu> onShelfSpuMap,
                                                                             Map<String, SkcBaseInnerVo> prototypeMap,
                                                                             Boolean onlyHistory, LocalDateTime handleEndTime) {
        //是否只同步历史数据
        if (onlyHistory && item.getCreatedTime().isAfter(handleEndTime)) {
            log.info("=== skc不是历史款: {} ===", item.getDesignCode());
            return null;
        }
        OnShelfSpu onShelfSpu = onShelfSpuMap.get(item.getStyleCode());
        if (Objects.isNull(onShelfSpu)) {
            log.info("=== 同步上架图spu上架信息不存在! skc:{} === ", item.getDesignCode());
            return null;
        }

        SkcBaseInnerVo skcBaseInnerVo = prototypeMap.get(item.getDesignCode());
        if (Objects.isNull(skcBaseInnerVo)) {
            log.info("=== 同步上架图skc信息不存在! skc:{} === ", item.getDesignCode());
            return null;
        }

        PrototypeMarketingPictureSaveOpenV2Req openReq = new PrototypeMarketingPictureSaveOpenV2Req();
        openReq.setExtStyleCode(item.getStyleCode());
        openReq.setExtDesignCode(item.getDesignCode());
        openReq.setMarketingPicture(item.getSkcImageList());
        openReq.setStyleMarketingPicture(onShelfSpu.getSpuDetailImageList());
        openReq.setBizChannel(skcBaseInnerVo.getBizChannel());
        return openReq;
    }

    /**
     * 推送上架图给致景
     *
     */
    public void zjSaveMarketingPicture(PrototypeMarketingPictureSaveOpenV2Req openReq) {
        // log.info("上架图推送致景 请求参数 {}", JSON.toJSONString(openReq));
        // log.info("上架图推送致景 请求参数 skc:{}", openReq.getExtDesignCode());
        DataResponse<Void> dataResponse = zjPlmDesignRemoteHelper.saveMarketingPicture(openReq);
        log.info("上架图推送致景 响应结果 skc:{}; dataResponse:{}", openReq.getExtDesignCode(), JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            throw new SdpDesignException("上架图推送致景-调用zj服务失败 " + dataResponse.getMessage());
        }
    }


    private ProductImageSyncVo handleSyncProductImage(SyncOnShelfImgReq req, List<String> spuList, String logKey, String threadKy, Map<String, String> oldSkcMap) {
        StopWatch sw = new StopWatch();
        sw.start("spu商品图同步, logKey:" + logKey + "; threadKy:" + threadKy);
        int handleSize = req.getHandleSize();
        List<List<String>> spuSplitList = CollectionUtil.split(spuList, handleSize);
        int spuSyncNum = 0;
        int skcSyncNum = 0;
        int countNum = 1;
        ProductImageSyncVo syncVo = new ProductImageSyncVo();
        for (List<String> styleCodeList : spuSplitList) {
            log.info("==== spu商品图同步, logKey:{}; threadKy:{}; countNum:{} ====", logKey, threadKy, countNum);
            List<String> popSpuList = new LinkedList<>();
            //从POP查询上架图信息
            Set<String> spuSet = new HashSet<>(styleCodeList);
            ProductImageChangeStateDto imageDto = popProductHelper.batchQueryImages(spuSet);
            // ProductImageChangeStateDto imageDto = new ProductImageChangeStateDto();
            List<ProductImageChangeStateDto.Data> dataList = imageDto.getDataList();
            if (CollUtil.isEmpty(dataList)) {
                continue;
            }
            for (ProductImageChangeStateDto.Data data : dataList) {
                List<ProductImageChangeStateDto.Skc> skcList = data.getSkcList();
                if (CollUtil.isNotEmpty(skcList)) {
                    if (req.getForceSync()) {
                        skcSyncNum += skcList.size();
                    }else {
                        //过滤已同步的skc
                        for (ProductImageChangeStateDto.Skc skc : skcList) {
                            if (Objects.isNull(oldSkcMap.get(skc.getSkc()))) {
                                skcSyncNum ++;
                            }
                        }
                    }
                }
                popSpuList.add(data.getSpuCode());
            }
            //出参转换
            PopProductImageChangeStateDto stateDto = this.coverProductImageStateDto(imageDto, oldSkcMap, req.getForceSync());
            //同步商品上架图
            onShelfSpuService.productImageUpdate(stateDto);
            imageDto = null;
            stateDto = null;
            spuSyncNum += dataList.size();
            log.info("===pop spu: {} ====", JSON.toJSONString(popSpuList));
            popSpuList = null;
            countNum ++;
        }
        sw.stop();
        log.info(sw.prettyPrint());
        log.info("==== spu商品图同步_完成,logKey:{}; threadKy:{}; spu数量:{}; skc数量:{}; 耗时:{}s; =========",
                logKey, threadKy, spuSyncNum, skcSyncNum,  sw.getTotalTimeSeconds());

        syncVo.setSpuNum(spuSyncNum);
        syncVo.setSkcNum(skcSyncNum);

        return syncVo;
    }

    private PopProductImageChangeStateDto coverProductImageStateDto(ProductImageChangeStateDto imageDto, Map<String, String> oldSkcMap, Boolean forceSync) {
        PopProductImageChangeStateDto stateDto = new PopProductImageChangeStateDto();
        if (CollUtil.isEmpty(imageDto.getDataList())) {
            return stateDto;
        }
        List<PopProductImageChangeStateDto.Data> dataList = StreamUtil.convertList(imageDto.getDataList(), item -> {
            PopProductImageChangeStateDto.Data data = new PopProductImageChangeStateDto.Data();
            BeanUtils.copyProperties(item, data);
            if (forceSync) {
                List<PopProductImageChangeStateDto.Data.Skc> skcDataList = StreamUtil.convertList(item.getSkcList(), skc -> {
                    PopProductImageChangeStateDto.Data.Skc skcData = new PopProductImageChangeStateDto.Data.Skc();
                    BeanUtils.copyProperties(skc, skcData);
                    return skcData;
                });
                data.setSkcList(skcDataList);
            } else {
                //过滤已同步的skc
                List<ProductImageChangeStateDto.Skc> skcList = item.getSkcList();
                if (CollUtil.isNotEmpty(skcList)) {
                    List<PopProductImageChangeStateDto.Data.Skc> skcDataList = new ArrayList<>();
                    for (ProductImageChangeStateDto.Skc skc : skcList) {
                        if (Objects.isNull(oldSkcMap.get(skc.getSkc()))) {
                            PopProductImageChangeStateDto.Data.Skc skcData = new PopProductImageChangeStateDto.Data.Skc();
                            BeanUtils.copyProperties(skc, skcData);
                            skcDataList.add(skcData);
                        }
                    }
                    data.setSkcList(skcDataList);
                }
            }
            return data;
        });
        stateDto.setDataList(dataList);
        return stateDto;
    }
}
