package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.pop.common.dto.CreateProductDto;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.DigitalPrintConverter;
import tech.tiangong.sdp.design.converter.PopCreateInfoConverter;
import tech.tiangong.sdp.design.entity.DigitalPrintingPrototype;
import tech.tiangong.sdp.design.entity.DigitalPrintingStyle;
import tech.tiangong.sdp.design.entity.DigitalPrintingStyleDetail;
import tech.tiangong.sdp.design.entity.ImageFile;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.enums.visual.VisualTaskImageTypeEnum;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.remote.ColorRemoteHelper;
import tech.tiangong.sdp.design.repository.DigitalPrintingPrototypeRepository;
import tech.tiangong.sdp.design.repository.DigitalPrintingStyleDetailRepository;
import tech.tiangong.sdp.design.repository.DigitalPrintingStyleRepository;
import tech.tiangong.sdp.design.service.DesignLogService;
import tech.tiangong.sdp.design.service.DigitalPrintingStyleService;
import tech.tiangong.sdp.design.service.VisualImagePackageService;
import tech.tiangong.sdp.design.vo.dto.DpAngleDiagram;
import tech.tiangong.sdp.design.vo.dto.DpPictureKitInfo;
import tech.tiangong.sdp.design.vo.dto.ImageDTO;
import tech.tiangong.sdp.design.vo.dto.LabelInfoDTO;
import tech.tiangong.sdp.design.vo.query.digital.DigitalPrintingQuery;
import tech.tiangong.sdp.design.vo.req.digital.*;
import tech.tiangong.sdp.design.vo.req.log.DesignLogSdpSaveReq;
import tech.tiangong.sdp.design.vo.req.visual.SaveOnShelfImagePackageReq;
import tech.tiangong.sdp.design.vo.resp.digital.*;
import tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo;
import tech.tiangong.sdp.design.vo.resp.style.PopCreateProductVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualTaskListExtraVo;
import tech.tiangong.sdp.utils.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 数码印花_服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalPrintingStyleServiceImpl implements DigitalPrintingStyleService {
    private final DigitalPrintingStyleRepository digitalPrintingStyleRepository;
    private final DigitalPrintingStyleDetailRepository digitalPrintingStyleDetailRepository;
    private final DigitalPrintingPrototypeRepository digitalPrintingPrototypeRepository;
    private final DesignLogService designLogService;
    private final VisualImagePackageService visualImagePackageService;
    private final PopProductHelper productHelper;
    private final BusinessCodeGenerator businessCodeGenerator;
    private final PopProductHelper popProductHelper;
    private final VisualTaskHelper visualTaskHelper;
    private final ColorRemoteHelper colorRemoteHelper;


    @Override
    public PageRespVo<DigitalPrintingQueryVo> page(DigitalPrintingQuery query) {
        IPage<DigitalPrintingQueryVo> page = digitalPrintingStyleRepository.findPage(query);
        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.empty();
        }
        List<Long> styleIdList = StreamUtil.convertListAndDistinct(page.getRecords(), DigitalPrintingQueryVo::getPrintingStyleId);

        //详情信息
        List<DigitalPrintingStyleDetail> detailList = digitalPrintingStyleDetailRepository.listByStyleIds(styleIdList);
        Map<Long, DigitalPrintingStyleDetail> detailMap = StreamUtil.list2Map(detailList, DigitalPrintingStyleDetail::getPrintingStyleId);

        page.getRecords().forEach(item -> {
            DigitalPrintingStyleDetail styleDetail = detailMap.get(item.getPrintingStyleId());
            if (Objects.nonNull(styleDetail)) {
                List<ImageDTO> styleImageList = styleDetail.getStyleImageList();
                if (CollectionUtil.isNotEmpty(styleImageList)){
                    List<String> styleImages = styleImageList.stream().map(ImageDTO::getOssImageUrl).collect(Collectors.toList());
                    item.setStyleImageList(styleImages);
                }
            }
        });

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }
    
    @Override
    public PageRespVo<DigitalPrintingVo> findInnerOfpPage(DigitalPrintingInnerReq query) {
        IPage<DigitalPrintingVo> page = digitalPrintingStyleRepository.findInnerOfpPage(query);
        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.empty();
        }
        List<Long> styleIdList = StreamUtil.convertListAndDistinct(page.getRecords(), DigitalPrintingVo::getPrintingStyleId);

        //详情信息
        List<DigitalPrintingStyleDetail> detailList = digitalPrintingStyleDetailRepository.listByStyleIds(styleIdList);
        Map<Long, DigitalPrintingStyleDetail> detailMap = StreamUtil.list2Map(detailList, DigitalPrintingStyleDetail::getPrintingStyleId);

        page.getRecords().forEach(item -> {
            DigitalPrintingStyleDetail styleDetail = detailMap.get(item.getPrintingStyleId());
            if (Objects.nonNull(styleDetail)) {
                List<ImageDTO> styleImageList = styleDetail.getStyleImageList();
                if (CollectionUtil.isNotEmpty(styleImageList)){
                    List<String> styleImages = styleImageList.stream().map(ImageDTO::getOssImageUrl).collect(Collectors.toList());
                    item.setStyleImageList(styleImages);
                }
                List<ImageDTO> productMainImageList = styleDetail.getImage301Urls();
                if (CollectionUtil.isNotEmpty(productMainImageList)){
                    List<String> productMainImages = productMainImageList.stream().map(ImageDTO::getOssImageUrl).collect(Collectors.toList());
                    item.setProductImageList(productMainImages);
                }
            }
        });

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    public DigitalPrintingDetailVo getDetailById(Long printingPrototypeId) {
        DigitalPrintingPrototype printingPrototype = digitalPrintingPrototypeRepository.getById(printingPrototypeId);
        SdpDesignException.notNull(printingPrototype, "skc信息不存在");

        DigitalPrintingStyle printingStyle = digitalPrintingStyleRepository.getByStyleCode(printingPrototype.getStyleCode());
        SdpDesignException.notNull(printingStyle, "spu信息不存在");

        DigitalPrintingStyleDetail styleDetail = digitalPrintingStyleDetailRepository.getByStyleId(printingStyle.getPrintingStyleId());
        SdpDesignException.notNull(styleDetail, "spu详情信息不存在");

        DigitalPrintingDetailVo vo = new DigitalPrintingDetailVo();
        BeanUtils.copyProperties(printingStyle, vo);
        vo.setPrintingPrototypeId(printingPrototype.getPrintingPrototypeId());
        vo.setDesignCode(printingPrototype.getDesignCode());
        vo.setPushStatus(printingPrototype.getPushStatus());
        vo.setColorName(printingPrototype.getColorName());
        vo.setColorCode(printingPrototype.getColorCode());
        vo.setColorAbbrCode(printingPrototype.getColorAbbrCode());
        vo.setAngleDiagramList(printingPrototype.getAngleDiagramList());
        vo.setOtherDetailUrl(printingPrototype.getOtherDetailUrl());

        List<ImageDTO> styleImageList = styleDetail.getStyleImageList();
        if (CollectionUtil.isNotEmpty(styleImageList)) {
            List<String> styleImages = styleImageList.stream().map(ImageDTO::getOssImageUrl).collect(Collectors.toList());
            vo.setStyleImageList(styleImages);
        }
        List<ImageDTO> productImageList = styleDetail.getProductImageList();
        if (CollectionUtil.isNotEmpty(productImageList)) {
            List<String> productImages = productImageList.stream().map(ImageDTO::getOssImageUrl).collect(Collectors.toList());
            vo.setProductImageList(productImages);
        }
        if (StrUtil.isNotBlank(styleDetail.getFabricInfo())) {
            vo.setFabricInfo(JSON.parseObject(styleDetail.getFabricInfo(), DigitalPrintingDetailVo.FabricInfo.class));
        }
        List<ImageDTO> bomFileList = styleDetail.getBomFileList();
        if (CollectionUtil.isNotEmpty(bomFileList)) {
            List<String> bomFiles = bomFileList.stream().map(ImageDTO::getOssImageUrl).collect(Collectors.toList());
            vo.setBomFileList(bomFiles);
        }
        List<ImageDTO> patternFileList = styleDetail.getPatternFileList();
        if (CollectionUtil.isNotEmpty(patternFileList)) {
            List<String> patternFiles = patternFileList.stream().map(ImageDTO::getOssImageUrl).collect(Collectors.toList());
            vo.setPatternFileList(patternFiles);
        }
        List<ImageDTO> productFileList = styleDetail.getProductFileList();
        if (CollectionUtil.isNotEmpty(productFileList)) {
            List<String> productFiles = StreamUtil.convertList(productFileList, ImageDTO::getOssImageUrl);
            vo.setProductFileList(productFiles);
        }

        return vo;
    }

    @Override
    public DigitalPrintingRePushVo rePush() {
        //查询推送失败的款
        List<DigitalPrintingPrototype> failSkcList = digitalPrintingPrototypeRepository.listFail();
        Map<String, List<DigitalPrintingPrototype>> failSkcMap = StreamUtil.groupingBy(failSkcList, DigitalPrintingPrototype::getStyleCode);

        List<String> styleCodeList = StreamUtil.convertListAndDistinct(failSkcList, DigitalPrintingPrototype::getStyleCode);
        List<DigitalPrintingStyle> failSpuList = digitalPrintingStyleRepository.listByStyleCodes(styleCodeList);

        List<Long> styleIdList = StreamUtil.convertListAndDistinct(failSpuList, DigitalPrintingStyle::getPrintingStyleId);
        List<DigitalPrintingStyleDetail> styleDetailList = digitalPrintingStyleDetailRepository.listByStyleIds(styleIdList);
        Map<Long, DigitalPrintingStyleDetail> styleDetailMap = StreamUtil.list2Map(styleDetailList, DigitalPrintingStyleDetail::getPrintingStyleId);

        List<DigitalPrintingRePushVo.PushSpuInfo> spuInfoList = new ArrayList<>();
        //失败款重推后更新推送状态为推送中(用户再次触发重推时就会过滤掉这些推送中的款)
        List<DigitalPrintingStyle> spuUpdateList = new ArrayList<>();
        failSpuList.forEach(spu -> {
            DigitalPrintingRePushVo.PushSpuInfo pushSpuInfo = new DigitalPrintingRePushVo.PushSpuInfo();
            pushSpuInfo.setStyleCode(spu.getStyleCode());
            //推送商品运营平台
            List<DigitalPrintingPrototype> skcInfoList = failSkcMap.get(spu.getStyleCode());
            DigitalPrintingStyleDetail styleDetail = styleDetailMap.get(spu.getPrintingStyleId());
            if (ObjectUtil.isNotNull(styleDetail)){
                CreateProductDto productReq = DigitalPrintConverter.convertProductCreateReq(spu, styleDetail, skcInfoList);
                popProductHelper.noticeCreateSpotProduct(productReq);

                //设置推送状态为推送中
                List<Long> prototypeIdList = StreamUtil.convertListAndDistinct(skcInfoList, DigitalPrintingPrototype::getPrintingPrototypeId);
                digitalPrintingPrototypeRepository.rePushUpdate(prototypeIdList);
                digitalPrintingStyleRepository.rePushUpdate(spu.getStyleCode());

                //日志
                skcInfoList.forEach(item -> {
                    this.addLog(item.getPrintingPrototypeId(), DesignLogContentEnum.DIGITAL_PRINTING_RE_PUSH.getDesc());
                });

                spuInfoList.add(pushSpuInfo);
            }
        });

        return DigitalPrintingRePushVo.builder()
                .pushNum(spuInfoList.size())
                .pushSpuInfoList(spuInfoList)
                .build();
    }

    /*
    private Map<String, AiCategoryMappingVO> getCategoryMappingVOMap(List<String> aiCategoryCodeList) {
        List<AiCategoryMappingVO> aiCategoryList = aiCategoryMappingRemoteHelper.findByAiCategoryCode(aiCategoryCodeList);
        return StreamUtil.list2Map(aiCategoryList, AiCategoryMappingVO::getAiCategoryCode);
    }

    private void updateCategory(Long printingStyleId, String categoryName, String categoryCode) {
        if (StrUtil.isBlank(categoryCode) || StrUtil.isBlank(categoryName)) {
            return;
        }
        DigitalPrintingStyle styleUpdate = new DigitalPrintingStyle();
        styleUpdate.setPrintingStyleId(printingStyleId);
        styleUpdate.setCategoryName(categoryName);
        styleUpdate.setCategory(categoryCode);
        digitalPrintingStyleRepository.updateById(styleUpdate);
    }
     */


    /*
    @Deprecated(since = "数码印花款一期")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DigitalPrintingStyleAddVo add(DigitalPrintingStyleAddReq req) {
        log.info("=== 创建数码印花款-req:{}===", JSON.toJSONString(req));
        String styleCode = req.getStyleCode();
        DigitalPrintingStyle existStyle = digitalPrintingStyleRepository.getByStyleCode(styleCode);
        SdpDesignException.isNull(existStyle, "款式已存在!spu:{}", styleCode);

        //创建数码印花款spu
        long printingStyleId = IdPool.getId();
        DigitalPrintingStyle printingStyle = new DigitalPrintingStyle();
        BeanUtils.copyProperties(req, printingStyle);
        printingStyle.setPrintingStyleId(printingStyleId);
        List<String> sizeStandardValue = req.getSizeStandardValue();
        if (CollectionUtil.isNotEmpty(sizeStandardValue)) {
            printingStyle.setSizeStandardValue(JSONUtil.toJsonStr(sizeStandardValue));
        }
        //AI品类映射内部品类
        Map<String, AiCategoryMappingVO> aiCategoryMappingVOMap = this.getCategoryMappingVOMap(Collections.singletonList(req.getAiCategory()));
        AiCategoryMappingVO aiCategoryMappingVO = aiCategoryMappingVOMap.get(req.getAiCategory());
        if (Objects.nonNull(aiCategoryMappingVO)) {
            printingStyle.setCategory(aiCategoryMappingVO.getCategoryCode());
            printingStyle.setCategoryName(aiCategoryMappingVO.getCategoryName());
        }

        //spu详情
        DigitalPrintingStyleDetail styleDetail = new DigitalPrintingStyleDetail();
        BeanUtils.copyProperties(req, styleDetail);
        styleDetail.setPrintingStyleId(printingStyleId);
        styleDetail.setPrintingStyleDetailId(IdPool.getId());
        styleDetail.setProductImageList(req.getImageFlowerUrls());
        List<DigitalPrintingStyleAddReq.LabelInfo> subjectLabels = req.getSubjectLabels();
        if (CollectionUtil.isNotEmpty(subjectLabels)){
            List<LabelInfoDTO> subjectLabel = subjectLabels.stream().map(subject -> {
                LabelInfoDTO labelInfoDTO = new LabelInfoDTO();
                labelInfoDTO.setEn(subject.getEn());
                labelInfoDTO.setCn(subject.getCn());
                return labelInfoDTO;
            }).collect(Collectors.toList());
            styleDetail.setSubjectLabel(subjectLabel);
        }
        // 面料信息
        DigitalPrintingStyleAddReq.FabricInfo fabricInfo = req.getFabricInfo();
        if (ObjectUtil.isNotNull(fabricInfo)) {
            styleDetail.setFabricInfo(JSONUtil.toJsonStr(fabricInfo));
        }
        //skc
        //推送状态
        Integer pushStatus = null;
        String errorMsg = null;
        if (StrUtil.isBlank(printingStyle.getCategory())) {
            pushStatus = Bool.NO.getCode();
            errorMsg = "品类不存在";
        }
        List<DigitalPrintingPrototype> skcInfoList = new ArrayList<>();
        int skcNum = 0;
        for (DigitalPrintingStyleAddReq.SkcInfo skcInfo : req.getSkcInfoList()) {
            DigitalPrintingPrototype skc = new DigitalPrintingPrototype();
            BeanUtils.copyProperties(skcInfo, skc);
            skc.setStyleCode(req.getStyleCode());
            skc.setPrintingPrototypeId(IdPool.getId());
            //设置skc编码
            String skcNumFormat = String.format("%1$02d", skcNum += 1);
            skc.setDesignCode(req.getStyleCode() + skcNumFormat);
            skc.setPushStatus(pushStatus);
            skc.setErrorMsg(errorMsg);
            skcInfoList.add(skc);
        }

        digitalPrintingStyleRepository.save(printingStyle);
        digitalPrintingStyleDetailRepository.save(styleDetail);
        digitalPrintingPrototypeRepository.saveBatch(skcInfoList);

        //推送pop, 异步处理
        this.pushProduct2PopByMq(Collections.singletonList(styleCode));

        //创建日志
        skcInfoList.forEach(item -> {
            this.addLog(item.getPrintingPrototypeId(), DesignLogContentEnum.DIGITAL_PRINTING_CREATE.getDesc());
        });

        List<DigitalPrintingStyleAddVo.DigitalSkcInfo> digitalSkcInfoList = new ArrayList<>();
        skcInfoList.forEach(item -> {
            DigitalPrintingStyleAddVo.DigitalSkcInfo digitalSkcInfo = new DigitalPrintingStyleAddVo.DigitalSkcInfo();
            BeanUtils.copyProperties(item, digitalSkcInfo);
            digitalSkcInfoList.add(digitalSkcInfo);
        });

        log.info("=== 创建数码印花款-成功, spu:{}===", req.getStyleCode());
        return DigitalPrintingStyleAddVo.builder()
                .printingStyleId(printingStyleId)
                .styleCode(req.getStyleCode())
                .skcInfoList(digitalSkcInfoList)
                .build();
    }

     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DpStyleAddVo> batchAdd(DpStyleBatchAddReq req) {
        log.info("=== 批量创建数码印花款 req:{}===", JSON.toJSONString(req));
        List<DpStyleAddReq> styleAddList = req.getStyleAddList();

        //实际业务场景调用是单个的, 不catch了有异常直接报错给到调用方
        List<DpStyleAddVo> addVoList = new ArrayList<>(styleAddList.size());
        for (DpStyleAddReq addReq : styleAddList) {
            addVoList.add(this.saveDpStyle(addReq));
        }
        /*
        for (DpStyleAddReq addReq : styleAddList) {
            try {
                DpStyleAddVo addVo = this.saveDpStyle(addReq);
                addVoList.add(addVo);
            } catch (Exception e) {
                log.error("数码印花款创建失败, sourceBizId:{}", addReq.getSourceBizId(), e);
                DpStyleAddVo failVo = new DpStyleAddVo().setSourceBizId(addReq.getSourceBizId())
                        .setSuccessful(false)
                        .setMessage(e.getMessage());
                addVoList.add(failVo);
            }
        }
         */
        log.info("=== 批量创建数码印花款 resp:{}===", JSON.toJSONString(addVoList));
        return addVoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DpStyleAddVo save(DpStyleAddReq req) {
        return this.saveDpStyle(req);
    }

    public DpStyleAddVo saveDpStyle(DpStyleAddReq req) {
        log.info("=== 创建数码印花款 req:{}===", JSON.toJSONString(req));
        //查询是否已存在
        SdpDesignException.notNull(req.getSourceBizId(), "sourceBizId不能为空");
        //本土价与跨境价2选1, 不能2个都为空
        SdpDesignException.isTrue(StrUtil.isNotBlank(req.getLocalPrice())
                || StrUtil.isNotBlank(req.getCrossBorderPrice()), "本地价或跨境价不能同时为空");

        DigitalPrintingStyle existStyle = digitalPrintingStyleRepository.getBySourceBizId(req.getSourceBizId());
        if (Objects.nonNull(existStyle)) {
            List<DigitalPrintingPrototype> dpSkcList = digitalPrintingPrototypeRepository.listBySpu(existStyle.getStyleCode());
            return this.buildAddVo(req,existStyle, dpSkcList);
        }

        //生成数码印花spu: 生成规则：版型号-花型编号-1个自增长流水; P01-aF0980-1
        String filed = req.getModelNumber() + StrUtil.DASHED + req.getFlowerNum() + StrUtil.DASHED;
        String styleCode = businessCodeGenerator.generateSerialNum(CodeRuleEnum.PRINT_STYLE_SERIAL_NUM, filed, () -> digitalPrintingStyleRepository.selectLatestStyleCode(filed));

        //校验spu数量
        this.checkStyleCount(req, styleCode);

        //spu
        long printingStyleId = IdPool.getId();
        DigitalPrintingStyle printingStyle = this.buildPrintingStyle(req, printingStyleId, styleCode);
        //spu详情
        DigitalPrintingStyleDetail styleDetail = this.buildPrintingStyleDetail(req, printingStyleId);
        //skc
        List<DigitalPrintingPrototype> skcInfoList = this.buildPrintingPrototypes(req, printingStyle);

        digitalPrintingStyleRepository.save(printingStyle);
        digitalPrintingStyleDetailRepository.save(styleDetail);
        digitalPrintingPrototypeRepository.saveBatch(skcInfoList);

        //新增图包
        this.save4DigitalPrinting(printingStyle, styleDetail, skcInfoList, styleCode);

        //商品创建推送pop
        CreateProductDto productReq = DigitalPrintConverter.convertProductCreateReq(printingStyle, styleDetail, skcInfoList);
        popProductHelper.noticeCreateSpotProduct(productReq);

        //创建日志
        skcInfoList.forEach(item -> {
            this.addLog(item.getPrintingPrototypeId(), DesignLogContentEnum.DIGITAL_PRINTING_CREATE.getDesc());
        });
        log.info("=== 创建数码印花款 成功, spu:{}; sourceBizId:{} ===", styleCode, req.getSourceBizId());

        return this.buildAddVo(req,printingStyle, skcInfoList);
    }

    private void save4DigitalPrinting(DigitalPrintingStyle printingStyle, DigitalPrintingStyleDetail styleDetail, List<DigitalPrintingPrototype> skcInfoList, String styleCode) {
        SaveOnShelfImagePackageReq packageReq = new SaveOnShelfImagePackageReq();
        packageReq.setStyleCode(printingStyle.getStyleCode());
        List<ImageDTO> allSkuRaw = new ArrayList<>();
        List<ImageDTO> firstSkuRaw = new ArrayList<>();

        //skc图片
        List<SaveOnShelfImagePackageReq.SkcImage> skcImageList = new ArrayList<>();
        this.setVisualSkcImage(styleDetail, skcInfoList, skcImageList, allSkuRaw, firstSkuRaw);
        packageReq.setSkcImages(skcImageList);

        //SPU图片
        this.buildVisualSpuImageInfo(printingStyle, styleDetail, skcInfoList, allSkuRaw, firstSkuRaw, packageReq);

        //新增视觉图包
        visualImagePackageService.save4DigitalPrinting(packageReq);
    }

    private void buildVisualSpuImageInfo(DigitalPrintingStyle printingStyle, DigitalPrintingStyleDetail styleDetail, List<DigitalPrintingPrototype> skcInfoList, List<ImageDTO> allSkuRaw, List<ImageDTO> firstSkuRaw, SaveOnShelfImagePackageReq packageReq) {
        //图片处理规则from pop: tech.tiangong.pop.service.product.impl.ProductCreateServiceImpl#validImageAndSave
        String styleCode = printingStyle.getStyleCode();
        List<SaveOnShelfImagePackageReq.SpuImage> spuImageList = new ArrayList<>();

        //101
        this.setVisualSpuImage(styleCode, styleDetail.getImage101Urls(), VisualTaskImageTypeEnum.SIZE_CHART, spuImageList);
        //201 → 仅两张--》0904 改成4张
        List<ImageDTO> image201Urls = styleDetail.getImage201Urls();
        if (CollectionUtil.isNotEmpty(styleDetail.getImage201Urls())) {
            if (image201Urls.size() > 4) {
                image201Urls = image201Urls.subList(0, 4);
            }
            this.setVisualSpuImage(styleCode, image201Urls, VisualTaskImageTypeEnum.CATEGORY_CHART, spuImageList);
        }

        //301 / 401/ 601
        this.setVisualSpuImage(styleCode, styleDetail.getImage301Urls(), VisualTaskImageTypeEnum.PRODUCT_MAIN, spuImageList);
        this.setVisualSpuImage(styleCode, styleDetail.getImage401Urls(), VisualTaskImageTypeEnum.SIZING_TABLE, spuImageList);
        this.setVisualSpuImage(styleCode, styleDetail.getImage601Urls(), VisualTaskImageTypeEnum.FABRIC, spuImageList);

        //0904 产品要求的不要详情图
        // //SPU-5XX: 1-首张skc图; 2-CAD渠道的图套;
        // List<ImageDTO> productDetailImageList = this.mergeSpuProductDetailImage(printingStyle.getStyleSource(), styleDetail.getPrintedImages(), firstSkuRaw);
        // this.setVisualSpuImage(styleCode, productDetailImageList, VisualTaskImageTypeEnum.PRODUCT_DETAIL, spuImageList);

        //SKC-5XX:1-skc的角度图; 2-细节图;
        List<ImageDTO> angleDetailList = this.buildAngleDetailImage(skcInfoList);
        this.setSkcDetailImage(angleDetailList, spuImageList);

        //OTHER图片: 1-LOGO_PRINT(styleSource=20)的PictureKit图片; 2-花型图;
        List<ImageDTO> otherImageList = this.mergeOtherImage(printingStyle, styleDetail);
        this.setVisualSpuImage(styleCode, otherImageList, VisualTaskImageTypeEnum.OTHER, spuImageList);

        packageReq.setSpuImages(spuImageList);
    }

    private List<ImageDTO> mergeOtherImage(DigitalPrintingStyle printingStyle, DigitalPrintingStyleDetail styleDetail) {
        List<ImageDTO> otherImageList = new ArrayList<>();

        // LOGO_PRINT(styleSource=20) 的 PictureKit 图片单独处理
        if (Objects.equals(DpSourceTypeEnum.AUTO.getCode(), printingStyle.getStyleSource())) {
            List<ImageDTO> kitInfoList = this.convertPictureKitToImages(styleDetail.getPrintedImages());
            otherImageList.addAll(kitInfoList);
        }

        //花型图
        if (CollUtil.isNotEmpty(styleDetail.getImageFlowerUrls())) {
            otherImageList.addAll(styleDetail.getImageFlowerUrls());
        }
        return otherImageList;
    }

    /**
     * 处理 PRODUCT_DETAIL 类型图片: 首张skc图, CAD渠道的图套
     */
    private List<ImageDTO> mergeSpuProductDetailImage(Integer styleSource, List<DpPictureKitInfo> printedImages, List<ImageDTO> firstSkuRaw) {
        List<ImageDTO> productDetailImages = new ArrayList<>();

        // 添加 firstSkuRaw
        if (CollUtil.isNotEmpty(firstSkuRaw)) {
            productDetailImages.addAll(firstSkuRaw);
        }

        // FULL_PRINT 情况(CAD渠道)：合并 PictureKit 图片
        if (Objects.equals(DpSourceTypeEnum.CAD.getCode(), styleSource)) {
            List<ImageDTO> pictureKitImages = printedImages.stream()
                    .filter(it -> StringUtils.isNotBlank(it.getImageUrl()))
                    .map(it -> new ImageDTO(null, it.getImageUrl()))
                    .toList();
            productDetailImages.addAll(pictureKitImages);
        }

        return productDetailImages;
    }

    /**
     * skc的角度图,细节图处理为详情图(skc编号前缀的5xx系列)
     */
    private void setSkcDetailImage(List<ImageDTO> angleDetailList, List<SaveOnShelfImagePackageReq.SpuImage> spuImageList) {
        if (CollUtil.isEmpty(angleDetailList)) {
            return;
        }
        VisualTaskImageTypeEnum detailImageType = VisualTaskImageTypeEnum.PRODUCT_DETAIL;
        SaveOnShelfImagePackageReq.SpuImage detailImage = spuImageList.stream()
                .filter(item -> Objects.equals(item.getImageType(), detailImageType.getCode()))
                .findFirst().orElse(null);

        List<ImageFile> angleDetailImageList = StreamUtil.convertList(angleDetailList, item -> {
            ImageFile imageFile = new ImageFile();
            imageFile.setOrgImgName(item.getOrgImgName());
            imageFile.setOssImageUrl(item.getOssImageUrl());
            return imageFile;
        });
        if (Objects.nonNull(detailImage)) {
            //细节图存在则添加
            List<ImageFile> exitsImages = detailImage.getImages();
            if (CollUtil.isEmpty(exitsImages)) {
                detailImage.setImages(angleDetailImageList);
            }else {
                exitsImages.addAll(angleDetailImageList);
            }
        }else {
            //spu下还没有细节图类型, 则创建
            SaveOnShelfImagePackageReq.SpuImage spuImage = new SaveOnShelfImagePackageReq.SpuImage();
            spuImage.setImageType(detailImageType.getCode());
            spuImage.setImageTypeDesc(detailImageType.getDesc());
            spuImage.setImages(angleDetailImageList);
            spuImageList.add(spuImage);
        }
    }

    private List<ImageDTO> buildAngleDetailImage(List<DigitalPrintingPrototype> skcInfoList) {
        List<ImageDTO> angleDetailList = new ArrayList<>();
        skcInfoList.forEach(item->{
            // 使用LinkedHashMap保持插入顺序
            Map<Integer, ImageDTO> orderedImages = new LinkedHashMap<>();

            // 1.强制按顺序处理正面/背面图
            Optional.ofNullable(item.getAngleDiagramList())
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(diagrams -> {
                        Map<String, DpAngleDiagram> angleMap = diagrams.stream()
                                .collect(Collectors.toMap(
                                        DpAngleDiagram::getAngleCode,
                                        Function.identity(),
                                        (oldVal, newVal) -> oldVal));

                        // 强制按指定顺序处理 角度编码 来自OPS 10 正面 20背面
                        List<String> requiredOrder = Arrays.asList("10", "20");
                        requiredOrder.forEach(angle -> {
                            DpAngleDiagram diagram = angleMap.get(angle);
                            if (diagram != null) {
                                orderedImages.put(orderedImages.size(),
                                        new ImageDTO(diagram.getAngleCode(), diagram.getWhiteboardDiagramUrl()));
                            }
                        });
                    });

            // 2.追加其他详情图（不参与顺序控制）
            Optional.ofNullable(item.getOtherDetailUrl())
                    .map(urls -> urls.split(","))
                    .ifPresent(urls -> Arrays.stream(urls)
                            .filter(StringUtils::isNotBlank)
                            .forEach(url -> {
                                ImageDTO img = new ImageDTO();
                                img.setOrgImgName(url.trim());
                                img.setOssImageUrl(url.trim());
                                orderedImages.put(orderedImages.size(), img);
                            }));

            // 3.转换最终列表（保持插入顺序） 修改文件名: skc-5xx.xxx
            List<ImageDTO> skcDetailImageList = new ArrayList<>(orderedImages.values());
            for (int i = 0; i < skcDetailImageList.size(); i++) {
                ImageDTO raw = skcDetailImageList.get(i);
                if (StrUtil.isBlank(raw.getOssImageUrl()) || raw.getOssImageUrl().isEmpty()) continue;
                String ext = FileNameUtil.extName(raw.getOssImageUrl());
                String orgImgName = item.getDesignCode() + StrUtil.DASHED + VisualTaskImageTypeEnum.PRODUCT_DETAIL.serial(i + 1) + StrUtil.DOT + ext;
                angleDetailList.add(new ImageDTO(orgImgName, raw.getOssImageUrl()));
            }
        });
        return angleDetailList;
    }

    private List<ImageDTO> convertPictureKitToImages(List<DpPictureKitInfo> kitInfoList) {
        return Optional.ofNullable(kitInfoList)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> {
                    // 获取第一个颜色代码
                    String firstColorCode = list.get(0).getColorAbbrCode();

                    // 过滤出第一个颜色的所有图片
                    List<DpPictureKitInfo> firstColorList = list.stream()
                            .filter(k -> firstColorCode.equals(k.getColorAbbrCode()))
                            .collect(Collectors.toList());

                    return firstColorList;
                })
                .map(PictureKitUtils::processPictureKitInfoList)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .map(k -> {
                            ImageDTO imageDTO = new ImageDTO();
                            imageDTO.setOssImageUrl(k.getImageUrl());
                            imageDTO.setOrgImgName(k.getImageFileName());
                            return imageDTO;
                        })
                        .collect(Collectors.toList())
                )
                .orElse(Collections.emptyList());
    }

    private void setVisualSkcImage(DigitalPrintingStyleDetail styleDetail, List<DigitalPrintingPrototype> skcInfoList,
                                   List<SaveOnShelfImagePackageReq.SkcImage> skcImageList, List<ImageDTO> allSkuRaw, List<ImageDTO> firstSkuRaw) {
        List<ImageDTO> styleImageList = styleDetail.getStyleImageList();
        skcInfoList.forEach(skc -> {
            String colorCode = skc.getColorCode();
            List<String> pictures = styleImageList.stream().filter(imageDTO -> {
                String orgImgName = imageDTO.getOrgImgName();
                return orgImgName.contains(colorCode);
            }).map(ImageDTO::getOssImageUrl).collect(Collectors.toList());

            List<ImageDTO> imageDTOS = this.buildImagesForColorCode(colorCode, pictures);
            if (CollectionUtil.isNotEmpty(imageDTOS)) {
                allSkuRaw.addAll(imageDTOS);
                firstSkuRaw.add(imageDTOS.getFirst());

                SaveOnShelfImagePackageReq.SkcImage skcImage = new SaveOnShelfImagePackageReq.SkcImage();
                skcImage.setDesignCode(skc.getDesignCode());
                skcImage.setColor(skc.getColorName());
                skcImage.setColorEn(skc.getColorCode());
                List<ImageFile> images = imageDTOS.stream().map(imageDTO -> {
                    ImageFile imageFile = new ImageFile();
                    imageFile.setOrgImgName(imageDTO.getOrgImgName());
                    imageFile.setOssImageUrl(imageDTO.getOssImageUrl());
                    return imageFile;
                }).toList();
                skcImage.setImages(images);

                skcImageList.add(skcImage);
            }
        });
    }

    /**
     * 根据颜色代码和图片URL生成ImageAniVo对象列表 (复制from pop)
     * 第一张图片使用颜色代码作为名称，后续图片使用颜色代码-索引格式
     */
    private List<ImageDTO> buildImagesForColorCode(String colorCode, List<String> imageUrls) {
        if (CollectionUtils.isEmpty(imageUrls)) {
            return Collections.emptyList();
        }

        return IntStream.range(0, imageUrls.size())
                .mapToObj(i -> {
                    String imageUrl = imageUrls.get(i);
                    if (StringUtils.isBlank(imageUrl)) {
                        return null;
                    }
                    int dotIndex = imageUrl.lastIndexOf(".");
                    // 跳过没有后缀的 url
                    if (dotIndex < 0 || dotIndex == imageUrl.length() - 1) {
                        return null;
                    }
                    String suffix = imageUrl.substring(dotIndex);
                    String imgName = (i == 0) ? colorCode + suffix : colorCode + StrUtil.DASHED + i + suffix;
                    ImageDTO imageDTO = new ImageDTO();
                    imageDTO.setOrgImgName(imgName);
                    imageDTO.setOssImageUrl(imageUrl);
                    return imageDTO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    private void setVisualSpuImage(String styleCode, List<ImageDTO> imageDTOList, VisualTaskImageTypeEnum imageTypeEnum, List<SaveOnShelfImagePackageReq.SpuImage> spuImageList) {
        if (CollUtil.isEmpty(imageDTOList)) {
            return;
        }
        SaveOnShelfImagePackageReq.SpuImage spuImage = new SaveOnShelfImagePackageReq.SpuImage();
        spuImage.setImageType(imageTypeEnum.getCode());
        spuImage.setImageTypeDesc(imageTypeEnum.getDesc());
        List<ImageFile> imageFileList = new ArrayList<>();
        for (int i = 0; i < imageDTOList.size(); i++) {
            ImageDTO imageDTO = imageDTOList.get(i);
            if (StrUtil.isBlank(imageDTO.getOssImageUrl())) {
                continue;
            }
            String extName = FileNameUtil.extName(imageDTO.getOssImageUrl());
            ImageFile imageFile = new ImageFile();
            imageFile.setOssImageUrl(imageDTO.getOssImageUrl());
            String orgImgName = imageDTO.getOrgImgName();
            if (!Objects.equals(imageTypeEnum, VisualTaskImageTypeEnum.OTHER)) {
                //重命名文件名
                String serialNum = imageTypeEnum.serial(i + 1);
                orgImgName = styleCode + StrUtil.DASHED + serialNum + StrUtil.DOT + extName;
            }
            imageFile.setOrgImgName(orgImgName);

            imageFileList.add(imageFile);
        }
        spuImage.setImages(imageFileList);

        spuImageList.add(spuImage);
    }

    private void checkStyleCount(DpStyleAddReq req, String styleCode) {
        String[] split = styleCode.split(StrUtil.DASHED);
        String serialNumStr = split[split.length - 1];
        try {
            int serialNum = Integer.parseInt(serialNumStr);
            SdpDesignException.isTrue(serialNum < 10,
                    "{}-{}对应的spu数量不能大于10个", req.getModelNumber(), req.getFlowerNum());
        } catch (NumberFormatException e) {
            throw new SdpDesignException("spu编码需要生成异常", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductFile(DpProductFileUpdateReq req) {
        log.info("=== 数码印花款更新生产资料 req:{}===", JSON.toJSONString(req));
        List<DpProductFileUpdateReq.ProductFileInfo> updateReqList = req.getProductFileUpdateList();
        if (CollUtil.isEmpty(updateReqList)) {
            return;
        }

        //spu
        List<String> spuList = StreamUtil.convertList(updateReqList, DpProductFileUpdateReq.ProductFileInfo::getStyleCode);
        List<DigitalPrintingStyle> printingStyleList = digitalPrintingStyleRepository.listByStyleCodes(spuList);
        if (CollUtil.isEmpty(printingStyleList)) {
            return;
        }
        Map<String, DigitalPrintingStyle> styleMap = StreamUtil.list2Map(printingStyleList, DigitalPrintingStyle::getStyleCode);

        //spu详情
        List<Long> styleIdList = StreamUtil.convertList(printingStyleList, DigitalPrintingStyle::getPrintingStyleId);
        List<DigitalPrintingStyleDetail> styleDetailList = digitalPrintingStyleDetailRepository.listByStyleIds(styleIdList);
        if (CollUtil.isEmpty(styleDetailList)) {
            return;
        }
        Map<Long, DigitalPrintingStyleDetail> styleDetailMap = StreamUtil.list2Map(styleDetailList, DigitalPrintingStyleDetail::getPrintingStyleId);

        //更新生产资料
        List<DigitalPrintingStyleDetail> detailUpdateList = new ArrayList<>(req.getProductFileUpdateList().size());
        List<DpProductFileUpdateReq.ProductFileInfo> popUpdateReqList = new ArrayList<>(req.getProductFileUpdateList().size());
        updateReqList.forEach(item -> {
            String styleCode = item.getStyleCode();
            DigitalPrintingStyle printingStyle = styleMap.get(styleCode);
            if (Objects.isNull(printingStyle)){
                return;
            }
            DigitalPrintingStyleDetail styleDetail = styleDetailMap.get(printingStyle.getPrintingStyleId());
            if (Objects.isNull(styleDetail)) {
                return;
            }
            DigitalPrintingStyleDetail updateDetail = new DigitalPrintingStyleDetail();
            updateDetail.setPrintingStyleDetailId(styleDetail.getPrintingStyleDetailId());
            updateDetail.setProductFileList(item.getProductFileList());
            detailUpdateList.add(updateDetail);

            popUpdateReqList.add(item);
        });
        log.info("=== 数码印花款更新生产资料 落库:{}===", JSON.toJSONString(detailUpdateList));
        digitalPrintingStyleDetailRepository.updateBatchById(detailUpdateList);

        //生产资料更新同步pop
        productHelper.updateProductMaterial(popUpdateReqList);
    }

    @Override
    public void updatePushState(DpPushStateUpdateReq req) {
        log.info("=== 推送pop状态更新 req:{} ===", JSON.toJSONString(req));

        List<DpPushStateUpdateReq.PushStateInfo> pushStateInfoList = req.getPushStateInfoList();
        SdpDesignException.notEmpty(pushStateInfoList, "更新信息不能为空!");
        List<String> styleCodeList = StreamUtil.convertListAndDistinct(pushStateInfoList, DpPushStateUpdateReq.PushStateInfo::getStyleCode);

        List<DigitalPrintingStyle> printingStyleList = digitalPrintingStyleRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(printingStyleList)) {
            return;
        }
        Map<String, DigitalPrintingStyle> styleMap = StreamUtil.list2Map(printingStyleList, DigitalPrintingStyle::getStyleCode);

        List<DigitalPrintingPrototype> digitalPrintingPrototypeList = digitalPrintingPrototypeRepository.listBySpuList(styleCodeList);
        if (CollUtil.isEmpty(digitalPrintingPrototypeList)) {
            return;
        }
        Map<String, List<DigitalPrintingPrototype>> skcGroupMap = StreamUtil.groupingBy(digitalPrintingPrototypeList, DigitalPrintingPrototype::getStyleCode);

        //更新推送状态
        List<DigitalPrintingStyle> spuUpdateList = new ArrayList<>();
        List<DigitalPrintingPrototype> skcUpdateList = new ArrayList<>();
        List<String> rePushSpu = new ArrayList<>();
        pushStateInfoList.forEach(item -> {
            String styleCode = item.getStyleCode();
            //更新spu推送状态
            DigitalPrintingStyle printingStyle = styleMap.get(styleCode);
            if (Objects.isNull(printingStyle)) {
                return;
            }
            if (Objects.equals(Bool.YES.getCode(), printingStyle.getSpuPushStatus())) {
                log.info("spu已推送成功, 无需更新");
                return;
            }
            DigitalPrintingStyle updateStyle = new DigitalPrintingStyle();
            updateStyle.setPrintingStyleId(printingStyle.getPrintingStyleId());
            updateStyle.setSpuPushStatus(item.getPushStatus());
            int pushCount = Objects.isNull(printingStyle.getPushCount()) ? 1 : printingStyle.getPushCount() + 1;
            updateStyle.setPushCount(pushCount);
            spuUpdateList.add(updateStyle);

            //更新skc推送状态
            List<DigitalPrintingPrototype> prototypeList = skcGroupMap.get(styleCode);
            if (CollUtil.isEmpty(prototypeList)) {
                return;
            }
            String msg = item.getErrorMsg();
            String subMsg = StrUtil.isNotBlank(msg)&&msg.length() > 200 ? msg.substring(0, 200) : msg;
            prototypeList.forEach(skc -> {
                if (Objects.equals(Bool.YES.getCode(), skc.getPushStatus())) {
                    log.info("skc已推送成功, 无需更新");
                    return;
                }
                DigitalPrintingPrototype updateSkc = new DigitalPrintingPrototype();
                updateSkc.setPrintingPrototypeId(skc.getPrintingPrototypeId());
                updateSkc.setPushStatus(item.getPushStatus());
                updateSkc.setErrorMsg(subMsg);
                skcUpdateList.add(updateSkc);
            });
            //如果是失败的, 且推送次数小于3次, 则重推
            if (Objects.equals(Bool.NO.getCode(), item.getPushStatus()) && pushCount <= 3) {
                rePushSpu.add(styleCode);
            }
        });

        if (CollUtil.isNotEmpty(spuUpdateList)) {
            digitalPrintingStyleRepository.updateBatchById(spuUpdateList);
        }
        if (CollUtil.isNotEmpty(skcUpdateList)) {
            digitalPrintingPrototypeRepository.updateBatchById(skcUpdateList);
        }
        if (CollUtil.isNotEmpty(rePushSpu)) {
            DpPushPopReq rePushReq = new DpPushPopReq();
            rePushReq.setStyleCodeList(styleCodeList);
            rePushReq.setPushState(Bool.NO.getCode());
            this.pushFailBySpu(rePushReq);
        }

    }

    public void syncProduct2Pop(List<String> styleCodeList, Integer pushState) {
        log.info("=== 数码印花款推送pop styleCodeList:{}; pushState:{} ===", JSON.toJSONString(styleCodeList), pushState);
        if (CollUtil.isEmpty(styleCodeList)) {
            return;
        }
        List<DigitalPrintingStyle> printingStyleList = digitalPrintingStyleRepository.listByStyleCodes(styleCodeList);
        SdpDesignException.notEmpty(printingStyleList, "spu信息不存在!");
        List<Long> styleIdList = StreamUtil.convertList(printingStyleList, DigitalPrintingStyle::getPrintingStyleId);

        List<DigitalPrintingStyleDetail> styleDetailList = digitalPrintingStyleDetailRepository.listByStyleIds(styleIdList);
        SdpDesignException.notEmpty(styleDetailList, "spu详情信息不存在!");
        Map<Long, DigitalPrintingStyleDetail> detailMap = StreamUtil.list2Map(styleDetailList, DigitalPrintingStyleDetail::getPrintingStyleId);

        List<DigitalPrintingPrototype> digitalPrintingPrototypeList = digitalPrintingPrototypeRepository.listBySpuList(styleCodeList);
        SdpDesignException.notEmpty(digitalPrintingPrototypeList, "skc信息不存在!");
        Map<String, List<DigitalPrintingPrototype>> skcGroupMap = StreamUtil.groupingBy(digitalPrintingPrototypeList, DigitalPrintingPrototype::getStyleCode);

        printingStyleList.forEach(printingStyle -> {
            this.pushProduct2Pop(pushState, printingStyle, detailMap.get(printingStyle.getPrintingStyleId()),
                    skcGroupMap.get(printingStyle.getStyleCode()));
        });


    }

    @Override
    public void pushFailBySpu(DpPushPopReq req) {
        List<String> styleCodeList = req.getStyleCodeList();
        if (CollUtil.isEmpty(styleCodeList)) {
            return;
        }
        List<DigitalPrintingStyle> printingStyleList = digitalPrintingStyleRepository.listByStyleCodes(styleCodeList);

        printingStyleList.forEach(item -> {
            try {
                //推送用户信息设置为spu的创建人
                UserContentHolder.set(new UserContent()
                        .setCurrentUserId(item.getCreatorId())
                        .setCurrentUserName(item.getCreatorName())
                        .setCurrentUserCode(item.getCreatorId()+"")
                        .setTenantId(2L)
                        .setSystemCode("SDP"));

                this.syncProduct2Pop(Collections.singletonList(item.getStyleCode()), req.getPushState());

            } catch (Exception e) {
                log.error("印花款推送失败, spu:{} ", item.getStyleCode(), e);
            } finally {
                UserContentHolder.clean();
            }
        });
    }

    @Override
    public List<PopCreateProductVo> queryStyleInfo4Pop(List<String> styleCodeList) {
        if (CollUtil.isEmpty(styleCodeList)) {
            return Collections.emptyList();
        }
        //spu
        List<DigitalPrintingStyle> printingStyleList = digitalPrintingStyleRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(printingStyleList)) {
            return Collections.emptyList();
        }
        List<Long> styleIdList = StreamUtil.convertList(printingStyleList, DigitalPrintingStyle::getPrintingStyleId);

        //spu详情
        List<DigitalPrintingStyleDetail> styleDetailList = digitalPrintingStyleDetailRepository.listByStyleIds(styleIdList);
        SdpDesignException.notEmpty(styleDetailList, "spu详情信息不存在!");
        if (CollUtil.isEmpty(styleDetailList)) {
            log.warn("spu详情信息不存在");
            return Collections.emptyList();
        }
        Map<Long, DigitalPrintingStyleDetail> detailMap = StreamUtil.list2Map(styleDetailList, DigitalPrintingStyleDetail::getPrintingStyleId);

        //skc
        List<DigitalPrintingPrototype> digitalPrintingPrototypeList = digitalPrintingPrototypeRepository.listBySpuList(styleCodeList);
        if (CollUtil.isEmpty(digitalPrintingPrototypeList)) {
            log.warn("skc详情信息不存在");
            return Collections.emptyList();
        }
        Map<String, List<DigitalPrintingPrototype>> skcGroupMap = StreamUtil.groupingBy(digitalPrintingPrototypeList, DigitalPrintingPrototype::getStyleCode);

        //pop商品信息封装
        List<PopCreateProductVo> productList = new ArrayList<>(printingStyleList.size());
        printingStyleList.forEach(printingStyle -> {
            DigitalPrintingStyleDetail styleDetail = detailMap.get(printingStyle.getPrintingStyleId());
            List<DigitalPrintingPrototype> prototypeList = skcGroupMap.get(printingStyle.getStyleCode());
            if (CollUtil.isEmpty(prototypeList) || Objects.isNull(styleDetail)) {
                return;
            }
            CreateProductDto productReq = DigitalPrintConverter.convertProductCreateReq(printingStyle, styleDetail, prototypeList);
            PopCreateProductVo productVo = PopCreateInfoConverter.buildPopCreateProductVo(productReq);
            productVo.setStyleType(SdpStyleTypeEnum.DIGITAL_PRINTING.getCode());

            productList.add(productVo);
        });

        return productList;
    }

    @Override
    public void fixFailState(DpPushStateFixReq req) {
        List<String> styleCodeList = req.getStyleCodeList();
        if (CollUtil.isEmpty(styleCodeList)) {
            return;
        }
        List<DigitalPrintingPrototype> prototypeList = digitalPrintingPrototypeRepository.listUnSuccessBySpuList(styleCodeList);
        if (CollUtil.isEmpty(prototypeList)) {
            return;
        }

        //校验是否推送到pop
        List<String> designCodeList = StreamUtil.convertListAndDistinct(prototypeList, DigitalPrintingPrototype::getDesignCode);
        Map<String,Boolean> skcIsExistMap = popProductHelper.checkSkcIsExist(designCodeList);

        List<Long> printingPrototypeIdList = new ArrayList<>();
        Set<String> spuSet = new HashSet<>();
        prototypeList.forEach(prototype -> {
            //查询skc在pop中是否已存在, 存在则更新推送状态
            if (skcIsExistMap.get(prototype.getDesignCode())) {
                log.info("=== 印花款更新推送状态, skc:{} ===", prototype.getDesignCode());
                printingPrototypeIdList.add(prototype.getPrintingPrototypeId());

                spuSet.add(prototype.getStyleCode());
            }
        });

        digitalPrintingPrototypeRepository.fixFailState(printingPrototypeIdList);
        digitalPrintingStyleRepository.fixFailState(spuSet);
    }

    @Override
    public List<VisualTaskListExtraVo> visualListExtra(List<String> styleCodes) {
        if (CollUtil.isEmpty(styleCodes)) {
            return Collections.emptyList();
        }
        List<DigitalPrintingPrototype> prototypeList = digitalPrintingPrototypeRepository.listBySpuList(styleCodes);
        if (CollUtil.isEmpty(prototypeList)) {
            return Collections.emptyList();
        }
        Map<String, List<DigitalPrintingPrototype>> skcGroupMap = StreamUtil.groupingBy(prototypeList, DigitalPrintingPrototype::getStyleCode);

        //构建返回结果
        return skcGroupMap.entrySet().stream()
                .map(entry -> this.buildVisualTaskListExtraVo(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * 构建单个SPU的 VisualTaskListExtraVo 对象
     */
    private VisualTaskListExtraVo buildVisualTaskListExtraVo(String styleCode,
                                                             List<DigitalPrintingPrototype> skcList) {
        List<VisualTaskListExtraVo.SkcInfo> skcInfoList = skcList.stream()
                .map(prototype -> {
                    ColorInfoVo colorInfo = ColorInfoVo.builder()
                            .color(prototype.getColorName())
                            .colorEnglishName(prototype.getColorCode())
                            .colorAbbrCode(prototype.getColorAbbrCode())
                            .build();
                    return VisualTaskListExtraVo.SkcInfo.builder()
                            .designCode(prototype.getDesignCode())
                            .colorInfoList(Collections.singletonList(colorInfo))
                            .build();
                })
                .collect(Collectors.toList());

        return VisualTaskListExtraVo.builder()
                .styleCode(styleCode)
                .skcInfoList(skcInfoList)
                .build();
    }

    private void pushProduct2Pop(Integer pushState, DigitalPrintingStyle printingStyle, DigitalPrintingStyleDetail styleDetail, List<DigitalPrintingPrototype> skcInfoList) {
        if (CollUtil.isEmpty(skcInfoList) || Objects.isNull(printingStyle) || Objects.isNull(styleDetail)) {
            return;
        }
        //根据状态推送
        List<DigitalPrintingPrototype> pushSkcList = skcInfoList.stream()
                .filter(skc -> Objects.equals(skc.getPushStatus(), pushState))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(pushSkcList)) {
            CreateProductDto productReq = DigitalPrintConverter.convertProductCreateReq(printingStyle, styleDetail, pushSkcList);
            popProductHelper.noticeCreateSpotProduct(productReq);
        }

    }

    private DpStyleAddVo buildAddVo(DpStyleAddReq req,
                                                 DigitalPrintingStyle printingStyle,
                                                 List<DigitalPrintingPrototype> skcInfoList) {
        List<DpStyleAddVo.DigitalSkcInfo> digitalSkcInfoList = new ArrayList<>();
        skcInfoList.forEach(item -> {
            DpStyleAddVo.DigitalSkcInfo digitalSkcInfo = new DpStyleAddVo.DigitalSkcInfo();
            BeanUtils.copyProperties(item, digitalSkcInfo);
            digitalSkcInfoList.add(digitalSkcInfo);
        });

        return new DpStyleAddVo()
                .setSourceBizId(req.getSourceBizId())
                .setPrintingStyleId(printingStyle.getPrintingStyleId())
                .setStyleCode(printingStyle.getStyleCode())
                .setSkcInfoList(digitalSkcInfoList)
                .setSuccessful(true)
                .setMessage("成功")
                ;
    }

    private List<DigitalPrintingPrototype> buildPrintingPrototypes(DpStyleAddReq req, DigitalPrintingStyle printingStyle) {
        List<DigitalPrintingPrototype> skcInfoList = new ArrayList<>();
        int skcNum = 0;
        for (DpStyleAddReq.SkcInfo skcInfo : req.getSkcInfoList()) {
            DigitalPrintingPrototype skc = new DigitalPrintingPrototype();
            BeanUtils.copyProperties(skcInfo, skc);
            skc.setStyleCode(printingStyle.getStyleCode());
            skc.setPrintingPrototypeId(IdPool.getId());
            //设置skc编码
            String skcNumFormat = String.format("%1$02d", skcNum += 1);
            skc.setDesignCode(printingStyle.getStyleCode() + skcNumFormat);
            //设置推送状态: 同步中
            skc.setPushStatus(DigitalPrintingPushStatusEnum.PUSHING.getCode());
            skcInfoList.add(skc);
        }
        return skcInfoList;
    }

    private DigitalPrintingStyleDetail buildPrintingStyleDetail(DpStyleAddReq req, long printingStyleId) {
        DigitalPrintingStyleDetail styleDetail = new DigitalPrintingStyleDetail();
        BeanUtils.copyProperties(req, styleDetail);
        styleDetail.setPrintingStyleId(printingStyleId);
        styleDetail.setPrintingStyleDetailId(IdPool.getId());
        styleDetail.setProductImageList(req.getImageFlowerUrls());
        List<DpStyleAddReq.LabelInfo> subjectLabels = req.getSubjectLabels();
        if (CollectionUtil.isNotEmpty(subjectLabels)){
            List<LabelInfoDTO> subjectLabel = subjectLabels.stream().map(subject -> {
                LabelInfoDTO labelInfoDTO = new LabelInfoDTO();
                labelInfoDTO.setEn(subject.getEn());
                labelInfoDTO.setCn(subject.getCn());
                return labelInfoDTO;
            }).collect(Collectors.toList());
            styleDetail.setSubjectLabel(subjectLabel);
        }
        // 面料信息
        DpStyleAddReq.FabricInfo fabricInfo = req.getFabricInfo();
        if (ObjectUtil.isNotNull(fabricInfo)) {
            styleDetail.setFabricInfo(JSONUtil.toJsonStr(fabricInfo));
        }
        if(CollectionUtil.isNotEmpty(req.getAttributes())){
            styleDetail.setAttributes(JSON.toJSONString(req.getAttributes()));
        }
        if(CollectionUtil.isNotEmpty(req.getSizeDetails())){
            styleDetail.setSizeDetail(JSON.toJSONString(req.getSizeDetails()));
        }
        return styleDetail;
    }

    private DigitalPrintingStyle buildPrintingStyle(DpStyleAddReq req,
                                                    long printingStyleId,
                                                    String styleCode) {
        DigitalPrintingStyle printingStyle = new DigitalPrintingStyle();
        BeanUtils.copyProperties(req, printingStyle);
        printingStyle.setWaveBandCode(req.getWaves());
        printingStyle.setPrintingStyleId(printingStyleId);
        printingStyle.setStyleCode(styleCode);
        printingStyle.setSourceType(DigitalPrintingSourceTypeEnum.FIRST_STAGE.getCode());
        printingStyle.setSpuPushStatus(DigitalPrintingPushStatusEnum.PUSHING.getCode());
        printingStyle.setPushCount(1);

        List<String> sizeStandardValue = req.getSizeStandardValue();
        if (CollectionUtil.isNotEmpty(sizeStandardValue)) {
            printingStyle.setSizeStandardValue(JSONUtil.toJsonStr(sizeStandardValue));
        }
        if (CollectionUtil.isNotEmpty(req.getStyleSeasonList())) {
            printingStyle.setStyleSeason(JSONUtil.toJsonStr(req.getStyleSeasonList()));
        }
        /*
        if (StrUtil.isBlank(req.getCategory()) || StrUtil.isBlank(req.getCategoryName())) {
            //AI品类映射内部品类
            AiCategoryMappingVO aiCategoryMappingVO = aiCategoryMappingVOMap.get(req.getAiCategory());
            if (Objects.nonNull(aiCategoryMappingVO)) {
                printingStyle.setCategory(aiCategoryMappingVO.getCategoryCode());
                printingStyle.setCategoryName(aiCategoryMappingVO.getCategoryName());
            }
        }
         */
        return printingStyle;
    }

    /*
    private void checkAdd(DpStyleAddReq req) {
        if (Objects.equals(req.getSourceType(), DigitalPrintingSourceTypeEnum.OLD.getCode())) {
            SdpDesignException.notBlank(req.getSourceSpu(), "sourceSpu不能为空");
            String sourceSpu = req.getSourceSpu();
            DigitalPrintingStyle existStyle = digitalPrintingStyleRepository.getBySourceSpu(sourceSpu);
            SdpDesignException.isNull(existStyle, "款式已存在! spu:{}", sourceSpu);
        }else if (Objects.equals(req.getSourceType(), DigitalPrintingSourceTypeEnum.FIRST_STAGE.getCode())) {
            SdpDesignException.notNull(req.getSourceBizId(), "sourceBizId不能为空");
            DigitalPrintingStyle existStyle = digitalPrintingStyleRepository.getBySourceBizId(req.getSourceBizId());
            SdpDesignException.isNull(existStyle, "款式已存在! sourceBizId:{}", req.getSourceBizId());
        }else {
            throw new SdpDesignException("未知业务来源!");
        }
    }

     */


    private void addLog(Long printingPrototypeId, String logContent) {
        DesignLogSdpSaveReq logSdpSaveReq = DesignLogSdpSaveReq.builder()
                .bizId(printingPrototypeId)
                .bizType(DesignLogBizTypeEnum.DIGITAL_PRINTING)
                .content(logContent)
                .build();
        designLogService.sdpSave(logSdpSaveReq);
    }


}
