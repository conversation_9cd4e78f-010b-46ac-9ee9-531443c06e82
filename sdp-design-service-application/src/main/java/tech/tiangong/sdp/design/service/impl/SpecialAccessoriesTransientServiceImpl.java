package tech.tiangong.sdp.design.service.impl;

import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.entity.SpecialAccessoriesTransient;
import tech.tiangong.sdp.design.repository.SpecialAccessoriesTransientRepository;
import tech.tiangong.sdp.design.service.SpecialAccessoriesTransientService;
import tech.tiangong.sdp.design.vo.query.bom.SpecialAccessoriesTransientQuery;
import tech.tiangong.sdp.design.vo.req.bom.SpecialAccessoriesTransientReq;
import tech.tiangong.sdp.design.vo.resp.bom.SpecialAccessoriesTransientVo;

/**
 * 特殊辅料_暂存表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpecialAccessoriesTransientServiceImpl implements SpecialAccessoriesTransientService {
    private final SpecialAccessoriesTransientRepository specialAccessoriesTransientRepository;

    @Override
    public PageRespVo<SpecialAccessoriesTransientVo> page(SpecialAccessoriesTransientQuery query) {
        IPage<SpecialAccessoriesTransientVo> page = specialAccessoriesTransientRepository.findPage(query);
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    public SpecialAccessoriesTransientVo getById(Long id) {
        SpecialAccessoriesTransient entity = specialAccessoriesTransientRepository.getById(id);
        SpecialAccessoriesTransientVo vo = new SpecialAccessoriesTransientVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SpecialAccessoriesTransientReq req) {
        SpecialAccessoriesTransient entity = new SpecialAccessoriesTransient();
        BeanUtils.copyProperties(req, entity);
        specialAccessoriesTransientRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SpecialAccessoriesTransientReq req) {
        SpecialAccessoriesTransient entity = new SpecialAccessoriesTransient();
        BeanUtils.copyProperties(req, entity);
        specialAccessoriesTransientRepository.updateById(entity);
    }

    @Override
    public void remove(Long id) {
        specialAccessoriesTransientRepository.removeById(id);
    }

}
