package tech.tiangong.sdp.design.remote.zj.bom;

import cn.yibuyun.framework.net.DataResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import tech.tiangong.sdp.core.config.ZjOpenFeignUserContentConfig;
import tech.tiangong.sdp.design.vo.req.zj.bom.BomMaterialDosageAccountByJVV2Req;
import tech.tiangong.sdp.design.vo.req.zj.bom.BomMaterialPrePlacementOpenReq;
import tech.tiangong.sdp.design.vo.req.zj.bom.BomOrderAddOpenReq;
import tech.tiangong.sdp.design.vo.resp.zj.bom.BomOrderOpenResp;

/**
 * @Created by j<PERSON><PERSON><PERSON>
 * @ClassName PlmRemoteHelper
 * @Description 致景PLM-BOM单模块 对接路由
 * @Date 2024/11/29 18:54
 */
@FeignClient(value = "plm-design",
        contextId = "PLM-BOMCommodityClient",
        configuration = ZjOpenFeignUserContentConfig.class,
        path = "/zj-tg-api/plm-design/open/v2",
        url = "${cx-tg.domain.url}")
public interface ZjPlmBomRemoteHelper {


    /**
     * 新增bom
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84453"
     *
     * @param req 设计款号列表
     * @return bom单信息
     */
    @PostMapping("/bom-order-open/add")
    DataResponse<BomOrderOpenResp> add(@RequestBody BomOrderAddOpenReq req);

    /**
     * 通知履约，进行物料齐套预占位
     * @see "https://yapi.textile-story.com/project/1404/interface/api/84461"
     *
     * @param req
     * @return
     */
    @PostMapping("/bom-order-open/pre-placement-complete-materials")
    DataResponse<Void> prePlacementCompleteMaterials(@RequestBody BomMaterialPrePlacementOpenReq req);

    /**
     * jv-Bom物料清单核算用量
     *
     * @param req req
     * @return 响应结果
     */
    @PostMapping("/bom-order-open/material/dosage-account-by-jv")
    DataResponse<Void> bomMaterialDosageAccountByJV(@Validated @RequestBody BomMaterialDosageAccountByJVV2Req req);

}
