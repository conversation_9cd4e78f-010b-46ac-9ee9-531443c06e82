package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.exception.BusinessException;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DesignLogBizTypeEnum;
import tech.tiangong.sdp.design.enums.visual.*;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.VisualQcService;
import tech.tiangong.sdp.design.service.VisualTaskHandleService;
import tech.tiangong.sdp.design.service.VisualTaskNodeStateService;
import tech.tiangong.sdp.design.vo.req.log.DesignLogSdpSaveReq;
import tech.tiangong.sdp.design.vo.req.visual.*;
import tech.tiangong.sdp.design.vo.resp.visual.BatchUploadImagesResp;
import tech.tiangong.sdp.utils.Bool;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class VisualTaskHandleServiceImpl implements VisualTaskHandleService {
    private final VisualTaskRepository visualTaskRepository;
    private final VisualTaskDetailRepository visualTaskDetailRepository;
    private final VisualTaskTryOnRepository visualTaskTryOnRepository;
    private final VisualTaskOnShelfRepository visualTaskOnShelfRepository;
    private final VisualImagePackageRepository visualImagePackageRepository;
    private final VisualQcRepository visualQcRepository;
    private final VisualTaskHelper visualTaskHelper;
    private final VisualTaskNodeStateService visualTaskNodeStateService;
    private final VisualQcService visualQcService;
    private final VisualDemandRepository visualDemandRepository;
    private final VisualSpuRepository visualSpuRepository;
    private final VisualTaskTryOnLogRepository visualTaskTryOnLogRepository;

    @Override
    public Boolean tryOnHandle(TryOnHandleReq req) {
        VisualTask visualTask = visualTaskRepository.getById(req.getTaskId());
        Assert.isTrue(visualTask!=null,"任务不存在");
        Assert.isTrue(VisualTaskStateEnum.DOING.getCode().equals(visualTask.getState()),"任务当前状态不能编辑");
        VisualTaskDetail visualTaskDetail = visualTaskDetailRepository.getByTaskId(req.getTaskId());
        Assert.isTrue(visualTaskDetail.getLatestTryOnDetailId()!=null,"任务未被分配tryOn处理人");
        // 判断是否有正在进行中的线上try on任务
        List<VisualTaskTryOnLog> processingLog = visualTaskTryOnLogRepository.getProcessingLogByVisualTaskId(req.getTaskId());
        Assert.isTrue(CollectionUtil.isEmpty(processingLog),"当前正在有线上try on 任务进行中，请稍后再试");
        //判断是否需要过程图
        VisualTaskProcessTypeEnum visualTaskProcessType = VisualTaskProcessTypeEnum.findByCode(visualTask.getProcessType());
        if(visualTaskProcessType.equals(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF)){
            Assert.isTrue(CollectionUtil.isNotEmpty(req.getTryOnImages()),"tryOn图不能为空");
        }else{
            Assert.isTrue(req.getOnShelfImages()!=null,"SPU图包不能为空");
        }

        //判断需求是否被更新
        VisualDemand visualDemand = visualDemandRepository.getById(visualTask.getLatestDemandId());
        Assert.isTrue(visualDemand.getVersionNum().equals(req.getDemandVersion()),VisualErrorCodeEnum.ERROR_98102.getCode());
        VisualSpu visualSpu = visualSpuRepository.getByStyleCode(visualTask.getStyleCode());
        //判断上架图处理是否被更新
        if(visualTaskDetail.getLatestOnShelfDetailId()!=null){
            VisualTaskOnShelf visualTaskOnShelf = visualTaskOnShelfRepository.getById(visualTaskDetail.getLatestOnShelfDetailId());
            Assert.isTrue(req.getOnShelfHandleVersion().equals(visualTaskOnShelf.getVersionNum()),VisualErrorCodeEnum.ERROR_98101.getCode());
        }
        //判断SPU图包是否被更新
        VisualImagePackage visualImagePackage = visualImagePackageRepository.getLatestByStyleCode(visualTask.getStyleCode());
        if(visualImagePackage!=null){
            Assert.isTrue(visualImagePackage.getVersionNum().equals(req.getImagePackageVersion()),VisualErrorCodeEnum.ERROR_98103.getCode());
        }
        VisualTaskTryOn oldVisualTaskTryOn = visualTaskTryOnRepository.getById(visualTaskDetail.getLatestTryOnDetailId());
        Assert.isTrue(oldVisualTaskTryOn.getVersionNum().equals(req.getTryOnHandleVersion()),VisualErrorCodeEnum.ERROR_98100.getCode());

        OnShelfImagePackage onShelfImagePackage = new OnShelfImagePackage();
        if(req.getOnShelfImages()!=null){
            onShelfImagePackage = req.getOnShelfImages();
            visualTaskHelper.checkOnShelfImagePackageImageFileName(onShelfImagePackage);
        }else{
            onShelfImagePackage = visualTaskHelper.classifyImage(visualSpu,req.getTryOnImages());
        }

        //第一次提交，不创建新的记录
        if(StringUtils.isBlank(oldVisualTaskTryOn.getOnShelfImages())){
            oldVisualTaskTryOn.setHandleState(VisualTaskHandleStateEnum.FINISH.getCode());
            oldVisualTaskTryOn.setTryOnImages(CollectionUtil.isNotEmpty(req.getTryOnImages()) ? JSONObject.toJSONString(req.getTryOnImages()) : null);
            oldVisualTaskTryOn.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
            visualTaskTryOnRepository.updateById(oldVisualTaskTryOn);
        }else {
            //复制旧版本，并记录提交结果
            VisualTaskTryOn visualTaskTryOn = new VisualTaskTryOn();
            visualTaskTryOn.setTryOnDetailId(IdPool.getId());
            visualTaskTryOn.setTaskId(oldVisualTaskTryOn.getTaskId());
            visualTaskTryOn.setHandleState(VisualTaskHandleStateEnum.FINISH.getCode());
            visualTaskTryOn.setTryOnHandlerId(oldVisualTaskTryOn.getTryOnHandlerId());
            visualTaskTryOn.setTryOnHandlerName(oldVisualTaskTryOn.getTryOnHandlerName());
            visualTaskTryOn.setTryOnImages(CollectionUtil.isNotEmpty(req.getTryOnImages()) ? JSONObject.toJSONString(req.getTryOnImages()) : null);

            visualTaskTryOn.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
            visualTaskTryOn.setVersionNum(oldVisualTaskTryOn.getVersionNum() + 1);
            visualTaskTryOn.setIsLatest(1);

            visualTaskTryOnRepository.save(visualTaskTryOn);

            //记录最新tryOn详情ID和修图详情ID
            visualTaskDetail.setLatestTryOnDetailId(visualTaskTryOn.getTryOnDetailId());
            visualTaskDetailRepository.updateById(visualTaskDetail);
            //将旧任务过期掉
            oldVisualTaskTryOn.setIsLatest(0);
            visualTaskTryOnRepository.updateById(oldVisualTaskTryOn);
        }
        //记录环节状态
        visualTaskNodeStateService.saveVisualTaskNodeStepState(visualTask.getTaskId(), VisualTaskStepEnum.HANDLE, VisualTaskNodeEnum.TRYON_HANDLE,VisualTaskHandleStateEnum.FINISH.getCode());

        VisualTaskOnShelf oldVisualTaskOnShelf = visualTaskDetail.getLatestOnShelfDetailId()!=null ? visualTaskOnShelfRepository.getById(visualTaskDetail.getLatestOnShelfDetailId()) : null;
        if(visualTaskProcessType.equals(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF)
                && oldVisualTaskOnShelf!=null
                && !VisualTaskHandleStateEnum.FINISH.getCode().equals(oldVisualTaskOnShelf.getHandleState())){
            oldVisualTaskOnShelf.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
            visualTaskOnShelfRepository.updateById(oldVisualTaskOnShelf);
            //记录环节状态
            visualTaskNodeStateService.saveVisualTaskNodeStepState(visualTask.getTaskId(), VisualTaskStepEnum.HANDLE, VisualTaskNodeEnum.ON_SHELF_HANDLE,VisualTaskHandleStateEnum.DOING.getCode());
        }
        //如果是仅tryOn，则需要重新进行质检
        else if(visualTaskProcessType.equals(VisualTaskProcessTypeEnum.TRY_ON)){
            List<VisualQc> qcList = initVisualQc(visualTask.getTaskId(),onShelfImagePackage,oldVisualTaskTryOn.getTryOnHandlerId(),oldVisualTaskTryOn.getTryOnHandlerName());
            if(CollectionUtil.isNotEmpty(qcList)){
                visualQcRepository.saveOrUpdateBatch(qcList);
                visualQcService.saveOrUpdateSpuIdentify(qcList);
            }
            //提交成功，则更新任务的质检状态为待质检
            visualTask.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
            visualTaskRepository.updateById(visualTask);
            //记录质检环节状态
            visualTaskNodeStateService.saveVisualTaskNodeStepState(visualTask.getTaskId(), VisualTaskStepEnum.QC, VisualTaskNodeEnum.VISUAL_QC,VisualTaskQcStateEnum.WAITING_QC.getCode());
        }
        //记录日志
        visualTaskHelper.addLog(visualTask.getStyleCode(),visualTask.getTaskId(),"编辑tryOn图");
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<BatchUploadImagesResp> batchUploadImageHandle(HandleBatchReq req) {
        if(req.getImageFileList()!=null){
            req.getImageFileList().forEach(imageFile->{
                Assert.isTrue(visualTaskHelper.isValidFileName(imageFile.getOrgImgName()),"文件名不能包含特殊字符"+imageFile.getOrgImgName());
            });
        }
        List<BatchUploadImagesResp> errorInfoList = new ArrayList<>();
        List<VisualTask> visualTasks = visualTaskRepository.listByIds(req.getTaskIds());
        List<VisualTaskDetail> details = visualTaskDetailRepository.listByTaskIds(req.getTaskIds());
        Set<String> styleCodes = visualTasks.stream().map(VisualTask::getStyleCode).collect(Collectors.toSet());
        List<VisualSpu> visualSpus = visualSpuRepository.listByStyleCodes(styleCodes);
        Map<String,VisualSpu> styleCodeToVisualSpuMap = visualSpus.stream().collect(Collectors.toMap(VisualSpu::getStyleCode,v->v,(v1,v2) -> v2));
        Map<Long,VisualTaskDetail> taskIdToDetailMap = details.stream().collect(Collectors.toMap(VisualTaskDetail::getTaskId, v->v,(k1, k2)->k2));

        List<VisualTaskTryOn> saveOrUpdateVisualTaskTryOnList = new ArrayList<>();
        List<VisualTaskOnShelf> saveOrUpdateVisualTaskOnShelfList = new ArrayList<>();
        List<VisualTask> updateVisualTaskList = new ArrayList<>();
        List<VisualTaskDetail> updateVisualTaskDetailList = new ArrayList<>();
        List<VisualQc> saveOrUpdateVisualQcList = new ArrayList<>();
        List<SaveVisualTaskNodeStepStateReq> saveVisualTaskNodeStepStateReqList = new ArrayList<>();
        List<DeleteVisualTaskNodeStepReq> deleteVisualTaskNodeStepReqList = new ArrayList<>();
        List<DesignLogSdpSaveReq> designLogSdpSaveReqList = new ArrayList<>();
        for(VisualTask visualTask : visualTasks){
            try {
//                Assert.isTrue(VisualTaskStateEnum.DOING.getCode().equals(visualTask.getState()), visualTask.getProcessCode()+"任务当前状态不能编辑");
                if(!VisualTaskStateEnum.DOING.getCode().equals(visualTask.getState())
                    || Bool.YES.getCode()==visualTask.getIsCancel()){
                    log.warn(visualTask.getProcessCode()+"任务当前状态不是处理中不能编辑");
                    continue;
                }
                VisualTaskDetail visualTaskDetail = taskIdToDetailMap.get(visualTask.getTaskId());
                Assert.isTrue(visualTaskDetail.getLatestTryOnDetailId() != null || visualTaskDetail.getLatestOnShelfDetailId() != null, visualTask.getProcessCode()+"任务未分配处理人");
                VisualSpu visualSpu = styleCodeToVisualSpuMap.get(visualTask.getStyleCode());
                VisualTaskProcessTypeEnum visualTaskProcessType = VisualTaskProcessTypeEnum.findByCode(visualTask.getProcessType());

                VisualTaskTryOn visualTaskTryOn = visualTaskDetail.getLatestTryOnDetailId() != null ? visualTaskTryOnRepository.getById(visualTaskDetail.getLatestTryOnDetailId()) : null;
                OnShelfImagePackage onShelfImagePackage = visualTaskHelper.classifyImage(visualSpu, req.getImageFileList());
                VisualTaskOnShelf visualTaskOnShelf = visualTaskDetail.getLatestOnShelfDetailId() != null ? visualTaskOnShelfRepository.getById(visualTaskDetail.getLatestOnShelfDetailId()) : null;
                //上传的tryOn图，只支持tryOn+修图的类型
                if (req.getUploadType() == 1
                        && VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.equals(visualTaskProcessType)) {
                    //记录tryOn图片
                    batchHandleVisualTaskTryOn(visualTaskTryOn,
                            req.getImageFileList(),
                            onShelfImagePackage,
                            visualTask,
                            visualTaskDetail,
                            saveOrUpdateVisualTaskTryOnList,
                            saveVisualTaskNodeStepStateReqList);

                    //初始化修图任务
                    batchHandleVisualTaskOnShelf(visualTaskOnShelf,null,
                            VisualTaskHandleStateEnum.DOING,
                            visualTask,
                            visualTaskDetail,
                            saveOrUpdateVisualTaskOnShelfList,
                            saveVisualTaskNodeStepStateReqList);
                    //记录日志
                    designLogSdpSaveReqList.add(DesignLogSdpSaveReq.builder().styleCode(visualTask.getStyleCode()).bizType(DesignLogBizTypeEnum.VISUAL).bizId(visualTask.getTaskId()).content("批量上传tryOn图").build());
                }
                //上传图包/尺寸表
                else if (req.getUploadType() == 2 || req.getUploadType() == 3) {
                    //tryOn+修图
                    if(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.equals(visualTaskProcessType)) {
                        //如果tryOn任务未完成，不能处理修图任务
                        if(visualTaskTryOn==null || !VisualTaskHandleStateEnum.FINISH.getCode().equals(visualTaskTryOn.getHandleState())){
                            throw new BusinessException(visualTask.getProcessCode()+"tryOn任务未处理完成，不能上传修图");
                        }
                        batchHandleVisualTaskOnShelf(visualTaskOnShelf,onShelfImagePackage,
                                VisualTaskHandleStateEnum.FINISH,
                                visualTask,
                                visualTaskDetail,
                                saveOrUpdateVisualTaskOnShelfList,
                                saveVisualTaskNodeStepStateReqList);
                    }
                    //仅tryOn
                    else if(VisualTaskProcessTypeEnum.TRY_ON.equals(visualTaskProcessType)){
                        //记录tryOn图片
                        batchHandleVisualTaskTryOn(visualTaskTryOn,
                                req.getImageFileList(),
                                onShelfImagePackage,
                                visualTask,
                                visualTaskDetail,
                                saveOrUpdateVisualTaskTryOnList,
                                saveVisualTaskNodeStepStateReqList);
                    }
                    //无需修图，修图
                    else {
                        batchHandleVisualTaskOnShelf(visualTaskOnShelf,onShelfImagePackage,
                                VisualTaskHandleStateEnum.FINISH,
                                visualTask,
                                visualTaskDetail,
                                saveOrUpdateVisualTaskOnShelfList,
                                saveVisualTaskNodeStepStateReqList);
                    }
                    Long handlerId = null;
                    String handlerName = null;
                    if(visualTaskDetail.getOnShelfHandlerId()!=null){
                        handlerId = visualTaskDetail.getOnShelfHandlerId();
                        handlerName = visualTaskDetail.getOnShelfHandlerName();
                    }else{
                        handlerId = visualTaskDetail.getTryOnHandlerId();
                        handlerName = visualTaskDetail.getTryOnHandlerName();
                    }
                    //初始化视觉质检任务
                    List<VisualQc> qcList = initVisualQc(visualTask.getTaskId(), onShelfImagePackage,handlerId,handlerName);
                    saveOrUpdateVisualQcList.addAll(qcList);
                    //记录质检环节状态
                    saveVisualTaskNodeStepStateReqList.add(SaveVisualTaskNodeStepStateReq.builder().visualTaskId(visualTask.getTaskId()).visualTaskStep(VisualTaskStepEnum.QC).visualTaskNode(VisualTaskNodeEnum.VISUAL_QC).state(VisualTaskQcStateEnum.WAITING_QC.getCode()).build());
                    //记录日志
                    designLogSdpSaveReqList.add(DesignLogSdpSaveReq.builder().styleCode(visualTask.getStyleCode()).bizType(DesignLogBizTypeEnum.VISUAL).bizId(visualTask.getTaskId()).content("批量上传修图").build());
                }
                else {
                    throw new BusinessException("上传类型与"+visualTask.getProcessCode()+"任务处理方式不匹配");
                }
                //记录最新tryOn详情ID和修图详情ID
                updateVisualTaskDetailList.add(visualTaskDetail);
                if(CollectionUtil.isNotEmpty(saveOrUpdateVisualQcList)){
                    //提交成功，则更新任务的质检状态为待质检
                    visualTask.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
                    updateVisualTaskList.add(visualTask);
                }
            }catch (Exception e) {
                log.error("批量上传图片异常",e);
                BatchUploadImagesResp imagesResp = new BatchUploadImagesResp();
                imagesResp.setStyleCode(visualTask.getStyleCode());
                imagesResp.setProcessCode(visualTask.getProcessCode());
                imagesResp.setReason(e.getMessage());
                errorInfoList.add(imagesResp);
            }
        }

        if(CollectionUtil.isNotEmpty(saveOrUpdateVisualTaskTryOnList)){
            visualTaskTryOnRepository.saveOrUpdateBatch(saveOrUpdateVisualTaskTryOnList);
        }
        if(CollectionUtil.isNotEmpty(saveOrUpdateVisualTaskOnShelfList)){
            visualTaskOnShelfRepository.saveOrUpdateBatch(saveOrUpdateVisualTaskOnShelfList);
        }
        if(CollectionUtil.isNotEmpty(updateVisualTaskList)){
            visualTaskRepository.updateBatchById(updateVisualTaskList);
        }
        if(CollectionUtil.isNotEmpty(updateVisualTaskDetailList)){
            visualTaskDetailRepository.updateBatchById(updateVisualTaskDetailList);
        }
        if(CollectionUtil.isNotEmpty(saveOrUpdateVisualQcList)){
            visualQcRepository.saveOrUpdateBatch(saveOrUpdateVisualQcList);
            visualQcService.saveOrUpdateSpuIdentify(saveOrUpdateVisualQcList);
        }
        if(CollectionUtil.isNotEmpty(saveVisualTaskNodeStepStateReqList)){
            visualTaskNodeStateService.saveVisualTaskNodeStepStates(saveVisualTaskNodeStepStateReqList);
        }
        if(CollectionUtil.isNotEmpty(deleteVisualTaskNodeStepReqList)){
            visualTaskNodeStateService.deleteVisualTaskNodeStepList(deleteVisualTaskNodeStepReqList);
        }
        if(CollectionUtil.isNotEmpty(designLogSdpSaveReqList)){
            visualTaskHelper.addLogs(designLogSdpSaveReqList);
        }
        return errorInfoList;
    }

    private void batchHandleVisualTaskTryOn(VisualTaskTryOn visualTaskTryOn,
                       List<ImageFile> imageFileList,
                       OnShelfImagePackage onShelfImagePackage,
                       VisualTask visualTask,
                       VisualTaskDetail visualTaskDetail,
                       List<VisualTaskTryOn> saveOrUpdateVisualTaskTryOnList,
                       List<SaveVisualTaskNodeStepStateReq> saveVisualTaskNodeStepStateReqList){
        //第一次提交，不创建新的记录
        if(visualTaskTryOn!=null && StringUtils.isBlank(visualTaskTryOn.getOnShelfImages())){
            visualTaskTryOn.setHandleState(VisualTaskHandleStateEnum.FINISH.getCode());
            visualTaskTryOn.setTryOnImages(CollectionUtil.isNotEmpty(imageFileList) ? JSONObject.toJSONString(imageFileList) : null);
            visualTaskTryOn.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
            saveOrUpdateVisualTaskTryOnList.add(visualTaskTryOn);
        }else{
            //复制旧版本，并记录提交结果
            VisualTaskTryOn newVisualTaskTryOn = new VisualTaskTryOn();
            newVisualTaskTryOn.setTryOnDetailId(IdPool.getId());
            newVisualTaskTryOn.setTaskId(visualTask.getTaskId());
            newVisualTaskTryOn.setHandleState(VisualTaskHandleStateEnum.FINISH.getCode());
            newVisualTaskTryOn.setTryOnHandlerId(visualTaskDetail.getTryOnHandlerId());
            newVisualTaskTryOn.setTryOnHandlerName(visualTaskDetail.getTryOnHandlerName());
            newVisualTaskTryOn.setTryOnImages(CollectionUtil.isNotEmpty(imageFileList) ? JSONObject.toJSONString(imageFileList) : null);

            newVisualTaskTryOn.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
            newVisualTaskTryOn.setVersionNum(visualTaskTryOn == null ? 1 : visualTaskTryOn.getVersionNum() + 1);
            newVisualTaskTryOn.setIsLatest(1);

            visualTaskDetail.setLatestTryOnDetailId(newVisualTaskTryOn.getTryOnDetailId());
            saveOrUpdateVisualTaskTryOnList.add(newVisualTaskTryOn);

            //将旧任务过期掉
            if (visualTaskTryOn != null) {
                visualTaskTryOn.setIsLatest(0);
                saveOrUpdateVisualTaskTryOnList.add(visualTaskTryOn);
            }
        }
        //记录tryOn环节状态
        saveVisualTaskNodeStepStateReqList.add(SaveVisualTaskNodeStepStateReq.builder().visualTaskId(visualTask.getTaskId()).visualTaskStep(VisualTaskStepEnum.HANDLE).visualTaskNode(VisualTaskNodeEnum.TRYON_HANDLE).state(VisualTaskHandleStateEnum.FINISH.getCode()).build());
    }

    private void batchHandleVisualTaskOnShelf(VisualTaskOnShelf visualTaskOnShelf,
                                              OnShelfImagePackage onShelfImagePackage,
                                              VisualTaskHandleStateEnum visualTaskHandleState,
                                              VisualTask visualTask,
                                              VisualTaskDetail visualTaskDetail,
                                              List<VisualTaskOnShelf> saveOrUpdateVisualTaskOnShelfList,
                                              List<SaveVisualTaskNodeStepStateReq> saveVisualTaskNodeStepStateReqList){
        //第一次提交，不创建新的记录
        if(visualTaskOnShelf!=null && StringUtils.isBlank(visualTaskOnShelf.getOnShelfImages())){
            visualTaskOnShelf.setHandleState(visualTaskHandleState.getCode());
            visualTaskOnShelf.setOnShelfImages(onShelfImagePackage==null ? null : JSONObject.toJSONString(onShelfImagePackage));
            saveOrUpdateVisualTaskOnShelfList.add(visualTaskOnShelf);
        }else{
            //复制旧版本，并记录提交结果
            VisualTaskOnShelf newVisualTaskOnShelf = new VisualTaskOnShelf();
            newVisualTaskOnShelf.setOnShelfDetailId(IdPool.getId());
            newVisualTaskOnShelf.setTaskId(visualTask.getTaskId());
            newVisualTaskOnShelf.setHandleState(visualTaskHandleState.getCode());
            newVisualTaskOnShelf.setOnShelfHandlerId(visualTaskDetail.getOnShelfHandlerId());
            newVisualTaskOnShelf.setOnShelfHandlerName(visualTaskDetail.getOnShelfHandlerName());
            newVisualTaskOnShelf.setOnShelfImages(onShelfImagePackage==null ? null : JSONObject.toJSONString(onShelfImagePackage));
            newVisualTaskOnShelf.setVersionNum(visualTaskOnShelf != null ? visualTaskOnShelf.getVersionNum() + 1 : 1);
            newVisualTaskOnShelf.setIsLatest(1);

            visualTaskDetail.setLatestOnShelfDetailId(newVisualTaskOnShelf.getOnShelfDetailId());
            saveOrUpdateVisualTaskOnShelfList.add(newVisualTaskOnShelf);
            //将旧任务过期掉
            if (visualTaskOnShelf != null) {
                visualTaskOnShelf.setIsLatest(0);
                saveOrUpdateVisualTaskOnShelfList.add(visualTaskOnShelf);
            }
        }
        //记录环节状态
        saveVisualTaskNodeStepStateReqList.add(SaveVisualTaskNodeStepStateReq.builder().visualTaskId(visualTask.getTaskId()).visualTaskStep(VisualTaskStepEnum.HANDLE).visualTaskNode(VisualTaskNodeEnum.ON_SHELF_HANDLE).state(visualTaskHandleState.getCode()).build());
    }

    @Override
    public Boolean onShelfHandle(OnShelfHandleReq req) {
        VisualTask visualTask = visualTaskRepository.getById(req.getTaskId());
        Assert.isTrue(visualTask!=null,"任务不存在");
        Assert.isTrue(VisualTaskStateEnum.DOING.getCode().equals(visualTask.getState()),"任务当前状态不能编辑");
        VisualTaskDetail visualTaskDetail = visualTaskDetailRepository.getByTaskId(req.getTaskId());
        Assert.isTrue(visualTaskDetail.getLatestOnShelfDetailId()!=null,"任务未被分配修图人");
        //判断需求是否被更新
        VisualDemand visualDemand = visualDemandRepository.getById(visualTask.getLatestDemandId());
        Assert.isTrue(visualDemand.getVersionNum().equals(req.getDemandVersion()),VisualErrorCodeEnum.ERROR_98102.getCode());
        //判断tryOn图处理是否被更新
        if(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.getCode().equals(visualTask.getProcessType())
                && visualTaskDetail.getLatestTryOnDetailId()!=null){
            VisualTaskTryOn visualTaskTryOn = visualTaskTryOnRepository.getById(visualTaskDetail.getLatestTryOnDetailId());
            Assert.isTrue(visualTaskTryOn.getVersionNum().equals(req.getTryOnHandleVersion()),VisualErrorCodeEnum.ERROR_98100.getCode());
        }
        //判断SPU图包是否被更新
        VisualImagePackage visualImagePackage = visualImagePackageRepository.getLatestByStyleCode(visualTask.getStyleCode());
        if(visualImagePackage!=null){
            Assert.isTrue(visualImagePackage.getVersionNum().equals(req.getImagePackageVersion()),VisualErrorCodeEnum.ERROR_98103.getCode());
        }
        VisualTaskOnShelf oldVisualTaskOnShelf = visualTaskOnShelfRepository.getById(visualTaskDetail.getLatestOnShelfDetailId());
        Assert.isTrue(req.getOnShelfHandleVersion().equals(oldVisualTaskOnShelf.getVersionNum()),VisualErrorCodeEnum.ERROR_98101.getCode());

        OnShelfHandleImagePackageReq onShelfHandleImagePackageReq = req.getOnShelfImages();
        Assert.isTrue(CollectionUtil.isNotEmpty(onShelfHandleImagePackageReq.getSpuImages()),"SPU图片不能为空");
        //设计图包的SPU
        onShelfHandleImagePackageReq.setStyleCode(visualTask.getStyleCode());
        //转换成上架图包对象
        OnShelfImagePackage onShelfImagePackage = new OnShelfImagePackage();
        onShelfImagePackage.setStyleCode(visualTask.getStyleCode());
        //转换SPU图
        List<OnShelfImagePackage.SpuImage> spuImages = onShelfHandleImagePackageReq.getSpuImages().stream().map(v->{
            OnShelfImagePackage.SpuImage spuImage = new OnShelfImagePackage.SpuImage();
            BeanUtils.copyProperties(v, spuImage);
            if(CollectionUtil.isNotEmpty(v.getImages())){
                spuImage.setImages(v.getImages());
            }
            return spuImage;
        }).collect(Collectors.toList());
        onShelfImagePackage.setSpuImages(spuImages);
        //转换SKC图
        if(CollectionUtil.isNotEmpty(onShelfHandleImagePackageReq.getSkcImages())) {
            List<OnShelfImagePackage.SkcImage> skcImages = onShelfHandleImagePackageReq.getSkcImages().stream().map(v -> {
                OnShelfImagePackage.SkcImage skcImage = new OnShelfImagePackage.SkcImage();
                BeanUtils.copyProperties(v, skcImage);
                if(CollectionUtil.isNotEmpty(v.getImages())){
                    skcImage.setImages(v.getImages());
                }
                return skcImage;
            }).collect(Collectors.toList());
            onShelfImagePackage.setSkcImages(skcImages);
        }
        visualTaskHelper.checkOnShelfImagePackageImageFileName(onShelfImagePackage);
        //第一次提交，不创建新的记录
        if(StringUtils.isBlank(oldVisualTaskOnShelf.getOnShelfImages())){
            oldVisualTaskOnShelf.setHandleState(VisualTaskHandleStateEnum.FINISH.getCode());
            oldVisualTaskOnShelf.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
            visualTaskOnShelfRepository.updateById(oldVisualTaskOnShelf);
        }else {
            //复制旧版本，并记录提交结果
            VisualTaskOnShelf visualTaskOnShelf = new VisualTaskOnShelf();
            visualTaskOnShelf.setOnShelfDetailId(IdPool.getId());
            visualTaskOnShelf.setTaskId(oldVisualTaskOnShelf.getTaskId());
            visualTaskOnShelf.setHandleState(VisualTaskHandleStateEnum.FINISH.getCode());
            visualTaskOnShelf.setOnShelfHandlerId(oldVisualTaskOnShelf.getOnShelfHandlerId());
            visualTaskOnShelf.setOnShelfHandlerName(oldVisualTaskOnShelf.getOnShelfHandlerName());
            visualTaskOnShelf.setOnShelfImages(JSONObject.toJSONString(onShelfImagePackage));
            visualTaskOnShelf.setVersionNum(oldVisualTaskOnShelf.getVersionNum() + 1);
            visualTaskOnShelf.setIsLatest(1);

            visualTaskDetail.setLatestOnShelfDetailId(visualTaskOnShelf.getOnShelfDetailId());
            visualTaskOnShelfRepository.save(visualTaskOnShelf);
            //将旧任务过期掉
            oldVisualTaskOnShelf.setIsLatest(0);
            visualTaskOnShelfRepository.updateById(oldVisualTaskOnShelf);
            //记录最新tryOn详情ID和修图详情ID
            visualTaskDetailRepository.updateById(visualTaskDetail);
        }
        //记录环节状态
        visualTaskNodeStateService.saveVisualTaskNodeStepState(visualTask.getTaskId(), VisualTaskStepEnum.HANDLE, VisualTaskNodeEnum.ON_SHELF_HANDLE,VisualTaskHandleStateEnum.FINISH.getCode());

        //初始化视觉质检任务
        List<VisualQc> qcList = initVisualQc(visualTask.getTaskId(), onShelfImagePackage,oldVisualTaskOnShelf.getOnShelfHandlerId(),oldVisualTaskOnShelf.getOnShelfHandlerName());
        if(CollectionUtil.isNotEmpty(qcList)){
            visualQcRepository.saveOrUpdateBatch(qcList);
            visualQcService.saveOrUpdateSpuIdentify(qcList);
        }
        //提交成功，则更新任务的质检状态为待质检
        visualTask.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
        visualTaskRepository.updateById(visualTask);

        //记录质检环节状态
        visualTaskNodeStateService.saveVisualTaskNodeStepState(visualTask.getTaskId(), VisualTaskStepEnum.QC, VisualTaskNodeEnum.VISUAL_QC,VisualTaskQcStateEnum.WAITING_QC.getCode());
        //记录日志
        visualTaskHelper.addLog(visualTask.getStyleCode(),visualTask.getTaskId(),"编辑修图");
        return true;
    }

    private List<VisualQc> initVisualQc(Long taskId,OnShelfImagePackage onShelfImagePackage,Long handlerId,String handlerName){
        List<VisualQc> list = new ArrayList<>();
        //初始化要质检的图片
        List<VisualQcRecord> visualQcRecords = listImageFileByOnShelfImagePackage(onShelfImagePackage).stream().map(imageFile -> {
            VisualQcRecord visualQcRecord = new VisualQcRecord();
            visualQcRecord.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
            visualQcRecord.setImageFile(imageFile);
            return visualQcRecord;
        }).collect(Collectors.toList());

        VisualQc oldVisualQc = visualQcRepository.getLatestByTaskId(taskId,VisualQcTypeEnum.VISUAL);
        //已有视觉质检任务未提交过
        if(oldVisualQc!=null
                && StringUtils.isBlank(oldVisualQc.getOnShelfImages())
                && VisualTaskQcStateEnum.WAITING_QC.getCode().equals(oldVisualQc.getQcState())){
            oldVisualQc.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
            oldVisualQc.setQcRecord(JSONObject.toJSONString(visualQcRecords));
            oldVisualQc.setTaskHandlerId(handlerId);
            oldVisualQc.setTaskHandlerName(handlerName);
            oldVisualQc.setTaskHandleTime(LocalDateTime.now());
            oldVisualQc.setTaskId(taskId);
            list.add(oldVisualQc);
        }
        //创建视觉质检任务
        else{
            VisualQc visualQc = new VisualQc();
            visualQc.setQcRecordId(IdPool.getId());
            visualQc.setTaskId(taskId);
            visualQc.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
            visualQc.setQcRecord(JSONObject.toJSONString(visualQcRecords));
            visualQc.setVersionNum(oldVisualQc!=null ? oldVisualQc.getVersionNum()+1 : 1);
            visualQc.setQcState(VisualTaskQcStateEnum.WAITING_QC.getCode());
            visualQc.setQcType(VisualQcTypeEnum.VISUAL.getCode());
            visualQc.setTaskHandlerId(handlerId);
            visualQc.setTaskHandlerName(handlerName);
            visualQc.setTaskHandleTime(LocalDateTime.now());
            visualQc.setIsLatest(1);
            list.add(visualQc);
            //将旧版本任务过期掉
            if(oldVisualQc!=null){
                oldVisualQc.setIsLatest(0);
                list.add(oldVisualQc);
            }
        }

        //过期掉已有买手任务，待视觉质检提交后再判断是否需要再生成买手质检
        VisualQc oldBuyerQc = visualQcRepository.getLatestByTaskId(taskId,VisualQcTypeEnum.BUYER);
        if(oldBuyerQc!=null) {
            oldBuyerQc.setIsLatest(0);
            list.add(oldBuyerQc);
        }
        return list;
    }

    public List<ImageFile> listImageFileByOnShelfImagePackage(OnShelfImagePackage onShelfImagePackage) {
        if(onShelfImagePackage==null){
            return Collections.emptyList();
        }
        List<ImageFile> list = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(onShelfImagePackage.getSpuImages())){
            onShelfImagePackage.getSpuImages().forEach(spuImage -> {
                list.addAll(spuImage.getImages());
            });
        }
        if(CollectionUtil.isNotEmpty(onShelfImagePackage.getSkcImages())){
            onShelfImagePackage.getSkcImages().forEach(skcImage -> {
                list.addAll(skcImage.getImages());
            });
        }
        return list;
    };
}
