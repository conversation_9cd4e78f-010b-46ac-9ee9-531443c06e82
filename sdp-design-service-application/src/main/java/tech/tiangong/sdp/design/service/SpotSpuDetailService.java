package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.dto.ai.AlibabaDistributionOutputDto;
import tech.tiangong.sdp.design.vo.req.spot.SpotAiAlibabaDistributionRetryReq;
import tech.tiangong.sdp.design.vo.req.spot.SpotSpuDetailReq;

/**
 * spot_spu_detail表(SpotSpuDetail)服务接口
 *
 * <AUTHOR>
 * @since 2025-02-25 11:38:47
 */
public interface SpotSpuDetailService {

    /**
     * 创建数据
     *
     * @param req 数据实体
     */
    void create(SpotSpuDetailReq req);

    /**
     * 更新数据
     *
     * @param req 数据实体
     */
    void update(SpotSpuDetailReq req);

    void doAlibabaDistributionJob(Long id);

    void retryAiAlibabaDistributionConvertResults(SpotAiAlibabaDistributionRetryReq req);

    void aiAlibabaDistributionJob(Integer day, Integer aiAlibabaDistributionSyncStatus, Integer limit);

    void aiAlibabaDistributionRetryJob(SpotAiAlibabaDistributionRetryReq req);

    void processAiAlibabaDistributionConvertResults(String spuId, AlibabaDistributionOutputDto resultDto);
}
