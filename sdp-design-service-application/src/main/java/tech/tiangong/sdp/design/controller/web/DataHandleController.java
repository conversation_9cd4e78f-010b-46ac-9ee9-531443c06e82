package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.DesignStyleService;
import tech.tiangong.sdp.design.service.data.StyleDataServiceImpl;
import tech.tiangong.sdp.design.vo.req.PushPopReq;
import tech.tiangong.sdp.design.vo.req.StyleAttributesReq;

import javax.validation.Valid;
import java.util.Set;

/**
 * 数据处理-web
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/data")
public class DataHandleController extends BaseController {


    private final StyleDataServiceImpl styleDataService;
    private final DesignStyleService designStyleService;

    /**
     * 初始化属性
     *
     * @tags data-0911
     *
     * @param req 批量处理实体
     * @return Void
     */
    @PostMapping("/attribute")
    public DataResponse<Void> initAttribute(@RequestBody @Valid StyleAttributesReq req) {
        styleDataService.initAttribute(req);
        return DataResponse.ok();
    }


    /**
     * 设计款-根据spu更新商品属性和基础资料到pop
     *
     * @tags data-0912
     *
     * @param styleCodeSet spu编码
     */
    @PostMapping("/spu/push-pop")
    DataResponse<Void> pushAttribute2Pop(@RequestBody Set<String> styleCodeSet){
        designStyleService.pushAttribute2Pop(styleCodeSet);
        return DataResponse.ok();
    }



    /**
     * 刷数据推POP接口
     *
     * @tags 数据推送
     * @param req 推送请求参数
     * @return 推送结果
     */
    @PostMapping("/push-pop-data")
    public DataResponse<Void> pushPopData(@RequestBody @Valid PushPopReq req) {
        styleDataService.pushPopData(req);
        return DataResponse.ok();
    }
}

