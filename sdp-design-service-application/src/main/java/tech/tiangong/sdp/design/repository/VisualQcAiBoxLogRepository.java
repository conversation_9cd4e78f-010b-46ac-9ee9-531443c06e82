package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualQcAiBoxLog;
import tech.tiangong.sdp.design.mapper.VisualQcAiBoxLogMapper;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Repository
public class VisualQcAiBoxLogRepository extends BaseRepository<VisualQcAiBoxLogMapper, VisualQcAiBoxLog> {

    public List<VisualQcAiBoxLog> listByQcRecordIds(List<Long> qcRecordIdList) {
        if (CollectionUtil.isEmpty(qcRecordIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(VisualQcAiBoxLog::getQcRecordId, qcRecordIdList)
                .list();
    }

    public List<VisualQcAiBoxLog> listByTaskIds(List<Long> taskIdList) {
        if (CollectionUtil.isEmpty(taskIdList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(VisualQcAiBoxLog::getTaskId, taskIdList)
                .list();
    }
}
