package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.collection.Collections;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.MaterialSnapshot;
import tech.tiangong.sdp.design.mapper.MaterialSnapshotMapper;
import tech.tiangong.sdp.design.vo.dto.bom.SnapshotSkuDto;
import tech.tiangong.sdp.design.vo.dto.material.MaterialSnapshotInsertDto;
import tech.tiangong.sdp.design.vo.query.material.MaterialSnapshotQuery;
import tech.tiangong.sdp.utils.Bool;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

;

/**
 * 物料快照表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class MaterialSnapshotRepository extends BaseRepository<MaterialSnapshotMapper, MaterialSnapshot> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<MaterialSnapshot> findPage(MaterialSnapshotQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    /**
     * 根据skuId查询物料快照信息
     * @param skuId skuId
     * @return 物料快照集合
     */
    public List<MaterialSnapshot> listBySkuId(Long skuId) {
        if (Objects.isNull(skuId)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(MaterialSnapshot::getSkuId, skuId)
                .eq(MaterialSnapshot::getIsDeleted, Bool.NO.getCode())
                .list();
    }

    public void batchInsert(List<MaterialSnapshotInsertDto> snapshotList) {
        if (CollUtil.isEmpty(snapshotList)) {
            return;
        }
        baseMapper.batchInsert(snapshotList);
    }

    public List<Long> getAllId() {
        return baseMapper.getAllId();
    }

//    public int countOld() {
//        return lambdaQuery().eq(MaterialSnapshot::getNewMaterial, Bool.NO.getCode()).count();
//    }

    public List<MaterialSnapshot> getMaterialPictureNotNull() {
        return baseMapper.getMaterialPictureNotNull();
    }

    public void updatePictureJson(Long materialSnapshotId, String pictureJson) {
        baseMapper.updatePictureJson(materialSnapshotId, pictureJson);
    }

    /**
     * 查询供应商id为null的物料记录
     */
    public List<MaterialSnapshot> listMaterialWithoutSupplier(Integer newMaterial) {
        return lambdaQuery()
                .eq(MaterialSnapshot::getNewMaterial, newMaterial)
                .isNull(MaterialSnapshot::getSupplierId)
                .eq(MaterialSnapshot::getIsDeleted, Bool.NO.getCode())
                .list();
    }

    public void cleanOld() {
        baseMapper.cleanOld();
    }

    public List<MaterialSnapshot> listAfterDate(LocalDateTime updateBeginDate) {
        if (Objects.isNull(updateBeginDate)) {
            return List.of();
        }
        return lambdaQuery()
                .ge(MaterialSnapshot::getCreatedTime, updateBeginDate)
                .groupBy(MaterialSnapshot::getSkuId).list();
    }

    public List<MaterialSnapshot> listByIds(List<Long> materialSnapshotIds) {
        if (Collections.isEmpty(materialSnapshotIds)) {
            return new ArrayList<>();
        }
        return super.listByIds(materialSnapshotIds);
    }

    public List<SnapshotSkuDto> listSnapshotId(Integer materialType) {
        return baseMapper.listSnapshotId(materialType);
    }
}
