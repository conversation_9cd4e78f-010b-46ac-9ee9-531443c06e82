package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.cache.commands.CacheCommands;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import team.aikero.murmuration.common.req.task.AeAttributeConvertRequest;
import team.aikero.murmuration.common.req.task.AlibabaDistributionRequest;
import team.aikero.murmuration.common.req.task.SkcColorIdentificationRequest;
import team.aikero.murmuration.common.req.task.SkcInfo;
import tech.tiangong.sdp.design.config.MurmurationConfig;
import tech.tiangong.sdp.design.constant.DesignRedisConstants;
import tech.tiangong.sdp.design.entity.SpotSkc;
import tech.tiangong.sdp.design.entity.SpotSkcDetail;
import tech.tiangong.sdp.design.entity.SpotSpu;
import tech.tiangong.sdp.design.entity.SpotSpuDetail;
import tech.tiangong.sdp.design.enums.ProductCommunicationEnum;
import tech.tiangong.sdp.design.mq.rocketmq.entity.MurmurationTaskStatusEnum;
import tech.tiangong.sdp.design.remote.MurmurationRemoteHelper;
import tech.tiangong.sdp.design.repository.SpotSkcDetailRepository;
import tech.tiangong.sdp.design.repository.SpotSkcRepository;
import tech.tiangong.sdp.design.repository.SpotSpuDetailRepository;
import tech.tiangong.sdp.design.repository.SpotSpuRepository;
import tech.tiangong.sdp.design.service.SpotSpuMurmurationService;
import tech.tiangong.sdp.design.vo.resp.spot.SpotPickStyleAddResultVo;
import tech.tiangong.sdp.qy.vo.dto.SkcPicDto;
import tech.tiangong.sdp.utils.AsyncTask;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * SpotSpu Murmuration AI服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpotSpuMurmurationServiceImpl implements SpotSpuMurmurationService {
    private final MurmurationRemoteHelper murmurationRemoteHelper;
    private final MurmurationConfig murmurationConfig;
    private final SpotSpuRepository spotSpuRepository;
    private final SpotSkcRepository spotSkcRepository;
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final SpotSkcDetailRepository spotSkcDetailRepository;
    private final CacheCommands cacheCommands;
    private final AsyncTaskExecutor asyncTaskExecutor;

    @Override
    @Async(value = "asyncTaskExecutor")
    public void submitMurmurationTasksForCommunicationProducts(List<SpotPickStyleAddResultVo> results) {
        try {
            // 过滤成功的结果
            List<SpotPickStyleAddResultVo> successResults = results.stream()
                    .filter(SpotPickStyleAddResultVo::isSuccessful)
                    .toList();
            
            if (successResults.isEmpty()) {
                log.info("没有成功的选款结果");
                return;
            }
            
            // 获取所有成功结果对应的SPU
            List<String> styleCodes = successResults.stream()
                    .map(SpotPickStyleAddResultVo::getStyleCode)
                    .distinct()
                    .toList();
            
            List<SpotSpu> allSpus = spotSpuRepository.listByStyleCodes(styleCodes);
            
            // 过滤出货通商品
            List<SpotSpu> communicationSpus = allSpus.stream()
                    .filter(spu -> Objects.equals(ProductCommunicationEnum.YES.getCode(), spu.getCommunication()))
                    .toList();
            
            if (communicationSpus.isEmpty()) {
                log.info("没有需要提交AI任务的货通商品");
                return;
            }
            
            log.info("开始为{}个货通商品提交AI任务", communicationSpus.size());
            
            // 为每个SPU的三种AI任务分别异步提交
            for (SpotSpu spu : communicationSpus) {
                log.info("开始为SPU[{}]异步提交AI任务", spu.getStyleCode());
                
                // 三种任务分别异步执行，使用wrapper处理用户信息传递
                CompletableFuture.runAsync(AsyncTask.wrapper(() -> submitAeAttributeConvertTask(spu)), asyncTaskExecutor);
                CompletableFuture.runAsync(AsyncTask.wrapper(() -> submitSkcColorIdentificationTasks(spu)), asyncTaskExecutor);
                CompletableFuture.runAsync(AsyncTask.wrapper(() -> submitAlibabaDistributionTask(spu)), asyncTaskExecutor);
                
                log.info("SPU[{}]所有AI任务已异步提交", spu.getStyleCode());
            }
            
        } catch (Exception e) {
            log.error("提交Murmuration AI任务异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 统一的补偿任务内部实现
     * 
     * @param spuCodes SPU编码列表
     * @param options 任务配置选项
     */
    private void compensateSubmitTasksInternal(List<String> spuCodes, MurmurationCompensationOptions options) {
        try {
            if (CollectionUtils.isEmpty(spuCodes)) {
                log.info("SPU编码列表为空，跳过补偿提交");
                return;
            }

            log.info("开始补偿提交{}个SPU的任务，配置: 属性={}, 颜色={}, 图包={}, 异步={}", 
                    spuCodes.size(), options.enableAttributeTask(), options.enableColorTask(),
                    options.enableDistributionTask(), options.useAsync());

            // 批量获取SPU
            List<SpotSpu> spuList = spotSpuRepository.listByStyleCodes(spuCodes);
            if (spuList.isEmpty()) {
                log.warn("未找到指定的SPU记录");
                return;
            }

            // 为每个SPU提交任务
            for (SpotSpu spu : spuList) {
                log.info("开始为SPU[{}]补偿提交AI任务", spu.getStyleCode());
                
                try {
                    if (options.useAsync()) {
                        // 异步执行
                        if (options.enableAttributeTask()) {
                            CompletableFuture.runAsync(AsyncTask.wrapper(() -> submitAeAttributeConvertTask(spu, true)), asyncTaskExecutor);
                        }
                        if (options.enableColorTask()) {
                            CompletableFuture.runAsync(AsyncTask.wrapper(() -> submitSkcColorIdentificationTasks(spu, true)), asyncTaskExecutor);
                        }
                        if (options.enableDistributionTask()) {
                            CompletableFuture.runAsync(AsyncTask.wrapper(() -> submitAlibabaDistributionTask(spu, true)), asyncTaskExecutor);
                        }
                    } else {
                        // 同步执行
                        if (options.enableAttributeTask()) {
                            submitAeAttributeConvertTask(spu, true);
                        }
                        if (options.enableColorTask()) {
                            submitSkcColorIdentificationTasks(spu, true);
                        }
                        if (options.enableDistributionTask()) {
                            submitAlibabaDistributionTask(spu, true);
                        }
                    }
                } catch (Exception e) {
                    log.error("补偿提交SPU[{}]AI任务失败: {}", spu.getStyleCode(), e.getMessage(), e);
                }
                
                log.info("SPU[{}]AI任务补偿提交完成", spu.getStyleCode());
            }
            
            log.info("补偿提交任务完成，成功处理{}个SPU", spuList.size());

        } catch (Exception e) {
            log.error("补偿提交任务异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void compensateSubmitSpuAttributeTasks(List<String> spuCodes) {
        compensateSubmitTasksInternal(spuCodes, MurmurationCompensationOptions.attributeOnly());
    }

    @Override
    public void compensateSubmitSkcColorTasks(List<String> skcCodes) {
        try {
            if (CollectionUtils.isEmpty(skcCodes)) {
                log.info("SKC编码列表为空，跳过补偿提交");
                return;
            }

            log.info("开始补偿提交{}个SKC的颜色识别任务", skcCodes.size());

            // 批量获取SKC
            List<SpotSkc> skcList = spotSkcRepository.listByDesignCodes(skcCodes);
            if (skcList.isEmpty()) {
                log.warn("compensateSubmitSkcColorTasks, 未找到指定的SKC记录");
                return;
            }

            // 为每个SKC提交颜色识别任务，需要获取对应的SPU信息
            for (SpotSkc skc : skcList) {
                try {
                    SpotSpu spu = spotSpuRepository.getByStyleCode(skc.getStyleCode());
                    if (spu == null) {
                        log.warn("未找到SKC[{}]对应的SPU[{}]", skc.getDesignCode(), skc.getStyleCode());
                        continue;
                    }

                    submitSkcColorIdentificationTask(spu, skc, true);

                } catch (Exception e) {
                    log.error("补偿提交SKC[{}]颜色识别任务失败: {}", skc.getDesignCode(), e.getMessage(), e);
                }
            }

            log.info("补偿提交SKC颜色识别任务完成，成功处理{}个SKC", skcList.size());

        } catch (Exception e) {
            log.error("补偿提交SKC颜色识别任务异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void compensateSubmitAlibabaDistributionTasks(List<String> spuCodes) {
        compensateSubmitTasksInternal(spuCodes, MurmurationCompensationOptions.distributionOnly());
    }

    @Override
    public void compensateSubmitSkcColorTasksBySpu(List<String> spuCodes) {
        compensateSubmitTasksInternal(spuCodes, MurmurationCompensationOptions.colorOnly());
    }

    /**
     * 基于SPU批量补偿提交Murmuration任务
     * 
     * @param spuCodes SPU编码列表
     */
    @Override
    public void compensateSubmitMurmurationTasksBySpu(List<String> spuCodes) {
        compensateSubmitTasksInternal(spuCodes, MurmurationCompensationOptions.allTasksAsync());
    }

    /**
     * 提交AE属性转换任务
     */
    private void submitAeAttributeConvertTask(SpotSpu spu) {
        submitAeAttributeConvertTask(spu, false);
    }
    
    /**
     * 提交AE属性转换任务
     * 
     * @param spu SpotSpu对象
     * @param forceSubmit 是否强制提交，true时忽略已有taskId的检查
     */
    private void submitAeAttributeConvertTask(SpotSpu spu, boolean forceSubmit) {
        log.info("为SPU[{}]提交AE属性转换任务", spu.getStyleCode());
        String lockKey = DesignRedisConstants.getSpuMurmurationTaskLock(spu.getStyleCode());
        Lock lock = cacheCommands.getDistributedMutexLock(lockKey);
        boolean isLocked = false;
        
        try {
            // 尝试获取分布式锁，10秒超时
            isLocked = lock.tryLock(10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("获取SPU[{}]属性转换任务锁失败，跳过处理", spu.getStyleCode());
                return;
            }

            // 获取最新的SPU信息，防止数据过期
            spu = spotSpuRepository.getByStyleCode(spu.getStyleCode());
            // 再次检查是否已经有AI属性任务ID，防止重复提交
            if (!forceSubmit && spu.getAiAttributeTaskId() != null) {
                log.info("SPU[{}]已有AI属性任务ID[{}]，跳过AE属性转换任务", spu.getStyleCode(), spu.getAiAttributeTaskId());
                return;
            }
            
            // 检查是否为货通商品
            if (!Objects.equals(ProductCommunicationEnum.YES.getCode(), spu.getCommunication())) {
                log.info("SPU[{}]非货通商品，跳过AE属性转换任务", spu.getStyleCode());
                return;
            }
            
            // 获取SPU详情信息
            SpotSpuDetail spuDetail = spotSpuDetailRepository.getBySpotSpuId(spu.getSpotSpuId());
            if (spuDetail == null) {
                log.warn("未找到SPU[{}]的详情信息，跳过AE属性转换任务", spu.getStyleCode());
                return;
            }
            
            // 构造AE属性转换请求
            AeAttributeConvertRequest request = new AeAttributeConvertRequest(
                    spu.getStyleCode(), // id (spu-style-code)
                    Optional.ofNullable(spu.getSpuName()).orElse(""), // productName
                    spu.getCategoryName(), // productType (内部品类)
                    processOriginalAttribute(spuDetail.getOriginalSpuAttribute()), // productInfo (处理后的原始属性)
                    Optional.ofNullable(spuDetail.getOriginalSpuImage()).orElse(""), // masterImgUrl (从SpuDetail获取图片URL)
                    murmurationConfig.getAip().getMuseAttrXlsxUrl(), // museAttrXlsxUrl
                    murmurationConfig.getAip().getAeTemplateXlsxUrl() // aeTemplateXlsxUrl
            );
            
            log.info("提交AE属性转换任务，SPU[{}]，request={}", spu.getStyleCode(), request);
            
            Long taskId = murmurationRemoteHelper.submitAeAttributeConvertTask(spu.getStyleCode(), request);
            
            // 更新SPU的AI任务状态和任务ID
            SpotSpu updateEntity = new SpotSpu();
            updateEntity.setSpotSpuId(spu.getSpotSpuId());
            updateEntity.setAiAttributeStatus(MurmurationTaskStatusEnum.PREPARED.getCode());
            updateEntity.setAiAttributeTaskId(taskId);
            spotSpuRepository.updateById(updateEntity);
            
            log.info("为SPU[{}]提交AE属性转换任务成功，taskId={}", spu.getStyleCode(), taskId);
            
        } catch (Exception e) {
            log.error("为SPU[{}]提交AE属性转换任务失败: {}", spu.getStyleCode(), e.getMessage(), e);
            // 更新为失败状态
            try {
                SpotSpu updateEntity = new SpotSpu();
                updateEntity.setSpotSpuId(spu.getSpotSpuId());
                updateEntity.setAiAttributeStatus(MurmurationTaskStatusEnum.FAILED.getCode()); // FAILED状态
                spotSpuRepository.updateById(updateEntity);
            } catch (Exception updateEx) {
                log.error("更新SPU[{}]AI任务失败状态异常: {}", spu.getStyleCode(), updateEx.getMessage());
            }
        } finally {
            // 释放锁
            if (isLocked) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.warn("释放SPU[{}]属性转换任务锁失败: {}", spu.getStyleCode(), e.getMessage());
                }
            }
        }
    }

    /**
     * 为所有SKC提交颜色识别任务（串行处理）
     */
    private void submitSkcColorIdentificationTasks(SpotSpu spu) {
            submitSkcColorIdentificationTasks(spu, false);
    }
    /**
     * 为所有SKC提交颜色识别任务（串行处理）
     * @param forceSubmit 是否强制提交，true时忽略已有taskId的检查
     */
    private void submitSkcColorIdentificationTasks(SpotSpu spu, boolean forceSubmit) {
        log.info("开始为SPU[{}]提交SKC颜色识别任务", spu.getStyleCode());
        // 检查是否为货通商品
        if (!Objects.equals(ProductCommunicationEnum.YES.getCode(), spu.getCommunication())) {
            log.info("SPU[{}]非货通商品，跳过SKC颜色识别任务", spu.getStyleCode());
            return;
        }
        
        // 获取该SPU下的所有SKC
        List<SpotSkc> skcList = spotSkcRepository.listByStyleCode(spu.getStyleCode());
        if (skcList.isEmpty()) {
            log.warn("SPU[{}]下没有找到SKC，跳过颜色识别任务", spu.getStyleCode());
            return;
        }
        
        log.info("开始为SPU[{}]的{}个SKC提交颜色识别任务", spu.getStyleCode(), skcList.size());
        
        // 串行处理所有SKC的颜色识别任务
        for (SpotSkc skc : skcList) {
            try {
                submitSkcColorIdentificationTask(spu, skc, forceSubmit);
            } catch (Exception e) {
                log.error("为SKC[{}]提交颜色识别任务失败: {}", skc.getDesignCode(), e.getMessage(), e);
            }
        }
        
        log.info("SPU[{}]所有SKC颜色识别任务提交完成", spu.getStyleCode());
    }

    /**
     * 提交单个SKC颜色识别任务（含SKC级别分布式锁和双重校验）
     * 
     * @param spu SpotSpu对象
     * @param skc SpotSkc对象
     * @param forceSubmit 是否强制提交，true时忽略已有taskId的检查
     */
    private void submitSkcColorIdentificationTask(SpotSpu spu, SpotSkc skc, boolean forceSubmit) {
        log.info("为SPU[{}], SKC[{}]提交颜色识别任务", spu.getStyleCode(), skc.getDesignCode());
        String lockKey = DesignRedisConstants.getSkcColorIdentificationTaskLock(skc.getDesignCode());
        Lock lock = cacheCommands.getDistributedMutexLock(lockKey);
        boolean isLocked = false;
        
        try {
            // 尝试获取SKC级别的分布式锁，5秒超时
            isLocked = lock.tryLock(5, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("获取为SPU[{}], SKC[{}]颜色识别任务锁失败，跳过处理", spu.getStyleCode(), skc.getDesignCode());
                return;
            }

            // 获取最新的SKC信息，防止数据过期
            String designCode = skc.getDesignCode();
            skc = spotSkcRepository.getByDesignCode(designCode);
            if (skc == null) {
                log.warn("未找到SPU[{}], SKC[{}]，跳过颜色识别任务", spu.getStyleCode(), designCode);
                return;
            }
            
            // 双重校验：检查是否已经有AI颜色识别任务ID，防止重复提交
            if (!forceSubmit && skc.getAiColorTaskId() != null) {
                log.info("SPU[{}], SKC[{}]已有AI颜色识别任务ID[{}]，跳过处理", spu.getStyleCode(), skc.getDesignCode(), skc.getAiColorTaskId());
                return;
            }

            // 获取SKC详情信息以获取图片列表
            SpotSkcDetail skcDetail = spotSkcDetailRepository.getBySpotSkcId(skc.getSpotSkcId());
            if (skcDetail == null) {
                log.warn("SPU[{}], 未找到SKC[{}]的详情信息，跳过颜色识别任务", spu.getStyleCode(), skc.getDesignCode());
                return;
            }
            
            // 验证SKC图片列表（假设图片存储在skcDetail中，具体字段需要根据实际情况调整）
            String skcMainImageUrl = getSkcMainImageUrl(skcDetail);
            if (skcMainImageUrl == null || skcMainImageUrl.trim().isEmpty()) {
                log.warn("SPU[{}], SKC[{}]主图为空，跳过颜色识别任务", spu.getStyleCode(), skc.getDesignCode());
                return;
            }
            
            // 构造SKC颜色识别请求
            SkcColorIdentificationRequest request = new SkcColorIdentificationRequest(
                    skcMainImageUrl, // url (SKC商品主图)
                    spu.getCategoryName(), // category (内部品类)
                    skcMainImageUrl, // outputUrl (同url)
                    spu.getStyleCode(), // spuId (spu-style-code)
                    skc.getDesignCode(), // skcId (skc-design-code)
                    skc.getOriginalColor(), // SKC原始颜色,1688颜色名
                    skcDetail.getOriginalSampleSize() // 1688尺码
            );
            
            log.info("提交SKC颜色识别任务，SPU[{}], SKC[designCode={}]，request={}", spu.getStyleCode(), skc.getDesignCode(), request);
            
            Long taskId = murmurationRemoteHelper.submitSkcColorIdentificationTask(skc.getDesignCode(), request);
            
            // 更新SKC的AI任务状态和任务ID
            SpotSkc updateEntity = new SpotSkc();
            updateEntity.setSpotSkcId(skc.getSpotSkcId());
            updateEntity.setAiColorStatus(MurmurationTaskStatusEnum.PREPARED.getCode()); // WAITING状态
            updateEntity.setAiColorTaskId(taskId);
            spotSkcRepository.updateById(updateEntity);
            
            log.info("为SPU[{}], SKC[designCode={}]提交颜色识别任务成功，taskId={}", spu.getStyleCode(), skc.getDesignCode(), taskId);
            
        } catch (Exception e) {
            log.error("为SPU[{}],SKC[designCode={}]提交颜色识别任务失败: {}", spu.getStyleCode(), skc.getDesignCode(), e.getMessage(), e);
            // 更新为失败状态
            try {
                SpotSkc updateEntity = new SpotSkc();
                updateEntity.setSpotSkcId(skc.getSpotSkcId());
                updateEntity.setAiColorStatus(MurmurationTaskStatusEnum.FAILED.getCode()); // FAILED状态
                spotSkcRepository.updateById(updateEntity);
            } catch (Exception updateEx) {
                log.error("更新SPU[{}],SKC[designCode={}]AI任务失败状态异常: {}", spu.getStyleCode(), skc.getDesignCode(), updateEx.getMessage());
            }
        } finally {
            // 释放SKC级别的锁
            if (isLocked) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.warn("释放SPU[{}],SKC[{}]颜色识别任务锁失败: {}", spu.getStyleCode(), skc.getDesignCode(), e.getMessage());
                }
            }
        }
    }
    
    /**
     * 获取SKC主图URL
     */
    private String getSkcMainImageUrl(SpotSkcDetail skcDetail) {
        if (skcDetail.getProductPictureList() != null && !skcDetail.getProductPictureList().isEmpty()) {
            return skcDetail.getProductPictureList().getFirst();
        }
        return null;
    }
    
    /**
     * 处理原始属性格式转换
     * 原始格式：JSON数组，需要按attrName分组，同组attrValue用逗号拼接，不同组用中文分号拼接
     * 
     * @param originalAttribute 原始属性JSON字符串
     * @return 处理后的属性字符串
     */
    private String processOriginalAttribute(String originalAttribute) {
        if (originalAttribute == null || originalAttribute.trim().isEmpty()) {
            return "";
        }
        
        try {
            // 解析JSON数组
            JSONArray jsonArray = JSON.parseArray(originalAttribute);
            if (jsonArray.isEmpty()) {
                return "";
            }
            
            // 按attrName分组
            Map<String, List<String>> groupedAttributes = new LinkedHashMap<>();
            
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject attribute = jsonArray.getJSONObject(i);
                if (attribute == null) continue;
                
                String attrName = attribute.getString("attrName");
                String attrValue = attribute.getString("attrValue");
                
                if (StringUtils.isNoneBlank(attrName, attrValue)) {
                    groupedAttributes.computeIfAbsent(attrName.trim(), k -> new ArrayList<>()).add(attrValue.trim());
                }
            }
            
            // 构建结果字符串：同组值用逗号拼接，不同组用中文分号拼接
            return groupedAttributes.entrySet().stream()
                    .map(entry -> entry.getKey() + "-" + String.join(",", entry.getValue()))
                    .collect(Collectors.joining("；"));
                    
        } catch (Exception e) {
            log.error("处理原始属性JSON格式异常: {}, originalAttribute={}", e.getMessage(), originalAttribute, e);
            return ""; // JSON解析失败时返回空字符串
        }
    }

    // 货通发起图包拉取（含分布式锁和双重校验）
    @Override
    public void submitAlibabaDistributionTask(SpotSpu spu) {
        submitAlibabaDistributionTask(spu, false);
    }
    
    /**
     * 货通发起图包拉取（含分布式锁和双重校验）
     * 
     * @param spu SpotSpu对象
     * @param forceSubmit 是否强制提交，true时忽略已有taskId的检查
     */
    private void submitAlibabaDistributionTask(SpotSpu spu, boolean forceSubmit) {
        log.info("开始为SPU[{}]提交图包拉取任务，forceSubmit={}", spu.getStyleCode(), forceSubmit);
        String styleCode = spu.getStyleCode(); // 提前获取styleCode，用于日志
        String lockKey = DesignRedisConstants.getSpuAlibabaDistributionTaskLock(styleCode);
        Lock lock = cacheCommands.getDistributedMutexLock(lockKey);
        boolean isLocked = false;
        
        try {
            // 尝试获取分布式锁，10秒超时
            isLocked = lock.tryLock(10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("获取SPU[{}]图包拉取任务锁失败，跳过处理", styleCode);
                return;
            }

            // 获取最新的SPU信息，防止数据过期
            spu = spotSpuRepository.getByStyleCode(styleCode);
            if (spu == null) {
                log.warn("未找到SPU[{}]，跳过图包拉取任务", styleCode);
                return;
            }
            
            // 检查是否为货通商品
            if (spu.getCommunication() == null || ProductCommunicationEnum.NO.getCode().equals(spu.getCommunication())) {
                log.info("SPU[{}]非货通商品，跳过图包拉取任务", spu.getStyleCode());
                return;
            }
            
            // 获取SpuDetail并检查是否已有图包拉取任务ID
            SpotSpuDetail spuDetail = spotSpuDetailRepository.getBySpotSpuId(spu.getSpotSpuId());
            if (spuDetail == null) {
                log.warn("未找到SPU[{}]的详情信息，跳过图包拉取任务", spu.getStyleCode());
                return;
            }
            
            // 双重校验：检查是否已经有图包拉取任务ID，防止重复提交
            if (!forceSubmit && spuDetail.getAiAlibabaDistributionTaskId() != null) {
                log.info("SPU[{}]已有图包拉取任务ID[{}]，跳过处理", spu.getStyleCode(), spuDetail.getAiAlibabaDistributionTaskId());
                return;
            }
            
            // 获取SKC图片信息
            List<SkcPicDto> skcInfoList = spotSkcRepository.findSkcPicList(List.of(spu.getStyleCode()));
            if(CollectionUtil.isEmpty(skcInfoList)){
                log.warn("SPU[{}]未找到SKC图片记录，跳过图包拉取任务", spu.getStyleCode());
                return;
            }
            
            // 提交获取图包任务
            doSubmitAlibabaDistributionTask(spu, spuDetail, skcInfoList);

        } catch (Exception e) {
            log.error("SPU[{}]图包拉取任务失败: {}", styleCode, e.getMessage(), e);
        } finally {
            // 释放锁
            if (isLocked) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.warn("释放SPU[{}]图包拉取任务锁失败: {}", styleCode, e.getMessage());
                }
            }
        }
    }

    private void doSubmitAlibabaDistributionTask(SpotSpu spu, SpotSpuDetail spuDetail, List<SkcPicDto> skcInfoList) {
        // 组装请求数据
        List<String> spuCarouselUrls = new ArrayList<>();
        if(StrUtil.isNotBlank(spuDetail.getOriginalSpuCarouselUrls())){
            spuCarouselUrls = JSONUtil.parseArray(spuDetail.getOriginalSpuCarouselUrls()).toList(String.class);
        }
        if(StrUtil.isBlank(spuDetail.getOriginalSpuImage())){
            log.warn("SPU图包[{}]拉取任务失败: 原始SPU图片为空", spu.getStyleCode());
            return;
        }
        List<String> spuDetails = new ArrayList<>();
        if(StrUtil.isNotBlank(spuDetail.getOriginalProductDetailPicList())){
            spuDetails = JSONUtil.parseArray(spuDetail.getOriginalProductDetailPicList()).toList(String.class);
        }
        if(CollUtil.isEmpty(spuDetails)){
            log.warn("spuDetail.getOriginalProductDetailPicList() is empty");
            return;
        }
        List<SkcInfo> skcList = new ArrayList<>();
        for(SkcPicDto skcInfo: skcInfoList){
            if(StrUtil.isBlank(skcInfo.getProductPictureList()) || CollectionUtil.isEmpty(JSONUtil.parseArray(skcInfo.getProductPictureList()).toList(String.class))){
                log.warn("未找到指定的SKC图片记录:{}",skcInfo.getDesignCode());
                continue;
            }
            List<String> skcPictureList = JSONUtil.parseArray(skcInfo.getProductPictureList()).toList(String.class);
            SkcInfo skc = new SkcInfo(skcPictureList.getFirst(),skcInfo.getDesignCode());
            skcList.add(skc);
        }

        // 校验都不为空再提交
        if(CollectionUtil.isNotEmpty(spuCarouselUrls)&&
                CollectionUtil.isNotEmpty(spuDetails)&&
                CollectionUtil.isNotEmpty(skcList)){
            // 轮播图最多取6张
            var carouselUrlsReq = spuCarouselUrls.size() > 6 ? spuCarouselUrls.subList(0, 6) : spuCarouselUrls;
            // 详情图最多36张
            var detailUrlsReq = spuDetails.size() > 36 ? spuDetails.subList(0, 36) : spuDetails;
            // skc最多10个
            var skcListReq = skcList.size() > 10 ? skcList.subList(0, 10) : skcList;
            AlibabaDistributionRequest request = new AlibabaDistributionRequest(spuDetail.getOriginalSpuImage(), carouselUrlsReq, detailUrlsReq, skcListReq);
            Long taskId = murmurationRemoteHelper.submitAlibabaDistributionTask(spu.getSpotSpuId().toString(),request);
            // 更新taskId
            spotSpuDetailRepository.update(new LambdaUpdateWrapper<SpotSpuDetail>()
                    .eq(SpotSpuDetail::getSpotSpuId, spu.getSpotSpuId())
                    .set(SpotSpuDetail::getAiAlibabaDistributionStatus, MurmurationTaskStatusEnum.PREPARED.getCode())
                    .set(SpotSpuDetail::getAiAlibabaDistributionTaskId, taskId));
            log.info("SPU图包[{}]拉取任务提交成功，taskId={}", spu.getStyleCode(), taskId);
        }else{
            log.warn("SPU图包[{}]拉取任务参数不完整", spu.getStyleCode());
        }

    }

    /**
     * Murmuration任务补偿配置选项
     *
     * @param enableAttributeTask    是否执行属性转换任务
     * @param enableColorTask        是否执行颜色识别任务
     * @param enableDistributionTask 是否执行图包拉取任务
     * @param useAsync               是否异步执行
     */
    private record MurmurationCompensationOptions(boolean enableAttributeTask, boolean enableColorTask,
                                                  boolean enableDistributionTask, boolean useAsync) {
        public static MurmurationCompensationOptions attributeOnly() {
            return new MurmurationCompensationOptions(true, false, false, false);
        }
        public static MurmurationCompensationOptions colorOnly() {
            return new MurmurationCompensationOptions(false, true, false, false);
        }
        public static MurmurationCompensationOptions distributionOnly() {
            return new MurmurationCompensationOptions(false, false, true, false);
        }
        public static MurmurationCompensationOptions allTasksSync() {
            return new MurmurationCompensationOptions(true, true, true, false);
        }
        public static MurmurationCompensationOptions allTasksAsync() {
            return new MurmurationCompensationOptions(true, true, true, true);
        }
    }
}