package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.BomCommonService;
import tech.tiangong.sdp.design.vo.req.DesignCodeReq;
import tech.tiangong.sdp.design.vo.req.bom_common.*;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderVo;
import tech.tiangong.sdp.design.vo.resp.bom_common.BomDetailVo;

import java.util.List;

/**
 * BOM单-公共接口-inner
 *
 *  提供给非PLM系统调用
 * <AUTHOR>
 * @date 2023/5/31 14:57
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/bom-common")
public class BomCommonInnerController {

    private final BomCommonService bomCommonService;

    /**
     * 根据设计款号-批量查询最新已提交的Bom信息
     *
     * @param req 入参
     * @return List<BomOrderVo>
     */
    @PostMapping("/bom-order/latest")
    DataResponse<List<BomOrderVo>> getLatestBomOrder(@Validated @RequestBody DesignCodeReq req) {
        return DataResponse.ok(bomCommonService.getLatestBomOrder(req));
    }

    /**
     * 根据BomID查询Bom详情
     *
     * @param req 入参
     * @return BomDetailVo
     */
    @PostMapping("/detail")
    DataResponse<BomDetailVo> getBomOrderDetail(@Validated @RequestBody BomDetailQuery req) {
        return DataResponse.ok(bomCommonService.getBomDetailById(req));
    }

    /**
     * 根据BomID-批量查询Bom详情
     *
     * @param req 入参
     * @return List<BomDetailVo>
     */
    @PostMapping("/list-detail")
    DataResponse<List<BomDetailVo>> listBomDetailById(@Validated @RequestBody BomDetailBatchQuery req) {
        return DataResponse.ok(bomCommonService.listBomDetailById(req));
    }

    /**
     * 根据设计款号-查询最新已提交的Bom详情
     *
     * @param req 入参
     * @return BomDetailVo
     */
    @PostMapping("/detail/skc-latest")
    DataResponse<BomDetailVo> getLatestBomDetailBySkc(@Validated @RequestBody LatestBomDetailQuery req) {
        return DataResponse.ok(bomCommonService.getLatestBomDetailBySkc(req));
    }

    /**
     * 根据设计款号-批量查询最新已提交的Bom详情
     *
     * @param req 入参
     * @return List<BomDetailVo>
     */
    @PostMapping("/batch-detail/skc-latest")
    DataResponse<List<BomDetailVo>> listLatestBomDetailBySkc(@Validated @RequestBody LatestBomDetailBatchQuery req) {
        return DataResponse.ok(bomCommonService.listLatestBomDetailBySkc(req));
    }

    /**
     * 查询设计款号下所有版本的Bom单
     *
     * @param req 入参
     * @return List<BomOrderVo>
     */
    @PostMapping("/list-skc-bom")
    DataResponse<List<BomOrderVo>> listSkcBom(@Validated @RequestBody SkcBomQuery req) {
        return DataResponse.ok(bomCommonService.listSkcBom(req));
    }


}
