package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.DimensionGleanTaskFollow;
import tech.tiangong.sdp.design.mapper.DimensionGleanTaskFollowMapper;
import tech.tiangong.sdp.design.vo.dimensiongleantask.DimensionGleanTaskPageQuery;
import tech.tiangong.sdp.design.vo.dto.dimensiongleantask.DimensionGleanTaskStateCountDto;
import tech.tiangong.sdp.design.vo.resp.dimensiongleantask.DimensionGleanTaskListVo;

import java.util.List;

/**
 * 3D采集任务跟踪（对应中台的3D采集任务）dimension_glean_task表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class DimensionGleanTaskFollowRepository extends BaseRepository<DimensionGleanTaskFollowMapper, DimensionGleanTaskFollow> {

    public List<DimensionGleanTaskStateCountDto> countByGleanState(){
        return baseMapper.countByGleanState();
    }

    public IPage<DimensionGleanTaskListVo> queryByPage(DimensionGleanTaskPageQuery query){
        return baseMapper.queryByPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public DimensionGleanTaskFollow getByGleanTaskAndSku(Long gleanTaskId, String skuCode){
        if(gleanTaskId==null || StringUtils.isBlank(skuCode)){
            return null;
        }
        return getOne(new LambdaQueryWrapper<DimensionGleanTaskFollow>()
                .eq(DimensionGleanTaskFollow::getGleanId,gleanTaskId)
                .eq(DimensionGleanTaskFollow::getSkuCode,skuCode),false);
    }

    public DimensionGleanTaskFollow getByGleanTaskCodeAndSku(String gleanTaskCode, String skuCode){
        if(StringUtils.isBlank(gleanTaskCode) || StringUtils.isBlank(skuCode)){
            return null;
        }
        return getOne(new LambdaQueryWrapper<DimensionGleanTaskFollow>()
                .eq(DimensionGleanTaskFollow::getGleanCode,gleanTaskCode)
                .eq(DimensionGleanTaskFollow::getSkuCode,skuCode),false);
    }
}
