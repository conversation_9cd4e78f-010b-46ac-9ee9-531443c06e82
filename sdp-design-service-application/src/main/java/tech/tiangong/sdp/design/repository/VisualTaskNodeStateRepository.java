package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.VisualTaskNodeState;
import tech.tiangong.sdp.design.enums.visual.VisualTaskNodeEnum;
import tech.tiangong.sdp.design.enums.visual.VisualTaskStepEnum;
import tech.tiangong.sdp.design.mapper.VisualTaskNodeStateMapper;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视觉任务处理环节节点状态流转服务仓库类
 *
 */
@Repository
public class VisualTaskNodeStateRepository extends BaseRepository<VisualTaskNodeStateMapper, VisualTaskNodeState> {

    public void deleteNodeStates(Long visualTaskId,
                                 VisualTaskStepEnum visualTaskStep,
                                List<VisualTaskNodeEnum> visualTaskNodes){
        this.remove(new LambdaUpdateWrapper<VisualTaskNodeState>()
                .eq(VisualTaskNodeState::getTaskId,visualTaskId)
                .eq(VisualTaskNodeState::getProcessStep, visualTaskStep.getCode())
                .in(VisualTaskNodeState::getProcessNode,visualTaskNodes.stream().map(VisualTaskNodeEnum::getCode).collect(Collectors.toList())));
    }

    public void deleteStep(List<Long> visualTaskIds,
                                 VisualTaskStepEnum visualTaskStep){
        this.remove(new LambdaUpdateWrapper<VisualTaskNodeState>()
                .in(VisualTaskNodeState::getTaskId,visualTaskIds)
                .eq(VisualTaskNodeState::getProcessStep, visualTaskStep.getCode()));
    }

    public void deleteNodeState(Long visualTaskId,
                                VisualTaskStepEnum visualTaskStep,
                                VisualTaskNodeEnum visualTaskNode){
        this.update(new LambdaUpdateWrapper<VisualTaskNodeState>()
                .eq(VisualTaskNodeState::getTaskId,visualTaskId)
                .eq(VisualTaskNodeState::getProcessStep, visualTaskStep.getCode())
                .eq(VisualTaskNodeState::getProcessNode,visualTaskNode.getCode())
                .set(VisualTaskNodeState::getIsDeleted, Bool.YES.getCode()));
    }

    public void changeNodeState(Long visualTaskId,
                                VisualTaskStepEnum visualTaskStep,
                                VisualTaskNodeEnum visualTaskNode,
                                Integer nodeState){
        VisualTaskNodeState visualTaskNodeState = getOne(new LambdaQueryWrapper<VisualTaskNodeState>()
                .eq(VisualTaskNodeState::getTaskId,visualTaskId)
                    .eq(VisualTaskNodeState::getProcessStep,visualTaskStep.getCode())
                    .eq(VisualTaskNodeState::getProcessNode,visualTaskNode.getCode())
        ,false);
        if(visualTaskNodeState==null){
            visualTaskNodeState = VisualTaskNodeState.builder()
                    .nodeStateId(IdPool.getId())
                    .taskId(visualTaskId)
                    .processStep(visualTaskStep.getCode())
                    .processNode(visualTaskNode.getCode())
                    .processNodeState(nodeState)
                    .remark(visualTaskNode.getDesc()+":"+visualTaskNode.getStateEnum().getDescByCode(nodeState))
                    .build();
        }else{
            visualTaskNodeState.setProcessNodeState(nodeState);
            visualTaskNodeState.setRemark(visualTaskNode.getDesc()+":"+visualTaskNode.getStateEnum().getDescByCode(nodeState));
        }
        this.saveOrUpdate(visualTaskNodeState);
    }

    public List<VisualTaskNodeState> listByVisualTaskIds(Collection<Long> visualTaskIds){
        if(CollectionUtils.isEmpty(visualTaskIds)){
            return Collections.emptyList();
        }
        return this.list(new LambdaQueryWrapper<VisualTaskNodeState>()
                .in(VisualTaskNodeState::getTaskId,visualTaskIds)
                .eq(VisualTaskNodeState::getIsDeleted, Bool.NO.getCode()));
    }

    public VisualTaskNodeState getNodeState(Long visualTaskId,
                                            VisualTaskStepEnum visualTaskStep,
                                            VisualTaskNodeEnum visualTaskNode){
        if(visualTaskId==null || visualTaskStep==null || visualTaskNode==null){
            return null;
        }
        return getOne(new LambdaQueryWrapper<VisualTaskNodeState>()
                        .eq(VisualTaskNodeState::getTaskId,visualTaskId)
                        .eq(VisualTaskNodeState::getProcessStep,visualTaskStep.getCode())
                        .eq(VisualTaskNodeState::getProcessNode,visualTaskNode.getCode())
                ,false);
    }

    public VisualTaskNodeState getLatestNodeState(Long visualTaskId,VisualTaskStepEnum visualTaskStep){
        if(visualTaskId==null || visualTaskStep==null){
            return null;
        }
        List<VisualTaskNodeState> list =  this.list(new LambdaQueryWrapper<VisualTaskNodeState>()
                .eq(VisualTaskNodeState::getTaskId,visualTaskId)
                .eq(VisualTaskNodeState::getProcessStep,visualTaskStep.getCode())
                .eq(VisualTaskNodeState::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualTaskNodeState::getCreatedTime));
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        VisualTaskNodeState nodeState = list.get(0);
        return nodeState;
    }

    /**
     * 查找指定环节的node
     */
    public  VisualTaskNodeState getNodeByProcessNode(Long visualTaskId, VisualTaskNodeEnum visualTaskNode) {
        if(visualTaskId==null || visualTaskNode==null){
            return null;
        }
        List<VisualTaskNodeState> list = this.list(new LambdaQueryWrapper<VisualTaskNodeState>()
                .eq(VisualTaskNodeState::getTaskId, visualTaskId)
                .eq(VisualTaskNodeState::getProcessNode, visualTaskNode.getCode())
                .eq(VisualTaskNodeState::getIsDeleted, Bool.NO.getCode()));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public List<VisualTaskNodeState> listByStepWithVisualTaskIds(List<VisualTaskStepEnum> visualTaskSteps,Collection<Long> visualTaskIds){
        if(CollectionUtils.isEmpty(visualTaskIds) || CollectionUtils.isEmpty(visualTaskSteps)){
            return Collections.emptyList();
        }
        return this.list(new LambdaQueryWrapper<VisualTaskNodeState>()
                .in(VisualTaskNodeState::getTaskId,visualTaskIds)
                .in(VisualTaskNodeState::getProcessStep,visualTaskSteps.stream().map(VisualTaskStepEnum::getCode).toArray())
                .eq(VisualTaskNodeState::getIsDeleted, Bool.NO.getCode()));
    }

    public List<VisualTaskNodeState> listByNodeWithVisualTaskIds(List<VisualTaskNodeEnum> visualTaskNodes,Collection<Long> visualTaskIds){
        if(CollectionUtils.isEmpty(visualTaskIds) || CollectionUtils.isEmpty(visualTaskNodes)){
            return Collections.emptyList();
        }
        return this.list(new LambdaQueryWrapper<VisualTaskNodeState>()
                .in(VisualTaskNodeState::getTaskId,visualTaskIds)
                .in(VisualTaskNodeState::getProcessNode,visualTaskNodes.stream().map(VisualTaskNodeEnum::getCode).toArray())
                .eq(VisualTaskNodeState::getIsDeleted, Bool.NO.getCode()));
    }
}