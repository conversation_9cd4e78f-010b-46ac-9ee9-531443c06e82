package tech.tiangong.sdp.design.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import tech.tiangong.pop.common.dto.CreateProductDto;
import tech.tiangong.pop.common.dto.Image;
import tech.tiangong.pop.common.dto.ImageLabelInfoDto;
import tech.tiangong.pop.common.dto.ProductAttributesV2Dto;
import tech.tiangong.sdp.design.entity.DigitalPrintingPrototype;
import tech.tiangong.sdp.design.entity.DigitalPrintingStyle;
import tech.tiangong.sdp.design.entity.DigitalPrintingStyleDetail;
import tech.tiangong.sdp.design.enums.DigitalPrintingTypeEnum;
import tech.tiangong.sdp.design.enums.DpSourceTypeEnum;
import tech.tiangong.sdp.design.enums.SdpStyleTypeEnum;
import tech.tiangong.sdp.design.vo.base.AttributeVo;
import tech.tiangong.sdp.design.vo.dto.ImageDTO;
import tech.tiangong.sdp.design.vo.dto.LabelInfoDTO;
import tech.tiangong.sdp.design.vo.req.digital.DigitalPrintingStyleAddReq;
import tech.tiangong.sdp.design.vo.req.digital.TemplateSizeDetailReq;
import tech.tiangong.sdp.design.vo.resp.digital.DigitalPrintingDetailVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@UtilityClass
public class DigitalPrintConverter {


    public CreateProductDto convertProductCreateReq(DigitalPrintingStyle printingStyle, DigitalPrintingStyleDetail styleDetail, List<DigitalPrintingPrototype> skcInfoList) {
        CreateProductDto productReq = new CreateProductDto();
        productReq.setStyleType(SdpStyleTypeEnum.DIGITAL_PRINTING.getCode());
        productReq.setSourceBizId(printingStyle.getSourceBizId());
        productReq.setInspiraSourceId(styleDetail.getInspireSourceId());
        productReq.setPrototypeNum(printingStyle.getModelNumber());
        productReq.setSelectStyleId(printingStyle.getChosenId());
        productReq.setSelectStyleName(printingStyle.getChosenName());
        productReq.setSelectStyleTime(printingStyle.getChosenTime());
        //0904取消默认 根据选择来传
        // productReq.setSupplyMode(DictConstant.SUPPLY_MODE_DIGITAL_PRINTING);
        productReq.setMarketCode(printingStyle.getMarketCode());
        productReq.setMarketSeriesCode(printingStyle.getMarketSeriesCode());
        productReq.setWaves(printingStyle.getWaveBandName());
        productReq.setPlanningType(printingStyle.getPlanningType());
        productReq.setClothingStyleCode(printingStyle.getClothingStyleCode());
        productReq.setClothingStyleName(printingStyle.getClothingStyleName());
        productReq.setProductThemeCode(printingStyle.getProductThemeCode());
        productReq.setProductThemeName(printingStyle.getProductThemeName());
        //主图只要1张
        List<ImageDTO> styleImageList = styleDetail.getStyleImageList();
        if (CollUtil.isNotEmpty(styleImageList)) {
            productReq.setMainImgUrl(styleImageList.getFirst().getOssImageUrl());
        }
        productReq.setSpuCode(printingStyle.getStyleCode());
        productReq.setProductTitle("");
        productReq.setCategoryCode(printingStyle.getCategory());
        productReq.setCategoryName(printingStyle.getCategoryName());
        productReq.setSizeGroupName(printingStyle.getSizeStandard());
        productReq.setSizeGroupCode(printingStyle.getSizeStandardCode());
        productReq.setImageFlowerUrls(convertImage(styleDetail.getImageFlowerUrls()));
        productReq.setAttributesList(convertAttribute(styleDetail));
        //skc数据
        productReq.setDataList(convertSkc(printingStyle, skcInfoList,styleImageList));

        List<LabelInfoDTO> subjectLabel = styleDetail.getSubjectLabel();
        if (CollectionUtil.isNotEmpty(subjectLabel)){
            List<String> titleList = subjectLabel.stream().map(LabelInfoDTO::getEn).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            productReq.setTitleList(titleList);
            //图片标签信息集合
            List<ImageLabelInfoDto> labelInfoDtoList = subjectLabel.stream().map(item -> {
                ImageLabelInfoDto infoDto = new ImageLabelInfoDto();
                infoDto.setEn(item.getEn());
                infoDto.setCn(item.getCn());
                return infoDto;
            }).toList();
            productReq.setImageLabelInfoList(labelInfoDtoList);
        }
        //印花类型: 1-logo印(默认); 2-满印(暂未使用); 取选款的渠道来源 styleSource: 10-CAD贴图(满印) 20-自动化贴图(logo印)
        Integer styleSource = printingStyle.getStyleSource();
        if (Objects.isNull(styleSource) || Objects.equals(DpSourceTypeEnum.AUTO.getCode(), styleSource)) {
            productReq.setPrintType(DigitalPrintingTypeEnum.LOGO_PRINT.getCode());
        } else if (Objects.equals(DpSourceTypeEnum.CAD.getCode(), styleSource)) {
            productReq.setPrintType(DigitalPrintingTypeEnum.FULL_PRINT.getCode());
        }
        //生产资料信息
        productReq.setMaterialImageList(convertImage(styleDetail.getProductFileList()));

        //0904
        productReq.setStyleSeason(printingStyle.getStyleSeason());
        productReq.setStyleElementCode(printingStyle.getElementCode());
        productReq.setStyleElementName(printingStyle.getElementName());
        productReq.setQualityLevel(printingStyle.getQualityLevel());
        productReq.setQualityLevelCode(printingStyle.getQualityLevelCode());
        productReq.setSupplyMode(printingStyle.getSupplyModeCode());


        if(StringUtils.isNotBlank(styleDetail.getAttributes())){
            String attributes = styleDetail.getAttributes();
            List<AttributeVo> attributeVos = JSON.parseArray(attributes, AttributeVo.class);

            // 转换为 ProductAttributesV2 列表，以 values 维度转换
            List<ProductAttributesV2Dto> productAttributesV2List = attributeVos.stream()
                    .filter(attributeVo -> attributeVo.getValues() != null) // 过滤掉没有值的属性
                    .flatMap(attributeVo -> attributeVo.getValues().stream()
                            .map(value -> {
                                ProductAttributesV2Dto productAttributesV2 = new ProductAttributesV2Dto();
                                productAttributesV2.setAttributeId(attributeVo.getAttributeId());
                                productAttributesV2.setCategoryId(attributeVo.getCategoryId());
                                productAttributesV2.setAttributeValueId(value.getAttributeValueId());
                                productAttributesV2.setAttributeValue(value.getAttributeValue());
                                return productAttributesV2;
                            }))
                    .collect(Collectors.toList());

            productReq.setAttributesV2List(productAttributesV2List);
        }


        if(StringUtils.isNotBlank(styleDetail.getSizeDetail())){
            String sizeDetail = styleDetail.getSizeDetail();
            List<TemplateSizeDetailReq> templateSizeDetailReqs = JSON.parseArray(sizeDetail, TemplateSizeDetailReq.class);

            // 转换为 ProductSizeDetail 列表
            List<CreateProductDto.ProductSizeDetail> productSizeDetails = templateSizeDetailReqs.stream()
                    .map(templateReq -> {
                        CreateProductDto.ProductSizeDetail productSizeDetail = new CreateProductDto.ProductSizeDetail();
                        productSizeDetail.setPartName(templateReq.getPartName());

                        // 转换 SizeData 列表到 ProductSizeJson 列表
                        if (templateReq.getSizeList() != null) {
                            List<CreateProductDto.ProductSizeJson> productSizeJsons = templateReq.getSizeList().stream()
                                    .map(sizeData -> {
                                        CreateProductDto.ProductSizeJson productSizeJson = new CreateProductDto.ProductSizeJson();
                                        productSizeJson.setSize(sizeData.getSize());
                                        // 将 BigDecimal 转换为 String
                                        productSizeJson.setData(sizeData.getData() != null ? sizeData.getData().toString() : null);
                                        return productSizeJson;
                                    })
                                    .collect(Collectors.toList());
                            productSizeDetail.setSizeJson(productSizeJsons);
                        }

                        return productSizeDetail;
                    })
                    .collect(Collectors.toList());

            productReq.setSizeDetails(productSizeDetails);
        }

        return productReq;
    }

    private List<Image> convertImage(List<ImageDTO> images) {
        if (CollectionUtil.isEmpty(images)) {
            return List.of();
        }
        return images.stream().map(it -> {
            Image image = new Image();
            image.setOrgImgName(it.getOrgImgName());
            image.setOssImageUrl(it.getOssImageUrl());
            return image;
        }).collect(Collectors.toList());
    }

    private List<CreateProductDto.Attributes> convertAttribute(DigitalPrintingStyleDetail styleDetail) {
        String fabricInfo = styleDetail.getFabricInfo();
        if (StrUtil.isBlank(fabricInfo)) {
            return List.of();
        }
        DigitalPrintingDetailVo.FabricInfo fabric = JSONUtil.toBean(fabricInfo, DigitalPrintingDetailVo.FabricInfo.class);
        List<DigitalPrintingStyleAddReq.Attributes> attributesList = fabric.getAttributesList();
        if (CollectionUtil.isEmpty(attributesList)) {
            return List.of();
        }
        return attributesList.stream().map(attr -> {
            CreateProductDto.Attributes attributes = new CreateProductDto.Attributes();
            attributes.setAttributeName("面料成分");
            attributes.setAttributeValue(attr.getAttributeName());
            return attributes;
        }).collect(Collectors.toList());
    }

    private List<CreateProductDto.Skc> convertSkc(DigitalPrintingStyle printingStyle,
                                                  List<DigitalPrintingPrototype> skcInfoList, List<ImageDTO> styleImageList) {
        if (CollectionUtil.isEmpty(skcInfoList)) {
            return List.of();
        }
        return skcInfoList.stream().map(skc -> {
            CreateProductDto.Skc skcInfo = new CreateProductDto.Skc();
            skcInfo.setSkc(skc.getDesignCode());
            skcInfo.setColor(skc.getColorName());
            String colorCode = skc.getColorCode();
            skcInfo.setColorCode(colorCode);
            skcInfo.setColorAbbrCode(skc.getColorAbbrCode());
            List<String> pictures = styleImageList.stream().filter(imageDTO -> {
                String orgImgName = imageDTO.getOrgImgName();
                return orgImgName.contains(colorCode);
            }).map(ImageDTO::getOssImageUrl).collect(Collectors.toList());
            skcInfo.setPictures(pictures);

            if (StrUtil.isNotBlank(printingStyle.getCrossBorderPrice())) {
                skcInfo.setCbPrice(new BigDecimal(printingStyle.getCrossBorderPrice()));
            }
            if (StrUtil.isNotBlank(printingStyle.getLocalPrice())) {
                skcInfo.setLocalPrice(new BigDecimal(printingStyle.getLocalPrice()));
            }
            String sizeStandardValue = printingStyle.getSizeStandardValue();
            if (StrUtil.isNotBlank(sizeStandardValue)) {
                List<String> sizes = JSON.parseArray(sizeStandardValue, String.class);
                List<CreateProductDto.Sku> skuList = sizes.stream().map(value -> {
                    CreateProductDto.Sku sku = new CreateProductDto.Sku();
                    sku.setSizeName(value);
                    return sku;
                }).collect(Collectors.toList());
                skcInfo.setSkuList(skuList);
            }
            return skcInfo;
        }).toList();
    }
}
