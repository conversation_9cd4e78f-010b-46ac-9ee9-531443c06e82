package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.entity.SpotSpu;
import tech.tiangong.sdp.design.vo.resp.spot.SpotPickStyleAddResultVo;

import java.util.List;

/**
 * SpotSpu Murmuration AI服务接口
 * 
 * <AUTHOR>
 */
public interface SpotSpuMurmurationService {
    
    /**
     * 为货通商品提交Murmuration任务
     * 
     * @param results 选款结果列表
     */
    void submitMurmurationTasksForCommunicationProducts(List<SpotPickStyleAddResultVo> results);
    
    /**
     * 补偿提交SPU属性转换任务
     * 
     * @param spuCodes SPU编码列表
     */
    void compensateSubmitSpuAttributeTasks(List<String> spuCodes);
    
    /**
     * 补偿提交SKC颜色识别任务
     * 
     * @param skcCodes SKC编码列表
     */
    void compensateSubmitSkcColorTasks(List<String> skcCodes);
    
    /**
     * 基于SPU补偿提交SKC颜色识别任务
     * 
     * @param spuCodes SPU编码列表，将为每个SPU下的所有SKC提交颜色识别任务
     */
    void compensateSubmitSkcColorTasksBySpu(List<String> spuCodes);
    
    /**
     * 补偿提交Alibaba图包拉取任务
     * 
     * @param spuCodes SPU编码列表
     */
    void compensateSubmitAlibabaDistributionTasks(List<String> spuCodes);
    
    /**
     * 基于SPU批量补偿提交Murmuration任务（属性转换+颜色识别+图包拉取）
     * 
     * @param spuCodes SPU编码列表
     */
    void compensateSubmitMurmurationTasksBySpu(List<String> spuCodes);

    // 货通发起图包拉取
    void submitAlibabaDistributionTask(SpotSpu spu);
}