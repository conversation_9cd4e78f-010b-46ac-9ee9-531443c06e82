package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.CheckCountMaterialService;
import tech.tiangong.sdp.design.vo.req.bom.CheckCountMaterialOperateReq;
import tech.tiangong.sdp.design.vo.resp.bom.CheckCountMaterialOperateVo;

/**
 * sdp特辅-inner
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/sdp-material")
public class CheckCountMaterialInnerController {

	private final CheckCountMaterialService sdpSpecialMaterialService;


	/**
	 * 核算环节操作物料
	 *
	 * @param req 请求
	 * @return 响应结果
	 */
	@PostMapping("/check-count/operate")
	@NoRepeatSubmitLock(lockTime = 10L)
	DataResponse<CheckCountMaterialOperateVo> operateMaterial(@Validated @RequestBody CheckCountMaterialOperateReq req) {
		return DataResponse.ok(sdpSpecialMaterialService.operateMaterial(req));
	}


}
