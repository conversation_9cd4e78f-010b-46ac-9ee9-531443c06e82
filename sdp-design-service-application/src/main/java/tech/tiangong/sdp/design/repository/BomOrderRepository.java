package tech.tiangong.sdp.design.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.enums.BomOrderStateEnum;
import tech.tiangong.sdp.design.mapper.BomOrderMapper;
import tech.tiangong.sdp.design.vo.dto.bom.BomOrderListDto;
import tech.tiangong.sdp.design.vo.dto.bom.BomOrderStateStatisticsDto;
import tech.tiangong.sdp.design.vo.query.bom.BomOrderListQuery;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 干什么
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/13 14:59
 */
@Repository
public class BomOrderRepository extends BaseRepository<BomOrderMapper, BomOrder> {

	/**
	 * 根据设计款ID查询最新的Bom订单
	 * @param prototypeId 设计款ID
	 * @return
	 */
	public BomOrder getLatestByPrototypeId(Long prototypeId) {
		return baseMapper.selectOne(new QueryWrapper<BomOrder>().lambda().eq(BomOrder::getPrototypeId, prototypeId)
				.orderByDesc(BomOrder::getCreatedTime).last("limit 1"));
	}

	/**
	 * 获取最新的Bom单号
	 * @return
	 */
	public String selectLatestBomCode() {
		BomOrder bomOrder = baseMapper.selectOne(new QueryWrapper<BomOrder>().lambda().select(BomOrder::getBomCode)
				.orderByDesc(BomOrder::getBomCode).last("limit 1"));
		return Optional.ofNullable(bomOrder).map(BomOrder::getBomCode).orElse("");
	}

	public List<BomOrderListDto> pageQuery(BomOrderListQuery query) {
		return baseMapper.pageQuery(query);

	}

	public BomOrder getEntityByBomId(Long bomId) {
		return baseMapper.selectOne(new QueryWrapper<BomOrder>().lambda().eq(BomOrder::getBomId, bomId));
	}

	public List<BomOrder> getListByPrototypeId(Long prototypeId) {
		return baseMapper.selectList(new QueryWrapper<BomOrder>().lambda().eq(BomOrder::getPrototypeId,prototypeId)
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode()));
	}

	public List<BomOrder> getListByPrototypeIdAndState(Long prototypeId, BomOrderStateEnum bomOrderStateEnum) {
		return baseMapper.selectList(new QueryWrapper<BomOrder>().lambda().eq(BomOrder::getPrototypeId, prototypeId)
				.eq(BomOrder::getState, bomOrderStateEnum.getCode())
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode()));
	}

	public List<BomOrder> getListByPrototypeIds(List<Long> prototypeIdList) {
		return baseMapper.selectList(new QueryWrapper<BomOrder>().lambda().in(BomOrder::getPrototypeId,prototypeIdList)
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode()));
	}

	public List<BomOrder> getListByDesignCodes(List<String> designCodeList) {
		return baseMapper.selectList(new QueryWrapper<BomOrder>().lambda().in(BomOrder::getDesignCode,designCodeList)
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode()));
	}

	public List<BomOrderStateStatisticsDto> bomStateStatistics(Long designerId,List<String> designerGroupNameList) {
		List<BomOrderStateStatisticsDto> bomOrderStateStatisticsDtos = baseMapper.bomStateStatistics(designerId, designerGroupNameList);
		//暂存状态的数量
		/*
		Integer bomTransientStateQuantity = baseMapper.bomTransientStateStatistics(designerId);
		BomOrderStateStatisticsDto stateStatisticsDto = new BomOrderStateStatisticsDto();
		stateStatisticsDto.setBomOrderState(BomOrderStateEnum.IS_TRANSIENT.getCode());
		stateStatisticsDto.setQuantity(bomTransientStateQuantity);
		bomOrderStateStatisticsDtos.add(stateStatisticsDto);
		 */

		return bomOrderStateStatisticsDtos;
	}

	public List<BomOrder> getListByBomCode(String bomCode) {
		return baseMapper.selectList(new QueryWrapper<BomOrder>().lambda().eq(BomOrder::getBomCode, bomCode)
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode()));
	}

	public List<BomOrder> getListByBomCodeList(List<String> bomCodeList) {
		return baseMapper.selectList(new QueryWrapper<BomOrder>().lambda().in(BomOrder::getBomCode, bomCodeList)
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode()));
	}

	public List<BomOrder> getListByDesignCodeAndState(String designCode, BomOrderStateEnum stateEnum) {
		return baseMapper.selectList(new QueryWrapper<BomOrder>().lambda().in(BomOrder::getDesignCode, designCode)
				.eq(BomOrder::getState, stateEnum.getCode())
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode()));
	}

	/**
	 * 根据设计款号获取最新的Bom
	 * @param designCode
	 * @return
	 */
	public BomOrder getLatestByDesignCode(String designCode) {
		return baseMapper.selectOne(new QueryWrapper<BomOrder>().lambda().eq(BomOrder::getDesignCode, designCode)
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode())
				.orderByDesc(BomOrder::getVersionNum).last("limit 1"));
	}

	/**
	 * 根据设计款号获取最新已提交后的Bom-过滤找料中的
	 * @param designCode
	 * @return
	 */
	public BomOrder getLatestSubmitBomByDesignCode(String designCode) {
		List<Integer> submitStateList = this.getBomSubmitStateList();
		return baseMapper.selectOne(new QueryWrapper<BomOrder>().lambda().eq(BomOrder::getDesignCode, designCode)
				.in(BomOrder::getState, submitStateList)
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode())
				.eq(BomOrder::getMaterialSearchState, Bool.NO.getCode())
				//.orderByDesc(BomOrder::getCreatedTime).last("limit 1"));
				.orderByDesc(BomOrder::getVersionNum).last("limit 1"));
	}

	/**
	 * 根据设计款号获取最新已提交后的Bom
	 * @param designCode
	 * @return
	 */
	public BomOrder getLatestSubmitBom(String designCode) {
		List<Integer> submitStateList = this.getBomSubmitStateList();
		return baseMapper.selectOne(new QueryWrapper<BomOrder>().lambda().eq(BomOrder::getDesignCode, designCode)
				.in(BomOrder::getState, submitStateList)
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode())
				.orderByDesc(BomOrder::getVersionNum).last("limit 1"));
	}

	/**
	 * 根据设计款号获取已提交的bom版本列表
	 * @param designCode
	 * @return
	 */
	public List<BomOrder> getSubmittedBomVersionList(String designCode) {
		List<Integer> submitStateList = this.getBomSubmitStateList();
		return lambdaQuery()
				.eq(BomOrder::getDesignCode, designCode)
				.in(BomOrder::getState, submitStateList)
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode())
				.orderByDesc(BomOrder::getVersionNum)
				.list();
	}

	public List<BomOrder> listByDesignCode(String designCode, Boolean noSearch) {
		// List<Integer> submitStateList = this.getBomSubmitStateList();
		return lambdaQuery()
				.eq(BomOrder::getDesignCode, designCode)
				.eq(noSearch, BomOrder::getMaterialSearchState, Bool.NO.getCode())
				.eq(BomOrder::getIsDeleted, Bool.NO.getCode())
				.orderByDesc(BomOrder::getVersionNum)
				.list();
	}

	private List<Integer> getBomSubmitStateList() {
		return BomOrderStateEnum.submitStateList().stream().map(BomOrderStateEnum::getCode).collect(Collectors.toList());
	}

	/**
	 * 根据设计款号获取最新的Bom
	 * @param designCode
	 * @return
	 */
	public BomOrder getLatestBomByDesignCode(String designCode) {
		return baseMapper.selectOne(new QueryWrapper<BomOrder>().lambda().eq(BomOrder::getDesignCode, designCode)
				.orderByDesc(BomOrder::getVersionNum).last("limit 1"));
	}


	public List<BomOrder> getLatestBomOrderList(List<String> designCodeList, List<Integer> stateList, boolean noSearch) {
		if (CollectionUtil.isEmpty(designCodeList)) {
			return Collections.emptyList();
		}
		List<BomOrder> latestBomOrderList = baseMapper.listLatestBomOrder(designCodeList, stateList, noSearch);
		/*
		//这个方法因底层 cn.yibuyun.id.IdPool.getId 算法有问题,创建时间最大的,ID并不是最大的
		// List<BomOrder> latestBomOrderList = baseMapper.getLatestBomOrderList(designCodeList, stateList);
		List<BomOrder> latestBomOrderList = designCodeList.parallelStream().map(designCode ->
				baseMapper.selectOne(new QueryWrapper<BomOrder>().lambda()
						.eq(BomOrder::getDesignCode, designCode)
						.in(BomOrder::getState, stateList)
						.eq(noSearch, BomOrder::getMaterialSearchState, Bool.NO.getCode())
						.orderByDesc(BomOrder::getVersionNum).last("limit 1")))
				.collect(Collectors.toList());
		 */
		return latestBomOrderList.stream().filter(Objects::nonNull).collect(Collectors.toList());
	}

	public List<String> getAllCommitedDesignCode() {
		return baseMapper.getAllCommitedDesignCode();
	}

	public void updateNoTransient(Long bomId) {
		BomOrder updateBomOrder = BomOrder.builder().bomId(bomId)
				.transientState(Bool.NO.getCode())
				.build();
		baseMapper.updateById(updateBomOrder);
	}

	/**
	 * 根据设计款号查询最新已提交bom单
	 */
	public List<BomOrder> listLatestSubmitBomOrder(List<String> designCodeList, Boolean noSearch) {
		if (CollUtil.isEmpty(designCodeList)) {
			return Collections.emptyList();
		}
		List<Integer> submitStateList = this.getBomSubmitStateList();
		return baseMapper.listLatestBomOrder(designCodeList, submitStateList, noSearch);
	}
}

