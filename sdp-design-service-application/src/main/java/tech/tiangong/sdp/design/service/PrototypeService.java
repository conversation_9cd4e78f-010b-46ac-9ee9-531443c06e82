package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.clothes.vo.dto.CheckPriceExecuteProcessMqDto;
import tech.tiangong.sdp.design.entity.DesignStyleVersion;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.enums.DesignStyleSourceTypeEnum;
import tech.tiangong.sdp.design.vo.base.prototype.SalesChannelUpdate;
import tech.tiangong.sdp.design.vo.dto.prototype.CheckPriceProcessMqDto;
import tech.tiangong.sdp.design.vo.dto.style.DesignStyleUpdateDto;
import tech.tiangong.sdp.design.vo.req.manage.PrototypeCancelReq;
import tech.tiangong.sdp.design.vo.req.prototype.BatchPrintReq;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeCreateReq;
import tech.tiangong.sdp.design.vo.req.prototype.inner.*;
import tech.tiangong.sdp.design.vo.resp.DesignerVo;
import tech.tiangong.sdp.design.vo.resp.SkcTagVO;
import tech.tiangong.sdp.design.vo.resp.prototype.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 版单-主表服务接口
 *
 * <AUTHOR>
 * @since 2021-08-09 14:43:17
 */
public interface PrototypeService {

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 数据实体
     */
    PrototypeVo getById(Long id);

    /**
     * 批量生产版单
     *
     * @param prototypeCreateReqList
     * @return 创建结果
     */
    void batchCreating(List<PrototypeCreateReq> prototypeCreateReqList);

    /**
     * 创建正常打版的SKC(SPU创建后调用)
     *
     * @param designStyleVersion SPU对象
     * @param sourceType         款式来源
     * @param designPicture 设计图
     * @return 正常打版skc信息
     */
    NormalSkcCreateResp normalSkcCreate(DesignStyleVersion designStyleVersion, DesignStyleSourceTypeEnum sourceType, String designPicture);

    /**
     * 批量创建正常打版的SKC
     *
     * @param designStyleVersionList SPU对象
     * @param sourceType             款式来源
     * @param stylePictureMap spu图片Map
     * @return 正常打版skc信息
     */
    List<NormalSkcCreateResp> normalSkcBatchCreate(List<DesignStyleVersion> designStyleVersionList, DesignStyleSourceTypeEnum sourceType, Map<String, String> stylePictureMap);

    /**
     * 复色
     * <p>
     * 业务校验逻辑调用方处理, 当前接口只负责生成数据
     *
     * @param normalPrototypeId 需要复色打版的版单id
     * @return 复色打版的版单id
     */
    Long colorsMakingCreate(Long normalPrototypeId);

    /**
     * 获取已提交版本的详情(看不到临时保存的版本)
     *
     * @param designCode
     * @return
     */
    PrototypeVo getSavedInfoByDesignCode(String designCode);

    /**
     * 查询该设计款号是否存在
     *
     * @param designCode 设计款号（精确）
     * @return 相应结果 ture-存在 false-不存在
     */
    Boolean exists(String designCode);

    /**
     * 根据版单id查询已拆版详情
     *
     * @param prototypeId 版单id
     * @return 响应结果
     */
    PrototypeVo getDoneDetailById(Long prototypeId);

    /**
     * 根据designCode查询所有已提交的版单基础信息
     *
     * @param designCode 设计款号
     * @return 响应结果
     */
    List<PrototypeVo> getVersionList(String designCode);

    /**
     * 根据prototypeId获取已提交的版单详情
     *
     * @param prototypeId 版单id
     * @return 版单详情(包含物料信息)
     */
    PrototypeVo getSavedInfoByPrototypeId(Long prototypeId);

    /**
     * 批量查询设计款号-获取已提交版本的详情(看不到临时保存的版本)
     *
     * @param req
     * @return
     */
    List<PrototypeVo> getSavedInfoListByDesignCode(PrototypeListInnerReq req);

    /**
     * 根据id批量查询设计款号
     *
     * @param req
     * @return
     */
    List<PrototypeVo> getListByPrototypeId(PrototypeListByIdInnerReq req);

    /**
     * 获取复色款号的详情
     *
     * @param designCode
     * @return
     */
    PrototypeMakeSameVo getMakeSameInfoByDesignCode(String designCode);


    /**
     * 设计款管理-取消设计款
     *
     * @param prototypeId
     * @param cancelReq
     */
    void cancelPrototype(Long prototypeId, PrototypeCancelReq cancelReq, String currentUserBbCode);

    /**
     * 批量查询设计版单打印信息
     *
     * @param req 入参
     * @return 版单打印信息集合
     */
    List<PrototypePrintInfoVo> batchPrintInfo(BatchPrintReq req);

    // /**
    //  * 通过设计款号获取打印信息(批量)
    //  *
    //  * @param req 参数
    //  * @return 响应结果
    //  */
    // PrototypePrintInfoBatchVo getPrintInfoByDesignCodeBatch(PrototypePrintBatchInnerReq req);

    /**
     * 根据设计款号更新核价状态
     *
     * @param req 请求参数
     */
    void updateCheckPriceState(PrototypeCheckPriceInnerReq req);

    /**
     * 根据spu款式号查询该款式号下的设计款是否存在已完成的情况
     *
     * @param req 请求参数
     * @return 响应结果
     */
    List<PrototypeVo> selectDemandDone(PrototypeDemandListReq req);

    /**
     * 根据当前设计款号查找可用的复色款号
     *
     * @param designCode 设计款号
     */
    List<MakeSameDesignCodeVo> queryMakeSameByDesignCode(String designCode);

    /**
     * 数据迁移
     * <p>
     * 采购信息关联版单信息表
     *
     * @return void
     */
    void prototypeToPurchasePrototype();

    /**
     * 获取设计师信息
     *
     * @param userId 用户id（设计师id）
     * @return DesignerVo
     */
    DesignerVo getDesignerInfo(Long userId);

    List<PrototypeSplicingInfoVo> queryIsSplicingByDesignCodes(PrototypeSplicingBatchInnerReq req);

    /**
     * 根据spuCode更新所有prototype表与prototype_history表中SPU维度的信息
     *
     * @param updateDto spu更新dto
     */
    void updateSpuInfoWithinHistory(DesignStyleUpdateDto updateDto);

    /**
     * 同步核价状态
     *
     * @param mqDto 核价信息
     */
    void syncCheckPriceProcess(CheckPriceExecuteProcessMqDto mqDto);

    /**
     * 改款关联skc信息查询-查skc下最新已提交的版单信息
     *
     * @param designCode 设计款号
     * @return 改款关联skc信息
     */
    PrototypeChangeInfoVo getSkcChangeInfo(String designCode);

    /**
     * 根据spu查询skc
     *
     * @param styleCode 设计款号
     * @return skc列表
     */
    List<String> getSkcListBySpu(String styleCode);

    /**
     * 以map结构获取prototype
     */
    Map<String, SkcTagVO> mapSkcTagByDesignCodes(Set<String> designCodes);

    /**
     * 获取skc标签内容
     *
     * @param designCodes skc
     * @return skc标签内容
     */
    Map<String, SkcTagVO> mapSkcTags(Set<String> designCodes);

    // /**
    //  * 根据颜色编码-批量查询对应设计款id集合
    //  *
    //  * @param req 入参
    //  * @return List<SkcColorVo>
    //  */
    // List<SkcColorVo> getSkcColor(SkcColorReq req);

    // /**
    //  * 根据颜色编码-批量查询对应设计款是否存在
    //  *
    //  * @param req 入参
    //  * @return List<String> 元素为颜色编码; 返回有关联款式的颜色编码集合;
    //  */
    // List<String> checkSkcColor(SkcColorReq req);


    /**
     * 根据设计款号 + 设计师id 查询 设计数量
     *
     * @param designCode 设计款号
     * @param designerId 设计师id
     * @return 数量
     */
    Integer countByDesignCodeAndDesignerId(String designCode, Long designerId);


    /**
     * 批量更新
     * @param updates
     * @return
     */
    int batchUpdateWithMyBatisPlus(List<SalesChannelUpdate> updates);

}