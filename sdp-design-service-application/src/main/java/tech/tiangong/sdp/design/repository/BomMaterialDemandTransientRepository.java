package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.BomMaterialDemandTransient;
import tech.tiangong.sdp.design.mapper.BomMaterialDemandTransientMapper;
import tech.tiangong.sdp.design.vo.query.bom.BomMaterialDemandTransientQuery;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialDemandTransientVo;

import java.util.List;
import java.util.Objects;

/**
 * bom物料需求_暂存表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class BomMaterialDemandTransientRepository extends BaseRepository<BomMaterialDemandTransientMapper, BomMaterialDemandTransient> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<BomMaterialDemandTransientVo> findPage(BomMaterialDemandTransientQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    public List<BomMaterialDemandTransient> listByBomTransientId(Long bomTransientId) {
        if (Objects.isNull(bomTransientId)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(BomMaterialDemandTransient::getBomTransientId, bomTransientId)
                .list();
    }

    /**
     * 根据暂存bomId与原需求id查询暂存需求
     */
    public BomMaterialDemandTransient getByBomTransientIdAndOriginDemandId(Long bomTransientId, Long originDemandId) {
        if (Objects.isNull(bomTransientId) || Objects.isNull(originDemandId)) {
            return null;
        }
        return lambdaQuery()
                .eq(BomMaterialDemandTransient::getBomTransientId, bomTransientId)
                .eq(BomMaterialDemandTransient::getOriginDemandId, originDemandId)
                .last("limit 1")
                .one();
    }
}
