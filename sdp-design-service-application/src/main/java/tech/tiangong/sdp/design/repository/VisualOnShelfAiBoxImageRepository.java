package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualOnShelfAiBoxImage;
import tech.tiangong.sdp.design.mapper.VisualOnShelfAiBoxImageMapper;

import java.util.List;

@Repository
public class VisualOnShelfAiBoxImageRepository extends BaseRepository<VisualOnShelfAiBoxImageMapper, VisualOnShelfAiBoxImage> {

    public List<VisualOnShelfAiBoxImage> listByTaskId(Long taskId) {
        return list(new LambdaQueryWrapper<VisualOnShelfAiBoxImage>()
                .eq(VisualOnShelfAiBoxImage::getTaskId, taskId));
    }

    public List<VisualOnShelfAiBoxImage> listByTaskIdList(List<Long> taskIdList) {
        return list(new LambdaQueryWrapper<VisualOnShelfAiBoxImage>()
                .in(VisualOnShelfAiBoxImage::getTaskId, taskIdList));
    }
}
