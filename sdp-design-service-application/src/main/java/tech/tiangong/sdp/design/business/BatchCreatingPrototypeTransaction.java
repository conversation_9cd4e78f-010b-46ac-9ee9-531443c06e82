package tech.tiangong.sdp.design.business;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.extra.validation.BeanValidationResult;
import cn.hutool.extra.validation.ValidationUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.entity.PrototypeDetail;
import tech.tiangong.sdp.design.entity.PrototypeHistory;
import tech.tiangong.sdp.design.enums.DemandTaskTypeEnum;
import tech.tiangong.sdp.design.enums.PrototypeStatusEnum;
import tech.tiangong.sdp.design.enums.SampleTypeEnum;
import tech.tiangong.sdp.design.enums.SkcTypeEnum;
import tech.tiangong.sdp.design.helper.StyleLibraryHelper;
import tech.tiangong.sdp.design.remote.DesignerRemoteHelper;
import tech.tiangong.sdp.design.repository.PrototypeDetailRepository;
import tech.tiangong.sdp.design.repository.PrototypeHistoryRepository;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeCreateReq;
import tech.tiangong.sdp.material.pojo.dto.DesignerDTO;
import tech.tiangong.sdp.material.pojo.web.request.designer.DesignerRemoteReq;
import tech.tiangong.sdp.utils.AsyncTask;
import tech.tiangong.sdp.utils.CodeRuleEnum;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2021/8/28
 */
@Slf4j
public class BatchCreatingPrototypeTransaction implements Transaction {

    private List<PrototypeCreateReq> prototypeCreateReqList;

    private PrototypeRepository prototypeRepository;
    private PrototypeDetailRepository prototypeDetailRepository;
    private PrototypeHistoryRepository prototypeHistoryRepository;
    private DesignerRemoteHelper designerRemoteHelper;
    private StyleLibraryHelper styleLibraryHelper;

    public BatchCreatingPrototypeTransaction(List<PrototypeCreateReq> prototypeCreateReqList) {
        this.prototypeCreateReqList = prototypeCreateReqList;
    }



    protected void create() {
        for (PrototypeCreateReq req : prototypeCreateReqList) {
            log.info("新增skc={}", req.getDesignCode());
            Prototype prototype = new Prototype();
            BeanUtils.copyProperties(req, prototype);
            long prototypeId = IdPool.getId();
            prototype.setPrototypeId(prototypeId);
            //第一个版本是1
            prototype.setVersionNum(1);
            prototype.setLatestVersionNum(1);
            prototype.setLatestPrototypeId(prototypeId);
            // prototype.setProcessingStep(ProcessingStepEnum.PROTOTYPE.getCode());
            prototype.setPrototypeStatus(PrototypeStatusEnum.WAIT_DECOMPOSE.getCode());
            prototype.setIsDoneVersion(Boolean.FALSE);
            prototype.setMakeMoreLatestTime(LocalDateTime.now());
            // prototype.setFakeId(createFakeId(req.getDesignCode()));



            String deliveryPeriod = req.getDeliveryPeriod();
            //有可能是小数
            LocalDateTime now = LocalDateTime.now();
            // prototype.setDeliveryTime(calDeliveryTime(now, deliveryPeriod));
            prototype.setIsDoneVersion(Boolean.FALSE);
            prototype.setSkcCreatedTime(now);

            //设置设计师
            setDesigner(prototype, req);
            //如果是复色打版，需清空颜色
            if (SkcTypeEnum.COMPOUND_COLORS.getCode().equals(prototype.getSkcType())){
                prototype.setColor("");
            }

            PrototypeDetail prototypeDetail = new PrototypeDetail();
            BeanUtils.copyProperties(req, prototypeDetail);
            prototypeDetail.setPrototypeId(prototypeId);
            // prototypeDetail.setCustomerPicture(StrUtil.join(StringPool.COMMA, req.getCustomerPicture()));
            prototypeDetail.setPrototypeDetailId(IdPool.getId());

            //存到历史表
            PrototypeHistory prototypeHistory = new PrototypeHistory();
            BeanUtils.copyProperties(prototype, prototypeHistory);

            Assert.isTrue(prototypeRepository.save(prototype), "保存新增设计款失败");
            Assert.isTrue(prototypeDetailRepository.save(prototypeDetail), "保存新增设计款详细信息失败");
            Assert.isTrue(prototypeHistoryRepository.save(prototypeHistory), "保存新增设计款历史失败");

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    CompletableFuture.runAsync(AsyncTask.wrapper(()->{
                        //请求向量创建
                        styleLibraryHelper.prototypeStyleLibrarySaveOrUpdate(prototype,req.getCategoryName(),prototypeDetail,1);
                    }));
                }
            });

            log.info("完成新增skc={} prototypeId={}", prototype.getDesignCode(), prototypeId);
        }
    }

    /**
     * 创建fakeId
     * @param designCode
     * @return
     */
    private Long createFakeId(String designCode){
        String fakeIdStr = StrUtil.removePrefixIgnoreCase(designCode, "skc");
        fakeIdStr = StrUtil.removePrefixIgnoreCase(fakeIdStr, CodeRuleEnum.CLOTHING_SKC_CODE_JV.getCode());
        return Long.valueOf(fakeIdStr);
    }

    private void setDesigner(Prototype prototype, PrototypeCreateReq req) {
        try {
            DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
            designerRemoteReq.setDesignerId(String.valueOf(req.getDesignerId()));
            DesignerDTO designerDTO = designerRemoteHelper.getByDesignerId(designerRemoteReq);
            if (Objects.nonNull(designerDTO)) {
                prototype.setDesignerId(req.getDesignerId());
                prototype.setDesignerCode(designerDTO.getDesignerCode());
                prototype.setDesignerName(designerDTO.getDesignerName());
                prototype.setDesignerGroup(designerDTO.getDesignerGroupName());
                prototype.setDesignerGroupCode(designerDTO.getDesignerGroupCode());
            } else {
                log.warn("新增skc={} 不存在此设计师id={} code={} name={}", req.getDesignCode(), req.getDesignerId()
                        , req.getDesignerCode(), req.getDesignerName());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    protected void validate() {
        log.info("批量创建版单。data={}", JSONUtil.toJsonStr(prototypeCreateReqList));
        for (PrototypeCreateReq req : prototypeCreateReqList) {
            BeanValidationResult validationResult = ValidationUtil.warpValidate(req);
            Assert.isTrue(validationResult.isSuccess(), JSONUtil.toJsonStr(validationResult.getErrorMessages()));

            //判断deliveryPeriod 是数字格式
            Assert.isTrue(NumberUtil.isNumber(req.getDeliveryPeriod()), "{}不是数值！请输入正确的数值"
                    ,req.getDeliveryPeriod());
            Prototype prototype = prototypeRepository.getByDesignCode(req.getDesignCode());
            Assert.isNull(prototype, "已存在设计款号！");
            Assert.notNull(DemandTaskTypeEnum.findByCode(req.getDemandTaskType()), "订单类型不对");
            Assert.notNull(SampleTypeEnum.findByCode(req.getSampleType()), "打版类型不对");
        }
    }


    protected void initBean() {
        prototypeRepository = SpringUtil.getBean(PrototypeRepository.class);
        prototypeDetailRepository = SpringUtil.getBean(PrototypeDetailRepository.class);
        prototypeHistoryRepository = SpringUtil.getBean(PrototypeHistoryRepository.class);
        designerRemoteHelper = SpringUtil.getBean(DesignerRemoteHelper.class);
    }


    @Override
    public void execute() {
        initBean();
        validate();
        create();
    }
}
