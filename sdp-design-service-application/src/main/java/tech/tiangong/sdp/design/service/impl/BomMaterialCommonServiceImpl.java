package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.util.Json;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yibuyun.scm.common.dto.accessories.response.*;
import com.yibuyun.scm.common.dto.fabric.response.CommoditySkuCollectionRespVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierExtVo;
import com.yibuyun.scm.common.dto.supplier.response.CommoditySupplierInfoVo;
import com.yibuyun.scm.common.enums.fabric.DefaultSalesRegionEnum;
import com.zjkj.scf.bundle.common.dto.demand.dto.request.MaterialDetailReq;
import com.zjkj.scf.bundle.common.dto.demand.dto.response.CraftDemandMatchDetailDto;
import com.zjkj.scf.bundle.common.dto.demand.enums.BusinessTypeEnum;
import com.zjkj.scf.bundle.common.dto.demand.enums.DemandChannelEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.BomOrderConverter;
import tech.tiangong.sdp.design.converter.BomOrderMaterialConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.CraftDemandStateEnum;
import tech.tiangong.sdp.design.enums.MaterialDemandTypeEnum;
import tech.tiangong.sdp.design.enums.SpecialAccessoriesStateEnum;
import tech.tiangong.sdp.design.enums.SpecialHandleTypeEnum;
import tech.tiangong.sdp.design.helper.PrepareBomCraftCycleHelper;
import tech.tiangong.sdp.design.remote.DemandRemoteHelper;
import tech.tiangong.sdp.design.remote.ProductRemoteHelper;
import tech.tiangong.sdp.design.remote.SupplierInfoRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.BomMaterialCommonService;
import tech.tiangong.sdp.design.service.MaterialSnapshotService;
import tech.tiangong.sdp.design.vo.dto.bom.BomCopyInfoDto;
import tech.tiangong.sdp.design.vo.dto.bom.BomMaterialInfoDto;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderUpdateV3Req;
import tech.tiangong.sdp.design.vo.req.bom.CraftDemandSaveV3Req;
import tech.tiangong.sdp.design.vo.req.material.MaterialSnapshotCreateReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;
import tech.tiangong.sdp.design.vo.resp.material.MaterialSnapshotVo;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * bom物料 公共处理接口
 * <AUTHOR>
 * @date 2022/11/19 15:06
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class BomMaterialCommonServiceImpl implements BomMaterialCommonService {

    private final ProductRemoteHelper productRemoteHelper;
    private final MaterialSnapshotService materialSnapshotService;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final BomOrderTransientRepository bomOrderTransientRepository;
    private final BomMaterialDemandRepository bomMaterialDemandRepository;
    private final BomOrderMaterialTransientRepository bomOrderMaterialTransientRepository;
    private final BomMaterialDemandTransientRepository bomMaterialDemandTransientRepository;
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final PrototypeRepository prototypeRepository;
    private final SupplierInfoRemoteHelper supplierInfoRemoteHelper;
    private final DemandRemoteHelper demandRemoteHelper;
    private final SpecialAccessoriesRepository specialAccessoriesRepository;
    private final SpecialAccessoriesTransientRepository specialAccessoriesTransientRepository;
    private final DesignRemarksRepository designRemarksRepository;
    private final CraftDemandInfoRepository craftDemandInfoRepository;
    private final CraftDemandInfoTransientRepository craftDemandInfoTransientRepository;
    private final PrepareBomCraftCycleHelper prepareBomCraftCycleHelper;

    @Override
    public BomMaterialInfoDto queryAndCheckMaterialInfo(BomMaterialHandleReq req, Long bomOrderId) {
        BomMaterialInfoDto materialInfoDto = new BomMaterialInfoDto();

        Map<Integer, Set<Long>> skuIdMap = this.buildSkuIdMap(req);

        //物料唯一值spu@sku
        Map<String, Long> querySpuSkuMap = this.getSpuSkuMap(req);

        //辅料商品(包含特辅)
        Set<Long> accessoriesSkuIds = new HashSet<>();
        Set<Long> normalAccessoriesSkuIds = skuIdMap.get(MaterialDemandTypeEnum.ACCESSORIES.getCode());
        Set<Long> specialAccessoriesSkuIds =  skuIdMap.get(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode());
        if (CollUtil.isNotEmpty(normalAccessoriesSkuIds)) {
            accessoriesSkuIds.addAll(normalAccessoriesSkuIds);
        }
        if (CollUtil.isNotEmpty(specialAccessoriesSkuIds)) {
            accessoriesSkuIds.addAll(specialAccessoriesSkuIds);
        }
        List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
        Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream()
                .map(ProductSpuInfoVo::getSkus)
                .flatMap(Collection::stream)
                //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
                .filter(item -> Objects.nonNull(querySpuSkuMap.get(BomOrderConverter.getAccessoriesSpuSkuId(item))))
                .collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream().collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
        this.checkAccessoriesSkuParam(accessoriesSkuMap, accessoriesSpuMap, bomOrderId, req);
        materialInfoDto.setAccessoriesSkuMap(accessoriesSkuMap).setAccessoriesSpuMap(accessoriesSpuMap);

        //辅料分类
        Set<Long> accessoriesCategoryIds = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getCategoryId).collect(Collectors.toSet());
        List<CategoryTreeMapVo> categoryTreeList = productRemoteHelper.getCategoryInfo(accessoriesCategoryIds);
        Map<Long, CategoryTreeMapVo> categoryTreeMap = categoryTreeList.stream().collect(Collectors.toMap(CategoryTreeMapVo::getCategoryId, Function.identity(), (k1, k2) -> k1));
        materialInfoDto.setCategoryTreeMap(categoryTreeMap);

        //面料商品
        Set<Long> fabricSkuIds = skuIdMap.get(MaterialDemandTypeEnum.FABRIC.getCode());
        List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuSetEnableState(fabricSkuIds, querySpuSkuMap);
        //注: key要用spu+sku确定唯一性; 因为中台的面料一个sku可能对应多个spu
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = StreamUtil.list2Map(fabricSkuInfos, BomOrderConverter::getLyFabricSpuSkuId);
        this.checkFabricSkuParam(fabricSkuMap, bomOrderId, req);
        materialInfoDto.setFabricSkuMap(fabricSkuMap);
        return materialInfoDto;
    }

    @Override
    public BomMaterialInfoDto queryAndCheckMaterialInfoTransient(BomMaterialHandleReq req,
                                                                 BomOrder bomOrder,
                                                                 BomOrderTransient transientBom) {
        BomMaterialInfoDto materialInfoDto = new BomMaterialInfoDto();

        Map<Integer, Set<Long>> skuIdMap = this.buildSkuIdMap(req);

        //需要查询的sku
        Map<String, Long> querySpuSkuMap = this.getSpuSkuMap(req);

        //辅料商品(包含特辅)
        Set<Long> accessoriesSkuIds = new HashSet<>(16);
        Set<Long> normalAccessoriesSkuIds = skuIdMap.get(MaterialDemandTypeEnum.ACCESSORIES.getCode());
        Set<Long> specialAccessoriesSkuIds =  skuIdMap.get(MaterialDemandTypeEnum.SPECIAL_ACCESSORIES.getCode());
        if (CollUtil.isNotEmpty(normalAccessoriesSkuIds)) {
            accessoriesSkuIds.addAll(normalAccessoriesSkuIds);
        }
        if (CollUtil.isNotEmpty(specialAccessoriesSkuIds)) {
            accessoriesSkuIds.addAll(specialAccessoriesSkuIds);
        }
        List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
        Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream()
                .map(ProductSpuInfoVo::getSkus)
                .flatMap(Collection::stream)
                //一个sku可能对应过个spu, 只查当前需要的spu+sku物料
                .filter(item -> Objects.nonNull(querySpuSkuMap.get(BomOrderConverter.getAccessoriesSpuSkuId(item))))
                .collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
        Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream().collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
        this.checkAccessoriesSkuParamTransient(accessoriesSkuMap, accessoriesSpuMap, transientBom, req);
        materialInfoDto.setAccessoriesSkuMap(accessoriesSkuMap).setAccessoriesSpuMap(accessoriesSpuMap);

        //辅料分类
        Set<Long> accessoriesCategoryIds = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getCategoryId).collect(Collectors.toSet());
        List<CategoryTreeMapVo> categoryTreeList = productRemoteHelper.getCategoryInfo(accessoriesCategoryIds);
        Map<Long, CategoryTreeMapVo> categoryTreeMap = categoryTreeList.stream().collect(Collectors.toMap(CategoryTreeMapVo::getCategoryId, Function.identity(), (k1, k2) -> k1));
        materialInfoDto.setCategoryTreeMap(categoryTreeMap);

        //面料商品
        Set<Long> fabricSkuIds = skuIdMap.get(MaterialDemandTypeEnum.FABRIC.getCode());
        List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(fabricSkuIds, querySpuSkuMap);
        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = StreamUtil.list2Map(fabricSkuInfos, BomOrderConverter::getLyFabricSpuSkuId);
        this.checkFabricSkuParamTransient(fabricSkuMap, transientBom, req);
        materialInfoDto.setFabricSkuMap(fabricSkuMap);
        return materialInfoDto;
    }

    private Map<String, Long> getSpuSkuMap(BomMaterialHandleReq req) {
        Map<String, Long> addSpuSkuMap = StreamUtil.list2MapWithValue(req.getAddBomMaterials(),
                BomOrderUpdateV3Req.AddBomMaterialReq::getSpuSkuId, BomOrderUpdateV3Req.AddBomMaterialReq::getSkuId);

        Map<String, Long> updateSpuSkuMap = StreamUtil.list2MapWithValue(req.getUpdateBomMaterials(),
                BomOrderUpdateV3Req.UpdateBomMaterialReq::getSpuSkuId, BomOrderUpdateV3Req.UpdateBomMaterialReq::getSkuId);

        return this.mergeIdMap(addSpuSkuMap, updateSpuSkuMap);
    }

    private <T, K> Map<T, K> mergeIdMap(Map<T, K> map1, Map<T, K> map2) {
        Map<T, K> mergeMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(map1)) {
            mergeMap.putAll(map1);
        }
        if (CollUtil.isNotEmpty(map2)) {
            mergeMap.putAll(map2);
        }
        return mergeMap;
    }

    @Override
    public BomMaterialInfoDto queryMaterialInfoBySkuId(Set<Long>  skuIdSet, MaterialDemandTypeEnum demandTypeEnum) {
        BomMaterialInfoDto materialInfoDto = new BomMaterialInfoDto();
        if (CollUtil.isEmpty(skuIdSet) || Objects.isNull(demandTypeEnum)) {
            return materialInfoDto;
        }
        if (Objects.equals(demandTypeEnum, MaterialDemandTypeEnum.FABRIC)) {
            //面料商品
            List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(skuIdSet);
            Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = StreamUtil.list2Map(fabricSkuInfos, BomOrderConverter::getLyFabricSpuSkuId);
            materialInfoDto.setFabricSkuMap(fabricSkuMap);
            return materialInfoDto;
        }
        if (Objects.equals(demandTypeEnum, MaterialDemandTypeEnum.ACCESSORIES)) {
            //辅料商品
            List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(skuIdSet);
            Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus).flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
            Map<Long, ProductSpuInfoVo> accessoriesSpuMap = accessoriesSkuInfos.stream().collect(Collectors.toMap(ProductSpuInfoVo::getSpuId, Function.identity(), (k1, k2) -> k1));
            materialInfoDto.setAccessoriesSkuMap(accessoriesSkuMap).setAccessoriesSpuMap(accessoriesSpuMap);

            //辅料分类
            Set<Long> accessoriesCategoryIds = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getCategoryId).collect(Collectors.toSet());
            List<CategoryTreeMapVo> categoryTreeList = productRemoteHelper.getCategoryInfo(accessoriesCategoryIds);
            Map<Long, CategoryTreeMapVo> categoryTreeMap = categoryTreeList.stream().collect(Collectors.toMap(CategoryTreeMapVo::getCategoryId, Function.identity(), (k1, k2) -> k1));
            materialInfoDto.setCategoryTreeMap(categoryTreeMap);
        }
        return materialInfoDto;
    }

    @Override
    public void handlerMaterialSnapshotGenerate(List<MaterialSnapshotCreateReq> materialSnapshotReqList,
                                                 List<BomOrderMaterial> newBomMaterials,
                                                 List<CraftDemandSaveV3Req> craftDemandAddReqList,
                                                 List<MaterialDetailReq> supplyChainMaterialReqs,
                                                 Prototype prototype) {
        if (CollectionUtil.isEmpty(materialSnapshotReqList)) {
            return;
        }
        //查询供应商信息
        List<Long> specialAccessoriesSupplierIds = materialSnapshotReqList.stream().map(MaterialSnapshotCreateReq::getSupplierId).collect(Collectors.toList());
        Map<Long, CommoditySupplierInfoVo> supplierMap = this.getSupplierInfo(specialAccessoriesSupplierIds);

        if (CollUtil.isEmpty(supplierMap)) {
            List<Long> materialIdList = materialSnapshotReqList.stream().map(MaterialSnapshotCreateReq::getBomMaterialId).collect(Collectors.toList());
            List<String> materialNameList = newBomMaterials.stream()
                    .filter(item -> materialIdList.contains(item.getBomMaterialId()))
                    .map(BomOrderMaterial::getPrototypeMaterialName).collect(Collectors.toList());
            String msg = "新增物料" + Json.serialize(materialNameList);
            throw new SdpDesignException(msg + "缺少供应商信息，请联系面辅料部补充供应商信息后再提交");
        }
        //设置供应商合作关系(履约的开票状态)
        Map<Long, BomOrderMaterial> materialMap = newBomMaterials.stream()
                .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
        materialSnapshotReqList.forEach(item -> {
            if (Objects.nonNull(item.getSupplierId())) {
                BomOrderMaterial bomOrderMaterial = materialMap.get(item.getBomMaterialId());
                SdpDesignException.notNull(bomOrderMaterial, "物料添加失败, 找不到对应物料! {}", item.getBomMaterialId());

                CommoditySupplierInfoVo supplierInfoVo = supplierMap.get(item.getSupplierId());
                if (Objects.isNull(supplierInfoVo)) {
                    throw new SdpDesignException(bomOrderMaterial.getPrototypeMaterialName() + "缺少供应商信息，请联系面辅料部补充供应商信息后再提交");
                }
                CommoditySupplierExtVo supplierExtVo = supplierInfoVo.getExt();
                if (Objects.nonNull(supplierExtVo)) {
                    item.setInvoiceState(supplierExtVo.getInvoiceState());
                }
            }
        });

        //新增物料快照
        Map<Long, MaterialSnapshotCreateReq> snapshotCreateReqMap = materialSnapshotReqList.stream()
                .collect(Collectors.toMap(MaterialSnapshotCreateReq::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
        Map<Long, Long> materialSnapshotMap = materialSnapshotService.batchCreate4Bom(materialSnapshotReqList);

        //维护bom单物料与物料快照关系
        newBomMaterials.forEach(bomOrderMaterial -> {
            Long materialSnapshotId = materialSnapshotMap.get(bomOrderMaterial.getBomMaterialId());
            if (Objects.isNull(materialSnapshotId)) {
                return;
            }

            MaterialSnapshotCreateReq materialSnapshotCreateReq = snapshotCreateReqMap.get(bomOrderMaterial.getBomMaterialId());
            bomOrderMaterial.setMaterialSnapshotId(materialSnapshotId);
            supplyChainMaterialReqs.add(BomOrderConverter.buildSupplyChainMaterialReq(prototype, bomOrderMaterial, materialSnapshotCreateReq));
            craftDemandAddReqList.forEach(craft -> {
                if (Objects.equals(craft.getBomMaterialId(), bomOrderMaterial.getBomMaterialId())) {
                    //数据库不维护该字段, 推送二次工艺用
                    craft.setMaterialSnapshotId(materialSnapshotId);
                }
            });
        });
    }

    @Override
    public void handlerMaterialSnapshotGenerateTransient(List<MaterialSnapshotCreateReq> materialSnapshotReqList,
                                                List<BomOrderMaterialTransient> newBomMaterials,
                                                List<CraftDemandSaveV3Req> craftDemandAddReqList) {
        if (CollectionUtil.isEmpty(materialSnapshotReqList)) {
            return;
        }
        //查询供应商信息
        List<Long> specialAccessoriesSupplierIds = materialSnapshotReqList.stream().map(MaterialSnapshotCreateReq::getSupplierId).collect(Collectors.toList());
        Map<Long, CommoditySupplierInfoVo> supplierMap = this.getSupplierInfo(specialAccessoriesSupplierIds);

        if (CollUtil.isEmpty(supplierMap)) {
            List<String> materialNameList = materialSnapshotReqList.stream().map(MaterialSnapshotCreateReq::getSkuCode).collect(Collectors.toList());
            String msg = "物料SKU：" + Json.serialize(materialNameList);
            throw new SdpDesignException(msg + "缺少供应商信息，请联系面辅料部补充供应商信息后再提交");
        }
        //设置供应商合作关系(履约的开票状态)
        materialSnapshotReqList.forEach(item -> {
            if (Objects.nonNull(item.getSupplierId())) {
                CommoditySupplierInfoVo supplierInfoVo = supplierMap.get(item.getSupplierId());
                if (Objects.isNull(supplierInfoVo)) {
                    throw new SdpDesignException("物料SKU：" +item.getSkuCode() + "缺少供应商信息，请联系面辅料部补充供应商信息后再提交");
                }
                CommoditySupplierExtVo supplierExtVo = supplierInfoVo.getExt();
                if (Objects.nonNull(supplierExtVo)) {
                    item.setInvoiceState(supplierExtVo.getInvoiceState());
                }
            }
        });

        //新增物料快照
        // Map<Long, MaterialSnapshotCreateReq> snapshotCreateReqMap = materialSnapshotReqList.stream()
        //         .collect(Collectors.toMap(MaterialSnapshotCreateReq::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
        Map<Long, Long> materialSnapshotMap = materialSnapshotService.batchCreate4Bom(materialSnapshotReqList);

        //维护bom单物料与物料快照关系
        newBomMaterials.forEach(bomOrderMaterial -> {
            Long materialSnapshotId = materialSnapshotMap.get(bomOrderMaterial.getBomMaterialId());
            if (Objects.isNull(materialSnapshotId)) {
                return;
            }
            bomOrderMaterial.setMaterialSnapshotId(materialSnapshotId);
            craftDemandAddReqList.forEach(craft -> {
                if (Objects.equals(craft.getBomMaterialId(), bomOrderMaterial.getBomMaterialId())) {
                    //数据库不维护该字段, 推送二次工艺用
                    craft.setMaterialSnapshotId(materialSnapshotId);
                }
            });
        });
    }

    @Override
    public List<SpecialAccessories> resetSpecialAccessories(BomOrder bomOrder,
                                                             List<BomOrderUpdateV3Req.AddBomMaterialReq> specialAccessoriesReqList,
                                                             Map<Long, ProductSkuVo> accessoriesSkuMap,
                                                             Map<Long, ProductSpuInfoVo> accessoriesSpuMap,
                                                             Map<Long, SpecialAccessories> accessoriesMap,
                                                             List<DesignRemarksReq> designRemarksReqList) {
        List<SpecialAccessories> newSaList = new LinkedList<>();
        specialAccessoriesReqList.forEach(item -> {
            SpecialAccessories specialAccessories = accessoriesMap.get(item.getBomMaterialIdCopy());
            if (Objects.isNull(specialAccessories)) {
                return;
            }
            ProductSkuVo productSkuVo = accessoriesSkuMap.get(item.getSkuId());
            ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(item.getCommodityId());
            if (Objects.isNull(productSkuVo) || Objects.isNull(productSpuInfoVo)) {
                return;
            }

            SpecialAccessories saEo = new SpecialAccessories();
            BeanUtils.copyProperties(specialAccessories, saEo);
            long newSpecialAccessoryId = IdPool.getId();
            saEo.setSpecialAccessoriesId(newSpecialAccessoryId);
            saEo.setPrototypeId(bomOrder.getPrototypeId());
            saEo.setDesignCode(bomOrder.getDesignCode());
            saEo.setBomId(bomOrder.getBomId());
            saEo.setAccessoriesFlagId(IdPool.getId());
            //特殊辅料不能编辑, 只设置履约可修改的属性
            saEo.setCommodityNumber(productSkuVo.getProductNumber());
            saEo.setSkuAttrs(JSON.toJSONString(productSkuVo.getSkuAttrs()));
            //图片先置空再重新获取
            saEo.setSkuPicture(null);
            if (CollectionUtil.isNotEmpty(productSkuVo.getPictures())) {
                String pictureList = productSkuVo.getPictures().stream().map(ProductPictureWebVo::getPicturePath)
                        .collect(Collectors.joining(StrUtil.COMMA));
                saEo.setSkuPicture(pictureList);
            }
            //若sku图为空,取商品图 --v3.5.2_汶俊;
            else if (CollectionUtil.isNotEmpty(productSpuInfoVo.getPictures())){
                String pictureList = productSpuInfoVo.getPictures().stream().map(ProductPictureVo::getPicturePath)
                        .collect(Collectors.joining(StrUtil.COMMA));
                saEo.setSkuPicture(pictureList);
            }

            if (CollectionUtil.isNotEmpty(productSkuVo.getPrices())){
                ProductSkuPriceVo skuPriceVo = productSkuVo.getPrices().get(0);
                saEo.setSaleUnit(skuPriceVo.getValuationUnitName());
                saEo.setSkuPrice(skuPriceVo.getSkuPrice());
                saEo.setPurchasePrice(skuPriceVo.getPurchasePrice());
                saEo.setPriceReplyTime(skuPriceVo.getRevisedTime());
            }

            saEo.setSpuName(productSpuInfoVo.getProductName());
            saEo.setSpuCode(productSpuInfoVo.getSpuCode());
            saEo.setSpuCityName(productSpuInfoVo.getSpuCityName());
            saEo.setSupplierId(productSpuInfoVo.getSupplierId());
            saEo.setSupplierName(productSpuInfoVo.getSupplierName());
            saEo.setSupplierCode(productSpuInfoVo.getSupplierCode());

            //包装数量单位; 最小价格单位
            saEo.setPackNumber(productSkuVo.getPackNumber());
            saEo.setPackUnitName(productSkuVo.getPackUnitName());
            saEo.setPackAssistantUnitName(productSkuVo.getPackAssistantUnitName());
            saEo.setMinPrice(productSkuVo.getMinPrice());
            saEo.setMinPriceUnit(productSkuVo.getMinUnit());

            //获取最新的失效时间
            saEo.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());

            // 样衣与大货周期
            saEo.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
            saEo.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());

            //清空用量数据
            saEo.setDosageAccount(null);
            saEo.setDosageAccountUnit(null);
            saEo.setAttritionRate(null);
            saEo.setCreatorName(null);
            saEo.setCreatedTime(null);
            saEo.setCreatorId(null);
            saEo.setReviserId(null);
            saEo.setReviserName(null);
            saEo.setRevisedTime(null);
            newSaList.add(saEo);
            //备注
            if (StringUtils.isNotBlank(item.getRemark())) {
                designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomOrder.getBomId(), newSpecialAccessoryId, item.getRemark()));
            }
        });
        return newSaList;
    }

    @Override
    public List<SpecialAccessoriesTransient> resetSpecialAccessoriesTransient(BomOrder bomOrder,
                                                                              BomOrderTransient bomOrderTransient,
                                                                              List<BomOrderUpdateV3Req.AddBomMaterialReq> specialAccessoriesReqList,
                                                                              Map<Long, ProductSkuVo> accessoriesSkuMap,
                                                                              Map<Long, ProductSpuInfoVo> accessoriesSpuMap,
                                                                              Map<Long, SpecialAccessories> accessoriesMap,
                                                                              List<DesignRemarksReq> designRemarksReqList) {
        List<SpecialAccessoriesTransient> newSpecialTransientList = new LinkedList<>();
        specialAccessoriesReqList.forEach(item -> {
            SpecialAccessories originSpecial = accessoriesMap.get(item.getBomMaterialIdCopy());
            if (Objects.isNull(originSpecial)) {
                return;
            }
            ProductSkuVo productSkuVo = accessoriesSkuMap.get(item.getSkuId());
            ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(item.getCommodityId());
            if (Objects.isNull(productSkuVo) || Objects.isNull(productSpuInfoVo)) {
                return;
            }

            SpecialAccessoriesTransient specialTransient = new SpecialAccessoriesTransient();
            BeanUtils.copyProperties(originSpecial, specialTransient);
            long newSpecialAccessoryId = IdPool.getId();
            // Long originSpecialId = originSpecial.getSpecialAccessoriesId();
            specialTransient.setSpecialAccessoriesId(newSpecialAccessoryId);
            specialTransient.setBomTransientId(bomOrderTransient.getBomTransientId());
            specialTransient.setBomId(bomOrder.getBomId());
            specialTransient.setPrototypeId(bomOrder.getPrototypeId());
            specialTransient.setDesignCode(bomOrder.getDesignCode());
            // specialTransient.setOriginSpecialId(originSpecialId);
            specialTransient.setAccessoriesFlagId(IdPool.getId());
            //特殊辅料不能编辑, 只设置履约可修改的属性
            specialTransient.setCommodityNumber(productSkuVo.getProductNumber());
            specialTransient.setSkuAttrs(JSON.toJSONString(productSkuVo.getSkuAttrs()));
            //图片先置空再重新获取
            specialTransient.setSkuPicture(null);
            if (CollectionUtil.isNotEmpty(productSkuVo.getPictures())) {
                String pictureList = productSkuVo.getPictures().stream().map(ProductPictureWebVo::getPicturePath)
                        .collect(Collectors.joining(StrUtil.COMMA));
                specialTransient.setSkuPicture(pictureList);
            }
            //若sku图为空,取商品图 --v3.5.2_汶俊;
            else if (CollectionUtil.isNotEmpty(productSpuInfoVo.getPictures())){
                String pictureList = productSpuInfoVo.getPictures().stream().map(ProductPictureVo::getPicturePath)
                        .collect(Collectors.joining(StrUtil.COMMA));
                specialTransient.setSkuPicture(pictureList);
            }

            if (CollectionUtil.isNotEmpty(productSkuVo.getPrices())){
                ProductSkuPriceVo skuPriceVo = productSkuVo.getPrices().get(0);
                specialTransient.setSaleUnit(skuPriceVo.getValuationUnitName());
                specialTransient.setSkuPrice(skuPriceVo.getSkuPrice());
                specialTransient.setPurchasePrice(skuPriceVo.getPurchasePrice());
                specialTransient.setPriceReplyTime(skuPriceVo.getRevisedTime());
            }

            specialTransient.setSpuName(productSpuInfoVo.getProductName());
            specialTransient.setSpuCode(productSpuInfoVo.getSpuCode());
            specialTransient.setSpuCityName(productSpuInfoVo.getSpuCityName());
            specialTransient.setSupplierId(productSpuInfoVo.getSupplierId());
            specialTransient.setSupplierName(productSpuInfoVo.getSupplierName());
            specialTransient.setSupplierCode(productSpuInfoVo.getSupplierCode());

            //包装数量单位; 最小价格单位
            specialTransient.setPackNumber(productSkuVo.getPackNumber());
            specialTransient.setPackUnitName(productSkuVo.getPackUnitName());
            specialTransient.setPackAssistantUnitName(productSkuVo.getPackAssistantUnitName());
            specialTransient.setMinPrice(productSkuVo.getMinPrice());
            specialTransient.setMinPriceUnit(productSkuVo.getMinUnit());

            specialTransient.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());

            // 样衣与大货周期
            specialTransient.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
            specialTransient.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());

            //清空用量数据
            specialTransient.setDosageAccount(null);
            specialTransient.setDosageAccountUnit(null);
            specialTransient.setAttritionRate(null);
            specialTransient.setCreatorName(null);
            specialTransient.setCreatedTime(null);
            specialTransient.setCreatorId(null);
            specialTransient.setReviserId(null);
            specialTransient.setReviserName(null);
            specialTransient.setRevisedTime(null);
            newSpecialTransientList.add(specialTransient);
            //备注
            if (StringUtils.isNotBlank(item.getRemark())) {
                designRemarksReqList.add(BomOrderMaterialConverter.buildBomMaterialRemark(bomOrderTransient.getBomTransientId(), newSpecialAccessoryId, item.getRemark()));
            }
        });
        return newSpecialTransientList;
    }

    @Override
    public void createMaterialToSupplyChain(BomOrder bomOrder, List<BomOrderMaterialTransient> addTransientMaterialList) {
        if (CollUtil.isEmpty(addTransientMaterialList)) {
            return;
        }

        Map<Long, BomOrderMaterialTransient> transientMaterialMap = addTransientMaterialList.stream()
                .collect(Collectors.toMap(BomOrderMaterialTransient::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));
        Prototype prototype = prototypeRepository.getByDesignCode(bomOrder.getDesignCode());

        //查询暂存物料对应物料快照信息, 并封装MaterialDetailReq同步给履约
        List<MaterialDetailReq> supplyChainMaterialReqs = new LinkedList<>();
        List<Long> snapshotIdList = addTransientMaterialList.stream().map(BomOrderMaterialTransient::getMaterialSnapshotId).collect(Collectors.toList());
        materialSnapshotRepository.listByIds(snapshotIdList).forEach(materialSnapshot -> {
            BomOrderMaterialTransient materialTransient = transientMaterialMap.get(materialSnapshot.getMaterialSnapshotId());
            MaterialDetailReq materialDetailReq = this.buildSupplyChainMaterialReq(prototype, materialTransient, materialSnapshot);
            supplyChainMaterialReqs.add(materialDetailReq);
        });

        //bom提交同步至商品服务
        if (CollUtil.isNotEmpty(supplyChainMaterialReqs)) {
            // demandRemoteHelper.createMaterialToSupplyChain(supplyChainMaterialReqs);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<Long, Long> copySpecialAccessories(BomOrder oldBomOrder, BomOrder newBomOrder) {
        Long oldBomId = oldBomOrder.getBomId();
        //将之前特殊辅料未关联Bom的进行关联起来
        List<SpecialAccessories> specialAccessoriesList = specialAccessoriesRepository.getListByDesignCode(newBomOrder.getDesignCode());
        Set<Long> closeAccessoriesFlagIds = new HashSet<>();
        if (CollectionUtil.isNotEmpty(specialAccessoriesList)) {

            specialAccessoriesList.stream().filter(special -> Objects.equals(special.getState(), SpecialAccessoriesStateEnum.CLOSED.getCode()))
                    .forEach(special -> closeAccessoriesFlagIds.add(special.getAccessoriesFlagId()));

            List<SpecialAccessories> updateSpecialAccessories = specialAccessoriesList.stream()
                    .filter(special -> Objects.isNull(special.getBomId()))
                    .filter(special -> !closeAccessoriesFlagIds.contains(special.getAccessoriesFlagId()))
                    .map(special -> {
                        return SpecialAccessories.builder().specialAccessoriesId(special.getSpecialAccessoriesId())
                                .bomId(newBomOrder.getBomId()).build();
                    }).collect(Collectors.toList());
            specialAccessoriesRepository.updateBatchById(updateSpecialAccessories);
        }

        Map<Long, Long> oldNewSpecialIdMap = new HashMap<>();
        //复制拷贝旧BOM上的特殊辅料(不包含暂存的)-v3.5.1
        List<SpecialAccessories> oldSpecialAccessoriesList = specialAccessoriesRepository.getListByBomId(oldBomId);
        if (CollectionUtil.isEmpty(oldSpecialAccessoriesList)) {
            return oldNewSpecialIdMap;
        }

        List<DesignRemarks> newDesignRemarksList = new ArrayList<>(128);

        //更新旧bom特辅的采购周期
        // this.resetAndUpdateSpecialPriceCycle(oldSpecialAccessoriesList);

        List<SpecialAccessories> newSpecialAccessoriesList = oldSpecialAccessoriesList.stream()
                .filter(special -> Objects.equals(special.getState(), SpecialAccessoriesStateEnum.SUBMIT.getCode()))
                .filter(special -> !closeAccessoriesFlagIds.contains(special.getAccessoriesFlagId()))
                .map(oldSpecial -> {
                    SpecialAccessories newSpecial = new SpecialAccessories();
                    BeanUtils.copyProperties(oldSpecial, newSpecial);
                    long newSpecialId = IdPool.getId();
                    newSpecial.setSpecialAccessoriesId(newSpecialId);
                    newSpecial.setBomId(newBomOrder.getBomId());
                    newSpecial.setPrototypeId(newBomOrder.getPrototypeId());
                    oldNewSpecialIdMap.put(oldSpecial.getSpecialAccessoriesId(), newSpecialId);

                    //复制之前的备注(包含暂存的)
                    List<DesignRemarks> designRemarksList = designRemarksRepository.getListByBizChildIds(Lists.newArrayList(oldSpecial.getSpecialAccessoriesId()), null);
                    if (CollectionUtil.isNotEmpty(designRemarksList)) {
                        designRemarksList.stream().max(Comparator.comparing(DesignRemarks::getCreatedTime)).ifPresent(oldRemark ->{
                            DesignRemarks newRemarks = new DesignRemarks();
                            BeanUtils.copyProperties(oldRemark, newRemarks);
                            newRemarks.setDesignRemarksId(IdPool.getId());
                            newRemarks.setBizId(newBomOrder.getBomId());
                            newRemarks.setBizChildId(newSpecial.getSpecialAccessoriesId());
                            newDesignRemarksList.add(newRemarks);
                        });
                    }
                    return newSpecial;
                }).collect(Collectors.toList());

        specialAccessoriesRepository.saveBatch(newSpecialAccessoriesList);
        designRemarksRepository.saveBatch(newDesignRemarksList);

        return oldNewSpecialIdMap;
    }

    private void resetAndUpdateSpecialPriceCycle(List<SpecialAccessories> oldSpecialAccessoriesList) {
        if (CollUtil.isEmpty(oldSpecialAccessoriesList)) {
            return;
        }
        //查询最新的采购周期(bom-2升版本为bom-3时, 更新bom-2的物料的采购周期信息,再复制到bom-3)
        Set<Long> accessoriesSkuIds = oldSpecialAccessoriesList.stream().map(SpecialAccessories::getSkuId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(accessoriesSkuIds)) {
            List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
            Map<Long, ProductSkuVo> accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus)
                    .flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
            oldSpecialAccessoriesList.forEach(item -> {
                ProductSkuVo productSkuVo = accessoriesSkuMap.get(item.getSkuId());
                if (Objects.nonNull(productSkuVo)) {
                    item.setMinPrice(productSkuVo.getMinPrice());
                    //价格有效期结束时间
                    item.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());
                    //样衣与大货周期
                    item.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
                    item.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());
                }
            });
        }
        //更新旧bom的物料采购周期信息
        UserContent userContent = UserContentHolder.get();
        oldSpecialAccessoriesList.forEach(item -> {
            specialAccessoriesRepository.lambdaUpdate()
                    .set(SpecialAccessories::getSamplePurchasingCycle, item.getSamplePurchasingCycle())
                    .set(SpecialAccessories::getBulkPurchasingCycle, item.getBulkPurchasingCycle())
                    .set(SpecialAccessories::getRevisedTime, LocalDateTime.now())
                    .set(Objects.nonNull(userContent), SpecialAccessories::getReviserId, userContent.getCurrentUserId())
                    .set(Objects.nonNull(userContent), SpecialAccessories::getReviserName, userContent.getCurrentUserName())
                    .eq(SpecialAccessories::getSpecialAccessoriesId, item.getSpecialAccessoriesId())
                    .update();
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomCopyInfoDto copyDemandFabricAccessories(BomOrder oldBomOrder, BomOrder newBomOrder) {
        Long oldBomId = oldBomOrder.getBomId();
        Long newBomId = newBomOrder.getBomId();
        BomCopyInfoDto copyInfoDto = new BomCopyInfoDto();
        //旧bom的面辅料
        List<BomOrderMaterial> oldMaterialList = bomOrderMaterialRepository.getListByBomId(oldBomId);
        if (CollectionUtil.isEmpty(oldMaterialList)) {
            return copyInfoDto;
        }

        //查询并更新最新的采购周期(bom-2升版本为bom-3时, 更新bom-2的物料的采购周期信息,再复制到bom-3)
        // this.resetUpdateMaterialCycle(oldMaterialList);

        //复制需求
        List<BomMaterialDemand> oldDemandList = bomMaterialDemandRepository.listByBomId(oldBomId);

        List<BomMaterialDemand> newDemandList = new LinkedList<>();
        Map<Long, Long> oldNewDemandIdMap = new HashMap<>();
        oldDemandList.forEach(oldDemand -> {
            BomMaterialDemand newDemand = new BomMaterialDemand();
            BeanUtils.copyProperties(oldDemand, newDemand);
            long newDemandId = IdPool.getId();
            newDemand.setBomMaterialDemandId(newDemandId);
            newDemand.setBomId(newBomId);
            oldNewDemandIdMap.put(oldDemand.getBomMaterialDemandId(), newDemandId);
            newDemandList.add(newDemand);
        });

        //旧bom的工艺
        List<CraftDemandInfo> craftDemandInfoList = craftDemandInfoRepository.getListByBomIdsAndState(List.of(oldBomId), CraftDemandStateEnum.SUBMIT.getCode());
        //查询并更新最新的工艺周期
        // this.prepareAndUpdateCraftCycle(craftDemandInfoList);

        Map<Long, List<CraftDemandInfo>> craftDemandInfoMap = craftDemandInfoList.stream().collect(Collectors.groupingBy(CraftDemandInfo::getBomMaterialId));

        //新的物料,备注,工艺
        List<DesignRemarks> newDesignRemarksList = new LinkedList<>();
        List<CraftDemandInfo> newCraftDemandInfoList = new LinkedList<>();
        Map<Long, Long> oldNewMaterialIdMap = new HashMap<>();
        Map<Long, Long> oldNewCraftIdMap = new HashMap<>();
        List<BomOrderMaterial> newMaterialList = oldMaterialList.stream().map(oldMaterial -> {
            BomOrderMaterial newMaterial = new BomOrderMaterial();
            BeanUtils.copyProperties(oldMaterial, newMaterial);
            long newBomMaterialId = IdPool.getId();
            newMaterial.setBomMaterialId(newBomMaterialId);
            newMaterial.setBomId(newBomId);

            //关联新需求
            if (Objects.nonNull(oldMaterial.getBomMaterialDemandId())) {
                Long newDemandId = oldNewDemandIdMap.get(oldMaterial.getBomMaterialDemandId());
                newMaterial.setBomMaterialDemandId(newDemandId);
            }
            oldNewMaterialIdMap.put(oldMaterial.getBomMaterialId(), newBomMaterialId);

            List<CraftDemandInfo> oldCraftDemandInfoList = craftDemandInfoMap.get(oldMaterial.getBomMaterialId());
            if (CollectionUtil.isNotEmpty(oldCraftDemandInfoList)) {
                oldCraftDemandInfoList.forEach(oldCraft -> {
                    CraftDemandInfo newCraft = new CraftDemandInfo();
                    BeanUtils.copyProperties(oldCraft, newCraft);
                    long newCraftDemandId = IdPool.getId();
                    newCraft.setCraftDemandId(newCraftDemandId);
                    newCraft.setBomId(newBomId);
                    newCraft.setBomMaterialId(newMaterial.getBomMaterialId());
                    newCraftDemandInfoList.add(newCraft);
                    oldNewCraftIdMap.put(oldCraft.getCraftDemandId(), newCraftDemandId);
                });
            }

            //复制之前的备注(包含暂存的)
            List<DesignRemarks> designRemarksList = designRemarksRepository.getListByBizChildIds(Lists.newArrayList(oldMaterial.getBomMaterialId()), null);
            if (CollectionUtil.isNotEmpty(designRemarksList)) {
                designRemarksList.stream().max(Comparator.comparing(DesignRemarks::getCreatedTime)).ifPresent(oldRemark ->{
                    DesignRemarks newRemarks = new DesignRemarks();
                    BeanUtils.copyProperties(oldRemark, newRemarks);
                    newRemarks.setDesignRemarksId(IdPool.getId());
                    newRemarks.setBizId(newBomId);
                    newRemarks.setBizChildId(newMaterial.getBomMaterialId());
                    newDesignRemarksList.add(newRemarks);
                });
            }
            return newMaterial;
        }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(newDemandList)) {
            //关联最新的物料id
            newDemandList.forEach(item -> {
                Long newLatestMaterialId = oldNewMaterialIdMap.get(item.getLatestBomMaterialId());
                item.setLatestBomMaterialId(newLatestMaterialId);
            });
        }

        bomOrderMaterialRepository.saveBatch(newMaterialList);
        craftDemandInfoRepository.saveBatch(newCraftDemandInfoList);
        designRemarksRepository.saveBatch(newDesignRemarksList);
        bomMaterialDemandRepository.saveBatch(newDemandList);

        copyInfoDto
                .setOldNewDemandIdMap(oldNewDemandIdMap)
                .setOldNewMaterialIdMap(oldNewMaterialIdMap)
                .setOldNewCraftIdMap(oldNewCraftIdMap);
        log.info("bom升版本-新旧物料关系:{}", JSON.toJSONString(copyInfoDto));
        return copyInfoDto;

    }

    private void resetUpdateMaterialCycle(List<BomOrderMaterial> oldMaterialList) {
        if (CollUtil.isEmpty(oldMaterialList)) {
            return;
        }

        List<Long> snapshotIdList = oldMaterialList.stream().filter(item -> Objects.nonNull(item.getMaterialSnapshotId()))
                .map(BomOrderMaterial::getMaterialSnapshotId).collect(Collectors.toList());
        List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(snapshotIdList);
        if (CollUtil.isEmpty(snapshotList)) {
            return;
        }
        Map<Long, MaterialSnapshot> snapshotMap = snapshotList.stream().collect(Collectors.toMap(MaterialSnapshot::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));
        Set<Long> accessoriesSkuIds = snapshotList.stream().filter(item -> Objects.equals(MaterialDemandTypeEnum.ACCESSORIES.getCode(), item.getMaterialType()))
                .map(MaterialSnapshot::getSkuId).collect(Collectors.toSet());
        Set<Long> fabricSkuIdSet = snapshotList.stream().filter(item -> Objects.equals(MaterialDemandTypeEnum.FABRIC.getCode(), item.getMaterialType()))
                .map(MaterialSnapshot::getSkuId).collect(Collectors.toSet());

        Map<Long, ProductSkuVo> accessoriesSkuMap = new HashMap<>();
        if (CollUtil.isNotEmpty(accessoriesSkuIds)) {
            List<ProductSpuInfoVo> accessoriesSkuInfos = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
            accessoriesSkuMap = accessoriesSkuInfos.stream().map(ProductSpuInfoVo::getSkus).flatMap(Collection::stream).collect(Collectors.toMap(ProductSkuVo::getSkuId, Function.identity(), (k1, k2) -> k1));
        }

        Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap = new HashMap<>();
        if (CollUtil.isNotEmpty(fabricSkuIdSet)) {
            List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuInfo(fabricSkuIdSet);
            fabricSkuMap = StreamUtil.list2Map(fabricSkuInfos, BomOrderConverter::getLyFabricSpuSkuId);
        }

        for (BomOrderMaterial material : oldMaterialList) {
            if (Objects.isNull(material.getMaterialSnapshotId())) {
                continue;
            }
            MaterialSnapshot snapshot = snapshotMap.get(material.getMaterialSnapshotId());
            if (Objects.isNull(snapshot)) {
                continue;
            }
            //面料采购周期
            if (Objects.equals(MaterialDemandTypeEnum.FABRIC.getCode(), snapshot.getMaterialType())) {
                CommoditySkuCollectionRespVo.Sku skuVo = fabricSkuMap.get(snapshot.getSpuSkuId());
                if (Objects.nonNull(skuVo)) {
                    if (CollectionUtil.isNotEmpty(skuVo.getSkuPriceVos())) {
                        skuVo.getSkuPriceVos().stream().filter(skuPriceVo -> Objects.equals(skuPriceVo.getSalesRegionId(), DefaultSalesRegionEnum.NATION.getRegionId()))
                                .findFirst().ifPresent(skuPriceVo -> {
                            //足米价
                            material.setMeterPrice(skuPriceVo.getMeterPrice());
                            //价格有效期结束时间
                            material.setPriceInvalidTime(skuPriceVo.getValidityEndTime());
                        });
                    }
                    //样衣与大货周期
                    material.setSamplePurchasingCycle(skuVo.getSamplePurchasingCycle());
                    material.setBulkPurchasingCycle(skuVo.getBulkPurchasingCycle());

                    //更新最新采购周期
                    this.updateBomMaterialCycle(material);
                }
            }
            //辅料采购周期
            else if (Objects.equals(MaterialDemandTypeEnum.ACCESSORIES.getCode(), snapshot.getMaterialType())) {
                ProductSkuVo productSkuVo = accessoriesSkuMap.get(snapshot.getSkuId());
                if (Objects.nonNull(productSkuVo)) {
                    material.setMinPrice(productSkuVo.getMinPrice());
                    //价格有效期结束时间
                    material.setPriceInvalidTime(productSkuVo.getPriceExpireEnd());
                    //样衣与大货周期
                    material.setSamplePurchasingCycle(productSkuVo.getSamplePurchasingCycle());
                    material.setBulkPurchasingCycle(productSkuVo.getBulkPurchasingCycle());

                    //更新最新采购周期
                    this.updateBomMaterialCycle(material);
                }
            }

        }
    }

    private void updateBomMaterialCycle(BomOrderMaterial updateMaterial) {
        if (Objects.isNull(updateMaterial)) {
            return;
        }
        UserContent userContent = UserContentHolder.get();
        bomOrderMaterialRepository.lambdaUpdate()
                .set(BomOrderMaterial::getSamplePurchasingCycle, updateMaterial.getSamplePurchasingCycle())
                .set(BomOrderMaterial::getBulkPurchasingCycle, updateMaterial.getBulkPurchasingCycle())
                .set(BomOrderMaterial::getRevisedTime, LocalDateTime.now())
                .set(Objects.nonNull(userContent), BomOrderMaterial::getReviserId, userContent.getCurrentUserId())
                .set(Objects.nonNull(userContent), BomOrderMaterial::getReviserName, userContent.getCurrentUserName())
                .eq(BomOrderMaterial::getBomMaterialId, updateMaterial.getBomMaterialId())
                .update();
    }

    /**
     * 赋值并更新最新工艺周期
     */
    private void prepareAndUpdateCraftCycle(List<CraftDemandInfo> craftDemands) {
        if (CollUtil.isEmpty(craftDemands)) {
            return;
        }
        List<Long> listThirdDemandId = craftDemands.stream().
                map(CraftDemandInfo::getThirdPartyCraftDemandId).distinct().collect(Collectors.toList());
        listThirdDemandId.removeIf(Objects::isNull);
        if (CollUtil.isEmpty(listThirdDemandId)) {
            return;
        }
        // 获取第三方id对应工艺周期map
        Map<Long, CraftDemandMatchDetailDto.CraftDemandMatchDetail> mapThirdIdDetail
                = prepareBomCraftCycleHelper.getCraftCycleMatchByThirdId(listThirdDemandId);
        for (CraftDemandInfo craftDemandInfo : craftDemands) {
            if (Objects.isNull(craftDemandInfo.getThirdPartyCraftDemandId())) {
                continue;
            }
            CraftDemandMatchDetailDto.CraftDemandMatchDetail craftDemandMatchDetail =
                    mapThirdIdDetail.get(craftDemandInfo.getThirdPartyCraftDemandId());
            if (null != craftDemandMatchDetail) {
                // 赋值工艺周期
                craftDemandInfo.setSampleCraftCycle(craftDemandMatchDetail.getSampleCraftCycle());
                craftDemandInfo.setBulkCraftCycle(craftDemandMatchDetail.getBulkCraftCycle());
                // 更新工艺周期
                craftDemandInfoRepository.lambdaUpdate().
                        eq(CraftDemandInfo::getCraftDemandId, craftDemandInfo.getCraftDemandId()).
                        set(CraftDemandInfo::getSampleCraftCycle, craftDemandMatchDetail.getSampleCraftCycle()).
                        set(CraftDemandInfo::getBulkCraftCycle, craftDemandMatchDetail.getBulkCraftCycle()).update();

            }
        }
    }

    /**
     * 赋值并更新最新工艺周期-暂存工艺
     */
    private void prepareAndUpdateCraftCycleTransient(List<CraftDemandInfoTransient> craftDemands) {
        if (CollUtil.isEmpty(craftDemands)) {
            return;
        }
        List<Long> listThirdDemandId = craftDemands.stream().
                map(CraftDemandInfoTransient::getThirdPartyCraftDemandId).distinct().collect(Collectors.toList());
        listThirdDemandId.removeIf(Objects::isNull);
        if (CollUtil.isEmpty(listThirdDemandId)) {
            return;
        }
        // 获取第三方id对应工艺周期map
        Map<Long, CraftDemandMatchDetailDto.CraftDemandMatchDetail> mapThirdIdDetail
                = prepareBomCraftCycleHelper.getCraftCycleMatchByThirdId(listThirdDemandId);
        for (CraftDemandInfoTransient craftDemandInfo : craftDemands) {
            if (Objects.isNull(craftDemandInfo.getThirdPartyCraftDemandId())) {
                continue;
            }
            CraftDemandMatchDetailDto.CraftDemandMatchDetail craftDemandMatchDetail =
                    mapThirdIdDetail.get(craftDemandInfo.getThirdPartyCraftDemandId());
            if (null != craftDemandMatchDetail) {
                // 赋值工艺周期
                craftDemandInfo.setSampleCraftCycle(craftDemandMatchDetail.getSampleCraftCycle());
                craftDemandInfo.setBulkCraftCycle(craftDemandMatchDetail.getBulkCraftCycle());
                // 更新工艺周期
                craftDemandInfoTransientRepository.lambdaUpdate().
                        eq(CraftDemandInfoTransient::getCraftDemandId, craftDemandInfo.getCraftDemandId()).
                        set(CraftDemandInfoTransient::getSampleCraftCycle, craftDemandMatchDetail.getSampleCraftCycle()).
                        set(CraftDemandInfoTransient::getBulkCraftCycle, craftDemandMatchDetail.getBulkCraftCycle()).update();

            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyTransientInfo(BomOrder oldBomOrder, BomOrder newBomOrder, BomCopyInfoDto copyInfoDto) {
        // 对暂存bom进行复制(指向新的bom)
        Long oldBomId = oldBomOrder.getBomId();
        Long newBomId = newBomOrder.getBomId();

        BomOrderTransient transientBom = bomOrderTransientRepository.getByBomId(oldBomId);
        if (Objects.isNull(transientBom)) {
            return;
        }
        Long bomTransientId = transientBom.getBomTransientId();
        Map<Long, Long> oldNewDemandIdMap = copyInfoDto.getOldNewDemandIdMap();
        Map<Long, Long> oldNewMaterialIdMap = copyInfoDto.getOldNewMaterialIdMap();
        Map<Long, Long> oldNewCraftIdMap = copyInfoDto.getOldNewCraftIdMap();
        Map<Long, Long> oldNewSpecialIdMap = copyInfoDto.getOldNewSpecialIdMap();

        //将暂存bom中的bomId指向新的bomId
        BomOrderTransient transientBomUpdate = new  BomOrderTransient();
        transientBomUpdate.setBomTransientId(bomTransientId);
        transientBomUpdate.setBomId(newBomId);
        bomOrderTransientRepository.updateById(transientBomUpdate);

        //暂存需求指向新的bomId,关联新的原需求id
        List<BomMaterialDemandTransient> demandTransientList = bomMaterialDemandTransientRepository.listByBomTransientId(bomTransientId);
        List<BomMaterialDemandTransient> demandUpdateList = new LinkedList<>();
        demandTransientList.forEach(transientDemand -> {
            BomMaterialDemandTransient updateDemand = new BomMaterialDemandTransient();
            updateDemand.setBomMaterialDemandId(transientDemand.getBomMaterialDemandId());
            updateDemand.setBomId(newBomId);
            Long originDemandId = transientDemand.getOriginDemandId();
            if (Objects.nonNull(originDemandId)) {
                Long newOriginDemandId = oldNewDemandIdMap.get(originDemandId);
                updateDemand.setOriginDemandId(newOriginDemandId);
            }
            demandUpdateList.add(updateDemand);
        });
        if (CollUtil.isNotEmpty(demandUpdateList)) {
            bomMaterialDemandTransientRepository.updateBatchById(demandUpdateList);
        }

        //暂存物料指向新的bomId,关联新的原物料id
        List<BomOrderMaterialTransient> materialTransientList = bomOrderMaterialTransientRepository.listByBomTransientId(bomTransientId);
        List<BomOrderMaterialTransient> materialUpdateList = new LinkedList<>();
        materialTransientList.forEach(transientMaterial -> {
            BomOrderMaterialTransient updateMaterial = new BomOrderMaterialTransient();
            updateMaterial.setBomMaterialId(transientMaterial.getBomMaterialId());
            updateMaterial.setBomId(newBomId);
            Long originMaterialId = transientMaterial.getOriginMaterialId();
            if (Objects.nonNull(originMaterialId)) {
                Long newOriginMaterialId = oldNewMaterialIdMap.get(originMaterialId);
                updateMaterial.setOriginMaterialId(newOriginMaterialId);
            }
            materialUpdateList.add(updateMaterial);
        });
        if (CollUtil.isNotEmpty(materialUpdateList)) {
            bomOrderMaterialTransientRepository.updateBatchById(materialUpdateList);
        }

        //暂存工艺指向新的bomId, 关联新的原工艺id (暂存物料id没变)
        List<CraftDemandInfoTransient> craftTransientList = craftDemandInfoTransientRepository.listByBomTransientId(bomTransientId);
        //查询并更新最新的工艺周期
        this.prepareAndUpdateCraftCycleTransient(craftTransientList);

        List<CraftDemandInfoTransient> craftUpdateList = new LinkedList<>();
        craftTransientList.forEach(transientCraft -> {
            CraftDemandInfoTransient updateCraft = new CraftDemandInfoTransient();
            updateCraft.setCraftDemandId(transientCraft.getCraftDemandId());
            updateCraft.setBomId(newBomId);
            Long originCraftId = transientCraft.getOriginCraftDemandId();
            if (Objects.nonNull(originCraftId)) {
                Long newOriginMaterialId = oldNewCraftIdMap.get(originCraftId);
                updateCraft.setOriginCraftDemandId(newOriginMaterialId);
            }
            craftUpdateList.add(updateCraft);
        });
        if (CollUtil.isNotEmpty(craftUpdateList)) {
            craftDemandInfoTransientRepository.updateBatchById(craftUpdateList);
        }

        //暂存特辅指向新的bomId,关联新的原特辅id
        List<SpecialAccessoriesTransient> specialTransientList = specialAccessoriesTransientRepository.listByBomTransientId(bomTransientId);
        List<SpecialAccessoriesTransient> specialUpdateList = new LinkedList<>();
        specialTransientList.forEach(transientSpecial -> {
            SpecialAccessoriesTransient updateSpecial = new SpecialAccessoriesTransient();
            updateSpecial.setSpecialAccessoriesId(transientSpecial.getSpecialAccessoriesId());
            updateSpecial.setBomId(newBomId);
            Long originSpecialId = transientSpecial.getOriginSpecialId();
            if (Objects.nonNull(originSpecialId)) {
                Long newOriginMaterialId = oldNewSpecialIdMap.get(originSpecialId);
                updateSpecial.setOriginSpecialId(newOriginMaterialId);
            }
            specialUpdateList.add(updateSpecial);
        });
        if (CollUtil.isNotEmpty(specialUpdateList)) {
            specialAccessoriesTransientRepository.updateBatchById(specialUpdateList);
        }

        //因为暂存bomId与对应的暂存物料id没有变, 暂存备注不需要更新
    }


    private SpecialHandleTypeEnum getHandleTypeByCompareTime(BomOrder bomOrder, List<SpecialAccessories> specialList) {
        SdpDesignException.notEmpty(specialList, "getHandleTypeByCompareTime-特辅辅料为空!");
        SpecialAccessories latestSpecial = specialList.stream().max(Comparator.comparing(SpecialAccessories::getRevisedTime)).orElse(null);
        //bom特辅更新了, 使用原特辅, 不新增引用的特辅
        if (latestSpecial.getRevisedTime().isAfter(bomOrder.getRevisedTime())) {
            return SpecialHandleTypeEnum.ORIGIN_NOT_QUOTE;
        }
        //bom特辅在暂存操作前没有更新, 使用页面提交的特辅, 关闭原有特辅
        else {
            return SpecialHandleTypeEnum.QUOTE_CLOSE_ORIGIN;
        }
    }

    private MaterialDetailReq buildSupplyChainMaterialReq(Prototype prototype, BomOrderMaterialTransient materialTransient, MaterialSnapshot materialSnapshot) {
        MaterialDetailReq materialDetailReq = new MaterialDetailReq();
        materialDetailReq.setMaterialSnapshotId(materialSnapshot.getMaterialSnapshotId());
        materialDetailReq.setSkuId(materialSnapshot.getSkuId());
        materialDetailReq.setSpuId(materialSnapshot.getCommodityId());
        materialDetailReq.setSpuCode(materialSnapshot.getCommodityCode());
        materialDetailReq.setCategoryName(materialSnapshot.getCategoryName());
        materialDetailReq.setDemandTag(materialTransient.getPrototypeMaterialName());
        materialDetailReq.setCommodityType(materialSnapshot.getCommodityType());
        materialDetailReq.setDemandChannel(DemandChannelEnum.DESIGNER_CHOOSE_MATERIAL_NO_DEMAND.getCode());
        materialDetailReq.setBusinessType(BusinessTypeEnum.PROTOTYPE.getCode());
        materialDetailReq.setStyleCode(prototype.getStyleCode());
        materialDetailReq.setDesignCode(prototype.getDesignCode());
        return materialDetailReq;
    }


    /**
     * 获取供应商信息
     */
    @Override
    public Map<Long, CommoditySupplierInfoVo> getSupplierInfo(List<Long> supplierIds) {
        if (CollectionUtil.isEmpty(supplierIds)) {
            return Collections.emptyMap();
        }

        Map<Long, CommoditySupplierInfoVo> supplierInfoVoMap = new HashMap<>();
        try {
            List<CommoditySupplierInfoVo> supplierInfoList = supplierInfoRemoteHelper.getSupplierBaseInfo(supplierIds);
            supplierInfoVoMap = supplierInfoList.stream().collect(Collectors.toMap(CommoditySupplierInfoVo::getSupplierId, Function.identity(), (k1, k2) -> k1));
        } catch (Exception e) {
            log.warn("获取供应商异常", e);
        }
        return supplierInfoVoMap;
    }


    /**
     * 封装 新增与更新的面辅料对应的skuIdMap
     */
    private Map<Integer, Set<Long>> buildSkuIdMap(BomMaterialHandleReq req) {
        Map<Integer, Set<Long>> bomMaterialTypeMap1 = req.getAddBomMaterials().stream().collect(
                Collectors.groupingBy(BomOrderUpdateV3Req.AddBomMaterialReq::getDemandType, Collectors.mapping(BomOrderUpdateV3Req.AddBomMaterialReq::getSkuId, Collectors.toSet())));

        Map<Integer, Set<Long>> bomMaterialTypeMap2 = req.getUpdateBomMaterials().stream().collect(
                Collectors.groupingBy(BomOrderUpdateV3Req.UpdateBomMaterialReq::getDemandType, Collectors.mapping(BomOrderUpdateV3Req.UpdateBomMaterialReq::getSkuId, Collectors.toSet())));

        //key为商品类型,value的商品ID
        Map<Integer, Set<Long>> bomMaterialTypeMap = new HashMap<>(16);
        Stream.of(bomMaterialTypeMap1, bomMaterialTypeMap2).flatMap(map -> map.entrySet().stream()).forEach(entry -> {
            Integer key = entry.getKey();
            Set<Long> newValue = entry.getValue();
            if (bomMaterialTypeMap.containsKey(key)) {
                Set<Long> originValue = bomMaterialTypeMap.get(key);
                originValue.addAll(newValue);
                bomMaterialTypeMap.put(key, originValue);
            } else {
                bomMaterialTypeMap.put(key, newValue);
            }
        });
        return bomMaterialTypeMap;
    }


    private Map<Integer, Set<Long>> buildSkuIdMap4TransientSubmit(List<BomOrderMaterialTransient> addTransientMaterialList,
                                                           List<BomOrderMaterialTransient> updateTransientMaterialList) {
        Set<Long> materialSnapshotIdSet = new HashSet<>();
        if (CollUtil.isNotEmpty(addTransientMaterialList)) {
            Set<Long> addSnapshotIdSet = addTransientMaterialList.stream().filter(item -> Objects.nonNull(item.getMaterialSnapshotId())).map(BomOrderMaterialTransient::getMaterialSnapshotId).collect(Collectors.toSet());
            materialSnapshotIdSet.addAll(addSnapshotIdSet);
        }
        if (CollUtil.isNotEmpty(updateTransientMaterialList)) {
            Set<Long> updateSnapshotIdSet = updateTransientMaterialList.stream().filter(item -> Objects.nonNull(item.getMaterialSnapshotId())).map(BomOrderMaterialTransient::getMaterialSnapshotId).collect(Collectors.toSet());
            materialSnapshotIdSet.addAll(updateSnapshotIdSet);
        }
        SdpDesignException.notEmpty(materialSnapshotIdSet, "物料快照id为空-buildSkuIdMapTransient");

        List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(materialSnapshotIdSet);

        Map<Integer, Set<Long>> materialTypeMap = snapshotList.stream().collect(
                Collectors.groupingBy(MaterialSnapshot::getMaterialType, Collectors.mapping(MaterialSnapshot::getSkuId, Collectors.toSet())));

        //key为商品类型,value的商品ID
        Map<Integer, Set<Long>> bomMaterialTypeMap = new HashMap<>(16);
        Stream.of(materialTypeMap).flatMap(map -> map.entrySet().stream()).forEach(entry -> {
            Integer key = entry.getKey();
            Set<Long> newValue = entry.getValue();
            if (bomMaterialTypeMap.containsKey(key)) {
                Set<Long> originValue = bomMaterialTypeMap.get(key);
                originValue.addAll(newValue);
                bomMaterialTypeMap.put(key, originValue);
            } else {
                bomMaterialTypeMap.put(key, newValue);
            }
        });
        return bomMaterialTypeMap;
    }

    /**
     * 检查辅料商品必填参数
     */
    private void checkAccessoriesSkuParam(Map<Long, ProductSkuVo> accessoriesSkuMap,
                                          Map<Long, ProductSpuInfoVo> accessoriesSpuMap,
                                          Long bomOrderId,
                                          BomMaterialHandleReq req) {

        //校验新添加辅料sku必有属性
        List<BomOrderUpdateV3Req.AddBomMaterialReq> addBomMaterials = req.getAddBomMaterials();
        if (CollectionUtil.isNotEmpty(addBomMaterials)) {
            addBomMaterials.stream().filter(materialReq -> Objects.equals(materialReq.getDemandType(), MaterialDemandTypeEnum.ACCESSORIES.getCode())).forEach(materialReq -> {
                ProductSkuVo productSkuVo = accessoriesSkuMap.get(materialReq.getSkuId());
                SdpDesignException.notNull(productSkuVo, "履约辅料sku不存在! skuCode:{}; skuId:{}", materialReq.getSkuCode(), materialReq.getSkuId());
                ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(materialReq.getCommodityId());
                SdpDesignException.notNull(productSpuInfoVo, "履约辅料spu不存在! skuCode:{}; skuId:{}", materialReq.getCommodityCode(), materialReq.getCommodityId());
                SdpDesignException.isTrue(Objects.equals(productSpuInfoVo.getOnShelf(), Bool.YES.getCode()), "{} 的辅料商品 {} 已下架,请重新添加", materialReq.getPrototypeMaterialName(), productSkuVo.getSkuCode());
                SdpDesignException.isTrue(Objects.equals(productSpuInfoVo.getEnabled(), Bool.YES.getCode()), "{} 的辅料商品 {} 未启用,请重新添加", materialReq.getPrototypeMaterialName(), productSkuVo.getSkuCode());
                SdpDesignException.isTrue(Objects.nonNull(productSkuVo.getMinPrice()) && StringUtils.isNotBlank(productSkuVo.getMinUnit()), "{} 辅料商品 {} 缺少价格最小价格与单位,请联系物料管理员完善后再添加", materialReq.getPrototypeMaterialName(), productSkuVo.getSkuCode());
            });
        }

        //校验辅料更改规则sku必有属性
        List<BomOrderUpdateV3Req.UpdateBomMaterialReq> updateBomMaterials = req.getUpdateBomMaterials();
        if (CollectionUtil.isNotEmpty(updateBomMaterials)) {

            Map<Long, BomOrderMaterial> bomOrderMaterialMap = bomOrderMaterialRepository.getListByBomId(bomOrderId).stream()
                    .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
            updateBomMaterials.stream().filter(materialReq -> Objects.equals(materialReq.getDemandType(), MaterialDemandTypeEnum.ACCESSORIES.getCode()))
                    .forEach(materialReq -> {
                        if (CollUtil.isEmpty(bomOrderMaterialMap) || Objects.isNull(bomOrderMaterialMap.get(materialReq.getBomMaterialId()))) {
                            return;
                        }
                        BomOrderMaterial oldBomMaterial = bomOrderMaterialMap.get(materialReq.getBomMaterialId());
                        //查询物料的skuId有无变化,有改变则是更改规格了,得创建的新物料
                        MaterialSnapshotVo materialSnapshotVo = materialSnapshotService.getById(oldBomMaterial.getMaterialSnapshotId());
                        if (Objects.nonNull(materialSnapshotVo) && !Objects.equals(materialSnapshotVo.getSkuId(), materialReq.getSkuId())) {
                            ProductSkuVo productSkuVo = accessoriesSkuMap.get(materialReq.getSkuId());
                            SdpDesignException.notNull(productSkuVo, "履约辅料sku不存在! skuCode:{}; skuId:{}", materialReq.getSkuCode(), materialReq.getSkuId());
                            ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(materialReq.getCommodityId());
                            SdpDesignException.notNull(productSpuInfoVo, "履约辅料spu不存在! skuCode:{}; skuId:{}", materialReq.getCommodityCode(), materialReq.getCommodityId());
                            SdpDesignException.isTrue(Objects.equals(productSpuInfoVo.getOnShelf(), Bool.YES.getCode()), "{} 的辅料商品 {} 已下架,请重新添加", materialReq.getPrototypeMaterialName(), productSkuVo.getSkuCode());
                            SdpDesignException.isTrue(Objects.equals(productSpuInfoVo.getEnabled(), Bool.YES.getCode()), "{} 的辅料商品 {} 未启用,请重新添加", materialReq.getPrototypeMaterialName(), productSkuVo.getSkuCode());
                            SdpDesignException.isTrue(Objects.nonNull(productSkuVo.getMinPrice()) && StringUtils.isNotBlank(productSkuVo.getMinUnit()), "{} 辅料商品 {} 缺少价格最小价格与单位,请联系物料管理员完善后再添加", materialReq.getPrototypeMaterialName(), productSkuVo.getSkuCode());
                        }
                    });
        }
    }

    /**
     * 检查面料商品必填参数
     */
    private void checkFabricSkuParam(Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap,
                                     Long bomOrderId,
                                     BomMaterialHandleReq req) {
        //校验新添加面料sku必有属性
        List<BomOrderUpdateV3Req.AddBomMaterialReq> addBomMaterials = req.getAddBomMaterials();
        if (CollectionUtil.isNotEmpty(addBomMaterials)) {
            addBomMaterials.stream().filter(materialReq -> Objects.equals(materialReq.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())).forEach(materialReq -> {
                CommoditySkuCollectionRespVo.Sku skuVo = fabricSkuMap.get(materialReq.getSpuSkuId());
                SdpDesignException.notNull(skuVo, "履约面料sku不存在! skuCode:{}; skuId:{}", materialReq.getSkuCode(), materialReq.getSkuId());
                SdpDesignException.isTrue(Objects.equals(skuVo.getIsHouliu(),Bool.YES.getCode()), "{} 面料商品 {} 已下架，请重新添加", materialReq.getPrototypeMaterialName(), skuVo.getSkuCode());
                SdpDesignException.isTrue(StringUtils.equals(skuVo.getIsEnable(), String.valueOf(Bool.YES.getCode())), "{} 面料商品 {} 未启用，请重新添加", materialReq.getPrototypeMaterialName(), skuVo.getSkuCode());
                SdpDesignException.isTrue(CollectionUtil.isNotEmpty(skuVo.getSkuPriceVos()), "{} 面料商品 {} 缺少价格足米价与单位,请联系物料管理员完善后再添加", materialReq.getPrototypeMaterialName(), skuVo.getSkuCode());
                CommoditySkuCollectionRespVo.Sku.SkuPriceVo nationSkuPriceVo = skuVo.getSkuPriceVos().stream().filter(skuPriceVo -> Objects.equals(skuPriceVo.getSalesRegionId(), DefaultSalesRegionEnum.NATION.getRegionId())).findFirst().orElse(null);
                SdpDesignException.isTrue(Objects.nonNull(nationSkuPriceVo) && Objects.nonNull(nationSkuPriceVo.getMeterPrice()), "{} 面料商品 {} 缺少价格足米价与单位,请联系物料管理员完善后再添加", materialReq.getPrototypeMaterialName(), skuVo.getSkuCode());
            });
        }

        //校验面料更改规格sku必有属性
        List<BomOrderUpdateV3Req.UpdateBomMaterialReq> updateBomMaterials = req.getUpdateBomMaterials();
        if (CollectionUtil.isNotEmpty(updateBomMaterials)) {

            Map<Long, BomOrderMaterial> bomOrderMaterialMap = bomOrderMaterialRepository.getListByBomId(bomOrderId).stream()
                    .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
            updateBomMaterials.stream().filter(materialReq -> Objects.equals(materialReq.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode()))
                    .forEach(materialReq -> {
                        if (CollUtil.isEmpty(bomOrderMaterialMap) || Objects.isNull(bomOrderMaterialMap.get(materialReq.getBomMaterialId()))) {
                            return;
                        }
                        BomOrderMaterial oldBomMaterial = bomOrderMaterialMap.get(materialReq.getBomMaterialId());
                        //查询物料的skuId有无变化,有改变则是更改规格了,得创建的新物料
                        MaterialSnapshotVo materialSnapshotVo = materialSnapshotService.getById(oldBomMaterial.getMaterialSnapshotId());
                        if (Objects.nonNull(materialSnapshotVo) && !Objects.equals(materialSnapshotVo.getSkuId(), materialReq.getSkuId())) {
                            CommoditySkuCollectionRespVo.Sku skuVo = fabricSkuMap.get(materialReq.getSpuSkuId());
                            SdpDesignException.notNull(skuVo, "履约面料sku不存在! skuCode:{}; skuId:{}", materialReq.getSkuCode(), materialReq.getSkuId());
                            SdpDesignException.isTrue(Objects.equals(skuVo.getIsHouliu(), Bool.YES.getCode()), "{} 面料商品 {} 已下架，请重新添加", materialReq.getPrototypeMaterialName(), skuVo.getSkuCode());
                            SdpDesignException.isTrue(StringUtils.equals(skuVo.getIsEnable(), String.valueOf(Bool.YES.getCode())), "{} 面料商品 {} 未启用，请重新添加", materialReq.getPrototypeMaterialName(), skuVo.getSkuCode());
                            SdpDesignException.isTrue(CollectionUtil.isNotEmpty(skuVo.getSkuPriceVos()), "{} 面料商品 {} 缺少足米价与单位,请联系物料管理员完善后再添加", materialReq.getPrototypeMaterialName(), skuVo.getSkuCode());
                            CommoditySkuCollectionRespVo.Sku.SkuPriceVo nationSkuPriceVo = skuVo.getSkuPriceVos().stream().filter(skuPriceVo -> Objects.equals(skuPriceVo.getSalesRegionId(), DefaultSalesRegionEnum.NATION.getRegionId())).findFirst().orElse(null);
                            SdpDesignException.isTrue(Objects.nonNull(nationSkuPriceVo) && Objects.nonNull(nationSkuPriceVo.getMeterPrice()) && Objects.nonNull(skuVo.getUnit()), "{} 面料商品 {} 缺少全国足米价与单位,请联系物料管理员完善后再添加", materialReq.getPrototypeMaterialName(), skuVo.getSkuCode());
                        }
                    });
        }
    }


    /**
     * 检查辅料商品必填参数-暂存
     */
    private void checkAccessoriesSkuParamTransient(Map<Long, ProductSkuVo> accessoriesSkuMap,
                                                   Map<Long, ProductSpuInfoVo> accessoriesSpuMap,
                                                   BomOrderTransient transientBom,
                                                   BomMaterialHandleReq req) {

        //校验新添加辅料sku必有属性
        List<BomOrderUpdateV3Req.AddBomMaterialReq> addBomMaterials = req.getAddBomMaterials();
        if (CollectionUtil.isNotEmpty(addBomMaterials)) {
            addBomMaterials.stream().filter(materialReq -> Objects.equals(materialReq.getDemandType(), MaterialDemandTypeEnum.ACCESSORIES.getCode())).forEach(materialReq -> {
                ProductSkuVo productSkuVo = accessoriesSkuMap.get(materialReq.getSkuId());
                SdpDesignException.notNull(productSkuVo, "履约辅料sku不存在! skuCode:{}; skuId:{}", materialReq.getSkuCode(), materialReq.getSkuId());
                ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(materialReq.getCommodityId());
                SdpDesignException.notNull(productSpuInfoVo, "履约辅料spu不存在! skuCode:{}; skuId:{}", materialReq.getCommodityCode(), materialReq.getCommodityId());
            });
        }

        //校验辅料更改规则sku必有属性
        List<BomOrderUpdateV3Req.UpdateBomMaterialReq> updateBomMaterials = req.getUpdateBomMaterials();
        if (CollectionUtil.isNotEmpty(updateBomMaterials)) {

            Map<Long, BomOrderMaterialTransient> bomOrderMaterialMap = bomOrderMaterialTransientRepository.listByBomTransientId(transientBom.getBomTransientId()).stream()
                    .collect(Collectors.toMap(BomOrderMaterialTransient::getBomMaterialId, Function.identity(), (k1, k2) -> k1));
            updateBomMaterials.stream().filter(materialReq -> Objects.equals(materialReq.getDemandType(), MaterialDemandTypeEnum.ACCESSORIES.getCode()))
                    .forEach(materialReq -> {
                        if (CollUtil.isEmpty(bomOrderMaterialMap) || Objects.isNull(bomOrderMaterialMap.get(materialReq.getBomMaterialId()))) {
                            return;
                        }
                        BomOrderMaterialTransient oldBomMaterial = bomOrderMaterialMap.get(materialReq.getBomMaterialId());
                        //查询物料的skuId有无变化,有改变则是更改规格了,得创建的新物料
                        MaterialSnapshotVo materialSnapshotVo = materialSnapshotService.getById(oldBomMaterial.getMaterialSnapshotId());
                        if (!Objects.equals(materialSnapshotVo.getSkuId(), materialReq.getSkuId())) {
                            ProductSkuVo productSkuVo = accessoriesSkuMap.get(materialReq.getSkuId());
                            SdpDesignException.notNull(productSkuVo, "履约辅料sku不存在! skuCode:{}; skuId:{}", materialReq.getSkuCode(), materialReq.getSkuId());
                            ProductSpuInfoVo productSpuInfoVo = accessoriesSpuMap.get(materialReq.getCommodityId());
                            SdpDesignException.notNull(productSpuInfoVo, "履约辅料spu不存在! skuCode:{}; skuId:{}", materialReq.getCommodityCode(), materialReq.getCommodityId());
                        }
                    });
        }
    }


    /**
     * 检查面料商品必填参数-暂存
     */
    private void checkFabricSkuParamTransient(Map<String, CommoditySkuCollectionRespVo.Sku> fabricSkuMap,
                                              BomOrderTransient transientBom,
                                              BomMaterialHandleReq req) {
        //校验新添加面料sku必有属性
        List<BomOrderUpdateV3Req.AddBomMaterialReq> addBomMaterials = req.getAddBomMaterials();
        if (CollectionUtil.isNotEmpty(addBomMaterials)) {
            addBomMaterials.stream().filter(materialReq -> Objects.equals(materialReq.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode())).forEach(materialReq -> {
                CommoditySkuCollectionRespVo.Sku skuVo = fabricSkuMap.get(materialReq.getSpuSkuId());
                SdpDesignException.notNull(skuVo, "履约面料sku不存在! skuCode:{}; skuId:{}", materialReq.getSkuCode(), materialReq.getSkuId());
            });
        }

        //校验面料更改规格sku必有属性
        List<BomOrderUpdateV3Req.UpdateBomMaterialReq> updateBomMaterials = req.getUpdateBomMaterials();
        if (CollectionUtil.isNotEmpty(updateBomMaterials)) {

            Map<Long, BomOrderMaterialTransient> bomOrderMaterialMap = bomOrderMaterialTransientRepository.listByBomTransientId(transientBom.getBomTransientId()).stream()
                    .collect(Collectors.toMap(BomOrderMaterialTransient::getBomMaterialId, Function.identity(), (k1, k2) -> k1));

            updateBomMaterials.stream().filter(materialReq -> Objects.equals(materialReq.getDemandType(), MaterialDemandTypeEnum.FABRIC.getCode()))
                    .forEach(materialReq -> {
                        if (CollUtil.isEmpty(bomOrderMaterialMap) || Objects.isNull(bomOrderMaterialMap.get(materialReq.getBomMaterialId()))) {
                            return;
                        }
                        BomOrderMaterialTransient oldBomMaterial = bomOrderMaterialMap.get(materialReq.getBomMaterialId());
                        //查询物料的skuId有无变化,有改变则是更改规格了,得创建的新物料
                        MaterialSnapshotVo materialSnapshotVo = materialSnapshotService.getById(oldBomMaterial.getMaterialSnapshotId());
                        if (!Objects.equals(materialSnapshotVo.getSkuId(), materialReq.getSkuId())) {
                            CommoditySkuCollectionRespVo.Sku skuVo = fabricSkuMap.get(materialReq.getSpuSkuId());
                            SdpDesignException.notNull(skuVo, "履约面料sku不存在! skuCode:{}; skuId:{}", materialReq.getSkuCode(), materialReq.getSkuId());
                        }
                    });
        }
    }
}
