package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.DesignLogService;
import tech.tiangong.sdp.design.vo.req.log.DesignLogBizListReq;
import tech.tiangong.sdp.design.vo.req.log.DesignLogListReq;
import tech.tiangong.sdp.design.vo.resp.log.DesignLogVO;

import java.util.List;

/**
 * 操作日志-web
 * <br>CreateDate August 10,2021
 *
 * <AUTHOR>
 * @since 1.0
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/design/log")
public class DesignLogController extends BaseController {

    private final DesignLogService designLogService;

    /**
     * SKC日志查询
     *
     * @param req 查询入参
     * @return 设计打版操作日志实体
     */
    @PostMapping("/list")
    public DataResponse<List<DesignLogVO>> dateList(@RequestBody @Validated DesignLogListReq req) {
        return DataResponse.ok(designLogService.dateList(req));
    }


    /**
     * 日志查询
     *  调用页面: 灵感任务, 数码印花款, 采购齐套管理, 采购申请管理, 现货管理
     *
     * @param req 查询入参
     * @return 设计打版操作日志实体
     */
    @PostMapping("/biz/list")
    public DataResponse<List<DesignLogVO>> dateBizList(@RequestBody @Validated DesignLogBizListReq req) {
        return DataResponse.ok(designLogService.dateBizList(req));
    }

}
