package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.clothes.vo.req.DemolitionUpdateSampleClothesReq;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.converter.PrototypeConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.helper.StyleLibraryHelper;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.SampleClothesRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.req.DemolitionGenerateBomReq;
import tech.tiangong.sdp.design.vo.req.identify.SpuIdentifyAddReq;
import tech.tiangong.sdp.design.vo.req.log.DesignLogReq;
import tech.tiangong.sdp.design.vo.req.mq.DesignPrototypeMqDTO;
import tech.tiangong.sdp.design.vo.req.prototype.PrototypeOperateReq;
import tech.tiangong.sdp.design.vo.req.prototype.SpuVisualDemandReq;
import tech.tiangong.sdp.design.vo.req.prototype.inner.PushSpuSkc2ZjReq;
import tech.tiangong.sdp.design.vo.req.visual.SpuVisualDemandRecordSaveReq;
import tech.tiangong.sdp.design.vo.resp.prototype.MakeSameDesignCodeVo;
import tech.tiangong.sdp.design.vo.resp.prototype.PrototypeSubmitVisualCheckVo;
import tech.tiangong.sdp.design.vo.resp.prototype.PrototypeSubmitVo;
import tech.tiangong.sdp.utils.AsyncTask;
import tech.tiangong.sdp.utils.Bool;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * skc-提交服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrototypeOperateServiceImpl implements PrototypeOperateService {
    private final PrototypeRepository prototypeRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final PrototypeHistoryRepository prototypeHistoryRepository;
    private final DesignLogService designLogService;
    private final SampleClothesRemoteHelper sampleClothesRemoteHelper;
    private final MqProducer mqProducer;
    private final DesignStyleRepository designStyleRepository;
    private final BomOrderRepository bomOrderRepository;
    @Lazy
    @Resource
    private BomOrderService bomOrderService;
    @Lazy
    @Resource
    private DesignStyleService designStyleService;
    private final PopProductHelper popProductHelper;
    private final StyleLibraryHelper styleLibraryHelper;
    private final SpuVisualDemandRecordService spuVisualDemandRecordService;
    private final SpuVisualDemandRecordRepository spuVisualDemandRecordRepository;
    private final VisualImagePackageRepository visualImagePackageRepository;
    private final VisualTaskRepository visualTaskRepository;
    private final SpuIdentifyRepository spuIdentifyRepository;
    private final SpuIdentifyService spuIdentifyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrototypeSubmitVo save(PrototypeOperateReq prototypeReq) {
        log.info("拆板提交-请求参数 req:{}", JSON.toJSONString(prototypeReq));

        Prototype prototype = prototypeRepository.getByDesignCode(prototypeReq.getDesignCode());
        String designCode = prototype.getDesignCode();
        String oldColor = prototype.getColor();

        //默认选中SPU中维护的尺码组且不允许更改尺码组，用户可选择该尺码组下的尺码信息
        DesignStyle byStyleCode = designStyleRepository.getByStyleCode(prototype.getStyleCode());
        SdpDesignException.isTrue(prototypeReq.getSizeStandardCode().equals(byStyleCode.getSizeStandardCode()), "尺码组要与SPU一致!");

        //校验
        this.validateSave(prototypeReq, prototype, byStyleCode);

        //保存
        this.updatePrototypeSave(prototypeReq, prototype, byStyleCode);

        PushSpuSkc2ZjReq spuSkc2ZjReq = new PushSpuSkc2ZjReq();
        spuSkc2ZjReq.setDesignCodeList(Collections.singletonList(designCode));
        spuSkc2ZjReq.setPushZjTypeEnum(SpuSkcPushTypeEnum.SKC);

        //第一次拆版生成BOM单
        if (prototype.getVersionNum() == 1) {
            DemolitionGenerateBomReq req = new DemolitionGenerateBomReq();
            req.setDesignCode(designCode);
            req.setPrototypeId(prototype.getPrototypeId());
            log.info("拆板提交-第一次拆板-生成bom单 req:{}", JSON.toJSONString(req));
            bomOrderService.demolitionGenerateBom(req);

            spuSkc2ZjReq.setPushZjTypeEnum(SpuSkcPushTypeEnum.SPU_SKC);

            //提交视觉需求记录
            this.addVisualDemandRecord(prototypeReq, prototype);
        }

        //发起款式识别
        this.addSpuIdentify(prototype, prototypeReq.getDesignPicture());

        //推送SPU与SKC信息到致景
        designStyleService.pushSpuSkc2Zj(spuSkc2ZjReq);

        //拆板信息同步到打版
        this.handlerDemolitionSubmitToClothes(prototypeReq);

        //颜色变更推送POP
        if (!Objects.equals(prototypeReq.getColor(), oldColor)) {
            popProductHelper.updateDesignColor(prototype);
        }

        log.info("拆板提交-成功 designCode:{}; latestVersionNum:{}", designCode, prototypeReq.getLatestVersionNum());

        //返回skc下最新版本的bomId
        BomOrder bomOrder = bomOrderRepository.getLatestBomByDesignCode(designCode);
        SdpDesignException.notNull(bomOrder, "bom单不存在! ");

        return new PrototypeSubmitVo()
                .setDesignCode(designCode)
                .setLatestVersionBomId(bomOrder.getBomId())
                .setBomCode(bomOrder.getBomCode());

    }

    private void addSpuIdentify(Prototype prototype, List<String> designPicture) {
        //判断当前skc是否为spu下未取消的第一个创建的, 如果不是就不处理
        List<Prototype> prototypeList = prototypeRepository.listByStyleCode(prototype.getStyleCode());
        if (CollUtil.isEmpty(prototypeList)) {
            return;
        }
        //过滤未取消的, 按创建时间排序, 最早创建的skc
        Prototype firstPrototype = prototypeList.stream().filter(p -> !p.getIsCanceled())
                .min(Comparator.comparing(Prototype::getCreatedTime)).orElse(null);

        //当前skc不是spu下未取消的第一个创建的skc，不处理
        if (Objects.isNull(firstPrototype) || !Objects.equals(firstPrototype.getDesignCode(), prototype.getDesignCode())) {
            log.info("当前skc不是spu下未取消的第一个创建的skc，不处理, 不发起款式识别");
            return;
        }

        //取spu下第一个skc的第一张图发起款式识别
        String identifyPicture = designPicture.getFirst();
        if (StrUtil.isBlank(identifyPicture)) {
            log.info("设计图为空, 不发起款式识别");
            return;
        }

        //根据spu查询款式识别记录是否存在
        List<SpuIdentify> spuIdentifyList = spuIdentifyRepository.listByBizCodeSource(
                Collections.singletonList(prototype.getStyleCode()), SpuIdentifySourceEnum.DESIGN_STYLE.getCode(), null
        );

        Boolean identifyFlag = Boolean.FALSE;
        //不存在则对skc图发起款式识别
        if (CollUtil.isEmpty(spuIdentifyList)) {
            identifyFlag = Boolean.TRUE;
        }
        //已存在, 对比skc图与识别记录的识别图是否一致, 如果不一致, 发起款式识别
        else {
            String oldIdentifyUrl = spuIdentifyList.getFirst().getIdentifyUrl();
            if (!Objects.equals(oldIdentifyUrl, identifyPicture)) {
                identifyFlag = Boolean.TRUE;
            }
        }

        //发起款式识别
        if (identifyFlag) {
            SpuIdentifyAddReq identifyAddReq = SpuIdentifyAddReq.builder()
                    .sourceType(SpuIdentifySourceEnum.DESIGN_STYLE.getCode())
                    .bizId(prototype.getPrototypeId()).bizCode(prototype.getStyleCode())
                    .identifyUrl(identifyPicture)
                    .build();
            log.info("=== skc更新 发起款式识别 :{} ===", JSON.toJSONString(identifyAddReq));
            spuIdentifyService.batchAddAsync(Collections.singletonList(identifyAddReq));
        }
    }

    private void addVisualDemandRecord(PrototypeOperateReq prototypeReq, Prototype prototype) {
        if (Objects.isNull(prototypeReq.getVisualDemandInfo())) {
            return;
        }
        SpuVisualDemandReq visualDemandReq = prototypeReq.getVisualDemandInfo();

        //新增需求提交记录
        SpuVisualDemandRecordSaveReq visualDemandRecord = SpuVisualDemandRecordSaveReq.builder()
                .styleCode(prototype.getStyleCode())
                .spuType(SdpStyleTypeEnum.DESIGN.getCode())
                .handleType(SpuVisualHandleTypeEnum.SKC_SUBMIT.getCode())
                .demandType(visualDemandReq.getDemandType())
                .realObjectColorState(visualDemandReq.getRealObjectColorState())
                .skcList(List.of(prototype.getDesignCode()))
                .demandImageList(visualDemandReq.getDemandImageList())
                .demandDesc(visualDemandReq.getDemandDesc())
                .modelPicList(visualDemandReq.getModelPicList())
                .backgroundPicList(visualDemandReq.getBackgroundPicList())
                .posturePicList(visualDemandReq.getPosturePicList())
                .modelReferenceImageList(prototypeReq.getVisualDemandInfo().getModelReferenceImageList())
                .backgroundImageList(prototypeReq.getVisualDemandInfo().getBackgroundImageList())
                .modelFaceImageList(prototypeReq.getVisualDemandInfo().getModelFaceImageList())
                .build();
        spuVisualDemandRecordService.create(visualDemandRecord);
    }

    @Override
    public PrototypeSubmitVisualCheckVo checkVisualTask(String designCode) {
        SdpDesignException.notBlank(designCode, "skc为空!");
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        SdpDesignException.notNull(prototype, "skc不存在!");

        PrototypeSubmitVisualCheckVo checkVo = new PrototypeSubmitVisualCheckVo();
        checkVo.setDesignCode(designCode);
        checkVo.setNeedVisualDemand(Boolean.FALSE);

        //skc不是待提交状态, 不需要视觉需求
        if (!Objects.equals(prototype.getPrototypeStatus(), PrototypeStatusEnum.WAIT_DECOMPOSE.getCode())) {
            return checkVo;
        }
        String styleCode = prototype.getStyleCode();

        //spu下存在其他已提交的skc, 不需要视觉需求
        List<Prototype> prototypeList = prototypeRepository.listByStyleCodes(Collections.singletonList(styleCode), PrototypeStatusEnum.DECOMPOSED.getCode());
        if (CollUtil.isNotEmpty(prototypeList)) {
            return checkVo;
        }

        //判断是否需要维护视觉需求: spu下首次skc提交 and 图片管理中无图包 and 无在途或完成的上新任务存在（提交未生成的也算有
        SpuVisualDemandRecord visualDemandRecord = spuVisualDemandRecordRepository.getLatestBySpu(styleCode);
        //不是spu首次提交视觉需求
        if (Objects.nonNull(visualDemandRecord)) {
            return checkVo;
        }

        //图片管理中有图包记录
        VisualImagePackage imagePackage = visualImagePackageRepository.getLatestByStyleCode(styleCode);
        if (Objects.nonNull(imagePackage)) {
            return checkVo;
        }

        //存在非取消的视觉任务
        List<VisualTask> visualTaskList = visualTaskRepository.queryLatestVisualTaskBySpu(styleCode);
        VisualTask noCancelTask = visualTaskList.stream()
                .filter(item -> Objects.equals(item.getIsCancel(), Bool.NO.getCode()))
                .findFirst().orElse(null);
        if (Objects.nonNull(noCancelTask)) {
            return checkVo;
        }

        //满足弹框条件
        checkVo.setNeedVisualDemand(Boolean.TRUE);
        return checkVo;
    }

    protected void validateSave(PrototypeOperateReq prototypeReq, Prototype prototype, DesignStyle designStyle) {
        //判断有效拆版
        validateValidPrototype(prototypeReq, prototype);
        //校验开发信息
        validateDevelopmentInformation(prototypeReq, prototype);

        //校验SPU必填信息
        if (Objects.nonNull(designStyle.getDesignDemandId())
                || Objects.equals(designStyle.getBizChannel(), BizChannelEnum.OLD_JV.getCode())) {
            //判断校验款式品类必须有四级
            this.validateCategory(designStyle.getCategory(), designStyle.getCategoryName());

            StringBuilder sb = new StringBuilder();
            if (StrUtil.isBlank(designStyle.getQualityLevel())) {
                sb.append("品质等级不能为空;");
            }
            if (StrUtil.isBlank(designStyle.getWeaveModeCode())) {
                sb.append("织造方式不能为空;");
            }
            if (StrUtil.isBlank(designStyle.getWaveBandCode())) {
                sb.append("波段不能为空;");
            }
            if (StrUtil.isBlank(designStyle.getSupplyModeCode())) {
                sb.append("供给方式不能为空;");
            }
            if (StrUtil.isBlank(designStyle.getSuggestedSellingPrice())) {
                sb.append("建议售价不能为空;");
            }
            if (StrUtil.isBlank(designStyle.getStyleSeason())) {
                sb.append("季节不能为空;");
            }
            if (StrUtil.isBlank(designStyle.getSizeStandard())) {
                sb.append("尺码组不能为空;");
            }
//            if (StrUtil.isBlank(designStyle.getClothingStyleCode())) {
//                sb.append("款式风格不能为空;");
//            }
            if (StrUtil.isBlank(designStyle.getFitCode())) {
                sb.append("合身不能为空;");
            }
            if (StrUtil.isBlank(designStyle.getElasticCode())) {
                sb.append("弹性不能为空;");
            }
//            if (StrUtil.isBlank(designStyle.getSceneCode())) {
//                sb.append("场景不能为空;");
//            }
            log.info("=== 当前SPU缺少必要信息: {} ===", sb);
            SdpDesignException.isBlank(sb.toString(), "当前SPU缺少必要信息,请先维护好SPU必填信息再提交当前SKC");
        }
        if (Objects.equals(SkcSourceTypeEnum.TAO_SKC_SOURCE.getCode(), prototype.getSkcSourceType())
                || Objects.equals(SkcSourceTypeEnum.lOGO_SKC_SOURCE.getCode(), prototype.getSkcSourceType())) {
            //判断校验款式品类必须有四级
            this.validateCategory(designStyle.getCategory(), designStyle.getCategoryName());

            StringBuilder sb = new StringBuilder();
            if (StrUtil.isBlank(designStyle.getQualityLevel())) {
                sb.append("品质等级不能为空;");
            }
            if (StrUtil.isBlank(designStyle.getStyleSeason())) {
                sb.append("季节不能为空;");
            }
            if (StrUtil.isBlank(designStyle.getWeaveModeCode())) {
                sb.append("织造方式不能为空;");
            }
            if (StrUtil.isBlank(designStyle.getSizeStandard())) {
                sb.append("尺码标准不能为空;");
            }
            log.info("=== 当前SPU缺少必要信息: {} ===", sb);
            SdpDesignException.isBlank(sb.toString(), "当前SPU缺少必要信息,请先维护好SPU必填信息再提交当前SKC");
        }

    }

    private void validateCategory(String category, String categoryName) {
        SdpDesignException.notBlank(category, "款式品类编码不能为空");
        SdpDesignException.notBlank(categoryName, "款式品类名不能为空");

        // List<String> categoryCodeList = StrUtil.splitTrim(category, StrUtil.DASHED);
        // SdpDesignException.notEmpty(categoryCodeList, "款式品类格式不对");
        // SdpDesignException.isTrue(categoryCodeList.size() >= 4, "款式名称必传,且必须有四级");
        //
        // List<String> categoryNameList = StrUtil.splitTrim(categoryName, StrUtil.DASHED);
        // SdpDesignException.notEmpty(categoryNameList, "款式品类格式不对");
        // SdpDesignException.isTrue(categoryNameList.size() >= 4, "款式名称必传,且必须有四级");
    }


    protected void updatePrototypeSave(PrototypeOperateReq prototypeReq, Prototype prototype, DesignStyle designStyle) {
        Long donePrototypeId = prototype.getPrototypeId();
        Long latestPrototypeId = prototype.getLatestPrototypeId();

        //设置已拆版
        prototype.setPrototypeStatus(PrototypeStatusEnum.DECOMPOSED.getCode());
        LocalDateTime submitTime = LocalDateTime.now();
        prototype.setSubmitTime(submitTime);
        String referenceDesignCode = prototypeReq.getReferenceDesignCode();
        if (referenceDesignCode.isBlank()) {
            referenceDesignCode = null;
        }
        //正常款校验参考款号
        else if (SkcTypeEnum.NORMAL.getCode().equals(prototype.getSkcType())) {
            validateRefDesignCode(referenceDesignCode);
        }
        prototype.setReferenceDesignCode(referenceDesignCode);
        DesignPrototypeMqDTO mqDTO = new DesignPrototypeMqDTO();
        mqDTO.setStyleCode(prototype.getStyleCode());
        mqDTO.setPrototypeId(prototype.getPrototypeId());
        mqDTO.setDesignCode(prototype.getDesignCode());
        mqDTO.setSourceType(designStyle.getSourceType());
        mqDTO.setWaveBandCode(designStyle.getWaveBandCode());
        mqDTO.setWaveBandName(designStyle.getWaveBandName());
        mqDTO.setSupplyModeName(designStyle.getSupplyModeName());
        mqDTO.setSupplyModeCode(designStyle.getSupplyModeCode());
        mqDTO.setSkcType(prototype.getSkcType());
        mqDTO.setBizChannel(designStyle.getBizChannel());
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.DESIGN_PROTOTYPE_INDEX_SEND,
                DesignMqConstant.DESIGN_PROTOTYPE_INDEX_EXCHANGE,
                JSON.toJSONString(mqDTO));
        log.info("=== 同步skc信息类索引字段-mqMessageReq:{} ===", JSON.toJSONString(mqMessageReq));
        //发送消息
        mqProducer.sendOnAfterCommit(mqMessageReq);


        //第一种情况：第一个版本，还没编辑完成。
        if (!prototype.getIsDoneVersion()) {
            log.info("【拆版】提交第一个版本.版单={} designCode={} ", latestPrototypeId, prototypeReq.getDesignCode());
            PrototypeConverter.composePrototypeReqToPrototype(prototypeReq, prototype, prototype.getSkcCreatedTime());
            prototype.setIsDoneVersion(Boolean.TRUE);
            //设置第一个版本的完成时间
            prototype.setFirstVersionDoneTime(LocalDateTime.now());

            PrototypeHistory prototypeHistory = prototypeHistoryRepository.getByPrototypeId(donePrototypeId);
            PrototypeConverter.composePrototypeReqToPrototypeHistory(prototypeReq, prototypeHistory, prototype.getSkcCreatedTime());
            //设置此版本完成
            prototypeHistory.setSubmitTime(submitTime);
            prototypeHistory.setIsDoneVersion(Boolean.TRUE);
            prototypeHistory.setPrototypeStatus(PrototypeStatusEnum.DECOMPOSED.getCode());

            prototypeHistoryRepository.updateById(prototypeHistory);
            prototypeRepository.updateById(prototype);

            //更新prototype_detail表
            PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(latestPrototypeId);
            PrototypeConverter.convertPrototypeDetail(prototypeDetail, prototypeReq);
            prototypeDetailRepository.updateById(prototypeDetail);

            //增加日志
            addLog(latestPrototypeId, prototype.getDesignCode(), "完成了【设计拆版】");

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    CompletableFuture.runAsync(AsyncTask.wrapper(() -> {
                        //请求向量新增
                        styleLibraryHelper.prototypeStyleLibrarySaveOrUpdate(prototype, designStyle.getCategoryName(), prototypeDetail, 1);
                    }));
                }
            });
            return;
        }

        long prototypeIdNew = IdPool.getId();
        log.info("【拆版】提交新增版本.版单={} designCode={}", prototypeIdNew, prototypeReq.getDesignCode());
        //第三种情况：如果不存在临时保存的版本，就需要新增一个版本
        Integer versionNumNew = prototype.getVersionNum() + 1;

        //更新主表的新增版本和新版本的prototypeId
        PrototypeConverter.composePrototypeReqToPrototype(prototypeReq, prototype, prototype.getSkcCreatedTime());
        prototype.setLatestPrototypeId(prototypeIdNew);
        prototype.setVersionNum(versionNumNew);
        prototype.setLatestVersionNum(versionNumNew);
        prototype.setPrototypeId(prototypeIdNew);

        Integer prototypeStatus = prototype.getPrototypeStatus();

        //插入新历史表数据
        PrototypeHistory prototypeHistoryNew = new PrototypeHistory();
        BeanUtils.copyProperties(prototype, prototypeHistoryNew);
        prototypeHistoryRepository.save(prototypeHistoryNew);

        prototypeRepository.updateIdAndPrototype(prototype, donePrototypeId);

        //新增prototype_detail表
        PrototypeDetail prototypeDetail = new PrototypeDetail();
        PrototypeDetail prototypeDetailOld = prototypeDetailRepository.getByPrototypeId(latestPrototypeId);
        PrototypeConverter.convertPrototypeDetail(prototypeDetail, prototypeReq);
        prototypeDetail.setPrototypeId(prototypeIdNew);
        prototypeDetail.setPrototypeDetailId(IdPool.getId());
        if (Objects.nonNull(prototypeDetailOld)) {
            prototypeDetail.setCheckPriceState(prototypeDetailOld.getCheckPriceState());
            prototypeDetail.setCheckPriceId(prototypeDetailOld.getCheckPriceId());
            prototypeDetail.setPredictCheckPriceStatus(prototypeDetailOld.getPredictCheckPriceStatus());
            prototypeDetail.setPredictCheckPriceId(prototypeDetailOld.getPredictCheckPriceId());
        } else {
            prototypeDetail.setCheckPriceState(0);
            prototypeDetail.setPredictCheckPriceStatus(0);
        }
        prototypeDetailRepository.save(prototypeDetail);

        //增加日志
        addLog(prototypeIdNew, prototype.getDesignCode(), "完成了【再次拆版】");

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(AsyncTask.wrapper(() -> {
                    //请求向量创建
                    if (Objects.equals(prototypeStatus, PrototypeStatusEnum.WAIT_DECOMPOSE.getCode())) {
                        styleLibraryHelper.prototypeStyleLibrarySaveOrUpdate(prototype, designStyle.getCategoryName(), prototypeDetail, 1);
                    } else {
                        styleLibraryHelper.prototypeStyleLibrarySaveOrUpdate(prototype, designStyle.getCategoryName(), prototypeDetail, 2);
                    }
                }));
            }
        });

    }

    /**
     * 处理拆版提交至样衣打版更新其属性
     */
    private void handlerDemolitionSubmitToClothes(PrototypeOperateReq prototypeReq) {
        DemolitionUpdateSampleClothesReq updateClothesReq = new DemolitionUpdateSampleClothesReq();
        updateClothesReq.setDesignCode(prototypeReq.getDesignCode());
        // updateClothesReq.setCategory(prototypeReq.getCategory());
        // updateClothesReq.setCategoryName(prototypeReq.getCategoryName());

        if (CollectionUtil.isNotEmpty(prototypeReq.getDesignPicture())) {
            updateClothesReq.setDesignPicture(String.join(",", prototypeReq.getDesignPicture()));
        }
        // if (CollectionUtil.isNotEmpty(prototypeReq.getCustomerPicture())) {
        //     updateClothesReq.setCustomerPicture(String.join(",", prototypeReq.getCustomerPicture()));
        // }
        // if (StringUtils.isNotBlank(prototypeReq.getSampleAmount())) {
        //     updateClothesReq.setSampleAmount(Integer.valueOf(prototypeReq.getSampleAmount()));
        // }
        updateClothesReq.setColor(prototypeReq.getColor());
        updateClothesReq.setSizeStandard(prototypeReq.getSizeStandard());
        updateClothesReq.setSizeStandardCode(prototypeReq.getSizeStandardCode());
        updateClothesReq.setSampleSize(prototypeReq.getSampleSize());
        updateClothesReq.setMakeSameDesignCode(prototypeReq.getMakeSameDesignCode());
        // updateClothesReq.setStyleReferType(prototypeReq.getStyleReferType());
        // updateClothesReq.setStyleReferDesignCode(prototypeReq.getStyleReferDesignCode());
        // updateClothesReq.setSpecialCraftName(prototypeReq.getSpecialCraftName());
        // updateClothesReq.setPaymentAtPrice(prototypeReq.getPaymentAtPrice());

        sampleClothesRemoteHelper.demolitionUpdateSampleClothes(updateClothesReq);

        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.DESIGN_DEMOLITION_UPDATE,
                DesignMqConstant.DESIGN_DEMOLITION_UPDATE_EXCHANGE, null, JSON.toJSONString(updateClothesReq));
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

    private void addLog(Long bizId, String designCode, String content) {
        DesignLogReq designLogReq = DesignLogReq.builder()
                .bizType(DesignLogBizTypeEnum.DESIGN_PROTOTYPE)
                .content(content)
                .bizId(bizId)
                .designCode(designCode)
                .build();
        designLogService.create(designLogReq);
    }


    /**
     * 校验引用设计款号
     */
    private void validateRefDesignCode(String designCode) {
        Prototype prototype = prototypeRepository.getByDesignCode(designCode);
        Assert.notNull(prototype, "请勿引用不存在的设计款：{}", designCode);
        Assert.isTrue(prototype.getIsDoneVersion(), "请勿引用未拆版的设计款：{}", designCode);
    }

    /**
     * 校验开发信息
     */
    private void validateDevelopmentInformation(PrototypeOperateReq prototypeReq, Prototype prototype) {
        //判断复色打版，必须填复色款号
        // if (SkcTypeEnum.COMPOUND_COLORS.getCode().equals(prototype.getSkcType())) {
        //     Assert.notBlank(prototypeReq.getMakeSameDesignCode(), "复色操作,请填写复色款号！");
        //     validateRefDesignCode(prototypeReq.getMakeSameDesignCode());
        // }

        //判断校验款式品类必须有四级
        // validateCategory(prototypeReq);

        //颜色校验
        validateColor(prototypeReq);
        Assert.notEmpty(prototypeReq.getDesignPicture(), "设计图片至少要有1张");
        Assert.notBlank(prototypeReq.getSizeStandard(), "尺码标准不能为空");
        Assert.notBlank(prototypeReq.getSizeStandardCode(), "尺码标准编码不能为空");
        Assert.notBlank(prototypeReq.getSampleSize(), "样衣尺码不能为空");

        //如果传了视觉需求信息, 校验spu是否已提交了视觉需求
        PrototypeSubmitVisualCheckVo checkVo = this.checkVisualTask(prototype.getDesignCode());
        if (checkVo.getNeedVisualDemand()) {
            SdpDesignException.notNull(prototypeReq.getVisualDemandInfo(), "视觉需求信息不能为空!");
        }else {
            SdpDesignException.isNull(prototypeReq.getVisualDemandInfo(), "当前spu已提交视觉需求信息, 请刷新页面后重新提交!");
        }
    }


    /**
     * 校验颜色
     */
    private void validateColor(PrototypeOperateReq prototypeReq) {
        Assert.notBlank(prototypeReq.getColor(), "颜色不能为空");
        String removeColorKeyStr = StrUtil.removeAny(prototypeReq.getColor(), " ", "色");
        Assert.notBlank(removeColorKeyStr, "颜色不能只填'色'");
        List<MakeSameDesignCodeVo> makeSameDesignCodeVos = prototypeRepository.queryMakeSameByDesignCode(prototypeReq.getDesignCode());
        if (CollectionUtil.isNotEmpty(makeSameDesignCodeVos)) {
            makeSameDesignCodeVos.forEach(vo -> {
                //取消的版单不需要校验
                if (!vo.getIsCanceled()) {
                    String makeSameRemoveColorKeyStr = StrUtil.removeAny(vo.getColor(), " ", "色");
                    Assert.isFalse(StrUtil.equalsIgnoreCase(removeColorKeyStr, makeSameRemoveColorKeyStr), "当前spu下已存在该颜色，请检查填写内容");

                    //拼色校验
                    String separator = "、";
                    List<String> colorList = StrUtil.splitTrim(vo.getColor(), separator);
                    List<String> colorReqList = StrUtil.splitTrim(prototypeReq.getColor(), separator);

                    TreeSet<String> colorSet = new TreeSet<>(colorList);
                    TreeSet<String> colorReqSet = new TreeSet<>(colorReqList);

                    SdpDesignException.isTrue(!Objects.equals(colorSet, colorReqSet), "当前spu下已存在该颜色组合:{}", colorSet);
                }
            });
        }

    }

    /**
     * 判断有效的拆版
     */
    private void validateValidPrototype(PrototypeOperateReq prototypeReq, Prototype prototype) {
        Assert.notNull(prototype, "该设计款号已被修改，请刷新页面！");
        Assert.isTrue(prototype.getLatestVersionNum().equals(prototypeReq.getLatestVersionNum()), "该设计款号已有新版本，请刷新页面");
        //判断不能是已取消的版单
        Assert.isFalse(prototype.getIsCanceled(), "设计款号已取消！");
    }


}