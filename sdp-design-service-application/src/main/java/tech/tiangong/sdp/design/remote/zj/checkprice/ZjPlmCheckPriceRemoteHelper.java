package tech.tiangong.sdp.design.remote.zj.checkprice;

import cn.yibuyun.framework.net.DataResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import tech.tiangong.sdp.core.config.ZjOpenFeignUserContentConfig;
import tech.tiangong.sdp.design.vo.req.zj.checkprice.CheckPriceBatchAddV2OpenReq;

/**
 * @Created by jero<PERSON><PERSON>
 * @ClassName PlmCheckPriceRemoteHelper
 * @Description 致景PLM-核价模块 对接路由
 * @Date 2024/11/30 11:13
 */
@FeignClient(value = "plm-sample-clothes",
        contextId = "PLM-CheckPriceCommodityClient",
        configuration = ZjOpenFeignUserContentConfig.class,
        path = "/zj-tg-api/plm-sample-clothes/open/v2",
        url = "${cx-tg.domain.url}")
public interface ZjPlmCheckPriceRemoteHelper {


    /**
     * 给jv-plm 新系统 批量新增核价
     *
     * @see "https://yapi.textile-story.com/project/1650/interface/api/84460"
     *
     * @param req 核价信息
     * @return 响应
     */
    @PostMapping("/check-price/batch-save")
    DataResponse<Void> batchSave(@RequestBody CheckPriceBatchAddV2OpenReq req);
}
