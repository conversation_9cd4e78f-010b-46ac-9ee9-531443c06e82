package tech.tiangong.sdp.design.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.design.repository.PrototypeHistoryRepository;
import tech.tiangong.sdp.design.service.PrototypeHistoryService;

/**
 * 版单历史表服务
 *
 * <AUTHOR>
 * @since 2021-08-09 14:50:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrototypeHistoryServiceImpl implements PrototypeHistoryService {
    private final PrototypeHistoryRepository prototypeHistoryRepository;
}