package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.enums.BomMultiStateEnum;
import tech.tiangong.sdp.design.vo.req.bom.BomOrderUpdateV3Req;

/**
 * bom单_提交处理 接口
 * <AUTHOR>
 * @date 2022/11/16 20:31
 */
public interface BomSubmitHandleService {

    /**
     * 首次提交
     *
     * 适用场景: bom 待提交_无暂存    (信息直接维护在原表中)
     *
     * @param req 入参
     */
    void firstSubmitNoTransient(BomOrderUpdateV3Req req);

    /**
     * 首次提交
     *
     * 适用场景: bom待提交_有暂存
     *
     * 先按再次暂存处理: BomTransientHandleService#reTransient
     * 暂存完后再复制一份暂存信息到原表中
     * 从原表中汇总新增,删除的物料,工艺,需求信息
     * @param req 入参
     */
    void firstSubmitWithTransient(BomOrderUpdateV3Req req);

    /**
     * 再次提交
     *
     * 适用场景: bom 已提交/已核算/找料中_无暂存    (信息直接维护在原表中)
     *
     * @param req 入参
     * @return 新版本bom
     */
    BomOrder reSubmitNoTransient(BomOrderUpdateV3Req req, BomMultiStateEnum bomMultiStateEnum);

    /**
     * 再次提交
     *
     * 适用场景: bom 已提交/已核算/找料中_有暂存
     *
     * 先按再次暂存处理: BomTransientHandleService#reTransient4Submitted
     * 暂存完后再复制一份暂存信息到原表中
     * 从原表中汇总新增,删除的物料,工艺,需求信息
     *
     * @param req 入参
     * @return 新版本bom
     */
    BomOrder reSubmitWithTransient(BomOrderUpdateV3Req req, BomMultiStateEnum bomMultiStateEnum);

}
