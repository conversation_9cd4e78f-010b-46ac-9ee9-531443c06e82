package tech.tiangong.sdp.design.vo.resp.prototype;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import tech.tiangong.sdp.design.vo.dto.visual.BackgroundDTO;
import tech.tiangong.sdp.design.vo.dto.visual.ModelFaceDTO;
import tech.tiangong.sdp.design.vo.dto.visual.TryOnModelReferenceImageDTO;
import tech.tiangong.sdp.design.vo.resp.identity.MuseSpuIdentifyVo;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 设计款管理 spu + skc
 *
 * <AUTHOR>
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
public class PrototypeTagVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 5679086965017512544L;

    /**
     * SPU信息
     */
    private DesignStyleVo styleInfo;

    /**
     * skc基础信息
     */
    private PrototypeInfoVo prototypeInfo;

    /**
     * 灵感设计需求信息 (款式开发)
     */
    private DesignDemandInfo designDemandInfo;

    /**
     * 上架商品信息 (款式开发)
     */
    private OnShelfInfo onShelfInfo;

    /**
     * muse款式识别信息   -素材中心1期
     */
    private MuseSpuIdentifyVo museIdentifyInfo;

    /**
     * 灵感设计需求
     */
    @Data
    @SuperBuilder
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class DesignDemandInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = -3572155942378577417L;

        /**
         * 设计详情id
         */
        private Long designDemandId;

        /**
         * 供给方式-OPS
         */
        private String supplyModeName;

        /**
         * 供给方式编码
         */
        private String supplyModeCode;

        /**
         * 商品链接
         */
        private String productLink;

        /**
         * 建议风格-OPS
         */
        private String suggestedStyle;

        /**
         * 建议风格编码
         */
        private String suggestedStyleCode;

        /**
         * 国家站点编码
         */
        private String countrySiteCode;

        /**
         * 国家站点name
         */
        private String countrySiteName;

        /**
         * 店铺id
         */
        private Long storeId;

        /**
         * 店铺名称
         */
        private String storeName;


        /**
         * 灵感图来源编码
         */
        private String inspirationImageSourceCode;

        /**
         * 灵感图来源
         */
        private String inspirationImageSource;

        /**
         * 灵感源品牌编码
         */
        private String inspirationBrandCode;

        /**
         * 灵感源品牌
         */
        private String inspirationBrand;

        /**
         * 灵感品类编码-OPS
         */
        private String category;

        /**
         * 灵感品类名称
         */
        private String categoryName;

        /**
         * 建议售价
         */
        private String sellingPrice;

        /**
         * 期望成本(仿款时有值)  v1.020
         */
        private String expectedCostPrice;

        /**
         * 场景名称(ops: JV_scene)  v1.020
         */
        private String sceneName;

        /**
         * 场景编码  v1.020
         */
        private String sceneCode;

        /**
         * 企划来源name  v1.020
         */
        private String planningSourceName;

        /**
         * 企划来源code  v1.020
         */
        private String planningSourceCode;

        /**
         * aigc备注 --- 2025-01-16新增 (需求描述)
         * */
        private String aigcRemark;

        //视觉需求信息
        /**
         * 需求类型: 0无需修图; 1多视图; 2修图; 3多视图+修图
         */
        private Integer demandType;

        /**
         * 需求图片
         */
        private List<String> demandImageList;

        /**
         * 模特图
         */
        private List<String> modelPicList;

        /**
         * 背景图
         */
        private List<String> backgroundPicList;

        /**
         * 姿势图
         */
        private List<String> posturePicList;

        /**
         * 参考图
         */
        private List<TryOnModelReferenceImageDTO> modelReferenceImageList;

        /**
         * 背景图
         */
        private List<BackgroundDTO> backgroundImageList;

        /**
         * 模特脸图
         */
        private List<ModelFaceDTO> modelFaceImageList;

        /**
         * 原图
         */
        private String originalImage;

        /**
         * 灵感图集合
         */
        private List<String> inspirationImageList;


    }

    /**
     * 上架商品信息
     */
    @Data
    @SuperBuilder
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class OnShelfInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = -3572155942378577417L;

        /**
         * SPU编码
         */
        private String styleCode;

        /**
         * SKC编码
         */
        private String designCode;

        /**
         * spu详情图集合
         */
        private List<String> spuDetailImageList;

        /**
         * skc图集合
         */
        private List<String> skcImageList;
    }
}
