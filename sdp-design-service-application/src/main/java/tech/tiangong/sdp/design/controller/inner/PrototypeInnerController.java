package tech.tiangong.sdp.design.controller.inner;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import com.alibaba.excel.EasyExcel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tech.tiangong.sdp.design.service.PrototypeService;
import tech.tiangong.sdp.design.vo.req.prototype.inner.*;
import tech.tiangong.sdp.design.vo.resp.SkcTagVO;
import tech.tiangong.sdp.design.vo.resp.prototype.PrototypeSplicingInfoVo;
import tech.tiangong.sdp.design.vo.resp.prototype.PrototypeVo;
import tech.tiangong.sdp.qy.vo.excel.SkcSalesChannelExcelModel;
import tech.tiangong.sdp.utils.excel.SkcSalesChannelDataListener;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * 设计拆版-inner
 *
 * <AUTHOR>
 * @since 2021-08-09 14:43:19
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/prototype")
public class PrototypeInnerController extends BaseController {
    private final PrototypeService prototypeService;


    /**
     * 获取已提交版本的详情(看不到临时保存的版本)
     *
     * @param designCode 设计款号
     * @return 响应结果
     * @deprecated 使用新接口 tech.tiangong.sdp.design.controller.inner.SkcInnerController#getSavedInfoByDesignCode(java.lang.String)
     */
    @Deprecated(since = "款式开发")
    @GetMapping("saved-info/{designCode}")
    public DataResponse<PrototypeVo> getSavedInfoByDesignCode(@PathVariable(value = "designCode") String designCode) {
        return DataResponse.ok(prototypeService.getSavedInfoByDesignCode(designCode));
    }

    /**
     * 查询该设计款号是否存在
     * @param designCode 设计款号（精确）
     * @return 相应结果 ture-存在 false-不存在
     */
    @GetMapping("exists/{designCode}")
    public DataResponse<Boolean> exists(@PathVariable("designCode") String designCode) {
        return DataResponse.ok(prototypeService.exists(designCode));
    }

    /**
     * 批量查询设计款号-获取已提交版本的详情(看不到临时保存的版本)
     *
     * @return 响应结果
     */
    @PostMapping("saved-info-list")
    public DataResponse<List<PrototypeVo>> getSavedInfoListByDesignCode(@RequestBody @Validated PrototypeListInnerReq req) {
        return DataResponse.ok(prototypeService.getSavedInfoListByDesignCode(req));
    }

    /**
     * 根据id批量查询设计款号
     *
     * @return 响应结果
     */
    @PostMapping("list-by-id")
    public DataResponse<List<PrototypeVo>> getListByPrototypeId(@RequestBody @Validated PrototypeListByIdInnerReq req) {
        return DataResponse.ok(prototypeService.getListByPrototypeId(req));
    }


    /**
     * 根据设计款号更新核价状态
     *
     * @param req 入参
     * @return 响应结果
     */
    @PostMapping("/update/check-price-state")
    public DataResponse<Void> updateCheckPriceState(@RequestBody @Validated PrototypeCheckPriceInnerReq req) {
        prototypeService.updateCheckPriceState(req);
        return DataResponse.ok();
    }

    /**
     *根据spu款式号查询该款式号下的设计款是否存在已拆版完成的情况
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @PostMapping("/demand-done")
    public DataResponse<List<PrototypeVo>> selectDemandDone(@RequestBody @Validated PrototypeDemandListReq req){
        return DataResponse.ok(prototypeService.selectDemandDone(req));
    }

    // /**
    //  * 通过设计款号获取打印信息(批量)
    //  *
    //  * @param req 参数
    //  * @return 响应结果
    //  */
    // @PostMapping("/print-batch/design-order")
    // public DataResponse<PrototypePrintInfoBatchVo> getPrintInfoByDesignCodeBatch(@RequestBody @Validated PrototypePrintBatchInnerReq req){
    //     return DataResponse.ok(prototypeService.getPrintInfoByDesignCodeBatch(req));
    // }

    /**
     * 查询对应设计款号是否拼接款(提供给履约使用)
     *
     * @param req 查询是否拼接款入参
     * @return 响应结果
     */
    @PostMapping("/splicing")
    public DataResponse<List<PrototypeSplicingInfoVo>> queryIsSplicingByDesignCodes(@RequestBody @Validated PrototypeSplicingBatchInnerReq req){
        return DataResponse.ok(prototypeService.queryIsSplicingByDesignCodes(req));
    }

    // /**
    //  * 用SKC查 设计师、设计组、SPU （CRM专用）
    //  * @return DesignerInfoVo
    //  */
    // @GetMapping("/designerInfo/{designCode}")
    // public DataResponse<DesignerInfoVo> designerInfoByDesignCode(@PathVariable(value = "designCode") String designCode){
    //     return DataResponse.ok(prototypeService.designerInfoByDesignCode(designCode));
    // }


    /**
     * 获取skc标签内容
     *
     * @param designCodes skc
     * @return skc标签内容
     */
    @PostMapping("/skc-tag")
    DataResponse<Map<String, SkcTagVO>> getMapSkcTags(@RequestBody Set<String> designCodes) {
        return  DataResponse.ok(prototypeService.mapSkcTags(designCodes));
    }

    // /**
    //  * 根据颜色编码-批量查询对应设计款信息
    //  *
    //  * @param req 入参
    //  * @return List<SkcColorVo>
    //  */
    // @PostMapping("/list/skc-color")
    // DataResponse<List<SkcColorVo>> getSkcColor(@RequestBody @Validated SkcColorReq req) {
    //     return  DataResponse.ok(prototypeService.getSkcColor(req));
    // }

    // /**
    //  * 根据颜色编码-批量查询对应设计款是否存在
    //  *
    //  * @param req 入参
    //  * @return List<String> 元素为颜色编码; 返回有关联款式的颜色编码集合;
    //  */
    // @PostMapping("/check/skc-color")
    // DataResponse<List<String>> checkSkcColor(@RequestBody @Validated SkcColorReq req) {
    //     return  DataResponse.ok(prototypeService.checkSkcColor(req));
    // }


    /**
     * 导入SKC销售渠道数据
     */
    @NoRepeatSubmitLock
    @PostMapping("/import-sales-channel")
    public ImportResult importSalesChannel(MultipartFile file) throws IOException {
        long startTime = System.currentTimeMillis();

        // 创建监听器实例
        SkcSalesChannelDataListener listener = new SkcSalesChannelDataListener(prototypeService);

        // 读取Excel
        EasyExcel.read(
                file.getInputStream(),
                SkcSalesChannelExcelModel.class,
                listener
        ).sheet().headRowNumber(1).doRead();

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        log.info("数据处理完成，耗时 {} 毫秒", duration);

        return new ImportResult("success", duration);
    }

    /**
     * 导入结果对象
     */
    @Data
    @AllArgsConstructor
    public static class ImportResult {
        private String status;
        private long duration; // 处理耗时（毫秒）
    }

}