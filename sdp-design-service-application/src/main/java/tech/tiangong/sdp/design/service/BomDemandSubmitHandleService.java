package tech.tiangong.sdp.design.service;


import tech.tiangong.sdp.design.vo.req.bom.BomCraftSubmitHandleReq;
import tech.tiangong.sdp.design.vo.req.bom.BomDemandSubmitHandleReq;

/**
 * bom单_物料需求-提交处理 接口
 * <AUTHOR>
 * @date 2022/11/16 20:31
 */
public interface BomDemandSubmitHandleService {

    /**
     * 首次提交-需求处理
     *
     * 适用场景: bom 待提交_无暂存    (信息直接维护在原表中)
     *
     * 需求新增(); //首次提交,只会有新增的需求,维护到原表,不升版本
     * 调用履约接口创建需求, 将需求id封装到新增工艺入参中
     *
     * @param req 入参
     */
    BomCraftSubmitHandleReq firstSubmitNoTransient(BomDemandSubmitHandleReq req);

    /**
     * 再次提交-需求处理
     *
     * 适用场景: bom 已提交/已核算/找料中_无暂存    (信息直接维护在原表中)
     *
     * 需求新增(); //维护到原表, 新版本bom下
     * 需求更新(); //复制旧bom需求, 更新数据后,新增到新版本bom下
     * 需求删除(); //记录删除的需求id, 复制旧bom需求, close需求后,新增到新版本bom下
     * 调用履约接口创建需求, 将需求id封装到新增工艺入参中
     * 旧新旧物料id映射Map提交给工艺处理
     * @param req 入参
     */
    BomCraftSubmitHandleReq reSubmitNoTransient(BomDemandSubmitHandleReq req);

    /**
     * 有暂存-首次提交 -需求处理
     *
     * 适用场景: bom 待提交_有暂存
     *
     * 暂存完后再复制一份暂存信息到原表中(新bom)
     *
     * 调用履约接口创建需求, 将需求id封装到新增工艺入参中
     *
     * @param req 入参
     */
    BomCraftSubmitHandleReq firstSubmitWithTransient(BomDemandSubmitHandleReq req);

    /**
     * 有暂存-再次提交 -需求处理
     *
     * 适用场景: bom 已提交/已核算/找料中_有暂存
     *
     * 暂存完后再复制一份暂存信息到原表中(新bom)
     * 调用履约接口创建需求, 将需求id封装到新增工艺入参中
     * 旧新旧物料id映射Map提交给工艺处理
     * @param req 入参
     */
    BomCraftSubmitHandleReq reSubmitWithTransient(BomDemandSubmitHandleReq req);
}
