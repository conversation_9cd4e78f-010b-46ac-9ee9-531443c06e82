package tech.tiangong.sdp.design.service.impl;

import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.PushZjLog;
import tech.tiangong.sdp.design.repository.PushZjLogRepository;
import tech.tiangong.sdp.design.service.PushZjLogService;
import tech.tiangong.sdp.design.vo.query.PushZjLogQuery;
import tech.tiangong.sdp.design.vo.req.log.PushZjLogReq;
import tech.tiangong.sdp.design.vo.resp.PushZjLogVo;

/**
 * 数据推送致景记录表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PushZjLogServiceImpl implements PushZjLogService {
    private final PushZjLogRepository pushZjLogRepository;

    @Override
    public PageRespVo<PushZjLogVo> page(PushZjLogQuery query) {
        IPage<PushZjLogVo> page = pushZjLogRepository.findPage(query);
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(PushZjLogReq req) {
        SdpDesignException.notNull(req, "入参为空!");
        PushZjLog entity = new PushZjLog();
        BeanUtils.copyProperties(req, entity);
        entity.setPushZjLogId(IdPool.getId());
        pushZjLogRepository.save(entity);
    }

}
