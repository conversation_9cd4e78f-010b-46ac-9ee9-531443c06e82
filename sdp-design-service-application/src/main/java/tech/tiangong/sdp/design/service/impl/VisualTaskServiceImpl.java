package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.exception.BusinessException;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zjkj.booster.common.constant.DatePatternConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.visual.VisualImagePackageConverter;
import tech.tiangong.sdp.design.converter.visual.VisualTaskConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.enums.SdpStyleTypeEnum;
import tech.tiangong.sdp.design.enums.VisualImgUploadTypeEnum;
import tech.tiangong.sdp.design.enums.visual.*;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.remote.SdpOrderHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.service.download.DownloadTaskService;
import tech.tiangong.sdp.design.vo.dto.visual.BackgroundDTO;
import tech.tiangong.sdp.design.vo.dto.visual.ModelFaceDTO;
import tech.tiangong.sdp.design.vo.dto.visual.TryOnModelReferenceImageDTO;
import tech.tiangong.sdp.design.vo.query.visual.VisualTaskQuery;
import tech.tiangong.sdp.design.vo.req.prototype.inner.SkcBomSubmitReq;
import tech.tiangong.sdp.design.vo.req.visual.*;
import tech.tiangong.sdp.design.vo.resp.bom.BomOrderDetailVo;
import tech.tiangong.sdp.design.vo.resp.visual.*;
import tech.tiangong.sdp.prod.vo.resp.StyleSizeInfoVo;
import tech.tiangong.sdp.utils.BusinessCodeGenerator;
import tech.tiangong.sdp.utils.CodeRuleEnum;
import tech.tiangong.sdp.utils.StreamUtil;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 视觉任务服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VisualTaskServiceImpl implements VisualTaskService {
    private final VisualTaskRepository visualTaskRepository;
    private final VisualTaskDetailRepository visualTaskDetailRepository;
    private final VisualTaskTryOnRepository visualTaskTryOnRepository;
    private final VisualTaskOnShelfRepository visualTaskOnShelfRepository;
    private final VisualImagePackageRepository visualImagePackageRepository;
    private final VisualTaskConverter visualTaskConverter;
    private final BusinessCodeGenerator businessCodeGenerator;
    private final VisualTaskHelper visualTaskHelper;
    private final VisualTaskNodeStateService visualTaskNodeStateService;
    private final VisualSpuRepository visualSpuRepository;
    private final VisualDemandRepository visualDemandRepository;
    private final VisualQcRepository visualQcRepository;
    private final VisualTaskNodeStateRepository visualTaskNodeStateRepository;
    private final PrototypeRepository prototypeRepository;
    private final BomOrderService bomOrderService;
    private final SdpOrderHelper sdpOrderHelper;
    private final VisualImagePackageConverter visualImagePackageConverter;
    private final PrototypeManageService prototypeManageService;
    private final SpotSpuService spotSpuService;
    private final DigitalPrintingStyleService digitalPrintingStyleService;
    private final DownloadTaskService downloadTaskService;
    private final VisualTaskHandleService visualTaskHandleService;
    private final AsyncTaskExecutor asyncTaskExecutor;
    private final DateTimeFormatter PURE_DATETIME_PATTERN = DateTimeFormatter.ofPattern(DatePatternConstants.PURE_DATETIME_PATTERN);
    private final VisualTaskTryOnLogRepository visualTaskTryOnLogRepository;
    private final VisualOnShelfAiBoxImageRepository visualOnShelfAiBoxImageRepository;

    @Resource
    private  VisualTaskTryOnLogService visualTaskTryOnLogService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public VisualTask createByVisualDemand(VisualDemand visualDemand) {
        log.info("创建视觉任务开始:{}", JSONObject.toJSONString(visualDemand));
        //SPU的图包
        VisualImagePackage visualImagePackage = visualImagePackageRepository.getLatestByStyleCode(visualDemand.getStyleCode());
        //SPU最新的上新任务
        VisualTask latestNewArrivalTask = visualTaskRepository.getLatestNewArrivalTaskBySpu(visualDemand.getStyleCode());
        //SPU的尺寸表任务
        VisualTask sizeTypeTask = visualTaskRepository.getLatestNotCancelSizeTaskBySpu(visualDemand.getStyleCode());

        //尺寸表需求任务：由大货资料提交且动销时，或已动销后大货资料提交时触发。触发时，若上新任务已完成则生成一条任务。
        if(Bool.YES.getCode()==visualDemand.getIsProdOrder()){
            //没有图包，没有上新任务
            if(visualImagePackage==null && latestNewArrivalTask==null) {
                log.info("styleCode:{}大货资料发起创建尺寸表任务:没有图包，没有上新任务，需要先创建一个上新任务",visualDemand.getStyleCode());
//                return doCreateVisualTask(visualDemand,VisualTaskTypeEnum.NEW_ARRIVAL_TASK);
                throw new BusinessException("创建尺寸表任务失败："+visualDemand.getStyleCode()+"下没有图包和上新任务");
            }
            //只有图包
            else if(visualImagePackage!=null && latestNewArrivalTask==null && sizeTypeTask==null) {
                log.info("styleCode:{}大货资料发起创建尺寸表任务:只有图包，没有上新任务，没有尺寸表任务",visualDemand.getStyleCode());
                return doCreateVisualTask(visualDemand,VisualTaskTypeEnum.SIZE_TASK);
            }
            //只有上新任务，且已完成
            else if(latestNewArrivalTask!=null
                    && VisualTaskStateEnum.FINISH.getCode().equals(latestNewArrivalTask.getState())
                    && sizeTypeTask==null){
                log.info("styleCode:{}大货资料发起创建尺寸表任务:没有图包，只有上新任务且已完成，没有尺寸表任务",visualDemand.getStyleCode());
                return doCreateVisualTask(visualDemand,VisualTaskTypeEnum.SIZE_TASK);
            }
            //只有尺寸任务，且已完成
            else if(sizeTypeTask!=null
                    && VisualTaskStateEnum.FINISH.getCode().equals(sizeTypeTask.getState())
                    && latestNewArrivalTask==null){
                log.info("styleCode:{}大货资料发起创建尺寸表任务:没有图包，没有有上新任务，只有尺寸表任务且已完成",visualDemand.getStyleCode());
                return doCreateVisualTask(visualDemand,VisualTaskTypeEnum.SIZE_TASK);
            }
            //上新任务，尺寸任务都已完成
            else if(latestNewArrivalTask!=null
                    && VisualTaskStateEnum.FINISH.getCode().equals(latestNewArrivalTask.getState())
                    && sizeTypeTask!=null
                    && VisualTaskStateEnum.FINISH.getCode().equals(sizeTypeTask.getState())){
                log.info("styleCode:{}大货资料发起创建尺寸表任务:没有图包，有上新任务且已完成，有尺寸表任务且已完成",visualDemand.getStyleCode());
                return doCreateVisualTask(visualDemand,VisualTaskTypeEnum.SIZE_TASK);
            }
            throw new BusinessException("创建尺寸表任务失败："+visualDemand.getStyleCode()+"下仍有在途的上新任务或者尺寸表任务");
        }
        //新/优化创建新数据的节点为，上一条非尺寸任务状态为【已完成、已取消】时创建/或无上新任务但存在图包时则创建
        //上新需求任务：可由款创建、复色、人工创建触发。为SPU首个生效的视觉任务，即创建时，若SPU当前图片管理无图包且无上新任务或上新任务状态均为已取消时，即为上新任务。
        else if((visualImagePackage==null && latestNewArrivalTask==null)
                || (latestNewArrivalTask!=null && Bool.YES.getCode()==latestNewArrivalTask.getIsCancel())) {
            log.info("styleCode:{}非大货资料发起视觉需求任务:没有图包且没有上新任务，或者有上新任务且已取消，则生成一个上新任务",visualDemand.getStyleCode());
            return doCreateVisualTask(visualDemand,VisualTaskTypeEnum.NEW_ARRIVAL_TASK);
        }
        //其他情况都是优化需求任务
        else{
            log.info("styleCode:{}非大货资料发起视觉需求任务:已有图包或者上新任务，生成一个优化任务",visualDemand.getStyleCode());
            return doCreateVisualTask(visualDemand,VisualTaskTypeEnum.OPTIMIZE_TASK);
        }
    }

    private VisualTask doCreateVisualTask(VisualDemand visualDemand,VisualTaskTypeEnum visualTaskType) {
        String processCode = businessCodeGenerator.generate(CodeRuleEnum.VISUAL_TASK_CODE, visualTaskRepository::selectLatestProcessCode);
        VisualTask task = new VisualTask();
        task.setTaskId(IdPool.getId());
        task.setState(VisualTaskStateEnum.WAITING.getCode());
        task.setTaskType(visualTaskType.getCode());
        task.setLatestDemandId(visualDemand.getDemandId());
        task.setIsLatest(Bool.YES.getCode());
        task.setIsRepair(Bool.NO.getCode());
        task.setIsCancel(Bool.NO.getCode());
        task.setStyleCode(visualDemand.getStyleCode());
        task.setProcessCode(processCode);
        task.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
        task.setVersionNum(1);
        visualTaskRepository.save(task);
        VisualTaskDetail taskDetail = new VisualTaskDetail();
        taskDetail.setTaskDetailId(IdPool.getId());
        taskDetail.setTaskId(task.getTaskId());
        visualTaskDetailRepository.save(taskDetail);
        //记录日志
        visualTaskHelper.addLog(task.getStyleCode(),task.getTaskId(),"创建视觉任务");
        //通知POP
        visualTaskHelper.noticePopTaskState(task.getStyleCode(), task.getProcessCode(), VisualTaskStateEnum.findByCode(task.getState()));
        return task;
    }

    @Override
    public PageRespVo<VisualTaskListVo> page(VisualTaskQuery query) {
        visualTaskHelper.handleBaseQuery(query);
        IPage<VisualTaskInfoVo> page = visualTaskRepository.queryByPage(query);
        List<VisualTaskListVo> list = visualTaskConverter.trans2VisualTaskListVo(page.getRecords());
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), list);
    }

    @Override
    public List<VisualTaskListExtraVo> pageExtra(VisualTaskListExtraReq req) {
        List<String> styleCodeList = req.getStyleCodeList();
        List<VisualSpu> visualSpuList = visualSpuRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(visualSpuList)) {
            return List.of();
        }
        List<VisualTaskListExtraVo> extraVoList = new ArrayList<>(styleCodeList.size());
        //根据styleType分组,查询不同的数据
        Map<Integer, List<VisualSpu>> styleTypeGroupMap = StreamUtil.groupingBy(visualSpuList, VisualSpu::getStyleType);
        styleTypeGroupMap.forEach((styleType, spuList) -> {
            List<String> styleCodes = StreamUtil.convertListAndDistinct(spuList, VisualSpu::getStyleCode);
            List<VisualTaskListExtraVo> extraVos = null;
            //设计款
            if (Objects.equals(styleType, SdpStyleTypeEnum.DESIGN.getCode())) {
                extraVos = prototypeManageService.visualListExtra(styleCodes);
            }
            //现货款
            else if (Objects.equals(styleType, SdpStyleTypeEnum.SPOT.getCode())) {
                extraVos = spotSpuService.visualListExtra(styleCodes);
            }
            //数码印花款
            else if (Objects.equals(styleType, SdpStyleTypeEnum.DIGITAL_PRINTING.getCode())) {
                extraVos = digitalPrintingStyleService.visualListExtra(styleCodes);
            }
            if (CollUtil.isNotEmpty(extraVos)) {
                extraVoList.addAll(extraVos);
            }
        });

        return extraVoList;
    }

    @Override
    public VisualTaskListCountVo countState(VisualTaskQuery query) {
        visualTaskHelper.handleBaseQuery(query);
        VisualTaskListCountVo vo = new VisualTaskListCountVo();
        vo.setTaskStateCount(visualTaskRepository.getVisualTaskStateCountVo(query));
        vo.setTaskStepNodeStateCount(visualTaskRepository.getVisualTaskStepNodeStateCountVo(query));
        return vo;
    }

    @Override
    public List<VisualTaskTypeCountVo> countTaskType(VisualTaskQuery query){
        visualTaskHelper.handleBaseQuery(query);
        return visualTaskRepository.getVisualTaskTypeCountVo(query);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void allocate(AllocateTaskReq req){
        VisualTaskProcessTypeEnum processType = req.getProcessType()==null ? VisualTaskProcessTypeEnum.NO_FIX : VisualTaskProcessTypeEnum.findByCode(req.getProcessType());
        Integer isLastHandler = req.getIsLastHandler();
        //只要不是仅tryOn处理并且没有设置分配上新任务处理人，修图人都要填
        if(!VisualTaskProcessTypeEnum.TRY_ON.equals(processType) && isLastHandler==null){
            Assert.isTrue(req.getOnShelfHandlerId()!=null,"修图人不能为空");
        }
        List<VisualTask> tasks = visualTaskRepository.listByIds(req.getTaskIds());
        Assert.isTrue(CollectionUtil.isNotEmpty(tasks),"任务不存在");
        List<VisualTask> updateTasks = new ArrayList<>();
        List<VisualTaskDetail> updateTaskDetails = new ArrayList<>();
        List<VisualTaskTryOn> tryOnTasks = new ArrayList<>();
        List<VisualTaskOnShelf> onShelfTasks = new ArrayList<>();
        VisualTaskTryOn oldTaskTryOn = null;
        VisualTaskOnShelf oldTaskOnShelf = null;
        boolean isChangeProcessType = true;
        for(VisualTask task : tasks){
            Assert.isTrue(Bool.YES.getCode() !=task.getIsCancel()
            && !VisualTaskStateEnum.FINISH.getCode().equals(task.getState()),
                    task.getProcessCode()+"当前状态不能被分配");

            if(task.getProcessType()!=null && task.getProcessType().equals(processType.getCode())){
                log.warn(task.getProcessCode()+"处理方式没变不需要重新创建子任务和改变任务状态");
                isChangeProcessType = false;
            }

            if(isChangeProcessType) {
                task.setProcessType(processType.getCode());
                task.setState(VisualTaskStateEnum.DOING.getCode());
                task.setQcResult(VisualTaskQcResultEnum.WAITING_QC.getCode());
                task.setIsRepair(Bool.NO.getCode());
                updateTasks.add(task);
            }

            VisualTaskDetail taskDetail = visualTaskDetailRepository.getByTaskId(task.getTaskId());
            updateTaskDetails.add(taskDetail);

            //任务已有tryOn处理任务
            if (taskDetail.getLatestTryOnDetailId() != null) {
                oldTaskTryOn = visualTaskTryOnRepository.getById(taskDetail.getLatestTryOnDetailId());
            }
            //任务已有修图处理任务
            if(taskDetail.getLatestOnShelfDetailId()!=null) {
                oldTaskOnShelf = visualTaskOnShelfRepository.getById(taskDetail.getLatestOnShelfDetailId());
            }
            //分配上新任务处理人，如果上新任务处理人只有一个（修图或者tryOn人），不管处理方式改成什么，处理人都用那一个人
            if(isLastHandler!=null && isLastHandler.equals(Bool.YES.getCode())){
                //SPU的最新版的上新任务
                VisualTask latestOnShelfVisualTask = visualTaskRepository.getLatestNewArrivalTaskBySpu(task.getStyleCode());
                Assert.isTrue(latestOnShelfVisualTask!=null
                        && !latestOnShelfVisualTask.getTaskId().equals(task.getTaskId()),
                        task.getStyleCode()+"还没有上新任务");
                VisualTaskDetail latestOnShelfVisualTaskDetail = visualTaskDetailRepository.getByTaskId(latestOnShelfVisualTask.getTaskId());
                Assert.isTrue(latestOnShelfVisualTaskDetail!=null,task.getStyleCode()+"上新任务还没分配");
                Long tryOnHandlerId = latestOnShelfVisualTaskDetail.getTryOnHandlerId();
                String tryOnHandlerName = latestOnShelfVisualTaskDetail.getTryOnHandlerName();
                Long onShelfHandlerId = latestOnShelfVisualTaskDetail.getOnShelfHandlerId();
                String onShelfHandlerName = latestOnShelfVisualTaskDetail.getOnShelfHandlerName();
                Assert.isTrue(tryOnHandlerId!=null || onShelfHandlerId!=null,task.getStyleCode()+"上新任务还没分配处理人");
                //不是仅tryOn处理(即无需修图，修图，tryOn+修图)，则需要引用上新任务修图处理人
                if(!VisualTaskProcessTypeEnum.TRY_ON.equals(processType)){
//                    Assert.isTrue(latestOnShelfVisualTaskDetail.getOnShelfHandlerId()!=null,task.getStyleCode()+"上新任务没有分配修图人");
                    boolean needTryOn = false;
                    //如果这次处理还需要tryOn处理，则需要引用上新任务的tryOn处理人
                    if(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.equals(processType)){
//                        Assert.isTrue(latestOnShelfVisualTaskDetail.getTryOnHandlerId()!=null,task.getStyleCode()+"上新任务没有分配tryOn人");
                        if(tryOnHandlerId!=null){
                            taskDetail.setTryOnHandlerId(tryOnHandlerId);
                            taskDetail.setTryOnHandlerName(tryOnHandlerName);
                        }else{
                            taskDetail.setTryOnHandlerId(onShelfHandlerId);
                            taskDetail.setTryOnHandlerName(onShelfHandlerName);
                        }
                        needTryOn = true;
                        //如果没有变更处理方式，则更新处理人即可
                        if(!isChangeProcessType && oldTaskTryOn!=null) {
                            oldTaskTryOn.setTryOnHandlerId(taskDetail.getTryOnHandlerId());
                            oldTaskTryOn.setTryOnHandlerName(taskDetail.getTryOnHandlerName());
                            tryOnTasks.add(oldTaskTryOn);
                        }else{
                            VisualTaskTryOn taskTryOn = initVisualTaskTryOn(task.getTaskId(),taskDetail.getTryOnHandlerId(),taskDetail.getTryOnHandlerName(),oldTaskTryOn);
                            tryOnTasks.add(taskTryOn);
                            taskDetail.setLatestTryOnDetailId(taskTryOn.getTryOnDetailId());
                        }
                    }
                    if(onShelfHandlerId!=null){
                        taskDetail.setOnShelfHandlerId(onShelfHandlerId);
                        taskDetail.setOnShelfHandlerName(onShelfHandlerName);
                    }else{
                        taskDetail.setOnShelfHandlerId(tryOnHandlerId);
                        taskDetail.setOnShelfHandlerName(tryOnHandlerName);
                    }

                    //如果没有变更处理方式，则更新处理人即可
                    if(!isChangeProcessType && oldTaskOnShelf!=null) {
                        oldTaskOnShelf.setOnShelfHandlerId(taskDetail.getOnShelfHandlerId());
                        oldTaskOnShelf.setOnShelfHandlerName(taskDetail.getOnShelfHandlerName());
                        onShelfTasks.add(oldTaskOnShelf);
                    }else{
                        VisualTaskOnShelf taskOnShelf = initVisualTaskOnShelf(task.getTaskId(),taskDetail.getOnShelfHandlerId(),taskDetail.getOnShelfHandlerName(),oldTaskOnShelf);
                        taskOnShelf.setHandleState(needTryOn? VisualTaskHandleStateEnum.WAITING_START.getCode() : VisualTaskHandleStateEnum.DOING.getCode());
                        taskDetail.setLatestOnShelfDetailId(taskOnShelf.getOnShelfDetailId());
                        onShelfTasks.add(taskOnShelf);
                    }
                }else{//只要tryOn处理
//                    Assert.isTrue(latestOnShelfVisualTaskDetail.getTryOnHandlerId()!=null,task.getStyleCode()+"上新任务没有分配tryon人");
                    if(tryOnHandlerId!=null){
                        taskDetail.setTryOnHandlerId(tryOnHandlerId);
                        taskDetail.setTryOnHandlerName(tryOnHandlerName);
                    }else{
                        taskDetail.setTryOnHandlerId(onShelfHandlerId);
                        taskDetail.setTryOnHandlerName(onShelfHandlerName);
                    }
                    //如果没有变更处理方式，则更新处理人即可
                    if(!isChangeProcessType && oldTaskTryOn!=null) {
                        oldTaskTryOn.setTryOnHandlerId(taskDetail.getTryOnHandlerId());
                        oldTaskTryOn.setTryOnHandlerName(taskDetail.getTryOnHandlerName());
                        tryOnTasks.add(oldTaskTryOn);
                    }else{
                        VisualTaskTryOn taskTryOn = initVisualTaskTryOn(task.getTaskId(),taskDetail.getTryOnHandlerId(),taskDetail.getTryOnHandlerName(),oldTaskTryOn);
                        tryOnTasks.add(taskTryOn);
                        taskDetail.setLatestTryOnDetailId(taskTryOn.getTryOnDetailId());
                    }
                }
            }
            //仅tryOn处理
            else if(VisualTaskProcessTypeEnum.TRY_ON.equals(processType)){
                Assert.isTrue(req.getTryOnHandlerId()!=null && StringUtils.isNotBlank(req.getTryOnHandlerName()),"tryon人不能为空");

                taskDetail.setTryOnHandlerId(req.getTryOnHandlerId());
                taskDetail.setTryOnHandlerName(req.getTryOnHandlerName());
                //如果没有变更处理方式，则更新处理人即可
                if(!isChangeProcessType && oldTaskTryOn!=null) {
                    oldTaskTryOn.setTryOnHandlerId(taskDetail.getTryOnHandlerId());
                    oldTaskTryOn.setTryOnHandlerName(taskDetail.getTryOnHandlerName());
                    tryOnTasks.add(oldTaskTryOn);
                }else{
                    //将已有修图任务失效
                    if(oldTaskOnShelf!=null){
                        taskDetail.setOnShelfHandlerId(null);
                        taskDetail.setOnShelfHandlerName(null);
                        taskDetail.setLatestOnShelfDetailId(null);

                        oldTaskOnShelf.setIsLatest(Bool.NO.getCode());
                        onShelfTasks.add(oldTaskOnShelf);
                    }
                    VisualTaskTryOn taskTryOn = initVisualTaskTryOn(task.getTaskId(),taskDetail.getTryOnHandlerId(),taskDetail.getTryOnHandlerName(),oldTaskTryOn);
                    tryOnTasks.add(taskTryOn);
                    taskDetail.setLatestTryOnDetailId(taskTryOn.getTryOnDetailId());
                }
            }
            //tryOn+修图
            else if(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.equals(processType)){
                Assert.isTrue(req.getTryOnHandlerId()!=null && StringUtils.isNotBlank(req.getTryOnHandlerName()),"tryon人不能为空");
                Assert.isTrue(req.getOnShelfHandlerId()!=null && StringUtils.isNotBlank(req.getOnShelfHandlerName()),"修图人不能为空");
                taskDetail.setTryOnHandlerId(req.getTryOnHandlerId());
                taskDetail.setTryOnHandlerName(req.getTryOnHandlerName());
                taskDetail.setOnShelfHandlerId(req.getOnShelfHandlerId());
                taskDetail.setOnShelfHandlerName(req.getOnShelfHandlerName());

                //如果没有变更处理方式，则更新处理人即可
                if(!isChangeProcessType) {
                    if(oldTaskTryOn!=null){
                        oldTaskTryOn.setTryOnHandlerId(taskDetail.getTryOnHandlerId());
                        oldTaskTryOn.setTryOnHandlerName(taskDetail.getTryOnHandlerName());
                        tryOnTasks.add(oldTaskTryOn);
                    }
                    if(oldTaskOnShelf!=null){
                        oldTaskOnShelf.setOnShelfHandlerId(taskDetail.getOnShelfHandlerId());
                        oldTaskOnShelf.setOnShelfHandlerName(taskDetail.getOnShelfHandlerName());
                        onShelfTasks.add(oldTaskOnShelf);
                    }
                }else{
                    VisualTaskTryOn taskTryOn = initVisualTaskTryOn(task.getTaskId(),taskDetail.getTryOnHandlerId(),taskDetail.getTryOnHandlerName(),oldTaskTryOn);
                    tryOnTasks.add(taskTryOn);
                    taskDetail.setLatestTryOnDetailId(taskTryOn.getTryOnDetailId());

                    //此时修图状态为待开始
                    VisualTaskOnShelf taskOnShelf = initVisualTaskOnShelf(task.getTaskId(),taskDetail.getOnShelfHandlerId(),taskDetail.getOnShelfHandlerName(),oldTaskOnShelf);
                    taskOnShelf.setHandleState(VisualTaskHandleStateEnum.WAITING_START.getCode());
                    onShelfTasks.add(taskOnShelf);
                    taskDetail.setLatestOnShelfDetailId(taskOnShelf.getOnShelfDetailId());
                }
            }
            //无需修图，只修图
            else{
                Assert.isTrue(req.getOnShelfHandlerId()!=null && StringUtils.isNotBlank(req.getOnShelfHandlerName()),"修图人不能为空");

                taskDetail.setOnShelfHandlerId(req.getOnShelfHandlerId());
                taskDetail.setOnShelfHandlerName(req.getOnShelfHandlerName());
                //如果没有变更处理方式，则更新处理人即可
                if(!isChangeProcessType && oldTaskOnShelf!=null) {
                    oldTaskOnShelf.setOnShelfHandlerName(taskDetail.getOnShelfHandlerName());
                    oldTaskOnShelf.setOnShelfHandlerId(taskDetail.getOnShelfHandlerId());
                    onShelfTasks.add(oldTaskOnShelf);
                }else{
                    //过期当前tryon处理任务
                    if(oldTaskTryOn!=null){
                        taskDetail.setTryOnHandlerId(null);
                        taskDetail.setTryOnHandlerName(null);
                        taskDetail.setLatestTryOnDetailId(null);
                        oldTaskTryOn.setIsLatest(Bool.NO.getCode());
                        tryOnTasks.add(oldTaskTryOn);
                    }
                    VisualTaskOnShelf taskOnShelf = initVisualTaskOnShelf(task.getTaskId(),req.getOnShelfHandlerId(),req.getOnShelfHandlerName(),oldTaskOnShelf);
                    taskDetail.setLatestOnShelfDetailId(taskOnShelf.getOnShelfDetailId());
                    onShelfTasks.add(taskOnShelf);
                }
            }
        }

        if(CollectionUtil.isNotEmpty(tryOnTasks)){
            visualTaskTryOnRepository.saveOrUpdateBatch(tryOnTasks);
        }
        if(CollectionUtil.isNotEmpty(onShelfTasks)){
            visualTaskOnShelfRepository.saveOrUpdateBatch(onShelfTasks);
        }
        visualTaskDetailRepository.updateBatchById(updateTaskDetails);
        //如果任务流程有变更，则需要更新对应的环节状态
        if(CollectionUtil.isNotEmpty(updateTasks)) {
            visualTaskRepository.updateBatchById(updateTasks);
            //刷新处理环节的状态
            List<Long> taskIds = updateTasks.stream().map(VisualTask::getTaskId).collect(Collectors.toList());
            visualTaskNodeStateService.updateHandleState(taskIds);
            //过期掉质检记录及状态，分配任务后（即调整了修图流程）需要重新质检
            visualQcRepository.expireByTaskIds(taskIds);
            visualTaskNodeStateRepository.deleteStep(taskIds, VisualTaskStepEnum.QC);

            //spu维度推送pop
            this.noticeTaskState2Pop(updateTasks, VisualTaskStateEnum.DOING);
        }
        //记录日志
        tasks.forEach(task->{
            visualTaskHelper.addLog(task.getStyleCode(),task.getTaskId(),"任务分配");
        });
    }

    private void noticeTaskState2Pop(List<VisualTask> updateTasks, VisualTaskStateEnum stateEnum) {
        List<VisualTask> doingTaskList = updateTasks.stream()
                .filter(task -> Objects.equals(stateEnum.getCode(), task.getState()))
                .toList();
        Map<String, VisualTask> doingTaskMap = StreamUtil.list2Map(doingTaskList, VisualTask::getStyleCode);
        if (CollUtil.isNotEmpty(doingTaskMap)) {
            doingTaskMap.forEach((styleCode, task) ->
                    visualTaskHelper.noticePopTaskState(task.getStyleCode(),
                            task.getProcessCode(), stateEnum)
            );
        }
    }

    private VisualTaskTryOn initVisualTaskTryOn(Long taskId,Long tryOnHandlerId,
                                     String tryOnHandlerName,VisualTaskTryOn oldTaskTryOn){
        if(oldTaskTryOn!=null){
            oldTaskTryOn.setTryOnHandlerId(tryOnHandlerId);
            oldTaskTryOn.setTryOnHandlerName(tryOnHandlerName);
            oldTaskTryOn.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
            return oldTaskTryOn;
        }else{
            VisualTaskTryOn taskTryOn = new VisualTaskTryOn();
            taskTryOn.setTryOnDetailId(IdPool.getId());
            taskTryOn.setTaskId(taskId);
            taskTryOn.setTryOnHandlerId(tryOnHandlerId);
            taskTryOn.setTryOnHandlerName(tryOnHandlerName);
            taskTryOn.setVersionNum(1);
//            taskTryOn.setTryOnImages(null);
//            taskTryOn.setOnShelfImages(null);
            taskTryOn.setIsLatest(Bool.YES.getCode());
            taskTryOn.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
            return taskTryOn;
        }
    }

    private VisualTaskOnShelf initVisualTaskOnShelf(Long taskId,Long onShelfHandlerId,
                                                    String onShelfHandlerName,VisualTaskOnShelf oldTaskOnShelf){
        if(oldTaskOnShelf!=null){
            oldTaskOnShelf.setOnShelfHandlerId(onShelfHandlerId);
            oldTaskOnShelf.setOnShelfHandlerName(onShelfHandlerName);
            oldTaskOnShelf.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
            return oldTaskOnShelf;
        }else{
            VisualTaskOnShelf taskOnShelf = new VisualTaskOnShelf();
            taskOnShelf.setOnShelfDetailId(IdPool.getId());
            taskOnShelf.setTaskId(taskId);
            taskOnShelf.setOnShelfHandlerId(onShelfHandlerId);
            taskOnShelf.setOnShelfHandlerName(onShelfHandlerName);
            taskOnShelf.setVersionNum(1);
//            taskOnShelf.setOnShelfImages(null);
            taskOnShelf.setIsLatest(Bool.YES.getCode());
            taskOnShelf.setHandleState(VisualTaskHandleStateEnum.DOING.getCode());
            return taskOnShelf;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelVisualTask(CancelVisualTaskReq req){
        List<VisualTask> tasks = visualTaskRepository.listByIds(req.getVisualTaskIds());
        Assert.isTrue(CollectionUtil.isNotEmpty(tasks),"没找到对应的需求任务");

        tasks.forEach(task->{
            Assert.isTrue(Bool.YES.getCode()!=task.getIsCancel(),
                    "任务状态为[已取消]不能操作取消");
        });

        doCancelVisualTask(tasks,false);
        return true;
    }

    public Boolean doCancelVisualTask(List<VisualTask> visualTasks,Boolean isCancelSpu){
        if(CollectionUtil.isEmpty(visualTasks)){
            return false;
        }
        String logPrefix = isCancelSpu ? "取消SPU-" : "取消任务-";
        Map<Long,VisualTask> taskIdMap = visualTasks.stream().collect(Collectors.toMap(VisualTask::getTaskId, v->v,(k1, k2)->k2));
        List<VisualTaskDetail> details = visualTaskDetailRepository.listByTaskIds(taskIdMap.keySet());
        Map<Long,VisualTaskDetail> taskIdToDetailMap = details.stream().collect(Collectors.toMap(VisualTaskDetail::getTaskId, v->v,(k1, k2)->k2));
        //取消质检任务
        List<Long> visualTaskIds = visualTasks.stream().map(VisualTask::getTaskId).toList();
        List<VisualQc> qcList = visualQcRepository.listLatestByTaskIds(visualTaskIds);
        List<VisualQc> cancelQcList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(qcList)){
            //这里不升级版本，直接变更状态
            qcList.stream()
                    .filter(v->!VisualTaskQcStateEnum.CANCEL.getCode().equals(v.getQcState()))
                    .forEach(qc -> {
                        if(!VisualTaskQcStateEnum.FINISH.getCode().equals(qc.getQcState())){
                            //取消质检任务
                            qc.setQcState(VisualTaskQcStateEnum.CANCEL.getCode());
                            cancelQcList.add(qc);
                        }
                    });
            if(CollectionUtil.isNotEmpty(cancelQcList)){
                visualQcRepository.updateBatchById(cancelQcList);

                cancelQcList.forEach(qc->{
//                    VisualTask visualTask =  taskIdMap.get(qc.getTaskId());
                    //记录环节状态
                    VisualTaskNodeEnum visualTaskNode = VisualTaskNodeEnum.UNKNOWN;
                    switch (VisualQcTypeEnum.findByCode(qc.getQcType()))  {
                        case VISUAL -> visualTaskNode = VisualTaskNodeEnum.VISUAL_QC;
                        case BUYER -> visualTaskNode = VisualTaskNodeEnum.BUYER_QC;
                        case TRY_ON -> visualTaskNode = VisualTaskNodeEnum.TRY_ON_QC;
                    }
                    visualTaskNodeStateService.saveVisualTaskNodeStepState(qc.getTaskId(), VisualTaskStepEnum.QC, visualTaskNode,VisualTaskQcStateEnum.CANCEL.getCode());
//                    //记录日志
//                    String logContent = logPrefix+"取消" +VisualQcTypeEnum.findDescByCode(qc.getQcType());
//                    visualTaskHelper.addLog(visualTask.getStyleCode(),visualTask.getTaskId(),logContent);
                });
            }
        }

        //取消视觉任务及最新的子任务
        List<VisualTask> cancelTaskList = new ArrayList<>();
        List<VisualTaskTryOn> cancelTryOnList = new ArrayList<>();
        List<VisualTaskOnShelf> cancelOnShelfList = new ArrayList<>();

        //这里不升级版本，直接变更状态
        visualTasks.stream()
                .filter(v->Bool.NO.getCode()==v.getIsCancel())
                .forEach(task -> {
                    if(!VisualTaskStateEnum.FINISH.getCode().equals(task.getState())){
                        //取消主任务
                        task.setState(VisualTaskStateEnum.CANCEL.getCode());
                    }
                    task.setIsCancel(Bool.YES.getCode());
                    cancelTaskList.add(task);

                    //取消子任务
                    VisualTaskDetail detail = taskIdToDetailMap.get(task.getTaskId());
                    if(detail.getLatestTryOnDetailId()!=null){
                        VisualTaskTryOn visualTaskTryOn = visualTaskTryOnRepository.getById(detail.getLatestTryOnDetailId());
                        if(!VisualTaskHandleStateEnum.FINISH.getCode().equals(visualTaskTryOn.getHandleState())){
                            visualTaskTryOn.setHandleState(VisualTaskHandleStateEnum.CANCEL.getCode());
                            cancelTryOnList.add(visualTaskTryOn);
                        }
                    }
                    if(detail.getLatestOnShelfDetailId()!=null){
                        VisualTaskOnShelf visualTaskOnShelf = visualTaskOnShelfRepository.getById(detail.getLatestOnShelfDetailId());
                        if(!VisualTaskHandleStateEnum.FINISH.getCode().equals(visualTaskOnShelf.getHandleState())){
                            visualTaskOnShelf.setHandleState(VisualTaskHandleStateEnum.CANCEL.getCode());
                            cancelOnShelfList.add(visualTaskOnShelf);
                        }
                    }
                });
        if(CollectionUtil.isNotEmpty(cancelTryOnList)){
            visualTaskTryOnRepository.updateBatchById(cancelTryOnList);
            cancelTryOnList.forEach(tryOn -> {
//                VisualTask visualTask =  taskIdMap.get(tryOn.getTaskId());
                //记录环节状态
                visualTaskNodeStateService.saveVisualTaskNodeStepState(tryOn.getTaskId(), VisualTaskStepEnum.HANDLE, VisualTaskNodeEnum.TRYON_HANDLE,VisualTaskHandleStateEnum.CANCEL.getCode());
//                //记录日志
//                String logContent = logPrefix+"取消" +VisualTaskNodeEnum.TRYON_HANDLE.getDesc();
//                visualTaskHelper.addLog(visualTask.getStyleCode(),visualTask.getTaskId(),logContent);
            });
        }
        if(CollectionUtil.isNotEmpty(cancelOnShelfList)){
            visualTaskOnShelfRepository.updateBatchById(cancelOnShelfList);
            cancelOnShelfList.forEach(onShelf -> {
//                VisualTask visualTask =  taskIdMap.get(onShelf.getTaskId());
                //记录环节状态
                visualTaskNodeStateService.saveVisualTaskNodeStepState(onShelf.getTaskId(), VisualTaskStepEnum.HANDLE, VisualTaskNodeEnum.ON_SHELF_HANDLE,VisualTaskHandleStateEnum.CANCEL.getCode());
//                //记录日志
//                String logContent = logPrefix+"取消" +VisualTaskNodeEnum.ON_SHELF_HANDLE.getDesc();
//                visualTaskHelper.addLog(visualTask.getStyleCode(),visualTask.getTaskId(),logContent);
            });
        }
        if(CollectionUtil.isNotEmpty(cancelTaskList)){
            visualTaskRepository.updateBatchById(cancelTaskList);
            cancelTaskList.forEach(visualTask -> {
                //记录日志
                String logContent = logPrefix+"取消视觉任务";
                visualTaskHelper.addLog(visualTask.getStyleCode(),visualTask.getTaskId(),logContent);
            });

            //spu维度推送pop
            this.noticeTaskState2Pop(cancelTaskList, VisualTaskStateEnum.CANCEL);
        }
        return true;
    }

    public VisualDemandVo getVisualDemandById(Long demandId){
        VisualDemand visualDemand = visualDemandRepository.getById(demandId);
        VisualDemandVo demandVo = new VisualDemandVo();
        BeanUtils.copyProperties(visualDemand, demandVo);
        //需求图
        if(StringUtils.isNotBlank(demandVo.getDemandImages())){
            demandVo.setDemandImageList(StrUtil.splitTrim(demandVo.getDemandImages(), StrUtil.COMMA));
        }
        //模特图
        if(StringUtils.isNotBlank(demandVo.getModelPic())){
            demandVo.setModelPicList(StrUtil.splitTrim(demandVo.getModelPic(), StrUtil.COMMA));
        }
        //背景图
        if(StringUtils.isNotBlank(demandVo.getBackgroundPic())){
            demandVo.setBackgroundPicList(StrUtil.splitTrim(demandVo.getBackgroundPic(), StrUtil.COMMA));
        }
        //姿势图
        if(StringUtils.isNotBlank(demandVo.getPosturePic())){
            demandVo.setPosturePicList(StrUtil.splitTrim(demandVo.getPosturePic(), StrUtil.COMMA));
        }
        demandVo.setModelReferenceImageList(JSON.parseArray(visualDemand.getModelReferenceImage(), TryOnModelReferenceImageDTO.class));
        demandVo.setBackgroundImageList(JSON.parseArray(visualDemand.getBackgroundImage(), BackgroundDTO.class));
        demandVo.setModelFaceImageList(JSON.parseArray(visualDemand.getModelFaceImage(), ModelFaceDTO.class));
        return demandVo;
    }

    public VisualTaskDetailVo getVisualTaskDetailById(Long taskId){
        //任务信息
        VisualTask visualTask = visualTaskRepository.getById(taskId);
        VisualTaskDetailVo vo = new VisualTaskDetailVo();
        BeanUtils.copyProperties(visualTask, vo);
        //视觉需求
        VisualDemandVo demandVo = getVisualDemandById(visualTask.getLatestDemandId());
        vo.setDemandVo(demandVo);
        //SPU信息
        VisualSpu visualSpu = visualSpuRepository.getByStyleCode(visualTask.getStyleCode());
        VisualSpuVo spuVo = new VisualSpuVo();
        BeanUtils.copyProperties(visualSpu, spuVo);
        vo.setVisualSpu(spuVo);
        //处理信息
        VisualTaskDetail visualTaskDetail = visualTaskDetailRepository.getByTaskId(taskId);
        VisualImagePackage visualImagePackage = visualImagePackageRepository.getLatestByStyleCode(visualTask.getStyleCode());
        //处理信息-修图
        if(visualTaskDetail.getLatestOnShelfDetailId()!=null){
            VisualTaskOnShelf visualTaskOnShelf = visualTaskOnShelfRepository.getById(visualTaskDetail.getLatestOnShelfDetailId());
            VisualTaskOnShelfVo visualTaskOnShelfVo = new VisualTaskOnShelfVo();
            BeanUtils.copyProperties(visualTaskOnShelf, visualTaskOnShelfVo);
            if(StringUtils.isNotBlank(visualTaskOnShelf.getOnShelfImages())){
                OnShelfImagePackage onShelfImages = JSONObject.parseObject(visualTaskOnShelf.getOnShelfImages(),OnShelfImagePackage.class);
                if(onShelfImages!=null && StringUtils.isNotBlank(onShelfImages.getStyleCode())){
                    visualTaskOnShelfVo.setOnShelfImages(onShelfImages);
                }
            }
            //没有编辑过则默认返回图片库的
            else if(visualImagePackage!=null && StringUtils.isNotBlank(visualImagePackage.getOnShelfImages())){
                OnShelfImagePackage onShelfImages = JSONObject.parseObject(visualImagePackage.getOnShelfImages(),OnShelfImagePackage.class);
                if(onShelfImages!=null && StringUtils.isNotBlank(onShelfImages.getStyleCode())) {
                    visualTaskOnShelfVo.setOnShelfImages(onShelfImages);
                }
            }
            OnShelfImagePackage initOnShelfImages = visualTaskHelper.initOnShelfImagePackageBySpu(visualSpu);
            //如果图片库都无，则根据SPU初始化
            if(visualTaskOnShelfVo.getOnShelfImages()==null){
                visualTaskOnShelfVo.setOnShelfImages(initOnShelfImages);
            }
            //如果已有记录，且任务未完结，则补充未处理过的SKC
            else if(VisualTaskStateEnum.WAITING.getCode().equals(visualTask.getState())
                ||VisualTaskStateEnum.DOING.getCode().equals(visualTask.getState())){
                Map<String,OnShelfImagePackage.SkcImage> designCodes = initOnShelfImages.getSkcImages().stream().collect(Collectors.toMap(OnShelfImagePackage.SkcImage::getDesignCode,v->v,(k1,k2)->k2));
                List<OnShelfImagePackage.SkcImage> skcImages = visualTaskOnShelfVo.getOnShelfImages().getSkcImages();
                if(CollectionUtil.isNotEmpty(skcImages)){
                    Set<String> existDesignCodes = skcImages.stream().map(OnShelfImagePackage.SkcImage::getDesignCode).collect(Collectors.toSet());
                    designCodes.forEach((designCode,skcImage) -> {
                        if(!existDesignCodes.contains(designCode)){
                            skcImages.add(skcImage);
                        }
                    });
                }
            }
            visualTaskOnShelfVo.setHandleStateDesc(VisualTaskHandleStateEnum.findByCode(visualTaskOnShelf.getHandleState()).getDesc());
            vo.setOnShelfVo(visualTaskOnShelfVo);

            // 返回 修图中 发起的AIBox任务结果图
            vo.setOnShelfAiBoxResultList(getOnShelfAiBoxResultList(taskId));
        }
        //处理信息-tryOn
        if(visualTaskDetail.getLatestTryOnDetailId()!=null){
            VisualTaskTryOn visualTaskTryOn = visualTaskTryOnRepository.getById(visualTaskDetail.getLatestTryOnDetailId());
            VisualTaskTryOnVo visualTaskTryOnVo = new VisualTaskTryOnVo();
            BeanUtils.copyProperties(visualTaskTryOn, visualTaskTryOnVo);
            visualTaskTryOnVo.setTryOnImageList(JSONObject.parseArray(visualTaskTryOn.getTryOnImages(), ImageFile.class));

            visualTaskTryOnVo.setHandleStateDesc(VisualTaskHandleStateEnum.findByCode(visualTaskTryOn.getHandleState()).getDesc());
            if(StringUtils.isNotBlank(visualTaskTryOn.getOnShelfImages())){
                OnShelfImagePackage onShelfImages = JSONObject.parseObject(visualTaskTryOn.getOnShelfImages(),OnShelfImagePackage.class);
                if(onShelfImages!=null && StringUtils.isNotBlank(onShelfImages.getStyleCode())){
                    visualTaskTryOnVo.setOnShelfImages(onShelfImages);
                }
            }//没有编辑过则默认返回图片库的
            else if(visualImagePackage!=null && StringUtils.isNotBlank(visualImagePackage.getOnShelfImages())){
                OnShelfImagePackage onShelfImages = JSONObject.parseObject(visualImagePackage.getOnShelfImages(),OnShelfImagePackage.class);
                if(onShelfImages!=null && StringUtils.isNotBlank(onShelfImages.getStyleCode())) {
                    visualTaskTryOnVo.setOnShelfImages(onShelfImages);
                }
            }
            OnShelfImagePackage initOnShelfImages = visualTaskHelper.initOnShelfImagePackageBySpu(visualSpu);
            //如果图片库都无，则根据SPU初始化
            if(visualTaskTryOnVo.getOnShelfImages()==null){
                visualTaskTryOnVo.setOnShelfImages(initOnShelfImages);
            }//如果已有记录，且任务未完结，则补充未处理过的SKC
            else if(VisualTaskStateEnum.WAITING.getCode().equals(visualTask.getState())
                    ||VisualTaskStateEnum.DOING.getCode().equals(visualTask.getState())){
                Map<String,OnShelfImagePackage.SkcImage> designCodes = initOnShelfImages.getSkcImages().stream().collect(Collectors.toMap(OnShelfImagePackage.SkcImage::getDesignCode,v->v,(k1,k2)->k2));
                List<OnShelfImagePackage.SkcImage> skcImages = visualTaskTryOnVo.getOnShelfImages().getSkcImages();
                if(CollectionUtil.isNotEmpty(skcImages)){
                    Set<String> existDesignCodes = skcImages.stream().map(OnShelfImagePackage.SkcImage::getDesignCode).collect(Collectors.toSet());
                    designCodes.forEach((designCode,skcImage) -> {
                        if(!existDesignCodes.contains(designCode)){
                            skcImages.add(skcImage);
                        }
                    });
                }
            }
            vo.setTryOnVo(visualTaskTryOnVo);

            // 返回线上try on 跑图的内容
            vo.setTryOnQcResultList(visualTaskTryOnLogService.listTryOnTaskImageDetail(taskId));
        }

        //现有上架图
        if(visualImagePackage!=null){
            VisualImagePackageVo onShelfImagePackageVo = visualImagePackageConverter.trans2VisualImagePackageVo(visualImagePackage);
            vo.setVisualImagePackage(onShelfImagePackageVo);
        }

        //质检问题
        CompletableFuture<Void> visualQcFuture = CompletableFuture.runAsync(() ->
        {
            List<VisualQc> qcList = visualQcRepository.listLatestByTaskId(taskId);
            if(CollectionUtil.isNotEmpty(qcList)){
                VisualQc noPassQc = qcList.stream().filter(v->VisualTaskQcResultEnum.NO_PASS.getCode().equals(v.getQcResult())).findFirst().orElse(null);
                if(noPassQc!=null){
                    VisualQcVo noPassQcVo = new VisualQcVo();
                    BeanUtils.copyProperties(noPassQc, noPassQcVo);
                    List<VisualQcRecordVo> qcRecordVos = JSONObject.parseArray(noPassQc.getQcRecord(),VisualQcRecordVo.class);
                    if(CollectionUtil.isNotEmpty(qcRecordVos)){
                        qcRecordVos.forEach(qcRecordVo->{
                            qcRecordVo.setRemarkImageList(StrUtil.splitTrim(qcRecordVo.getRemarkImages(), StrUtil.COMMA));
                        });
                    }
                    noPassQcVo.setQcRecords(qcRecordVos);
                    vo.setVisualQcProblem(noPassQcVo);
                }
            }
        }, asyncTaskExecutor);

        //款式素材
        CompletableFuture<Void> spuImageMaterialFuture = CompletableFuture.runAsync(() ->
        {
            SpuImageMaterial spuImageMaterial = buildSpuImageVo(visualSpu);
            // 找出对应的try on 结果图
            Map<String, List<String>> tryOnTaskImage = visualTaskTryOnLogService.listTryOnTaskImage(taskId);
            if(spuImageMaterial!=null){
                spuImageMaterial.getSkcImageMaterials().forEach( e -> {
                    e.setTryOnImagesMap(tryOnTaskImage);
                });
                vo.setSpuImageMaterial(spuImageMaterial);
            }
        }, asyncTaskExecutor);

        //SPU bom
        CompletableFuture<Void> spuBomFuture = CompletableFuture.runAsync(() ->
        {
            SkcBomSubmitReq skcBomSubmitReq = new SkcBomSubmitReq();
            skcBomSubmitReq.setStyleCodeList(Collections.singletonList(visualTask.getStyleCode()));
            List<Prototype> prototypeList = prototypeRepository.listBomSubmitSkc(skcBomSubmitReq);
            if(CollectionUtil.isNotEmpty(prototypeList)){
                List<SpuSubmitBomSkcVo> skcBoms = new ArrayList<>();
                prototypeList.forEach(skc->{
                    SpuSubmitBomSkcVo skcBom = new SpuSubmitBomSkcVo();
                    skcBom.setPrototypeId(skc.getPrototypeId());
                    skcBom.setVersionNum(skc.getVersionNum());
                    skcBom.setDesignCode(skc.getDesignCode());
                    skcBom.setStyleCode(skc.getStyleCode());
                    try {
                        BomOrderDetailVo bomOrderDetailVo = bomOrderService.getLatestBomOrderDetail4Inner(skc.getDesignCode(), true);
                        if(bomOrderDetailVo!=null && CollectionUtil.isNotEmpty(bomOrderDetailVo.getBomOrderMaterialList())){
                            skcBom.setBomOrderMaterials(bomOrderDetailVo.getBomOrderMaterialList());
                        }
                    }catch (Exception e){
                        log.error("根据designCode:{}查询bom详情异常",skc.getDesignCode(),e);
                    }
                    skcBoms.add(skcBom);
                });
                vo.setSkcBoms(skcBoms);
            }
        }, asyncTaskExecutor);

        //尺寸表
        CompletableFuture<Void> styleSizeFuture = CompletableFuture.runAsync(() ->
        {
            List<StyleSizeInfoVo> styleSizeInfoVos = sdpOrderHelper.listSizeInfoByStyleCode(Collections.singletonList(visualTask.getStyleCode()));
            vo.setStyleSizeInfo(CollectionUtil.isNotEmpty(styleSizeInfoVos) ? styleSizeInfoVos.getFirst() : null);
        }, asyncTaskExecutor);
        CompletableFuture.allOf(visualQcFuture,spuImageMaterialFuture,spuBomFuture,styleSizeFuture).join();
        return vo;
    }

    @Override
    public SpuImageMaterial buildSpuImageVo(VisualSpu visualSpu) {
        String styleCode = visualSpu.getStyleCode();
        SpuImageMaterial spuImageMaterial = null;
        //设计款: spu: aigc图; skc:设计图, bom物料图, 3D图
        if (Objects.equals(visualSpu.getStyleType(), SdpStyleTypeEnum.DESIGN.getCode())) {
            spuImageMaterial = prototypeManageService.buildSpuImageMaterial(styleCode);
        }
        //现货款: spu:商品图, tryOn图, skc图;
        else if (Objects.equals(visualSpu.getStyleType(), SdpStyleTypeEnum.SPOT.getCode())) {
            spuImageMaterial = spotSpuService.buildSpuImageMaterial(styleCode);
        }
        return spuImageMaterial;
    }

    @Override
    public Long submitImageDownloadTask(DownloadVisualTaskImageReq req){
        DesignAsyncTaskTypeEnum asyncTaskType = DesignAsyncTaskTypeEnum.findByCode(req.getDownloadVisualTaskImageType());
        Assert.isTrue(asyncTaskType!=null,"异步任务类型错误");
        String taskName = asyncTaskType.getDesc()+"_"+PURE_DATETIME_PATTERN.format(LocalDateTime.now());
        String parameters = JSONObject.toJSONString(req.getTaskIds());
        return downloadTaskService.createTask(taskName,asyncTaskType,parameters);
    }

    @Override
    public List<BatchUploadImagesResp> batchUploadImages(BatchUploadImagesReq req){
        VisualTaskTypeEnum visualTaskType = VisualTaskTypeEnum.findByCode(req.getVisualTaskType());

        List<BatchUploadImagesResp> uploadResultFailList = new ArrayList<>();
        //批量上传尺寸表
        if (Objects.equals(req.getUploadType(), VisualImgUploadTypeEnum.SIZE.getCode())) {
            return this.sizeImageUploadHandle(req, uploadResultFailList);
        }

        //记录前端校验失败的数据
        List<BatchUploadImagesReq.SpuImage> spuImageList = req.getSpuImageList();
        List<BatchUploadImagesReq.SpuImage> errorSpuList = spuImageList.stream().filter(v->CollectionUtil.isNotEmpty(v.getFrontErrorMsgList())).toList();
        if(CollectionUtil.isNotEmpty(errorSpuList)){
            errorSpuList.forEach(spuImage->{
                spuImage.getFrontErrorMsgList().forEach(errorMsg->{
                    BatchUploadImagesResp f = new BatchUploadImagesResp();
                    f.setStyleCode(spuImage.getStyleCode());
                    f.setFileName(errorMsg.getFileName());
                    f.setReason(errorMsg.getErrorMsg());
                    uploadResultFailList.add(f);
                });
            });
        }

        //处理前端校验通过的数据
        Map<String, List<BatchUploadImagesReq.SpuImage>> styleCodeToImageFileMap = spuImageList.stream().filter(v->CollectionUtil.isEmpty(v.getFrontErrorMsgList())).collect(Collectors.groupingBy(BatchUploadImagesReq.SpuImage::getStyleCode));
        List<VisualSpu> spuList = visualSpuRepository.listByStyleCodes(styleCodeToImageFileMap.keySet());
        if(CollectionUtil.isEmpty(spuList)){
            uploadResultFailList.addAll(styleCodeToImageFileMap.keySet().stream().map(styleCode -> {
                BatchUploadImagesResp f = new BatchUploadImagesResp();
                f.setStyleCode(styleCode);
                f.setReason("SPU不存在");
                return f;
            }).toList());
        }else {
            //SPU状态在视觉质检和买手质检不允许上传
            List<String> passedSpuList = styleCodeToImageFileMap.keySet().stream().toList();
            Set<String> visualQcSpuSet = getInQcVisualTaskSpuList(req.getVisualTaskType(), passedSpuList, VisualTaskNodeEnum.VISUAL_QC.getCode());
            Set<String> buyerQcSpuSet = getInQcVisualTaskSpuList(req.getVisualTaskType(), passedSpuList, VisualTaskNodeEnum.BUYER_QC.getCode());
            Set<String> existStyleCodes = spuList.stream().map(VisualSpu::getStyleCode).collect(Collectors.toSet());
            styleCodeToImageFileMap.forEach((styleCode, imageFileList) -> {
                if (!existStyleCodes.contains(styleCode)) {
                    BatchUploadImagesResp f = new BatchUploadImagesResp();
                    f.setStyleCode(styleCode);
                    f.setReason("SPU不存在");
                    uploadResultFailList.add(f);
                } else if (visualQcSpuSet.contains(styleCode)) {
                    BatchUploadImagesResp f = new BatchUploadImagesResp();
                    f.setStyleCode(styleCode);
                    f.setReason("SPU处于视觉质检状态不允许上传");
                    uploadResultFailList.add(f);
                } else if (buyerQcSpuSet.contains(styleCode)) {
                    BatchUploadImagesResp f = new BatchUploadImagesResp();
                    f.setStyleCode(styleCode);
                    f.setReason("SPU处于买手质检状态不允许上传");
                    uploadResultFailList.add(f);
                } else {
                    //找出指定类型的任务
                    List<VisualTask> visualTasks = visualTaskRepository.listLatestBySpuTaskType(Collections.singletonList(styleCode), Collections.singletonList(visualTaskType.getCode()));
                    // 判断视觉任务是否有正在进行中的线上try on 任务，如果有则不可以上传try on 图
                    List<VisualTask> failTaskList = visualTasks.stream()
                            .filter(v -> Objects.equals(v.getProcessType(), VisualTaskProcessTypeEnum.TRY_ON.getCode()) || Objects.equals(v.getProcessType(), VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF.getCode()))
                            .filter(v -> {
                                List<VisualTaskTryOnLog> processingLog = visualTaskTryOnLogRepository.getProcessingLogByVisualTaskId(v.getTaskId());
                                return CollectionUtil.isNotEmpty(processingLog);
                            }).toList();
                    if (CollectionUtil.isNotEmpty(failTaskList)) {
                       uploadResultFailList.addAll( failTaskList.stream().map( v -> {
                           BatchUploadImagesResp f = new BatchUploadImagesResp();
                           f.setProcessCode(v.getProcessCode());
                           f.setStyleCode(styleCode);
                           f.setReason("存在正在进行中的线上try on任务, 不允许提交try on 图");
                           return f;
                       }).toList());
                       visualTasks.removeAll(failTaskList);
                    }
                    if (CollectionUtil.isNotEmpty(visualTasks)) {
                        List<ImageFile> imageFiles = imageFileList.stream().flatMap(v -> v.getImageFiles().stream()).collect(Collectors.toList());
                        HandleBatchReq handleBatchReq = new HandleBatchReq();
                        handleBatchReq.setTaskIds(visualTasks.stream().map(VisualTask::getTaskId).collect(Collectors.toList()));
                        handleBatchReq.setUploadType(req.getUploadType());
                        handleBatchReq.setImageFileList(imageFiles);
                        try {
                            uploadResultFailList.addAll(visualTaskHandleService.batchUploadImageHandle(handleBatchReq));
                        } catch (Exception e) {
                            VisualImgUploadTypeEnum uploadTypeEnum = VisualImgUploadTypeEnum.findByCode(req.getUploadType());
                            log.error("批量上传" + (uploadTypeEnum.getDesc()) + "异常，styleCode:" + styleCode, e);
                            BatchUploadImagesResp f = new BatchUploadImagesResp();
                            f.setStyleCode(styleCode);
                            f.setReason("系统异常" + e.getMessage());
                            uploadResultFailList.add(f);
                        }
                    } else {
                        BatchUploadImagesResp f = new BatchUploadImagesResp();
                        f.setStyleCode(styleCode);
                        f.setReason("不存在有效的" + visualTaskType.getDesc() + "+视觉任务");
                        uploadResultFailList.add(f);
                    }
                }
            });
        }
        //创建异常信息下载任务
        handleBatchUploadImageFail(uploadResultFailList, DesignAsyncTaskTypeEnum.DOWNLOAD_VISUAL_TASK_BATCH_UPLOAD_IMAGE_FAIL);
        return uploadResultFailList;
    }

    private List<BatchUploadImagesResp> sizeImageUploadHandle(BatchUploadImagesReq req, List<BatchUploadImagesResp> uploadResultFailList) {
        BatchUploadImagesReq.SizeImageInfo sizeImageInfo = req.getSizeImageInfo();
        if(Objects.isNull(sizeImageInfo)){
            uploadResultFailList.add(BatchUploadImagesResp.builder().reason("尺寸表信息为空").build());
        }
        //记录前端校验失败的数据
        List<BatchUploadImagesReq.FrontErrorMsg> frontErrorMsgList = sizeImageInfo.getFrontErrorMsgList();
        if (CollUtil.isNotEmpty(frontErrorMsgList)) {
            frontErrorMsgList.forEach(errorMsg -> {
                uploadResultFailList.add(BatchUploadImagesResp.builder().fileName(errorMsg.getFileName()).reason(errorMsg.getErrorMsg()).build());
            });
        }
        //尺寸表图片批量上传处理
        if(CollectionUtil.isEmpty(sizeImageInfo.getImageFiles())){
            uploadResultFailList.add(BatchUploadImagesResp.builder().reason("提交的尺寸表图片为空").build());
        }else {
            Map<String, List<ImageFile>> imagesBySpuCodeMap = this.getRightSizeSpuGroupMap(uploadResultFailList, sizeImageInfo);
            this.doSizeImageUpload(req, uploadResultFailList, imagesBySpuCodeMap);
        }

        //创建异常信息下载任务
        handleBatchUploadImageFail(uploadResultFailList, DesignAsyncTaskTypeEnum.DOWNLOAD_VISUAL_TASK_BATCH_UPLOAD_SIZE_IMAGE_FAIL);
        return uploadResultFailList;
    }

    private Map<String, List<ImageFile>> getRightSizeSpuGroupMap(List<BatchUploadImagesResp> uploadResultFailList, BatchUploadImagesReq.SizeImageInfo sizeImageInfo) {
        // 获取图片名称-前的SPU去和在途的任务做匹配，匹配到在途的需求则用图片名称匹配当前任务内上架图的对应图片名称，存在重名则替换否则为新增，匹配失败则抛异常数据到excel内给下载中心，成功则提交对应任务
        List<ImageFile> imageFileList = sizeImageInfo.getImageFiles();

        // 记录格式不符的图片
        imageFileList.stream()
                .filter(image -> image.getOrgImgName() != null && !image.getOrgImgName().contains(StrUtil.DASHED))
                .forEach(image -> {
                    uploadResultFailList.add(BatchUploadImagesResp.builder().fileName(image.getOrgImgName()).reason("图片名称格式不符合要求，应包含'-'").build());
                });

        //筛选出符合命名规则的图片（包含"-"）
        List<ImageFile> validImages = imageFileList.stream()
                .filter(image -> image.getOrgImgName() != null && image.getOssImageUrl() != null)
                .filter(image -> image.getOrgImgName().contains(StrUtil.DASHED))
                .toList();

        //按SPU分组图片
        Map<String, List<ImageFile>> imagesBySpuCodeMap = new HashMap<>();
        for (ImageFile image : validImages) {
            String spuCode = image.getOrgImgName().substring(0, image.getOrgImgName().lastIndexOf(StrUtil.DASHED));
            imagesBySpuCodeMap.computeIfAbsent(spuCode, k -> new ArrayList<>()).add(image);
        }
        return imagesBySpuCodeMap;
    }

    private void doSizeImageUpload(BatchUploadImagesReq req, List<BatchUploadImagesResp> uploadResultFailList, Map<String, List<ImageFile>> imagesBySpuCodeMap) {
        Set<String> imageSpuSet = imagesBySpuCodeMap.keySet();
        log.info("=== 尺寸表图片上传 图片分组spu:{} ===", JSON.toJSONString(imageSpuSet));
        if (CollUtil.isEmpty(imageSpuSet)) {
            uploadResultFailList.add(BatchUploadImagesResp.builder().reason("提交的图片无有效spu").build());
            return;
        }

        // 3. spu查询在途尺寸表任务
        List<VisualSpu> spuList = visualSpuRepository.listByStyleCodes(imageSpuSet);
        List<VisualTask> sizeTaskList = visualTaskRepository.listLatestHandlingTaskBySpuTaskType(imageSpuSet, Collections.singletonList(VisualTaskTypeEnum.SIZE_TASK.getCode()));
        if (CollUtil.isEmpty(sizeTaskList)) {
            uploadResultFailList.add(BatchUploadImagesResp.builder().reason("所有上传图片无在途尺寸表任务").build());
            return;
        }
        Map<String, VisualTask> sizeTaskMap = StreamUtil.list2Map(sizeTaskList, VisualTask::getStyleCode);

        //尺寸表任务的最新上架图
        List<Long> sizeTaskIdList = StreamUtil.convertListAndDistinct(sizeTaskList, VisualTask::getTaskId);
        List<VisualTaskOnShelf> onShelfList = visualTaskOnShelfRepository.listLatestByTaskId(sizeTaskIdList);
        Map<Long, VisualTaskOnShelf> sizeOnShelfMap = StreamUtil.list2Map(onShelfList, VisualTaskOnShelf::getTaskId);

        //spu最新图包图
        List<VisualImagePackage> imagePackageList = visualImagePackageRepository.listLatestByStyleCodes(imageSpuSet);
        Map<String, VisualImagePackage> imagePackageMap = StreamUtil.list2Map(imagePackageList, VisualImagePackage::getStyleCode);

        //尺寸表图片上传处理
        this.handleSizeImageUpload(req, uploadResultFailList, spuList, imagesBySpuCodeMap, sizeTaskMap, sizeOnShelfMap, imagePackageMap);
    }

    private void handleSizeImageUpload(BatchUploadImagesReq req,
                                       List<BatchUploadImagesResp> uploadResultFailList,
                                       List<VisualSpu> spuList,
                                       Map<String, List<ImageFile>> imagesBySpuCodeMap,
                                       Map<String, VisualTask> sizeTaskMap,
                                       Map<Long, VisualTaskOnShelf> sizeOnShelfMap,
                                       Map<String, VisualImagePackage> imagePackageMap) {
        if(CollectionUtil.isEmpty(spuList)){
            uploadResultFailList.addAll(imagesBySpuCodeMap.keySet().stream()
                    .map(styleCode -> {
                        List<String> noSpuFileNameList = StreamUtil.convertListAndDistinct(imagesBySpuCodeMap.get(styleCode), ImageFile::getOrgImgName);
                        return BatchUploadImagesResp.builder()
                                .styleCode(styleCode).fileName(StrUtil.join(StrUtil.COMMA, noSpuFileNameList)).reason("SPU不存在")
                                .build();
                    })
                    .toList());
            return;
        }
        Set<String> existStyleCodes = spuList.stream().map(VisualSpu::getStyleCode).collect(Collectors.toSet());
        imagesBySpuCodeMap.forEach((styleCode, reqImageList) -> {
            log.info("=== 尺寸表图片上传 处理spu:{} ===", styleCode);
            List<String> fileNameList = StreamUtil.convertListAndDistinct(reqImageList, ImageFile::getOrgImgName);
            if (!existStyleCodes.contains(styleCode)) {
                uploadResultFailList.add(BatchUploadImagesResp.builder()
                        .styleCode(styleCode).fileName(StrUtil.join(StrUtil.COMMA, fileNameList)).reason("SPU不存在")
                        .build());
                return;
            }
            //最新在途尺寸表任务
            VisualTask onWaySizeTask = sizeTaskMap.get(styleCode);
            if (Objects.isNull(onWaySizeTask)) {
                uploadResultFailList.add(BatchUploadImagesResp.builder()
                        .styleCode(styleCode).fileName(StrUtil.join(StrUtil.COMMA, fileNameList)).reason("在途尺寸表任务不存在")
                        .build());
                return;
            }
            VisualTaskOnShelf taskOnShelf = sizeOnShelfMap.get(onWaySizeTask.getTaskId());

            //用图片名称匹配当前任务内上架图的对应图片名称，存在重名则替换否则为新增，匹配失败则抛异常数据到excel内给下载中心，成功则提交对应任务
            List<ImageFile> onShelfImageFileList = this.buildSizeUploadImage(styleCode, reqImageList, taskOnShelf, imagePackageMap);
            if (CollUtil.isEmpty(onShelfImageFileList)) {
                uploadResultFailList.add(BatchUploadImagesResp.builder()
                        .styleCode(styleCode).fileName(StrUtil.join(StrUtil.COMMA, fileNameList)).reason("尺寸表任务上架图为空")
                        .build());
                return;
            }
            log.info("=== 尺寸表图片上传 处理后的上传图片:{} ===", JSON.toJSONString(onShelfImageFileList));

            HandleBatchReq handleBatchReq = new HandleBatchReq();
            handleBatchReq.setTaskIds(Collections.singletonList(onWaySizeTask.getTaskId()));
            handleBatchReq.setUploadType(req.getUploadType());
            handleBatchReq.setImageFileList(onShelfImageFileList);
            try {
                uploadResultFailList.addAll(visualTaskHandleService.batchUploadImageHandle(handleBatchReq));
            } catch (Exception e) {
                VisualImgUploadTypeEnum uploadTypeEnum = VisualImgUploadTypeEnum.findByCode(req.getUploadType());
                log.error("尺寸表批量上传" + (uploadTypeEnum.getDesc()) + "异常，styleCode:" + styleCode, e);
                BatchUploadImagesResp f = new BatchUploadImagesResp();
                f.setStyleCode(styleCode);
                f.setReason("系统异常" + e.getMessage());
                uploadResultFailList.add(f);
            }
        });
    }

    private List<ImageFile> buildSizeUploadImage(String styleCode,
                                                 List<ImageFile> reqImageList,
                                                 VisualTaskOnShelf taskOnShelf,
                                                 Map<String, VisualImagePackage> imagePackageMap) {
        String onShelfImages = null;

        //已有的上架图片为空, 使用图包的图片; 否则使用修图环节的上架图
        if (Objects.isNull(taskOnShelf) || StrUtil.isBlank(taskOnShelf.getOnShelfImages())) {
            VisualImagePackage imagePackage = imagePackageMap.get(styleCode);
            onShelfImages = imagePackage.getOnShelfImages();
            log.debug("=== 尺寸表图片上传 获取图包图片 spu:{} ===", styleCode);
        }else {
            onShelfImages = taskOnShelf.getOnShelfImages();
        }
        if (StrUtil.isBlank(onShelfImages)) {
            return null;
        }

        //已有的上架图片
        OnShelfImagePackage onShelfImagePackage = JSONObject.parseObject(onShelfImages, OnShelfImagePackage.class);
        List<ImageFile> originImageList = new ArrayList<>();
        if(Objects.nonNull(onShelfImagePackage)){
            if (CollUtil.isNotEmpty(onShelfImagePackage.getSpuImages())) {
                originImageList.addAll(onShelfImagePackage.getSpuImages().stream()
                        .filter(item -> CollUtil.isNotEmpty(item.getImages()))
                        .flatMap(v -> v.getImages().stream()).toList());
            }
            if (CollUtil.isNotEmpty(onShelfImagePackage.getSkcImages())) {
                originImageList.addAll(onShelfImagePackage.getSkcImages().stream()
                        .filter(item -> CollUtil.isNotEmpty(item.getImages()))
                        .flatMap(v -> v.getImages().stream()).toList());
            }
        }
        Map<String, ImageFile> originImageMap = StreamUtil.list2Map(originImageList, ImageFile::getOrgImgName);

        //存在重名则替换否则为新增
        reqImageList.forEach(reqImage -> {
            ImageFile imageFile = originImageMap.get(reqImage.getOrgImgName());
            if (Objects.isNull(imageFile)) {
                originImageList.add(reqImage);
                log.debug("=== 尺寸表图片上传 新增图片:{} ===", JSON.toJSONString(reqImage));
            }else {
                imageFile.setOssImageUrl(reqImage.getOssImageUrl());
                log.debug("=== 尺寸表图片上传 更新图片:{} ===", JSON.toJSONString(reqImage));
            }
        });

        return originImageList;
    }

    @Override
    public List<VisualTaskAllocateInfoVo> getNewArrivalTaskAllocateInfoBySpu(List<String> styleCodes){
        return styleCodes.stream().map(styleCode->{
            VisualTaskAllocateInfoVo resp = new VisualTaskAllocateInfoVo();
            resp.setStyleCode(styleCode);
            //SPU对应的上新任务
            VisualTask newArrivalTask = visualTaskRepository.getLatestNewArrivalTaskBySpu(styleCode);
            if(newArrivalTask==null){
                return resp;
            }
            VisualTaskDetail visualTaskDetail = visualTaskDetailRepository.getByTaskId(newArrivalTask.getTaskId());
            resp.setTaskId(newArrivalTask.getTaskId());
            resp.setProcessType(newArrivalTask.getProcessType());
            resp.setOnShelfHandlerName(visualTaskDetail!=null ? visualTaskDetail.getOnShelfHandlerName() : null);
            resp.setOnShelfHandlerId(visualTaskDetail!=null ? visualTaskDetail.getOnShelfHandlerId() : null);
            resp.setTryOnHandlerId(visualTaskDetail!=null ? visualTaskDetail.getTryOnHandlerId() : null);
            resp.setTryOnHandlerName(visualTaskDetail!=null ? visualTaskDetail.getTryOnHandlerName() : null);
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<VisualTaskProcessTypeVo> showOptionalProcessType(Long taskId){
        VisualTask task = visualTaskRepository.getById(taskId);
        Assert.isTrue(task!=null,"视觉任务不存在");
        VisualTaskProcessTypeEnum currentProcessType = VisualTaskProcessTypeEnum.findByCode(task.getProcessType());

        //任务已完成，已取消，则不能再变更处理方式
        if(VisualTaskStateEnum.FINISH.getCode().equals(task.getState())
                || Bool.YES.getCode()==task.getIsCancel()){
            return Collections.emptyList();
        }

        Set<VisualTaskProcessTypeEnum> processTypes = Arrays.stream(VisualTaskProcessTypeEnum.values())
                .filter(v->
                                !v.equals(VisualTaskProcessTypeEnum.UNKNOWN)).collect(Collectors.toSet());

        Set<VisualTaskProcessTypeEnum> notChecked = new HashSet<>();
        //如果有提交过处理内容，则不能再选择无须修图类型
        boolean hasSubmit = false;
        VisualTaskDetail visualTaskDetail = visualTaskDetailRepository.getByTaskId(task.getTaskId());
        if(visualTaskDetail.getLatestTryOnDetailId()!=null){
            VisualTaskTryOn taskTryOn = visualTaskTryOnRepository.getById(visualTaskDetail.getLatestTryOnDetailId());
            if(taskTryOn!=null && taskTryOn.getHandleState().equals(VisualTaskHandleStateEnum.FINISH.getCode())){
                hasSubmit = true;
                notChecked.add(VisualTaskProcessTypeEnum.NO_FIX);
                notChecked.add(VisualTaskProcessTypeEnum.TRY_ON);
            }
        }
        if(visualTaskDetail.getLatestOnShelfDetailId()!=null){
            VisualTaskOnShelf taskOnShelf = visualTaskOnShelfRepository.getById(visualTaskDetail.getLatestOnShelfDetailId());
            if(taskOnShelf!=null && taskOnShelf.getHandleState().equals(VisualTaskHandleStateEnum.FINISH.getCode())){
                hasSubmit = true;
                notChecked.add(VisualTaskProcessTypeEnum.NO_FIX);
                notChecked.add(VisualTaskProcessTypeEnum.TRY_0N_AND_FIX_ON_SHELF);
                if(!VisualTaskProcessTypeEnum.NO_FIX.equals(currentProcessType)){
                    notChecked.add(VisualTaskProcessTypeEnum.FIX_ON_SHELF);
                }
            }
        }
        if(hasSubmit){
            processTypes = processTypes.stream()
                    .filter(t->!notChecked.contains(t))
                    .collect(Collectors.toSet());
        }
        return processTypes.stream().map(v->{
                    return VisualTaskProcessTypeVo.builder()
                            .processType(v.getCode())
                            .processTypeName(v.getDesc()).build();
                }).toList();
    }

    @Override
    public List<VisualTaskInnerVo> listLatestBySpu(List<String> spuCodeList) {
        if (CollUtil.isEmpty(spuCodeList)) {
            return Collections.emptyList();
        }
        if (CollUtil.isNotEmpty(spuCodeList) && spuCodeList.size() > 1000) {
            throw new SdpDesignException("数量不能大于1000");
        }

        List<VisualTask> visualTaskList = visualTaskRepository.listLatestVisualTaskBySpu(spuCodeList);
        if (CollUtil.isEmpty(visualTaskList)) {
            return Collections.emptyList();
        }
        return StreamUtil.convertListBean(visualTaskList, VisualTaskInnerVo.class);
    }

    @Override
    public String batchTryOn(List<BatchTryOnReq> reqList) {
        // 查询任务状态
        Set<Long> taskIdSet = reqList.stream().map(BatchTryOnReq::getTaskId).collect(Collectors.toSet());
        List<VisualTask> visualTaskList = visualTaskRepository.listByIds(taskIdSet);
        Map<Long, BatchTryOnReq> taskIdReqMap = reqList.stream().collect(Collectors.toMap(BatchTryOnReq::getTaskId, Function.identity(), (v1, v2) -> v1));
        AtomicInteger successCount = new AtomicInteger();
        StringBuilder failProcessCode = new StringBuilder();
        for (VisualTask visualTask : visualTaskList) {
            BatchTryOnReq req = taskIdReqMap.get(visualTask.getTaskId());
            if (req == null) continue;
            try {
                visualTaskTryOnLogService.doSubmitTryOn(successCount, failProcessCode, visualTask, req);
            }catch (Exception e){
                log.error("batch Try on catch error , taskId: {}, message: {}",req.getTaskId(), e.getMessage());
                failProcessCode.append(" ");
                failProcessCode.append(visualTask.getProcessCode());
            }
        }
        String tips = "成功创建" + successCount + "个任务";
        if (failProcessCode.length() > 0) {
            tips += "，失败的任务编码：" + failProcessCode;
        }
        return tips;
    }

    @Override
    public List<BeforeAIBoxVo> beforeInvokeAiBox(List<Long> taskIdList) {
        List<VisualTask> visualTaskList = visualTaskRepository.listByIds(taskIdList);
        List<BeforeAIBoxVo> beforeAIBoxVoList = new ArrayList<>();
        for (VisualTask visualTask : visualTaskList) {
            if (!VisualTaskStateEnum.DOING.getCode().equals(visualTask.getState())) {
                log.info("batch try on task state is not doing, taskId:{}", visualTask.getTaskId());
                continue;
            }
            VisualTaskTryOn tryOnDetail = visualTaskTryOnRepository.getOne(new LambdaQueryWrapper<VisualTaskTryOn>()
                    .eq(VisualTaskTryOn::getTaskId, visualTask.getTaskId())
                    .eq(VisualTaskTryOn::getIsLatest, 1)
                    .last("limit 1"));
            // try on已经取消或者已经质检完成，不允许再提交任务
            if (tryOnDetail == null || VisualTaskHandleStateEnum.CANCEL.getCode().equals(tryOnDetail.getHandleState()) || VisualTaskHandleStateEnum.FINISH.getCode().equals(tryOnDetail.getHandleState())) {
                log.info("try on task current state can't handle, taskId:{}", visualTask.getTaskId());
                continue;
            }
            VisualTaskNodeState tryOnHandleNodeState = visualTaskNodeStateRepository.getNodeByProcessNode(visualTask.getTaskId(), VisualTaskNodeEnum.TRYON_HANDLE);
            VisualTaskNodeState tryOnQCNode = visualTaskNodeStateRepository.getNodeByProcessNode(visualTask.getTaskId(), VisualTaskNodeEnum.TRY_ON_QC);
            if (tryOnHandleNodeState == null) {
                log.error("batch try on task is not exist, taskId:{}", visualTask.getTaskId());
                continue;
            }
            // 1.try on 进行中
            boolean tryOnHandling = VisualTaskHandleStateEnum.DOING.getCode().equals(tryOnHandleNodeState.getProcessNodeState());
            // 2.try on已完成，try on质检中，重新发起try on
            boolean tryOnDoneOrQc = VisualTaskHandleStateEnum.FINISH.getCode().equals(tryOnHandleNodeState.getProcessNodeState()) &&
                    VisualTaskHandleStateEnum.getProcessingStatus().contains(tryOnQCNode.getProcessNodeState());
            // 以上情况都不是 不允许发起try on任务
            if (!tryOnHandling && !tryOnDoneOrQc) {
                log.error("current step is not allow to submit try on task, taskId:{}", visualTask.getTaskId());
                continue;
            }

            beforeAIBoxVoList.add(
                    BeforeAIBoxVo.builder()
                            .taskId(visualTask.getTaskId())
                            .tryOnDetailId(tryOnDetail.getTryOnDetailId())
                            .build()
            );
        }
        return beforeAIBoxVoList;
    }

    private void handleBatchUploadImageFail(List<BatchUploadImagesResp> uploadResultFailList, DesignAsyncTaskTypeEnum asyncTaskType){
        if(CollectionUtil.isNotEmpty(uploadResultFailList)){
            String taskName = asyncTaskType.getDesc() + StrUtil.UNDERLINE + PURE_DATETIME_PATTERN.format(LocalDateTime.now());
            String parameters = JSONObject.toJSONString(uploadResultFailList);
            downloadTaskService.createTask(taskName,asyncTaskType,parameters);
        }
    }

    /**
     * 根据视觉任务id获取修图中发起的AIBox生成图
     */
    private List<ImageFile> getOnShelfAiBoxResultList(Long taskId) {
        List<VisualOnShelfAiBoxImage> visualOnShelfAiBoxImages = visualOnShelfAiBoxImageRepository.listByTaskId(taskId);
        if (CollectionUtil.isEmpty(visualOnShelfAiBoxImages)){
            return Collections.emptyList();
        }

        return visualOnShelfAiBoxImages.stream()
                .map(e -> new ImageFile(e.getGeneratedImgName(), e.getGeneratedImg()))
                .toList();
    }

    /**
     * 获取在(视觉/买手)质检状态的视觉任务的SPU集合
     * @param taskType  任务类型1上新任务2尺寸任务3优化任务 参考VisualTaskTypeEnum
     * @param styleCodeList SPU编码
     * @param processNode   节点编码 参考VisualNodeEnum 101-tryOn处理 102-上架图处理 201-视觉质检 202-买手质检
     * @return  在(视觉/买手)质检状态的SPU集合
     */
    private Set<String> getInQcVisualTaskSpuList(Integer taskType,
                    List<String> styleCodeList,
                    Integer processNode) {
        if (CollectionUtil.isEmpty(styleCodeList)) {
            return Collections.emptySet();
        }
        VisualTaskQuery visualTaskQuery = new VisualTaskQuery();
        visualTaskQuery.setPageNum(1);
        visualTaskQuery.setPageSize(1000);
        visualTaskQuery.setTaskType(taskType);
        visualTaskQuery.setPersonal(Boolean.FALSE);
        visualTaskQuery.setStyleCodeList(styleCodeList);
        visualTaskQuery.setProcessNode(processNode);
        visualTaskQuery.setProcessStep(VisualTaskStepEnum.QC.getCode());
        visualTaskQuery.setNodeState(0);
        IPage<VisualTaskInfoVo> page = visualTaskRepository.queryByPage(visualTaskQuery);
        return page.getRecords().stream().map(VisualTaskInfoVo::getStyleCode).collect(Collectors.toSet());
    }

}
