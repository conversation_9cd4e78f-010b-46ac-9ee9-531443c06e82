package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.Json;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yibuyun.scm.common.enums.fabric.CategoryNo1Enum;
import com.yibuyun.scm.common.enums.fabric.CategoryNo2Enum;
import com.zjkj.scf.bundle.common.dto.demand.enums.CommodityTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.MaterialSnapshot;
import tech.tiangong.sdp.design.enums.MaterialDemandTypeEnum;
import tech.tiangong.sdp.design.repository.MaterialSnapshotRepository;
import tech.tiangong.sdp.design.service.MaterialSkuSnapshotService;
import tech.tiangong.sdp.design.service.MaterialSnapshotService;
import tech.tiangong.sdp.design.vo.query.material.MaterialSnapshotQuery;
import tech.tiangong.sdp.design.vo.req.material.MaterialSkuSnapshotReq;
import tech.tiangong.sdp.design.vo.req.material.MaterialSnapshotCreateReq;
import tech.tiangong.sdp.design.vo.req.material.SnapshotUnitUpdateReq;
import tech.tiangong.sdp.design.vo.resp.material.MaterialSnapshotVo;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 物料快照表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialSnapshotServiceImpl implements MaterialSnapshotService {
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final MaterialSkuSnapshotService skuSnapshotService;


    @Override
    public PageRespVo<MaterialSnapshotVo> page(MaterialSnapshotQuery query) {
        IPage<MaterialSnapshot> page = materialSnapshotRepository.findPage(query);

        List<MaterialSnapshot> records = page.getRecords();
        List<MaterialSnapshotVo> voList = List.of();
        if (CollUtil.isNotEmpty(records)) {
            voList = records.stream().map(this::entity2Vo).collect(Collectors.toList());
        }

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), voList);
    }

    @Override
    public MaterialSnapshotVo getById(Long id) {
        MaterialSnapshot entity = materialSnapshotRepository.getById(id);
        SdpDesignException.notNull(entity, "物料快照不存在! ");
        return this.entity2Vo(entity);
    }

    @Override
    public List<MaterialSnapshotVo> listByIds(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return List.of();
        }

        List<MaterialSnapshot> materialSnapshotList = materialSnapshotRepository.listByIds(idList);
        if (CollUtil.isEmpty(materialSnapshotList)) {
            return List.of();
        }

        return materialSnapshotList.stream().map(this::entity2Vo).collect(Collectors.toList());
    }

    @Override
    public List<MaterialSnapshotVo> listBySkuId(Long skuId) {
        if (Objects.isNull(skuId)) {
            return List.of();
        }
        List<MaterialSnapshot> materialSnapshotList = materialSnapshotRepository.listBySkuId(skuId);
        if (CollUtil.isEmpty(materialSnapshotList)) {
            return List.of();
        }

        return materialSnapshotList.stream().map(this::entity2Vo).collect(Collectors.toList());
    }

    private MaterialSnapshotVo entity2Vo(MaterialSnapshot entity) {
        MaterialSnapshotVo vo = new MaterialSnapshotVo();
        BeanUtils.copyProperties(entity, vo);
        this.resetValues(entity, vo);
        return vo;
    }

    /**
     * 属性转换
     */
    private void resetValues(MaterialSnapshot entity, MaterialSnapshotVo vo) {
        //物料图片
        String materialPictureJson = entity.getMaterialPicture();
        if (StringUtils.isNotBlank(materialPictureJson)) {
            vo.setMaterialPictureList(JSON.parseArray(materialPictureJson, String.class));
        }
        //成分
        String material = entity.getMaterial();
        if (StringUtils.isNotBlank(material)) {
            List<MaterialSnapshotVo.ComponentVo> componentVoList = JSON.parseArray(material, MaterialSnapshotVo.ComponentVo.class);
            vo.setComponentList(componentVoList);
        }
        //辅料属性
        String skuAttrs = entity.getSkuAttrs();
        if (StringUtils.isNotBlank(skuAttrs)) {
            List<MaterialSnapshotVo.SkuAttrValueVo> skuAttrValueVos = JSON.parseArray(skuAttrs, MaterialSnapshotVo.SkuAttrValueVo.class);
            vo.setSkuAttrList(skuAttrValueVos);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(MaterialSnapshotCreateReq req) {
        log.info("=== 物料快照-创建-req:{} ===", JSON.toJSONString(req));
        MaterialSnapshot entity = new MaterialSnapshot();
        long materialSnapshotId = IdPool.getId();
        BeanUtils.copyProperties(req, entity);

        //物料图片信息
        List<String> materialPictureList = req.getMaterialPictureList();
        if (CollUtil.isNotEmpty(materialPictureList)) {
            entity.setMaterialPicture(Json.serialize(materialPictureList));
        }

        entity.setMaterialSnapshotId(materialSnapshotId);
        //辅料最小单位
        entity.setMinPriceUnitOrigin(req.getMinPriceUnit());
        //面料花型
        entity.setFlowerCategory(StringUtils.isBlank(req.getCategory()) ? null : req.getCategory());

        //设置面辅料的类目信息
        this.setCategoryInfo(req, entity);

        materialSnapshotRepository.save(entity);
        log.info("=== 物料快照-创建成功-id:{} ===", materialSnapshotId);

        //记录sku全部属性快照信息
        MaterialSkuSnapshotReq skuSnapshotReq = MaterialSkuSnapshotReq.builder()
                .materialSnapshotId(materialSnapshotId)
                .skuId(entity.getSkuId())
                .materialType(entity.getMaterialType())
                .commodityType(entity.getCommodityType())
                .skuSnapshot(req.getSkuSnapshot())
                .build();
        skuSnapshotService.create(skuSnapshotReq);

        return materialSnapshotId;
    }

    private void setCategoryInfo(MaterialSnapshotCreateReq req, MaterialSnapshot entity) {
        String linkCode = StrUtil.BRACKET_START + StrUtil.DASHED + StrUtil.BRACKET_END;
        String categoryCode = null;
        String categoryName = null;

        //设置面辅料类目
        if (Objects.equals(MaterialDemandTypeEnum.FABRIC.getCode(), req.getMaterialType())) {

            String fabricCode = null;
            String fabricName = null;
            //categoryNo1要转换: code:  1 -> PURE ; 2 -> FLOWER; name: 1->净色; 2->花型;
            if (Objects.equals(CategoryNo1Enum.CATEGORY_NO1_1.getCode(), req.getCategoryNo1())) {
                fabricCode = CommodityTypeEnum.PURE.getCode();
                fabricName = CommodityTypeEnum.PURE.getDesc();
            } else if (Objects.equals(CategoryNo1Enum.CATEGORY_NO1_2.getCode(), req.getCategoryNo1())) {
                fabricCode = CommodityTypeEnum.FLOWER.getCode();
                fabricName = CommodityTypeEnum.FLOWER.getDesc();
            }
            //面料类目编码: "FABRIC[-]FLOWER[-]2"
            categoryCode = MaterialDemandTypeEnum.FABRIC.name() + linkCode + fabricCode + linkCode + req.getCategoryNo2();

            //面料类目名称: "面料[-]花型[-]梭织"
            categoryName = MaterialDemandTypeEnum.FABRIC.getDesc() + linkCode + fabricName + linkCode + CategoryNo2Enum.getByCode(req.getCategoryNo2()).getDesc();

        } else if (Objects.equals(MaterialDemandTypeEnum.ACCESSORIES.getCode(), req.getMaterialType())) {

            //辅料类目编码: "ACCESSORIES[-]6814441129560270848[-]6814441129560271064"
            List<Long> accessoriesCategoryIds = req.getAccessoriesCategoryIds();
            if (CollUtil.isNotEmpty(accessoriesCategoryIds)) {
                categoryCode = MaterialDemandTypeEnum.ACCESSORIES.name() + linkCode + StrUtil.join(linkCode, accessoriesCategoryIds);
            }

            //辅料类目名称: "辅料[-]综合类[-]魔术贴"
            List<String> accessoriesCategoryNames = req.getAccessoriesCategoryNames();
            if (CollUtil.isNotEmpty(accessoriesCategoryNames)) {
                categoryName = MaterialDemandTypeEnum.ACCESSORIES.name() + linkCode + StrUtil.join(linkCode, accessoriesCategoryNames);
            }
        }
        entity.setCategoryCode(categoryCode);
        entity.setCategoryName(categoryName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<Long, Long> batchCreate4Bom(List<MaterialSnapshotCreateReq> reqList) {
        SdpDesignException.notEmpty(reqList, "入参为空! ");
        log.info("=== 物料快照-批量创建-req:{} ===", JSON.toJSONString(reqList));
        Map<Long, Long> resultMap = new HashMap<>(reqList.size());

        reqList.forEach(item -> {
            Long materialSnapshotId = this.create(item);
            resultMap.put(item.getBomMaterialId(), materialSnapshotId);
        });
        log.info("=== 物料快照-批量创建成功:{} ===", JSON.toJSONString(resultMap));
        return resultMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateMinPriceUnit(List<SnapshotUnitUpdateReq> req) {
        log.info("=== 用量核算更新辅料最小单位-req:{} ===", JSON.toJSONString(req));
        SdpDesignException.notEmpty(req, "入参为空! ");
        req.forEach(item -> {
            Long materialSnapshotId = item.getMaterialSnapshotId();
            MaterialSnapshot entity = materialSnapshotRepository.getById(materialSnapshotId);
            if (Objects.isNull(entity)) {
                return;
            }
            SdpDesignException.isTrue(Objects.equals(entity.getMaterialType(), MaterialDemandTypeEnum.ACCESSORIES.getCode()),
                    "非辅料不能更新最小单位, materialSnapshotId:{}", materialSnapshotId);

            // 根据主键更新辅料最小单位
            boolean update = materialSnapshotRepository.update(Wrappers.lambdaUpdate(MaterialSnapshot.class)
                    .set(MaterialSnapshot::getMinPriceUnit, item.getMiniPriceUnit())
                    .set(MaterialSnapshot::getRemark,"用量核算更新辅料最小单位")
                    .set(MaterialSnapshot::getRevisedTime, LocalDateTime.now())
                    .eq(MaterialSnapshot::getMaterialSnapshotId, materialSnapshotId));
            if (update) {
                log.info("=== 用量核算更新辅料最小单位-更新成功, {} ===",item);
            }
        });
    }



}
