package tech.tiangong.sdp.design.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.SpotSpu;
import tech.tiangong.sdp.design.entity.SpotSpuDetail;
import tech.tiangong.sdp.design.entity.SpotSpuTryOnConfig;
import tech.tiangong.sdp.design.enums.DesignLogBizTypeEnum;
import tech.tiangong.sdp.design.enums.DesignLogContentEnum;
import tech.tiangong.sdp.design.enums.spot.SpotProductPictureStateEnum;
import tech.tiangong.sdp.design.enums.spot.SpotTryOnStateEnum;
import tech.tiangong.sdp.design.repository.SpotSpuDetailRepository;
import tech.tiangong.sdp.design.repository.SpotSpuRepository;
import tech.tiangong.sdp.design.repository.SpotSpuTryOnConfigRepository;
import tech.tiangong.sdp.design.service.DesignLogService;
import tech.tiangong.sdp.design.service.SpotSkcService;
import tech.tiangong.sdp.design.service.SpotSpuService;
import tech.tiangong.sdp.design.service.TryOnService;
import tech.tiangong.sdp.design.vo.req.log.DesignLogSdpSaveReq;
import tech.tiangong.sdp.design.vo.req.tryon.CheckForCreateReq;
import tech.tiangong.sdp.design.vo.req.tryon.GetParamsForCreateReq;
import tech.tiangong.sdp.design.vo.req.tryon.SyncSpuTryOnTaskStateReq;
import tech.tiangong.sdp.design.vo.resp.tryon.CreateTryOnParamVo;
import tech.tiangong.sdp.utils.StreamUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Service
public class TryOnServiceImpl implements TryOnService {

    private final SpotSpuRepository spotSpuRepository;
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final SpotSpuTryOnConfigRepository spotSpuTryOnConfigRepository;
    private final SpotSkcService spotSkcService;
    private final SpotSpuService spotSpuService;
    private final DesignLogService designLogService;
    @Override
    public Boolean checkForCreate(CheckForCreateReq req) {
        List<SpotSpu> list = spotSpuRepository.listByStyleCodes(req.getStyleCodes());
        List<Long> spotSpuIds = list.stream().map(SpotSpu::getSpotSpuId).collect(Collectors.toList());
        List<SpotSpuDetail> details = spotSpuDetailRepository.listBySpotSpuIds(spotSpuIds);
        Map<Long,SpotSpuDetail> spotSpuIdToDetailMap = details.stream().collect(Collectors.toMap(SpotSpuDetail::getSpotSpuId, v->v,(k1, k2)->k2));
        List<SpotSpuTryOnConfig> configs = spotSpuTryOnConfigRepository.listByStyleCodes(req.getStyleCodes());
        Map<String,SpotSpuTryOnConfig> configMap = configs.stream().collect(Collectors.toMap(SpotSpuTryOnConfig::getStyleCode,v->v,(k1, k2)->k2));
        list.forEach(spotSpu -> {
            //try on状态=待分配、待创建、未通过；去掉供给方式的校验；v1.0.5
            SdpDesignException.isTrue(Objects.equals(spotSpu.getTryOnStatus(), SpotTryOnStateEnum.WAIT_ALLOCATE.getCode())
                    || Objects.equals(spotSpu.getTryOnStatus(), SpotTryOnStateEnum.WAIT_CREATE.getCode())
                            || Objects.equals(spotSpu.getTryOnStatus(), SpotTryOnStateEnum.NO_PASS.getCode()),
                    "待分配、待创建、未通过的tryOn状态才支持发起tryOn");
            /*
            Assert.isTrue(spotSpu.getTryOnStatus()==null
                            || SpotTryOnStateEnum.PASS.getCode().intValue()!=spotSpu.getTryOnStatus(),
                    "存在已完成try on的SPU，请勿重复勾选");
            SpotSpuTryOnConfig spotSpuTryOnConfig = configMap.get(spotSpu.getStyleCode());
            Assert.isTrue(spotSpuTryOnConfig!=null,spotSpu.getStyleCode()+"未进行tryOn任务分配");
             */

            Assert.isTrue(spotSpu.getProductPictureStatus()!=null
                            && SpotProductPictureStateEnum.FINISH.getCode().intValue()==spotSpu.getProductPictureStatus(),
                    "存在商品图未齐全的SPU，不支持try on，请先维护商品图");
            SpotSpuDetail detail = spotSpuIdToDetailMap.get(spotSpu.getSpotSpuId());
            Assert.isTrue(detail!=null && !CollectionUtils.isEmpty(detail.getProductPictureList()),"存在商品图为空的SPU，不支持try on，请先维护商品图");

        });
        return true;
    }

    @Override
    public List<CreateTryOnParamVo> getParamsForCreate(GetParamsForCreateReq req) {
        List<SpotSpu> list = spotSpuRepository.listByStyleCodes(req.getStyleCodes());
        List<Long> spotSpuIds = list.stream().map(SpotSpu::getSpotSpuId).collect(Collectors.toList());
        List<SpotSpuDetail> details = spotSpuDetailRepository.listBySpotSpuIds(spotSpuIds);
        Map<Long,SpotSpuDetail> spotSpuIdToDetailMap = details.stream().collect(Collectors.toMap(SpotSpuDetail::getSpotSpuId, v->v,(k1, k2)->k2));
        List<String> styleCodes = list.stream().map(SpotSpu::getStyleCode).collect(Collectors.toList());
        Map<String,List<String>> styleCodeToProductPictureMap = new HashMap<String,List<String>>();
        Map<String,SpotSpu> styleCodeToSpotSpuMap = new HashMap<String,SpotSpu>();
        list.forEach(spotSpu -> {
            //try on状态=待分配、待创建、未通过；去掉供给方式的校验；v1.0.5
            SdpDesignException.isTrue(Objects.equals(spotSpu.getTryOnStatus(), SpotTryOnStateEnum.WAIT_ALLOCATE.getCode())
                            || Objects.equals(spotSpu.getTryOnStatus(), SpotTryOnStateEnum.WAIT_CREATE.getCode())
                            || Objects.equals(spotSpu.getTryOnStatus(), SpotTryOnStateEnum.NO_PASS.getCode()),
                    "待分配、待创建、未通过的tryOn状态才支持发起tryOn");
            /*
            //供给方式字典supply_mode
            Assert.isTrue(!spotSpu.getSupplyModeCode().equals(SupplyModeEnum.MANUFACTURER.getCode()),"请勿勾选ODM的SPU");
            Assert.isTrue(spotSpu.getTryOnStatus()==null || SpotTryOnStateEnum.PASS.getCode().intValue()!=spotSpu.getTryOnStatus(),
                    "存在已完成try on的SPU，请勿重复勾选");

             */
            SpotSpuDetail detail = spotSpuIdToDetailMap.get(spotSpu.getSpotSpuId());
            Assert.isTrue(detail!=null && !CollectionUtils.isEmpty(detail.getProductPictureList()),"存在商品图未齐全的SPU，不支持try on，请先维护商品图");

            styleCodeToProductPictureMap.put(spotSpu.getStyleCode(),detail.getProductPictureList());
            styleCodeToSpotSpuMap.put(spotSpu.getStyleCode(),spotSpu);
        });

        List<SpotSpuTryOnConfig> configs = spotSpuTryOnConfigRepository.listByStyleCodes(styleCodes);
        Map<String, SpotSpuTryOnConfig> configMap = StreamUtil.list2Map(configs, SpotSpuTryOnConfig::getStyleCode);
        List<CreateTryOnParamVo> result = new ArrayList<CreateTryOnParamVo>();
        for(Map.Entry<String,List<String>> entry : styleCodeToProductPictureMap.entrySet()){
            CreateTryOnParamVo vo = new CreateTryOnParamVo();
            SpotSpu spotSpu = styleCodeToSpotSpuMap.get(entry.getKey());
            CreateTryOnParamVo.SpotSpuInfo spotSpuInfo = new CreateTryOnParamVo.SpotSpuInfo();
            BeanUtils.copyProperties(spotSpu,spotSpuInfo);
            vo.setSpotSpuInfo(spotSpuInfo);
            vo.setProductPictureList(entry.getValue());
            SpotSpuTryOnConfig spotSpuTryOnConfig = configMap.get(entry.getKey());
            // Assert.isTrue(spotSpuTryOnConfig!=null,spotSpuInfo.getStyleCode()+"未进行tryOn任务分配");
            if (Objects.nonNull(spotSpuTryOnConfig)) {
                vo.setBackgroundPic(spotSpuTryOnConfig.getBackgroundPic());
                vo.setModelPic(spotSpuTryOnConfig.getModelPic());
                vo.setModelFacialPic(spotSpuTryOnConfig.getModelFacialPic());
            }
            result.add(vo);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncSpuTryOnTaskState(SyncSpuTryOnTaskStateReq req){
        log.info("同步tryOn任务状态和结果：{}", JSONObject.toJSONString(req));
        SpotSpu spotSpu = spotSpuRepository.getByStyleCode(req.getStyleCode());
        Assert.isTrue(spotSpu!=null,"款式不存在styleCode:"+req.getStyleCode());
        SpotTryOnStateEnum spotTryOnState = req.getSpotTryOnState();
        if(spotSpu.getTryOnStatus()!=null
                && SpotTryOnStateEnum.PASS.getCode().intValue()==spotSpu.getTryOnStatus()
        ){
            log.info("现货styleCode:"+req.getStyleCode()+"tryOn状态已通过，不再同步tryOn任务状态和结果");
            return;
        }
        if(spotSpu.getTryOnStatus()==null
            || spotTryOnState.getCode().intValue()!=spotSpu.getTryOnStatus()){
            spotSpu.setTryOnStatus(spotTryOnState.getCode());
            if(SpotTryOnStateEnum.PASS.equals(spotTryOnState)){
                spotSpu.setTryOnAuditTime(LocalDateTime.now());
            }
            spotSpuRepository.updateById(spotSpu);
            //审核通过，记录结果图片
            if(SpotTryOnStateEnum.PASS.equals(spotTryOnState)){
                //记录日志
                addLog(spotSpu.getStyleCode(),spotSpu.getSpotSpuId(),DesignLogContentEnum.SPOT_TRY_ON_PASS.getDesc());
                SpotSpuDetail detail = spotSpuDetailRepository.getBySpotSpuId(spotSpu.getSpotSpuId());
                if(detail!=null){
                    detail.setTryOnPictureList(req.getPassPictures());
                    spotSpuDetailRepository.updateById(detail);
                }
                //tryOn状态为通过，需要同步SKC到商品平台
                try {
                    spotSkcService.pushPopProductBySpotSpu(spotSpu);
                }catch (Exception e){
                    log.error("同步TryOn任务状态后推送商品信息异常",e);
                }
            }else if(SpotTryOnStateEnum.CREATED.equals(spotTryOnState)){
                //跳过分配直接发起try on的，记录try on人为发起人；
                if (Objects.isNull(spotSpu.getTryOnBizId())) {
                    spotSpuService.updateTryOnUser(spotSpu.getStyleCode());
                }
                //记录日志
                addLog(spotSpu.getStyleCode(),spotSpu.getSpotSpuId(),DesignLogContentEnum.SPOT_START_TRY_ON.getDesc());
            }else if(SpotTryOnStateEnum.NO_PASS.equals(spotTryOnState)){
                //记录日志
                addLog(spotSpu.getStyleCode(),spotSpu.getSpotSpuId(), DesignLogContentEnum.SPOT_TRY_ON_NO_PASS.getDesc());
            }
        }else{
            log.info("同步tryOn任务状态：当前tryOn任务状态为{}不需要更新",SpotTryOnStateEnum.getDescByCode(spotSpu.getTryOnStatus()));
        }
    }


    private void addLog(String styleCode, Long bizId, String logContent) {
        DesignLogSdpSaveReq logSdpSaveReq = DesignLogSdpSaveReq.builder()
                .bizId(bizId)
                .bizType(DesignLogBizTypeEnum.SPOT)
                .styleCode(styleCode)
                .content(logContent)
                .build();
        designLogService.sdpSave(logSdpSaveReq);
    }
}
