package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import org.springframework.web.multipart.MultipartFile;
import tech.tiangong.sdp.design.entity.DesignDemand;
import tech.tiangong.sdp.design.entity.DesignStyle;
import tech.tiangong.sdp.design.vo.dto.product.PopProductImageChangeStateDto;
import tech.tiangong.sdp.design.vo.query.style.DesignStyleQuery;
import tech.tiangong.sdp.design.vo.req.prototype.inner.PushSpuSkc2ZjReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleCreateReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleImportReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleUpdateReq;
import tech.tiangong.sdp.design.vo.req.style.SpuProductImgReq;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleCreateResp;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleImportResp;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleVo;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleWebDetailVo;
import tech.tiangong.sdp.design.vo.resp.zj.design.DesignStyleCreateOpenV2Resp;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * SPU表服务接口
 *
 * <AUTHOR>
 */
public interface DesignStyleService {

    // ======================== WEB =======================

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<DesignStyleVo> page(DesignStyleQuery query);

    /**
     * 编辑页详情查询
     *
     * @param styleCode spu编码
     * @return 响应结果
     */
    DesignStyleWebDetailVo getWebDetail(String styleCode);

    /**
     * 提交-创建SPU与SKC
     * @param req 入参
     * @return DesignStyleCreateResp
     */
    DesignStyleCreateResp createSpuSkc(DesignStyleCreateReq req);

    /**
     * 导入SPU
     * @param req 入参
     * @return 响应结果
     */
    DesignStyleImportResp importSpu(DesignStyleImportReq req);

    /**
     * excel导入
     * @param inputStream excel文件
     * @return 响应结果
     */
    List<DesignStyleImportResp> excelImport(InputStream inputStream);

    /**
     * 编辑SPU
     *
     * @param req 入参
     */
    void updateSpu(DesignStyleUpdateReq req);





    // ======================== INNER =======================


    /**
     * 灵感任务需求-创建SPU与SKC
     * @param designDemand 设计需求
     * @param platformName 平台名称
     * @return DesignStyleCreateResp
     */
    DesignStyleCreateResp createFromDemand(DesignDemand designDemand, String platformName);

    /**
     * 根据SPU编号查询SPU最新版本信息
     *
     * @param styleCode spu编码
     * @return spu对象
     */
    DesignStyleVo getLatestVersionByStyleCode(String styleCode);

    /**
     * 以map结构获取designStyle
     */
    Map<String, DesignStyle> mapDesignStyleByStyleCode(Set<String> styleCodes);

    /**
     * 推送SPU与SKC信息到致景
     *
     * @param req 入参
     */
    DesignStyleCreateOpenV2Resp pushSpuSkc2Zj(PushSpuSkc2ZjReq req);

    /**
     * 推送SPU与SKC信息到致景(推送已提交的,推送过的也会推送)
     * @param req 入参
     * @return 致景response
     */
    String forcePushSpuSkc2Zj(PushSpuSkc2ZjReq req);

    /**
     * 从POP查询spu上架图信息
     * @param req 入参
     * @return PopProductImageChangeStateDto
     */
    PopProductImageChangeStateDto listPopProductPicture(SpuProductImgReq req);

    void initHisData(MultipartFile file);

    void initHisDataV2(MultipartFile file);


    void pushAttribute2Pop(Set<String> styleCodeSet);
}
