package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.Assert;
import tech.tiangong.sdp.design.entity.SpotSpuImportTask;
import tech.tiangong.sdp.design.entity.SpotSpuImportTaskDetail;
import tech.tiangong.sdp.design.enums.SpotSpuImportTaskStatusEnum;
import tech.tiangong.sdp.design.enums.SpotSpuImportTaskUpOssStatusEnum;
import tech.tiangong.sdp.design.helper.UploaderOssHelper;
import tech.tiangong.sdp.design.repository.SpotSpuImportTaskDetailRepository;
import tech.tiangong.sdp.design.repository.SpotSpuImportTaskRepository;
import tech.tiangong.sdp.design.service.SpotSpuImportTaskService;
import tech.tiangong.sdp.design.service.SpotSpuService;
import tech.tiangong.sdp.design.vo.dto.SpotSpuUploadFileDto;
import tech.tiangong.sdp.design.vo.dto.SpotSpuUploadPictureDto;
import tech.tiangong.sdp.design.vo.query.spot.SpotSpuImportTaskQuery;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuImportTaskVo;
import tech.tiangong.sdp.utils.TransactionUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: caicijie
 * @description:
 * @Date: 2025/6/26 18:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpotSpuImportTaskServiceImpl implements SpotSpuImportTaskService {
    private final SpotSpuImportTaskRepository spotSpuImportTaskRepository;
    private final SpotSpuImportTaskDetailRepository spotSpuImportTaskDetailRepository;
    private final UploaderOssHelper uploaderOssHelper;
    private final SpotSpuService spotSpuService;
    private final PlatformTransactionManager transactionManager;
    private static String SPOT_SPU_IMPORT_TASK_ = "SPOT:SPU:IMPORT:TASK:";
    /**
     * 导入图片包列表
     * @param query
     * @return
     */
    @Override
    public PageRespVo<SpotSpuImportTaskVo> page(SpotSpuImportTaskQuery query) {
        //spu列表查询
        IPage<SpotSpuImportTaskVo> page = spotSpuImportTaskRepository.findPage(query);
        if (Objects.isNull(page) || CollectionUtil.isEmpty(page.getRecords())) {
            return PageRespVoHelper.empty();
        }
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    /**
     * 取消
     * @param taskId
     */
    @Override
    public void cancel(Long taskId){
        SpotSpuImportTask spuImportTask = spotSpuImportTaskRepository.getById(taskId);
        Assert.isTrue(spuImportTask!=null,"任务不存在");
        Assert.isTrue(SpotSpuImportTaskStatusEnum.WAITING.getCode().equals(spuImportTask.getTaskStatus()), "任务不是等待状态，不能取消");
        spuImportTask.setTaskStatus(SpotSpuImportTaskStatusEnum.CANCEL.getCode());
        spotSpuImportTaskRepository.updateById(spuImportTask);
    }

    /**
     * 图包执行定时任务
     */
    @Override
    public void importRun(){
        // TODO 考虑加Limit
        List<SpotSpuImportTask> spuImportTaskList = spotSpuImportTaskRepository.findByStatus(
                Arrays.asList(SpotSpuImportTaskStatusEnum.WAITING.getCode(),SpotSpuImportTaskStatusEnum.PROCESSING.getCode()));
        if(CollectionUtil.isEmpty(spuImportTaskList)){
            return;
        }
        for(SpotSpuImportTask spuImportTask : spuImportTaskList){
            RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
            RLock lock = redissonClient.getLock(SPOT_SPU_IMPORT_TASK_ + spuImportTask.getId());
            boolean isLock = false ;
            try {
                isLock = lock.tryLock(10, TimeUnit.SECONDS);
                if (!isLock) {
                    log.info("spotSpuImport任务进行中{}", spuImportTask.getId());
                    continue;
                }

                // 再查一次是否完成，不是等待直接跳过
                SpotSpuImportTask task = spotSpuImportTaskRepository.getById(spuImportTask.getId());
                if(!List.of(SpotSpuImportTaskStatusEnum.WAITING.getCode(),SpotSpuImportTaskStatusEnum.PROCESSING.getCode()).contains(task.getTaskStatus())){
                    continue;
                }
                task.setTaskStatus(SpotSpuImportTaskStatusEnum.PROCESSING.getCode());
                spotSpuImportTaskRepository.updateById(task);
                List<SpotSpuImportTaskDetail> spuImportTaskDetailList = spotSpuImportTaskDetailRepository.findByTaskId(spuImportTask.getId());
                List<String> picUrlMd5 = spuImportTaskDetailList.stream().map(SpotSpuImportTaskDetail::getPicMd5).toList();
                Map<String, String> existPicUrl = new HashMap<>();
                if (CollectionUtil.isNotEmpty(picUrlMd5)) {
                    List<SpotSpuImportTaskDetail> existPicUrlList = spotSpuImportTaskDetailRepository.findByPicMd5(picUrlMd5);
                    if(CollectionUtil.isNotEmpty(existPicUrlList)){
                        existPicUrl = existPicUrlList.stream().collect(Collectors.toMap(SpotSpuImportTaskDetail::getPicMd5, SpotSpuImportTaskDetail::getOssPicUrl, (a, b) -> a));
                    }
                }
                List<String> errorMsgList = new ArrayList<>();
                for (SpotSpuImportTaskDetail spuImportTaskDetail : spuImportTaskDetailList) {
                    if (SpotSpuImportTaskUpOssStatusEnum.FAIL.getCode().equals(spuImportTaskDetail.getUpOssStatus())) {
                        errorMsgList.add(String.format("第%d行图片下载失败：%s", spuImportTaskDetail.getExcelRow(), spuImportTaskDetail.getPicUrl()));
                        continue;
                    }
                    // 先查是否上传过
                    String existOss = existPicUrl.get(spuImportTaskDetail.getPicMd5());
                    if (StrUtil.isNotBlank(existOss)) {
                        spuImportTaskDetail.setOssPicUrl(existOss);
                        spuImportTaskDetail.setUpOssStatus(SpotSpuImportTaskUpOssStatusEnum.COMPLETED.getCode());
                    } else {
                        try {
                            String ossUrl = uploaderOssHelper.transferFrom(spuImportTaskDetail.getPicUrl());
                            spuImportTaskDetail.setOssPicUrl(ossUrl);
                            spuImportTaskDetail.setUpOssStatus(SpotSpuImportTaskUpOssStatusEnum.COMPLETED.getCode());
                        } catch (Exception e) {
                            log.warn(String.format("uploaderOssHelper.transferFrom error 第%d行图片上传失败", spuImportTaskDetail.getExcelRow()), e);
                            errorMsgList.add(String.format("第%d行图片下载失败：%s", spuImportTaskDetail.getExcelRow(), spuImportTaskDetail.getPicUrl()));
                            spuImportTaskDetail.setUpOssStatus(SpotSpuImportTaskUpOssStatusEnum.FAIL.getCode());
                        }
                    }
                }
                // 更新任务
                spotSpuImportTaskDetailRepository.updateBatchById(spuImportTaskDetailList);
                if (CollectionUtil.isNotEmpty(errorMsgList)) {
                    spuImportTask.setTaskStatus(SpotSpuImportTaskStatusEnum.FAIL.getCode());
                    spuImportTask.setErrMsg(String.join("；", errorMsgList));
                    spotSpuImportTaskRepository.updateById(spuImportTask);
                    continue;
                }
                // 导入现货
                updateSpotSpu(spuImportTask, errorMsgList, spuImportTaskDetailList);
            } catch (Exception e) {
                log.error("spotSpuImportTaskDetailRepository.updateBatchById error", e);
                spuImportTask.setTaskStatus(SpotSpuImportTaskStatusEnum.FAIL.getCode());
                spuImportTask.setErrMsg(String.format("导入图片失败: %s", e.getMessage()));
                spuImportTask.setFinishTime(LocalDateTime.now());
                spotSpuImportTaskRepository.updateById(spuImportTask);
            }finally {
                if (isLock && lock.isHeldByCurrentThread() ) {
                    lock.unlock();
                }
            }
        }
    }

    private void updateSpotSpu(SpotSpuImportTask spuImportTask, List<String> errorMsgList, List<SpotSpuImportTaskDetail> spuImportTaskDetailList) {
        // 手动开启事务
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            // 设置创建人
            UserContentHolder.set(new UserContent()
                    .setCurrentUserId(spuImportTask.getCreatorId())
                    .setCurrentUserName(spuImportTask.getCreatorName())
                    .setTenantId(spuImportTask.getTenantId())
                    .setSystemCode("SDP"));
            // 更新主图
            List<String> spuCode = spuImportTaskDetailList.stream().map(SpotSpuImportTaskDetail::getSpuCode).distinct().toList();
            spotSpuService.updateSpuStateByUploadPicture(spuImportTask.getPictureType(), spuCode);
            // 组装明细数据
            buildPicDetail(spuImportTask, spuImportTaskDetailList);
            spotSpuService.updateSpotSpuDetailPicture(spuImportTask.getPictureType(), spuCode, buildPicDetail(spuImportTask, spuImportTaskDetailList));
            spuImportTask.setTaskStatus(SpotSpuImportTaskStatusEnum.COMPLETED.getCode());
            spuImportTask.setFinishTime(LocalDateTime.now());
            spotSpuImportTaskRepository.updateById(spuImportTask);
            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("spotSpuService.updateSpuStateByUploadPicture error", e);
            // 回滚事务
            transactionManager.rollback(status);
            log.error("spotSpuService.updateSpuStateByUploadPicture error", e);
            spuImportTask.setTaskStatus(SpotSpuImportTaskStatusEnum.FAIL.getCode());
            spuImportTask.setFinishTime(LocalDateTime.now());
            spuImportTask.setErrMsg(String.format("导入图片失败: %s", e.getMessage()));
            spotSpuImportTaskRepository.updateById(spuImportTask);
        }
    }

    private  Map<String, SpotSpuUploadPictureDto> buildPicDetail(SpotSpuImportTask spuImportTask, List<SpotSpuImportTaskDetail> spuImportTaskDetailList) {
        Map<String, SpotSpuUploadPictureDto> fileNameMap = new HashMap<>();
        Map<String,List<SpotSpuImportTaskDetail>> styleCodeMap = spuImportTaskDetailList.stream().collect(Collectors.groupingBy(SpotSpuImportTaskDetail::getSpuCode));
        for (Map.Entry<String, List<SpotSpuImportTaskDetail>> entry : styleCodeMap.entrySet()) {
            List<SpotSpuImportTaskDetail> detailList = entry.getValue();
            SpotSpuUploadPictureDto uploadPictureDto = new SpotSpuUploadPictureDto();
            uploadPictureDto.setFileName(spuImportTask.getFileName());
            uploadPictureDto.setImageUrls(detailList.stream().map(detail -> {
                SpotSpuUploadFileDto uploadFileDto = new SpotSpuUploadFileDto();
                uploadFileDto.setOssImageUrl(detail.getOssPicUrl());
                return uploadFileDto;
            }).toList());
            fileNameMap.put(entry.getKey(), uploadPictureDto);
        }
        return fileNameMap;
    }

}
