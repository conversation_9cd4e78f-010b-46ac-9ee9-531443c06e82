package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.converter.spot.SpotConverter;
import tech.tiangong.sdp.design.enums.ProductCommunicationEnum;
import tech.tiangong.sdp.design.service.SpotSpuCommunicationService;
import tech.tiangong.sdp.design.service.SpotSpuMurmurationService;
import tech.tiangong.sdp.design.service.SpotSpuService;
import tech.tiangong.sdp.design.vo.req.spot.PickStyleAddReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotPickStyleAddVo;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuCommunicationBatchPushResult;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuInfoVo;

import java.util.List;
import java.util.Objects;

/**
 * 现货管理SPU-inner
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/spot-spu")
public class SpotSpuInnerController extends BaseController {

    private final SpotSpuService spotSpuService;
    private final SpotSpuMurmurationService spotSpuMurmurationService;
    private final SpotSpuCommunicationService spotSpuCommunicationService;

    /**
     * 根据spu查询现货信息
     * @param styleCode
     * @return
     */
    @GetMapping("/get-info-by-style-code")
    DataResponse<SpotSpuInfoVo> getInfoByStyleCode(@RequestParam("styleCode") String styleCode) {
        return DataResponse.ok(spotSpuService.getInfoByStyleCode(styleCode));
    }

    /**
     * 选款创建 - 标准流程
     *
     * @param req 入参
     * @return response
     */
    @NoRepeatSubmitLock
    @PostMapping("/pick-style/add")
    DataResponse<List<SpotPickStyleAddVo>> pickStyleAdd(@RequestBody @Validated PickStyleAddReq req) {
        log.info("===选款创建 req: {} ===", JSON.toJSONString(req));
        
        UserContent userContent = buildUserContent(req);
        UserContentHolder.set(userContent);

        log.info("pickStyleAdd userContent: {}", JSON.toJSONString(userContent));
        return DataResponse.ok(SpotConverter.buildSpotPickStyleAddVo(spotSpuService.pickStyleAdd(req)));
    }

    /**
     * 选款创建 - 1688货通商品（放宽校验）
     * 用于货通商品先建款，后续由AI补充属性
     *
     * @param req 入参
     * @return response
     */
    @NoRepeatSubmitLock
    @PostMapping("/pick-style/add-communication")
    DataResponse<List<SpotPickStyleAddVo>> pickStyleAddForCommunication(@RequestBody PickStyleAddReq req) {
        log.info("===1688货通商品选款创建 req: {} ===", JSON.toJSONString(req));
        
        // 标记为货通商品
        if (req.getSpuInfoList() != null) {
            req.getSpuInfoList().forEach(spu -> {
                if (spu.getCommunication() == null) {
                    spu.setCommunication(ProductCommunicationEnum.YES.getCode());
                }
            });
        }
        
        UserContent userContent = buildUserContent(req);
        UserContentHolder.set(userContent);
        
        log.info("pickStyleAddForCommunication userContent: {}", JSON.toJSONString(userContent));
        // 调用相同的service方法，但因为标记了communication，会跳过部分校验
        return DataResponse.ok(SpotConverter.buildSpotPickStyleAddVo(spotSpuService.pickStyleAdd(req)));
    }
    
    /**
     * 补偿提交SPU属性转换任务
     * 
     * @param spuCodes SPU编码列表
     * @return response
     */
    @PostMapping("/murmuration/compensate-submit-spu")
    DataResponse<Void> compensateSubmitSpuAttributeTasks(@RequestBody List<String> spuCodes) {
        log.info("===补偿提交SPU属性转换任务 spuCodes: {} ===", JSON.toJSONString(spuCodes));
        if (CollectionUtils.isEmpty(spuCodes)) {
            return DataResponse.failed("SPU编码不能为空");
        }
        setSystemUser();
        spotSpuMurmurationService.compensateSubmitSpuAttributeTasks(spuCodes);
        return DataResponse.ok();
    }
    
    /**
     * 补偿提交SKC颜色识别任务
     * 
     * @param skcCodes SKC编码列表
     * @return response
     */
    @PostMapping("/murmuration/compensate-submit-skc")
    DataResponse<Void> compensateSubmitSkcColorTasks(@RequestBody List<String> skcCodes) {
        log.info("===补偿提交SKC颜色识别任务 skcCodes: {} ===", JSON.toJSONString(skcCodes));
        if (CollectionUtils.isEmpty(skcCodes)) {
            return DataResponse.failed("SKC编码不能为空");
        }
        setSystemUser();
        spotSpuMurmurationService.compensateSubmitSkcColorTasks(skcCodes);
        return DataResponse.ok();
    }

    /**
     * 基于SPU补偿提交SKC颜色识别任务
     * 
     * @param spuCodes SPU编码列表，将为每个SPU下的所有SKC提交颜色识别任务
     * @return response
     */
    @PostMapping("/murmuration/compensate-submit-skc-by-spu")
    DataResponse<Void> compensateSubmitSkcColorTasksBySpu(@RequestBody List<String> spuCodes) {
        log.info("===基于SPU补偿提交SKC颜色识别任务 spuCodes: {} ===", JSON.toJSONString(spuCodes));
        if (CollectionUtils.isEmpty(spuCodes)) {
            return DataResponse.failed("SPU编码不能为空");
        }
        setSystemUser();
        spotSpuMurmurationService.compensateSubmitSkcColorTasksBySpu(spuCodes);
        return DataResponse.ok();
    }

    /**
     * 补偿提交Alibaba图包拉取任务
     * 
     * @param spuCodes SPU编码列表
     * @return response
     */
    @PostMapping("/murmuration/compensate-submit-alibaba-distribution")
    DataResponse<Void> compensateSubmitAlibabaDistributionTasks(@RequestBody List<String> spuCodes) {
        log.info("===补偿提交Alibaba图包拉取任务 spuCodes: {} ===", JSON.toJSONString(spuCodes));
        if (CollectionUtils.isEmpty(spuCodes)) {
            return DataResponse.failed("SPU编码不能为空");
        }
        setSystemUser();
        spotSpuMurmurationService.compensateSubmitAlibabaDistributionTasks(spuCodes);
        return DataResponse.ok();
    }

    /**
     * 基于SPU批量补偿提交Murmuration任务（属性转换+颜色识别+图包拉取）
     * 
     * @param spuCodes SPU编码列表
     * @return response
     */
    @PostMapping("/murmuration/compensate-submit-all-by-spu")
    DataResponse<Void> compensateSubmitMurmurationTasksBySpu(@RequestBody List<String> spuCodes) {
        log.info("===基于SPU批量补偿提交Murmuration任务 spuCodes: {} ===", JSON.toJSONString(spuCodes));
        if (CollectionUtils.isEmpty(spuCodes)) {
            return DataResponse.failed("SPU编码不能为空");
        }
        setSystemUser();
        spotSpuMurmurationService.compensateSubmitMurmurationTasksBySpu(spuCodes);
        return DataResponse.ok();
    }

    /**
     * 触发推送现货货通SPU到POP
     * 
     * @param styleCodes SPU编码
     * @return 处理结果
     */
    @PostMapping("/communication/push-spu-to-pop")
    public DataResponse<SpotSpuCommunicationBatchPushResult> manualPushSpuToPop(@RequestBody List<String> styleCodes) {
        log.info("触发推送现货货通SPU到POP");
        
        if (CollectionUtils.isEmpty(styleCodes)) {
            return DataResponse.failed("SPU编码不能为空");
        }
        setSystemUser();
        return DataResponse.ok(spotSpuCommunicationService.batchPushSpuToPop(styleCodes));
    }

    /**
     * 触发批量更新货通商品属性到POP
     *
     * @param styleCodes SPU编码
     * @return 处理结果
     */
    @PostMapping("/update-spu-to-pop")
    public DataResponse<Void> batchUpdateProductAttribute2Pop(@RequestBody List<String> styleCodes) {
        log.info("触发批量更新货通商品属性到POP");

        if (CollectionUtils.isEmpty(styleCodes)) {
            return DataResponse.failed("SPU编码不能为空");
        }

        setSystemUser();
        spotSpuService.batchUpdateProductAttribute2Pop(styleCodes);
        return  DataResponse.ok();
    }

    /**
     * 设置系统用户上下文
     */
    private void setSystemUser() {
        UserContentHolder.set(new UserContent()
                .setCurrentUserId(0L)
                .setCurrentUserCode("0")
                .setCurrentUserName("系统")
                .setTenantId(2L)
                .setSystemCode("SDP"));
    }
    
    /**
     * 构建用户上下文（抽取公共方法）
     */
    private UserContent buildUserContent(PickStyleAddReq req) {
        UserContent userContent = new UserContent();
        userContent.setCurrentUserId(Objects.isNull(req.getOperateId()) ? 0L : req.getOperateId());
        userContent.setCurrentUserCode("0");
        userContent.setCurrentUserName(StringUtils.isBlank(req.getOperateName()) ? "系统" : req.getOperateName());
        userContent.setTenantId(2L);
        userContent.setSystemCode("SDP");
        return userContent;
    }
}
