package tech.tiangong.sdp.design.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.tiangong.sdp.design.repository.PrototypeDetailRepository;
import tech.tiangong.sdp.design.service.PrototypeDetailService;

/**
 * 版单详情表服务
 *
 * <AUTHOR>
 * @since 2021-08-09 14:50:25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrototypeDetailServiceImpl implements PrototypeDetailService {
    private final PrototypeDetailRepository prototypeDetailRepository;


}