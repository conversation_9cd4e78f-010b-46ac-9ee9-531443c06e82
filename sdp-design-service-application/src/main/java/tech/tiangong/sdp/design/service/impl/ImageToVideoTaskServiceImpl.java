package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.design.entity.ImageToVideoTask;
import tech.tiangong.sdp.design.entity.VisualImagePackage;
import tech.tiangong.sdp.design.enums.visual.CroppedTaskStatusEnum;
import tech.tiangong.sdp.design.helper.ImageCroppingTaskHelper;
import tech.tiangong.sdp.design.helper.VisualTaskHelper;
import tech.tiangong.sdp.design.mq.rocketmq.entity.MurmurationTaskStatusEnum;
import tech.tiangong.sdp.design.mq.rocketmq.entity.TaskStatusNotification;
import tech.tiangong.sdp.design.repository.ImageToVideoTaskRepository;
import tech.tiangong.sdp.design.repository.VisualImagePackageRepository;
import tech.tiangong.sdp.design.service.ImageToVideoTaskService;
import tech.tiangong.sdp.design.vo.req.visual.BatchImageToVideoTaskReq;
import tech.tiangong.sdp.design.vo.req.visual.ImageToVideoTaskReq;
import tech.tiangong.sdp.design.vo.resp.visual.ImageFileResp;
import tech.tiangong.sdp.utils.Bool;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.extension.toolkit.Db.*;

/**
 * 图生视频任务记录表 Service 实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageToVideoTaskServiceImpl implements ImageToVideoTaskService {

    private final ImageToVideoTaskRepository imageToVideoTaskRepository;
    private final ImageCroppingTaskHelper imageCroppingTaskHelper;
    private final VisualImagePackageRepository visualImagePackageRepository;
    private final VisualTaskHelper visualTaskHelper;

    @Override
    public ImageToVideoTask getByTaskId(String taskId) {
        LambdaQueryWrapper<ImageToVideoTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImageToVideoTask::getTaskId, taskId);
        return getOne(queryWrapper);
    }

    @Override
    public boolean updateTaskStatus(String taskId, Integer status, String videoResults) {
        ImageToVideoTask task = getByTaskId(taskId);
        if (task == null) {
            return false;
        }

        task.setTaskStatus(status);
        if (videoResults != null) {
            task.setVideoResults(videoResults);
        }

        if (status == 2 || status == 3) { // 成功或失败
            task.setCompletedTime(LocalDateTime.now());
        }

        return updateById(task);
    }

    @Override
    public Long createImageToVideoTask(Long packageId, String styleCode, String mainUrl, String promptText) {
        ImageToVideoTask task = new ImageToVideoTask();
        task.setImageToVideoTaskId(IdPool.getId());
        task.setPackageId(packageId);
        task.setStyleCode(styleCode);
        task.setMainUrl(mainUrl);
        task.setPromptText(promptText);
        task.setBizType("IMAGE_TO_VIDEO");
        task.setTaskStatus(0); // 待处理
        task.setRetryCount(0);
        task.setMaxRetries(3);
        task.setNotificationSent(0);

        save(task);

        return task.getTaskId();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchCreateImageToVideoTask(BatchImageToVideoTaskReq request) {
        // 参数校验
        if (request == null || CollectionUtil.isEmpty(request.getTasks())) {
            log.warn("批量创建图生视频任务请求参数为空");
            return false;
        }
        List<ImageToVideoTask> imageToVideoTasks = request.getTasks().stream().map(a -> this.createTaskEntity(a)).toList();
        imageToVideoTaskRepository.saveBatch(imageToVideoTasks);

        // 使用 CompletableFuture 异步处理每个任务
        List<CompletableFuture<Void>> futures = imageToVideoTasks.stream()
                .map(task -> CompletableFuture.runAsync(() -> processSingleTask(task)))
                .collect(Collectors.toList());

        return Boolean.TRUE;
    }




    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleImageToVideoTaskCallback(TaskStatusNotification notification) {
        log.info("处理图生视频任务回调:bizId={}, taskId={}, status={}",
                notification.getBizId(), notification.getTaskId(), notification.getStatus());

        // 根据第三方任务ID查找对应的任务记录
        ImageToVideoTask imageToVideoTask = imageToVideoTaskRepository.getById(notification.getBizId());
        if (Objects.isNull(imageToVideoTask)) {
            log.warn("未找到对应的图生视频任务: taskId={}", notification.getTaskId());
            return;
        }

        // 根据通知状态更新任务状态
        if (notification.getStatus() == MurmurationTaskStatusEnum.COMPLETED) {
            // 任务成功
            handleSuccessCallback(notification, imageToVideoTask);
        } else if (notification.getStatus() == MurmurationTaskStatusEnum.FAILED) {
            // 任务失败
            handleFailedCallback(notification, imageToVideoTask);
        } else {
            // 其他状态（如处理中）
            handleProcessingCallback(notification, imageToVideoTask);
        }
    }

    /**
     * 处理成功回调
     */
    private void handleSuccessCallback(TaskStatusNotification notification, ImageToVideoTask task) {
        try {
            // 将视频结果转换为JSON字符串
            List<ImageFileResp> list =
                    notification.getResults().stream().map(a -> {
                        ImageFileResp imageFileResp = new ImageFileResp();
                        imageFileResp.setOrgImgName(extractFileNameFromUrl(a.getUrl()));
                        imageFileResp.setOssImageUrl(a.getUrl());
                        return imageFileResp;
                    }).collect(Collectors.toList());
            // 更新任务状态和结果
            task.setTaskStatus(CroppedTaskStatusEnum.SUCCESS.getCode()); // 成功
            task.setVideoResults(JSON.toJSONString(list));
            task.setCompletedTime(LocalDateTime.now());
            task.setRevisedTime(LocalDateTime.now());

            imageToVideoTaskRepository.updateById(task);

            log.info("图生视频任务成功完成: taskId={}, 内部任务ID={}",
                    notification.getTaskId(), task.getImageToVideoTaskId());

            VisualImagePackage visualImagePackage = visualImagePackageRepository.getById(task.getPackageId());
            if(Objects.isNull(visualImagePackage)){
                return;
            }
            VisualImagePackage latestByStyleCode = visualImagePackageRepository.getLatestByStyleCode(task.getStyleCode());
            String videoGenerations = latestByStyleCode.getVideoGenerations();
            if(StringUtils.isNotBlank(videoGenerations)){
                List<ImageFileResp> imageFileResps = JSON.parseArray(videoGenerations, ImageFileResp.class);
                list.addAll(imageFileResps);
            }
            //如果发起任务的版本不是最新版本  要把最新版本和发起任务的版本一起追加更新
            if(!latestByStyleCode.getPackageId().equals(visualImagePackage.getPackageId())){
                latestByStyleCode.setVideoGenerations(JSON.toJSONString(list));
                latestByStyleCode.setVideoGenerationStatus(CroppedTaskStatusEnum.SUCCESS.getCode());
                visualImagePackageRepository.updateById(latestByStyleCode);
            }


            visualImagePackage.setVideoGenerations(JSON.toJSONString(list));
            visualImagePackage.setVideoGenerationStatus(CroppedTaskStatusEnum.SUCCESS.getCode());
            visualImagePackageRepository.updateById(visualImagePackage);
            //通知POP图片变更
            visualTaskHelper.noticePopUpdateVideo(visualImagePackage.getStyleCode(),list);

        } catch (Exception e) {
            log.error("处理图生视频成功回调异常: taskId={}", notification.getTaskId(), e);
            // 即使处理异常，也不应该影响其他任务
        }
    }

    /**
     * 处理失败回调
     */
    private void handleFailedCallback(TaskStatusNotification notification, ImageToVideoTask task) {
        try {
            // 更新任务状态为失败
            task.setTaskStatus(CroppedTaskStatusEnum.FAILED.getCode()); // 失败
            task.setErrorMessage("图生视频任务失败: " + notification.getStatus());
            task.setCompletedTime(LocalDateTime.now());
            task.setRevisedTime(LocalDateTime.now());

            imageToVideoTaskRepository.updateById(task);


            visualImagePackageRepository.updateVideoGenerationStatus(task.getPackageId(), CroppedTaskStatusEnum.FAILED.getCode());
            log.warn("图生视频任务失败: taskId={}, 内部任务ID={}",
                    notification.getTaskId(), task.getImageToVideoTaskId());

            // 可以在这里添加失败处理逻辑，如重试或发送失败通知等

        } catch (Exception e) {
            log.error("处理图生视频失败回调异常: taskId={}", notification.getTaskId(), e);
        }
    }

    /**
     * 处理处理中回调
     */
    private void handleProcessingCallback(TaskStatusNotification notification, ImageToVideoTask task) {
        try {
            // 更新任务状态为处理中
            task.setTaskStatus(1); // 处理中
            task.setRevisedTime(LocalDateTime.now());

            imageToVideoTaskRepository.updateById(task);

            log.info("图生视频任务处理中: taskId={}, 内部任务ID={}",
                    notification.getTaskId(), task.getImageToVideoTaskId());

        } catch (Exception e) {
            log.error("处理图生视频处理中回调异常: taskId={}", notification.getTaskId(), e);
        }
    }




    /**
     * 创建任务实体对象
     */
    private ImageToVideoTask createTaskEntity(ImageToVideoTaskReq taskItem) {
        ImageToVideoTask task = new ImageToVideoTask();
        task.setImageToVideoTaskId(IdPool.getId());
        task.setPackageId(taskItem.getPackageId());
        task.setStyleCode(taskItem.getStyleCode());
        task.setMainUrl(taskItem.getMainUrl());
        task.setPromptText(taskItem.getPromptText());
        task.setBizType("IMAGE_TO_VIDEO");
        task.setTaskStatus(0); // 待处理
        task.setRetryCount(0);
        task.setMaxRetries(3);
        task.setNotificationSent(0);

        return task;
    }


    /**
     * 处理单个图生视频任务
     */
    private void processSingleTask(ImageToVideoTask task) {
        try {
            log.info("开始处理图生视频任务，任务ID: {}", task.getImageToVideoTaskId());

            // 提交图生视频任务
            Long taskId = imageCroppingTaskHelper.submitImageToVideoTask(task);

            // 更新任务状态和任务ID
            task.setTaskId(taskId);
            task.setTaskStatus(CroppedTaskStatusEnum.SUBMITTED.getCode()); // 设置为处理中状态

            // 更新数据库
            imageToVideoTaskRepository.updateById(task);
            log.info("成功提交图生视频任务，内部任务ID: {}, 第三方任务ID: {}",
                    task.getImageToVideoTaskId(), taskId);

            visualImagePackageRepository.updateVideoGenerationStatus(task.getPackageId(), CroppedTaskStatusEnum.SUBMITTED.getCode());

        } catch (Exception e) {
            log.error("处理图生视频任务失败，内部任务ID: {}", task.getImageToVideoTaskId(), e);

            // 更新任务状态为失败
            task.setTaskStatus(CroppedTaskStatusEnum.FAILED.getCode()); // 设置为失败状态
            task.setErrorMessage("发起图生视频处理任务失败: " + e.getMessage());
            visualImagePackageRepository.updateVideoGenerationStatus(task.getPackageId(), CroppedTaskStatusEnum.FAILED.getCode());

            try {
                imageToVideoTaskRepository.updateById(task);
            } catch (Exception ex) {
                log.error("更新失败任务状态异常，任务ID: {}", task.getImageToVideoTaskId(), ex);
            }
        }
    }



    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }

        try {
            // 去除URL参数部分（问号后的内容）
            String urlWithoutParams = url.split("\\?")[0];

            // 获取最后一个斜杠后的部分
            String[] pathParts = urlWithoutParams.split("/");
            String fileNameWithEncoding = pathParts[pathParts.length - 1];

            // URL解码（处理百分号编码）
            String fileName = URLDecoder.decode(fileNameWithEncoding, StandardCharsets.UTF_8.toString());

            // 确保文件名有正确的扩展名
            if (!fileName.toLowerCase().endsWith(".mp4")) {
                fileName += ".mp4";
            }

            return fileName;
        } catch (Exception e) {
            log.warn("从URL提取文件名失败: {}", url, e);
            return null;
        }
    }
}
