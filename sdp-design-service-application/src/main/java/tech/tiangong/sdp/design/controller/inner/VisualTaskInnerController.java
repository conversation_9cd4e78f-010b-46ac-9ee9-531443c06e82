package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tech.tiangong.sdp.design.entity.VisualDemand;
import tech.tiangong.sdp.design.service.VisualDemandService;
import tech.tiangong.sdp.design.service.VisualTaskService;
import tech.tiangong.sdp.design.vo.req.visual.VisualTaskInnerVo;

import java.util.List;


/**
 * 视觉任务管理-inner
 *
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/visual-task")
public class VisualTaskInnerController extends BaseController {
    private final VisualDemandService visualDemandService;
    private final VisualTaskService visualTaskService;

    /**
     * 大货资料-创建视觉需求
     *
     * @deprecated  不再通过sdk调用, 已改为MQ对接: tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum#CREATE_VISUAL_DEMAND_TASK
     * @param styleCode spu
     * @return 需求id
     */
    @Deprecated
    @NoRepeatSubmitLock
    @PostMapping("/create-visual-demand/prod-order/{styleCode}")
    public DataResponse<Long> createVisualDemand4ProdOrder(@PathVariable(value = "styleCode") String styleCode) {
        VisualDemand visualDemand = visualDemandService.createVisualDemand4ProdOrder(styleCode);
        return DataResponse.ok(visualDemand.getDemandId());
    }


    /**
     * spu最新视觉任务查询
     *
     * @param spuCodeList spu编码集合, 最大1000
     * @return 视觉任务信息
     */
    @PostMapping("/latest-by-spu")
    public DataResponse<List<VisualTaskInnerVo>> listLatestBySpu(@RequestBody List<String> spuCodeList) {
        return DataResponse.ok(visualTaskService.listLatestBySpu(spuCodeList));
    }

}
