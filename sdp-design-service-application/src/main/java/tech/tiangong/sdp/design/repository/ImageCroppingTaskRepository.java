package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.ImageCroppingTask;
import tech.tiangong.sdp.design.entity.MaterialSnapshot;
import tech.tiangong.sdp.design.mapper.ImageCroppingTaskMapper;
import tech.tiangong.sdp.utils.Bool;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 图片裁剪任务记录表
 */
@Repository
public class ImageCroppingTaskRepository extends BaseRepository<ImageCroppingTaskMapper, ImageCroppingTask> {


    public List<ImageCroppingTask> listByPackageIdStatus(Long packageId,Integer taskStatus) {
        if (Objects.isNull(packageId)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(ImageCroppingTask::getPackageId, packageId)
                .eq(Objects.nonNull(taskStatus), ImageCroppingTask::getTaskStatus,taskStatus)
                .eq(ImageCroppingTask::getIsDeleted, Bool.NO.getCode())
                .list();
    }
}
