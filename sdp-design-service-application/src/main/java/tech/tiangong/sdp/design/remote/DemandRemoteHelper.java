package tech.tiangong.sdp.design.remote;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import com.alibaba.fastjson.JSON;
import com.yibuyun.scm.common.dto.houliu.request.IdsReq;
import com.yibuyun.scm.common.dto.houliu.response.DemandDetailResp;
import com.yibuyun.scm.open.client.houliu.SCMHouliuDemandClient;
import com.zjkj.booster.common.protocol.DataResponse;
import com.zjkj.scf.bundle.common.dto.demand.dto.request.ApplyCloseCraftDemandReqDto;
import com.zjkj.scf.bundle.common.dto.demand.dto.request.CraftDemandAddReqWrapper;
import com.zjkj.scf.bundle.common.dto.demand.dto.request.MaterialDetailBatchReq;
import com.zjkj.scf.bundle.common.dto.demand.dto.request.MaterialDetailReq;
import com.zjkj.scf.bundle.common.dto.demand.dto.response.*;
import com.zjkj.scf.bundle.common.dto.demand.enums.DemandCloseEnum;
import com.zjkj.scf.bundle.sdk.client.demand.feign.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import tech.tiangong.sdp.clothes.client.SecondCraftDemandClient;
import tech.tiangong.sdp.clothes.vo.req.IdListReq;
import tech.tiangong.sdp.clothes.vo.resp.SecondCraftDemandDetailVo;
import tech.tiangong.sdp.core.channel.ChannelHeaderFactory;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.remote.zj.demand.ZjPlmDemandRemoteHelper;
import tech.tiangong.sdp.design.vo.req.zj.demand.AccessoryDemandCreateOpenV2Req;
import tech.tiangong.sdp.design.vo.resp.zj.demand.AccessoryDemandCreateOpenV2Resp;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2021/8/19
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DemandRemoteHelper {
    public static final String MATERIAL_DEMAND_ID_NAME = "MATERIAL_DEMAND_ID";
    private final CraftDemandClient craftDemandClient;
    private final SecondCraftDemandClient secondCraftDemandClient;
    private final DemandMatchClient demandMatchClient;
    private final CraftMatchClient craftMatchClient;
    private final MaterialDetailClient materialDetailClient;

    private final SCMHouliuDemandClient scmHouliuDemandClient;
    private final SampleCraftTaskClient sampleCraftTaskClient;

    private final ZjPlmDemandRemoteHelper plmDemandRemoteHelper;


    /**
     * 获取工艺需求处理登记信息
     *
     * @param supplyChainCraftDemandIds 履约工艺需求ID列表
     */
    public List<CraftDemandRegistrationInfoDto> getRegistrationInfo(List<Long> supplyChainCraftDemandIds) {
        if (CollectionUtil.isEmpty(supplyChainCraftDemandIds)) {
            return Collections.emptyList();
        }
        com.zjkj.scf.bundle.common.dto.common.req.IdsReq req = new com.zjkj.scf.bundle.common.dto.common.req.IdsReq();
        req.setIds(supplyChainCraftDemandIds);
        log.info("【获取工艺需求处理登记信息】调用履约需求服务 请求参数:{}", JSON.toJSONString(req));

        try {
            DataResponse<List<CraftDemandRegistrationInfoDto>> response = craftDemandClient.getRegistrationInfo(req);
            log.info("【获取工艺需求处理登记信息】调用履约需求服务 响应:{}", JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                throw new SdpDesignException("【获取工艺需求处理登记信息】调用履约需求服务 失败");
            }
            return response.getData();
        } catch (Exception e) {
            log.error("【获取工艺需求处理登记信息】调用履约需求服务 异常", e);
            throw new SdpDesignException("【获取工艺需求处理登记信息】调用履约需求服务 异常");
        }

    }


    /**
     * 获取工艺匹配信息
     *
     * @param matchIdList 履约需求匹配ID
     */
    public List<DemandMatchListRespDto.DemandMatchInfo> getDemandMatchInfo(List<Long> matchIdList) {
        if (CollectionUtil.isEmpty(matchIdList)) {
            return Collections.emptyList();
        }

        com.zjkj.scf.bundle.common.dto.common.req.IdsReq req = new com.zjkj.scf.bundle.common.dto.common.req.IdsReq();
        req.setIds(matchIdList);
        log.info("【获取工艺需求匹配信息】调用履约需求服务 请求参数:{}", JSON.toJSONString(req));

        try {
            DataResponse<DemandMatchListRespDto> response = demandMatchClient.getMatchList(req);
            log.info("【获取工艺需求匹配信息】调用履约需求服务 响应:{}", JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("【获取工艺需求匹配信息】失败");
                throw new SdpDesignException("【获取工艺需求匹配信息】调用履约需求服务失败");
            }
            return response.getData().getList();
        } catch (Exception e) {
            log.error("【获取工艺需求匹配信息】调用履约需求服务异常", e);
            throw new SdpDesignException("【获取工艺需求匹配信息】调用履约需求服务异常", e);
        }
    }

    /**
     * 获取工艺匹配详情信息
     *
     * @param supplyChainCraftDemandId 履约需求ID
     */
    public List<CraftDemandMatchDetailDto.CraftDemandMatchDetail> getCraftDemandMatchDetail(Long supplyChainCraftDemandId) {
        if (Objects.isNull(supplyChainCraftDemandId)) {
            return null;
        }

        log.info("【获取工艺需求匹配详细信息】调用履约需求服务 请求参数:{}", supplyChainCraftDemandId);

        try {
            DataResponse<CraftDemandMatchDetailDto> response = craftMatchClient.craftMatchDetail(supplyChainCraftDemandId);
            log.info("【获取工艺需求匹配详细信息】调用履约需求服务 响应:{}", JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("【获取工艺需求匹配详细信息】失败");
                throw new SdpDesignException("【获取工艺需求匹配详细信息】调用履约需求服务失败");
            }
            return Objects.isNull(response.getData()) ? Collections.emptyList() : response.getData().getCraftDemandMatchDetailList();
        } catch (Exception e) {
            log.error("【获取工艺需求匹配详细信息】调用履约需求服务异常", e);
            throw new SdpDesignException("【获取工艺需求匹配详细信息】调用履约需求服务异常", e);
        }
    }

    /**
     * 获取已确认的工艺匹配信息
     *
     * @param supplyChainCraftDemandIdList 履约工艺需求ID集合
     */
    public List<CraftDemandMatchDetailDto.CraftDemandMatchDetail> getCraftMatchConfirmList(List<Long> supplyChainCraftDemandIdList) {
        if (CollUtil.isEmpty(supplyChainCraftDemandIdList)) {
            return List.of();
        }

        log.info("【获取已确认的工艺匹配信息】调用履约需求服务 请求参数:{}", supplyChainCraftDemandIdList);

        try {
            DataResponse<CraftDemandMatchDetailDto> response = craftMatchClient.craftMatchConfirmList(supplyChainCraftDemandIdList);
            log.info("【获取已确认的工艺匹配信息】调用履约需求服务 响应:{}", JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                throw new SdpDesignException("【获取已确认的工艺匹配信息】调用履约需求服务失败");
            }
            return Objects.isNull(response.getData()) ? Collections.emptyList() : response.getData().getCraftDemandMatchDetailList();
        } catch (Exception e) {
            throw new SdpDesignException("【获取已确认的工艺匹配信息】调用履约需求服务异常", e);
        }
    }

    /**
     * 根据第三方需求ID获取二次工艺信息
     *
     * @param supplyChainCraftDemandIds 履约工艺需求ID列表
     */
    public List<SecondCraftDemandDetailVo> getSecondCraftByThirdPartyDemand(List<Long> supplyChainCraftDemandIds) {
        if (CollectionUtil.isEmpty(supplyChainCraftDemandIds)) {
            return Collections.emptyList();
        }
        IdListReq idListReq = new IdListReq();
        idListReq.setIds(supplyChainCraftDemandIds);
        log.info("【根据第三方需求ID获取二次工艺信息】调用设计打版服务 请求参数:{}", JSON.toJSONString(idListReq));

        try {
            cn.yibuyun.framework.net.DataResponse<List<SecondCraftDemandDetailVo>> response = secondCraftDemandClient.getSecondCraftByThirdPartyDemandId(idListReq);
            log.info("【根据第三方需求ID获取二次工艺信息】调用设计打版服务 响应:{}", JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                throw new SdpDesignException("【根据第三方需求ID获取二次工艺信息】调用设计打版服务 失败");
            }
            return response.getData();
        } catch (Exception e) {
            log.error("【根据第三方需求ID获取二次工艺信息】调用设计打版服务 异常", e);
            throw new SdpDesignException("【根据第三方需求ID获取二次工艺信息】调用设计打版服务 异常", e);
        }

    }


    /**
     * 工艺添加至履约
     * @param craftDemandAddReqList
     */
    @Deprecated(since = "JV迁移")
    public List<DemandCreateResp> craftAddToSupplyChain(List<CraftDemandAddReqWrapper> craftDemandAddReqList) {

        log.info("【创建工艺需求】调用需求服务请求参数 :{}", JSON.toJSONString(craftDemandAddReqList));
        MultiValueMap<String, String> headers = (MultiValueMap<String, String>) ChannelHeaderFactory.DEMAND_SDK.getChannelHeader().getHeaderValue();

        DataResponse<List<DemandCreateResp>> dataResponse = craftDemandClient.addBatch(headers, craftDemandAddReqList);

        log.info("【创建工艺需求】调用需求服务响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            log.error("【创建工艺需求】调用需求服务失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException("【创建工艺需求】调用需求服务失败 " + dataResponse.getMessage());
        }
        return dataResponse.getData();
    }


    /**
     * 工艺关闭至履约
     * @param supplyChainCraftDemandIds
     */
    public void craftCloseToSupplyChain(List<Long> supplyChainCraftDemandIds) {

        ApplyCloseCraftDemandReqDto closeCraftDemandReq = new ApplyCloseCraftDemandReqDto();

        com.zjkj.scf.bundle.common.dto.common.req.IdsReq idsReqDto = new com.zjkj.scf.bundle.common.dto.common.req.IdsReq();
        idsReqDto.setIds(supplyChainCraftDemandIds);

        closeCraftDemandReq.setIdsReqDto(idsReqDto);
        closeCraftDemandReq.setDemandClose(DemandCloseEnum.OTHER);
        UserContent userContent = UserContentHolder.get();
        closeCraftDemandReq.setApplyUserId(userContent.getCurrentUserId());
        closeCraftDemandReq.setApplyUserName(userContent.getCurrentUserName());

        log.info("【拆版删除工艺需求】调用需求服务请求参数 :{}", JSON.toJSONString(closeCraftDemandReq));
        MultiValueMap<String, String> headers = (MultiValueMap<String, String>) ChannelHeaderFactory.DEMAND_SDK.getChannelHeader().getHeaderValue();
        DataResponse<ApplyCloseCraftDemandRespDto> dataResponse = craftDemandClient.applyClose(headers, closeCraftDemandReq);
        log.info("【拆版删除工艺需求】调用需求服务响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));

        if (!dataResponse.isSuccessful()) {
            log.error("【拆版删除工艺需求】调用需求服务失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException("【拆版删除工艺需求】调用需求服务失败 " + dataResponse.getMessage());
        }
    }

    /**
     * 物料确认关闭工艺至打版
     * @param delMatchIdList
     */
    /*
    public void materialMatchCloseCraftTaskToClothes(List<Long> delMatchIdList) {
        if (CollectionUtil.isEmpty(delMatchIdList)) {
            return;
        }

        SecondCraftCancelReq secondCraftCancelReq = new SecondCraftCancelReq();
        secondCraftCancelReq.setMaterialMatchIds(delMatchIdList);
        secondCraftCancelReq.setReason("物料确认切换匹配结果关闭工艺任务");

        log.info("【物料确认关闭工艺任务】调用打版服务 请求参数 :{}", JSON.toJSONString(secondCraftCancelReq));
        cn.yibuyun.framework.net.DataResponse<Void> dataResponse = secondCraftDemandClient.materialMatchCraftCancel(secondCraftCancelReq);
        log.info("【物料确认关闭工艺任务】调用打版服务 响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));

        if (!dataResponse.isSuccessful()) {
            log.error("【物料确认关闭工艺任务】调用打版服务失败 message:{}", dataResponse.getMessage());
            throw new PlmDesignException("【物料确认关闭工艺任务】调用打版服务失败 " + dataResponse.getMessage());
        }
    }

     */

    /**
     * 创建履约物料
     * @param supplyChainMaterialReqs
     */
    public void createMaterialToSupplyChain(List<MaterialDetailReq> supplyChainMaterialReqs) {

        if (CollectionUtil.isEmpty(supplyChainMaterialReqs)) {
            return ;
        }
        MaterialDetailBatchReq materialDetailBatchReq = new MaterialDetailBatchReq();
        materialDetailBatchReq.setDetailReqs(supplyChainMaterialReqs);
        log.info("【创建履约物料】调用履约需求服务 请求参数 :{}", JSON.toJSONString(materialDetailBatchReq));

        DataResponse<Boolean> dataResponse = materialDetailClient.create(materialDetailBatchReq);

        log.info("【创建履约物料】调用履约需求服务 响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            log.error("【创建履约物料】调用履约需求服务 失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException("【创建履约物料】调用履约需求服务 失败 " + dataResponse.getMessage());
        }
    }



    /**
     * 创建辅料找料需求
     * @param createBatchReq 入参
     */
    public List<AccessoryDemandCreateOpenV2Resp.DemandInfo> accessoryDemandCreate(AccessoryDemandCreateOpenV2Req createBatchReq) {
        if (Objects.isNull(createBatchReq) || CollectionUtil.isEmpty(createBatchReq.getAccessoryDemandInfos())) {
            return null;
        }
        log.info("【创建辅料找料需求】 请求参数 :{}", JSON.toJSONString(createBatchReq));
        //调用致景新接口 创建辅料找料需求-v2
        cn.yibuyun.framework.net.DataResponse<AccessoryDemandCreateOpenV2Resp> dataResponse = plmDemandRemoteHelper.accessoryDemandCreate(createBatchReq);
        log.info("【创建辅料找料需求】 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            log.error("【创建辅料找料需求】 失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException("【创建辅料需求】 失败 " + dataResponse.getMessage());
        }
        return Optional.ofNullable(dataResponse.getData()).orElseGet(AccessoryDemandCreateOpenV2Resp::new).getDemandInfos();
    }

    // /**
    //  * 创建辅料找料需求
    //  * @param createBatchReq 入参
    //  */
    // public List<AccessoryDemandCreateOpenResp.DemandInfo> accessoryDemandCreate(AccessoryDemandCreateOpenReq createBatchReq) {
    //     if (Objects.isNull(createBatchReq) || CollectionUtil.isEmpty(createBatchReq.getAccessoryDemandInfos())) {
    //         return null;
    //     }
    //     log.info("【创建辅料找料需求】 请求参数 :{}", JSON.toJSONString(createBatchReq));
    //     //todo  调用致景新接口 创建辅料找料需求-v2    plmDemandRemoteHelper.accessoryDemandCreate()
    //     // plmDemandRemoteHelper.accessoryDemandCreate()
    //     // 可以统一维护到 ZjDesignRemoteHelper类里
    //     cn.yibuyun.framework.net.DataResponse<AccessoryDemandCreateOpenResp> dataResponse = bomDemandOpenClient.accessoryDemandCreate(createBatchReq);
    //     log.info("【创建辅料找料需求】 dataResponse:{}", JSON.toJSONString(dataResponse));
    //     if (!dataResponse.isSuccessful()) {
    //         log.error("【创建辅料找料需求】 失败 message:{}", dataResponse.getMessage());
    //         throw new SdpDesignException("【创建辅料需求】 失败 " + dataResponse.getMessage());
    //     }
    //     return Optional.ofNullable(dataResponse.getData()).orElseGet(AccessoryDemandCreateOpenResp::new).getDemandInfos();
    // }

    /**
     * 批量查询找料需求信息（好料账户服务）
     * @param supplyDemandIdSet 履约需求id集合
     */
    public List<DemandDetailResp> batchQueryDemand(Set<Long> supplyDemandIdSet) {
        if (CollectionUtil.isEmpty(supplyDemandIdSet)) {
            return List.of();
        }
        IdsReq idsReq = new IdsReq();
        idsReq.setIds(supplyDemandIdSet);
        log.info("【批量查询需求信息】 请求参数 :{}", JSON.toJSONString(supplyDemandIdSet));
        cn.yibuyun.framework.net.DataResponse<List<DemandDetailResp>> dataResponse = scmHouliuDemandClient.query(idsReq);
        log.info("【批量查询需求信息】 响应结果 dataResponse:{}", JSON.toJSONString(dataResponse));
        if (!dataResponse.isSuccessful()) {
            log.error("【批量查询需求信息】 失败 message:{}", dataResponse.getMessage());
            throw new SdpDesignException("【批量查询需求信息】 失败 " + dataResponse.getMessage());
        }
        return dataResponse.getData();
    }


    /**
	 * 根据履约工艺需求Id查询样衣任务
	 */
	public Map<Long, List<CraftSampleTaskInnerInfoResp>> getTaskBySupplyChainCraftIds(List<Long> supplyChainCraftIdList) {
		log.info("【根据工艺需求Id查询样衣任务】请求参数 req:{}", JSON.toJSONString(supplyChainCraftIdList));

		try {
			DataResponse<Map<Long, List<CraftSampleTaskInnerInfoResp>>> response = sampleCraftTaskClient.findByTaskByCraftDemandId(supplyChainCraftIdList);
			log.info("【根据工艺需求Id查询样衣任务】响应结果:{}", JSON.toJSONString(response));
			if (!response.isSuccessful()) {
				throw new SdpDesignException("【根据工艺需求Id查询样衣任务】 失败");
			}
			return response.getData();
		} catch (Exception e) {
			log.error("【根据工艺需求Id查询样衣任务】异常", e);
			throw new SdpDesignException("【根据工艺需求Id查询样衣任务】 异常");
		}

	}

}
