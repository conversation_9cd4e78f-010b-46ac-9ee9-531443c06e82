package tech.tiangong.sdp.design.service;

import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksBatchBizListReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksListReq;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;
import tech.tiangong.sdp.design.vo.resp.remark.DesignRemarksBatchListReq;
import tech.tiangong.sdp.design.vo.resp.remark.DesignRemarksVO;

import java.util.List;
import java.util.Map;

/**
* 设计打版备注信息
* <br>CreateDate August 10,2021
* <AUTHOR>
* @since 1.0
*/
public interface DesignRemarksService {

    /**
    * 创建数据
    * @param req 数据实体
    * @return 创建结果
    */
    DesignRemarksVO create(DesignRemarksReq req);

    /**
     * 查询-根据设计款号查询
     *
     * @param req 设计打版备注信息对象
     * @return 设计打版备注信息实体
     */
    List<DesignRemarksVO> dataList(DesignRemarksListReq req);

    /**
     * 批量查询-根据设计款号批量查询
     *
     * @param req 设计打版备注信息对象
     * @return 设计打版备注信息实体
     */
    Map<String, List<DesignRemarksVO>> batchDataList(DesignRemarksBatchListReq req);

    /**
     * 批量查询-根据业务主键进行查询
     *
     * @param req 设计打版备注信息对象
     * @return 设计打版备注信息实体
     */
    Map<Long, List<DesignRemarksVO>> batchBizDataList(DesignRemarksBatchBizListReq req);



}