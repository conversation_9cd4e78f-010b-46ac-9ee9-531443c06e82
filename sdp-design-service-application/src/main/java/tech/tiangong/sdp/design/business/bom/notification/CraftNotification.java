package tech.tiangong.sdp.design.business.bom.notification;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import com.alibaba.fastjson.JSON;
import com.zjkj.scf.bundle.common.dto.common.req.IdsReq;
import com.zjkj.scf.bundle.common.dto.demand.enums.BusinessTypeEnum;
import com.zjkj.scf.bundle.common.dto.demand.enums.BusinessVersionEnum;
import com.zjkj.scf.bundle.common.dto.demand.enums.DemandChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import tech.tiangong.sdp.clothes.vo.resp.SecondCraftDemandDetailVo;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.converter.CraftDemandInfoConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.CraftDemandStateEnum;
import tech.tiangong.sdp.design.remote.DemandRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.vo.dto.bom.CraftNotificationDto;
import tech.tiangong.sdp.design.vo.req.zj.demand.CraftDemandCloseOpenV2Req;
import tech.tiangong.sdp.design.vo.req.zj.demand.CraftDemandCreateOpenV2Req;
import tech.tiangong.sdp.design.vo.resp.zj.demand.CraftDemandCreateOpenV2Resp;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class CraftNotification implements Notification<CraftNotificationDto> {
    private List<CraftNotificationDto> craftNotificationDtos = new LinkedList<>();

    private final BomOrderRepository bomOrderRepository = SpringUtil.getBean(BomOrderRepository.class);
    private final BomOrderMaterialRepository bomOrderMaterialRepository = SpringUtil.getBean(BomOrderMaterialRepository.class);
    private final PrototypeHistoryRepository prototypeHistoryRepository = SpringUtil.getBean(PrototypeHistoryRepository.class);
    private final PrototypeDetailRepository prototypeDetailRepository = SpringUtil.getBean(PrototypeDetailRepository.class);
    private DemandRemoteHelper demandRemoteHelper = SpringUtil.getBean(DemandRemoteHelper.class);
    private ZjDesignRemoteHelper zjDesignRemoteHelper = SpringUtil.getBean(ZjDesignRemoteHelper.class);
    private final CraftDemandInfoRepository craftDemandInfoRepository = SpringUtil.getBean(CraftDemandInfoRepository.class);
    // private final CraftDemandInfoTransientRepository craftDemandInfoTransientRepository = SpringUtil.getBean(CraftDemandInfoTransientRepository.class);
    private final BomMaterialDemandRepository bomMaterialDemandRepository = SpringUtil.getBean(BomMaterialDemandRepository.class);


    @Override
    public void add(CraftNotificationDto dto) {
        if (Objects.nonNull(dto)) {
            craftNotificationDtos.add(dto);
        }
    }

    @Override
    public void addBatch(List<CraftNotificationDto> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            craftNotificationDtos.addAll(list);
        }
    }

    @Override
    public List<CraftNotificationDto> getAll() {
        return craftNotificationDtos;
    }

    @Override
    public void send() {
        //推送工艺到履约
        log.info("推送工艺到履约:{}", JSONUtil.toJsonStr(craftNotificationDtos));
        List<CraftNotificationDto> addCraftNotificationDtos = craftNotificationDtos.stream().filter(e -> Objects.equals(CraftDemandStateEnum.SUBMIT.getCode(), e.getState())).collect(Collectors.toList());
        List<CraftNotificationDto> closeCraftNotificationDtos = craftNotificationDtos.stream().filter(e -> Objects.equals(CraftDemandStateEnum.CLOSED.getCode(), e.getState())).collect(Collectors.toList());

        handlerAddBomCraftDemandSync(addCraftNotificationDtos);
        handlerDelBomCraftDemandSync(closeCraftNotificationDtos);

        log.info("推送工艺到履约,完成");
    }


    /**
     * 处理将新增bom二次工艺同步给履约组
     *
     * @param addCraftDemandList
     */
    private void handlerAddBomCraftDemandSync(List<CraftNotificationDto> addCraftDemandList) {
        if (CollectionUtil.isEmpty(addCraftDemandList)) {
            log.info("bom提交v3无新增工艺");
            return;
        }
        Long bomId = addCraftDemandList.get(0).getBomId();
        BomOrder bomOrder = bomOrderRepository.getById(bomId);

        log.info("bom提交v3新增工艺同步履约-addCraftDemandList :{} ====", JSON.toJSONString(addCraftDemandList));
        Long prototypeId = bomOrder.getPrototypeId();
        //将新增二次工艺同步给履约组
        List<CraftNotificationDto> craftDemandList = JSON.parseArray(JSON.toJSONString(addCraftDemandList), CraftNotificationDto.class);

        Set<Long> bomMaterialIdSet = craftDemandList.stream().map(CraftNotificationDto::getBomMaterialId).collect(Collectors.toSet());
        Map<Long, BomOrderMaterial> bomOrderMaterialMap = bomOrderMaterialRepository.listByIds(bomMaterialIdSet).stream()
                .collect(Collectors.toMap(BomOrderMaterial::getBomMaterialId, Function.identity(), (k1, k2) -> k1));

        PrototypeHistory prototypeHistory = prototypeHistoryRepository.getByPrototypeId(prototypeId);
        PrototypeDetail prototypeDetail = prototypeDetailRepository.getByPrototypeId(prototypeHistory.getPrototypeId());
        List<CraftDemandCreateOpenV2Req.CraftDemandAddReq> craftDemandAddReqList = craftDemandList.stream().filter(craft -> Objects.isNull(craft.getThirdPartyCraftDemandId()))
                .map(craft -> {

                    BomOrderMaterial bomOrderMaterial = bomOrderMaterialMap.get(craft.getBomMaterialId());
                    SdpDesignException.notNull(bomOrderMaterial, "同步工艺需求给履约-bom物料信息查询失败! bomMaterialId:{}", craft.getBomMaterialId());
                    CraftDemandCreateOpenV2Req.CraftDemandAddReq craftDemandAddReq = null;
                    //根据是否有履约的物料需求id来判断是否是选料还是辅料需求
                    if (Objects.nonNull(bomOrderMaterial.getBomMaterialDemandId())) {
                        BomMaterialDemand bomMaterialDemand = bomMaterialDemandRepository.getById(bomOrderMaterial.getBomMaterialDemandId());
                        craftDemandAddReq = buildCraftDemandAddRequest4Demand(craft, bomMaterialDemand.getSupplyChainDemandId(), bomMaterialDemand.getSupplyChainDemandCode(), prototypeHistory, prototypeDetail);
                    } else {
                        Long materialSnapshotId = bomOrderMaterial.getMaterialSnapshotId();
                        //设置物料快照id, 履约创建工艺必须参数
                        SdpDesignException.notNull(materialSnapshotId, "同步工艺需求给履约-bom物料快照id不存在! materialSnapshotId:{}", materialSnapshotId);
                        craftDemandAddReq = buildCraftDemandAddRequest4Material(craft, materialSnapshotId, prototypeHistory, prototypeDetail);
                    }

                    return craftDemandAddReq;
                }).collect(Collectors.toList());

        //将供应链添加的工艺需求与设计打版关联起来
        List<CraftDemandCreateOpenV2Resp.DemandInfo> demandCreateRespList = zjDesignRemoteHelper.craftDemandCreate(craftDemandAddReqList, prototypeHistory.getBizChannel());
        Map<Long, CraftDemandCreateOpenV2Resp.DemandInfo> demandCreateRespMap = demandCreateRespList.stream().collect(Collectors.toMap(demandResp -> {
            Map<String, Object> extra = demandResp.getExtra();
            return Long.valueOf(extra.get(CraftDemandInfoConverter.CRAFT_DEMAND_ID).toString());
        }, Function.identity(), (k1, k2) -> k1));


        //更新数据库

        List<CraftDemandInfoTransient> craftDemandInfoTransients = new LinkedList<>();

        List<CraftDemandInfo> craftDemandInfos = addCraftDemandList.stream().map(dto -> {

            CraftDemandCreateOpenV2Resp.DemandInfo demandCreateResp = demandCreateRespMap.get(dto.getCraftDemandId());
            if (Objects.nonNull(demandCreateResp)) {
                CraftDemandInfo craftDemandInfo = new CraftDemandInfo();
                craftDemandInfo.setCraftDemandId(dto.getCraftDemandId());
                craftDemandInfo.setThirdPartyCraftDemandId(demandCreateResp.getDemandId());
                craftDemandInfo.setThirdPartyCraftDemandCode(demandCreateResp.getDemandCode());

                //需设置回传的工艺需求id
                dto.setThirdPartyCraftDemandId(demandCreateResp.getDemandId());
                dto.setThirdPartyCraftDemandCode(demandCreateResp.getDemandCode());

                CraftDemandInfoTransient craftDemandInfoTransient = new CraftDemandInfoTransient();
                BeanUtils.copyProperties(craftDemandInfo, craftDemandInfoTransient);
                craftDemandInfoTransients.add(craftDemandInfoTransient);

                return craftDemandInfo;
            } else {
                log.error("同步工艺需求给履约-履约返回工艺需求id为空! craftDemandId:{}", dto.getCraftDemandId());
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        log.info("更新工艺需求信息 craftDemandInfos:{}", JSON.toJSONString(craftDemandInfos));
        craftDemandInfoRepository.updateBatchById(craftDemandInfos);
        //TODO 优化代码
        // craftDemandInfoTransientRepository.updateBatchById(craftDemandInfoTransients);
    }


    /**
     * 处理将bom删除二次工艺同步给履约组
     *
     * @param craftDemandInfoList
     */
    private void handlerDelBomCraftDemandSync(List<CraftNotificationDto> craftDemandInfoList) {

        if (CollectionUtil.isEmpty(craftDemandInfoList)) {
            log.info("【bom删除二次工艺需求v3】无此二次工艺");
            return;
        }

        List<Long> thirdPartyCraftDemandIds = craftDemandInfoList.stream().filter(craftDemandInfo -> Objects.nonNull(craftDemandInfo.getThirdPartyCraftDemandId()))
                .map(CraftNotificationDto::getThirdPartyCraftDemandId).collect(Collectors.toList());
        List<Long> craftDemandIdList = craftDemandInfoList.stream().filter(craftDemandInfo -> Objects.nonNull(craftDemandInfo.getCraftDemandId()))
                .map(CraftNotificationDto::getCraftDemandId).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(thirdPartyCraftDemandIds)) {
            log.info("【bom删除二次工艺需求v3】还未与履约工艺需求关联");
            return;
        }
        log.info("【bom删除二次工艺需求v3】原生工艺ID :{}", JSON.toJSONString(thirdPartyCraftDemandIds));
        //工艺取消时,也同步将这个工艺的二次工艺取消
        List<SecondCraftDemandDetailVo> secondCraftDemandList = demandRemoteHelper.getSecondCraftByThirdPartyDemand(thirdPartyCraftDemandIds);
        if (CollectionUtil.isNotEmpty(secondCraftDemandList)) {
            List<Long> thirdPartyChildCraftDemandIds = secondCraftDemandList.stream().filter(second -> Objects.nonNull(second.getThirdPartyChildCraftDemandId()))
                    .map(SecondCraftDemandDetailVo::getThirdPartyChildCraftDemandId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(thirdPartyChildCraftDemandIds)) {
                thirdPartyCraftDemandIds.addAll(thirdPartyChildCraftDemandIds);
            }
        }

        IdsReq idsReqDto = new IdsReq();
        idsReqDto.setIds(thirdPartyCraftDemandIds);
        CraftDemandCloseOpenV2Req closeCraftDemandReq = new CraftDemandCloseOpenV2Req();
        // closeCraftDemandReq.setExtCraftDemandIds(craftDemandIdList);
        closeCraftDemandReq.setCraftDemandIds(thirdPartyCraftDemandIds);
        // closeCraftDemandReq.setDemandClose(DemandCloseEnum.OTHER.getDesc());
        closeCraftDemandReq.setApplyUserName(UserContentHolder.get().getCurrentUserName());

        //调用致景服务删除工艺需求
        zjDesignRemoteHelper.craftDemandClose(closeCraftDemandReq);

    }

    /**
     * 辅料需求的，工艺需求请求
     *
     * @param craft
     * @param prototypeHistory
     * @param prototypeDetail
     * @return
     */
    private static CraftDemandCreateOpenV2Req.CraftDemandAddReq buildCraftDemandAddRequest4Demand(CraftNotificationDto craft,Long supplyChainDemandId,
                                                                                                  String supplyChainDemandCode,
                                                                                                  PrototypeHistory prototypeHistory, PrototypeDetail prototypeDetail) {
        CraftDemandCreateOpenV2Req.CraftDemandAddReq craftDemandAddReq = buildDefaultCraftDemandAddRequest(craft, prototypeHistory, prototypeDetail);
        List<CraftDemandCreateOpenV2Req.CraftDemandAddReq.PrototypeReq> prototypeReq = craftDemandAddReq.getPrototypeReq();
        if (CollectionUtil.isNotEmpty(prototypeReq)) {
            prototypeReq.forEach(prototype -> {
                prototype.setCraftRelationDemandId(supplyChainDemandId);
                prototype.setCraftRelationDemandCode(supplyChainDemandCode);
            });
        }
        craftDemandAddReq.getCraftDemandBaseReq().setDemandChannel(DemandChannelEnum.BILL_MATERIAL.getCode());

        return craftDemandAddReq;
    }

    /**
     * 选料的，工艺需求请求
     *
     * @param craft
     * @param prototypeHistory
     * @param prototypeDetail
     * @return
     */
    private static CraftDemandCreateOpenV2Req.CraftDemandAddReq  buildCraftDemandAddRequest4Material(CraftNotificationDto craft,Long materialSnapshotId, PrototypeHistory prototypeHistory, PrototypeDetail prototypeDetail) {
        CraftDemandCreateOpenV2Req.CraftDemandAddReq  craftDemandAddReq = buildDefaultCraftDemandAddRequest(craft, prototypeHistory, prototypeDetail);
        List<CraftDemandCreateOpenV2Req.CraftDemandAddReq .PrototypeReq> prototypeReq = craftDemandAddReq.getPrototypeReq();
        if (CollectionUtil.isNotEmpty(prototypeReq)) {
            prototypeReq.forEach(prototype -> {
                prototype.setMaterialSnapshotId(materialSnapshotId);
            });
        }
        craftDemandAddReq.getCraftDemandBaseReq().setDemandChannel(DemandChannelEnum.DESIGNER_CHOOSE_MATERIAL_NO_DEMAND.getCode());

        return craftDemandAddReq;
    }


    private static CraftDemandCreateOpenV2Req.CraftDemandAddReq buildDefaultCraftDemandAddRequest(CraftNotificationDto craft, PrototypeHistory prototypeHistory, PrototypeDetail prototypeDetail) {
        CraftDemandCreateOpenV2Req.CraftDemandAddReq craftDemandAddReq = new CraftDemandCreateOpenV2Req.CraftDemandAddReq();
        craftDemandAddReq.setBusinessType(BusinessTypeEnum.PROTOTYPE.name());

        CraftDemandCreateOpenV2Req.CraftDemandAddReq.PrototypeReq prototypeReq = new CraftDemandCreateOpenV2Req.CraftDemandAddReq.PrototypeReq();
        //辅料需求一定要传CraftRelationDemandId、CraftRelationDemandCode
        //prototypeReq.setCraftRelationDemandId(craft.getRelationDemandId());
        //prototypeReq.setCraftRelationDemandCode(DEMAND_CODE_IS_NULL);
        prototypeReq.setExtPrototypeCode(prototypeHistory.getDesignCode());
        prototypeReq.setPrototypeCategory(prototypeHistory.getCategoryName());
        //只有是选料的时候才会传
//        prototypeReq.setMaterialSnapshotId(craft.getMaterialSnapshotId());
        // prototypeReq.setRegionId(prototypeHistory .getRegionId());
        // String regionName = prototypeHistory.getRegionName();
        // regionName = StringUtils.contains(regionName, "市") ? regionName : regionName + "市";
        // prototypeReq.setRegionName(regionName);
        Map<String, Object> extra = new HashMap<>(16);
        extra.put(CraftDemandInfoConverter.CRAFT_DEMAND_ID, craft.getCraftDemandId());
        prototypeReq.setExtra(extra);
        craftDemandAddReq.setPrototypeReq(List.of(prototypeReq));

        CraftDemandCreateOpenV2Req.CraftDemandAddReq.CraftDemandBaseReq craftDemandBaseReq = new CraftDemandCreateOpenV2Req.CraftDemandAddReq.CraftDemandBaseReq();
        BeanUtils.copyProperties(craft, craftDemandBaseReq);
        //注意：选料是传：DemandChannelEnum.DESIGNER_CHOOSE_MATERIAL_NO_DEMAND、辅料需求是传：DemandChannelEnum.BILL_MATERIAL
//        craftDemandBaseReq.setDemandChannel(DemandChannelEnum.DESIGNER_CHOOSE_MATERIAL_NO_DEMAND.getCode());
        craftDemandBaseReq.setBusinessVersion(BusinessVersionEnum.PROTOTYPE_BULK_INTEGRATED+"");
        craftDemandBaseReq.setCreateUser(craft.getCreatorName());
        // craftDemandBaseReq.setCreateId(craft.getCreatorId());
        craftDemandAddReq.setCraftDemandBaseReq(craftDemandBaseReq);

        CraftDemandCreateOpenV2Req.CraftDemandAddReq.CraftDemandDetailReq craftDemandDetailReq = new CraftDemandCreateOpenV2Req.CraftDemandAddReq.CraftDemandDetailReq();
        BeanUtils.copyProperties(craft, craftDemandDetailReq);
        craftDemandDetailReq.setCraftUndertakerObj(craft.getFactoryName());
        craftDemandDetailReq.setFactory(craft.getCustomerSupplyFactory());
        craftDemandDetailReq.setFactoryId(craft.getInnerFactoryId());
        // PrototypeStyleReferEnum prototypeStyleReferEnum = PrototypeStyleReferEnum.findByCode(prototypeDetail.getStyleReferType());
        // craftDemandDetailReq.setUrgencyType(Objects.nonNull(prototypeStyleReferEnum) ? prototypeStyleReferEnum.getDesc() : "");
        craftDemandAddReq.setCraftDemandDetailReq(craftDemandDetailReq);
        return craftDemandAddReq;
    }


}
