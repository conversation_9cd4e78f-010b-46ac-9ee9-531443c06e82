package tech.tiangong.sdp.design.service.download.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.DesignAsyncTask;
import tech.tiangong.sdp.design.entity.VisualDemandAsyncTask;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.enums.DownloadTaskStatusEnum;
import tech.tiangong.sdp.design.repository.DesignAsyncTaskRepository;
import tech.tiangong.sdp.design.repository.VisualDemandAsyncTaskRepository;
import tech.tiangong.sdp.design.service.VisualDemandService;
import tech.tiangong.sdp.design.service.download.DownloadTaskService;
import tech.tiangong.sdp.design.vo.dto.download.FileUploadDTO;
import tech.tiangong.sdp.design.vo.query.download.DownloadTaskQuery;
import tech.tiangong.sdp.design.vo.req.visual.SaveVisualDemandBySpuReq;
import tech.tiangong.sdp.design.vo.resp.download.DesignAsyncTaskVo;
import tech.tiangong.sdp.design.vo.resp.download.DownloadTaskPageVo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 下载任务管理
 *
 * <AUTHOR>
 * @date 2024/8/27
 */
@Slf4j
@Service
@AllArgsConstructor
public class VisualDemandAsyncTaskServiceImpl  {

    private final VisualDemandAsyncTaskRepository visualDemandAsyncTaskRepository;
    private final VisualDemandService visualDemandService;



    /**
     * 保存异步任务
     */
    public void saveAsyncTaskList(List<SaveVisualDemandBySpuReq> reqs) {
        List<String> codes = visualDemandAsyncTaskRepository.list().stream().map(VisualDemandAsyncTask::getStyleCode).toList();

        // 1. 去重 + 提取styleCode
        Map<String, SaveVisualDemandBySpuReq> reqMap = reqs.stream()
                .collect(Collectors.toMap(
                        SaveVisualDemandBySpuReq::getStyleCode,
                        Function.identity(),
                        (existing, replacement) -> existing // 保留第一个
                ));

        // 使用Stream流式操作批量构建任务
        List<VisualDemandAsyncTask> tasks = reqMap.values().stream()
                .filter(req -> !codes.contains(req.getStyleCode()))
                .map(req -> {
                    VisualDemandAsyncTask task = new VisualDemandAsyncTask();
                    task.setStyleCode(req.getStyleCode());
                    task.setDemandReq(JSONObject.toJSONString(req)); // 使用JSON序列化
                    task.setRetryCount(0);
                    return task;
                })
                .collect(Collectors.toList());
        visualDemandAsyncTaskRepository.saveBatch(tasks);
    }

    public void handle() {
        List<VisualDemandAsyncTask> pendingTasks = visualDemandAsyncTaskRepository.getPendingTasks();
        if (CollectionUtil.isNotEmpty(pendingTasks)) {
            pendingTasks.forEach(task -> {
                    handleUser(task);
                    // 处理任务逻辑，例如调用其他服务或执行数据库操作等
                    task.setState(1); // 假设处理中状态为1
                    visualDemandAsyncTaskRepository.updateById(task);
                    // 异步执行创建逻辑
                    CompletableFuture.runAsync(() -> {
                        handleUser(task);
                        try {
                            create(task);
                        } catch (Exception e) {
                            //失败处理
                            task.setState(3);
                            task.setRetryCount(task.getRetryCount() + 1);
                            task.setErrorMsg(e.getMessage());
                            task.setProcessedTime(LocalDateTime.now());
                            visualDemandAsyncTaskRepository.updateById(task);
                        }
                    });
            });
        }
    }

    public void create(VisualDemandAsyncTask task){
        SaveVisualDemandBySpuReq demandReq = JSONObject.parseObject(
                task.getDemandReq(), SaveVisualDemandBySpuReq.class);
        visualDemandService.createVisualDemand4Front(demandReq);
        task.setState(2);
        task.setRetryCount(task.getRetryCount() + 1);
        task.setProcessedTime(LocalDateTime.now());
        visualDemandAsyncTaskRepository.updateById(task);
    }

    public void handleUser(VisualDemandAsyncTask task){
        UserContent user = new UserContent();
        user.setCurrentUserId(task.getCreatorId());
        user.setCurrentUserCode("");
        user.setTenantId(2L);
        user.setSystemCode("SDP");
        user.setCurrentUserName(task.getCreatorName());
        UserContentHolder.set(user);
    }
}
