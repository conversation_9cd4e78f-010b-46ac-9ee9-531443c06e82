package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import team.aikero.admin.common.vo.DictVo;
import team.aikero.blade.core.exception.BusinessException;
import team.aikero.blade.oss.OssTemplate;
import team.aikero.blade.oss.OssTemplateExt;
import tech.tiangong.id.IdPool;
import tech.tiangong.pop.common.dto.ProductImageChangeStateDto;
import tech.tiangong.pop.common.resp.ShopResp;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.constant.SdpDesignConstant;
import tech.tiangong.sdp.design.converter.DesignStyleConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.excel.DesignStyleImportData;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.helper.UploaderOssHelper;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.DesignerRemoteHelper;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.vo.base.prototype.OpsObject;
import tech.tiangong.sdp.design.vo.dto.product.PopProductImageChangeStateDto;
import tech.tiangong.sdp.design.vo.dto.spot.StyleHisDataUploadExcelDto;
import tech.tiangong.sdp.design.vo.dto.style.DesignStyleUpdateDto;
import tech.tiangong.sdp.design.vo.query.style.DesignStyleQuery;
import tech.tiangong.sdp.design.vo.req.demand.DesignDemandCreateReq;
import tech.tiangong.sdp.design.vo.req.identify.SpuIdentifyAddReq;
import tech.tiangong.sdp.design.vo.req.log.PushZjLogReq;
import tech.tiangong.sdp.design.vo.req.mq.DesignPrototypeMqDTO;
import tech.tiangong.sdp.design.vo.req.mq.DesignStyleUpdateDTO;
import tech.tiangong.sdp.design.vo.req.mq.demand.ProductUpdateMqDto;
import tech.tiangong.sdp.design.vo.req.prototype.inner.PushSpuSkc2ZjReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleCreateReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleImportReq;
import tech.tiangong.sdp.design.vo.req.style.DesignStyleUpdateReq;
import tech.tiangong.sdp.design.vo.req.style.SpuProductImgReq;
import tech.tiangong.sdp.design.vo.req.zj.design.DesignStyleCreateOpenV2Req;
import tech.tiangong.sdp.design.vo.resp.prototype.ColorInfoVo;
import tech.tiangong.sdp.design.vo.resp.prototype.NormalSkcCreateResp;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleCreateResp;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleImportResp;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleVo;
import tech.tiangong.sdp.design.vo.resp.style.DesignStyleWebDetailVo;
import tech.tiangong.sdp.design.vo.resp.zj.design.DesignStyleCreateOpenV2Resp;
import tech.tiangong.sdp.material.pojo.dto.DesignerDTO;
import tech.tiangong.sdp.material.pojo.web.request.designer.DesignerRemoteReq;
import tech.tiangong.sdp.qy.converter.DictTreeConverter;
import tech.tiangong.sdp.utils.Bool;
import tech.tiangong.sdp.utils.CommonUtil;
import tech.tiangong.sdp.utils.StreamUtil;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SPU表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DesignStyleServiceImpl implements DesignStyleService {
    private final DesignStyleRepository designStyleRepository;
    private final DesignStyleVersionRepository designStyleVersionRepository;
    private final ClothingCodeGenerateService clothingCodeGenerateService;
    private final DesignerRemoteHelper designerRemoteHelper;
    private final DesignDemandRepository designDemandRepository;
    private final DesignDemandDetailRepository designDemandDetailRepository;
    private final PrototypeService prototypeService;
    private final PrototypeRepository prototypeRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final PushZjLogService pushZjLogService;
    private final PushZjLogRepository pushZjLogRepository;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;

    private final MqProducer mqProducer;
    private final PopProductHelper popProductHelper;
    private final VisualSpuService visualSpuService;
    private final SpuIdentifyService spuIdentifyService;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final UploaderOssHelper ossHelper;

    @Override
    public PageRespVo<DesignStyleVo> page(DesignStyleQuery query) {
        IPage<DesignStyle> page = designStyleRepository.findPage(query);

        List<DesignStyle> records = page.getRecords();
        List<DesignStyleVo> voList = List.of();
        if (CollUtil.isNotEmpty(records)) {
            voList = records.stream().map(this::entity2Vo).collect(Collectors.toList());
        }

        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), voList);
    }

    @Override
    public DesignStyleWebDetailVo getWebDetail(String styleCode) {
        SdpDesignException.notBlank(styleCode, "spu编码为空! ");
        DesignStyle entity = designStyleRepository.getByStyleCode(styleCode);
        if (Objects.isNull(entity)) {
            return null;
        }
        DesignStyleVo designStyleVo = this.entity2Vo(entity);
        DesignStyleWebDetailVo detailVo = new DesignStyleWebDetailVo();
        BeanUtils.copyProperties(designStyleVo, detailVo);

        //当SPU下有已提交的SKC,不允许再次编辑尺码标准
        detailVo.setUpdateSizeStandard(Bool.YES.getCode());
        List<Prototype> prototypeList = prototypeRepository.listByStyleCode(entity.getStyleCode());
        if (CollUtil.isNotEmpty(prototypeList)) {
            Prototype prototype = prototypeList.stream()
                    .filter(item -> Objects.equals(PrototypeStatusEnum.DECOMPOSED.getCode(), item.getPrototypeStatus()))
                    .findFirst().orElse(null);
            if (Objects.nonNull(prototype)) {
                detailVo.setUpdateSizeStandard(Bool.NO.getCode());
            }
        }

        //灵感任务需求信息
        this.setDemandInfo(detailVo);

        return detailVo;
    }

    private void setDemandInfo(DesignStyleWebDetailVo updateDetailVo) {
        Long designDemandId = updateDetailVo.getDesignDemandId();
        if (Objects.isNull(designDemandId)) {
            return;
        }
        DesignDemand designDemand = designDemandRepository.getById(designDemandId);
        if (Objects.isNull(designDemand)) {
            return;
        }

        DesignStyleWebDetailVo.DesignDemandInfo designDemandInfo = new DesignStyleWebDetailVo.DesignDemandInfo();
        BeanUtils.copyProperties(designDemand, designDemandInfo);

        DesignDemandDetail demandDetail = designDemandDetailRepository.getByDemandId(designDemandId);
        if (Objects.nonNull(demandDetail)) {
            designDemandInfo.setOriginalImage(demandDetail.getOriginalImage());
            List<String> imageAllList = new ArrayList<>();
            if(!CollUtil.isEmpty(demandDetail.getInspirationImageList())){
                imageAllList.addAll(demandDetail.getInspirationImageList());
            }

            //展示4k图
            List<DesignDemandCreateReq.ImageInfo> imageList = demandDetail.getInspirationImageJson();
            if(null!= imageList){
                List<String> ultraHdUrlList = imageList.stream()
                        .filter(item -> StrUtil.isNotBlank(item.getUltraHdUrl()))
                        .map(DesignDemandCreateReq.ImageInfo::getUltraHdUrl)
                        .collect(Collectors.toList());
                if(!CollUtil.isEmpty(ultraHdUrlList)){
                    imageAllList.addAll(ultraHdUrlList);
                }
            }
            designDemandInfo.setInspirationImageList(imageAllList);
            designDemandInfo.setAigcRemark(demandDetail.getAigcRemark());
            updateDetailVo.setAigcRemark(demandDetail.getAigcRemark());
        }
        updateDetailVo.setDesignDemandInfo(designDemandInfo);
    }

    @Override
    public DesignStyleVo getLatestVersionByStyleCode(String styleCode) {
        SdpDesignException.notBlank(styleCode, "spu编码为空! ");
        DesignStyle entity = designStyleRepository.getByStyleCode(styleCode);
        return this.entity2Vo(entity);
    }

    private List<DesignStyleVo> getDesignStyleVoList(List<String> styleCodeList) {
        SdpDesignException.notEmpty(styleCodeList, "spu编码为空! ");
        List<DesignStyle> entityList = designStyleRepository.listByStyleCodes(styleCodeList);
        if (CollUtil.isEmpty(entityList)) {
            return Collections.emptyList();
        }
         return entityList.stream()
                 .map(this::entity2Vo)
                 .collect(Collectors.toList());
    }

    private DesignStyleVo entity2Vo(DesignStyle entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        DesignStyleVo vo = new DesignStyleVo();
        BeanUtils.copyProperties(entity, vo);
        this.resetValues(entity, vo);
        return vo;
    }

    private void resetValues(DesignStyle entity, DesignStyleVo vo) {
        //季节
        String styleSeason = entity.getStyleSeason();
        if (StringUtils.isNotBlank(styleSeason)) {
            vo.setStyleSeasonList(JSON.parseArray(styleSeason, OpsObject.class));
        }
        vo.setAttributes(entity.getAttributes());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DesignStyleCreateResp createSpuSkc(DesignStyleCreateReq req) {
        log.info("=== 创建SPU与SKC-入参:{} ===", JSON.toJSONString(req));
        DesignStyleSourceTypeEnum sourceTypeEnum = DesignStyleSourceTypeEnum.findByCode(req.getSourceType());
        //校验入参
        this.checkCreateSpuSkc(req, sourceTypeEnum);
        UserContent userContent = UserContentHolder.get();
        Long currentUserId = userContent.getCurrentUserId();

        //自建spu查询店铺对应平台信息
        if (Objects.equals(DesignStyleSourceTypeEnum.SELF_SPU_SOURCE.getCode(), req.getSourceType())) {
            String platformName = this.getPlatformName(req.getStoreName());
            SdpDesignException.notBlank(platformName, "店铺对应平台信息为空! 店铺名:{}", req.getStoreName());
            req.setPlatformName(platformName);
        }

        //设计师才能创建SPU
        DesignerDTO designerDTO = this.checkDesigner(currentUserId);

        //新增SPU并归档
        long designStyleId = IdPool.getId();
        String styleCode = clothingCodeGenerateService.generateSpuCode(ClothingCodeSourceEnum.DESIGN);
        Integer styleVersionNum = 1;
        DesignStyle designStyleEo = DesignStyleConverter.buildSpuCreateEo(req, designStyleId, styleCode,
                styleVersionNum, currentUserId, designerDTO);

        DesignStyleCreateResp styleCreateResp = this.saveSpuAndSkc(designStyleEo, sourceTypeEnum, null);
        log.info("=== 创建SPU与SKC成功-返回:{} ===", JSON.toJSONString(styleCreateResp));
        return styleCreateResp;
    }

    private DesignerDTO checkDesigner(Long currentUserId) {
        DesignerRemoteReq designerRemoteReq = new DesignerRemoteReq();
        designerRemoteReq.setDesignerId(currentUserId + "");
        DesignerDTO designerDTO = designerRemoteHelper.getByDesignerId(designerRemoteReq);
        SdpDesignException.notNull(designerDTO, "设计师才能创建SPU! 请先加入设计组");
        return designerDTO;
    }

    private DesignStyleCreateResp saveSpuAndSkc(DesignStyle designStyleEo, DesignStyleSourceTypeEnum sourceTypeEnum, String stylePicture) {
        designStyleRepository.save(designStyleEo);

        DesignStyleVersion designStyleVersion = new DesignStyleVersion();
        long designStyleVersionId = IdPool.getId();
        designStyleVersion.setDesignStyleVersionId(designStyleVersionId);
        BeanUtils.copyProperties(designStyleEo, designStyleVersion);
        designStyleVersionRepository.save(designStyleVersion);

        //自动创建一个正常打版的SKC
        NormalSkcCreateResp patternMakingCreateResp = prototypeService.normalSkcCreate(designStyleVersion, sourceTypeEnum, stylePicture);

        //发起skc创建MQ
        this.sendPrototypeIndexMq(sourceTypeEnum, designStyleVersion, patternMakingCreateResp);

        //封装返回对象
        return DesignStyleCreateResp.builder()
                .designStyleId(designStyleEo.getDesignStyleId())
                .styleCode(designStyleEo.getStyleCode())
                .versionNum(designStyleEo.getVersionNum())
                .prototypeId(patternMakingCreateResp.getPrototypeId())
                .designCode(patternMakingCreateResp.getDesignCode())
                .build();
    }

    private void sendPrototypeIndexMq(DesignStyleSourceTypeEnum sourceTypeEnum, DesignStyleVersion designStyleEo, NormalSkcCreateResp skcCreateResp) {
        DesignPrototypeMqDTO mqDTO = new DesignPrototypeMqDTO();
        mqDTO.setStyleCode(designStyleEo.getStyleCode());
        mqDTO.setPrototypeId(skcCreateResp.getPrototypeId());
        mqDTO.setDesignCode(skcCreateResp.getDesignCode());
        mqDTO.setSourceType(sourceTypeEnum.getCode());
        mqDTO.setStyleSeasonList(JSON.parseArray(designStyleEo.getStyleSeason(), OpsObject.class) );
        mqDTO.setWaveBandCode(designStyleEo.getWaveBandCode());
        mqDTO.setWaveBandName(designStyleEo.getWaveBandName());
        mqDTO.setSupplyModeName(designStyleEo.getSupplyModeName());
        mqDTO.setSupplyModeCode(designStyleEo.getSupplyModeCode());
        //这里写死  正常打版  上面也是正常创建一个正常打版
        mqDTO.setSkcType(SkcTypeEnum.NORMAL.getCode());
        mqDTO.setBizChannel(BizChannelEnum.NEW_JV.getCode());
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.DESIGN_PROTOTYPE_INDEX_SEND,
                DesignMqConstant.DESIGN_PROTOTYPE_INDEX_EXCHANGE,
                JSON.toJSONString(mqDTO));
        //发送消息
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DesignStyleImportResp importSpu(DesignStyleImportReq req) {
        log.info("===  导入SPU req:{} ===", JSON.toJSONString(req));
        DesignStyleSourceTypeEnum sourceTypeEnum = DesignStyleSourceTypeEnum.SPU_IMPORT;

        UserContent userContent = UserContentHolder.get();
        Long currentUserId = userContent.getCurrentUserId();

        //设计师才能创建SPU
        DesignerDTO designerDTO = this.checkDesigner(currentUserId);

        //构建SPU
        String styleCode = clothingCodeGenerateService.generateSpuCode(ClothingCodeSourceEnum.DESIGN);
        DesignStyle designStyleEo = DesignStyleConverter.buildSpuImportCreateEo(req, styleCode, currentUserId, designerDTO);
        designStyleEo.setSourceType(sourceTypeEnum.getCode());

        //创建spu与skc
        DesignStyleCreateResp styleCreateResp = this.saveSpuAndSkc(designStyleEo, sourceTypeEnum, req.getStylePicture());

        //发起款式识别
        this.addSpuIdentify(req, styleCreateResp);

        log.info("=== 导入SPU 创建SPU与SKC成功-返回:{} ===", JSON.toJSONString(styleCreateResp));
        return DesignStyleImportResp.builder().categoryName(req.getCategoryName()).styleCode(styleCode).build();
    }

    /**
     * 添加图片识别任务
     */
    private void addSpuIdentify(DesignStyleImportReq req, DesignStyleCreateResp spuCreateResp) {

        SpuIdentifyAddReq identifyAddInfo = SpuIdentifyAddReq.builder()
                .sourceType(SpuIdentifySourceEnum.DESIGN_STYLE.getCode())
                .bizId(spuCreateResp.getDesignStyleId()).bizCode(spuCreateResp.getStyleCode())
                .identifyUrl(req.getStylePicture())
                .build();

        spuIdentifyService.batchAddAsync(Collections.singletonList(identifyAddInfo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DesignStyleImportResp> excelImport(InputStream inputStream) {
        //读取excel
        List<DesignStyleImportData> reqList =  EasyExcel.read(inputStream)
                .head(DesignStyleImportData.class)
                .sheet()
                .doReadSync();
        log.info("=== excel导入SPU reqList:{} ===", JSON.toJSONString(reqList));
        SdpDesignException.notEmpty(reqList, "导入数据为空!");
        SdpDesignException.isTrue(reqList.size() <= 100, "最多导入100条记录");

        // 图片URL转存
        for (DesignStyleImportData row : reqList) {
            String stylePicture = row.getStylePicture();
            if (StringUtils.isBlank(stylePicture)) {
                throw new BusinessException("款式图片URL不能为空");
            }
            String transferred = ossHelper.transferFrom(stylePicture);
            row.setStylePicture(transferred);
        }
        
        //品类映射校验与设值
        this.checkExcelImportSpu(reqList);

        DesignStyleSourceTypeEnum sourceTypeEnum = DesignStyleSourceTypeEnum.SPU_IMPORT_EXCEL;
        UserContent userContent = UserContentHolder.get();
        Long currentUserId = userContent.getCurrentUserId();

        //设计师才能创建SPU
        DesignerDTO designerDTO = this.checkDesigner(currentUserId);

        //新增spu
        List<DesignStyle> designStyleList = new ArrayList<>(reqList.size());
        List<DesignStyleVersion> designStyleVersionList = new ArrayList<>(reqList.size());
        Map<String, String> stylePictureMap = new HashMap<>(reqList.size());
        this.buildExcelImportSpu(reqList, currentUserId, designerDTO, sourceTypeEnum, designStyleList, designStyleVersionList, stylePictureMap);
        designStyleRepository.saveBatch(designStyleList);
        designStyleVersionRepository.saveBatch(designStyleVersionList);

        //自动创建一个正常打版的SKC
        List<NormalSkcCreateResp> skcCreateRespList = prototypeService.normalSkcBatchCreate(designStyleVersionList, sourceTypeEnum, stylePictureMap);
        Map<String, NormalSkcCreateResp> skcCreateMap = StreamUtil.list2Map(skcCreateRespList, NormalSkcCreateResp::getStyleCode);

        //发起款式识别
        List<SpuIdentifyAddReq> identifyAddInfoList = new ArrayList<>(reqList.size());
        skcCreateRespList.forEach(item -> {
            String stylePicture = stylePictureMap.get(item.getStyleCode());
            if (StrUtil.isBlank(stylePicture)) {
                return;
            }
            identifyAddInfoList.add(SpuIdentifyAddReq.builder()
                    .sourceType(SpuIdentifySourceEnum.DESIGN_STYLE.getCode())
                    .bizId(item.getPrototypeId()).bizCode(item.getStyleCode())
                    .identifyUrl(stylePicture)
                    .build());
        });
        spuIdentifyService.batchAddAsync(identifyAddInfoList);

        //发起skc创建MQ
        designStyleVersionList.forEach(designStyleVersion -> {
            NormalSkcCreateResp skcCreateResp = skcCreateMap.get(designStyleVersion.getStyleCode());
            if (Objects.isNull(skcCreateResp)) {
                return;
            }
            this.sendPrototypeIndexMq(sourceTypeEnum, designStyleVersion, skcCreateResp);
        });

        List<DesignStyleImportResp> respList = designStyleList.stream()
                .map(designStyle -> DesignStyleImportResp.builder()
                        .styleCode(designStyle.getStyleCode())
                        .categoryName(designStyle.getCategoryName())
                        .build())
                .collect(Collectors.toList());
        log.info("=== excel导入SPU respList:{} ===", JSON.toJSONString(respList));
        return respList;
    }

    private void checkExcelImportSpu(List<DesignStyleImportData> reqList) {
        //查询品类映射Map
        Map<String, String> categoryMap = this.getCategoryDictMap();

        //校验与品类匹配
        reqList.forEach(item -> {
            SdpDesignException.notBlank(item.getStylePicture(), "款式图片不能为空");
            SdpDesignException.notBlank(item.getCategoryName(), "品类名称不能为空");
            String categoryCode = categoryMap.get(item.getCategoryName());
            SdpDesignException.notBlank(categoryCode, "品类错误, 编码映射失败! {}", item.getCategoryName());
            item.setCategory(categoryCode);
        });
    }

    private void buildExcelImportSpu(List<DesignStyleImportData> reqList, Long currentUserId, DesignerDTO designerDTO,
                                     DesignStyleSourceTypeEnum sourceTypeEnum, List<DesignStyle> designStyleList,
                                     List<DesignStyleVersion> designStyleVersionList, Map<String, String> stylePictureMap) {
        reqList.forEach(item -> {
            DesignStyleImportReq importReq = DesignStyleImportReq.builder()
                    .categoryName(item.getCategoryName()).category(item.getCategory()).stylePicture(item.getStylePicture())
                    .build();
            //构建SPU
            String styleCode = clothingCodeGenerateService.generateSpuCode(ClothingCodeSourceEnum.DESIGN);
            DesignStyle designStyleEo = DesignStyleConverter.buildSpuImportCreateEo(importReq, styleCode, currentUserId, designerDTO);
            designStyleEo.setSourceType(sourceTypeEnum.getCode());
            designStyleList.add(designStyleEo);

            DesignStyleVersion designStyleVersion = new DesignStyleVersion();
            long designStyleVersionId = IdPool.getId();
            designStyleVersion.setDesignStyleVersionId(designStyleVersionId);
            BeanUtils.copyProperties(designStyleEo, designStyleVersion);
            designStyleVersionList.add(designStyleVersion);

            //款式图片
            stylePictureMap.put(styleCode, item.getStylePicture());
        });
    }


    /**
     * 获取品类Map<名称,编码>
     */
    private Map<String, String> getCategoryDictMap() {
        List<DictVo> dictResp = dictValueRemoteHelper.listByDictCodes(Collections.singletonList(DictConstant.CLOTHING_CATEGORY));
        Map<String, DictVo> dictValueMap = dictResp.stream().collect(Collectors.toMap(DictVo::getDictCode, Function.identity(), (k1, k2) -> k1));
        DictVo categoryDictVo = dictValueMap.get(DictConstant.CLOTHING_CATEGORY);
        return DictTreeConverter.convertToMap(categoryDictVo, "-");
    }


    private String getPlatformName(String storeName) {
        if (StrUtil.isBlank(storeName)) {
            return null;
        }

        List<ShopResp> shopList = popProductHelper.getShopList(Collections.singletonList(storeName));
        if (CollUtil.isEmpty(shopList)) {
            return null;
        }

        return shopList.getFirst().getPlatformName();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DesignStyleCreateResp createFromDemand(DesignDemand designDemand, String platformName) {
        SdpDesignException.notNull(designDemand, "灵感设计需求为空!");

        //灵感需求创建spu,skc
        DesignStyleCreateReq createReq = DesignStyleConverter.buildDesignStyleCreateReq4Demand(designDemand, platformName);
        return this.createSpuSkc(createReq);

        /*
        //新增款式识别
        spuIdentifyService.add4DesignDemand(designDemand, spuSkcCreateResp);

        return spuSkcCreateResp;
         */
    }

    private static DesignStyleCreateReq buildDesignStyleCreateReq4Demand(DesignDemand designDemand, String platformName) {
        DesignStyleCreateReq createReq = new DesignStyleCreateReq();
        createReq.setSourceType(DesignStyleSourceTypeEnum.DESIGN_DEMAND.getCode());
        createReq.setDesignDemandId(designDemand.getDesignDemandId());
        //供给方式, 波段, 建议售价, 货盘类型, 国家, 店铺, 平台, 元素, 选款人(买手) 添加到spu中
        createReq.setSupplyModeCode(designDemand.getSupplyModeCode());
        createReq.setSupplyModeName(designDemand.getSupplyModeName());
        createReq.setSuggestedSellingPrice(designDemand.getSellingPrice());
        //如果是仿款, 建议售价取期望成本
        if (Objects.equals(designDemand.getSupplyModeName(), SdpDesignConstant.COPY_STYLE_NAME)) {
            createReq.setSuggestedSellingPrice(designDemand.getExpectedCostPrice());
        }
        createReq.setPalletTypeCode(designDemand.getPalletTypeCode());
        createReq.setPalletTypeName(designDemand.getPalletTypeName());
        createReq.setWaveBandCode(designDemand.getWaveBandCode());
        createReq.setWaveBandName(designDemand.getWaveBandName());
        createReq.setCountrySiteCode(designDemand.getCountrySiteCode());
        createReq.setCountrySiteName(designDemand.getCountrySiteName());
        createReq.setStoreId(designDemand.getStoreId());
        createReq.setStoreName(designDemand.getStoreName());
        createReq.setPlatformName(platformName);
        createReq.setInspirationImageSource(designDemand.getInspirationImageSource());
        createReq.setInspirationImageSourceCode(designDemand.getInspirationImageSourceCode());
        createReq.setInspirationBrand(designDemand.getInspirationBrand());
        createReq.setInspirationBrandCode(designDemand.getInspirationBrandCode());
        createReq.setProductThemeCode(designDemand.getProductThemeCode());
        createReq.setProductThemeName(designDemand.getProductThemeName());
        createReq.setElementCode(designDemand.getElementCode());
        createReq.setElementName(designDemand.getElementName());
        createReq.setBuyerId(designDemand.getChosenId());
        createReq.setBuyerName(designDemand.getChosenName());
        createReq.setPlanningType(designDemand.getPlanningType());
        createReq.setMarketCode(designDemand.getMarketCode());
        createReq.setMarketSeriesCode(designDemand.getMarketSeriesCode());
        return createReq;
    }


    private void checkCreateSpuSkc(DesignStyleCreateReq req, DesignStyleSourceTypeEnum sourceTypeEnum) {
        SdpDesignException.notNull(sourceTypeEnum, "款来源不正确! sourceType:{}", req.getSourceType());
        //判断校验款式品类必须有四级
        if (Objects.equals(sourceTypeEnum, DesignStyleSourceTypeEnum.DESIGN_DEMAND)) {
            return;
        }
        this.validateCategory(req.getCategory(), req.getCategoryName());
    }

    private void validateCategory(String category, String categoryName) {
        SdpDesignException.notBlank(category, "款式品类编码不能为空");
        SdpDesignException.notBlank(categoryName, "款式品类名不能为空");

        // List<String> categoryCodeList = StrUtil.splitTrim(category, StrUtil.DASHED);
        // SdpDesignException.notEmpty(categoryCodeList, "款式品类格式不对");
        // SdpDesignException.isTrue(categoryCodeList.size() >= 4, "款式名称必传,且必须有四级");
        //
        // List<String> categoryNameList = StrUtil.splitTrim(categoryName, StrUtil.DASHED);
        // SdpDesignException.notEmpty(categoryNameList, "款式品类格式不对");
        // SdpDesignException.isTrue(categoryNameList.size() >= 4, "款式名称必传,且必须有四级");
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpu(DesignStyleUpdateReq req) {
        log.info("=== 编辑SPU-入参:{} ===", JSON.toJSONString(req));
        //1,校验
        DesignStyle designStyle = designStyleRepository.getById(req.getDesignStyleId());
        String styleCode = designStyle.getStyleCode();
        SdpDesignException.notNull(req.getSizeStandard(),"标准尺码不能为空");
        SdpDesignException.notNull(req.getSizeStandardCode(),"标准尺码不能为空");
        this.checkUpdateSpu(req, designStyle);

        //spu下的skc
        List<Prototype> skcList = prototypeRepository.listByStyleCode(styleCode);

        UserContent userContent = UserContentHolder.get();
        SdpDesignException.notNull(userContent, "用户信息获取失败, 请先登陆! ");

        LocalDateTime now = LocalDateTime.now();
        Integer newVersionNum = designStyle.getVersionNum() + 1;

        //2,更新design_spu表, 要更新可以为null的字段: 参考链接;
        //注: 编辑spu不会更新设计师信息
        DesignStyleUpdateDto updateDto = DesignStyleConverter.buildDesignStyleUpdateDto(req, styleCode, newVersionNum, now);
        designStyleRepository.updateSpuInfo(updateDto, userContent);

        //3,升版本,归档SPU(design_spu_version表)
        long designStyleVersionId = IdPool.getId();
        DesignStyleVersion designStyleVersionEo = new DesignStyleVersion();
        designStyleVersionEo.setDesignStyleVersionId(designStyleVersionId);
        BeanUtils.copyProperties(designStyle, designStyleVersionEo);
        BeanUtils.copyProperties(updateDto, designStyleVersionEo);
        designStyleVersionRepository.save(designStyleVersionEo);

        //4,根据spuCode更新所有prototype表与prototype_history表中SPU维度的信息
        prototypeService.updateSpuInfoWithinHistory(updateDto);

        //视觉需求spu更新
        visualSpuService.updateSpu(styleCode, SdpStyleTypeEnum.DESIGN, null);

        //有已提交的且bizChannel为3的skc则同步致景
        if (CollUtil.isNotEmpty(skcList)) {
            Prototype submitSkc = skcList.stream()
                    .filter(item -> Objects.equals(item.getPrototypeStatus(), PrototypeStatusEnum.DECOMPOSED.getCode())
                            && Objects.equals(item.getBizChannel(), BizChannelEnum.NEW_JV.getCode()))
                    .findFirst().orElse(null);
            if (Objects.nonNull(submitSkc)) {
                //推送SPU与SKC信息到致景(skc可以重复推送, 版本一致时致景不处理)
                PushSpuSkc2ZjReq spuSkc2ZjReq = new PushSpuSkc2ZjReq();
                spuSkc2ZjReq.setDesignCodeList(Collections.singletonList(submitSkc.getDesignCode()));
                spuSkc2ZjReq.setPushZjTypeEnum(SpuSkcPushTypeEnum.SPU);
                this.pushSpuSkc2Zj(spuSkc2ZjReq);
            }
        }

        //6.同步SPU信息
        List<String> skcListBySpu = StreamUtil.convertListAndDistinct(skcList, Prototype::getDesignCode);
        this.sendSpuUpdateMq(req, styleCode, skcListBySpu);

        //品类/风格更新推送运营平台
        DesignStyle newSpu = designStyleRepository.getByStyleCode(styleCode);
        this.categoryStyleUpdate2Pop(designStyle, newSpu);

        //商品属性修改
        this.updateProductAttribute2Pop(designStyle, newSpu);
        //SPU基础信息的修改
        this.updateSpuBaseInfo2Pop(newSpu);
        log.info("=== 编辑SPU-成功: styleCode:{}; versionNum:{}; spuId:{} ===", designStyle.getStyleCode(), newVersionNum, designStyle.getDesignStyleId());
    }

    private void categoryStyleUpdate2Pop(DesignStyle oldSpu, DesignStyle newSpu) {
        //品类/风格 更新推送运营平台
        PopCategoryStyleUpdateTypeEnum updateTypeEnum = null;
        if (!Objects.equals(oldSpu.getCategory(), newSpu.getCategory())
                && !Objects.equals(oldSpu.getClothingStyleCode(), newSpu.getClothingStyleCode())) {
            updateTypeEnum = PopCategoryStyleUpdateTypeEnum.CATEGORY_AND_STYLE;
        }else {
            if (!Objects.equals(oldSpu.getCategory(), newSpu.getCategory())) {
                updateTypeEnum = PopCategoryStyleUpdateTypeEnum.CATEGORY;
            }else if (!Objects.equals(oldSpu.getClothingStyleCode(), newSpu.getClothingStyleCode())
                    ||!Objects.equals(oldSpu.getPlanningType(), newSpu.getPlanningType())){
                updateTypeEnum = PopCategoryStyleUpdateTypeEnum.STYLE;
            }
        }
        if (Objects.nonNull(updateTypeEnum)) {
            popProductHelper.updateDesignCategoryAndStyle(newSpu, updateTypeEnum);
        }
    }

    private void sendSpuUpdateMq(DesignStyleUpdateReq req, String styleCode, List<String> skcListBySpu) {
        DesignStyleUpdateDTO designStyleUpdateDTO = new DesignStyleUpdateDTO();
        designStyleUpdateDTO.setStyleCode(styleCode);
        designStyleUpdateDTO.setDesignCodes(skcListBySpu);
        designStyleUpdateDTO.setCategory(req.getCategory());
        designStyleUpdateDTO.setCategoryName(req.getCategoryName());
        designStyleUpdateDTO.setWaveBandCode(req.getWaveBandCode());
        designStyleUpdateDTO.setWaveBandName(req.getWaveBandName());

        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.SPU_UPDATE,
                DesignMqConstant.DESIGN_SPU_UPDATE_EXCHANGE,
                JSON.toJSONString(designStyleUpdateDTO));
        //发送消息
        log.info("===发送mq消息-编辑SPU: mqMessageReq: {} ===", JSON.toJSONString(mqMessageReq));
        mqProducer.sendOnAfterCommit(mqMessageReq);
    }

    private void checkUpdateSpu(DesignStyleUpdateReq req, DesignStyle designStyle) {
        SdpDesignException.notNull(designStyle, "SPU不存在! ");
        SdpDesignException.isTrue(Objects.equals(designStyle.getVersionNum(), req.getVersionNum()),
                "当前SPU已更新, 请刷新获取最新版本SPU信息后再编辑! ");

        //判断校验款式品类必须有四级
        this.validateCategory(req.getCategory(), req.getCategoryName());

        //当SPU下有已提交的SKC,不允许再次编辑尺码标准
        if (!Objects.equals(req.getSizeStandardCode(), designStyle.getSizeStandardCode())) {
            List<Prototype> prototypeList = prototypeRepository.listByStyleCode(designStyle.getStyleCode());
            if (CollUtil.isNotEmpty(prototypeList)) {
                Prototype prototype = prototypeList.stream()
                        .filter(item -> Objects.equals(PrototypeStatusEnum.DECOMPOSED.getCode(), item.getPrototypeStatus()))
                        .findFirst().orElse(null);
                if (Objects.nonNull(prototype)) {
                    throw new SdpDesignException("尺码标准不能修改, skc已提交:" + prototype.getDesignCode());
                }
            }
        }
        //店铺校验
        if (StrUtil.isNotBlank(req.getStoreName())) {
            List<ShopResp> shopList = popProductHelper.getShopList(Collections.singletonList(req.getStoreName()));
            SdpDesignException.notEmpty(shopList, "店铺不存在! {}", req.getStoreName());
            req.setPlatformName(shopList.getFirst().getPlatformName());
        }
    }

    @Override
    public Map<String, DesignStyle> mapDesignStyleByStyleCode(Set<String> styleCodes) {
        return CommonUtil.getMapByQuery(designStyleRepository::listByStyleCodes, styleCodes, DesignStyle::getStyleCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DesignStyleCreateOpenV2Resp pushSpuSkc2Zj(PushSpuSkc2ZjReq req) {
        log.info("=== 款信息推送致景-req: {} === ", JSON.toJSONString(req));
        //查询skc信息
        List<String> designCodeList = req.getDesignCodeList();
        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
        SdpDesignException.notEmpty(prototypeList, "设计款不存在!");

        List<Long> prototypeIdList = StreamUtil.convertListAndDistinct(prototypeList, Prototype::getPrototypeId);
        List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIdList);
        Map<Long, PrototypeDetail> prototypeDetailMap = StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);

        //查询过滤后skc对应spu信息
        List<String> styleCodeList = StreamUtil.convertListAndDistinct(prototypeList, Prototype::getStyleCode);
        List<DesignStyleVo> styleVoList = this.getDesignStyleVoList(styleCodeList);
        Map<String, DesignStyleVo> styleVoMap = StreamUtil.list2Map(styleVoList, DesignStyleVo::getStyleCode);

        UserContent userContent = UserContentHolder.get();
        //封装推送款式给致景的入参
        List<DesignStyleCreateOpenV2Req.DesignStyleCreateInfo> createReqList = new ArrayList<>();
        prototypeList.forEach(skc -> {
            //skc已提交, 未取消, 颜色不为空
            SdpDesignException.isTrue(Objects.equals(skc.getPrototypeStatus(), PrototypeStatusEnum.DECOMPOSED.getCode()), "skc未提交! {}", skc.getDesignCode());
            SdpDesignException.isFalse(skc.getIsCanceled(), "skc已取消! {}", skc.getDesignCode());
            SdpDesignException.isTrue(StrUtil.isNotBlank(skc.getColor()), "skc颜色为空! {}", skc.getDesignCode());
            //skc对应的spu
            DesignStyleVo styleVo = styleVoMap.get(skc.getStyleCode());
            SdpDesignException.notNull(styleVo, "spu信息不存在, skc:{}", skc.getDesignCode());
            //SPU信息
            DesignStyleCreateOpenV2Req.DesignStyleCreateInfo createInfo = new DesignStyleCreateOpenV2Req.DesignStyleCreateInfo();
            // 渠道信息
            createInfo.setExtDesignCode(skc.getDesignCode());
            createInfo.setExtPrototypeId(skc.getPrototypeId());
            createInfo.setExtStyleCode(styleVo.getStyleCode());
            createInfo.setExtDesignStyleId(styleVo.getDesignStyleId());
            createInfo.setDesignVersionNum(styleVo.getVersionNum());
            createInfo.setExtDesignStyleVersionId(skc.getDesignStyleVersionId());
            createInfo.setPrototypeVersionNum(skc.getVersionNum());
            createInfo.setCreatorName(userContent.getCurrentUserName());
            //基础信息(都没有)
            //设计师信息
            createInfo.setDesignerName(skc.getDesignerName());
            //spu款式信息
            createInfo.setCategory(styleVo.getCategory());
            createInfo.setCategoryName(styleVo.getCategoryName());
            createInfo.setWeaveModeCode(styleVo.getWeaveModeCode());
            createInfo.setWeaveMode(styleVo.getWeaveMode());
            createInfo.setQualityLevel(styleVo.getQualityLevel());
            createInfo.setQualityLevelCode(styleVo.getQualityLevelCode());
            createInfo.setSizeStandardCode(styleVo.getSizeStandardCode());
            createInfo.setSizeStandard(styleVo.getSizeStandard());
            createInfo.setStyleSeasonList(styleVo.getStyleSeasonList());
            createInfo.setWaveBandCode(styleVo.getWaveBandCode());
            createInfo.setWaveBandName(styleVo.getWaveBandName());
            createInfo.setClothingStyle(styleVo.getClothingStyleName());
            createInfo.setBizChannel(styleVo.getBizChannel());

            //SKC信息
            this.setSckInfo2Zj(prototypeDetailMap, skc, createInfo);

            createReqList.add(createInfo);
        });

        //推送给致景
        DesignStyleCreateOpenV2Resp response = zjDesignRemoteHelper.pushSpuSkc2Zj(createReqList);

        //记录推送标识
        List<String> spuCodeList = StreamUtil.convertListAndDistinct(prototypeList, Prototype::getStyleCode);
        Map<String, PushZjLog> spuPushMap = StreamUtil.list2Map(pushZjLogRepository.listByBizCodesAndType(spuCodeList,
                        Collections.singletonList(PushZjTypeEnum.SPU.getCode())), PushZjLog::getBizCode);
        Map<Long, Prototype> prototypeMap = StreamUtil.list2Map(prototypeList, Prototype::getPrototypeId);
        List<DesignStyleCreateOpenV2Resp.DesignStyleInfoMapping> responseList = response.getDesignStyleInfoMappingList();
        if (CollUtil.isNotEmpty(responseList)) {
            responseList.forEach(item -> {
                Prototype prototype = prototypeMap.get(item.getExtPrototypeId());
                if (Objects.isNull(prototype)) {
                    return;
                }
                //spu推送记录
                if (Objects.isNull(spuPushMap.get(prototype.getStyleCode()))) {
                    DesignStyleVo spuVo = styleVoMap.get(prototype.getStyleCode());
                    pushZjLogService.create(PushZjLogReq.builder()
                            .pushType(PushZjTypeEnum.SPU.getCode())
                            .bizId(spuVo.getDesignStyleId())
                            .bizCode(spuVo.getStyleCode())
                            .zjBizId(item.getDesignStyleId())
                            .zjBizCode(item.getStyleCode())
                            .build());
                }
                //skc推送记录
                if (Objects.equals(req.getPushZjTypeEnum(), SpuSkcPushTypeEnum.SPU_SKC) || Objects.equals(req.getPushZjTypeEnum(), SpuSkcPushTypeEnum.SKC)) {
                    pushZjLogService.create(PushZjLogReq.builder()
                            .pushType(PushZjTypeEnum.SKC.getCode())
                            .bizId(prototype.getPrototypeId())
                            .bizCode(prototype.getDesignCode())
                            .zjBizId(item.getPrototypeId())
                            .zjBizCode(item.getDesignCode())
                            .build());
                }
            });
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String forcePushSpuSkc2Zj(PushSpuSkc2ZjReq req) {
        //查询skc信息
        List<String> designCodeList = req.getDesignCodeList();
        List<Prototype> prototypeList = prototypeRepository.listByDesignCodes(designCodeList);
        SdpDesignException.notEmpty(prototypeList, "设计款不存在!");

        //只推已提交的skc
        List<Prototype> pushSkcList = prototypeList.stream()
                .filter(item -> Objects.equals(item.getPrototypeStatus(), PrototypeStatusEnum.DECOMPOSED.getCode()))
                .collect(Collectors.toList());
        SdpDesignException.notEmpty(pushSkcList, "skc都未提交,不推送!");


        List<Long> prototypeIdList = StreamUtil.convertListAndDistinct(pushSkcList, Prototype::getPrototypeId);
        List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.getListByPrototypeIds(prototypeIdList);
        Map<Long, PrototypeDetail> prototypeDetailMap = StreamUtil.list2Map(prototypeDetailList, PrototypeDetail::getPrototypeId);

        //查询skc对应spu信息
        List<String> styleCodeList = StreamUtil.convertListAndDistinct(pushSkcList, Prototype::getStyleCode);
        List<DesignStyleVo> styleVoList = this.getDesignStyleVoList(styleCodeList);
        Map<String, DesignStyleVo> styleVoMap = StreamUtil.list2Map(styleVoList, DesignStyleVo::getStyleCode);

        //封装推送款式给致景的入参
        List<DesignStyleCreateOpenV2Req.DesignStyleCreateInfo> createReqList = new ArrayList<>();
        pushSkcList.forEach(skc -> {
            //skc对应的spu
            DesignStyleVo styleVo = styleVoMap.get(skc.getStyleCode());
            SdpDesignException.notNull(styleVo, "spu信息不存在, skc:{}", skc.getDesignCode());
            //SPU信息
            DesignStyleCreateOpenV2Req.DesignStyleCreateInfo createInfo = new DesignStyleCreateOpenV2Req.DesignStyleCreateInfo();
            // 渠道信息
            createInfo.setExtDesignCode(skc.getDesignCode());
            createInfo.setExtPrototypeId(skc.getPrototypeId());
            createInfo.setExtStyleCode(styleVo.getStyleCode());
            createInfo.setExtDesignStyleId(styleVo.getDesignStyleId());
            createInfo.setDesignVersionNum(styleVo.getVersionNum());
            createInfo.setExtDesignStyleVersionId(skc.getDesignStyleVersionId());
            createInfo.setPrototypeVersionNum(skc.getVersionNum());
            createInfo.setCreatorName(skc.getDesignerName());
            //基础信息(都没有)
            //设计师信息
            createInfo.setDesignerName(skc.getDesignerName());
            //spu款式信息
            createInfo.setCategory(styleVo.getCategory());
            createInfo.setCategoryName(styleVo.getCategoryName());
            createInfo.setWeaveModeCode(styleVo.getWeaveModeCode());
            createInfo.setWeaveMode(styleVo.getWeaveMode());
            createInfo.setQualityLevel(styleVo.getQualityLevel());
            createInfo.setQualityLevelCode(styleVo.getQualityLevelCode());
            createInfo.setSizeStandardCode(styleVo.getSizeStandardCode());
            createInfo.setSizeStandard(styleVo.getSizeStandard());
            createInfo.setStyleSeasonList(styleVo.getStyleSeasonList());
            createInfo.setWaveBandCode(styleVo.getWaveBandCode());
            createInfo.setWaveBandName(styleVo.getWaveBandName());
            createInfo.setClothingStyle(styleVo.getClothingStyleName());
            createInfo.setBizChannel(styleVo.getBizChannel());
            //SKC信息
            this.setSckInfo2Zj(prototypeDetailMap, skc, createInfo);

            createReqList.add(createInfo);
        });

        //推送给致景
        DesignStyleCreateOpenV2Resp response = zjDesignRemoteHelper.pushSpuSkc2Zj(createReqList);
        return JSON.toJSONString(response);
    }


    @Override
    public PopProductImageChangeStateDto listPopProductPicture(SpuProductImgReq req) {
        ProductImageChangeStateDto imageDto = popProductHelper.batchQueryImages(req.getSpuSet());

        PopProductImageChangeStateDto stateDto = new PopProductImageChangeStateDto();
        if (CollUtil.isEmpty(imageDto.getDataList())) {
            return stateDto;
        }
        List<PopProductImageChangeStateDto.Data> dataList = StreamUtil.convertList(imageDto.getDataList(), item -> {
            PopProductImageChangeStateDto.Data data = new PopProductImageChangeStateDto.Data();
            BeanUtils.copyProperties(item, data);
            List<PopProductImageChangeStateDto.Data.Skc> skcDataList = StreamUtil.convertList(item.getSkcList(), skc -> {
                PopProductImageChangeStateDto.Data.Skc skcData = new PopProductImageChangeStateDto.Data.Skc();
                BeanUtils.copyProperties(skc, skcData);
                return skcData;
            });
            data.setSkcList(skcDataList);
            return data;
        });
        stateDto.setDataList(dataList);

        return stateDto;
    }

    private void setSckInfo2Zj(Map<Long, PrototypeDetail> prototypeDetailMap, Prototype skc, DesignStyleCreateOpenV2Req.DesignStyleCreateInfo createInfo) {
        createInfo.setPrototypeVersionNum(skc.getVersionNum());
        createInfo.setExtDesignCode(skc.getDesignCode());
        createInfo.setExtPrototypeId(skc.getPrototypeId());
        createInfo.setExtDesignStyleVersionId(skc.getDesignStyleVersionId());
        //设计师要取skc的信息
        createInfo.setDesignerName(skc.getDesignerName());

        //skc详情
        PrototypeDetail prototypeDetail = prototypeDetailMap.get(skc.getPrototypeId());
        SdpDesignException.notNull(prototypeDetail, "skc详情不存在, skc:{}", skc.getDesignCode());
        List<ColorInfoVo> colorInfoList = prototypeDetail.getColorInfoList();
        if (CollUtil.isNotEmpty(colorInfoList)) {
            List<DesignStyleCreateOpenV2Req.ColorInfo> colorList = colorInfoList.stream().map(item -> {
                DesignStyleCreateOpenV2Req.ColorInfo colorInfo = new DesignStyleCreateOpenV2Req.ColorInfo();
                colorInfo.setColor(item.getColor());
                colorInfo.setColorNumber(item.getColorNumber());
                return colorInfo;
            }).toList();
            createInfo.setColorList(colorList);
        }

        createInfo.setSampleSize(prototypeDetail.getSampleSize());
        if (StringUtils.isNotBlank(prototypeDetail.getDesignPicture())) {
            List<String> designPicture = StrUtil.splitTrim(prototypeDetail.getDesignPicture(), StrUtil.COMMA);
            createInfo.setDesignPicture(designPicture);
        }
        createInfo.setRemark(prototypeDetail.getRemark());
        createInfo.setIsSplicing(prototypeDetail.getIsSplicing());
    }

    /**
     * 刷新历史数据
     * @param file
     */
    @Override
    public void initHisData(MultipartFile file){
        List<StyleHisDataUploadExcelDto> uploadDtoList = new ArrayList<>();
        try {
            uploadDtoList = EasyExcel.read(file.getInputStream())
                    .head(StyleHisDataUploadExcelDto.class)
                    .sheet()
                    .doReadSync();

        } catch (IOException e) {
            log.error("导入失败", e);
            throw new BusinessException("导入失败", e);
        }
        if (CollectionUtil.isEmpty(uploadDtoList)) {
            throw new BusinessException("导入数据为空");
        }
        Map<String, StyleHisDataUploadExcelDto> spuMap = uploadDtoList.stream().collect(Collectors.toMap(StyleHisDataUploadExcelDto::getSpuCode, t -> t));
        List<String> spuCodeList = uploadDtoList.stream().map(StyleHisDataUploadExcelDto::getSpuCode).distinct().toList();
        List<DesignStyle> designStyleList = designStyleRepository.listByStyleCodes(spuCodeList);
        Assert.isTrue(CollectionUtil.isNotEmpty(designStyleList), "未找到对应的款式");
        // 市场风格
        Map<String, String> marketMap = dictValueRemoteHelper.getMarketNameMap(1);
        Map<String, String> marketSeriesMap = dictValueRemoteHelper.getMarketNameMap(2);
        Map<String, String> marketStyleMap = dictValueRemoteHelper.getMarketNameMap(3);
        List<DesignStyle> updateSpuList = new ArrayList<>();
        for(DesignStyle designStyle : designStyleList){
            StyleHisDataUploadExcelDto excelDto = spuMap.get(designStyle.getStyleCode());
            if(excelDto == null){
                continue;
            }
            // TODO 市场使用业务给的，没有不导入 需确认
            if(excelDto.getMarketName() == null){
                continue;
            }
            String maretkCode = marketMap.get(excelDto.getMarketName());
            if(StrUtil.isBlank(maretkCode)){
                log.warn("未找到对应的市场spu：{},市场:{}", excelDto.getSpuCode(), excelDto.getMarketName());
                continue;
            }
            designStyle.setMarketCode(maretkCode);
            /**
             * 系列
             * 1、业务给了使用业务给的
             * 2、中东市场：【传统本士服饰-Classic 传统本士服饰-Glamour 新式本士服饰-Modern 国际货盘-Global】取末级，否则空
             * 3、欧美市场：从【风格】末级值取值，用-号拆分
             * 4：东南亚市场：拿场景
             */
            String seriesName = "";
            if(StrUtil.isNotEmpty(excelDto.getMarketSeriesName())){
                seriesName = excelDto.getMarketSeriesName();
            }else{
                List<String> zdPalletTypeName = List.of("传统本士服饰-Classic","传统本士服饰-Glamour","新式本士服饰-Modern","国际货盘-Global");
                if("中东".equals(excelDto.getMarketName()) && zdPalletTypeName.contains(designStyle.getPalletTypeName())){
                    seriesName =getLastPartAfterSplit(designStyle.getPalletTypeName(),'-');
                }else if("欧美".equals(excelDto.getMarketName()) && StrUtil.isNotBlank(designStyle.getClothingStyleName())){
                    seriesName = getLastPartAfterSplit(designStyle.getClothingStyleName(),'-');
                }else if ("东南亚".equals(excelDto.getMarketName()) && StrUtil.isNotBlank(designStyle.getSceneName())){
                    seriesName = designStyle.getSceneName();
                }
            }
            designStyle.setMarketSeriesCode(marketSeriesMap.get(excelDto.getMarketName() + "-"+ seriesName));
            if(StrUtil.isEmpty(designStyle.getMarketSeriesCode())){
                log.warn("未找到对应的系列：spu{},市场名称：{}，系列名称:{}", excelDto.getSpuCode(), excelDto.getMarketName(),seriesName);
            }
            /**
             * 风格
             * 1、中东：取原风格，删除AR-AR ，并转换为中文casual-休闲、elegant-优雅、luxury-华丽、romatic-浪漫、Boho-度假
             * 2、欧美：为空
             * 3、东南亚：取风格末级
             */
            if(StrUtil.isNotEmpty(designStyle.getMarketSeriesCode())){
                String marketStyleName = "";
                if("中东".equals(excelDto.getMarketName()) && StrUtil.isNotBlank(designStyle.getClothingStyleName())){
                    Map<String, String> styleMapping = new HashMap<>();
                    styleMapping.put("casual", "休闲");
                    styleMapping.put("elegant", "优雅");
                    styleMapping.put("luxury", "华丽");
                    styleMapping.put("romatic", "浪漫");
                    styleMapping.put("Boho", "度假");
                    marketStyleName = styleMapping.get(designStyle.getClothingStyleName().replace("AR-AR ", ""));
                }else if("欧美".equals(excelDto.getMarketName())){
                    marketStyleName = "";
                }else if("东南亚".equals(excelDto.getMarketName()) && StrUtil.isNotBlank(designStyle.getClothingStyleName())){
                    marketStyleName=getLastPartAfterSplit(designStyle.getClothingStyleName(),'-');
                }
                String marketStyleCode = marketStyleMap.get(excelDto.getMarketName().concat("-").concat(seriesName).concat("-").concat(marketStyleName));
                if(StrUtil.isNotBlank(marketStyleCode)){
                    designStyle.setClothingStyleName(marketStyleName);
                    designStyle.setClothingStyleCode(marketStyleCode);
                }
            }
            updateSpuList.add(designStyle);

        }
        designStyleRepository.updateBatchById(updateSpuList);
    }

    @Override
    public void initHisDataV2(MultipartFile file){
        List<StyleHisDataUploadExcelDto> uploadDtoList = new ArrayList<>();
        try {
            uploadDtoList = EasyExcel.read(file.getInputStream())
                    .head(StyleHisDataUploadExcelDto.class)
                    .sheet()
                    .doReadSync();

        } catch (IOException e) {
            log.error("导入失败", e);
            throw new BusinessException("导入失败", e);
        }
        if (CollectionUtil.isEmpty(uploadDtoList)) {
            throw new BusinessException("导入数据为空");
        }
        Map<String, StyleHisDataUploadExcelDto> spuMap = uploadDtoList.stream().collect(Collectors.toMap(StyleHisDataUploadExcelDto::getSpuCode, t -> t));
        List<String> spuCodeList = uploadDtoList.stream().map(StyleHisDataUploadExcelDto::getSpuCode).distinct().toList();
        List<DesignStyle> designStyleList = designStyleRepository.listByStyleCodes(spuCodeList);
        Assert.isTrue(CollectionUtil.isNotEmpty(designStyleList), "未找到对应的款式");
        // 市场风格
        Map<String, String> marketMap = dictValueRemoteHelper.getMarketNameMap(1);
        Map<String, String> marketSeriesMap = dictValueRemoteHelper.getMarketNameMap(2);
        Map<String, String> marketStyleMap = dictValueRemoteHelper.getMarketNameMap(3);
        List<DesignStyle> updateSpuList = new ArrayList<>();
        for(DesignStyle designStyle : designStyleList){
            StyleHisDataUploadExcelDto excelDto = spuMap.get(designStyle.getStyleCode());
            if(excelDto == null){
                continue;
            }
            if(StrUtil.isNotBlank(excelDto.getMarketName())){
                designStyle.setMarketCode(marketMap.get(excelDto.getMarketName()));
            }
            if (StrUtil.isNotBlank(excelDto.getMarketName()) && StrUtil.isNotBlank(excelDto.getMarketSeriesName())){
                designStyle.setMarketSeriesCode(marketSeriesMap.get(excelDto.getMarketName() + "-" + excelDto.getMarketSeriesName()));
            }
            if(StrUtil.isNotBlank(excelDto.getMarketName() ) && StrUtil.isNotBlank(excelDto.getMarketSeriesName()) && StrUtil.isNotBlank(excelDto.getMarketStyleName())){
                designStyle.setClothingStyleCode(marketStyleMap.get(excelDto.getMarketName() + "-" + excelDto.getMarketSeriesName() + "-" + excelDto.getMarketStyleName()));
                if(StrUtil.isNotBlank(designStyle.getClothingStyleCode())){
                    designStyle.setClothingStyleName(excelDto.getMarketStyleName());
                }
            }
            updateSpuList.add(designStyle);
        }

        designStyleRepository.updateBatchById(updateSpuList);
    }

    @Override
    public void pushAttribute2Pop(Set<String> styleCodeSet) {
        if (CollectionUtil.isEmpty(styleCodeSet)) {
            return;
        }

        //SPU信息
        List<DesignStyle> designStyles = designStyleRepository.listByStyleCodes(styleCodeSet);
        if (CollectionUtil.isEmpty(designStyles)) {
            log.info("pushAttribute2Pop designStyles isEmpty");
            return;
        }
        //SKC信息
        List<Prototype> prototypeList = prototypeRepository.listByStyleCodes(styleCodeSet.stream().toList(), PrototypeStatusEnum.DECOMPOSED.getCode(), Boolean.FALSE);
        Map<String, List<Prototype>> prototypeSpuMap = StreamUtil.groupingBy(prototypeList, Prototype::getStyleCode);

        designStyles.forEach(designStyle -> {
            //更新商品属性
            popProductHelper.updateProductAttribute(designStyle.getStyleCode(), designStyle.getAttributes());
            //更新SPU基础信息
            updateSpuBaseInfo2Pop(designStyle);
            //更新供货价
            popProductHelper.updateProductEstimateCheckPriceByDesignStyle(designStyle, prototypeSpuMap.getOrDefault(designStyle.getStyleCode(), Collections.emptyList()));
        });
    }

    public  String getLastPartAfterSplit(String input, char separator) {
        List<String> parts = StrUtil.split(input, separator);
        return parts.isEmpty() ? null : parts.getLast();
    }



    private void updateProductAttribute2Pop(DesignStyle oldSpu, DesignStyle newSpu) {
        //商品属性 更新推送运营平台
        if (Objects.isNull(oldSpu.getAttributes()) && Objects.isNull(newSpu.getAttributes())) {
            return;
        }

        if (Objects.isNull(newSpu.getAttributes())) {
            return;
        }

        if ((Objects.isNull(oldSpu.getAttributes()) && Objects.nonNull(newSpu.getAttributes())) ||
                (oldSpu.getAttributes().size() != newSpu.getAttributes().size()) ||
                !JSON.toJSONString(oldSpu.getAttributes()).equals(JSON.toJSONString(newSpu.getAttributes()))) {
            popProductHelper.updateProductAttribute(newSpu.getStyleCode(), newSpu.getAttributes());
        }
    }




    private void updateSpuBaseInfo2Pop(DesignStyle newSpu) {
        // 基本信息更新推送运营平台
        ProductUpdateMqDto.SpuBaseInfo spuBaseInfo = ProductUpdateMqDto.SpuBaseInfo.builder()
                .clothingStyleName(newSpu.getClothingStyleName())
                .clothingStyleCode(newSpu.getClothingStyleCode())
                .planningType(newSpu.getPlanningType())
                .marketCode(newSpu.getMarketCode())
                .marketSeriesCode(newSpu.getMarketSeriesCode())
                .waves(newSpu.getWaveBandName()) // 根据DesignStyle字段映射，可能需要调整
                .goodsRepType(newSpu.getPalletTypeName()) // 根据DesignStyle字段映射
                .weaveModeCode(newSpu.getWeaveModeCode())
                .weaveMode(newSpu.getWeaveMode())
                .qualityLevel(newSpu.getQualityLevel())
                .qualityLevelCode(newSpu.getQualityLevelCode())
                .productThemeCode(newSpu.getProductThemeCode())
                .productThemeName(newSpu.getProductThemeName())
                .shopId(newSpu.getStoreId()) // 字段名映射
                .shopName(newSpu.getStoreName()) // 字段名映射
                .countrys(Collections.singletonList(newSpu.getCountrySiteCode())) // 根据DesignStyle字段调整
                .spotType(null) // DesignStyle中没有直接对应字段
                .spotTypeCode(null) // DesignStyle中没有直接对应字段
                .buyerId(newSpu.getBuyerId())
                .buyerName(newSpu.getBuyerName())
                .productLink(newSpu.getReferLink()) // 根据DesignStyle字段映射
                .spuName(null) // DesignStyle中没有直接对应字段
                .spuNameTrans(null) // DesignStyle中没有直接对应字段
                .build();

        popProductHelper.updateProductBaseInfo(newSpu.getStyleCode(),spuBaseInfo);
    }




}
