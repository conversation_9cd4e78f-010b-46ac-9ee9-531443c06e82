package tech.tiangong.sdp.design.controller.web;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.ImageToVideoTaskService;
import tech.tiangong.sdp.design.vo.req.visual.BatchImageToVideoTaskReq;

import javax.validation.Valid;


/**
 * 图生视频任务记录表 Controller
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/image-to-video-task")
public class ImageToVideoTaskController extends BaseController {


    private final ImageToVideoTaskService imageToVideoTaskService;

    /**
     * 批量创建图生视频任务
     */
    @PostMapping("/batch-create")
    public DataResponse<Boolean> batchCreateTask(
            @RequestBody @Valid BatchImageToVideoTaskReq request) {
        return DataResponse.ok(imageToVideoTaskService.batchCreateImageToVideoTask(request));
    }


}
