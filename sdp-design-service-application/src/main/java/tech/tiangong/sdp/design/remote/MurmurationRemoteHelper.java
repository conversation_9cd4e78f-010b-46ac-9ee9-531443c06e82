package tech.tiangong.sdp.design.remote;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import team.aikero.blade.core.protocol.DataResponse;
import team.aikero.murmuration.common.req.task.*;
import team.aikero.murmuration.sdk.client.task.*;
import team.aikero.murmuration.common.enums.SearchDimension;
import team.aikero.murmuration.common.req.BatchSearchSimilarityRequest;
import team.aikero.murmuration.common.vo.SearchInfo;
import team.aikero.murmuration.common.vo.SearchSimilarityVo;
import team.aikero.murmuration.sdk.client.VectorClient;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.SdpDesignConstant;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Murmuration 任务客户端远程接口调用
 */
@Slf4j
@Service
@AllArgsConstructor
public class MurmurationRemoteHelper {
    private final AeAttributeConvertTaskClient aeAttributeConvertTaskClient;
    private final SkcColorIdentificationTaskClient skcColorIdentificationTaskClient;
    private final AlibabaDistributionTaskClient alibabaDistributionTaskClient;
    private final ImageCroppingTaskClient imageCroppingTaskClient;
    private final Text2VideoTaskClient text2VideoTaskClient;

    /**
     * 提交 AE 属性转换任务
     * 
     * @param bizId 业务ID
     * @param bizType 业务类型
     * @param request AE属性转换请求对象
     * @return 任务ID
     */
    public Long submitAeAttributeConvertTask(String bizId, String bizType, AeAttributeConvertRequest request) {
        Assert.hasText(bizId, "业务ID不能为空");
        Assert.hasText(bizType, "业务类型不能为空");
        Assert.notNull(request, "请求对象不能为空");
        
        log.info("【提交AE属性转换任务】开始，bizId={}, bizType={}, request={}", 
                bizId, bizType, JSON.toJSONString(request));
        
        try {
            DataResponse<Long> response = aeAttributeConvertTaskClient.createTask(bizId, bizType, request);
            
            if (!response.getSuccessful()) {
                throw new SdpDesignException("提交AE属性转换任务失败：" + response.getMessage());
            }

            Long taskId = response.getData();
            log.info("【提交AE属性转换任务】成功，taskId={}", taskId);

            return taskId;
        } catch (Exception e) {
            log.error("【提交AE属性转换任务】异常，bizId={}, bizType={}, error={}",
                    bizId, bizType, e.getMessage(), e);
            throw new SdpDesignException("提交AE属性转换任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提交 SKC 颜色识别任务
     *
     * @param bizId 业务ID
     * @param bizType 业务类型
     * @param request SKC颜色识别请求对象
     * @return 任务ID
     */
    public Long submitSkcColorIdentificationTask(String bizId, String bizType, SkcColorIdentificationRequest request) {
        Assert.hasText(bizId, "业务ID不能为空");
        Assert.hasText(bizType, "业务类型不能为空");
        Assert.notNull(request, "请求对象不能为空");

        log.info("【提交SKC颜色识别任务】开始，bizId={}, bizType={}, request={}",
                bizId, bizType, JSON.toJSONString(request));

        try {
            DataResponse<Long> response = skcColorIdentificationTaskClient.createTask(bizId, bizType, request);

            if (!response.getSuccessful()) {
                throw new SdpDesignException("提交SKC颜色识别任务失败：" + response.getMessage());
            }
            
            Long taskId = response.getData();
            log.info("【提交SKC颜色识别任务】成功，taskId={}", taskId);
            
            return taskId;
        } catch (Exception e) {
            log.error("【提交SKC颜色识别任务】异常，bizId={}, bizType={}, error={}", 
                    bizId, bizType, e.getMessage(), e);
            throw new SdpDesignException("提交SKC颜色识别任务失败: " + e.getMessage(), e);
        }
    }
    private final VectorClient vectorClient;

    /**
     * 提交 1688自动铺货图包任务
     *
     * @param bizId 业务ID
     * @param bizType 业务类型
     * @param request 1688自动铺货图包请求对象
     * @return 任务ID
     */
    public Long submitAlibabaDistributionTask(String bizId, String bizType, AlibabaDistributionRequest request) {
        Assert.hasText(bizId, "业务ID不能为空");
        Assert.hasText(bizType, "业务类型不能为空");
        Assert.notNull(request, "请求对象不能为空");

        log.info("【提交1688自动铺货图包识别任务】开始，bizId={}, bizType={}, request={}",
                bizId, bizType, JSON.toJSONString(request));

        try {
            DataResponse<Long> response = alibabaDistributionTaskClient.createTask(bizId, bizType, request);

            if (!response.getSuccessful()) {
                throw new SdpDesignException("提交1688自动铺货图包任务失败：" + response.getMessage());
            }

            Long taskId = response.getData();
            log.info("【提交1688自动铺货图包任务】成功，taskId={}", taskId);

            return taskId;
        } catch (Exception e) {
            log.error("【提交1688自动铺货图包任务】异常，bizId={}, bizType={}, error={}",
                    bizId, bizType, e.getMessage(), e);
            throw new SdpDesignException("提交1688自动铺货图包任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提交 AE 属性转换任务（使用统一的 bizType）
     *
     * @param bizId 业务ID
     * @param request AE属性转换请求对象
     * @return 任务ID
     */
    public Long submitAeAttributeConvertTask(String bizId, AeAttributeConvertRequest request) {
        return submitAeAttributeConvertTask(bizId, SdpDesignConstant.Murmuration.AE_ATTRIBUTE_CONVERT_BIZ_TYPE, request);
    }

    /**
     * 提交 SKC 颜色识别任务（使用统一的 bizType）
     *
     * @param bizId 业务ID
     * @param request SKC颜色识别请求对象
     * @return 任务ID
     */
    public Long submitSkcColorIdentificationTask(String bizId, SkcColorIdentificationRequest request) {
        return submitSkcColorIdentificationTask(bizId, SdpDesignConstant.Murmuration.SKC_COLOR_IDENTIFICATION_BIZ_TYPE, request);
    }

    /**
     * 提交 SKC 1688自动铺货图包任务（使用统一的 bizType）
     *
     * @param bizId 业务ID
     * @param request 1688自动铺货图包请求对象
     * @return 任务ID
     */
    public Long submitAlibabaDistributionTask(String bizId, AlibabaDistributionRequest request) {
        return submitAlibabaDistributionTask(bizId, SdpDesignConstant.Murmuration.ALIBABA_DISTRIBUTION_BIZ_TYPE, request);
    }


    /**
     * 提交智能裁剪任务
     * @param bizId
     * @param request
     * @return
     */
    public Long submitImageCroppingTask(String bizId,ImageCroppingRequest request){
        return submitImageCroppingTask(bizId,SdpDesignConstant.Murmuration.IMAGE_CROPPING_BIZ_TYPE, request);
    }


    /**
     * 提交图片生成视频任务
     * @param bizId
     * @param request
     * @return
     */
    public Long submitImageToVideoTask(String bizId,Text2VideoRequest request){
        return submitImageToVideoTask(bizId,SdpDesignConstant.Murmuration.IMAGE_TO_VIDEO_BIZ_TYPE, request);
    }


    /**
     * 提供图片裁剪任务
     * @param bizId
     * @param bizType
     * @param request
     * @return
     */
    public Long submitImageCroppingTask(String bizId, String bizType, ImageCroppingRequest request) {
        Assert.hasText(bizId, "业务ID不能为空");
        Assert.hasText(bizType, "业务类型不能为空");
        Assert.notNull(request, "请求对象不能为空");

        log.info("【提供图片裁剪任务】开始，bizId={}, bizType={}, request={}",
                bizId, bizType, JSON.toJSONString(request));

        try {
            DataResponse<Long> response = imageCroppingTaskClient.createTask(bizId, bizType, request);

            log.info("【提供图片裁剪任务】返回，taskId={}", JSON.toJSONString(response));
            if (!response.getSuccessful()) {
                throw new SdpDesignException("提供图片裁剪任务失败：" + response.getMessage());
            }

            Long taskId = response.getData();
            log.info("【提供图片裁剪任务】成功，taskId={}", taskId);

            return taskId;
        } catch (Exception e) {
            log.error("【提供图片裁剪任务】异常，bizId={}, bizType={}, error={}",
                    bizId, bizType, e.getMessage(), e);
            throw new SdpDesignException("提供图片裁剪任务失败: " + e.getMessage(), e);
        }
    }



    /**
     * 提供图片生成视频
     * @param bizId
     * @param bizType
     * @param request
     * @return
     */
    public Long submitImageToVideoTask(String bizId, String bizType, Text2VideoRequest request) {
        Assert.hasText(bizId, "业务ID不能为空");
        Assert.hasText(bizType, "业务类型不能为空");
        Assert.notNull(request, "请求对象不能为空");

        log.info("【提供图片生成视频】开始，bizId={}, bizType={}, request={}",
                bizId, bizType, JSON.toJSONString(request));

        try {
            DataResponse<Long> response = text2VideoTaskClient.createTask(bizId, bizType, request);

            log.info("【提供图片生成视频】返回，taskId={}", JSON.toJSONString(response));
            if (!response.getSuccessful()) {
                throw new SdpDesignException("提供图片生成视频失败：" + response.getMessage());
            }

            Long taskId = response.getData();
            log.info("【提供图片生成视频任务】成功，taskId={}", taskId);

            return taskId;
        } catch (Exception e) {
            log.error("【提供图片生成视频任务】异常，bizId={}, bizType={}, error={}",
                    bizId, bizType, e.getMessage(), e);
            throw new SdpDesignException("提供图片裁剪任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检索单张图片相似度
     */
    public Map<SearchDimension, SearchInfo> singleSearchSimilarity(String searchUrl) {
        try {
            log.info("=== 款式识别 searchUrl:{}", searchUrl);
            DataResponse<Map<SearchDimension, SearchInfo>> response = vectorClient.searchSimilarity(
                    searchUrl,
                    Set.of(SearchDimension.MUSE_SAME_STYLE, SearchDimension.INFRINGEMENT)
            );
            log.info("=== 款式识别 response:{}", JSON.toJSONString(response));

            SdpDesignException.isTrue(response.getSuccessful(), response.getMessage());
            SdpDesignException.notNull(response.getData(), "muse图片相似度查询 数据为空");
            return response.getData();
        } catch (Exception e) {
            log.error("muse图片相似度查询 失败:{} ", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 批量检索图片相似度
     */
    public List<SearchSimilarityVo> batchSearchSimilarity(Set<String> searchUrlSet) {
        BatchSearchSimilarityRequest request = new BatchSearchSimilarityRequest(
                searchUrlSet,
                Set.of(SearchDimension.MUSE_SAME_STYLE, SearchDimension.INFRINGEMENT)
        );
        try {
            log.info("=== 款式识别 request:{}", JSON.toJSONString(request));
            DataResponse<List<SearchSimilarityVo>> response = vectorClient.batchSearchSimilarity(request);
            log.info("=== 款式识别 response:{}", JSON.toJSONString(response));

            SdpDesignException.isTrue(response.getSuccessful(), response.getMessage());
            SdpDesignException.notNull(response.getData(), "muse图片相似度查询 数据为空");
            return response.getData();
        } catch (Exception e) {
            log.error("muse图片相似度批量查询 失败:{} ", e.getMessage(), e);
            throw e;
        }
    }
}