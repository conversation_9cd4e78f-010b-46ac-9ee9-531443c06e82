package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.TryOnService;
import tech.tiangong.sdp.design.vo.req.tryon.SyncSpuTryOnTaskStateReq;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/try-on")
public class TryOnInnerController extends BaseController {

    private final TryOnService tryOnService;
    /**
     * 根据spu编号同步tryOn任务状态及选图结果
     */
    @PostMapping("/sync-spu-try-on-task-state")
    public DataResponse<Void> syncSpuTryOnTaskState(@RequestBody SyncSpuTryOnTaskStateReq req) {
        tryOnService.syncSpuTryOnTaskState(req);
        return DataResponse.ok();
    }
}
