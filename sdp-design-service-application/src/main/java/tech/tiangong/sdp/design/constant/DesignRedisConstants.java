package tech.tiangong.sdp.design.constant;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2021/12/20
 */
public interface DesignRedisConstants {
    /**
     * redis key ，撮合系统前缀
     */
    String  REDIS_KEY_PREFIX = "sdp:sdp_design:";


    /**
     * redis缓存key前缀
     */
    String CACHE_KEY_PREFIX = "SDP_DESIGN:REDIS_CACHE:";

    /**
     * redis缓存key-bom详情-sdk
     */
    String BOM_DETAIL_BY_ID_SDK = CACHE_KEY_PREFIX + "BOM:DETAIL_BY_ID_SDK:";


    /**
     * 物料确认后超3小时未下采购_通知key
     */
    String KEY_RESULT_PURCHASE_NOTICE ="SDP_DESIGN:MATERIAL_TRACK:RESULT_PURCHASE_NOTICE:";


    /**
     * BOM工艺删除临时保存
     */
    String BOM_CRAFT_DEL ="SDP_DESIGN:BOM_CRAFT_DEL:";

    /**
     * BOM物料删除临时保存
     */
    String BOM_MATERIAL_DEL ="SDP_DESIGN:BOM_CRAFT_DEL:";

    /**
     * 拆版裁前工艺待推送
     */
    String DEMOLITION_PRE_CRAFT_WAIT_PUSH ="SDP_DESIGN:DEMOLITION:PRE_CRAFT:WAIT_PUSH:";

    /**
     * 拆版暂存删除工艺
     */
    String DEMOLITION_TEMPORARY_DEL_CRAFT ="SDP_DESIGN:DEMOLITION:TEMPORARY:DEL:CRAFT:";

    /**
     * 版单打印锁
     */
    String PROTOTYPE_QRCODE_LOCK = "SDP_DESIGN:PROTOTYPE_QRCODE_LOCK:";

    /**
     * 历史数据SPU同步锁
     */
    String OLD_SPU_LOCK ="SDP_DESIGN:STYLE:lOCK";

    /**
     * 历史SPU回刷skc
     */
    String OLD_SPU_SKC ="SDP_DESIGN:OLD_SPU_SKC:lOCK";

    /**
     * 历史匹配同步物料快照
     */
    String OLD_MATERIAL_TO_SNAPSHOT ="SDP_DESIGN:MATERIAL_SNAPSHOT:OLD_SYNC";

    /**
     * 物料快照信息同步给履约
     */
    String MATERIAL_SNAPSHOT_TO_DEMAND ="SDP_DESIGN:MATERIAL_SNAPSHOT:TO_DEMAND";

    /**
     * 样衣编号生成锁
     */
    String CLOTHING_CODE_GENERATE_LOCK = "SDP_DESIGN:CLOTHING_CODE_GENERATE:LOCK";

    /**
     * CRM客户端创建改款需求
     */
    String KEY_CREATE_INTENTION_BY_CUSTOMER = REDIS_KEY_PREFIX + "CREATE_INTENTION_BY_CUSTOMER:{}";

    /**
     * 唯品会用户缓存锁的key
     */
    String VIP_USER_CACHE_LOCK = "SDP_DESIGN:VIP_USER:LOCK";

    /**
     * 唯品会用户缓存
     */
    String VIP_USER_CACHE = "SDP_DESIGN:VIP_USER:CACHE";

    /**
     * 上架图spu缓存
     */
    String ON_SHELF_IMAGE_SPU_CACHE = "SDP_DESIGN:ON_SHELF_IMAGE:SPU";

    /**
     * 选款创建spu缓存
     */
    String PICK_STYLE_SPU_CACHE = "SDP_DESIGN:PICK_STYLE:SPU";

    /**
     * 选款创建_新增spu缓存
     */
    String PICK_STYLE_NEW_SPU_CACHE = "SDP_DESIGN:PICK_STYLE:NEW_SPU";

    /**
     * 异步任务_下载锁
     */
    String SYNC_TASK_DOWNLOAD_LOCK = "SDP_DESIGN:SYNC_TASK:DOWNLOAD:";

    /**
     * 视觉任务发起锁
     */
    String VISUAL_CREATE_LOCK = "SDP_DESIGN:VISUAL_DEMAND:CREATE:";

    /**
     * SPU Murmuration AI任务提交锁
     */
    String SPU_MURMURATION_TASK_LOCK = "SDP_DESIGN:SPU_MURMURATION:TASK:";

    /**
     * SKC颜色识别任务锁
     */
    String SKC_COLOR_IDENTIFICATION_TASK_LOCK = "SDP_DESIGN:SKC_COLOR:TASK:";

    /**
     * SPU图包拉取任务锁
     */
    String SPU_ALIBABA_DISTRIBUTION_TASK_LOCK = "SDP_DESIGN:SPU_ALIBABA_DISTRIBUTION:TASK:";

    /**
     * SPU维度的SKC颜色更新锁（用于处理颜色识别结果入库时避免并发重复）
     */
    String SPU_SKC_COLOR_UPDATE_LOCK = "SDP_DESIGN:SPU_SKC_COLOR:LOCK:";

    /**
     * SPU内某颜色的分配锁（styleCode+color 维度），允许不同颜色并行处理
     */
    String SPU_COLOR_ASSIGN_LOCK = "SDP_DESIGN:SPU_COLOR_ASSIGN:LOCK:";


    static String formatKey(String redisKeyTemplate,Object value){
        return StrUtil.format(redisKeyTemplate,value);
    }

    /**
     * 物料确认后超3小时未下采购_通知key
     * @param matchResultId 匹配结果id
     * @return key
     */
    static String getPurchaseTimeOutNoticeKey(Long matchResultId) {
        return KEY_RESULT_PURCHASE_NOTICE + matchResultId;
    }

    /**
     * BOM工艺删除临时保存Key值
     * @param bomId
     * @return
     */
    static String getBomCraftDelKey(Long bomId) {
        return BOM_CRAFT_DEL + bomId;
    }

    /**
     * BOM物料删除临时保存Key值
     * @param bomId
     * @return
     */
    static String getBomMaterialDelKey(Long bomId) {
        return BOM_MATERIAL_DEL + bomId;
    }


    /**
     * 拆版裁前工艺待推送Key值
     * @param materialDemandId 物料需求ID
     * @return
     */
    static String getDemolitionPreCraftWaitPushKey(Long materialDemandId) {
        return DEMOLITION_PRE_CRAFT_WAIT_PUSH + materialDemandId;
    }

    /**
     * 拆版暂存删除工艺Key值
     * @param prototypeId 打版单id
     * @return
     */
    static String getDemolitionTemporaryDelCraftKey(Long prototypeId) {
        return DEMOLITION_TEMPORARY_DEL_CRAFT + prototypeId;
    }

    /**
     * bom详情key-sdk
     * @param bomId bomId
     * @return key
     */
    static String getBomDetailSdkKey(Long bomId) {
        return BOM_DETAIL_BY_ID_SDK + bomId;
    }


    /**
     * bom详情key-sdk
     * @param spuCode bomId
     * @return key
     */
    static String getOnShelfImageSpuKey(String spuCode) {
        return ON_SHELF_IMAGE_SPU_CACHE + spuCode;
    }


    /**
     * 选款创建spu key
     * @param supplierNameStyle 供应商名称与款号
     * @return key
     */
    static String getPickStyleKey(String supplierNameStyle) {
        return PICK_STYLE_SPU_CACHE + supplierNameStyle;
    }

    /**
     * 选款创建_新增spu key
     * @param supplierNameStyle 供应商名称与款号
     * @return key
     */
    static String getPickStyleNewSpuKey(String supplierNameStyle) {
        return PICK_STYLE_NEW_SPU_CACHE + supplierNameStyle;
    }
    /**
     * 视觉需求创建 key
     * @param styleCode 供应商名称与款号
     * @return key
     */
    static String getVisualCreateLock(String styleCode) {
        return VISUAL_CREATE_LOCK + styleCode;
    }

    /**
     * SPU Murmuration AI任务提交锁 key
     * @param styleCode SPU样式编码
     * @return key
     */
    static String getSpuMurmurationTaskLock(String styleCode) {
        return SPU_MURMURATION_TASK_LOCK + styleCode;
    }

    /**
     * SKC颜色识别任务锁 key
     * @param designCode SKC设计编码
     * @return key
     */
    static String getSkcColorIdentificationTaskLock(String designCode) {
        return SKC_COLOR_IDENTIFICATION_TASK_LOCK + designCode;
    }

    /**
     * SPU图包拉取任务锁 key
     * @param styleCode SPU样式编码
     * @return key
     */
    static String getSpuAlibabaDistributionTaskLock(String styleCode) {
        return SPU_ALIBABA_DISTRIBUTION_TASK_LOCK + styleCode;
    }

    /**
     * 获取SPU维度的SKC颜色更新锁 key
     * @param styleCode SPU样式编码
     * @return key
     */
    static String getSpuSkcColorUpdateLock(String styleCode) {
        return SPU_SKC_COLOR_UPDATE_LOCK + styleCode;
    }

    /**
     * 获取SPU内颜色分配锁 key（styleCode + color）
     * @param styleCode SPU样式编码
     * @param color 颜色（会trim）
     * @return key
     */
    static String getSpuColorAssignLock(String styleCode, String color) {
        String normColor = color == null ? "NULL" : color.trim();
        return SPU_COLOR_ASSIGN_LOCK + styleCode + ":" + normColor;
    }




}
