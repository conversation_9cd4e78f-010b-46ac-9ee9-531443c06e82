package tech.tiangong.sdp.design.controller.web;


import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.tiangong.sdp.design.service.SpotSpuSupplierService;
import tech.tiangong.sdp.design.vo.req.spot.SpotSpuSupplierReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotSpuSupplierVo;

import java.util.List;

/**
 * SPU关联供应商
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.WEB + UrlVersionConstant.VERSION_V1 + "/spot-spu-supplier")
public class SpotSpuSupplierController extends BaseController {
    private final SpotSpuSupplierService spotSpuSupplierService;


    /**
     * SPU关联供应商列表
     *
     * @return 响应结果
     */
    @PostMapping("/all/list")
    public DataResponse<List<SpotSpuSupplierVo>> getAllSupplierList(@RequestBody SpotSpuSupplierReq req) {
        return DataResponse.ok(spotSpuSupplierService.getAllSupplierList(req));
    }



}
