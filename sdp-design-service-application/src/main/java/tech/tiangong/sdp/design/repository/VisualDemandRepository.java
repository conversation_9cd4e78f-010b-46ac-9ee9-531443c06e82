package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import cn.yibuyun.framework.enumeration.Bool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.VisualDemand;
import tech.tiangong.sdp.design.mapper.VisualDemandMapper;

/**
 * (VisualDemand)服务仓库类
 */
@Repository
public class VisualDemandRepository extends BaseRepository<VisualDemandMapper, VisualDemand> {

    public VisualDemand getLatestBySpuCode(String spuCode) {
        return getOne(new LambdaQueryWrapper<VisualDemand>()
                .eq(VisualDemand::getStyleCode, spuCode)
                .eq(VisualDemand::getIsLatest,Bool.YES.getCode())
                .eq(VisualDemand::getIsDeleted, Bool.NO.getCode())
                .orderByDesc(VisualDemand::getCreatedTime),false);
    }
}
