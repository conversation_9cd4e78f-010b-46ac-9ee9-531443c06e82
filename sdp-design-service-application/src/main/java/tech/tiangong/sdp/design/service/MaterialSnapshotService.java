package tech.tiangong.sdp.design.service;

import cn.yibuyun.framework.net.PageRespVo;
import tech.tiangong.sdp.design.vo.query.material.MaterialSnapshotQuery;
import tech.tiangong.sdp.design.vo.req.material.MaterialSnapshotCreateReq;
import tech.tiangong.sdp.design.vo.req.material.SnapshotUnitUpdateReq;
import tech.tiangong.sdp.design.vo.resp.material.MaterialSnapshotVo;

import java.util.List;
import java.util.Map;

/**
 * 物料快照表服务接口
 *
 * <AUTHOR>
 */
public interface MaterialSnapshotService {

    /**
     * 分页查询
     *
     * @param query 查询对象
     * @return 分页结果
     */
    PageRespVo<MaterialSnapshotVo> page(MaterialSnapshotQuery query);

    /**
     * 根据主键查询详情
     *
     * @param id 主键
     * @return 物料快照信息
     */
    MaterialSnapshotVo getById(Long id);

    /**
     * 根据主键批量查询
     * @param idList 主键集合
     * @return 物料快照集合
     */
    List<MaterialSnapshotVo> listByIds(List<Long> idList);

    /**
     * 根据skuId查询
     * @param skuId skuId
     * @return 物料快照集合
     */
    List<MaterialSnapshotVo> listBySkuId(Long skuId);

    /**
     * 创建物料快照
     *
     * @param req 数据实体
     * @return 物料快照主键id
     */
    Long create(MaterialSnapshotCreateReq req);

    /**
     * 批量创建物料快照(bom调用)
     *
     * @param reqList 数据实体
     * @return Map<bom物料id, 物料快照id>
     */
    Map<Long, Long> batchCreate4Bom(List<MaterialSnapshotCreateReq> reqList);


    // ============== inner 接口 =============================

    /**
     * 批量更新物料快照辅料最小单位
     * @param req 更新req
     */
    void batchUpdateMinPriceUnit(List<SnapshotUnitUpdateReq> req);


}
