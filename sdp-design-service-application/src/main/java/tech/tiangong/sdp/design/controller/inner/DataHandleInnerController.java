package tech.tiangong.sdp.design.controller.inner;

import cn.yibuyun.framework.cache.commands.CacheCommands;
import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.framework.web.annotation.NoRepeatSubmitLock;
import cn.yibuyun.framework.web.base.BaseController;
import cn.yibuyun.framework.web.base.UrlVersionConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tech.tiangong.sdp.core.aop.ResetCurrentUser;
import tech.tiangong.sdp.design.constant.DesignRedisConstants;
import tech.tiangong.sdp.design.service.*;
import tech.tiangong.sdp.design.service.download.impl.VisualDemandAsyncTaskServiceImpl;
import tech.tiangong.sdp.design.vo.dto.spot.SpotCategoryUpdateDto;
import tech.tiangong.sdp.design.vo.req.prototype.SkcOnShelfImg2ZjReq;
import tech.tiangong.sdp.design.vo.req.prototype.SpuProductTypeUpdateReq;
import tech.tiangong.sdp.design.vo.req.prototype.SyncOnShelfImg2ZjReq;
import tech.tiangong.sdp.design.vo.req.prototype.SyncOnShelfImgReq;
import tech.tiangong.sdp.design.vo.req.spot.SpotCategoryUpdateReq;
import tech.tiangong.sdp.design.vo.req.spot.SpotSkcPushPopTypeReq;
import tech.tiangong.sdp.design.vo.req.spot.SpuPushPopReq;
import tech.tiangong.sdp.design.vo.req.spot.SpuPushPopTypeReq;
import tech.tiangong.sdp.design.vo.req.visual.VisualImageCreateReq;
import tech.tiangong.sdp.design.vo.resp.spot.SpotPayeeUpdateVo;
import tech.tiangong.sdp.design.vo.resp.spot.StylePushPopTypeVo;
import tech.tiangong.sdp.design.vo.resp.spot.StylePushPopVo;
import tech.tiangong.sdp.design.vo.resp.style.ProductImageSyncVo;
import tech.tiangong.sdp.design.vo.resp.visual.BatchCreateVisualDemandResp;
import tech.tiangong.sdp.design.vo.resp.visual.InitVisualSpuVo;
import tech.tiangong.sdp.design.vo.resp.visual.VisualImageCreateVo;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.concurrent.locks.Lock;

/**
 * 数据处理 controller
 * <AUTHOR>
 * @date 2022/11/27 21:11
 */


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1 + "/data-handle")
public class DataHandleInnerController extends BaseController {

    private final BomDataHandleService bomDataHandleService;
    private final PrototypeDataHandleService prototypeDataHandleService;
    private final PurchaseDataHandleService purchaseDataHandleService;
    private final DesignStyleDataHandleService styleDataHandleService;
    private final DesignDemandDataHandleService designDemandDataHandleService;
    private final SpotDataHandleService spotDataHandleService;
    private final VisualDataHandleService visualDataHandleService;
    private final DesignStyleService designStyleService;
    @Resource
    private CacheCommands cacheCommands;

    private final VisualDemandAsyncTaskServiceImpl visualDemandAsyncTaskService;


    // ====================================== 视觉 ======================================

    /**
     * 根据设计款spu创建图包并推送pop
     * @param req 入参
     * @return 创建图包成功的spu
     */
    @NoRepeatSubmitLock
    @ResetCurrentUser
    @PostMapping("/visual/design-style/create-image")
    public DataResponse<List<VisualImageCreateVo>> createImageByDesignStyle(@RequestBody @Validated VisualImageCreateReq req) {
        return DataResponse.ok(visualDataHandleService.createImageByDesignStyle(req));
    }

    /**
     * 图包推送pop
     * @param styleCodeList 入参
     * @return Void
     */
    @NoRepeatSubmitLock
    @ResetCurrentUser
    @PostMapping("/visual/push-pop")
    public DataResponse<Void> imagePackagePushPop(@RequestBody List<String> styleCodeList) {
        visualDataHandleService.pushPop(styleCodeList);
        return DataResponse.ok();
    }

    /**
     * pop图包查询
     *
     * @param styleCode spu编码
     * @return 响应结果
     */
    @GetMapping("/pop-img/{styleCode}")
    public DataResponse<String> getPopImgUrl(@PathVariable(value = "styleCode") String styleCode) {
        return DataResponse.ok(visualDataHandleService.getPopImgUrl(styleCode));
    }

//    /**
//     * 批量创建视觉任务-excel导入
//     */
//    @ResetCurrentUser
//    @NoRepeatSubmitLock
//    @PostMapping("/visual/demand-create")
//    public DataResponse<List<BatchCreateVisualDemandResp>> batchCreateVisualDemand(@RequestParam("file") MultipartFile file, @RequestParam(value = "creatorCode", required = false) String creatorCode) throws IOException {
//        return DataResponse.ok(visualDataHandleService.batchCreateVisualDemand(file.getInputStream(), creatorCode));
//    }

    /**
     * 初始化视觉spu
     * @param styleCodeList spu
     * @return 新增的视觉spu; 存在时不处理,不返回
     */
    @NoRepeatSubmitLock
    @ResetCurrentUser
    @PostMapping("/visual/init-spu")
    public DataResponse<List<InitVisualSpuVo>> initVisualSpu(@RequestBody List<String> styleCodeList) {
        return DataResponse.ok(visualDataHandleService.initVisualSpu(styleCodeList));
    }

    // ====================================== 现货 以下 ======================================

    /**
     * 更新收款人-excel导入
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/spot/update-payee")
    public DataResponse<List<SpotPayeeUpdateVo>> updatePayee(MultipartFile file) throws IOException {
        return DataResponse.ok(spotDataHandleService.updatePayee(file.getInputStream()));
    }

    /**
     * 更新收款人与资料状态-excel导入
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/spot/payee-resource-state")
    public DataResponse<List<SpotPayeeUpdateVo>> updatePayeeResourceState(MultipartFile file) throws IOException {
        return DataResponse.ok(spotDataHandleService.updatePayeeResourceState(file.getInputStream()));
    }

    /**
     * 指定spu推送pop-excel导入
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/spot/push-2-pop")
    public DataResponse<List<String>> push2Pop(MultipartFile file) throws IOException {
        return DataResponse.ok(spotDataHandleService.push2Pop(file.getInputStream()));
    }

    /**
     * 指定spu与skc推送pop-excel导入
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/spot/push-pop/spu-skc")
    public DataResponse<List<String>> pushPopBySpuSkc(MultipartFile file,
                                                      @RequestParam(value = "pushData", required = false) Boolean pushData) throws IOException {
        return DataResponse.ok(spotDataHandleService.pushPopBySpuSkc(file.getInputStream(), pushData));
    }

    /**
     * 根据spu更新品类并推送pop
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/spot-update/spu-category")
    public DataResponse<List<SpotCategoryUpdateDto>> updateCategoryBySpu(@RequestBody @Validated List<SpotCategoryUpdateReq> list) {
        return DataResponse.ok(spotDataHandleService.updateCategoryBySpu(list));
    }

    /**
     * 现货根据spu与更新类型-推送spu更新数据到pop
     *
     * 品类/款式风格/代购价/预估核价/定价类型
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/spot/push-pop-spu/with-type")
    public DataResponse<List<StylePushPopTypeVo>> pushPopSpuWithType(@RequestBody @Validated SpuPushPopTypeReq req) {
        return DataResponse.ok(spotDataHandleService.pushPopSpuWithType(req));
    }

    /**
     * 现货-根据skc与更新类型-推送skc更新数据到pop
     *
     * 颜色/尺码信息
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/spot/push-pop-skc/with-type")
    public DataResponse<List<StylePushPopTypeVo>> pushPopSkcWithType(@RequestBody @Validated SpotSkcPushPopTypeReq req) {
        return DataResponse.ok(spotDataHandleService.pushPopSkcWithType(req));
    }



    // ====================================== 现货 以上 ======================================




    // ====================================== 灵感设计相关 以下 ======================================
    /**
     * 灵感设计-更新提交人
     * @param designDemandId 灵感设计id(若为空, 更新所有缺少提交人的数据)
     * @return Void
     */
    @ResetCurrentUser
    @PostMapping("/design-demand/update_submit_user")
    public DataResponse<Void> updateSubmitUser(String designDemandId){
        designDemandDataHandleService.updateSubmitUser(designDemandId);
        return DataResponse.ok();
    }


    // ====================================== 灵感设计相关 以上 ======================================











    // ====================================== 设计款相关 以下 ======================================

    /**
     * 设计款-根据spu推送未核价款式资料到pop(pop已存在的skc不推送)
     *
     * @param req 入参
     * @return SkcDetailInnerVo
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/spu/no-price/batch-push-pop")
    DataResponse<List<StylePushPopVo>> batchPushNoPriceStyle2Pop(@RequestBody @Validated SpuPushPopReq req){
        return  DataResponse.ok(styleDataHandleService.batchPushNoPriceStyle2Pop(req));
    }

    /**
     * 设计款-根据spu更新商品属性和基础资料到pop
     *
     * @param styleCodeSet spu编码
     */
    //@ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/spu/push-attribute-pop")
    DataResponse<Void> pushAttribute2Pop(@RequestBody Set<String> styleCodeSet){
        designStyleService.pushAttribute2Pop(styleCodeSet);
        return DataResponse.ok();
    }

    /**
     * 设计款-根据spu与更新类型-推送spu更新数据到pop (品类/款式风格)
     *
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/spu/push-pop-spu")
    public DataResponse<List<StylePushPopTypeVo>> pushSpuWithType(@RequestBody @Validated SpuPushPopTypeReq req) {
        return DataResponse.ok(styleDataHandleService.pushSpuWithType(req));
    }

    /**
     * 设计款-根据skc推送颜色更新数据到pop
     *
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock
    @PostMapping("/skc/push-color-pop")
    public DataResponse<List<StylePushPopTypeVo>> pushSkcColor(@RequestBody List<String> designCodeList) {
        return DataResponse.ok(styleDataHandleService.pushSkcColor(designCodeList));
    }


    /**
     * 更新SPU的店铺对应平台信息
     * @param styleCodeList 入参
     * @return Void
     */
    @NoRepeatSubmitLock
    @ResetCurrentUser
    @PostMapping("/spu/update-platform")
    public DataResponse<Void> updatePlatform(@RequestBody List<String> styleCodeList) {
        styleDataHandleService.updatePlatform(styleCodeList);
        return DataResponse.ok();
    }

    /**
     * 更新SPU的商品类型
     * @param req 入参
     * @return Void
     */
    @NoRepeatSubmitLock
    @ResetCurrentUser
    @PostMapping("/spu/update-product-type")
    public DataResponse<Void> updateProductType(@RequestBody @Validated SpuProductTypeUpdateReq req) {
        styleDataHandleService.updateProductType(req);
        return DataResponse.ok();
    }

    /**
     * 对接致景-更新旧款bizChannel
     * @param styleCode spu
     * @return Void
     */
    @NoRepeatSubmitLock
    @ResetCurrentUser
    @PostMapping("/zj/update-biz-channel")
    public DataResponse<Void> updateBizChannel(String styleCode) {
        prototypeDataHandleService.updateBizChannel(styleCode);
        return DataResponse.ok();
    }

    /**
     * 同步上架图给致景-可全量同步
     * @param req 入参
     * @return skc数量
     */
    @NoRepeatSubmitLock
    @ResetCurrentUser
    @PostMapping("/on-shelf-img/sync-2-zj")
    public DataResponse<Integer> syncImg2Zj(@RequestBody SyncOnShelfImg2ZjReq req) {
        return DataResponse.ok(prototypeDataHandleService.syncImg2Zj(req));
    }

    /**
     * 根据skc推送上架图给致景
     */
    @PostMapping("/on-shelf-img/push-2-zj")
    public DataResponse<List<String>> onShelfImg2Zj(@RequestBody SkcOnShelfImg2ZjReq req) {
        return DataResponse.ok(prototypeDataHandleService.onShelfImg2Zj(req));
    }

    /**
     * 同步商品运营商品上架图
     * @return 更新数量
     */
    @ResetCurrentUser
    @PostMapping("/spu/sync/on-shelf-img")
    public DataResponse<ProductImageSyncVo> syncOnShelfImg(@RequestBody @Validated SyncOnShelfImgReq req) {
        return DataResponse.ok(prototypeDataHandleService.syncOnShelfImg(req));
    }


    /**
     * 买手款StyleVersionId修复
     * @param designCode 设计款号,为空时刷新全部
     * @return 更新数量
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock(lockTime = 10L)
    @PostMapping("/buyer-design/update-style-version-id")
    public DataResponse<Integer> updateBuyerStyleVersionId(String designCode){
        return DataResponse.ok(prototypeDataHandleService.updateBuyerStyleVersionId(designCode));
    }


    /**
     * 历史SPU处理
     *
     * @return 处理的spu数量
     */
    @PostMapping("/handle-old-spu")
    public DataResponse<Integer> handleOldSpu() {
        Lock lock = cacheCommands.getDistributedMutexLock(DesignRedisConstants.OLD_SPU_LOCK);
        boolean tryLock = lock.tryLock();
        Assert.isTrue(tryLock, "正在处理，请稍后再试");
        try {
            return DataResponse.ok(styleDataHandleService.handleOldSpu());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 历史SPU回刷SKC
     *
     * @param styleCode spu编码
     * @return 处理的spu数量
     */
    @PostMapping("/old-spu-skc")
    @NoRepeatSubmitLock(lockTime = 10L)
    public DataResponse<Integer> oldSpu2Skc(String styleCode) {
        Lock lock = cacheCommands.getDistributedMutexLock(DesignRedisConstants.OLD_SPU_SKC);
        boolean tryLock = lock.tryLock();
        Assert.isTrue(tryLock, "正在处理，请稍后再试");
        try {
            return DataResponse.ok(styleDataHandleService.oldSpu2Skc(styleCode));
        } finally {
            lock.unlock();
        }
    }


    // ====================================== 设计款相关 以上 ======================================






    // ====================================== bom相关 以下 ========================================

    /**
     * bom暂存历史数据刷新-v3.11
     * v3.5.1,bom添加了暂存功能, 暂存的数据维护在原表中; v3.11新增了暂存表, 需要将历史的暂存数据迁移到暂存表中;
     */
    @ResetCurrentUser
    @PostMapping("/bom/transient-move")
    public DataResponse<Void> moveTransientData(@RequestParam String designCode){
        bomDataHandleService.moveTransientData(designCode);
        return DataResponse.ok();
    }

    /**
     * 清除-bom暂存历史数据  -v3.11
     * 清除v3.5.1需求中生成的bom暂存数据
     */
    @ResetCurrentUser
    @PostMapping("/bom/transient-clean")
    public DataResponse<Void> cleanTransientData(@RequestParam(value = "designCode") String designCode){
        bomDataHandleService.cleanTransientData(designCode);
        return DataResponse.ok();
    }

    /**
     * 更新bom物料表中的物料类型  -v3.11
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock(lockTime = 10L)
    @PostMapping("/bom/update-material-type")
    public DataResponse<Long> updateMaterialType(){
        return DataResponse.ok(bomDataHandleService.updateMaterialType());
    }

    /**
     * 更新bom-面料的采购周期信息  -v3.20.3
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock(lockTime = 10L)
    @PostMapping("/bom/purchase-cycle-fabric")
    public DataResponse<Void> updatePurchaseCycleFabric(){
        bomDataHandleService.updatePurchaseCycleFabric();
        return DataResponse.ok();
    }

    /**
     * 更新bom-辅料的采购周期信息  -v3.20.3
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock(lockTime = 10L)
    @PostMapping("/bom/purchase-cycle-accessories")
    public DataResponse<Void> updatePurchaseCycleAccessories(){
        bomDataHandleService.updatePurchaseCycleAccessories();
        return DataResponse.ok();
    }

    /**
     * 更新bom-特辅的采购周期信息  -v3.20.3
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock(lockTime = 10L)
    @PostMapping("/bom/purchase-cycle-special")
    public DataResponse<Void> updatePurchaseCycleSpecial(){
        bomDataHandleService.updatePurchaseCycleSpecial();
        return DataResponse.ok();
    }

    /**
     * 更新bom-暂存物料的采购周期信息  -v3.20.3
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock(lockTime = 10L)
    @PostMapping("/bom/purchase-cycle-transient")
    public DataResponse<Void> updatePurchaseCycleTransient(){
        bomDataHandleService.updatePurchaseCycleTransient();
        return DataResponse.ok();
    }




    // ====================================== bom相关 以上 ========================================




    // ====================================== 采购相关 以下 ========================================

    /**
     * 刷新自动下采购的辅料裁前工艺
     * @param purchaseOrderNo 采购单号,为空时刷新全部
     * @return 更新数量
     */
    @ResetCurrentUser
    @NoRepeatSubmitLock(lockTime = 10L)
    @PostMapping("/purchase/update-cutting-process")
    public DataResponse<Integer> updateCuttingProcess(@RequestParam(value = "purchaseOrderNo", required = false) String purchaseOrderNo){
        return DataResponse.ok(purchaseDataHandleService.updateCuttingProcess(purchaseOrderNo));
    }


    // ====================================== 采购相关 以上 ========================================



    /**
     * 视觉任务、批量处理
     */
    @PostMapping("/visual/async")
    public DataResponse<Void> handle(){
        visualDemandAsyncTaskService.handle();
        return DataResponse.ok();
    }

}
