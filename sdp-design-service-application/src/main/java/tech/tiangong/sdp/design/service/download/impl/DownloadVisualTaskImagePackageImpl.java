package tech.tiangong.sdp.design.service.download.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.yibuyun.framework.exception.BusinessException;
import com.alibaba.fastjson.JSONObject;
import com.zjkj.booster.common.constant.DatePatternConstants;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;
import tech.tiangong.sdp.core.config.DownloadProperties;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.DesignAsyncTaskTypeEnum;
import tech.tiangong.sdp.design.helper.ImageDownloadHelper;
import tech.tiangong.sdp.design.helper.UploaderOssHelper;
import tech.tiangong.sdp.design.repository.VisualImagePackageRepository;
import tech.tiangong.sdp.design.repository.VisualTaskRepository;
import tech.tiangong.sdp.design.service.download.DownloadTaskStrategy;
import tech.tiangong.sdp.design.vo.dto.download.FileUploadDTO;
import tech.tiangong.sdp.utils.FileCompressUtils;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
/**
 * 下载视觉任务的上架图（最终图包）
 */
@Slf4j
@AllArgsConstructor
@Component
public class DownloadVisualTaskImagePackageImpl implements DownloadTaskStrategy {

    private final VisualTaskRepository visualTaskRepository;
    private final VisualImagePackageRepository visualImagePackageRepository;
    private final ImageDownloadHelper imageDownloadHelper;
    private final DateTimeFormatter PURE_DATETIME_PATTERN = DateTimeFormatter.ofPattern(DatePatternConstants.PURE_DATETIME_PATTERN);
    private final Long DOWNLOAD_TIMEOUT_SECONDS = 10 * 60L; // 10分钟超时
    private final AsyncTaskExecutor asyncTaskExecutor;
    private final DownloadProperties downloadProperties;
    private final UploaderOssHelper uploaderOssHelper;

    @Override
    public DesignAsyncTaskTypeEnum getTaskType() {
        return DesignAsyncTaskTypeEnum.DOWNLOAD_VISUAL_TASK_IMAGE_PACKAGE;
    }

    @Override
    public List<FileUploadDTO> processDownloadTask(DesignAsyncTask task) {
        log.info("==== DownloadVisualTaskImagePackageImpl processDownloadTask {}:{}",getTaskType().getDesc(), JSONObject.toJSONString(task));
        File tempDir = null;
        try {
            tempDir = createTempDirectory(task.getAsyncTaskId());

            List<Long> visualTaskIds = JSONObject.parseArray(task.getParameters(), Long.class);
            List<VisualTask> visualTasks = visualTaskRepository.listByIds(visualTaskIds);
            Set<String> styleCodes = visualTasks.stream().map(VisualTask::getStyleCode).collect(Collectors.toSet());
            List<VisualImagePackage> imagePackages = visualImagePackageRepository.listLatestByStyleCodes(styleCodes);
//            if (CollectionUtil.isEmpty(imagePackages)) {
//                throw new BusinessException("未找到任何图片记录, asyncTaskId: "+task.getAsyncTaskId()+", styleCodes:"+JSONObject.toJSONString(styleCodes));
//            }
            Map<String,VisualImagePackage> spuImagePackageMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(imagePackages)) {
                spuImagePackageMap.putAll(imagePackages.stream().collect(Collectors.toMap(VisualImagePackage::getStyleCode,v->v,(k1,k2)->k2)));
            }
            styleCodes.forEach(styleCode -> {
                if (!spuImagePackageMap.containsKey(styleCode)) {
                    spuImagePackageMap.put(styleCode, new VisualImagePackage());
                }
            });
            // 2. 异步下载图片并按SPU组织目录
            File finalTempDir = tempDir;
            List<CompletableFuture<File>> futures = new ArrayList<>();
            spuImagePackageMap.forEach((styleCode,imagePackage)->{
                futures.add(CompletableFuture.supplyAsync(()->downloadImagesForSpu(styleCode,imagePackage, finalTempDir),asyncTaskExecutor)
                                .orTimeout(DOWNLOAD_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                                .exceptionally(throwable->handleDownloadException(throwable, imagePackage)));
            });

            // 3. 等待所有下载任务完成
            List<File> downloadedFiles = waitForDownloadCompletion(futures);

            if (CollectionUtil.isEmpty(downloadedFiles)) {
                throw new BusinessException("所有文件下载均失败1");
            }
            downloadedFiles = downloadedFiles.stream().filter(Objects::nonNull).toList();
            if (CollectionUtil.isEmpty(downloadedFiles)) {
                throw new BusinessException("所有文件下载均失败2");
            }
            // 4. 压缩文件
            return compressFiles(task.getAsyncTaskId(), tempDir, downloadedFiles);
        } catch (Exception e) {
            log.error("系统处理异常: taskId={}, req={}, message={}",task.getAsyncTaskId(),task.getParameters(), e.getMessage());
            throw new BusinessException("下载任务处理失败", e);
        } finally {
            cleanupTempFiles(tempDir);
        }
    }

    /**
     * 压缩文件
     */
    private List<FileUploadDTO> compressFiles(Long taskId, File tempDir, List<File> files) {
        if (files.isEmpty()) {
            return Collections.emptyList();
        }

        Integer maxZipSizeMb = downloadProperties.getMaxZipSizeMb();
        String baseFileName = "images_"+taskId;
        List<File> zipFiles = FileCompressUtils.zipFilesWithSizeLimit(
                tempDir,
                baseFileName,
                maxZipSizeMb,
                files);

        if (zipFiles.isEmpty()) {
            throw new BusinessException("压缩文件失败，压缩后返回空文件");
        }
        return zipFiles.stream().map(uploaderOssHelper::createFileUploadDTO).collect(Collectors.toList());
    }

    /**
     * 下载单个SPU的所有图片
     */
    private File downloadImagesForSpu(String spuCode,VisualImagePackage imagePackage, File parentDir){
        if (StringUtils.isBlank(spuCode)) {
            log.warn("downloadImagesForSpu 异常， styleCode为空");
            return null;
        }

        File spuDir = new File(parentDir, spuCode);
        try {
            FileUtils.forceMkdir(spuDir);

            if(imagePackage!=null) {
                List<ImageFile> images = new ArrayList<>();
                OnShelfImagePackage onShelfImagePackage = JSONObject.parseObject(imagePackage.getOnShelfImages(), OnShelfImagePackage.class);
                if(onShelfImagePackage!=null){
                    if (CollectionUtil.isNotEmpty(onShelfImagePackage.getSpuImages())) {
                        images.addAll(onShelfImagePackage.getSpuImages().stream()
                                .filter(item -> CollUtil.isNotEmpty(item.getImages()))
                                .flatMap(v -> v.getImages().stream()).toList());
                    }
                    if (CollectionUtil.isNotEmpty(onShelfImagePackage.getSkcImages())) {
                        images.addAll(onShelfImagePackage.getSkcImages().stream()
                                .filter(item -> CollUtil.isNotEmpty(item.getImages()))
                                .flatMap(v -> v.getImages().stream()).toList());
                    }
                }

                if (CollectionUtil.isEmpty(images)) {
                    log.warn("SPU没有图片记录:{}", spuCode);
                } else {
                    for (ImageFile imageFile : images) {
                        imageDownloadHelper.downloadSingleImageToDir(imageFile, spuDir);
                    }
                }
            }
        } catch (Exception e) {
            log.error("下载SPU图片失败: {}",spuCode,e);
        }

        return spuDir;

    }

    /**
     * 处理下载异常
     */
    private File handleDownloadException(Throwable throwable, VisualImagePackage visualImagePackage){
        log.error("下载图片失败: visualImagePackage={}, error={}",JSONObject.toJSONString(visualImagePackage),throwable.getMessage());
        return null;
    }

    /**
     * 等待所有下载任务完成
     */
    private List<File> waitForDownloadCompletion(List<CompletableFuture<File>> futures) {
        if(CollectionUtil.isEmpty(futures)){
            return null;
        }
        return futures.stream().map(future ->{
            try {
                return future.join();
            } catch (CompletionException e) {
                // 捕获异步任务中的异常
                Throwable cause = e.getCause(); // 获取原始异常
                log.error("下载任务执行失败: 原因={}",(cause!=null && StringUtils.isNotBlank(cause.getMessage()) ? cause.getMessage() : "未知"));
            }
            return null;
        }).collect(Collectors.toList());
    }

    private File createTempDirectory(Long taskId) throws IOException {
        String tempDirStr = FileUtils.getTempDirectoryPath() + File.separator +
                "download_" + taskId + File.separator +
                PURE_DATETIME_PATTERN.format(LocalDateTime.now());

        File tempDir = new File(tempDirStr);
        FileUtils.forceMkdir(tempDir);
        return tempDir;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(File tempDir) {
        try {
            if(tempDir!=null){
                FileUtils.deleteDirectory(tempDir);
            }
        } catch (IOException e) {
            log.error("清理临时文件失败:"+tempDir.getAbsolutePath(),e);
        }
    }
}
