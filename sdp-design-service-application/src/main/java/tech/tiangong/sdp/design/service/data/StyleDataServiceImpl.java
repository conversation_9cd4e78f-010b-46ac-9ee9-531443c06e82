package tech.tiangong.sdp.design.service.data;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.zjkj.aigc.common.resp.FloatPrintTemplateInnerDetailVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import team.aikero.admin.common.vo.AttributeVo;
import team.aikero.admin.common.vo.DictVo;
import team.aikero.blade.core.exception.BusinessException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.ProductUpdateTypeEnum;
import tech.tiangong.sdp.design.helper.PopProductHelper;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.AigcDigitalPrintHelper;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.vo.req.PushPopReq;
import tech.tiangong.sdp.design.vo.req.StyleAttributesReq;
import tech.tiangong.sdp.design.vo.req.digital.TemplateSizeDetailReq;
import tech.tiangong.sdp.design.vo.req.mq.demand.ProductUpdateMqDto;
import tech.tiangong.sdp.design.vo.req.zj.prod.StyleSizeReq;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 款式数据处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StyleDataServiceImpl {

    private final DesignStyleRepository designStyleRepository;
    private final SpotSpuRepository spotSpuRepository;
    private final SpotSpuDetailRepository spotSpuDetailRepository;
    private final DigitalPrintingStyleRepository digitalPrintingStyleRepository;
    private final DigitalPrintingStyleDetailRepository digitalPrintingStyleDetailRepository;
    private final MqProducer mqProducer;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final AigcDigitalPrintHelper aigcDigitalPrintHelper;
    private final PopProductHelper popProductHelper;

    // 缓存品类对应的尺码明细
    private final Map<String, List<ProductUpdateMqDto.ProductSizeDetail>> categorySizeDetailCache = new ConcurrentHashMap<>();


    @Transactional(rollbackFor = Exception.class)
    public void initAttribute(StyleAttributesReq req) {
        // 参数验证
        if (req == null) {
            throw new BusinessException("请求参数不能为空");
        }
        if (CollectionUtil.isEmpty(req.getStyleCodeList())) {
            throw new BusinessException("源款式编码列表不能为空");
        }
        if (CollectionUtil.isEmpty(req.getStyleCodes())) {
            throw new BusinessException("目标款式编码列表不能为空");
        }

        switch (req.getType()) {
            case 1:
                //设计款 (List类型attributes)
                List<DesignStyle> templateStyles = designStyleRepository.listByStyleCodes(req.getStyleCodeList());
                Map<String, DesignStyle> templateMap = templateStyles.stream()
                        .filter(style -> CollectionUtil.isNotEmpty(style.getAttributes()))
                        .collect(Collectors.toMap(DesignStyle::getCategory, style -> style, (existing, replacement) -> existing));

                if (templateMap.isEmpty()) {
                    log.warn("未找到有效的模板设计款，源编码列表: {}", req.getStyleCodeList());
                    return;
                }

                List<DesignStyle> designStyles = designStyleRepository.listByStyleCodes(req.getStyleCodes());
                if (CollectionUtil.isEmpty(designStyles)) {
                    log.info("目标设计款列表为空");
                    return;
                }

                int updateCount = 0;
                for (DesignStyle designStyle : designStyles) {
                    if (CollectionUtil.isEmpty(designStyle.getAttributes()) || req.getForceOverride()) {
                        DesignStyle template = templateMap.get(designStyle.getCategory());
                        if (template != null) {
                            designStyle.setAttributes(template.getAttributes());
                            updateCount++;
                            log.debug("款式 {} (品类: {}) 使用模板 {} 的属性",
                                    designStyle.getStyleCode(), designStyle.getCategory(), template.getStyleCode());
                        }
                    }
                }

                if (updateCount > 0) {
                    designStyleRepository.updateBatchById(designStyles);
                    log.info("成功更新{}个设计款的属性，使用模板数量: {}", updateCount, templateMap.size());
                } else {
                    log.info("没有符合条件的设计款需要更新");
                }
                break;

            case 2:
                //现货款 (List类型attributes)
                // 🔧 简化：先获取模板SpotSpu，按品类分组
                List<SpotSpu> templateSpuList = spotSpuRepository.listByStyleCodes(req.getStyleCodeList());
                if (CollectionUtil.isEmpty(templateSpuList)) {
                    log.warn("未找到模板现货款，源编码列表: {}", req.getStyleCodeList());
                    return;
                }

                Map<String, SpotSpu> templateSpuMap = templateSpuList.stream()
                        .collect(Collectors.toMap(SpotSpu::getCategory, spu -> spu, (existing, replacement) -> existing));

                // 批量查询模板详情并构建品类映射
                List<SpotSpuDetail> templateDetails = spotSpuDetailRepository.listBySpotSpuIds(
                        templateSpuList.stream().map(SpotSpu::getSpotSpuId).collect(Collectors.toList()));

                Map<String, SpotSpuDetail> templateDetailMap = templateDetails.stream()
                        .filter(detail -> CollectionUtil.isNotEmpty(detail.getAttributes()))
                        .collect(Collectors.toMap(
                                detail -> templateSpuMap.values().stream()
                                        .filter(spu -> spu.getSpotSpuId().equals(detail.getSpotSpuId()))
                                        .map(SpotSpu::getCategory)
                                        .findFirst().orElse(""),
                                detail -> detail,
                                (existing, replacement) -> existing
                        ));

                if (templateDetailMap.isEmpty()) {
                    log.warn("未找到有效的模板现货款，源编码列表: {}", req.getStyleCodeList());
                    return;
                }

                // 处理目标现货款
                List<SpotSpu> spotSpuList = spotSpuRepository.listByStyleCodes(req.getStyleCodes());
                if (CollectionUtil.isEmpty(spotSpuList)) {
                    log.info("目标现货款列表为空");
                    return;
                }

                List<SpotSpu> validSpuList = spotSpuList.stream()
                        .filter(spu -> templateDetailMap.containsKey(spu.getCategory()))
                        .toList();

                if (validSpuList.isEmpty()) {
                    log.warn("没有找到品类匹配的现货款，可用模板品类: {}", templateDetailMap.keySet());
                    return;
                }

                List<SpotSpuDetail> spotSpuDetails = spotSpuDetailRepository.listBySpotSpuIds(
                        validSpuList.stream().map(SpotSpu::getSpotSpuId).collect(Collectors.toList()));

                // 🔧 简化：构建SpuId到品类的映射，避免重复查找
                Map<Long, String> spuIdToCategoryMap = validSpuList.stream()
                        .collect(Collectors.toMap(SpotSpu::getSpotSpuId, SpotSpu::getCategory));

                // ✨ 新增：强制覆盖逻辑判断
                List<SpotSpuDetail> needUpdateDetails = new ArrayList<>();
                for (SpotSpuDetail spotSpuDetail : spotSpuDetails) {
                    if (CollectionUtil.isEmpty(spotSpuDetail.getAttributes()) || req.getForceOverride()) {
                        String category = spuIdToCategoryMap.get(spotSpuDetail.getSpotSpuId());
                        if (category != null) {
                            SpotSpuDetail template = templateDetailMap.get(category);
                            if (template != null) {
                                spotSpuDetail.setAttributes(template.getAttributes());
                                needUpdateDetails.add(spotSpuDetail);
                            }
                        }
                    }
                }

                if (!needUpdateDetails.isEmpty()) {
                    spotSpuDetailRepository.updateBatchById(needUpdateDetails);
                    log.info("成功更新{}个现货款的属性，使用模板数量: {}", needUpdateDetails.size(), templateDetailMap.size());
                } else {
                    log.info("没有符合条件的现货款需要更新");
                }
                break;

            case 3:
                // 数码印花款 (String类型attributes)
                List<DigitalPrintingStyle> digitalPrintingStyles =
                        digitalPrintingStyleRepository.listByStyleCodes(req.getStyleCodes());
                if (CollectionUtil.isEmpty(digitalPrintingStyles)) {
                    log.info("目标数码印花款列表为空");
                    return;
                }

                // 按modelNumber分组
                Map<String, List<DigitalPrintingStyle>> stylesByModelNumber = digitalPrintingStyles.stream()
                        .filter(style -> StrUtil.isNotBlank(style.getModelNumber()))
                        .collect(Collectors.groupingBy(DigitalPrintingStyle::getModelNumber));

                if (stylesByModelNumber.isEmpty()) {
                    log.warn("所有目标数码印花款的modelNumber都为空，无法处理");
                    return;
                }

                // 获取所有款式的Detail信息
                List<Long> styleIds = digitalPrintingStyles.stream()
                        .map(DigitalPrintingStyle::getPrintingStyleId)
                        .collect(Collectors.toList());
                List<DigitalPrintingStyleDetail> digitalPrintingStyleDetails =
                        digitalPrintingStyleDetailRepository.listByStyleIds(styleIds);

                // 构建款式ID到Detail的映射
                Map<Long, DigitalPrintingStyleDetail> detailMap = digitalPrintingStyleDetails.stream()
                        .collect(Collectors.toMap(DigitalPrintingStyleDetail::getPrintingStyleId, Function.identity()));

                List<DigitalPrintingStyleDetail> needUpdateStyleDetails = new ArrayList<>();
                Map<String, String> modelNumberAttributesCache = new HashMap<>(); // 缓存modelNumber对应的属性

                // 按modelNumber分组处理
                for (Map.Entry<String, List<DigitalPrintingStyle>> entry : stylesByModelNumber.entrySet()) {
                    String modelNumber = entry.getKey();
                    List<DigitalPrintingStyle> stylesWithSameModel = entry.getValue();

                    // 获取该modelNumber对应的属性
                    String attributesJson = modelNumberAttributesCache.get(modelNumber);
                    if (StringUtils.isBlank(attributesJson)) {
                        try {
                            FloatPrintTemplateInnerDetailVO detailVO =
                                    aigcDigitalPrintHelper.getByPatternCode(modelNumber);
                            if (detailVO != null && CollectionUtil.isNotEmpty(detailVO.getAttributes())) {
                                // 将List<AttributeVo>转换为JSON字符串
                                attributesJson = JSON.toJSONString(detailVO.getAttributes());
                                modelNumberAttributesCache.put(modelNumber, attributesJson);
                            } else {
                                log.warn("未获取到modelNumber为{}的属性信息", modelNumber);
                                continue; // 跳过这个modelNumber的处理
                            }
                        } catch (Exception e) {
                            log.error("调用aigcDigitalPrintHelper获取modelNumber为{}的属性信息失败", modelNumber, e);
                            continue; // 跳过这个modelNumber的处理
                        }
                    }

                    // 更新该modelNumber对应的所有款式的属性
                    for (DigitalPrintingStyle style : stylesWithSameModel) {
                        DigitalPrintingStyleDetail detail = detailMap.get(style.getPrintingStyleId());
                        if (detail == null) {
                            log.warn("未找到款式{}的详情信息", style.getStyleCode());
                            continue;
                        }

                        if (StrUtil.isBlank(detail.getAttributes()) || req.getForceOverride()) {
                            detail.setAttributes(attributesJson);
                            needUpdateStyleDetails.add(detail);
                        }
                    }
                }

                if (!needUpdateStyleDetails.isEmpty()) {
                    digitalPrintingStyleDetailRepository.updateBatchById(needUpdateStyleDetails);
                    log.info("成功更新{}个数码印花款的属性，涉及{}个不同的modelNumber",
                            needUpdateStyleDetails.size(), modelNumberAttributesCache.size());
                } else {
                    log.info("没有符合条件的数码印花款需要更新");
                }
                break;

            default:
                log.info("不支持的款式类型: {}", req.getType());
                throw new BusinessException("不支持的款式类型: " + req.getType() + "，支持类型：1-设计款，2-现货款，3-数码印花款");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void pushPopData(PushPopReq req) {
        // 参数验证
        if (req == null) {
            throw new BusinessException("请求参数不能为空");
        }
        if (CollectionUtil.isEmpty(req.getStyleCodes())) {
            throw new BusinessException("款式编码列表不能为空");
        }
        if (req.getOperationType() == null) {
            throw new BusinessException("操作类型不能为空");
        }

        // 根据操作类型处理
        switch (req.getOperationType()) {
            case 1:
                // 尺码明细操作
                processSizeDetails(req);
                break;
            case 2:
                // 商品属性
                processProductAttributes(req);
                break;
            case 3:
                // 款式基本信息
                processBaseInfo(req);
                break;
            default:
                log.error("不支持的操作类型: {}", req.getOperationType());
                throw new BusinessException("不支持的操作类型: " + req.getOperationType());
        }
    }

    /**
     * 处理尺码明细推送
     */
    private void processSizeDetails(PushPopReq req) {
        switch (req.getType()) {
            case 1:
                // 设计款
                processDesignStyleSizeDetails(req.getStyleCodes());
                break;
            case 2:
                // 现货款
                processSpotSpuSizeDetails(req.getStyleCodes());
                break;
            case 3:
                processDigitalPrintingSizeDetails(req.getStyleCodes());
                break;
            default:
                log.error("不支持的款式类型: {}", req.getType());
                throw new BusinessException("不支持的款式类型: " + req.getType());
        }
    }

    /**
     * 处理设计款尺码明细
     */
    private void processDesignStyleSizeDetails(List<String> styleCodes) {
        List<DesignStyle> designStyles = designStyleRepository.listByStyleCodes(styleCodes);
        if (CollectionUtil.isEmpty(designStyles)) {
            log.info("未找到设计款，款式编码列表: {}", styleCodes);
            return;
        }

        // 按品类分组，避免重复处理相同品类
        Map<String, List<DesignStyle>> categoryStylesMap = designStyles.stream()
                .filter(style -> StringUtils.isNotBlank(style.getCategory())) // 添加过滤条件
                .collect(Collectors.groupingBy(DesignStyle::getCategory));

        int successCount = 0;
        int failCount = 0;

        for (Map.Entry<String, List<DesignStyle>> entry : categoryStylesMap.entrySet()) {
            String category = entry.getKey();
            List<DesignStyle> categoryStyles = entry.getValue();

            try {
                // 获取品类对应的尺码明细（使用缓存避免重复请求）
                List<ProductUpdateMqDto.ProductSizeDetail> sizeDetails = getSizeDetailsByCategory(category);
                if (CollectionUtil.isEmpty(sizeDetails)) {
                    log.warn("品类 {} 的尺码明细为空，影响 {} 个款式", category, categoryStyles.size());
                    failCount += categoryStyles.size();
                    continue;
                }

                // 为每个款式单独构建并发送MQ消息
                for (DesignStyle style : categoryStyles) {
                    try {
                        // 为单个款式构建MQ消息
                        ProductUpdateMqDto.Data data = ProductUpdateMqDto.Data.builder()
                                .opType(ProductUpdateTypeEnum.UPDATE_PRODUCT_SIZE_DETAIL.getCode())
                                .spuCode(style.getStyleCode())
                                .sizeDetails(sizeDetails)
                                .build();

                        ProductUpdateMqDto mqDTO = ProductUpdateMqDto.builder()
                                .dataList(Collections.singletonList(data))
                                .build();

                        MqMessageReq mqMessageReq = MqMessageReq.build(
                                MqBizTypeEnum.PROTOTYPE_MANAGE_CANCEL_SKC,
                                DesignMqConstant.PROTOTYPE_MANAGE_CANCEL_SKC_EXCHANGE,
                                DesignMqConstant.PROTOTYPE_MANAGE_CANCEL_SKC_ROUTING_KEY,
                                JSONUtil.toJsonStr(mqDTO)
                        );

                        mqProducer.sendOnAfterCommit(mqMessageReq);
                        successCount++;
                        log.info("成功推送设计款 {} 的尺码明细", style.getStyleCode());

                    } catch (Exception e) {
                        failCount++;
                        log.error("推送设计款 {} 尺码明细失败", style.getStyleCode(), e);
                    }
                }

            } catch (Exception e) {
                failCount += categoryStyles.size();
                log.error("处理品类 {} 下设计款尺码明细失败", category, e);
            }
        }

        log.info("设计款尺码明细推送完成: 成功 {}, 失败 {}", successCount, failCount);
    }

    /**
     * 处理现货款尺码明细
     */
    private void processSpotSpuSizeDetails(List<String> styleCodes) {
        List<SpotSpu> spotSpus = spotSpuRepository.listByStyleCodes(styleCodes);
        if (CollectionUtil.isEmpty(spotSpus)) {
            log.info("未找到现货款，款式编码列表: {}", styleCodes);
            return;
        }

        // 按品类分组，避免重复处理相同品类
        Map<String, List<SpotSpu>> categorySpusMap = spotSpus.stream()
                .filter(style -> StringUtils.isNotBlank(style.getCategory())) // 添加过滤条件
                .collect(Collectors.groupingBy(SpotSpu::getCategory));

        int successCount = 0;
        int failCount = 0;

        for (Map.Entry<String, List<SpotSpu>> entry : categorySpusMap.entrySet()) {
            String category = entry.getKey();
            List<SpotSpu> categorySpus = entry.getValue();

            try {
                // 获取品类对应的尺码明细（使用缓存避免重复请求）
                List<ProductUpdateMqDto.ProductSizeDetail> sizeDetails = getSizeDetailsByCategory(category);
                if (CollectionUtil.isEmpty(sizeDetails)) {
                    log.warn("品类 {} 的尺码明细为空，影响 {} 个现货款", category, categorySpus.size());
                    failCount += categorySpus.size();
                    continue;
                }

                // 为每个现货款单独构建并发送MQ消息
                for (SpotSpu spu : categorySpus) {
                    try {
                        // 为单个现货款构建MQ消息
                        ProductUpdateMqDto.Data data = ProductUpdateMqDto.Data.builder()
                                .opType(ProductUpdateTypeEnum.UPDATE_PRODUCT_SIZE_DETAIL.getCode())
                                .spuCode(spu.getStyleCode())
                                .sizeDetails(sizeDetails)
                                .build();

                        ProductUpdateMqDto mqDTO = ProductUpdateMqDto.builder()
                                .dataList(Collections.singletonList(data))
                                .build();

                        MqMessageReq mqMessageReq = MqMessageReq.build(
                                MqBizTypeEnum.PROTOTYPE_MANAGE_CANCEL_SKC,
                                DesignMqConstant.PROTOTYPE_MANAGE_CANCEL_SKC_EXCHANGE,
                                DesignMqConstant.PROTOTYPE_MANAGE_CANCEL_SKC_ROUTING_KEY,
                                JSONUtil.toJsonStr(mqDTO)
                        );

                        mqProducer.sendOnAfterCommit(mqMessageReq);
                        successCount++;
                        log.info("成功推送现货款 {} 的尺码明细", spu.getStyleCode());

                    } catch (Exception e) {
                        failCount++;
                        log.error("推送现货款 {} 尺码明细失败", spu.getStyleCode(), e);
                    }
                }

            } catch (Exception e) {
                failCount += categorySpus.size();
                log.error("处理品类 {} 下现货款尺码明细失败", category, e);
            }
        }

        log.info("现货款尺码明细推送完成: 成功 {}, 失败 {}", successCount, failCount);
    }

    /**
     * 根据品类获取尺码明细（使用缓存）
     */
    private List<ProductUpdateMqDto.ProductSizeDetail> getSizeDetailsByCategory(String category) {
        // 先从缓存中获取
        if (categorySizeDetailCache.containsKey(category)) {
            return categorySizeDetailCache.get(category);
        }
        log.info("品类 {} 的字典配置缓存中尺码明细不存在", category);
        // 查询字典获取尺码明细
        DictVo dictVoByCategory = dictValueRemoteHelper.getDictVoByCategory(category);
        if (dictVoByCategory == null) {
            log.warn("未找到品类 {} 的字典配置", category);
            // 缓存空结果避免重复查询
            // 缓存空结果避免重复查询
            categorySizeDetailCache.put(category, Collections.emptyList());
            return Collections.emptyList();
        }

        // 转换尺码明细
        List<ProductUpdateMqDto.ProductSizeDetail> sizeDetails = convertSizeDetails(dictVoByCategory);
        log.info("品类 {} 的字典配置缓存中尺码明细转换完成", category);
        // 缓存结果
        categorySizeDetailCache.put(category, sizeDetails);

        return sizeDetails;
    }

    /**
     * 从字典中转换尺码明细
     */
    private List<ProductUpdateMqDto.ProductSizeDetail> convertSizeDetails(DictVo dictVoByCategory) {
        if (dictVoByCategory == null || CollectionUtil.isEmpty(dictVoByCategory.getAttributes())) {
            return Collections.emptyList();
        }

        Optional<AttributeVo> codeAttributeOpt = dictVoByCategory.getAttributes().stream()
                .filter(attr -> attr != null && "CMMXB".equals(attr.getCode()))
                .findFirst();

        if (!codeAttributeOpt.isPresent() || StringUtils.isBlank(codeAttributeOpt.get().getName())) {
            return Collections.emptyList();
        }

        String jsonString = codeAttributeOpt.get().getName();
        List<TemplateSizeDetailReq> sizeDetailReqs;

        try {
            // 解析JSON
            sizeDetailReqs = JSON.parseArray(jsonString, TemplateSizeDetailReq.class);
        } catch (Exception e) {
            log.error("JSON解析失败，原始字符串: {}", jsonString, e);
            throw new RuntimeException("字典里的产品尺寸详情JSON格式错误,请检查配置", e);
        }

        // 转换为 ProductSizeDetail 列表
        return sizeDetailReqs.stream()
                .map(templateReq -> {
                    ProductUpdateMqDto.ProductSizeDetail productSizeDetail =
                            ProductUpdateMqDto.ProductSizeDetail.builder().partName(templateReq.getPartName()).build();

                    // 转换 SizeData 列表到 ProductSizeJson 列表
                    if (templateReq.getSizeList() != null) {
                        List<StyleSizeReq.SizeData> productSizeJsons = templateReq.getSizeList().stream()
                                .map(sizeData -> {
                                    StyleSizeReq.SizeData productSizeJson = new StyleSizeReq.SizeData();
                                    productSizeJson.setSize(sizeData.getSize());
                                    // 将 BigDecimal 转换为 String
                                    productSizeJson.setData(sizeData.getData());
                                    return productSizeJson;
                                })
                                .collect(Collectors.toList());
                        productSizeDetail.setSizeJson(productSizeJsons);
                    }

                    return productSizeDetail;
                })
                .collect(Collectors.toList());
    }



    /**
     * 处理商品属性推送
     */
    private void processProductAttributes(PushPopReq req) {
        switch (req.getType()) {
            case 1:
                // 设计款
                processDesignStyleProductAttributes(req.getStyleCodes());
                break;
            case 2:
                // 现货款
                processSpotSpuProductAttributes(req.getStyleCodes());
                break;
            case 3:
                // 数码印花款
                processDigitalPrintingProductAttributes(req.getStyleCodes());
                break;
            default:
                log.error("不支持的款式类型: {}", req.getType());
                throw new BusinessException("不支持的款式类型: " + req.getType());
        }
    }

    /**
     * 处理设计款商品属性推送
     */
    private void processDesignStyleProductAttributes(List<String> styleCodes) {
        List<DesignStyle> designStyles = designStyleRepository.listByStyleCodes(styleCodes);
        if (CollectionUtil.isEmpty(designStyles)) {
            log.info("未找到设计款，款式编码列表: {}", styleCodes);
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (DesignStyle designStyle : designStyles) {
            try {
                popProductHelper.updateProductAttribute(designStyle.getStyleCode(), designStyle.getAttributes());
                successCount++;
                log.info("成功推送设计款 {} 的商品属性", designStyle.getStyleCode());
            } catch (Exception e) {
                failCount++;
                log.error("推送设计款 {} 商品属性失败", designStyle.getStyleCode(), e);
            }
        }

        log.info("设计款商品属性推送完成: 成功 {}, 失败 {}", successCount, failCount);
    }

    /**
     * 处理现货款商品属性推送
     */
    private void processSpotSpuProductAttributes(List<String> styleCodes) {
        List<SpotSpu> spotSpuList = spotSpuRepository.getAllocationListByStyleCodes(styleCodes);
        if (CollectionUtil.isEmpty(spotSpuList)) {
            log.info("未找到现货款，款式编码列表: {}", styleCodes);
            return;
        }

        List<Long> spotIds = spotSpuList.stream().map(SpotSpu::getSpotSpuId).toList();
        List<SpotSpuDetail> spotSpuDetailList = spotSpuDetailRepository.listBySpotSpuIds(spotIds);
        Map<Long, SpotSpuDetail> spotSpuDetailMap = spotSpuDetailList.stream()
                .collect(Collectors.toMap(SpotSpuDetail::getSpotSpuId, v -> v));

        int successCount = 0;
        int failCount = 0;

        for (SpotSpu spotSpu : spotSpuList) {
            try {
                SpotSpuDetail detail = spotSpuDetailMap.get(spotSpu.getSpotSpuId());
                if (detail != null) {
                    popProductHelper.updateProductAttribute(spotSpu.getStyleCode(), detail.getAttributes());
                    successCount++;
                    log.info("成功推送现货款 {} 的商品属性", spotSpu.getStyleCode());
                } else {
                    failCount++;
                    log.warn("未找到现货款 {} 的详情信息", spotSpu.getStyleCode());
                }
            } catch (Exception e) {
                failCount++;
                log.error("推送现货款 {} 商品属性失败", spotSpu.getStyleCode(), e);
            }
        }

        log.info("现货款商品属性推送完成: 成功 {}, 失败 {}", successCount, failCount);
    }

    /**
     * 处理数码印花款商品属性推送
     */
    private void processDigitalPrintingProductAttributes(List<String> styleCodes) {
        List<DigitalPrintingStyle> digitalPrintingStyles = digitalPrintingStyleRepository.listByStyleCodes(styleCodes);
        if (CollectionUtil.isEmpty(digitalPrintingStyles)) {
            log.info("未找到数码印花款，款式编码列表: {}", styleCodes);
            return;
        }

        // 获取所有款式的Detail信息
        List<Long> styleIds = digitalPrintingStyles.stream()
                .map(DigitalPrintingStyle::getPrintingStyleId)
                .collect(Collectors.toList());
        List<DigitalPrintingStyleDetail> digitalPrintingStyleDetails =
                digitalPrintingStyleDetailRepository.listByStyleIds(styleIds);

        // 构建款式ID到Detail的映射
        Map<Long, DigitalPrintingStyleDetail> detailMap = digitalPrintingStyleDetails.stream()
                .collect(Collectors.toMap(DigitalPrintingStyleDetail::getPrintingStyleId, Function.identity()));

        int successCount = 0;
        int failCount = 0;

        for (DigitalPrintingStyle style : digitalPrintingStyles) {
            try {
                DigitalPrintingStyleDetail detail = detailMap.get(style.getPrintingStyleId());
                if (detail != null && StrUtil.isNotBlank(detail.getAttributes())) {
                    // 将JSON字符串转换为List<Attribute>
                    List<tech.tiangong.sdp.design.vo.base.AttributeVo> attributes = JSON.parseArray(detail.getAttributes(), tech.tiangong.sdp.design.vo.base.AttributeVo.class);
                    popProductHelper.updateProductAttribute(style.getStyleCode(), attributes);
                    successCount++;
                    log.info("成功推送数码印花款 {} 的商品属性", style.getStyleCode());
                } else {
                    failCount++;
                    log.warn("未找到数码印花款 {} 的属性信息", style.getStyleCode());
                }
            } catch (Exception e) {
                failCount++;
                log.error("推送数码印花款 {} 商品属性失败", style.getStyleCode(), e);
            }
        }

        log.info("数码印花款商品属性推送完成: 成功 {}, 失败 {}", successCount, failCount);
    }




    /**
     * 处理数码印花款尺码明细推送
     */
    private void processDigitalPrintingSizeDetails(List<String> styleCodes) {
        List<DigitalPrintingStyle> digitalPrintingStyles = digitalPrintingStyleRepository.listByStyleCodes(styleCodes);
        if (CollectionUtil.isEmpty(digitalPrintingStyles)) {
            log.info("未找到数码印花款，款式编码列表: {}", styleCodes);
            return;
        }

        // 按modelNumber分组，避免重复调用接口
        Map<String, List<DigitalPrintingStyle>> stylesByModelNumber = digitalPrintingStyles.stream()
                .filter(style -> StrUtil.isNotBlank(style.getModelNumber()))
                .collect(Collectors.groupingBy(DigitalPrintingStyle::getModelNumber));

        if (stylesByModelNumber.isEmpty()) {
            log.warn("所有目标数码印花款的modelNumber都为空，无法处理");
            return;
        }

        int successCount = 0;
        int failCount = 0;
        Map<String, List<ProductUpdateMqDto.ProductSizeDetail>> modelNumberSizeDetailsCache = new HashMap<>();

        // 按modelNumber分组处理
        for (Map.Entry<String, List<DigitalPrintingStyle>> entry : stylesByModelNumber.entrySet()) {
            String modelNumber = entry.getKey();
            List<DigitalPrintingStyle> stylesWithSameModel = entry.getValue();

            // 获取该modelNumber对应的尺码明细
            List<ProductUpdateMqDto.ProductSizeDetail> sizeDetails = modelNumberSizeDetailsCache.get(modelNumber);
            if (sizeDetails == null) {
                try {
                    FloatPrintTemplateInnerDetailVO detailVO = aigcDigitalPrintHelper.getByPatternCode(modelNumber);
                    if (detailVO != null && CollectionUtil.isNotEmpty(detailVO.getSizeDetails())) {
                        // 将FloatPrintTemplateInnerDetailVO.SizeDetail转换为ProductUpdateMqDto.ProductSizeDetail
                        sizeDetails = convertToProductSizeDetails(detailVO.getSizeDetails());
                        modelNumberSizeDetailsCache.put(modelNumber, sizeDetails);
                    } else {
                        log.warn("未获取到modelNumber为{}的尺码明细信息", modelNumber);
                        failCount += stylesWithSameModel.size();
                        continue; // 跳过这个modelNumber的处理
                    }
                } catch (Exception e) {
                    log.error("调用aigcDigitalPrintHelper获取modelNumber为{}的尺码明细失败", modelNumber, e);
                    failCount += stylesWithSameModel.size();
                    continue; // 跳过这个modelNumber的处理
                }
            }

            // 为该modelNumber对应的所有款式推送尺码明细
            for (DigitalPrintingStyle style : stylesWithSameModel) {
                try {
                    // 构建MQ消息
                    ProductUpdateMqDto.Data data = ProductUpdateMqDto.Data.builder()
                            .opType(ProductUpdateTypeEnum.UPDATE_PRODUCT_SIZE_DETAIL.getCode())
                            .spuCode(style.getStyleCode())
                            .sizeDetails(sizeDetails)
                            .build();

                    ProductUpdateMqDto mqDTO = ProductUpdateMqDto.builder()
                            .dataList(Collections.singletonList(data))
                            .build();

                    MqMessageReq mqMessageReq = MqMessageReq.build(
                            MqBizTypeEnum.PROTOTYPE_MANAGE_CANCEL_SKC,
                            DesignMqConstant.PROTOTYPE_MANAGE_CANCEL_SKC_EXCHANGE,
                            DesignMqConstant.PROTOTYPE_MANAGE_CANCEL_SKC_ROUTING_KEY,
                            JSONUtil.toJsonStr(mqDTO)
                    );

                    mqProducer.sendOnAfterCommit(mqMessageReq);
                    successCount++;
                    log.info("成功推送数码印花款 {} 的尺码明细", style.getStyleCode());
                } catch (Exception e) {
                    failCount++;
                    log.error("推送数码印花款 {} 尺码明细失败", style.getStyleCode(), e);
                }
            }
        }

        log.info("数码印花款尺码明细推送完成: 成功 {}, 失败 {}", successCount, failCount);
    }


    public List<ProductUpdateMqDto.ProductSizeDetail> convertToProductSizeDetails(List<com.zjkj.aigc.common.req.TemplateSizeDetailReq> sizeDetails) {
        return sizeDetails.stream()
                .map(templateReq -> {
                    ProductUpdateMqDto.ProductSizeDetail productSizeDetail =
                            ProductUpdateMqDto.ProductSizeDetail.builder().partName(templateReq.getPartName()).build();

                    // 转换 SizeData 列表到 ProductSizeJson 列表
                    if (templateReq.getSizeList() != null) {
                        List<StyleSizeReq.SizeData> productSizeJsons = templateReq.getSizeList().stream()
                                .map(sizeData -> {
                                    StyleSizeReq.SizeData productSizeJson = new StyleSizeReq.SizeData();
                                    productSizeJson.setSize(sizeData.getSize());
                                    // 将 BigDecimal 转换为 String
                                    productSizeJson.setData(sizeData.getData());
                                    return productSizeJson;
                                })
                                .collect(Collectors.toList());
                        productSizeDetail.setSizeJson(productSizeJsons);
                    }

                    return productSizeDetail;
                })
                .collect(Collectors.toList());
    }



    /**
     * 处理款式基本信息推送
     */
    private void processBaseInfo(PushPopReq req) {
        switch (req.getType()) {
            case 1:
                // 设计款
                processDesignStyleBaseInfo(req.getStyleCodes());
                break;
            case 2:
                // 现货款
                processSpotSpuBaseInfo(req.getStyleCodes());
                break;
            case 3:
                // 数码印花款
                processDigitalPrintingBaseInfo(req.getStyleCodes());
                break;
            default:
                log.error("不支持的款式类型: {}", req.getType());
                throw new BusinessException("不支持的款式类型: " + req.getType());
        }
    }

    /**
     * 处理设计款基本信息推送
     */
    private void processDesignStyleBaseInfo(List<String> styleCodes) {
        List<DesignStyle> designStyles = designStyleRepository.listByStyleCodes(styleCodes);
        if (CollectionUtil.isEmpty(designStyles)) {
            log.info("未找到设计款，款式编码列表: {}", styleCodes);
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (DesignStyle designStyle : designStyles) {
            try {
                // 构建基本信息
                ProductUpdateMqDto.SpuBaseInfo spuBaseInfo = ProductUpdateMqDto.SpuBaseInfo.builder()
                        .clothingStyleName(designStyle.getClothingStyleName())
                        .clothingStyleCode(designStyle.getClothingStyleCode())
                        .planningType(designStyle.getPlanningType())
                        .marketCode(designStyle.getMarketCode())
                        .marketSeriesCode(designStyle.getMarketSeriesCode())
                        .waves(designStyle.getWaveBandName())
                        .goodsRepType(designStyle.getPalletTypeName())
                        .weaveModeCode(designStyle.getWeaveModeCode())
                        .weaveMode(designStyle.getWeaveMode())
                        .qualityLevel(designStyle.getQualityLevel())
                        .qualityLevelCode(designStyle.getQualityLevelCode())
                        .productThemeCode(designStyle.getProductThemeCode())
                        .productThemeName(designStyle.getProductThemeName())
                        .shopId(designStyle.getStoreId())
                        .shopName(designStyle.getStoreName())
                        .countrys(Collections.singletonList(designStyle.getCountrySiteCode()))
                        .buyerId(designStyle.getBuyerId())
                        .buyerName(designStyle.getBuyerName())
                        .productLink(designStyle.getReferLink())
                        .build();

                popProductHelper.updateProductBaseInfo(designStyle.getStyleCode(), spuBaseInfo);
                successCount++;
                log.info("成功推送设计款 {} 的基本信息", designStyle.getStyleCode());
            } catch (Exception e) {
                failCount++;
                log.error("推送设计款 {} 基本信息失败", designStyle.getStyleCode(), e);
            }
        }

        log.info("设计款基本信息推送完成: 成功 {}, 失败 {}", successCount, failCount);
    }

    /**
     * 处理现货款基本信息推送
     */
    private void processSpotSpuBaseInfo(List<String> styleCodes) {
        List<SpotSpu> spotSpus = spotSpuRepository.listByStyleCodes(styleCodes);
        if (CollectionUtil.isEmpty(spotSpus)) {
            log.info("未找到现货款，款式编码列表: {}", styleCodes);
            return;
        }

        // 获取现货款详情信息
        List<Long> spotIds = spotSpus.stream().map(SpotSpu::getSpotSpuId).toList();
        List<SpotSpuDetail> spotSpuDetailList = spotSpuDetailRepository.listBySpotSpuIds(spotIds);
        Map<Long, SpotSpuDetail> spotSpuDetailMap = spotSpuDetailList.stream()
                .collect(Collectors.toMap(SpotSpuDetail::getSpotSpuId, v -> v));

        int successCount = 0;
        int failCount = 0;

        for (SpotSpu spotSpu : spotSpus) {
            try {
                SpotSpuDetail detail = spotSpuDetailMap.get(spotSpu.getSpotSpuId());
                if (detail != null) {
                    // 构建基本信息
                    ProductUpdateMqDto.SpuBaseInfo spuBaseInfo = ProductUpdateMqDto.SpuBaseInfo.builder()
                            .clothingStyleName(spotSpu.getClothingStyleName())
                            .clothingStyleCode(spotSpu.getClothingStyleCode())
                            .planningType(spotSpu.getPlanningType())
                            .marketCode(spotSpu.getMarketCode())
                            .marketSeriesCode(spotSpu.getMarketSeriesCode())
                            .waves(spotSpu.getWaveBandName())
                            .goodsRepType(spotSpu.getPalletTypeName())
                            .weaveModeCode(spotSpu.getWeaveModeCode())
                            .weaveMode(spotSpu.getWeaveMode())
                            .qualityLevel(spotSpu.getQualityLevel())
                            .qualityLevelCode(spotSpu.getQualityLevelCode())
                            .productThemeCode(spotSpu.getProductThemeCode())
                            .productThemeName(spotSpu.getProductThemeName())
                            .shopId(spotSpu.getStoreId())
                            .shopName(spotSpu.getStoreName())
                            .countrys(Collections.singletonList(spotSpu.getCountrySiteCode()))
                            .spotTypeCode(spotSpu.getSpotTypeCode())
                            .buyerId(spotSpu.getBuyerId())
                            .buyerName(spotSpu.getBuyerName())
                            .productLink(detail.getProductLink())
                            .spuName(spotSpu.getSpuName())
                            .spuNameTrans(spotSpu.getSpuNameTrans())
                            .build();

                    popProductHelper.updateProductBaseInfo(spotSpu.getStyleCode(), spuBaseInfo);
                    successCount++;
                    log.info("成功推送现货款 {} 的基本信息", spotSpu.getStyleCode());
                } else {
                    failCount++;
                    log.warn("未找到现货款 {} 的详情信息", spotSpu.getStyleCode());
                }
            } catch (Exception e) {
                failCount++;
                log.error("推送现货款 {} 基本信息失败", spotSpu.getStyleCode(), e);
            }
        }

        log.info("现货款基本信息推送完成: 成功 {}, 失败 {}", successCount, failCount);
    }

    /**
     * 处理数码印花款基本信息推送
     */
    private void processDigitalPrintingBaseInfo(List<String> styleCodes) {
        List<DigitalPrintingStyle> digitalPrintingStyles = digitalPrintingStyleRepository.listByStyleCodes(styleCodes);
        if (CollectionUtil.isEmpty(digitalPrintingStyles)) {
            log.info("未找到数码印花款，款式编码列表: {}", styleCodes);
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (DigitalPrintingStyle style : digitalPrintingStyles) {
            try {
                // 数码印花款基本信息推送（根据实际字段调整）
                // ProductUpdateMqDto.SpuBaseInfo spuBaseInfo = ProductUpdateMqDto.SpuBaseInfo.builder()
                        // 根据数码印花款的实际字段设置基本信息
                        // .productLink(style.getReferLink()) // 示例字段
                        // .build();

                // popProductHelper.updateProductBaseInfo(style.getStyleCode(), spuBaseInfo);
                successCount++;
                log.info("成功推送数码印花款 {} 的基本信息", style.getStyleCode());
            } catch (Exception e) {
                failCount++;
                log.error("推送数码印花款 {} 基本信息失败", style.getStyleCode(), e);
            }
        }

        log.info("数码印花款基本信息推送完成: 成功 {}, 失败 {}", successCount, failCount);
    }
}
