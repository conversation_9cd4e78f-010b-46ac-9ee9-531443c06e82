package tech.tiangong.sdp.design.business.bom.notification;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.zjkj.scf.bundle.common.dto.demand.enums.DemandCategory1Enum;
import com.zjkj.scf.bundle.common.dto.demand.enums.DemandCraftsRequireEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import tech.tiangong.sdp.clothes.mq.CraftDemandMqConstant;
import tech.tiangong.sdp.clothes.mq.body.BomOrderCraftDemandSyncMessage;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.CraftDemandInfo;
import tech.tiangong.sdp.design.entity.MaterialPurchaseFollow;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.repository.CraftDemandInfoRepository;
import tech.tiangong.sdp.design.repository.MaterialPurchaseFollowRepository;
import tech.tiangong.sdp.design.service.OrderMaterialFollowService;
import tech.tiangong.sdp.design.vo.dto.bom.ReplenishPreCuttingCraftDto;
import tech.tiangong.sdp.design.vo.req.ordermaterial.MaterialDemandReq;
import tech.tiangong.sdp.design.vo.req.ordermaterial.MaterialOrderToCraftReq;
import tech.tiangong.sdp.design.vo.resp.ordermaterial.MaterialDemandVo;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ReplenishPreCuttingCraftNotification implements Notification<ReplenishPreCuttingCraftDto> {
    private List<ReplenishPreCuttingCraftDto> replenishPreCuttingCraftDtoList = new LinkedList<>();
    private final MqProducer mqProducer = SpringUtil.getBean(MqProducer.class);
    private final OrderMaterialFollowService orderMaterialFollowService = SpringUtil.getBean(OrderMaterialFollowService.class);
    private final MaterialPurchaseFollowRepository materialPurchaseFollowRepository = SpringUtil.getBean(MaterialPurchaseFollowRepository.class);
    private final CraftDemandInfoRepository craftDemandInfoRepository = SpringUtil.getBean(CraftDemandInfoRepository.class);
    @Override
    public void add(ReplenishPreCuttingCraftDto replenishPreCuttingCraftDto) {
        if (replenishPreCuttingCraftDto != null) {
            replenishPreCuttingCraftDtoList.add(replenishPreCuttingCraftDto);
        }
    }

    @Override
    public void addBatch(List<ReplenishPreCuttingCraftDto> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            replenishPreCuttingCraftDtoList.addAll(list);
        }
    }

    @Override
    public List<ReplenishPreCuttingCraftDto> getAll() {
        return replenishPreCuttingCraftDtoList;
    }

    @Override
    public void send() {
        if (CollectionUtil.isEmpty(replenishPreCuttingCraftDtoList)) {
            log.info("补推裁前工艺需求到采购单为空");
            return;
        }

        Map<BomOrder, List<ReplenishPreCuttingCraftDto>> map = replenishPreCuttingCraftDtoList.stream().collect(Collectors.groupingBy(ReplenishPreCuttingCraftDto::getBomOrder));
        map.forEach((bomOrder, replenishPreCuttingCraftDtos) -> {
            List<ReplenishPreCuttingCraftDto.Craft> addCraftDemandList = replenishPreCuttingCraftDtos.stream().flatMap(list -> list.getCrafts().stream()).collect(Collectors.toList());
            log.info("补推裁前工艺需求到采购单，bomId:{},addCraftDemandList:{}", bomOrder.getBomId(), JSONUtil.toJsonStr(addCraftDemandList));
            pushPreCuttingCraftTask(bomOrder, addCraftDemandList);
        });
    }




    public void pushPreCuttingCraftTask(BomOrder bomOrder, List<ReplenishPreCuttingCraftDto.Craft> addCraftDemandList) {
        List<ReplenishPreCuttingCraftDto.Craft> preCuttingCraftList = addCraftDemandList.stream().filter(craftDemandInfo ->
                Objects.equals(craftDemandInfo.getCraftsRequire(), DemandCraftsRequireEnum.PRE_CUTTING_CRAFTS.getCode())).collect(Collectors.toList());
        log.info("补推裁前工艺需求到采购单入参 bomOrder:{}  preCuttingCraftList:{}", JSON.toJSONString(bomOrder), JSON.toJSONString(preCuttingCraftList));

        if (CollectionUtil.isEmpty(preCuttingCraftList)) {
            log.info("补推裁前工艺需求到采购单无裁前工艺需求");
            return;
        }

        MaterialOrderToCraftReq orderToCraftReq = new MaterialOrderToCraftReq();
        orderToCraftReq.setDesignCode(bomOrder.getDesignCode());

        List<MaterialDemandReq> materialDemandReqList = new ArrayList<>();

        //辅料需求，都必须有物料才能采购。如果是找料中，没回匹配的，就无需推送。 所以v3.11是一定会有materialSnapshotId
        preCuttingCraftList.stream().filter(e->Objects.nonNull(e.getMaterialSnapshotId()))
                .collect(Collectors.groupingBy(ReplenishPreCuttingCraftDto.Craft::getMaterialSnapshotId))
                .forEach((key, value) -> {
            List<Long> thirdPartyCraftDemandIdList = value.stream().map(ReplenishPreCuttingCraftDto.Craft::getThirdPartyCraftDemandId).collect(Collectors.toList());
            MaterialDemandReq materialDemandReq = new MaterialDemandReq();
            materialDemandReq.setMaterialSnapshotId(key);
            materialDemandReq.setThirdPartyCraftDemandIdList(thirdPartyCraftDemandIdList);
            materialDemandReqList.add(materialDemandReq);
        });


        orderToCraftReq.setMaterialDemandList(materialDemandReqList);
        log.info("补推裁前工艺需求到采购单查询剪版单或者齐套单下的工艺信息 参数:{}", JSON.toJSONString(orderToCraftReq));

        try {
            List<MaterialDemandVo> materialDemandVoList = orderMaterialFollowService.materialOrderToCraft(orderToCraftReq);
            log.info("补推裁前工艺需求到采购单查询剪版单或者齐套单下的工艺信息 响应 :{}", JSON.toJSONString(materialDemandVoList));
            if (CollectionUtil.isEmpty(materialDemandVoList)) {
                log.info("补推裁前工艺需求到采购单无待齐套、待发货的齐套单");
                return;
            }
            /*
            v1.0.2-p2需求先不处理-新增裁前二次工艺补推任务优化
            Set<Long> purchaseMaterialSnapshotIds = materialDemandVoList.stream().map(MaterialDemandVo::getMaterialSnapshotId).collect(Collectors.toSet());//materialPurchaseFollows.stream().map(MaterialPurchaseFollow::getMaterialSnapshotId).collect(Collectors.toSet());
            //物料下过采购的才去推履约
            preCuttingCraftList.stream().filter(v->{
                return purchaseMaterialSnapshotIds.contains(v.getMaterialSnapshotId());
            }).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(preCuttingCraftList)) {
                log.info("补推裁前工艺需求到采购单，齐套单中有对应物料的剪配版单存在，不再推送");
                return;
            }*/

            BomOrderCraftDemandSyncMessage demandSyncMessage = new BomOrderCraftDemandSyncMessage();
            demandSyncMessage.setPrototypeId(bomOrder.getPrototypeId());
            demandSyncMessage.setDesignCode(bomOrder.getDesignCode());
            demandSyncMessage.setBomId(bomOrder.getBomId());
            demandSyncMessage.setCreator(bomOrder.getCreatorId());
            demandSyncMessage.setCreatedName(bomOrder.getCreatorName());
            demandSyncMessage.setCreatedTime(bomOrder.getCreatedTime());

            Map<Long, ReplenishPreCuttingCraftDto.Craft> preCuttingCraftMap = preCuttingCraftList.stream().collect(Collectors.toMap(ReplenishPreCuttingCraftDto.Craft::getThirdPartyCraftDemandId, Function.identity(), (k1, k2) -> k1));
            List<BomOrderCraftDemandSyncMessage.CraftInfo> craftInfoList = new ArrayList<>();

            materialDemandVoList.forEach(materialDemandVo -> {
                if (CollectionUtil.isNotEmpty(materialDemandVo.getThirdPartyCraftDemandIdList())) {
                    materialDemandVo.getThirdPartyCraftDemandIdList().forEach(thirdPartyCraftDemandId -> {
                        BomOrderCraftDemandSyncMessage.CraftInfo craftInfo = new BomOrderCraftDemandSyncMessage.CraftInfo();
                        ReplenishPreCuttingCraftDto.Craft craft = preCuttingCraftMap.get(thirdPartyCraftDemandId);
                        BeanUtils.copyProperties(craft, craftInfo);
                        if (StringUtils.equals(craft.getCategory1(), DemandCategory1Enum.FABRIC.getName())) {
                            craftInfo.setCuttingOrderCode(materialDemandVo.getCuttingOrderCode());
                        } else {
                            craftInfo.setAccessoriesMatchOrderCode(materialDemandVo.getMatchOrderCode());
                        }

                        craftInfoList.add(craftInfo);
                    });
                }
            });

            demandSyncMessage.setAddCraftInfoList(craftInfoList);

            log.info("补推裁前工艺需求到采购单 mq参数:{}", JSON.toJSONString(demandSyncMessage));
            //发送消息
            MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.BOM_PRE_CRAFT_DEMAND_CLOTHES,
                    CraftDemandMqConstant.BOM_ORDER_PRE_CUTTING_CRAFT_EXCHANGE, CraftDemandMqConstant.BOM_ORDER_PRE_CUTTING_CRAFT_ROUTING_KEY,
                    JSON.toJSONString(demandSyncMessage));

            mqProducer.sendOnAfterCommit(mqMessageReq);

        } catch (Exception e) {
            log.error("补推裁前工艺需求到采购单异常", e);
        }
    }


}
