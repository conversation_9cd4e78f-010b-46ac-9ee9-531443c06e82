package tech.tiangong.sdp.design.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import tech.tiangong.sdp.design.entity.VisualTaskTryOnLog;
import tech.tiangong.sdp.design.enums.visual.VisualTaskHandleStateEnum;
import tech.tiangong.sdp.design.mapper.VisualTaskTryOnLogMapper;
import cn.yibuyun.framework.base.repository.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Repository
public class VisualTaskTryOnLogRepository extends BaseRepository<VisualTaskTryOnLogMapper, VisualTaskTryOnLog> {
    public List<VisualTaskTryOnLog> getLogByVisualTaskId(Long visualTaskId) {
       return this.list(new LambdaQueryWrapper<VisualTaskTryOnLog>()
                .eq(VisualTaskTryOnLog::getTaskId, visualTaskId));
    }

    /**
     * 获取有效的try on日志(已完成状态)
     */
    public List<VisualTaskTryOnLog> getValidLogByVisualTaskId(Long visualTaskId) {
        return this.list(new LambdaQueryWrapper<VisualTaskTryOnLog>()
                .eq(VisualTaskTryOnLog::getTaskId, visualTaskId)
                .eq(VisualTaskTryOnLog::getTryOntTaskState, VisualTaskHandleStateEnum.FINISH.getCode()));
    }

    /**
     * 批量获取有效的try on日志(已完成状态)
     */
    public List<VisualTaskTryOnLog> getValidLogByVisualTaskIdBatch(List<Long> visualTaskIdList) {
        return this.list(new LambdaQueryWrapper<VisualTaskTryOnLog>()
                .in(VisualTaskTryOnLog::getTaskId, visualTaskIdList)
                .eq(VisualTaskTryOnLog::getTryOntTaskState, VisualTaskHandleStateEnum.FINISH.getCode()));
    }

    public List<VisualTaskTryOnLog> getProcessingLogByVisualTaskId(Long visualTaskId) {
        return this.list(new LambdaQueryWrapper<VisualTaskTryOnLog>()
                .eq(VisualTaskTryOnLog::getTaskId, visualTaskId)
                .in(VisualTaskTryOnLog::getTryOntTaskState, Arrays.asList(
                        VisualTaskHandleStateEnum.WAITING_START.getCode(),
                        VisualTaskHandleStateEnum.DOING.getCode()
                )));
    }

    public List<VisualTaskTryOnLog> getProcessingAndFinishLogByTryOnDetailId(Long tryOnDetailId) {
        if (Objects.isNull(tryOnDetailId)) return Collections.emptyList();

        return this.list(new LambdaQueryWrapper<VisualTaskTryOnLog>()
                .eq(VisualTaskTryOnLog::getTryOnDetailId, tryOnDetailId)
                .in(VisualTaskTryOnLog::getTryOntTaskState, Arrays.asList(
                        VisualTaskHandleStateEnum.WAITING_START.getCode(),
                        VisualTaskHandleStateEnum.DOING.getCode(),
                        VisualTaskHandleStateEnum.FINISH.getCode()
                )));
    }
}