package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import cn.yibuyun.framework.bean.user.UserContent;
import cn.yibuyun.framework.bean.user.UserContentHolder;
import cn.yibuyun.framework.enumeration.Bool;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import cn.yibuyun.plm.design.open.vo.resp.design.AccessoriesMatchOrderOpenResp;
import cn.yibuyun.plm.design.open.vo.resp.design.CuttingOrderSimpleOpenResp;
import cn.yibuyun.plm.design.open.vo.resp.design.PurchaseApplyFollowResultOpenResp;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.yibuyun.scm.common.dto.accessories.response.ProductSpuInfoVo;
import com.yibuyun.scm.common.dto.fabric.response.CommoditySkuCollectionRespVo;
import com.yibuyun.scm.common.enums.fabric.DefaultSalesRegionEnum;
import com.zjkj.booster.common.protocol.DataResponse;
import com.zjkj.scf.bundle.common.dto.cutting.dto.mq.AccessoriesMatchOrderChangeMessage;
import com.zjkj.scf.bundle.common.dto.cutting.dto.mq.OrderStateChangeMessage;
import com.zjkj.scf.bundle.common.dto.cutting.dto.req.AccessoriesMatchOrderInnerCancelReq;
import com.zjkj.scf.bundle.common.dto.cutting.dto.req.OrderRemoteCancelReq;
import com.zjkj.scf.bundle.common.dto.cutting.dto.resp.*;
import com.zjkj.scf.bundle.common.dto.cutting.enums.MatchPlateStateEnum;
import com.zjkj.scf.bundle.common.dto.cuttinglog.dto.resp.CuttingOperatingSimpleLogVo;
import com.zjkj.scf.bundle.sdk.client.cutting.AccessoriesMatchOrderFeign;
import com.zjkj.scf.bundle.sdk.client.cuttinglog.openfeign.OperatingLogServiceFeign;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import team.aikero.admin.common.vo.DictVo;
import tech.tiangong.id.IdPool;
import tech.tiangong.sdp.clothes.client.SecondCraftDemandClient;
import tech.tiangong.sdp.clothes.enums.SecondCraftChannelEnum;
import tech.tiangong.sdp.clothes.vo.req.PurchaseCraftReq;
import tech.tiangong.sdp.clothes.vo.req.SecondCraftCancelReq;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.constant.DesignMqConstant;
import tech.tiangong.sdp.design.constant.DictConstant;
import tech.tiangong.sdp.design.converter.MaterialPurchaseFollowConverter;
import tech.tiangong.sdp.design.converter.PurchaseApplyFollowConverter;
import tech.tiangong.sdp.design.converter.PurchasePrototypeInfoConverter;
import tech.tiangong.sdp.design.entity.*;
import tech.tiangong.sdp.design.enums.*;
import tech.tiangong.sdp.design.mq.MqMessageReq;
import tech.tiangong.sdp.design.mq.enums.MqBizTypeEnum;
import tech.tiangong.sdp.design.mq.producer.MqProducer;
import tech.tiangong.sdp.design.remote.DictValueRemoteHelper;
import tech.tiangong.sdp.design.remote.ProductRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.remote.ZjNotify2OldJvRemoteHelper;
import tech.tiangong.sdp.design.repository.*;
import tech.tiangong.sdp.design.service.DesignLogService;
import tech.tiangong.sdp.design.service.MaterialPurchaseFollowService;
import tech.tiangong.sdp.design.service.PurchaseApplyFollowService;
import tech.tiangong.sdp.design.vo.dto.bom.CommodityJoinBomDto;
import tech.tiangong.sdp.design.vo.req.log.DesignLogReq;
import tech.tiangong.sdp.design.vo.req.purchase.CancelPurchaseApplyReq;
import tech.tiangong.sdp.design.vo.req.purchase.PurchaseApplyFollowPageReq;
import tech.tiangong.sdp.design.vo.req.purchase.PurchaseApplyMaterialConfirmBatchReq;
import tech.tiangong.sdp.design.vo.resp.purchase.OrderPurchaseSimpleLogVO;
import tech.tiangong.sdp.design.vo.resp.purchase.PurchaseApplyFollowPageVO;
import tech.tiangong.sdp.utils.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* 剪版需求跟进表（采购申请跟进）
* <br>CreateDate August 09,2021
* <AUTHOR>
* @since 1.0
*/
@Slf4j
@Service
@AllArgsConstructor
public class PurchaseApplyFollowServiceImpl implements PurchaseApplyFollowService {
    private final PurchaseApplyFollowRepository purchaseApplyFollowRepository;
    private final BusinessCodeGenerator businessCodeGenerator;
    private final MaterialPurchaseFollowRepository materialPurchaseFollowRepository;
    private final MaterialPurchaseFollowService materialPurchaseFollowService;
    private final DesignLogService designLogService;
    private final PrototypeHistoryRepository prototypeHistoryRepository;
    private final DesignStyleRepository designStyleRepository;
    private final PurchaseCancelRecordRepository purchaseCancelRecordRepository;
    private final OnShelfSkcRepository onShelfSkcRepository;
    private final AccessoriesMatchOrderFeign accessoriesMatchOrderFeign;

    private final OperatingLogServiceFeign operatingLogServiceFeign;
    private final PurchasePrototypeInfoRepository purchasePrototypeInfoRepository;
    private final PrototypeDetailRepository prototypeDetailRepository;
    private final SecondCraftDemandClient secondCraftDemandClient;
    private final BomOrderRepository bomOrderRepository;
    private final MqProducer mqProducer;
    private final MaterialSnapshotRepository materialSnapshotRepository;
    private final BomOrderMaterialRepository bomOrderMaterialRepository;
    private final ProductRemoteHelper productRemoteHelper;
    private final ZjDesignRemoteHelper zjDesignRemoteHelper;
    private final DictValueRemoteHelper dictValueRemoteHelper;
    private final ZjNotify2OldJvRemoteHelper zjNotify2OldJvRemoteHelper;

    private static String ACCESSORIES_MATERIAL_PURCHASE_STATUS = "ACCESSORIES:MATERIAL:PURCHASE:STATUS:";
    private static String FABRIC_MATERIAL_PURCHASE_STATUS = "FABRIC:MATERIAL:PURCHASE:STATUS:";


    /**
     * 采购管理列表页数据加载
     *
     * @param req 查询对象
     * @return {@link PurchaseApplyFollowPageVO}
     */
    @Override
    public PageRespVo<PurchaseApplyFollowPageVO> pageList(PurchaseApplyFollowPageReq req) {
        Page<PurchaseApplyFollowPageVO> page;
        if (req.getPageSize() != -1) {
            page = PageHelper.startPage(req.getPageNum(), req.getPageSize());
        }else{
            page = new Page<>();
        }
        Set<String> purchaseOrderNos = getPurchaseOrderNoList(req.getMaterialKittingCode());
        if(StringUtil.isNotEmpty(req.getMaterialKittingCode()) && CollectionUtil.isEmpty(purchaseOrderNos)){
            PageInfo<PurchaseApplyFollowPageVO> pageInfo = new PageInfo<>(page);
            return PageRespVoHelper.of(pageInfo.getPageNum(), pageInfo.getTotal(), pageInfo.getList());
        }
        if(CollectionUtil.isNotEmpty(purchaseOrderNos)){
            req.setPurchaseOrderNoList(purchaseOrderNos);
        }
        page = PageHelper.startPage(req.getPageNum(), req.getPageSize())
                .doSelectPage(() -> purchaseApplyFollowRepository.pageList(req));
        List<PurchaseApplyFollowPageVO> list = page.getResult();
        // 查询期望采购完成日期，采购申请原因
        Set<String> purchaseOrderNoList = list.stream().map(PurchaseApplyFollowPageVO::getPurchaseOrderNo).collect(Collectors.toSet());
        if(CollectionUtil.isNotEmpty(purchaseOrderNoList)){
            List<MaterialPurchaseFollow> materialPurchaseFollows = materialPurchaseFollowRepository
                    .list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                    .in(MaterialPurchaseFollow::getPurchaseOrderNo,purchaseOrderNoList));
            if(CollectionUtil.isNotEmpty(materialPurchaseFollows)){
                Map<String,List<MaterialPurchaseFollow>> groupBy = materialPurchaseFollows.stream()
                        .collect(Collectors.groupingBy(MaterialPurchaseFollow::getPurchaseOrderNo));
                list.forEach(vo ->{
                    List<MaterialPurchaseFollow> materialList= groupBy.get(vo.getPurchaseOrderNo());
                    vo.setPurchaseApplyTime(materialList != null ? materialList.get(0).getPurchaseApplyTime():null);//期望采购完成日期
                    vo.setPurchaseApplyCause(materialList != null ? materialList.get(0).getPurchaseApplyCause():"");//采购申请原因
                    if(CollectionUtil.isNotEmpty(materialList)){
                        List<MaterialPurchaseFollow> collect = materialList.stream().filter(x -> x.getMaterialPurchaseStatusCode()!=null).collect(Collectors.toList());
                        Optional<MaterialPurchaseFollow> min = collect.stream().min(Comparator.comparing(MaterialPurchaseFollow::getMaterialPurchaseStatusCode));
                        if(min.isPresent()){
                            vo.setMaterialPurchaseStatus(min.get().getMaterialPurchaseStatus());
                            vo.setMaterialCode(min.get().getMaterialKittingCode());
                        }
                    }
                });
            }
        }
        PageInfo<PurchaseApplyFollowPageVO> pageInfo = new PageInfo<>(page);

        Assert.isTrue(pageInfo != null, "分页返回参数为空");
        return PageRespVoHelper.of(pageInfo.getPageNum(), pageInfo.getTotal(), pageInfo.getList());
    }


    /**
     * 取消采购申请
     *
     * @param req 查询对象
     * @return void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelPurchaseApply(CancelPurchaseApplyReq req) {
        // 判断该采购单是否已经取消
        SdpDesignException.isTrue(checkIsCancel(req),"有已取消的采购单，请重新选择");
        UserContent userContent = UserContentHolder.get();
        log.info("......开始取消面辅料采购跟进单操作......");
        List<MaterialPurchaseFollow> materialPurchaseFollowList = new ArrayList<>();
        List<PurchaseApplyFollow> purchaseApplyFollowList = new ArrayList<>();
        List<MaterialPurchaseFollow> purchaseCraftMaterialCancels = new ArrayList<>();
        req.getPurchaseOrderNoList().forEach(purchaseOrderNo -> {
            if(StringUtils.isNotEmpty(purchaseOrderNo)){
                List<PurchaseApplyFollow> purchaseApplyFollows = purchaseApplyFollowRepository
                        .list(Wrappers.<PurchaseApplyFollow>lambdaQuery()
                        .eq(PurchaseApplyFollow::getPurchaseOrderNo, purchaseOrderNo));
                PurchaseApplyFollow purchaseApplyFollow = purchaseApplyFollows.get(0);
                PurchaseApplyFollow pf = new PurchaseApplyFollow();
                saveDesignLog(purchaseApplyFollow,"点击了【取消采购申请】");
                pf.setPurchaseApplyFollowId(purchaseApplyFollow.getPurchaseApplyFollowId());
                pf.setCancelReason(req.getCancelReason());
                pf.setCancelTime(LocalDateTime.now());
                pf.setCancelUserId(userContent != null ? userContent.getCurrentUserId():null);
                pf.setCancelUserName(userContent != null ? userContent.getCurrentUserName():"");
                pf.setStatus(PurchaseApplyStatusEnum.CANCEL.getCode());
                purchaseApplyFollowList.add(pf);
                List<MaterialPurchaseFollow> materialPurchaseFollows = materialPurchaseFollowRepository
                        .list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                        .eq(MaterialPurchaseFollow::getPurchaseOrderNo, purchaseOrderNo));
                for (MaterialPurchaseFollow follw : materialPurchaseFollows){
                    // 不等于大货样采购的
                    if(!SourceEnum.LARGE_CARGO_MAKING.getCode().equals(purchaseApplyFollow.getSource())
                            && PurchaseApplyStatusEnum.EFFECTIVE.getCode().equals(follw.getStatus())){
                        purchaseCraftMaterialCancels.add(follw);
                    }
                    follw.setCancelReason(req.getCancelReason());
                    follw.setStatus(PurchaseApplyStatusEnum.CANCEL.getCode());
                    follw.setCancelUserId(userContent != null ? userContent.getCurrentUserId():null);
                    follw.setCancelUserName(userContent != null ? userContent.getCurrentUserName():"");
                    follw.setCancelTime(LocalDateTime.now());

                    materialPurchaseFollowList.add(follw);
                }
                // 添加操作日志
                if(CollectionUtil.isNotEmpty(purchaseApplyFollows)){
                    saveDesignLog(purchaseApplyFollow,"【取消采购申请】");
                }
            }
        });

        //调用致景接口取消采购申请
        zjDesignRemoteHelper.cancelPurchaseApply(req);

        if(CollectionUtil.isNotEmpty(purchaseApplyFollowList)){
            log.info("取消采购申请跟进单：{}", JSON.toJSON(purchaseApplyFollowList));
            purchaseApplyFollowRepository.updateBatchById(purchaseApplyFollowList);
            if(CollectionUtil.isNotEmpty(materialPurchaseFollowList)){
                // 取消普通样衣采购的工艺任务
                this.purchaseCraftCancel(purchaseCraftMaterialCancels,req.getCancelReason());
            }
        }
        log.info("......结束取消面辅料采购跟进单操作......");
    }



    /**
     *
     * 处理sku采购第一次通知履约开款信息
     * @param purchaseFollowsReq
     * @param demandTypeEnum
     */
    private void handlerPurchaseSkuToSupplyChain(List<MaterialPurchaseFollow> purchaseFollowsReq, MaterialDemandTypeEnum demandTypeEnum) {
        log.info("【处理sku采购第一次通知履约开款信息】物料类型：{}  请求参数: {}", demandTypeEnum.getDesc(), JSON.toJSONString(purchaseFollowsReq));
        if (CollectionUtil.isEmpty(purchaseFollowsReq)) {
            return;
        }

        String designCode = purchaseFollowsReq.get(0).getDesignCode();
        BomOrder bomOrder = bomOrderRepository.getLatestBomByDesignCode(designCode);
        List<MaterialPurchaseFollow> purchaseFollows = materialPurchaseFollowRepository.list(new QueryWrapper<MaterialPurchaseFollow>().lambda().eq(MaterialPurchaseFollow::getDesignCode, designCode));
        Map<Long, Long> purchaseSkuCountMap = purchaseFollows.stream().filter(purchase -> Objects.nonNull(purchase.getSkuId())).collect(Collectors.groupingBy(MaterialPurchaseFollow::getSkuId, Collectors.counting()));

        Set<Long> skuIds = purchaseFollowsReq.stream().filter(purchaseFollow -> Objects.equals(purchaseSkuCountMap.get(purchaseFollow.getSkuId()), 1L))
                .map(MaterialPurchaseFollow::getSkuId).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(skuIds)) {
            log.info("【sku采购第一次通知履约开款信息】无满足条件sku");
            return;
        }

        final Map<String, String> messageHeaders = new HashMap<>(16);
        CommodityJoinBomDto commodityJoinBomDto = new CommodityJoinBomDto().setBomId(bomOrder.getBomId()).setSkuIds(skuIds);
        if (Objects.equals(demandTypeEnum, MaterialDemandTypeEnum.FABRIC)) {
            commodityJoinBomDto.setCommodityCategory(1001);
            messageHeaders.putIfAbsent("acs_code","HOULIU_COMMODITY_JOIN_BOM");
        } else {
            commodityJoinBomDto.setCommodityCategory(1002);
            messageHeaders.putIfAbsent("acs_code","HOULIU_PRODUCT_JOIN_BOM");
        }

        //发送消息
        MqMessageReq mqMessageReq = MqMessageReq.build(MqBizTypeEnum.BOM_SUBMIT_SYNC_COMMODITY, DesignMqConstant.ORDER_UPDATE_COMMODITY_EXCHANGE,
                null, JSON.toJSONString(commodityJoinBomDto));
        mqProducer.sendOnAfterCommit(mqMessageReq, messageHeaders);
    }

    /**
     * 同步供应履约的采购信息状态(面料)
     *
     * @param message 面料采购信息同步信息
     * @param mqMessageReq mq消息体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialPurchaseStatusCallBack(OrderStateChangeMessage message, MqMessageReq mqMessageReq) {
        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        RLock lock = redissonClient.getLock(FABRIC_MATERIAL_PURCHASE_STATUS + message.getOrderCode());
        try {
            boolean isLock = lock.tryLock(6, TimeUnit.SECONDS);
            SdpDesignException.isTrue(isLock, "请求频繁，请稍后再试");

            log.info("=====================> 面料采购信息同步 message:{}",JSON.toJSONString(message));
            if(ObjectUtil.isNotEmpty(message) && ObjectUtil.isNotEmpty(message.getOrderCode())){
                List<MaterialPurchaseFollow> materialPurchaseFollows = materialPurchaseFollowRepository
                        .list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                                .eq(MaterialPurchaseFollow::getCuttingCode, message.getOrderCode()));
                log.info("=====================> 面料采购信息同步 materialPurchaseFollows:{}",JSON.toJSONString(materialPurchaseFollows));
                if(CollectionUtil.isNotEmpty(materialPurchaseFollows)){
                    List<MaterialPurchaseFollow> materialPurchaseFollowList = new ArrayList<>();
                    materialPurchaseFollows.forEach(materialPurchaseFollow -> {
                        log.info("materialPurchaseFollow状态：{},message:{}",materialPurchaseFollow.getMaterialPurchaseStatusCode(),message.getOrderState());
                        if(!materialPurchaseFollow.getMaterialPurchaseStatusCode().equals(MaterialPurchaseStatusEnum.COMPLETED.getCode())){
                            if(ObjectUtil.isEmpty(materialPurchaseFollow.getReceivingTime())){
                                log.info("1、materialPurchaseFollow状态：{},message:{}",materialPurchaseFollow.getMaterialPurchaseStatusCode(),message.getOrderState());
                                MaterialPurchaseFollow mf  = new MaterialPurchaseFollow();
                                mf.setMaterialPurchaseFollowId(materialPurchaseFollow.getMaterialPurchaseFollowId());
                                mf.setMaterialPurchaseStatusCode(message.getOrderState());
                                mf.setReceivingTime(ObjectUtil.isNotEmpty(message.getRevisedTime()) ? message.getRevisedTime():null);
                                mf.setMaterialPurchaseStatus(MaterialPurchaseStatusEnum.getByCode(message.getOrderState()).getName());
                                materialPurchaseFollowList.add(mf);
                            } else if(materialPurchaseFollow.getReceivingTime().isBefore(message.getRevisedTime())){
                                log.info("2、materialPurchaseFollow状态：{},message:{}",materialPurchaseFollow.getMaterialPurchaseStatusCode(),message.getOrderState());
                                MaterialPurchaseFollow mf  = new MaterialPurchaseFollow();
                                mf.setMaterialPurchaseFollowId(materialPurchaseFollow.getMaterialPurchaseFollowId());
                                mf.setMaterialPurchaseStatusCode(message.getOrderState());
                                mf.setReceivingTime(ObjectUtil.isNotEmpty(message.getRevisedTime()) ? message.getRevisedTime():null);
                                mf.setMaterialPurchaseStatus(MaterialPurchaseStatusEnum.getByCode(message.getOrderState()).getName());
                                materialPurchaseFollowList.add(mf);
                            } else if(materialPurchaseFollow.getReceivingTime().equals(message.getRevisedTime())){
                                log.info("3、materialPurchaseFollow状态：{},message:{}",materialPurchaseFollow.getMaterialPurchaseStatusCode(),message.getOrderState());
                                if(materialPurchaseFollow.getMaterialPurchaseStatusCode().compareTo(message.getOrderState()) == -1){
                                    MaterialPurchaseFollow mf = new MaterialPurchaseFollow();
                                    mf.setMaterialPurchaseFollowId(materialPurchaseFollow.getMaterialPurchaseFollowId());
                                    mf.setMaterialPurchaseStatusCode(message.getOrderState());
                                    mf.setReceivingTime(ObjectUtil.isNotEmpty(message.getRevisedTime()) ? message.getRevisedTime():null);
                                    mf.setMaterialPurchaseStatus(MaterialPurchaseStatusEnum.getByCode(message.getOrderState()).getName());
                                    materialPurchaseFollowList.add(mf);
                                }
                            }
                        }
                    });
                    if(CollectionUtil.isNotEmpty(materialPurchaseFollowList)){
                        materialPurchaseFollowRepository.updateBatchById(materialPurchaseFollowList);
                    }

                }else {
                    log.info("=== 面料剪版单状态同步-转发旧JV ===");
                    zjNotify2OldJvRemoteHelper.notify2OldJv(mqMessageReq);
                }
            }
        }catch (InterruptedException e){
            throw new SdpDesignException(e.getMessage(), e);
        } finally {
            if (lock.isLocked()) {
                TransactionUtil.afterCompletion(()->{
                    lock.unlock();
                });

            }
        }

    }

    /**
     * 同步供应履约的采购信息状态(辅料)
     *
     * @param message      辅料采购信息同步信息
     * @param mqMessageReq
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void accessoriesMaterialPurchaseStatusCallBack(AccessoriesMatchOrderChangeMessage message, MqMessageReq mqMessageReq) {
        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        RLock lock = redissonClient.getLock(ACCESSORIES_MATERIAL_PURCHASE_STATUS + message.getOrderCode());
        try {
            boolean isLock = lock.tryLock(6, TimeUnit.SECONDS);
            SdpDesignException.isTrue(isLock, "请求频繁，请稍后再试");

            log.info("=====================> 辅料采购信息同步 message:{}",JSON.toJSONString(message));
            if(ObjectUtil.isNotEmpty(message) && ObjectUtil.isNotEmpty(message.getOrderCode())){
                List<MaterialPurchaseFollow> materialPurchaseFollows = materialPurchaseFollowRepository.list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                        .eq(MaterialPurchaseFollow::getCuttingCode, message.getOrderCode()));
                log.info("=====================> 辅料采购信息同步 materialPurchaseFollows:{}",JSON.toJSONString(materialPurchaseFollows));
                if(CollectionUtil.isNotEmpty(materialPurchaseFollows)){
                    List<MaterialPurchaseFollow> materialPurchaseFollowList = new ArrayList<>();
                    materialPurchaseFollows.forEach(materialPurchaseFollow -> {
                        log.info("materialPurchaseFollow状态：{},message:{}",materialPurchaseFollow.getMaterialPurchaseStatusCode(),message.getMatchPlateState());
                        if(!materialPurchaseFollow.getMaterialPurchaseStatusCode().equals(MatchPlateStateEnum.COMPLETED.getCode())){
                            if(ObjectUtil.isEmpty(materialPurchaseFollow.getReceivingTime())){
                                log.info("1、materialPurchaseFollow状态：{},message:{}",materialPurchaseFollow.getMaterialPurchaseStatusCode(),message.getMatchPlateState());
                                MaterialPurchaseFollow mf = new MaterialPurchaseFollow();
                                mf.setMaterialPurchaseFollowId(materialPurchaseFollow.getMaterialPurchaseFollowId());
                                mf.setMaterialPurchaseStatusCode(message.getMatchPlateState());
                                mf.setReceivingTime(ObjectUtil.isNotEmpty(message.getRevisedTime()) ? message.getRevisedTime():null);
                                mf.setMaterialPurchaseStatus(MatchPlateStateEnum.getEntityByCode(message.getMatchPlateState()).getDesc());
                                materialPurchaseFollowList.add(mf);
                            } else if(materialPurchaseFollow.getReceivingTime().isBefore(message.getRevisedTime())){
                                log.info("2、materialPurchaseFollow状态：{},message:{}",materialPurchaseFollow.getMaterialPurchaseStatusCode(),message.getMatchPlateState());
                                MaterialPurchaseFollow mf = new MaterialPurchaseFollow();
                                mf.setMaterialPurchaseFollowId(materialPurchaseFollow.getMaterialPurchaseFollowId());
                                mf.setMaterialPurchaseStatusCode(message.getMatchPlateState());
                                mf.setReceivingTime(ObjectUtil.isNotEmpty(message.getRevisedTime()) ? message.getRevisedTime():null);
                                mf.setMaterialPurchaseStatus(MatchPlateStateEnum.getEntityByCode(message.getMatchPlateState()).getDesc());
                                materialPurchaseFollowList.add(mf);
                            } else if(materialPurchaseFollow.getReceivingTime().equals(message.getRevisedTime())){
                                log.info("3、materialPurchaseFollow状态：{},message:{}",materialPurchaseFollow.getMaterialPurchaseStatusCode(),message.getMatchPlateState());
                                if(materialPurchaseFollow.getMaterialPurchaseStatusCode().compareTo(message.getMatchPlateState()) == -1){
                                    MaterialPurchaseFollow mf = new MaterialPurchaseFollow();
                                    mf.setMaterialPurchaseFollowId(materialPurchaseFollow.getMaterialPurchaseFollowId());
                                    mf.setMaterialPurchaseStatusCode(message.getMatchPlateState());
                                    mf.setReceivingTime(ObjectUtil.isNotEmpty(message.getRevisedTime()) ? message.getRevisedTime():null);
                                    mf.setMaterialPurchaseStatus(MatchPlateStateEnum.getEntityByCode(message.getMatchPlateState()).getDesc());
                                    materialPurchaseFollowList.add(mf);
                                }
                            }
                        }

                    });
                    if(CollectionUtil.isNotEmpty(materialPurchaseFollowList)){
                        materialPurchaseFollowRepository.updateBatchById(materialPurchaseFollowList);
                    }

                }else {
                    log.info("=== 辅料配版单状态同步-转发旧JV ===");
                    zjNotify2OldJvRemoteHelper.notify2OldJv(mqMessageReq);
                }
            }
        }catch (InterruptedException e){
            throw new SdpDesignException(e.getMessage(), e);
        } finally {
            if (lock.isLocked()) {
                TransactionUtil.afterCompletion(()->{
                    lock.unlock();
                });

            }
        }

    }

    /**
     * 采购记录
     *
     * @param demandType 需求类型: 1, 面料; 2, 辅料
     * @param orderCode 剪版单号
     * @return {@link List<OrderPurchaseSimpleLogVO>}
     */
    @Override
    public List<OrderPurchaseSimpleLogVO> purchaseOrderLog(MaterialDemandTypeEnum demandType, String orderCode) {
        List<OrderPurchaseSimpleLogVO> OrderPurchaseSimpleLogVOList = new ArrayList<>();
        if(MaterialDemandTypeEnum.FABRIC.getCode().equals(demandType.getCode())){
            DataResponse<List<CuttingOperatingSimpleLogVo>> logRes = operatingLogServiceFeign.getLogByOrderNo(orderCode);
            log.debug("=== 面料剪版单日志查询: response:{} ====", JSON.toJSONString(logRes));
            List<CuttingOperatingSimpleLogVo> data = null;
            if(logRes.isSuccessful()){
                data = logRes.getData();
            }
            if(CollectionUtil.isNotEmpty(data)){
                data.forEach(d -> {
                    OrderPurchaseSimpleLogVO vo = new OrderPurchaseSimpleLogVO();
                    BeanUtils.copyProperties(d,vo);
                    vo.setContent(d.getDescription());
                    vo.setOperator(d.getCreatorName());
                    OrderPurchaseSimpleLogVOList.add(vo);
                });
            }
        } else if(MaterialDemandTypeEnum.ACCESSORIES.getCode().equals(demandType.getCode())){
            DataResponse<List<AccessoriesMatchOrderSimpleLogWebVo>> logRes = accessoriesMatchOrderFeign.log(orderCode);
            log.debug("=== 辅料配版单日志查询: response:{} ====", JSON.toJSONString(logRes));
            List<AccessoriesMatchOrderSimpleLogWebVo> data = null;
            if(logRes.isSuccessful()){
                data = logRes.getData();
            }
            if(CollectionUtil.isNotEmpty(data)){
                data.forEach(d -> {
                    OrderPurchaseSimpleLogVO vo = new OrderPurchaseSimpleLogVO();
                    BeanUtils.copyProperties(d,vo);
                    vo.setOperator(d.getCreatorName());
                    OrderPurchaseSimpleLogVOList.add(vo);
                });
            }
        }
        return OrderPurchaseSimpleLogVOList;

    }

    /**
     * 获取最大的采购单号
     *
     * @return 采购单号
     */
    public String selectLatestGCCode() {
        return purchaseApplyFollowRepository.selectLatestGCCode();
    }

    /**
     * 获取最大的面料采购需求单号
     *
     * @return 采购单号
     */
    public String selectLatestFabricPurchaseCode() {
        return materialPurchaseFollowRepository.selectLatestFabricPurchaseCode();
    }
    /**
     * 获取最大的辅料采购需求单号
     *
     * @return 采购单号
     */
    public String selectLatestAccessoriesPurchaseCode() {
        return materialPurchaseFollowRepository.selectLatestAccessoriesPurchaseCode();
    }


    /**
     * 供应履约回传剪版单号+剪版状态 更新到本地采购数据状态
     *
     * @param cuttingOrderSimpleRespList 面料剪版单信息
     */
    public void updateCuttingCodeAndPurchaseStatus(List<CuttingOrderSimpleOpenResp> cuttingOrderSimpleRespList){
        // 需要记录返回的信息，防止出现事务问题时可以人工补偿数据信息  TODO
        if(CollectionUtil.isNotEmpty(cuttingOrderSimpleRespList)){
            cuttingOrderSimpleRespList.forEach(cuttingOrderSimpleResp -> {
                if(StringUtils.isNotEmpty(cuttingOrderSimpleResp.getPurchaseOrderNo())){
                    Long materialSnapshotId = cuttingOrderSimpleResp.getMaterialSnapshotId();
                    List<Long> materialSnapshotIdList = new ArrayList<>();
                    materialSnapshotIdList.add(cuttingOrderSimpleResp.getMaterialSnapshotId());
                    List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(materialSnapshotIdList);
                    if (CollectionUtils.isNotEmpty(snapshotList)) {
                        Set<Long> newIdList = new HashSet<>(materialSnapshotIdList.size());
                        for (MaterialSnapshot snapshot : snapshotList) {
                            //历史物料(走履约匹配的), 取matchId
                            if (Objects.equals(snapshot.getNewMaterial(), Bool.NO.getCode()) && Objects.nonNull(snapshot.getMatchId())) {
                                materialSnapshotId = snapshot.getMatchId();
                            }
                        }
                    }

                    List<MaterialPurchaseFollow> mps = materialPurchaseFollowRepository
                            .list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                            .eq(MaterialPurchaseFollow::getPurchaseOrderNo,cuttingOrderSimpleResp.getPurchaseOrderNo())
                            .eq(MaterialPurchaseFollow::getMaterialSnapshotId,materialSnapshotId));
                    if(CollectionUtil.isNotEmpty(mps)){
                        List<MaterialPurchaseFollow> materialPurchaseFollows = new ArrayList<>();
                        mps.forEach(mp ->{
                            MaterialPurchaseFollow mf = new MaterialPurchaseFollow();
                            mf.setMaterialPurchaseFollowId(mp.getMaterialPurchaseFollowId());
                            mf.setMaterialPurchaseStatusCode(cuttingOrderSimpleResp.getOrderState());
                            mf.setMaterialPurchaseStatus(MaterialPurchaseStatusEnum.getByCode(cuttingOrderSimpleResp.getOrderState()).getName());
                            mf.setCuttingCode(cuttingOrderSimpleResp.getOrderCode());
                            materialPurchaseFollows.add(mf);
                        });
                        if(CollectionUtil.isNotEmpty(materialPurchaseFollows)){
                            materialPurchaseFollowService.updateBatchById(materialPurchaseFollows);
                        }

                    }

                }
            });
        }
    }

    /**
     * 供应履约回传剪版单号+剪版状态 更新到本地采购数据状态
     *
     * @param accessoriesMatchOrderInnerList 辅料配版单信息
     */
    public void updateAccessoriesMatchOrderPurchaseStatus(List<AccessoriesMatchOrderOpenResp> accessoriesMatchOrderInnerList){
        // 需要记录返回的信息，防止出现事务问题时可以人工补偿数据信息  TODO
        if(CollectionUtil.isNotEmpty(accessoriesMatchOrderInnerList)){
            accessoriesMatchOrderInnerList.forEach(cuttingOrderSimpleResp -> {
                if(StringUtils.isNotEmpty(cuttingOrderSimpleResp.getPurchaseOrderNo())){
                    Long materialSnapshotId = cuttingOrderSimpleResp.getMaterialSnapshotId();
                    List<Long> materialSnapshotIdList = new ArrayList<>();
                    materialSnapshotIdList.add(cuttingOrderSimpleResp.getMaterialSnapshotId());
                    List<MaterialSnapshot> snapshotList = materialSnapshotRepository.listByIds(materialSnapshotIdList);
                    if (CollectionUtils.isNotEmpty(snapshotList)) {
                        Set<Long> newIdList = new HashSet<>(materialSnapshotIdList.size());
                        for (MaterialSnapshot snapshot : snapshotList) {
                            //历史物料(走履约匹配的), 取matchId
                            if (Objects.equals(snapshot.getNewMaterial(), Bool.NO.getCode()) && Objects.nonNull(snapshot.getMatchId())) {
                                materialSnapshotId = snapshot.getMatchId();
                            }
                        }
                    }
                    List<MaterialPurchaseFollow> mps = materialPurchaseFollowRepository.list(Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                            .eq(MaterialPurchaseFollow::getPurchaseOrderNo,cuttingOrderSimpleResp.getPurchaseOrderNo())
                            .eq(MaterialPurchaseFollow::getMaterialSnapshotId,materialSnapshotId));
                    if(CollectionUtil.isNotEmpty(mps)){
                        List<MaterialPurchaseFollow> materialPurchaseFollows = new ArrayList<>();
                        mps.forEach(mp ->{
                            MaterialPurchaseFollow mf = new MaterialPurchaseFollow();
                            mf.setMaterialPurchaseFollowId(mp.getMaterialPurchaseFollowId());
                            mf.setMaterialPurchaseStatusCode(Integer.valueOf(cuttingOrderSimpleResp.getMatchPlateState()));
                            mf.setMaterialPurchaseStatus(MatchPlateStateEnum.getEntityByCode(Integer.valueOf(cuttingOrderSimpleResp.getMatchPlateState())).getDesc());
                            mf.setCuttingCode(cuttingOrderSimpleResp.getOrderCode());
                            materialPurchaseFollows.add(mf);
                        });
                        if(CollectionUtil.isNotEmpty(materialPurchaseFollows)){
                            materialPurchaseFollowService.updateBatchById(materialPurchaseFollows);
                        }
                    }

                }
            });
        }
    }

    /**
     * 添加操作日志
     *
     * @param purchaseApplyFollow 剪版需求跟进表（采购申请跟进）
     * @param content 操作内容
     */
    public void saveDesignLog(PurchaseApplyFollow purchaseApplyFollow,String content){
        log.info("=================================> 操作日志记录：{},purchaseApplyFollow:{}",content,JSON.toJSONString(purchaseApplyFollow));
        if(ObjectUtil.isEmpty(purchaseApplyFollow)){
            return;
        }
        try {
            PurchasePrototypeInfo purchasePrototypeInfo = purchasePrototypeInfoRepository.getById(purchaseApplyFollow.getPrototypeId());
            if(ObjectUtil.isEmpty(purchasePrototypeInfo)){
                TimeUnit.SECONDS.sleep(4L);
                purchasePrototypeInfo = purchasePrototypeInfoRepository.getById(purchaseApplyFollow.getPrototypeId());
            }
            DesignLogReq designLogReq = DesignLogReq.builder().bizId(purchaseApplyFollow.getPurchaseApplyFollowId())
                    .bizType(DesignLogBizTypeEnum.MATERIAL_PURCHASE)
                    .bizId(purchaseApplyFollow.getPurchaseApplyFollowId())
                    .designCode(purchaseApplyFollow.getDesignCode())
                    .bizVersionNum(purchaseApplyFollow.getPurchaseCount())
                    .content(content)
                    .build();
            designLogService.saveDesignLog(designLogReq,purchasePrototypeInfo);
        } catch (InterruptedException e) {
            log.error("添加操作日志异常：{}",e.getMessage());
        }

    }

    /**
     * 校验是否有已经取消的采购单
     *
     * @param req 取消采购申请的参数
     * @return boolean
     */
    public boolean checkIsCancel(CancelPurchaseApplyReq req){
        if(CollectionUtil.isEmpty(req.getPurchaseOrderNoList())){
            return false;
        }
        List<PurchaseApplyFollow> purchaseApplyFollows = purchaseApplyFollowRepository.getBaseMapper()
                .selectList(Wrappers.<PurchaseApplyFollow>lambdaQuery()
                .in(PurchaseApplyFollow::getPurchaseOrderNo, req.getPurchaseOrderNoList()));
        for(PurchaseApplyFollow purchaseApplyFollow : purchaseApplyFollows){
            // 状态（剪版单的状态） 1|有效，0|取消
            if(PurchaseApplyStatusEnum.CANCEL.getCode().equals(purchaseApplyFollow.getStatus())){
                return false;
            }
        }
        return true;
    }



    /**
     * 供应履约取消采购单参数（面料）
     * @param list 面辅料采购跟进表
     * @param cancelReason 取消原因
     * @return {@link List<OrderRemoteCancelReq>}
     */
    public List<OrderRemoteCancelReq> orderRemoteCancelReq(List<MaterialPurchaseFollow> list
            ,String cancelReason){
        List<OrderRemoteCancelReq> orderRemoteCancelReqList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(list)){
            list.stream().forEach(v -> {
                OrderRemoteCancelReq cancelReq = new OrderRemoteCancelReq();
                cancelReq.setCancelReason(cancelReason);
                cancelReq.setOrderCode(v.getCuttingCode());
                cancelReq.setRemark(cancelReason);
                orderRemoteCancelReqList.add(cancelReq);
            });
        }
        return orderRemoteCancelReqList;
    }


    /**
     * 出现异常时供应履约取消采购单
     * @param cuttingOrderSimpleResponse 取消剪版单参数
     * @param cancelReason 取消参数
     * @return {@link List<OrderRemoteCancelReq>}
     */
    public List<OrderRemoteCancelReq> orderExceptionCancelReq(DataResponse<List<CuttingOrderSimpleResp>> cuttingOrderSimpleResponse
            ,String cancelReason){
        List<OrderRemoteCancelReq> orderRemoteCancelReqList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(cuttingOrderSimpleResponse.getData())){
            cuttingOrderSimpleResponse.getData().forEach(v -> {
                OrderRemoteCancelReq cancelReq = new OrderRemoteCancelReq();
                cancelReq.setCancelReason(cancelReason);
                cancelReq.setOrderCode(v.getOrderCode());
                cancelReq.setRemark(cancelReason);
                orderRemoteCancelReqList.add(cancelReq);
            });
        }
        return orderRemoteCancelReqList;
    }


    /**
     * 出现异常时供应履约取消采购单
     * @param listDataResponse 取消配版单参数
     * @param cancelReason 取消参数
     * @return {@link List<OrderRemoteCancelReq>}
     */
    public List<AccessoriesMatchOrderInnerCancelReq> accessoriesOrderExceptionCancelReq(DataResponse<List<AccessoriesMatchOrderInnerVo>> listDataResponse
            ,String cancelReason){
        List<AccessoriesMatchOrderInnerCancelReq> orderRemoteCancelReqList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(listDataResponse.getData())){
            listDataResponse.getData().forEach(v -> {
                AccessoriesMatchOrderInnerCancelReq cancelReq = new AccessoriesMatchOrderInnerCancelReq();
                cancelReq.setReason(cancelReason);
                cancelReq.setOrderCode(v.getOrderCode());
                cancelReq.setRemark(cancelReason);
                orderRemoteCancelReqList.add(cancelReq);
            });
        }
        return orderRemoteCancelReqList;
    }


    /**
     * 供应履约取消采购单参数（辅料）
     * @param list 面辅料采购跟进表
     * @param cancelReason 取消原因
     * @return {@link List<AccessoriesMatchOrderInnerCancelReq>}
     */
    public List<AccessoriesMatchOrderInnerCancelReq> accessoriesMatchOrderCancelReq(List<MaterialPurchaseFollow> list,
                                                                                    String cancelReason){
        List<AccessoriesMatchOrderInnerCancelReq> accessoriesMatchOrderCancelReqList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(list)){
            list.forEach(v -> {
                AccessoriesMatchOrderInnerCancelReq accessoriesMatchOrderInnerCancelReq = new AccessoriesMatchOrderInnerCancelReq();
                accessoriesMatchOrderInnerCancelReq.setReason(cancelReason);
                accessoriesMatchOrderInnerCancelReq.setOrderCode(v.getCuttingCode());
                accessoriesMatchOrderInnerCancelReq.setRemark(cancelReason);
                accessoriesMatchOrderCancelReqList.add(accessoriesMatchOrderInnerCancelReq);
            });
        }
        return accessoriesMatchOrderCancelReqList;
    }

    /**
     * 保存取消物料采购记录（失败的）
     * @param list 面辅料采购跟进信息
     * @param dataList 配版单取消参数
     * @param accessoriesMatchOrderCancedataList 配版单取消参数
     */
    public void savePurchaseCancelRecord(List<MaterialPurchaseFollow> list,List<CuttingOrderCancelResp> dataList,
                                         List<AccessoriesMatchOrderCancelInnerVo> accessoriesMatchOrderCancedataList){
        if(CollectionUtil.isNotEmpty(list)){
            UserContent userContent = UserContentHolder.get();
            if(CollectionUtil.isNotEmpty(dataList)){
                Map<String, List<CuttingOrderCancelResp>> collect = dataList.stream()
                        .collect(Collectors.groupingBy(CuttingOrderCancelResp::getOrderCode));
                List<PurchaseCancelRecord> purchaseCancelRecords = new ArrayList<>();
                list.forEach(v ->{
                    List<CuttingOrderCancelResp> cuttingOrderCancelResps = collect.get(v.getCuttingCode());
                    if(CollectionUtil.isNotEmpty(cuttingOrderCancelResps) && cuttingOrderCancelResps.get(0).getIsDel() == 0){
                        PurchaseCancelRecord record = new PurchaseCancelRecord();
                        record.setPurchaseCancelRecordId(IdPool.getId());
                        record.setPurchaseOrderNo(v.getPurchaseOrderNo());
                        record.setMaterialPurchaseStatus(v.getMaterialPurchaseStatus());
                        record.setCuttingCode(cuttingOrderCancelResps.get(0).getOrderCode());
                        record.setFailCause(cuttingOrderCancelResps.get(0).getCancelFailReason());
                        record.setMaterialFailStatus(ObjectUtil.isNotEmpty(cuttingOrderCancelResps.get(0).getOrderState()) ?
                                MaterialPurchaseStatusEnum.getByCode(cuttingOrderCancelResps.get(0).getOrderState()).getName()
                                : "");
                        record.setOperatorId(userContent != null ? userContent.getCurrentUserId():null);
                        record.setOperatorTime(LocalDateTime.now());
                        purchaseCancelRecords.add(record);
                    }
                });
                if(CollectionUtil.isNotEmpty(purchaseCancelRecords)){
                    purchaseCancelRecordRepository.saveBatch(purchaseCancelRecords);
                }
            }
            if(CollectionUtil.isNotEmpty(accessoriesMatchOrderCancedataList)){
                Map<String, List<AccessoriesMatchOrderCancelInnerVo>> collect = accessoriesMatchOrderCancedataList.stream()
                        .collect(Collectors.groupingBy(AccessoriesMatchOrderCancelInnerVo::getOrderCode));
                List<PurchaseCancelRecord> purchaseCancelRecords = new ArrayList<>();
                list.forEach(v ->{
                    List<AccessoriesMatchOrderCancelInnerVo> AccessoriesMatchOrderCancelInnerVos = collect.get(v.getCuttingCode());
                    if(CollectionUtil.isNotEmpty(AccessoriesMatchOrderCancelInnerVos) && AccessoriesMatchOrderCancelInnerVos.get(0).getIsDel() == 0){
                        PurchaseCancelRecord record = new PurchaseCancelRecord();
                        record.setPurchaseCancelRecordId(IdPool.getId());
                        record.setPurchaseOrderNo(v.getPurchaseOrderNo());
                        record.setMaterialPurchaseStatus(v.getMaterialPurchaseStatus());
                        record.setCuttingCode(AccessoriesMatchOrderCancelInnerVos.get(0).getOrderCode());
                        record.setFailCause(AccessoriesMatchOrderCancelInnerVos.get(0).getCancelFailReason());
                        record.setMaterialFailStatus(ObjectUtil.isNotEmpty(AccessoriesMatchOrderCancelInnerVos.get(0).getMatchPlateState()) ?
                                MaterialPurchaseStatusEnum.getByCode(AccessoriesMatchOrderCancelInnerVos.get(0).getMatchPlateState()).getName()
                                : "");
                        record.setOperatorId(userContent != null ? userContent.getCurrentUserId():null);
                        record.setOperatorTime(LocalDateTime.now());
                        purchaseCancelRecords.add(record);
                    }
                });
                if(CollectionUtil.isNotEmpty(purchaseCancelRecords)){
                    purchaseCancelRecordRepository.saveBatch(purchaseCancelRecords);
                }
            }

        }

    }


    private Set<String> getPurchaseOrderNoList(String materialCode){
        if(StringUtil.isEmpty(materialCode)){
            return Set.of();
        }
        List<MaterialPurchaseFollow> list = materialPurchaseFollowRepository.list(
                Wrappers.<MaterialPurchaseFollow>lambdaQuery()
                        .like(MaterialPurchaseFollow::getMaterialKittingCode, materialCode));
        if(CollectionUtil.isEmpty(list)){
            return Set.of();
        }
        Set<String> collect = list.stream().map(MaterialPurchaseFollow::getPurchaseOrderNo).collect(Collectors.toSet());
        return collect;
    }


    /**
     * 保存采购信息关联版单信息
     *
     * @param purchasePrototypeInfoId 关联order_material_follow、purchase_apply_follow、order_material_follow==prototype_id
     * @param prototypeHistory 版单历史表实体类
     * @param prototypeDetailList 版单历史详情表实体类
     */
    public Long savePurchasePrototypeInfo(Long purchasePrototypeInfoId,PrototypeHistory prototypeHistory,
                                          List<PrototypeDetail> prototypeDetailList){
        List<PurchasePrototypeInfo> list = purchasePrototypeInfoRepository.list(Wrappers.<PurchasePrototypeInfo>lambdaQuery()
                .eq(PurchasePrototypeInfo::getPrototypeId, prototypeHistory.getPrototypeId()));
        if(CollectionUtil.isEmpty(list)){
            PrototypeDetail prototypeDetail = null;
            if (CollectionUtil.isNotEmpty(prototypeDetailList)){
                prototypeDetail = prototypeDetailList.get(0);
            }
            PurchasePrototypeInfo purchasePrototypeInfo = PurchasePrototypeInfoConverter.of(
                    purchasePrototypeInfoId,prototypeHistory, prototypeDetail,"",null);
            if(ObjectUtil.isNotEmpty(purchasePrototypeInfo)){
                purchasePrototypeInfoRepository.save(purchasePrototypeInfo);
            }
        } else {
             purchasePrototypeInfoId = list.get(0).getPurchasePrototypeInfoId();
        }
        return purchasePrototypeInfoId;
    }




    /**
     * 采购申请(开发BOM采购申请--批量)
     *
     * @param req 开发BOM采购申请请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void purchaseApplyMaterialConfirmBatch(PurchaseApplyMaterialConfirmBatchReq req) {
        log.info("=====================> start【采购申请(开发BOM采购申请--批量)】 <===================== ");
        log.info("=====================> req: {} ",JSONUtil.toJsonStr(req));
        UserContent userContent = UserContentHolder.get();
        this.requiredBatchCheckAndOther(req);
        req.setCreatorName(userContent.getCurrentUserName());
        req.setPurchaseOrderNo(businessCodeGenerator.generate(CodeRuleEnum.DESIGN_PURCHASE_ORDER_NO, this::selectLatestGCCode));
        PrototypeHistory prototypeHistory = prototypeHistoryRepository.getByPrototypeId(req.getPrototypeId());
        Assert.isTrue(ObjectUtil.isNotEmpty(prototypeHistory),"版单信息为空");
        DesignStyle designStyle = designStyleRepository.getByStyleCode(prototypeHistory.getStyleCode());
        SdpDesignException.notNull(designStyle, "spu信息为空!");

        //若物料有工艺,校验第三方工艺需求id;(推送裁前工艺任务需要)
        this.checkThirdPartyCraftDemandId(req);

        // 保存采购信息关联版单信息
        List<PrototypeDetail> prototypeDetailList = prototypeDetailRepository.list(Wrappers.<PrototypeDetail>lambdaQuery()
                .eq(PrototypeDetail::getPrototypeId, prototypeHistory.getPrototypeId()));
        Long purchasePrototypeInfoId = IdPool.getId();
        purchasePrototypeInfoId = savePurchasePrototypeInfo(purchasePrototypeInfoId,prototypeHistory,prototypeDetailList);

        //采购次数  , 根据设计款号查询 同一设计款号在采购申请单弹框中，选中物料信息后，点击【确定】按钮的次数
        Integer count = purchaseApplyFollowRepository.selectCount(req.getDesignCode());
        PurchaseApplyFollow purchaseApplyFollow = PurchaseApplyFollowConverter.batchMaterialConfirmOf(purchasePrototypeInfoId,req,count);

        log.info("=====================> 【采购申请(开发BOM采购申请--批量)】designCode:{},purchaseApplyFollow信息：{}", purchaseApplyFollow.getDesignCode(),JSONUtil.toJsonStr(purchaseApplyFollow));

        // 推款之后改为开发BOM
        purchaseApplyFollow.setSource(SourceEnum.BOM_ORDER.getCode());
        purchaseApplyFollowRepository.save(purchaseApplyFollow);
        req.setPurchaseApplyFollowId(purchaseApplyFollow.getPurchaseApplyFollowId());

        List<Long> snapshotIdList = StreamUtil.convertListAndDistinct(req.getPurchaseApplyList(), PurchaseApplyMaterialConfirmBatchReq.PurchaseListReq::getMaterialSnapshotId);
        List<MaterialSnapshot> materialSnapshotList = materialSnapshotRepository.listByIds(snapshotIdList);
        SdpDesignException.notEmpty(materialSnapshotList, "物料信息不存在!");
        // Map<Long, MaterialSnapshot> snapshotMap = StreamUtil.list2Map(materialSnapshotList, MaterialSnapshot::getMaterialSnapshotId);

        Map<Long, CommoditySkuCollectionRespVo.Sku.SkuPriceVo> skuPriceMap = new HashMap<>();
        //校验面辅料启用状态
        this.checkMaterialEnable(materialSnapshotList, skuPriceMap);

        //其他入参信息设置
        this.buildOtherInfo(req, prototypeHistory, skuPriceMap, designStyle);

        // 生成面辅料采购跟进数据
        List<MaterialPurchaseFollow> materialPurchaseFollows = MaterialPurchaseFollowConverter.materialTrackOfList(prototypeHistory,purchasePrototypeInfoId,req);
        log.info("=====================> materialPurchaseFollow信息：{}", JSONUtil.toJsonStr(materialPurchaseFollows));
        materialPurchaseFollowRepository.saveBatch(materialPurchaseFollows);

        //skc上架图
        List<String> skcImageList = this.getOnshelfSkcImageList(prototypeHistory.getDesignCode());

        //调用致景接口创建采购单
        PurchaseApplyFollowResultOpenResp purchaseResp = zjDesignRemoteHelper.batchPurchaseApplyMaterial(req, prototypeHistory.getBizChannel(), skcImageList);
        List<CuttingOrderSimpleOpenResp> cuttingOrderSimpleOpenResp = purchaseResp.getCuttingOrderSimpleOpenResp();
        List<AccessoriesMatchOrderOpenResp> accessoriesMatchOrderOpenResp = purchaseResp.getAccessoriesMatchOrderOpenResp();

        //关联剪配版单数据
        updateCuttingCodeAndPurchaseStatus(cuttingOrderSimpleOpenResp);
        updateAccessoriesMatchOrderPurchaseStatus(accessoriesMatchOrderOpenResp);

        // 添加操作日志
        CompletableFuture.runAsync(AsyncTask.wrapper(()->{
            saveDesignLog(purchaseApplyFollow,"批量下采购单");
        }));

        // 二次工艺采购
        materialConfirmBatchSecondCraftPurchase(req,prototypeHistory,cuttingOrderSimpleOpenResp,accessoriesMatchOrderOpenResp);
        log.info("=====================> end【采购申请批量)】 <===================== ");
    }

    private void buildOtherInfo(PurchaseApplyMaterialConfirmBatchReq req, PrototypeHistory prototypeHistory, Map<Long, CommoditySkuCollectionRespVo.Sku.SkuPriceVo> skuPriceMap, DesignStyle designStyle) {
        //设置bom信息
        this.setPurchseBomInfo(req);
        //平台信息
        req.setPlatformName(designStyle.getPlatformName());
        //散剪倍率
        BigDecimal scatterCutRatio = this.getCutRatio();
        req.getPurchaseApplyList().forEach(item -> {
            if(MaterialDemandTypeEnum.FABRIC.getCode().equals(item.getDemandType())){
                CommoditySkuCollectionRespVo.Sku.SkuPriceVo skuPriceVo = skuPriceMap.get(item.getSkuId());
                SdpDesignException.notNull(skuPriceVo, "{} 缺少足米价信息! ", item.getMaterialCategory());
                SdpDesignException.notNull(skuPriceVo.getMeterPrice(), "{} 缺少足米价!", item.getMaterialCategory());
                BigDecimal scatterCutPrice = skuPriceVo.getMeterPrice().multiply(scatterCutRatio);

                //散剪倍率与散剪价
                item.setScatterCutRatio(scatterCutRatio);
                item.setScatterCutPrice(scatterCutPrice);
                item.setCustomerItemNo(req.getDesignCode());

                //面料采购需求单号
                String purchaseRequestCode = businessCodeGenerator.generate(CodeRuleEnum.FABRIC_PURCHASE_REQUEST_CODE, this::selectLatestFabricPurchaseCode);
                item.setPurchaseRequestCode(purchaseRequestCode);

            } else if (MaterialDemandTypeEnum.ACCESSORIES.getCode().equals(item.getDemandType())) {
                //辅料采购需求单号
                String purchaseRequestCode = businessCodeGenerator.generate(CodeRuleEnum.ACCESSORIES_PURCHASE_REQUEST_CODE, this::selectLatestAccessoriesPurchaseCode);
                item.setPurchaseRequestCode(purchaseRequestCode);
                item.setCustomerItemNo(req.getDesignCode());
            }
        });
    }

    private List<String> getOnshelfSkcImageList(String designCode) {
        List<OnShelfSkc> onShelfSkcList = onShelfSkcRepository.listByDesignCodes(Collections.singletonList(designCode));
        if (CollUtil.isEmpty(onShelfSkcList)) {
            return Collections.emptyList();
        }
        return onShelfSkcList.getFirst().getSkcImageList();
    }


    private BigDecimal getCutRatio() {
        //默认剪版倍率
        BigDecimal defaultCutRatio = new BigDecimal("2");

        Map<String, DictVo> dictValueMap = dictValueRemoteHelper.mapByDictCodes(List.of(DictConstant.SMALL_ORDER_RATIO));
        DictVo dictVo = dictValueMap.get(DictConstant.SMALL_ORDER_RATIO);
        if (Objects.isNull(dictVo) || CollUtil.isEmpty(dictVo.getChildren())) {
            return defaultCutRatio;
        }

        DictVo fabricVo = dictVo.getChildren().stream()
                .filter(item -> Objects.equals(item.getDictName(), "面料"))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(fabricVo)) {
            return defaultCutRatio;
        }
        String cutRatioName = null;
        try {
            cutRatioName = fabricVo.getChildren().getFirst().getDictName();
            defaultCutRatio = new BigDecimal(cutRatioName);
        } catch (Exception e) {
            log.error("剪版倍率获取失败:cutRatioName:{}", cutRatioName, e);
        }

        return defaultCutRatio;
    }

    private void setPurchseBomInfo(PurchaseApplyMaterialConfirmBatchReq req) {
        PurchaseApplyMaterialConfirmBatchReq.PurchaseListReq purchaseListReq = req.getPurchaseApplyList().get(0);
        BomOrderMaterial bomOrderMaterial = bomOrderMaterialRepository.getById(purchaseListReq.getBomMaterialId());
        SdpDesignException.notNull(bomOrderMaterial, "bom单物料不能存在, bomMaterialId:{}", purchaseListReq.getBomMaterialId());
        BomOrder bomOrder = bomOrderRepository.getById(bomOrderMaterial.getBomId());
        req.setBomId(bomOrder.getBomId());
        req.setBomVersionNum(bomOrder.getVersionNum());
    }

    private void checkMaterialEnable(List<MaterialSnapshot> materialSnapshotList, Map<Long, CommoditySkuCollectionRespVo.Sku.SkuPriceVo> skuPriceMap) {

        //校验面料启用状态
        List<MaterialSnapshot> fabricList = materialSnapshotList.stream()
                .filter(item -> Objects.equals(MaterialDemandTypeEnum.FABRIC.getCode(), item.getMaterialType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(fabricList)) {
            Map<String, Long> querySpuSkuMap = StreamUtil.list2MapWithValue(fabricList, MaterialSnapshot::getSpuSkuId, MaterialSnapshot::getMaterialSnapshotId);
            Set<Long> fabricSkuIds = StreamUtil.convertSet(fabricList, MaterialSnapshot::getSkuId);
            List<CommoditySkuCollectionRespVo.Sku> fabricSkuInfos = productRemoteHelper.getFabricSkuSetEnableState(fabricSkuIds, querySpuSkuMap);
            fabricSkuInfos.forEach(sku -> {
                SdpDesignException.isTrue(StringUtils.equals(sku.getIsEnable(), String.valueOf(Bool.YES.getCode())),
                        "面料商品未启用，请重新选择物料: sku: {}",  sku.getSkuCode());
                SdpDesignException.notEmpty(sku.getSkuPriceVos(), "面料缺少足米价信息, sku: {}", sku.getSkuCode());
                CommoditySkuCollectionRespVo.Sku.SkuPriceVo skuPriceVo = sku.getSkuPriceVos().stream()
                        .filter(item -> Objects.equals(item.getSalesRegionId(), DefaultSalesRegionEnum.NATION.getRegionId()))
                        .findFirst().orElse(null);
                SdpDesignException.notNull(skuPriceVo, "面料缺少足米价信息, sku: {}", sku.getSkuCode());
                skuPriceMap.put(sku.getSkuId(), skuPriceVo);
            });
        }

        //校验辅料启用状态
        List<MaterialSnapshot> accessoriesList = materialSnapshotList.stream()
                .filter(item -> Objects.equals(MaterialDemandTypeEnum.ACCESSORIES.getCode(), item.getMaterialType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(accessoriesList)) {
            Set<Long> accessoriesSkuIds = StreamUtil.convertSet(accessoriesList, MaterialSnapshot::getSkuId);
            List<ProductSpuInfoVo> accessoriesSpuInfoList = productRemoteHelper.getAccessoriesSkuInfo(accessoriesSkuIds);
            accessoriesSpuInfoList.forEach(spu -> {
                SdpDesignException.isTrue(Objects.equals(spu.getEnabled(), 1),
                        "辅料商品未启用，请重新选择物料: spu: {}",  spu.getSpuCode());
            });
        }

    }

    private void checkThirdPartyCraftDemandId(PurchaseApplyMaterialConfirmBatchReq req) {
        for(PurchaseApplyMaterialConfirmBatchReq.PurchaseListReq purchaseReq : req.getPurchaseApplyList()) {
            List<CraftDemandInfo> craftDemandInfoList = purchaseReq.getCraftDemandInfoList();
            if(CollectionUtil.isEmpty(craftDemandInfoList)){
                continue;
            }
            craftDemandInfoList.forEach(item -> SdpDesignException.notNull(item.getThirdPartyCraftDemandId(), "采购失败, {}的第三方工艺需求ID缺失!", purchaseReq.getMaterialName()));
        }
    }


    /**
     * 必填信息校验
     *
     * @param req 物料确认下采购单的请求参数
     */
    public void requiredBatchCheckAndOther(PurchaseApplyMaterialConfirmBatchReq req){
        Assert.isTrue(ObjectUtil.isNotEmpty(req.getPrototypeId()),"打版信息id不能为空");
        Assert.isTrue(StringUtil.isNotEmpty(req.getStyleCode()),"成衣SPU(款式SPU)不能为空");
        Assert.isTrue(StringUtil.isNotEmpty(req.getDesignCode()),"设计款号不能为空");
        Assert.isTrue(StringUtil.isNotEmpty(req.getPurchaseApplyCause()),"采购申请原因不能为空");
        req.getPurchaseApplyList().forEach(purchaseApply -> {
            Assert.isTrue(StringUtil.isNotEmpty(purchaseApply.getPurchaseQuantity()),purchaseApply.getMaterialCategory() + ":采购数量不能为空");
            //Assert.isTrue(ObjectUtil.isNotEmpty(purchaseApply.getTrackResultId()),purchaseApply.getMaterialCategory() + ":物料确认结果ID不能为空");
            //Assert.isTrue(ObjectUtil.isNotEmpty(purchaseApply.getDemandId()),purchaseApply.getMaterialCategory() + ":需求id不能为空");

            BigDecimal b = new BigDecimal(purchaseApply.getPurchaseQuantity());
            SdpDesignException.isTrue(b.compareTo(BigDecimal.ZERO) > 0,purchaseApply.getMaterialCategory() + ":采购数量不能小于0");

            // 判断整数位是否大于4位
            BigDecimal bigDecimal = b.setScale(0, BigDecimal.ROUND_DOWN);
            if (bigDecimal.compareTo(new BigDecimal("9999")) == 1) {
                throw new SdpDesignException(purchaseApply.getMaterialCategory() + ":采购数量整数位大于4位");
            }
            // 需求类型: 1, 面料; 2, 辅料
            if(MaterialDemandTypeEnum.FABRIC.getCode().equals(purchaseApply.getDemandType())){
                Assert.isTrue(ObjectUtil.isNotEmpty(purchaseApply.getColorCardPictureUrl()),purchaseApply.getMaterialCategory() + ":面料色卡图片不能为空");
            }

        });

    }


    /**
     * 二次工艺采购
     *
     * @param req 采购申请请求参数
     * @param prototypeHistory 版单历史表实体类
     */
    public void materialConfirmBatchSecondCraftPurchase(PurchaseApplyMaterialConfirmBatchReq req,
                                                        PrototypeHistory prototypeHistory,
                                                        List<CuttingOrderSimpleOpenResp> cuttingOrderSimpleRespList,
                                                        List<AccessoriesMatchOrderOpenResp> accessoriesMatchOrderResponse){
        Map<Long, CuttingOrderSimpleOpenResp> cuttingOrderSimpleRespMap = getCuttingOrderSimpleRespMap(cuttingOrderSimpleRespList);
        Map<Long, AccessoriesMatchOrderOpenResp> accessoriesMatchOrderInnerVoMap = getAccessoriesMatchOrderInnerVoMap(accessoriesMatchOrderResponse);

        PurchaseCraftReq purchaseCraftReq = new PurchaseCraftReq();
        List<PurchaseCraftReq.CraftReq> craftReqs = new ArrayList<>();
        for(PurchaseApplyMaterialConfirmBatchReq.PurchaseListReq materialpurchaseApplys : req.getPurchaseApplyList()){
            Long materialSnapshotId = materialpurchaseApplys.getMaterialSnapshotId();
            List<CraftDemandInfo> craftDemandInfoList = materialpurchaseApplys.getCraftDemandInfoList();
            if(CollectionUtil.isEmpty(craftDemandInfoList)){
                continue;
            }
            CuttingOrderSimpleOpenResp cuttingOrderSimpleResp = null;
            if(ObjectUtil.isNotEmpty(cuttingOrderSimpleRespMap)){
                cuttingOrderSimpleResp = cuttingOrderSimpleRespMap.get(materialSnapshotId);
            }
            AccessoriesMatchOrderOpenResp accessoriesMatchOrderInnerVo = null;
            if(ObjectUtil.isNotEmpty(accessoriesMatchOrderInnerVoMap)){
                accessoriesMatchOrderInnerVo = accessoriesMatchOrderInnerVoMap.get(materialSnapshotId);
            }
            String orderCode = ObjectUtil.isNotEmpty(cuttingOrderSimpleResp) ? cuttingOrderSimpleResp.getOrderCode() : "";
            if(StringUtil.isEmpty(orderCode)){
                orderCode = ObjectUtil.isNotEmpty(accessoriesMatchOrderInnerVo) ? accessoriesMatchOrderInnerVo.getOrderCode() : "";
            }
            for(CraftDemandInfo craftDemandInfo : craftDemandInfoList){
                PurchaseCraftReq.CraftReq craftReq = new PurchaseCraftReq.CraftReq();
                craftReq.setThirdPartyCraftDemandId(craftDemandInfo.getThirdPartyCraftDemandId());
                craftReq.setOrderCode(orderCode);
                //craftReq.setMaterialMatchId(materialSnapshotId);
                craftReqs.add(craftReq);
            }
        }
        // 二次工艺采购
        purchaseCraftReq.setDesignCode(prototypeHistory.getDesignCode());
        purchaseCraftReq.setSecondCraftChannel(SecondCraftChannelEnum.DESIGN_BOM);
        purchaseCraftReq.setCraftReqList(craftReqs);
        log.info(" ====================>  二次工艺采购信息 purchaseCraftReq：{}",JSONUtil.toJsonStr(purchaseCraftReq));
        if(CollectionUtil.isNotEmpty(craftReqs)){
            CompletableFuture.runAsync(AsyncTask.wrapper(()->{
                try{
                    cn.yibuyun.framework.net.DataResponse<Void> voidDataResponse = secondCraftDemandClient.secondCraftPurchase(purchaseCraftReq);
                    log.info(" ====================>  二次工艺采购是否成功：{}，response信息 ：{}",
                            voidDataResponse.isSuccessful(),voidDataResponse.getMessage());
                }catch (Exception e){
                    log.info(" ====================>  二次工艺采购异常:{}",e.getMessage());
                }
            }));
        }
    }

    public Map<Long, CuttingOrderSimpleOpenResp> getCuttingOrderSimpleRespMap(List<CuttingOrderSimpleOpenResp> cuttingOrderSimpleResponse){
        List<CuttingOrderSimpleOpenResp> cuttingOrderSimpleRespList = null;
        Map<Long, CuttingOrderSimpleOpenResp> cuttingOrderSimpleRespMap = null;
        if(ObjectUtil.isNotEmpty(cuttingOrderSimpleResponse)){
            cuttingOrderSimpleRespList = cuttingOrderSimpleResponse;
            if(CollectionUtil.isNotEmpty(cuttingOrderSimpleRespList)){
                cuttingOrderSimpleRespMap = cuttingOrderSimpleRespList.stream()
                        .collect(Collectors.toMap(CuttingOrderSimpleOpenResp::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));
            }
        }

        return cuttingOrderSimpleRespMap;
    }


    public Map<Long, AccessoriesMatchOrderOpenResp> getAccessoriesMatchOrderInnerVoMap(List<AccessoriesMatchOrderOpenResp> listDataResponse){
        List<AccessoriesMatchOrderOpenResp> accessoriesMatchOrderInnerVoList = null;
        Map<Long, AccessoriesMatchOrderOpenResp> accessoriesMatchOrderInnerVoMap = null;
        if(ObjectUtil.isNotEmpty(listDataResponse)){
            accessoriesMatchOrderInnerVoList = listDataResponse;
            if(CollectionUtil.isNotEmpty(accessoriesMatchOrderInnerVoList)){
                accessoriesMatchOrderInnerVoMap = accessoriesMatchOrderInnerVoList.stream()
                        .collect(Collectors.toMap(AccessoriesMatchOrderOpenResp::getMaterialSnapshotId, Function.identity(), (k1, k2) -> k1));
            }
        }

        return accessoriesMatchOrderInnerVoMap;
    }

    public void purchaseCraftCancel(List<MaterialPurchaseFollow> purchaseCraftMaterialCancels,String cancelReason){
        log.info("===========================> 取消物料的裁前工艺任务(取消采购申请)，相关物料信息：{}",JSONUtil.toJsonStr(purchaseCraftMaterialCancels));
        if(CollectionUtil.isEmpty(purchaseCraftMaterialCancels)){
            return ;
        }
        SecondCraftCancelReq secondCraftCancelReq = new SecondCraftCancelReq();
        List<String> orderCodeList = new ArrayList<>();
        purchaseCraftMaterialCancels.forEach(vo -> {
            orderCodeList.add(vo.getCuttingCode());
        });
        secondCraftCancelReq.setOrderCodeList(orderCodeList);
        secondCraftCancelReq.setReason(cancelReason);

        String message = "";

        try{
            cn.yibuyun.framework.net.DataResponse<Void> voidDataResponse = secondCraftDemandClient.purchaseCraftCancel(secondCraftCancelReq);
            log.info(" ====================>  (取消采购申请)取消物料的裁前工艺任务是否成功：{}，response信息 ：{}", voidDataResponse.isSuccessful(),voidDataResponse.getMessage());

            if(!voidDataResponse.isSuccessful()){
                message = voidDataResponse.getMessage();
            }
            SdpDesignException.isTrue(voidDataResponse.isSuccessful(),"(取消采购申请)取消物料的裁前工艺任务失败:" + message);
        }catch (Exception e){
            message = StringUtil.isNotEmpty(message)? message : e.getMessage();
            log.info(" ====================>  (取消采购申请)取消物料的裁前工艺任务异常:{},{}" + message,e.getMessage());
            throw new SdpDesignException("【(取消采购申请)取消物料的裁前工艺任务异常】" + message,e);
        }
    }


}