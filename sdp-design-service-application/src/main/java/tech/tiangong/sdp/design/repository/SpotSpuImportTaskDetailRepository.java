package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.SpotSpuImportTask;
import tech.tiangong.sdp.design.entity.SpotSpuImportTaskDetail;
import tech.tiangong.sdp.design.mapper.SpotSpuImportTaskDetailMapper;
import tech.tiangong.sdp.utils.Bool;
import tech.tiangong.sdp.utils.Md5Utils;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @Author: caicijie
 * @description:
 * @Date: 2025/6/26 17:37
 */
@Repository
public class SpotSpuImportTaskDetailRepository extends BaseRepository<SpotSpuImportTaskDetailMapper, SpotSpuImportTaskDetail> {

    public List<SpotSpuImportTaskDetail> findByTaskId(Long taskId) {
        return list(new LambdaQueryWrapper<SpotSpuImportTaskDetail>()
                .eq(SpotSpuImportTaskDetail::getTaskId, taskId)
                .eq(SpotSpuImportTaskDetail::getIsDeleted, Bool.NO.getCode())
        );
    }

    public List<SpotSpuImportTaskDetail> findByPicMd5(List<String> urlMd5) {
        return list(new LambdaQueryWrapper<SpotSpuImportTaskDetail>()
                .in(SpotSpuImportTaskDetail::getPicMd5, urlMd5)
                .isNotNull(SpotSpuImportTaskDetail::getOssPicUrl)
                .eq(SpotSpuImportTaskDetail::getIsDeleted, Bool.NO.getCode())
        );
    }
}
