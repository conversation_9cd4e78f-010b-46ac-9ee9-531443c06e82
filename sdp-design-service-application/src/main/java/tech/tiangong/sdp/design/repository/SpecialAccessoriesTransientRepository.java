package tech.tiangong.sdp.design.repository;

import cn.yibuyun.framework.base.repository.BaseRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import tech.tiangong.sdp.design.entity.SpecialAccessoriesTransient;
import tech.tiangong.sdp.design.mapper.SpecialAccessoriesTransientMapper;
import tech.tiangong.sdp.design.vo.query.bom.SpecialAccessoriesTransientQuery;
import tech.tiangong.sdp.design.vo.resp.bom.SpecialAccessoriesTransientVo;

import java.util.List;
import java.util.Objects;

/**
 * 特殊辅料_暂存表服务仓库类
 *
 * <AUTHOR>
 */
@Repository
public class SpecialAccessoriesTransientRepository extends BaseRepository<SpecialAccessoriesTransientMapper, SpecialAccessoriesTransient> {

    /**
     * 分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public IPage<SpecialAccessoriesTransientVo> findPage(SpecialAccessoriesTransientQuery query) {
        return baseMapper.findPage(new Page<>(query.getPageNum(), query.getPageSize()), query);
    }

    /**
     * 根据暂存bomId查暂存特辅
     * @param bomTransientId 暂存bomId
     */
    public List<SpecialAccessoriesTransient> listByBomTransientId(Long bomTransientId) {
        if (Objects.isNull(bomTransientId)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(SpecialAccessoriesTransient::getBomTransientId, bomTransientId)
                .list();
    }

    /**
     * 根据暂存bomId与暂存状态查出暂存特辅
     */
    public List<SpecialAccessoriesTransient> listByBomTransientIdTransientState(Long bomTransientId, Integer state) {
        if (Objects.isNull(bomTransientId)) {
            return List.of();
        }
        return lambdaQuery()
                .eq(SpecialAccessoriesTransient::getBomTransientId, bomTransientId)
                .eq(Objects.nonNull(state), SpecialAccessoriesTransient::getState, state)
                .list();
    }
}
