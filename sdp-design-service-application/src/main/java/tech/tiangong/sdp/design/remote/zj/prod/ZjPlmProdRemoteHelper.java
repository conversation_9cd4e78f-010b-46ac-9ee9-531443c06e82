package tech.tiangong.sdp.design.remote.zj.prod;

import cn.yibuyun.framework.net.DataResponse;
import cn.yibuyun.plm.order.info.common.prod.vo.req.CreateOrUpdateStyleInfoByAllDataListReq;
import cn.yibuyun.plm.order.info.common.prod.vo.resp.CreateOrUpdateStyleInfoByAllDataResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import tech.tiangong.sdp.core.config.ZjOpenFeignUserContentConfig;

import java.util.List;

/**
 * @Created by jeromeliu
 * @ClassName PlmProdRemoteHelper
 * @Description 致景PLM-生产资料模块 对接路由
 * @Date 2024/11/30 11:33
 */
@FeignClient(value = "plm-order-info",
        contextId = "PLM-ProdCommodityClient",
        configuration = ZjOpenFeignUserContentConfig.class,
        path = "/zj-tg-api",
        url = "${cx-tg.domain.url}")
public interface ZjPlmProdRemoteHelper {

    /**
     * 订单生产资料同步
     *
     * @see "https://yapi.textile-story.com/project/2712/interface/api/73348"
     * @param req 入参
     * @return 响应结果
     */
    @PostMapping("/plm-order-info/inner/v1/style-info/create-or-update-style-info-by-allData")
    DataResponse<List<CreateOrUpdateStyleInfoByAllDataResp>> createOrUpdateStyleInfoByAllData(@RequestBody CreateOrUpdateStyleInfoByAllDataListReq req);
}
