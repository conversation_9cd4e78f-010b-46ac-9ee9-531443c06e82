package tech.tiangong.sdp.design.business.remark;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import tech.tiangong.sdp.design.entity.DesignRemarks;
import tech.tiangong.sdp.design.entity.OrderMaterialFollow;
import tech.tiangong.sdp.design.entity.PurchasePrototypeInfo;
import tech.tiangong.sdp.design.repository.OrderMaterialFollowRepository;
import tech.tiangong.sdp.design.repository.PurchasePrototypeInfoRepository;
import tech.tiangong.sdp.design.vo.req.remarks.DesignRemarksReq;

/**
 * 物料齐套备注信息 封装器
 * <AUTHOR>
 * @create 2021/8/19
 */
public class ComposeDesignRemarkMaterialFollow implements ComposeDesignRemark{
    @Override
    public DesignRemarks compose(DesignRemarksReq req, DesignRemarks designRemarks) {
        OrderMaterialFollowRepository orderMaterialFollowRepository = SpringUtil.getBean(OrderMaterialFollowRepository.class);
        OrderMaterialFollow orderMaterialFollow = orderMaterialFollowRepository.getById(req.getBizId());
        Assert.notNull(orderMaterialFollow,"不存在此物料齐套跟进信息! ");
        designRemarks.setBizId(orderMaterialFollow.getOrderMaterialFollowId());
        designRemarks.setBizType(req.getBizType());
        designRemarks.setBizVersionNum(1);

        /*PrototypeHistoryRepository prototypeRepository = SpringUtil.getBean(PrototypeHistoryRepository.class);
        if(ObjectUtil.isNotEmpty(orderMaterialFollow.getPrototypeId())) {
            PrototypeHistory prototype = prototypeRepository.getById(orderMaterialFollow.getPrototypeId());
            Assert.notNull(prototype, "设计款号不存在! ");
            designRemarks.setPrototypeId(prototype.getPrototypeId());
            designRemarks.setStyleCode(prototype.getStyleCode());
            designRemarks.setDesignCode(prototype.getDesignCode());
            designRemarks.setVersionNum(prototype.getVersionNum());
        }*/

        PurchasePrototypeInfoRepository prototypeInfoRepository = SpringUtil.getBean(PurchasePrototypeInfoRepository.class);
        if(ObjectUtil.isNotEmpty(orderMaterialFollow.getPrototypeId())) {
            PurchasePrototypeInfo purchasePrototypeInfo = prototypeInfoRepository.getById(orderMaterialFollow.getPrototypeId());
            Assert.notNull(purchasePrototypeInfo, "设计款号不存在! ");
            designRemarks.setStyleCode(purchasePrototypeInfo.getStyleCode());
            designRemarks.setDesignCode(purchasePrototypeInfo.getDesignCode());
        }

        return designRemarks;
    }
}
