package tech.tiangong.sdp.design.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.yibuyun.framework.net.PageRespVo;
import cn.yibuyun.framework.util.PageRespVoHelper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.design.entity.BomMaterialDemandTransient;
import tech.tiangong.sdp.design.repository.BomMaterialDemandTransientRepository;
import tech.tiangong.sdp.design.service.BomMaterialDemandTransientService;
import tech.tiangong.sdp.design.vo.query.bom.BomMaterialDemandTransientQuery;
import tech.tiangong.sdp.design.vo.req.bom.BomMaterialDemandTransientReq;
import tech.tiangong.sdp.design.vo.resp.bom.BomMaterialDemandTransientVo;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * bom物料需求_暂存表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BomMaterialDemandTransientServiceImpl implements BomMaterialDemandTransientService {
    private final BomMaterialDemandTransientRepository bomMaterialDemandTransientRepository;

    @Override
    public PageRespVo<BomMaterialDemandTransientVo> page(BomMaterialDemandTransientQuery query) {
        IPage<BomMaterialDemandTransientVo> page = bomMaterialDemandTransientRepository.findPage(query);
        return PageRespVoHelper.of((int) page.getCurrent(), page.getTotal(), page.getRecords());
    }

    @Override
    public BomMaterialDemandTransientVo getById(Long id) {
        BomMaterialDemandTransient entity = bomMaterialDemandTransientRepository.getById(id);
        if (Objects.isNull(entity)) {
            return null;
        }
        return this.entity2Vo(entity);
    }

    private BomMaterialDemandTransientVo entity2Vo(BomMaterialDemandTransient entity) {
        BomMaterialDemandTransientVo vo = new BomMaterialDemandTransientVo();
        BeanUtils.copyProperties(entity, vo);
        String demandPicture = entity.getDemandPicture();
        if (StringUtils.isNotBlank(demandPicture)) {
            vo.setDemandPictureList(StrUtil.splitTrim(demandPicture, StrUtil.COMMA));
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(BomMaterialDemandTransientReq req) {
        BomMaterialDemandTransient entity = new BomMaterialDemandTransient();
        BeanUtils.copyProperties(req, entity);
        bomMaterialDemandTransientRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BomMaterialDemandTransientReq req) {
        BomMaterialDemandTransient entity = new BomMaterialDemandTransient();
        BeanUtils.copyProperties(req, entity);
        bomMaterialDemandTransientRepository.updateById(entity);
    }

    @Override
    public void remove(Long id) {
        bomMaterialDemandTransientRepository.removeById(id);
    }

    @Override
    public List<BomMaterialDemandTransientVo> listByBomTransientId(Long bomTransientId) {
        if (Objects.isNull(bomTransientId)) {
            return List.of();
        }

        return bomMaterialDemandTransientRepository.listByBomTransientId(bomTransientId).stream()
                .map(this::entity2Vo)
                .collect(Collectors.toList());
    }

}
