package tech.tiangong.sdp.design.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tech.tiangong.sdp.core.exception.SdpDesignException;
import tech.tiangong.sdp.design.entity.BomOrder;
import tech.tiangong.sdp.design.entity.Prototype;
import tech.tiangong.sdp.design.enums.PushZjTypeEnum;
import tech.tiangong.sdp.design.remote.ZjDesignRemoteHelper;
import tech.tiangong.sdp.design.repository.BomOrderRepository;
import tech.tiangong.sdp.design.repository.PrototypeRepository;
import tech.tiangong.sdp.design.service.BomOperateService;
import tech.tiangong.sdp.design.service.ZjHandleService;
import tech.tiangong.sdp.design.vo.dto.bom.BomHouliuDemandDeleteDto;
import tech.tiangong.sdp.design.vo.req.mq.BomSubmit2ZjMqDto;
import tech.tiangong.sdp.design.vo.req.mq.CancelPrototypeMqDTO;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/24 19:38
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class ZjHandleServiceImpl implements ZjHandleService {

    private final ZjDesignRemoteHelper ZjDesignRemoteHelper;

    private final BomOrderRepository bomOrderRepository;
    private final PrototypeRepository prototypeRepository;
    @Resource
    @Lazy
    private BomOperateService bomOperateService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSkc(List<CancelPrototypeMqDTO> cancelPrototypeMqDTOList) {
        SdpDesignException.notEmpty(cancelPrototypeMqDTOList, "入参为空!");
        cancelPrototypeMqDTOList.forEach(ZjDesignRemoteHelper::cancelSkc);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void houLiuDemandDelete(BomHouliuDemandDeleteDto demandDeleteDto) {
        SdpDesignException.notNull(demandDeleteDto, "入参为空!");
        ZjDesignRemoteHelper.houLiuDemandDelete(demandDeleteDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bomSubmit2Zj(BomSubmit2ZjMqDto mqDto) {
        //bom提交推送致景
        SdpDesignException.notNull(mqDto, "入参为空!");
        SdpDesignException.notNull(mqDto.getBomId(), "入参为空!");
        BomOrder bomOrder = bomOrderRepository.getById(mqDto.getBomId());
        SdpDesignException.notNull(bomOrder, "bom为空!");

        //推送bom信息给致景
        bomOperateService.pushBom2Zj(bomOrder, PushZjTypeEnum.BOM_SUBMIT);
    }


}
