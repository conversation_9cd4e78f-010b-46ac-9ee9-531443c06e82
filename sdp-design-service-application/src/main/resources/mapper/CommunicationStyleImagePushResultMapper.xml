<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.sdp.design.communication.mapper.CommunicationStyleImagePushResultMapper">
    <select id="findUnPushedSpuList" resultType="tech.tiangong.sdp.design.communication.CommunicationSpu">
        SELECT s.style_code, v.main_url, s.supply_mode_code, s.category
        FROM spot_spu s
             JOIN visual_image_package v ON v.style_code = s.style_code AND v.is_latest = 1
        WHERE s.pushed_to_pop = 1
          AND s.communication = 1
          AND v.main_url IS NOT NULL
          AND v.main_url != ''
          AND NOT EXISTS(SELECT 1 FROM communication_style_image_push_result t WHERE t.spu_code = s.style_code)
        LIMIT #{limit}
    </select>
</mapper>
