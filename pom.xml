<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <groupId>tech.tiangong.sdp</groupId>
    <artifactId>sdp-design-parent</artifactId>
    <version>${revision}</version>
    <name>sdp-design-parent</name>
    <description>款式开发平台-设计中心服务</description>

    <!--云板房父pom-->
    <parent>
        <groupId>cn.yibuyun</groupId>
        <artifactId>yibuyun-parent</artifactId>
        <version>2.2.29</version>
    </parent>

    <properties>
        <!-- 只需修改此处版本 -->
        <!-- SNAPSHOT版本规范：当前RELEASE版本的下一个版本加上需求编号。例如： 0.0.7-v1.3-SNAPSHOT-->
        <revision>1.0.22</revision>
    </properties>

    <profiles>
        <profile>

            <id>all-modules</id>
            <modules>
                <module>sdp-design-service-application</module>
                <module>sdp-design-service-sdk</module>
                <module>sdp-design-common</module>
            </modules>
        </profile>

        <profile>
            <id>all-component-modules</id>
            <modules>
                <module>sdp-design-service-sdk</module>
                <module>sdp-design-common</module>
            </modules>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>jv-releases</id>
            <url>https://nexus.tiangong.site/repository/aikero-releases</url>
        </repository>
        <snapshotRepository>
            <id>jv-snapshots</id>
            <url>https://nexus.tiangong.site/repository/aikero-snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>META-INF/**</include>
                    <include>template/*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.2.7</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>
